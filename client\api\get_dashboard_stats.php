<?php
/**
 * API لجلب إحصائيات لوحة التحكم
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

try {
    require_once '../../config/database.php';

    // تسجيل وقت البداية
    $start_time = microtime(true);

    // استعلام واحد محسن لجلب جميع الإحصائيات
    $main_stats_query = $pdo->prepare("
        SELECT
            -- إحصائيات الجلسات النشطة
            (SELECT COUNT(*) FROM sessions s2
             JOIN devices d2 ON s2.device_id = d2.device_id
             WHERE d2.client_id = ? AND s2.status = 'active') as active_sessions,

            -- إحصائيات اليوم
            COUNT(CASE WHEN DATE(s.start_time) = CURRENT_DATE THEN s.session_id END) as today_sessions,
            COALESCE(SUM(CASE WHEN DATE(s.start_time) = CURRENT_DATE AND s.status = 'completed'
                         THEN s.total_cost ELSE 0 END), 0) as today_income,

            -- إحصائيات الشهر والسنة
            COALESCE(SUM(CASE WHEN MONTH(s.start_time) = MONTH(CURRENT_DATE)
                              AND YEAR(s.start_time) = YEAR(CURRENT_DATE)
                              AND s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as month_income,
            COALESCE(SUM(CASE WHEN YEAR(s.start_time) = YEAR(CURRENT_DATE)
                              AND s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as year_income
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
    ");

    $main_stats_query->execute([$client_id, $client_id]);
    $main_stats = $main_stats_query->fetch(PDO::FETCH_ASSOC);

    // استعلام منفصل للأجهزة (سريع)
    $devices_stats_query = $pdo->prepare("
        SELECT
            COUNT(*) as total_devices,
            SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_devices,
            SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_devices,
            SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_devices
        FROM devices
        WHERE client_id = ?
    ");
    $devices_stats_query->execute([$client_id]);
    $devices_stats = $devices_stats_query->fetch(PDO::FETCH_ASSOC);

    // استعلام منفصل للعملاء (سريع)
    $customers_count_query = $pdo->prepare("SELECT COUNT(*) as total_customers FROM customers WHERE client_id = ?");
    $customers_count_query->execute([$client_id]);
    $customers_count = $customers_count_query->fetch(PDO::FETCH_ASSOC);

    // جلب تفاصيل الجلسات النشطة (فقط إذا كانت موجودة)
    $active_sessions_details = [];
    if ($main_stats['active_sessions'] > 0) {
        $active_sessions_details_query = $pdo->prepare("
            SELECT s.session_id, s.start_time, d.device_name, d.device_type, d.hourly_rate,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
                   (TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) * d.hourly_rate / 60) as current_cost
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ? AND s.status = 'active'
            ORDER BY s.start_time DESC
            LIMIT 10
        ");
        $active_sessions_details_query->execute([$client_id]);
        $active_sessions_details = $active_sessions_details_query->fetchAll(PDO::FETCH_ASSOC);
    }

    // حساب وقت التنفيذ
    $execution_time = microtime(true) - $start_time;
    
    // إعداد الاستجابة المحسنة
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'execution_time' => round($execution_time * 1000, 2) . 'ms',
        'active_sessions' => $main_stats['active_sessions'] ?? 0,
        'today' => [
            'total_sessions' => $main_stats['today_sessions'] ?? 0,
            'today_income' => $main_stats['today_income'] ?? 0
        ],
        'devices' => [
            'total_devices' => $devices_stats['total_devices'] ?? 0,
            'available_devices' => $devices_stats['available_devices'] ?? 0,
            'occupied_devices' => $devices_stats['occupied_devices'] ?? 0,
            'maintenance_devices' => $devices_stats['maintenance_devices'] ?? 0
        ],
        'income' => [
            'month_income' => $main_stats['month_income'] ?? 0,
            'year_income' => $main_stats['year_income'] ?? 0
        ],
        'customers' => [
            'total_customers' => $customers_count['total_customers'] ?? 0
        ],
        'active_sessions_details' => $active_sessions_details,
        'performance' => [
            'queries_count' => $main_stats['active_sessions'] > 0 ? 4 : 3,
            'cache_enabled' => false,
            'optimization_level' => 'high'
        ]
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    error_log("Dashboard stats API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'error' => 'حدث خطأ في قاعدة البيانات',
        'debug' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
    
} catch (Exception $e) {
    error_log("General error in dashboard stats API: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage()
    ]);
}
?>
