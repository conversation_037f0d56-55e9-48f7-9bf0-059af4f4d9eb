RewriteEngine On

<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header unset X-Powered-By
    Header unset Server
</IfModule>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|tmp|temp|env|key)$">
    Require all denied
</FilesMatch>

Options -Indexes

<Files "*.ini">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.key">
    Require all denied
</Files>

<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code\(.*\) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*(iframe|object|embed).*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\.\.//?)+ [OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [OR]
    RewriteCond %{QUERY_STRING} ^.*(select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

LimitRequestBody 10485760
ServerSignature Off

RedirectMatch 404 /\.git
RedirectMatch 404 /\.svn
RedirectMatch 404 /\.hg

<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(-|curl|wget|libwww-perl|python|nikto|scan) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (binlar|casper|cmsworldmap|comodo|diavol|dotbot|feedfinder|flicky|ia_archiver|jakarta|kmccrew|nutch|planetwork|purebot|pycurl|skygrid|sucker|turnit|vikspider|zmeu) [NC]
    RewriteRule .* - [F]
</IfModule>

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

ErrorDocument 404 /playgood/404.php
ErrorDocument 403 /playgood/403.php
ErrorDocument 500 /playgood/500.php

RewriteRule ^home$ index.php [NC,L]
RewriteRule ^dashboard$ client/dashboard.php [NC,L]
RewriteRule ^login$ client/login.php [NC,L]
RewriteRule ^register$ register.php [NC,L]
RewriteRule ^admin$ admin/login.php [NC,L]
