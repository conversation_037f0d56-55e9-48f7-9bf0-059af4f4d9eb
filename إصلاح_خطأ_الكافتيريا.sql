-- =====================================================
-- إصلاح خطأ عمود category في جدول cafeteria_items
-- =====================================================
-- هذا السكريبت يحل مشكلة العمود المفقود 'category'
-- =====================================================

USE `station`;

-- التحقق من وجود العمود category
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = 'station' 
    AND table_name = 'cafeteria_items' 
    AND column_name = 'category'
);

-- إضافة العمود إذا لم يكن موجوداً
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE cafeteria_items ADD COLUMN category varchar(50) DEFAULT NULL AFTER price',
    'SELECT "العمود category موجود بالفعل" AS status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- تحديث العمود category بناءً على category_id إذا كان فارغاً
UPDATE cafeteria_items ci
LEFT JOIN categories cat ON ci.category_id = cat.category_id
SET ci.category = cat.name
WHERE ci.category IS NULL AND cat.name IS NOT NULL;

-- إضافة فهرس للعمود الجديد
CREATE INDEX IF NOT EXISTS idx_cafeteria_category ON cafeteria_items(category);

-- عرض النتيجة
SELECT 
    'تم إصلاح مشكلة عمود category بنجاح' AS message,
    COUNT(*) AS total_items,
    COUNT(CASE WHEN category IS NOT NULL THEN 1 END) AS items_with_category
FROM cafeteria_items;
