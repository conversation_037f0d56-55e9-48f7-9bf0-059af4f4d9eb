<?php
/**
 * إعداد سريع لنظام الصلاحيات
 * يتحقق من النظام ويقوم بإعداده إذا لم يكن موجوداً
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد سريع لنظام الصلاحيات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card shadow'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-shield-alt me-2'></i>إعداد سريع لنظام الصلاحيات</h3>
        </div>
        <div class='card-body'>";

try {
    $setup_needed = false;
    $messages = [];
    
    // فحص الجداول المطلوبة
    $required_tables = ['permissions', 'pages', 'employee_permissions', 'employee_pages'];
    
    echo "<h5><i class='fas fa-search me-2'></i>فحص الجداول المطلوبة</h5>";
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>جدول $table موجود</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>جدول $table غير موجود</p>";
            $setup_needed = true;
        }
    }
    
    // فحص عمود custom_permissions
    echo "<h5><i class='fas fa-search me-2'></i>فحص عمود الصلاحيات المخصصة</h5>";
    $stmt = $pdo->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $has_custom_permissions = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'custom_permissions') {
            $has_custom_permissions = true;
            break;
        }
    }
    
    if ($has_custom_permissions) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>عمود custom_permissions موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>عمود custom_permissions غير موجود</p>";
        $setup_needed = true;
    }
    
    if ($setup_needed) {
        echo "<div class='alert alert-warning'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>يحتاج النظام إلى إعداد</h6>
                <p>سيتم تشغيل ملف SQL لإنشاء الجداول المطلوبة...</p>
              </div>";
        
        // تشغيل ملف SQL
        $sql_file = '../create_employee_permissions_system.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            $sql_statements = explode(';', $sql_content);
            
            echo "<h5><i class='fas fa-cogs me-2'></i>تشغيل إعدادات قاعدة البيانات</h5>";
            
            $success_count = 0;
            foreach ($sql_statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $success_count++;
                    } catch (PDOException $e) {
                        if (!strpos($e->getMessage(), 'already exists') && 
                            !strpos($e->getMessage(), 'Duplicate entry')) {
                            echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage() . "</p>";
                        }
                    }
                }
            }
            
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم تنفيذ $success_count عملية بنجاح</p>";
            
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>ملف SQL غير موجود: $sql_file</p>";
        }
    } else {
        echo "<div class='alert alert-success'>
                <h6><i class='fas fa-check-circle me-2'></i>النظام جاهز!</h6>
                <p>جميع الجداول والإعدادات موجودة ومُعدة بشكل صحيح.</p>
              </div>";
    }
    
    // فحص البيانات الأساسية
    echo "<h5><i class='fas fa-database me-2'></i>فحص البيانات الأساسية</h5>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions WHERE is_active = 1");
    $permissions_count = $stmt->fetch()['count'];
    
    if ($permissions_count > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>توجد $permissions_count صلاحية في النظام</p>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد صلاحيات في النظام</p>";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    
    if ($pages_count > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>توجد $pages_count صفحة في النظام</p>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد صفحات في النظام</p>";
    }
    
    echo "<div class='alert alert-info mt-4'>
            <h6><i class='fas fa-info-circle me-2'></i>الخطوات التالية:</h6>
            <ol>
                <li>انتقل إلى <a href='employees.php' class='alert-link'>صفحة الموظفين</a></li>
                <li>اختر موظف للتعديل</li>
                <li>فعّل الصلاحيات المخصصة</li>
                <li>اختر الصلاحيات المطلوبة</li>
                <li>احفظ التغييرات</li>
            </ol>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h6><i class='fas fa-exclamation-circle me-2'></i>خطأ في قاعدة البيانات</h6>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='employees.php' class='btn btn-primary me-2'>
                <i class='fas fa-users me-2'></i>إدارة الموظفين
            </a>
            <a href='check_permissions_system.php' class='btn btn-info me-2'>
                <i class='fas fa-search me-2'></i>فحص مفصل
            </a>
            <a href='../setup_employee_permissions.php' class='btn btn-secondary'>
                <i class='fas fa-cogs me-2'></i>إعداد متقدم
            </a>
        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
