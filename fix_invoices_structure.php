<?php
/**
 * سكريپت إصلاح بنية جدول الفواتير
 * يصلح المشاكل في استخدام invoice_id بدلاً من id
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح بنية جدول الفواتير - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-warning text-dark'>
                    <h3 class='mb-0'>
                        <i class='fas fa-wrench me-2'></i>
                        إصلاح بنية جدول الفواتير
                    </h3>
                </div>
                <div class='card-body'>";

try {
    echo "<h5>1. فحص بنية جدول الفواتير الحالية</h5>";
    
    // فحص بنية الجدول
    $columns_check = $pdo->query("DESCRIBE invoices");
    $columns = $columns_check->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>
            <table class='table table-sm table-bordered'>
                <thead class='table-light'>
                    <tr>
                        <th>اسم العمود</th>
                        <th>النوع</th>
                        <th>مفتاح</th>
                        <th>إضافي</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>";
    
    $has_invoice_id = false;
    $has_id = false;
    $invoice_id_auto = false;
    
    foreach ($columns as $column) {
        $field = $column['Field'];
        $type = $column['Type'];
        $key = $column['Key'];
        $extra = $column['Extra'];
        
        if ($field === 'invoice_id') {
            $has_invoice_id = true;
            if ($extra === 'auto_increment') $invoice_id_auto = true;
        }
        if ($field === 'id') $has_id = true;
        
        $status_class = '';
        $status_text = '';
        
        if ($field === 'invoice_id' && $key === 'PRI' && $extra === 'auto_increment') {
            $status_class = 'table-success';
            $status_text = 'صحيح';
        } elseif ($field === 'id') {
            $status_class = 'table-danger';
            $status_text = 'مشكلة - عمود إضافي';
        } else {
            $status_class = 'table-light';
            $status_text = 'عادي';
        }
        
        echo "<tr class='$status_class'>
                <td><strong>$field</strong></td>
                <td>$type</td>
                <td>" . ($key ?: '-') . "</td>
                <td>" . ($extra ?: '-') . "</td>
                <td>$status_text</td>
              </tr>";
    }
    
    echo "</tbody></table></div>";
    
    echo "<h5>2. تحليل المشاكل</h5>";
    
    $issues = [];
    $fixes = [];
    
    if (!$has_invoice_id) {
        $issues[] = "عمود invoice_id غير موجود";
        $fixes[] = "إضافة عمود invoice_id";
    } elseif (!$invoice_id_auto) {
        $issues[] = "عمود invoice_id ليس AUTO_INCREMENT";
        $fixes[] = "تعديل عمود invoice_id ليكون AUTO_INCREMENT";
    }
    
    if ($has_id) {
        $issues[] = "عمود id إضافي موجود";
        $fixes[] = "حذف عمود id الإضافي";
    }
    
    if (empty($issues)) {
        echo "<div class='alert alert-success'>
                <h6><i class='fas fa-check-circle me-2'></i>بنية جدول الفواتير صحيحة!</h6>
                <p>جدول الفواتير يستخدم invoice_id بشكل صحيح.</p>
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>مشاكل مكتشفة:</h6>
                <ul class='mb-0'>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul></div>";
        
        echo "<div class='alert alert-info'>
                <h6><i class='fas fa-tools me-2'></i>الإصلاحات المطلوبة:</h6>
                <ul class='mb-0'>";
        foreach ($fixes as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul></div>";
    }
    
    echo "<h5>3. تطبيق الإصلاحات</h5>";
    
    if (!empty($issues)) {
        // إصلاح المشاكل
        if ($has_id) {
            echo "<p class='text-warning'><i class='fas fa-trash me-2'></i>حذف عمود id الإضافي...</p>";
            try {
                $pdo->exec("ALTER TABLE invoices DROP COLUMN id");
                echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم حذف عمود id بنجاح</p>";
            } catch (PDOException $e) {
                echo "<p class='text-danger'><i class='fas fa-times me-2'></i>فشل في حذف عمود id: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        if (!$has_invoice_id) {
            echo "<p class='text-info'><i class='fas fa-plus me-2'></i>إضافة عمود invoice_id...</p>";
            try {
                $pdo->exec("ALTER TABLE invoices ADD COLUMN invoice_id INT AUTO_INCREMENT PRIMARY KEY FIRST");
                echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إضافة عمود invoice_id بنجاح</p>";
            } catch (PDOException $e) {
                echo "<p class='text-danger'><i class='fas fa-times me-2'></i>فشل في إضافة عمود invoice_id: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } elseif (!$invoice_id_auto) {
            echo "<p class='text-info'><i class='fas fa-edit me-2'></i>تعديل عمود invoice_id...</p>";
            try {
                $pdo->exec("ALTER TABLE invoices MODIFY invoice_id INT AUTO_INCREMENT PRIMARY KEY");
                echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تعديل عمود invoice_id بنجاح</p>";
            } catch (PDOException $e) {
                echo "<p class='text-danger'><i class='fas fa-times me-2'></i>فشل في تعديل عمود invoice_id: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    echo "<h5>4. اختبار الفواتير</h5>";
    
    // اختبار جلب الفواتير
    try {
        $test_query = $pdo->prepare("
            SELECT 
                i.invoice_id,
                i.invoice_number,
                i.payment_status,
                i.total_cost,
                s.session_id,
                d.device_name,
                c.name as customer_name
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            LIMIT 5
        ");
        $test_query->execute();
        $test_invoices = $test_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($test_invoices)) {
            echo "<div class='alert alert-success'>
                    <h6><i class='fas fa-check me-2'></i>اختبار ناجح!</h6>
                    <p>تم جلب " . count($test_invoices) . " فاتورة بنجاح.</p>
                  </div>";
            
            echo "<h6>عينة من الفواتير:</h6>";
            echo "<div class='table-responsive'>
                    <table class='table table-sm'>
                        <thead class='table-light'>
                            <tr>
                                <th>Invoice ID</th>
                                <th>رقم الفاتورة</th>
                                <th>الجهاز</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>";
            
            foreach ($test_invoices as $invoice) {
                $status_class = [
                    'pending' => 'warning',
                    'paid' => 'success',
                    'cancelled' => 'danger'
                ][$invoice['payment_status']] ?? 'secondary';
                
                $status_text = [
                    'pending' => 'معلق',
                    'paid' => 'مدفوع',
                    'cancelled' => 'ملغي'
                ][$invoice['payment_status']] ?? $invoice['payment_status'];
                
                echo "<tr>
                        <td><strong>{$invoice['invoice_id']}</strong></td>
                        <td>{$invoice['invoice_number']}</td>
                        <td>" . htmlspecialchars($invoice['device_name']) . "</td>
                        <td>" . htmlspecialchars($invoice['customer_name'] ?: 'غير محدد') . "</td>
                        <td>{$invoice['total_cost']} ج.م</td>
                        <td><span class='badge bg-$status_class'>$status_text</span></td>
                      </tr>";
            }
            
            echo "</tbody></table></div>";
            
        } else {
            echo "<div class='alert alert-info'>
                    <h6><i class='fas fa-info-circle me-2'></i>لا توجد فواتير</h6>
                    <p>لا توجد فواتير في قاعدة البيانات حالياً.</p>
                  </div>";
        }
        
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>فشل في اختبار الفواتير!</h6>
                <p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
              </div>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h4><i class='fas fa-check-circle me-2'></i>تم إصلاح بنية جدول الفواتير! 🎉</h4>
            <p>يمكنك الآن استخدام صفحة الفواتير بشكل طبيعي.</p>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/invoices.php' class='btn btn-success btn-lg me-2'>
                <i class='fas fa-file-invoice me-1'></i>اختبار صفحة الفواتير
            </a>
            <a href='client/sessions.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-play-circle me-1'></i>صفحة الجلسات
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary btn-lg'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
