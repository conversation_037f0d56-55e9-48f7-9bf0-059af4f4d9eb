<?php
/**
 * سكريبت إصلاح قاعدة البيانات
 * يقوم بإضافة الأعمدة والجداول المفقودة تلقائياً
 */

require_once 'config/database.php';

echo "<h1>إصلاح قاعدة البيانات - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$fixes_applied = 0;
$errors = [];

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    echo "<h2>جاري تطبيق الإصلاحات...</h2>";
    
    // 1. إضافة أعمدة مفقودة لجدول sessions
    echo "<h3>1. إصلاح جدول sessions</h3>";
    
    $session_fixes = [
        "ALTER TABLE sessions ADD COLUMN client_id INT NULL" => "إضافة عمود client_id",
        "ALTER TABLE sessions ADD COLUMN total_cost DECIMAL(10,2) DEFAULT 0.00" => "إضافة عمود total_cost",
        "ALTER TABLE sessions ADD COLUMN customer_id INT NULL" => "إضافة عمود customer_id",
        "ALTER TABLE sessions ADD COLUMN created_by INT NULL" => "إضافة عمود created_by",
        "ALTER TABLE sessions ADD COLUMN updated_by INT NULL" => "إضافة عمود updated_by"
    ];
    
    foreach ($session_fixes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: blue;'>ℹ️ $description (موجود مسبقاً)</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ $description: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 2. تحديث client_id في sessions من devices
    echo "<h3>2. تحديث بيانات الجلسات</h3>";
    try {
        $update_sql = "UPDATE sessions s 
                       JOIN devices d ON s.device_id = d.device_id 
                       SET s.client_id = d.client_id 
                       WHERE s.client_id IS NULL";
        $stmt = $pdo->prepare($update_sql);
        $stmt->execute();
        $updated_rows = $stmt->rowCount();
        echo "<p style='color: green;'>✅ تم تحديث $updated_rows جلسة بمعرف العميل الصحيح</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في تحديث الجلسات: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // 3. إضافة أعمدة لجدول cafeteria_items
    echo "<h3>3. إصلاح جدول cafeteria_items</h3>";
    try {
        $pdo->exec("ALTER TABLE cafeteria_items ADD COLUMN client_id INT NOT NULL DEFAULT 1");
        echo "<p style='color: green;'>✅ إضافة عمود client_id لجدول cafeteria_items</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: blue;'>ℹ️ عمود client_id موجود مسبقاً في cafeteria_items</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ خطأ في إضافة عمود client_id: " . $e->getMessage() . "</p>";
        }
    }
    
    // 4. إنشاء جدول session_products إذا لم يكن موجود
    echo "<h3>4. إنشاء جدول session_products</h3>";
    try {
        $session_products_sql = "
            CREATE TABLE IF NOT EXISTS session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            )
        ";
        $pdo->exec($session_products_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول session_products</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ جدول session_products: " . $e->getMessage() . "</p>";
    }
    
    // 5. إنشاء جدول invoices إذا لم يكن موجود
    echo "<h3>5. إنشاء جدول invoices</h3>";
    try {
        $invoices_sql = "
            CREATE TABLE IF NOT EXISTS invoices (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                invoice_number VARCHAR(50) NOT NULL UNIQUE,
                time_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                products_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                client_id INT NOT NULL,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_invoices_session_id (session_id),
                INDEX idx_invoices_client_id (client_id),
                INDEX idx_invoices_invoice_number (invoice_number)
            )
        ";
        $pdo->exec($invoices_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول invoices</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ جدول invoices: " . $e->getMessage() . "</p>";
    }
    
    // 6. إضافة فهارس لتحسين الأداء
    echo "<h3>6. إضافة فهارس لتحسين الأداء</h3>";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_sessions_client_id ON sessions(client_id)" => "فهرس client_id في sessions",
        "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)" => "فهرس status في sessions",
        "CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id)" => "فهرس device_id في sessions",
        "CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status)" => "فهرس مركب في devices"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ $description: " . $e->getMessage() . "</p>";
        }
    }
    
    // 7. التحقق من البيانات
    echo "<h3>7. التحقق من البيانات</h3>";
    
    // عد الجلسات بدون client_id
    $stmt = $pdo->query("SELECT COUNT(*) FROM sessions WHERE client_id IS NULL");
    $sessions_without_client = $stmt->fetchColumn();
    
    if ($sessions_without_client > 0) {
        echo "<p style='color: red;'>⚠️ يوجد $sessions_without_client جلسة بدون معرف عميل</p>";
        
        // محاولة إصلاحها
        try {
            $pdo->exec("UPDATE sessions SET client_id = 1 WHERE client_id IS NULL");
            echo "<p style='color: green;'>✅ تم تعيين معرف العميل الافتراضي للجلسات</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ فشل في إصلاح الجلسات: " . $e->getMessage() . "</p>";
            $errors[] = $e->getMessage();
        }
    } else {
        echo "<p style='color: green;'>✅ جميع الجلسات لها معرف عميل صحيح</p>";
    }
    
    // تأكيد المعاملة
    $pdo->commit();
    
    echo "<h2 style='color: green;'>✅ تم تطبيق الإصلاحات بنجاح!</h2>";
    echo "<p><strong>عدد الإصلاحات المطبقة:</strong> $fixes_applied</p>";
    
    if (count($errors) > 0) {
        echo "<h3 style='color: orange;'>تحذيرات:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: orange;'>$error</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li>تحديث الصفحة وتجربة النظام</li>";
    echo "<li>التحقق من عمل الجلسات بشكل صحيح</li>";
    echo "<li>اختبار إضافة المنتجات للجلسات</li>";
    echo "<li>التأكد من عمل التقارير والإحصائيات</li>";
    echo "</ol>";
    
    echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h2 style='color: red;'>❌ حدث خطأ أثناء تطبيق الإصلاحات</h2>";
    echo "<p style='color: red;'>$e->getMessage()</p>";
    echo "<p>يرجى التحقق من إعدادات قاعدة البيانات والمحاولة مرة أخرى.</p>";
}

echo "</div>";
?>
