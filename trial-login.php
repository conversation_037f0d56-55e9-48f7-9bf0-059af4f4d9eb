<?php
session_start();
require_once 'includes/trial-auth.php';

// إعادة توجيه إذا كان مسجل دخول بالفعل
if (isTrialLoggedIn() && isTrialSessionValid()) {
    header('Location: trial-dashboard.php');
    exit;
}

$error = '';
$success = '';

// التحقق من رسائل الخطأ من URL
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'trial_expired':
            $error = 'انتهت صلاحية التجربة المجانية. يمكنك إنشاء حساب جديد أو الاشتراك في الخدمة الكاملة.';
            break;
        case 'session_invalid':
            $error = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
            break;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        $result = trialLogin($email, $password);
        
        if ($result['success']) {
            header('Location: trial-dashboard.php');
            exit;
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول التجريبي - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .trial-login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }

        .trial-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .trial-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .trial-header p {
            margin: 0;
            opacity: 0.9;
        }

        .trial-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .trial-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-trial {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-trial:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-register {
            background: var(--success-gradient);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .trial-info {
            background: #f8f9fa;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 12px;
            text-align: center;
        }

        .trial-info h6 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .trial-info p {
            color: #4a5568;
            margin: 0;
            font-size: 0.9rem;
        }

        .trial-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #718096;
            font-size: 0.9rem;
        }

        .trial-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .trial-footer a:hover {
            text-decoration: underline;
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            color: #718096;
            font-size: 0.9rem;
        }

        @media (max-width: 576px) {
            .trial-login-container {
                margin: 10px;
            }
            
            .trial-header {
                padding: 1.5rem;
            }
            
            .trial-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="trial-login-container">
        <div class="trial-header">
            <div class="trial-badge">
                <i class="fas fa-gift me-2"></i>
                تجربة مجانية
            </div>
            <h1>
                <i class="fas fa-gamepad me-2"></i>
                PlayGood
            </h1>
            <p>تسجيل الدخول للتجربة المجانية</p>
        </div>

        <form method="POST" class="trial-form">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>

            <button type="submit" class="btn-trial">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>

            <div class="divider">
                <span>أو</span>
            </div>

            <a href="trial-register.php" class="btn btn-register">
                <i class="fas fa-rocket me-2"></i>
                ابدأ تجربة مجانية جديدة
            </a>
        </form>

        <div class="trial-info">
            <h6>
                <i class="fas fa-info-circle me-2"></i>
                معلومات التجربة المجانية
            </h6>
            <p>
                مدة التجربة: 3 ساعات كاملة<br>
                جميع المميزات متاحة<br>
                لا حاجة لبطاقة ائتمان
            </p>
        </div>

        <div class="trial-footer">
            <a href="index.php">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للصفحة الرئيسية
            </a>
            <br><br>
            <a href="client/login.php">تسجيل دخول العملاء</a> | 
            <a href="admin/login.php">تسجيل دخول الإدارة</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
