<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    header('Location: ../login.php');
    exit();
}

// تضمين قاعدة البيانات للتحقق من الصلاحيات
if (!isset($pdo)) {
    require_once __DIR__ . '/../../config/database.php';
}

/**
 * التحقق من صلاحية الوصول لصفحة معينة
 * @param string $page_name اسم الصفحة
 * @return bool هل العميل لديه صلاحية الوصول
 */
function hasPagePermission($page_name) {
    global $pdo;

    if (!isset($_SESSION['client_id'])) {
        return false;
    }

    try {
        // التحقق من وجود جدول صلاحيات الصفحات
        $table_check = $pdo->query("SHOW TABLES LIKE 'client_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، السماح بالوصول لجميع الصفحات
            return true;
        }

        $stmt = $pdo->prepare("
            SELECT
                COALESCE(cpp.is_enabled, cp.is_default) as has_permission
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.page_name = ? AND cp.is_active = TRUE
        ");
        $stmt->execute([$_SESSION['client_id'], $page_name]);
        $result = $stmt->fetch();

        return $result ? (bool)$result['has_permission'] : false;

    } catch (Exception $e) {
        // في حالة حدوث خطأ، السماح بالوصول (للأمان)
        return true;
    }
}

/**
 * التحقق من صلاحية الصفحة الحالية وإعادة التوجيه إذا لم تكن مسموحة
 */
function checkCurrentPagePermission() {
    $current_page = basename($_SERVER['PHP_SELF'], '.php');

    // الصفحات المستثناة من فحص الصلاحيات
    $excluded_pages = ['login', 'logout', 'profile'];

    if (!in_array($current_page, $excluded_pages)) {
        if (!hasPagePermission($current_page)) {
            // إعادة التوجيه إلى لوحة التحكم مع رسالة خطأ
            $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
            header('Location: dashboard.php');
            exit();
        }
    }
}

/**
 * جلب قائمة الصفحات المسموحة للعميل الحالي
 * @return array قائمة الصفحات المسموحة
 */
function getAllowedPages() {
    global $pdo;

    if (!isset($_SESSION['client_id'])) {
        return [];
    }

    try {
        // التحقق من وجود جدول صلاحيات الصفحات
        $table_check = $pdo->query("SHOW TABLES LIKE 'client_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، إرجاع جميع الصفحات الأساسية
            return [
                'dashboard', 'devices', 'sessions', 'customers', 'reports',
                'invoices', 'settings', 'profile'
            ];
        }

        $stmt = $pdo->prepare("
            SELECT cp.page_name, cp.page_label, cp.page_url, cp.page_icon, cp.category
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.is_active = TRUE
            AND COALESCE(cpp.is_enabled, cp.is_default) = TRUE
            ORDER BY cp.category, cp.page_label
        ");
        $stmt->execute([$_SESSION['client_id']]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        // في حالة حدوث خطأ، إرجاع الصفحات الأساسية
        return [
            'dashboard', 'devices', 'sessions', 'customers', 'reports',
            'invoices', 'settings', 'profile'
        ];
    }
}

// فحص صلاحية الصفحة الحالية
checkCurrentPagePermission();
?>