<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// حماية الصفحة
protectAdminPage($pdo);

$page_title = "مراقبة الأمان";

// الحصول على إحصائيات الأمان
$middleware = SecureSessionMiddleware::getInstance($pdo);
$security_stats = $middleware->getSecurityStats();

// جلب سجلات الأمان الأخيرة
try {
    $logs_query = $pdo->prepare("
        SELECT * FROM session_security_logs 
        ORDER BY created_at DESC 
        LIMIT 50
    ");
    $logs_query->execute();
    $security_logs = $logs_query->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $security_logs = [];
}

// جلب الجلسات النشطة
try {
    $sessions_query = $pdo->prepare("
        SELECT ss.*, 
               CASE 
                   WHEN ss.user_type = 'admin' THEN a.full_name
                   WHEN ss.user_type = 'client' THEN c.business_name
                   WHEN ss.user_type = 'employee' THEN e.name
               END as user_name
        FROM secure_sessions ss
        LEFT JOIN admins a ON ss.user_id = a.admin_id AND ss.user_type = 'admin'
        LEFT JOIN clients c ON ss.user_id = c.client_id AND ss.user_type = 'client'
        LEFT JOIN employees e ON ss.user_id = e.id AND ss.user_type = 'employee'
        WHERE ss.is_valid = TRUE
        ORDER BY ss.last_activity DESC
    ");
    $sessions_query->execute();
    $active_sessions = $sessions_query->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $active_sessions = [];
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        مراقبة الأمان
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- إحصائيات الأمان -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $security_stats['active_sessions'] ?? 0; ?></h3>
                                    <p class="mb-0">الجلسات النشطة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $security_stats['attacks_today'] ?? 0; ?></h3>
                                    <p class="mb-0">هجمات اليوم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($active_sessions); ?></h3>
                                    <p class="mb-0">جلسات محمية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($security_logs); ?></h3>
                                    <p class="mb-0">سجلات الأمان</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أنواع الهجمات -->
                    <?php if (!empty($security_stats['attack_types'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>أنواع الهجمات الأكثر شيوعاً</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>نوع الهجوم</th>
                                            <th>عدد المحاولات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($security_stats['attack_types'] as $attack): ?>
                                        <tr>
                                            <td>
                                                <?php 
                                                $attack_names = [
                                                    'hijack_attempt' => 'محاولة سرقة جلسة',
                                                    'invalid_token' => 'رمز أمان غير صحيح',
                                                    'ip_mismatch' => 'عدم تطابق IP',
                                                    'agent_mismatch' => 'تغيير User Agent',
                                                    'expired_session' => 'جلسة منتهية الصلاحية'
                                                ];
                                                echo $attack_names[$attack['attack_type']] ?? $attack['attack_type'];
                                                ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger"><?php echo $attack['count']; ?></span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- الجلسات النشطة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>الجلسات النشطة المحمية</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>النوع</th>
                                            <th>IP Address</th>
                                            <th>آخر نشاط</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($active_sessions as $session): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($session['user_name'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $session['user_type'] === 'admin' ? 'danger' : 
                                                        ($session['user_type'] === 'client' ? 'primary' : 'info'); 
                                                ?>">
                                                    <?php 
                                                    $types = ['admin' => 'مدير', 'client' => 'عميل', 'employee' => 'موظف'];
                                                    echo $types[$session['user_type']] ?? $session['user_type'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($session['ip_address']); ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', strtotime($session['last_activity'])); ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-warning" 
                                                        onclick="terminateSession('<?php echo $session['session_id']; ?>')">
                                                    <i class="fas fa-times"></i> إنهاء
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- سجلات الأمان -->
                    <div class="row">
                        <div class="col-12">
                            <h5>سجلات الأمان الأخيرة</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الوقت</th>
                                            <th>نوع الهجوم</th>
                                            <th>IP Address</th>
                                            <th>التفاصيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($security_logs as $log): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $log['attack_type'] === 'hijack_attempt' ? 'danger' : 'warning'; 
                                                ?>">
                                                    <?php 
                                                    $attack_names = [
                                                        'hijack_attempt' => 'محاولة سرقة جلسة',
                                                        'invalid_token' => 'رمز أمان غير صحيح',
                                                        'ip_mismatch' => 'عدم تطابق IP',
                                                        'agent_mismatch' => 'تغيير User Agent',
                                                        'expired_session' => 'جلسة منتهية الصلاحية'
                                                    ];
                                                    echo $attack_names[$log['attack_type']] ?? $log['attack_type'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                            <td><?php echo htmlspecialchars($log['details']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function terminateSession(sessionId) {
    if (confirm('هل أنت متأكد من إنهاء هذه الجلسة؟')) {
        fetch('api/terminate_session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: sessionId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنهاء الجلسة بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الشبكة');
        });
    }
}

// تحديث الصفحة كل 30 ثانية
setInterval(function() {
    location.reload();
}, 30000);
</script>

<?php require_once 'includes/footer.php'; ?>
