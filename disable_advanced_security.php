<?php
/**
 * تعطيل مؤقت للنظام الأمني المتقدم
 * لحل مشاكل إعادة التوجيه
 */

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تعطيل النظام الأمني المتقدم - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .disable-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; }
    </style>
</head>
<body>
<div class='container py-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-8'>
            <div class='disable-card p-4 shadow'>
                <h1 class='text-center mb-4'>
                    <i class='fas fa-pause-circle text-warning'></i>
                    تعطيل النظام الأمني المتقدم
                </h1>";

// إنشاء نسخة مبسطة من auth_guard.php
$simple_auth_guard = '<?php
/**
 * حارس المصادقة المبسط - Auth Guard (Simple Version)
 * نسخة مبسطة لحل مشاكل إعادة التوجيه
 */

if (!defined("AUTH_GUARD")) {
    define("AUTH_GUARD", true);
}

/**
 * حماية صفحات المدير (مبسط)
 */
function protectAdminPage($pdo) {
    if (!isset($_SESSION["admin_id"])) {
        header("Location: login.php");
        exit;
    }
    return true;
}

/**
 * حماية صفحات العميل (مبسط)
 */
function protectClientPage($pdo) {
    if (!isset($_SESSION["client_id"])) {
        header("Location: login.php");
        exit;
    }
    return true;
}

/**
 * حماية صفحات الموظف (مبسط)
 */
function protectEmployeePage($pdo) {
    if (!isset($_SESSION["employee_id"])) {
        header("Location: employee-login.php");
        exit;
    }
    return true;
}

/**
 * حماية API endpoints (مبسط)
 */
function protectApiEndpoint($pdo, $user_type = "client") {
    $is_valid = false;
    
    switch ($user_type) {
        case "admin":
            $is_valid = isset($_SESSION["admin_id"]);
            break;
        case "client":
            $is_valid = isset($_SESSION["client_id"]);
            break;
        case "employee":
            $is_valid = isset($_SESSION["employee_id"]);
            break;
    }
    
    if (!$is_valid) {
        http_response_code(401);
        header("Content-Type: application/json");
        echo json_encode([
            "success" => false,
            "error" => "غير مصرح لك بالوصول",
            "code" => "INVALID_SESSION"
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    return true;
}

/**
 * تسجيل خروج مبسط
 */
function secureLogout($pdo, $redirect_url = null) {
    session_start();
    session_destroy();
    
    if ($redirect_url) {
        header("Location: " . $redirect_url);
        exit;
    }
}

/**
 * دوال مساعدة فارغة للتوافق
 */
function createSecureUserSession($pdo, $user_id, $user_type) {
    // دالة فارغة للتوافق
    return true;
}

function destroySecureUserSession($pdo) {
    // دالة فارغة للتوافق
    return true;
}

function addSecurityHeaders() {
    // Headers أساسية فقط
    header("X-Frame-Options: DENY");
    header("X-Content-Type-Options: nosniff");
}

// تطبيق headers أساسية
addSecurityHeaders();
';

try {
    // إنشاء نسخة احتياطية من الملف الأصلي
    if (file_exists('includes/auth_guard.php')) {
        copy('includes/auth_guard.php', 'includes/auth_guard_backup.php');
        echo "<p class='text-success'>✅ تم إنشاء نسخة احتياطية من auth_guard.php</p>";
    }
    
    // كتابة النسخة المبسطة
    file_put_contents('includes/auth_guard_simple.php', $simple_auth_guard);
    echo "<p class='text-success'>✅ تم إنشاء auth_guard_simple.php</p>";
    
    // استبدال الملف الأصلي
    file_put_contents('includes/auth_guard.php', $simple_auth_guard);
    echo "<p class='text-success'>✅ تم استبدال auth_guard.php بالنسخة المبسطة</p>";
    
    echo "<div class='alert alert-warning mt-4'>
        <h4>⚠️ تم تعطيل النظام الأمني المتقدم</h4>
        <p>تم استبدال النظام الأمني المتقدم بنسخة مبسطة لحل مشاكل إعادة التوجيه.</p>
        <ul>
            <li>الحماية الأساسية ما زالت تعمل</li>
            <li>تم حفظ نسخة احتياطية في auth_guard_backup.php</li>
            <li>يمكن استعادة النظام المتقدم لاحقاً</li>
        </ul>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <h4>❌ خطأ في التعطيل</h4>
        <p>حدث خطأ: " . $e->getMessage() . "</p>
    </div>";
}

echo "<div class='text-center mt-4'>
    <a href='admin/login.php' class='btn btn-primary me-2'>
        <i class='fas fa-user-shield'></i> اختبار دخول المدير
    </a>
    <a href='client/login.php' class='btn btn-success me-2'>
        <i class='fas fa-store'></i> اختبار دخول العميل
    </a>
    <a href='client/employee-login.php' class='btn btn-info me-2'>
        <i class='fas fa-user-tie'></i> اختبار دخول الموظف
    </a>
</div>";

echo "<div class='mt-4'>
    <h5>خطوات استعادة النظام المتقدم لاحقاً:</h5>
    <ol>
        <li>تشغيل <code>fix_security_tables.php</code> لإنشاء الجداول</li>
        <li>استعادة الملف من <code>includes/auth_guard_backup.php</code></li>
        <li>اختبار النظام باستخدام <code>test_security_system.php</code></li>
    </ol>
</div>";

echo "</div></div></div></div></body></html>";
?>
