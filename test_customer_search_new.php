<?php
/**
 * سكريپت اختبار البحث عن العملاء
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار البحث عن العملاء</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-info text-white'>
            <h3><i class='fas fa-search me-2'></i>اختبار البحث عن العملاء</h3>
        </div>
        <div class='card-body'>";

try {
    // فحص جدول العملاء
    $customers_query = $pdo->query("SELECT customer_id, name, phone, email, client_id FROM customers ORDER BY name LIMIT 10");
    $customers = $customers_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h5>العملاء الموجودين:</h5>";
    if (!empty($customers)) {
        echo "<table class='table table-sm'>
                <thead>
                    <tr><th>ID</th><th>الاسم</th><th>الهاتف</th><th>Client ID</th></tr>
                </thead>
                <tbody>";
        
        foreach ($customers as $customer) {
            echo "<tr>
                    <td>{$customer['customer_id']}</td>
                    <td>" . htmlspecialchars($customer['name']) . "</td>
                    <td>" . htmlspecialchars($customer['phone']) . "</td>
                    <td>{$customer['client_id']}</td>
                  </tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<div class='alert alert-warning'>لا يوجد عملاء</div>";
    }
    
    // نموذج اختبار البحث
    echo "<h5>اختبار البحث:</h5>
          <div class='row'>
              <div class='col-md-8'>
                  <input type='text' id='searchInput' class='form-control' placeholder='ابحث عن عميل...'>
              </div>
              <div class='col-md-4'>
                  <button onclick='testSearch()' class='btn btn-primary w-100'>بحث</button>
              </div>
          </div>
          <div id='searchResults' class='mt-3'></div>";
    
    // اختبار API مباشرة
    if (!empty($customers)) {
        $test_customer = $customers[0];
        $test_query = substr($test_customer['name'], 0, 3);
        
        echo "<h5>اختبار API مباشرة:</h5>";
        echo "<p>البحث عن: <strong>$test_query</strong></p>";
        
        $_SESSION['client_id'] = 1; // للاختبار
        
        $search_term = "%{$test_query}%";
        $search_stmt = $pdo->prepare("
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = ?
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC
            LIMIT 10
        ");
        
        $search_stmt->execute([1, $search_term, $search_term, $search_term]);
        $search_results = $search_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($search_results)) {
            echo "<div class='alert alert-success'>تم العثور على " . count($search_results) . " نتيجة</div>";
            echo "<table class='table table-sm'>
                    <thead>
                        <tr><th>ID</th><th>الاسم</th><th>الهاتف</th></tr>
                    </thead>
                    <tbody>";
            
            foreach ($search_results as $result) {
                echo "<tr>
                        <td>{$result['id']}</td>
                        <td>" . htmlspecialchars($result['name']) . "</td>
                        <td>" . htmlspecialchars($result['phone']) . "</td>
                      </tr>";
            }
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-warning'>لم يتم العثور على نتائج</div>";
        }
    }
    
    echo "<div class='text-center mt-4'>
            <a href='client/dashboard.php' class='btn btn-primary me-2'>لوحة التحكم</a>
            <a href='client/customers.php' class='btn btn-success'>إدارة العملاء</a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "        </div>
    </div>
</div>

<script>
function testSearch() {
    const query = document.getElementById('searchInput').value.trim();
    const resultsDiv = document.getElementById('searchResults');
    
    if (query.length < 2) {
        resultsDiv.innerHTML = '<div class=\"alert alert-warning\">يرجى إدخال على الأقل حرفين</div>';
        return;
    }
    
    resultsDiv.innerHTML = '<div class=\"alert alert-info\">جاري البحث...</div>';
    
    fetch(`client/api/search-customers.php?q=\${encodeURIComponent(query)}`)
        .then(response => response.text())
        .then(text => {
            console.log('Response:', text);
            try {
                const customers = JSON.parse(text);
                
                if (Array.isArray(customers) && customers.length > 0) {
                    let html = '<div class=\"alert alert-success\">تم العثور على ' + customers.length + ' نتيجة:</div>';
                    html += '<div class=\"list-group\">';
                    
                    customers.forEach(customer => {
                        html += `<div class=\"list-group-item\">
                                    <strong>\${customer.name}</strong><br>
                                    <small>\${customer.phone}</small>
                                 </div>`;
                    });
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<div class=\"alert alert-warning\">لم يتم العثور على نتائج</div>';
                }
            } catch (e) {
                resultsDiv.innerHTML = '<div class=\"alert alert-danger\">خطأ في البيانات: ' + text + '</div>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class=\"alert alert-danger\">خطأ في الاتصال: ' + error.message + '</div>';
        });
}
</script>

</body>
</html>";
?>
