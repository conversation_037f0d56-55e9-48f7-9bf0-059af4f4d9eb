<?php
require_once '../../config/database.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

try {
    // تحديد معرف العميل
    $client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
    
    // جلب الإحصائيات
    $stats_sql = "SELECT 
        COUNT(*) as total_products,
        SUM(CASE WHEN stock_quantity <= 0 THEN 1 ELSE 0 END) as out_of_stock,
        SUM(CASE WHEN stock_quantity <= min_stock_level AND stock_quantity > 0 THEN 1 ELSE 0 END) as low_stock,
        SUM(stock_quantity * cost_price) as total_inventory_value,
        SUM(CASE WHEN stock_quantity > min_stock_level THEN 1 ELSE 0 END) as available_products
        FROM cafeteria_items WHERE client_id = ?";
    
    $stats_stmt = $pdo->prepare($stats_sql);
    $stats_stmt->execute([$client_id]);
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب المنتجات التي تحتاج تنبيه
    $alerts_sql = "SELECT name, stock_quantity, min_stock_level 
                   FROM cafeteria_items 
                   WHERE client_id = ? AND (stock_quantity <= 0 OR stock_quantity <= min_stock_level)
                   ORDER BY stock_quantity ASC
                   LIMIT 10";
    
    $alerts_stmt = $pdo->prepare($alerts_sql);
    $alerts_stmt->execute([$client_id]);
    $alerts = $alerts_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب أحدث حركات المخزن
    $movements_sql = "SELECT im.*, ci.name as product_name
                      FROM inventory_movements im
                      JOIN cafeteria_items ci ON im.product_id = ci.id
                      WHERE im.client_id = ?
                      ORDER BY im.created_at DESC
                      LIMIT 5";
    
    $movements_stmt = $pdo->prepare($movements_sql);
    $movements_stmt->execute([$client_id]);
    $recent_movements = $movements_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total_products' => number_format($stats['total_products']),
            'out_of_stock' => number_format($stats['out_of_stock']),
            'low_stock' => number_format($stats['low_stock']),
            'available_products' => number_format($stats['available_products']),
            'total_inventory_value' => number_format($stats['total_inventory_value'], 2)
        ],
        'alerts' => $alerts,
        'recent_movements' => $recent_movements
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ غير متوقع'
    ], JSON_UNESCAPED_UNICODE);
}
?>
