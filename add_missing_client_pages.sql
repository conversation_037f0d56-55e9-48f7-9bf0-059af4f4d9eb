-- إضافة الصفحات المطلوبة لنظام صلاحيات العملاء - PlayGood
-- تشغيل هذا الملف لإضافة الصفحات المطلوبة: الأوردر، الماليات، الإيرادات، المصروفات، الموظفين، الغرف

-- التحقق من وجود الجداول المطلوبة
SELECT 'بدء إضافة الصفحات المطلوبة...' as message;

-- إضافة الصفحات المطلوبة إلى جدول client_pages
INSERT IGNORE INTO client_pages (page_name, page_label, page_url, page_icon, category, description, is_default) VALUES

-- صفحات الأوردر والطلبات
('orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات والأوردرات المستقلة', FALSE),

-- صفحات الماليات والإيرادات والمصروفات
('finances', 'إدارة الماليات', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة الأمور المالية العامة', FALSE),
('revenues', 'الإيرادات', 'revenues.php', 'fas fa-arrow-up', 'finances', 'إدارة وتتبع الإيرادات', FALSE),
('expenses', 'المصروفات', 'expenses.php', 'fas fa-arrow-down', 'finances', 'إدارة وتتبع المصروفات', FALSE),

-- صفحات الموظفين
('employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة بيانات وصلاحيات الموظفين', FALSE),

-- صفحات الغرف
('rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف وقاعات', FALSE);

-- عرض النتائج
SELECT 'تم إضافة الصفحات المطلوبة بنجاح!' as message;

-- عرض الصفحات المضافة حديثاً
SELECT
    page_name as 'اسم الصفحة',
    page_label as 'التسمية',
    category as 'الفئة',
    CASE WHEN is_default = 1 THEN 'نعم' ELSE 'لا' END as 'افتراضية',
    CASE WHEN is_active = 1 THEN 'نشطة' ELSE 'غير نشطة' END as 'الحالة'
FROM client_pages
WHERE page_name IN (
    'orders', 'finances', 'revenues', 'expenses', 'employees', 'rooms'
)
ORDER BY category, page_label;

-- عرض جميع الصفحات المتاحة مجمعة حسب الفئة
SELECT
    category as 'الفئة',
    COUNT(*) as 'عدد الصفحات',
    GROUP_CONCAT(page_label SEPARATOR ', ') as 'الصفحات'
FROM client_pages
WHERE is_active = 1
GROUP BY category
ORDER BY category;
