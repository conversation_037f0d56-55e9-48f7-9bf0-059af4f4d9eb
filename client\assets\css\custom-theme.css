/* CSS مخصص للمظهر - تم إنشاؤه تلقائياً */

:root {
    --custom-primary: #dc3545;
    --custom-primary-light: #ff6878;
    --custom-primary-dark: #a90212;
    --custom-secondary: #6c757d;
    --custom-accent: #fd7e14;
    --custom-accent-light: #ffb147;
    --custom-accent-dark: #ca4b00;
    --custom-primary-rgb: 220, 53, 69;
    --custom-accent-rgb: 253, 126, 20;
}

/* الهيدر والنافبار */
.navbar-dark.bg-primary,
.navbar.bg-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%) !important;
}

.navbar-brand:hover {
    color: var(--custom-accent) !important;
}

.nav-link.active {
    background-color: rgba(var(--custom-accent-rgb), 0.2) !important;
    color: var(--custom-accent) !important;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%);
    border-color: var(--custom-primary);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--custom-primary-dark) 0%, var(--custom-primary) 100%);
    border-color: var(--custom-primary-dark);
}

.btn-success,
.btn-success-gradient {
    background: linear-gradient(135deg, var(--custom-accent) 0%, var(--custom-accent-dark) 100%);
    border-color: var(--custom-accent);
}

.btn-success:hover,
.btn-success-gradient:hover {
    background: linear-gradient(135deg, var(--custom-accent-dark) 0%, var(--custom-accent) 100%);
    border-color: var(--custom-accent-dark);
}

/* البطاقات والعناصر */
.card-header {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-light) 100%);
    color: white;
}

.stats-card {
    border-right-color: var(--custom-primary) !important;
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-right-color: var(--custom-accent) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* النماذج */
.form-control:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

.form-select:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

/* التبويبات */
.nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

.nav-pills .nav-link {
    color: var(--custom-primary);
}

.nav-pills .nav-link:hover {
    background-color: rgba(var(--custom-primary-rgb), 0.1);
}

/* التقدم */
.progress-bar {
    background: linear-gradient(90deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

/* الشارات */
.badge.bg-primary {
    background-color: var(--custom-primary) !important;
}

.badge.bg-success {
    background-color: var(--custom-accent) !important;
}

/* الروابط */
a {
    color: var(--custom-primary);
}

a:hover {
    color: var(--custom-primary-dark);
}

/* تحسينات عامة */
.text-primary {
    color: var(--custom-primary) !important;
}

.text-success {
    color: var(--custom-accent) !important;
}

.section-title {
    color: var(--custom-primary);
}
