<?php
/**
 * اختبار دالة حذف المنتجات من الجلسات
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار دالة حذف المنتجات من الجلسات</h1>";
echo "<hr>";

try {
    echo "<h2>1. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $pdo->exec("INSERT INTO sessions (device_id, start_time, status, client_id) VALUES (1, NOW(), 'active', 1)");
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: green;'>✅ تم العثور على جلسة نشطة: $session_id</p>";
    }
    
    // البحث عن منتج
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        // إنشاء منتج تجريبي
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)");
        $product_id = $pdo->lastInsertId();
        $product_name = 'منتج تجريبي';
        $product_price = 5.00;
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: green;'>✅ تم العثور على منتج: $product_name ($product_id)</p>";
    }
    
    echo "<h2>2. إضافة منتجات للجلسة</h2>";
    
    // حذف أي منتجات موجودة مسبقاً
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ?")->execute([$session_id]);
    
    // إضافة عدة سجلات لنفس المنتج
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, 2, $product_price]); // سجل 1
    $stmt->execute([$session_id, $product_id, 1, $product_price]); // سجل 2
    $stmt->execute([$session_id, $product_id, 3, $product_price]); // سجل 3
    
    echo "<p style='color: green;'>✅ تم إضافة 3 سجلات للمنتج في الجلسة</p>";
    
    // عرض المنتجات الحالية
    $stmt = $pdo->prepare("SELECT * FROM session_products WHERE session_id = ?");
    $stmt->execute([$session_id]);
    $products = $stmt->fetchAll();
    
    echo "<p><strong>المنتجات الحالية في الجلسة:</strong></p>";
    echo "<ul>";
    foreach ($products as $product_record) {
        echo "<li>ID: {$product_record['id']}, المنتج: {$product_record['product_id']}, الكمية: {$product_record['quantity']}, السعر: {$product_record['price']}</li>";
    }
    echo "</ul>";
    
    echo "<h2>3. اختبار API الحذف</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/delete_session_product.php';
    
    $delete_data = [
        'session_id' => $session_id,
        'product_id' => $product_id
    ];
    
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    echo "<p><strong>البيانات المرسلة:</strong> " . json_encode($delete_data, JSON_UNESCAPED_UNICODE) . "</p>";
    
    // اختبار API عبر cURL
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($delete_data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p><strong>كود الاستجابة HTTP:</strong> $http_code</p>";
    echo "<p><strong>استجابة API:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($response) . "</pre>";
    
    // تحليل الاستجابة
    $result = json_decode($response, true);
    if ($result) {
        if ($result['success']) {
            echo "<p style='color: green;'>✅ نجح الحذف!</p>";
            echo "<p><strong>عدد السجلات المحذوفة:</strong> {$result['deleted_rows']}</p>";
            echo "<p><strong>التكلفة الجديدة:</strong> {$result['total_cost']} ج.م</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل الحذف: {$result['error']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ خطأ في تحليل استجابة JSON</p>";
    }
    
    echo "<h2>4. فحص النتيجة</h2>";
    
    // فحص المنتجات المتبقية
    $stmt = $pdo->prepare("SELECT * FROM session_products WHERE session_id = ?");
    $stmt->execute([$session_id]);
    $remaining_products = $stmt->fetchAll();
    
    echo "<p><strong>المنتجات المتبقية في الجلسة:</strong></p>";
    if (count($remaining_products) > 0) {
        echo "<ul>";
        foreach ($remaining_products as $product_record) {
            echo "<li>ID: {$product_record['id']}, المنتج: {$product_record['product_id']}, الكمية: {$product_record['quantity']}, السعر: {$product_record['price']}</li>";
        }
        echo "</ul>";
        echo "<p style='color: orange;'>⚠️ لا تزال هناك منتجات متبقية - قد تكون هناك مشكلة في الحذف</p>";
    } else {
        echo "<p style='color: green;'>✅ تم حذف جميع المنتجات بنجاح</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حذف المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار JavaScript</h2>
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="testDeleteFunction()">
                    <i class="fas fa-trash me-2"></i>اختبار دالة الحذف
                </button>
            </div>
            <div class="col-md-6">
                <div id="test-results"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    const testProductId = <?php echo $product_id; ?>;
    
    function testDeleteFunction() {
        const resultDiv = document.getElementById('test-results');
        resultDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</p>';
        
        // محاكاة دالة deleteEditSessionProduct
        console.log('Testing delete function with:', { sessionId: testSessionId, productId: testProductId });
        
        if (!testSessionId || !testProductId) {
            resultDiv.innerHTML = '<p style="color: red;">❌ بيانات الاختبار غير مكتملة</p>';
            return;
        }
        
        const deleteData = {
            session_id: parseInt(testSessionId),
            product_id: parseInt(testProductId)
        };
        
        console.log('Sending delete request:', deleteData);
        
        fetch('client/api/delete_session_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(deleteData)
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div style="color: green;">
                            <p>✅ نجح الحذف!</p>
                            <p><strong>الرسالة:</strong> ${data.message}</p>
                            <p><strong>عدد السجلات المحذوفة:</strong> ${data.deleted_rows}</p>
                            <p><strong>التكلفة الجديدة:</strong> ${data.total_cost} ج.م</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ فشل الحذف: ${data.error}</p>`;
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                resultDiv.innerHTML = `
                    <div style="color: red;">
                        <p>❌ خطأ في تحليل الاستجابة</p>
                        <details>
                            <summary>تفاصيل الخطأ</summary>
                            <pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = `<p style="color: red;">❌ خطأ في الشبكة: ${error.message}</p>`;
        });
    }
    </script>
</body>
</html>
