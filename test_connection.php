<?php
// اختبار اتصال قاعدة البيانات
try {
    require_once 'config/database.php';
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!<br>";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "✅ تم تنفيذ الاستعلام بنجاح: " . $result['test'] . "<br>";
    
    // اختبار وجود الجداول الأساسية
    $tables = ['clients', 'devices', 'customers', 'sessions'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ الجدول $table موجود<br>";
        } else {
            echo "❌ الجدول $table غير موجود<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage();
}
?>
