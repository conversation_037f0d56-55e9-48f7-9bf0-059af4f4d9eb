<?php
/**
 * إصلاح مشكلة حذف الأجهزة - PlayGood
 * تشغيل هذا الملف لإصلاح قيود المرجعية التي تمنع حذف الأجهزة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة حذف الأجهزة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card shadow'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح مشكلة حذف الأجهزة</h3>
        </div>
        <div class='card-body'>";

try {
    require_once 'config/database.php';
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            بدء عملية إصلاح قيود قاعدة البيانات...
          </div>";
    
    $fixes_applied = 0;
    $errors = [];
    
    // 1. إزالة القيد المرجعي الحالي
    echo "<h5>1. إزالة القيد المرجعي الحالي</h5>";
    try {
        $pdo->exec("ALTER TABLE sessions DROP FOREIGN KEY sessions_ibfk_1");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إزالة القيد المرجعي القديم</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "check that column/key exists") !== false) {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>القيد المرجعي غير موجود مسبقاً</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في إزالة القيد: " . $e->getMessage() . "</p>";
            $errors[] = $e->getMessage();
        }
    }
    
    // 2. تعديل عمود device_id ليقبل NULL
    echo "<h5>2. تعديل عمود device_id ليقبل NULL</h5>";
    try {
        $pdo->exec("ALTER TABLE sessions MODIFY COLUMN device_id INT NULL");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم تعديل عمود device_id ليقبل NULL</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في تعديل العمود: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // 3. إضافة القيد المرجعي الجديد
    echo "<h5>3. إضافة القيد المرجعي الجديد</h5>";
    try {
        $pdo->exec("ALTER TABLE sessions 
                   ADD CONSTRAINT sessions_device_fk 
                   FOREIGN KEY (device_id) REFERENCES devices(device_id) 
                   ON DELETE SET NULL ON UPDATE CASCADE");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إضافة القيد المرجعي الجديد مع SET NULL</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في إضافة القيد الجديد: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // 4. تنظيف البيانات
    echo "<h5>4. تنظيف البيانات</h5>";
    try {
        $stmt = $pdo->prepare("UPDATE sessions 
                              SET device_id = NULL 
                              WHERE device_id NOT IN (SELECT device_id FROM devices)");
        $stmt->execute();
        $affected = $stmt->rowCount();
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم تنظيف {$affected} جلسة بجهاز غير موجود</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في تنظيف البيانات: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // 5. إضافة فهارس لتحسين الأداء
    echo "<h5>5. إضافة فهارس لتحسين الأداء</h5>";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id)" => "فهرس device_id في sessions",
        "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)" => "فهرس status في sessions",
        "CREATE INDEX IF NOT EXISTS idx_sessions_client_device ON sessions(client_id, device_id)" => "فهرس مركب في sessions",
        "CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status)" => "فهرس مركب في devices"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم إضافة {$description}</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>{$description}: " . $e->getMessage() . "</p>";
        }
    }
    
    // 6. اختبار الإصلاح
    echo "<h5>6. اختبار الإصلاح</h5>";
    try {
        // فحص بنية الجدول
        $stmt = $pdo->query("DESCRIBE sessions");
        $columns = $stmt->fetchAll();
        
        $device_id_column = null;
        foreach ($columns as $column) {
            if ($column['Field'] === 'device_id') {
                $device_id_column = $column;
                break;
            }
        }
        
        if ($device_id_column && $device_id_column['Null'] === 'YES') {
            echo "<p class='success'><i class='fas fa-check me-2'></i>عمود device_id يقبل NULL بنجاح</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>عمود device_id لا يزال لا يقبل NULL</p>";
        }
        
        // فحص القيود المرجعية
        $stmt = $pdo->query("SELECT CONSTRAINT_NAME, DELETE_RULE 
                            FROM information_schema.REFERENTIAL_CONSTRAINTS 
                            WHERE TABLE_NAME = 'sessions' AND REFERENCED_TABLE_NAME = 'devices'");
        $constraints = $stmt->fetchAll();
        
        if (!empty($constraints)) {
            foreach ($constraints as $constraint) {
                if ($constraint['DELETE_RULE'] === 'SET NULL') {
                    echo "<p class='success'><i class='fas fa-check me-2'></i>القيد المرجعي يدعم SET NULL: {$constraint['CONSTRAINT_NAME']}</p>";
                } else {
                    echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>القيد المرجعي لا يدعم SET NULL: {$constraint['CONSTRAINT_NAME']}</p>";
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
    
    // النتيجة النهائية
    echo "<div class='alert alert-" . (empty($errors) ? 'success' : 'warning') . " mt-4'>
            <h5><i class='fas fa-" . (empty($errors) ? 'check-circle' : 'exclamation-triangle') . " me-2'></i>النتيجة النهائية</h5>
            <p>تم تطبيق {$fixes_applied} إصلاح بنجاح</p>";
    
    if (!empty($errors)) {
        echo "<p>عدد الأخطاء: " . count($errors) . "</p>";
        echo "<details><summary>تفاصيل الأخطاء</summary><ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul></details>";
    }
    
    echo "</div>";
    
    if (empty($errors)) {
        echo "<div class='alert alert-info'>
                <i class='fas fa-info-circle me-2'></i>
                يمكنك الآن الذهاب إلى <a href='client/devices.php' class='alert-link'>صفحة الأجهزة</a> وتجربة حذف جهاز.
              </div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-circle me-2'></i>
            خطأ عام: " . htmlspecialchars($e->getMessage()) . "
          </div>";
}

echo "        </div>
    </div>
</div>
</body>
</html>";
?>
