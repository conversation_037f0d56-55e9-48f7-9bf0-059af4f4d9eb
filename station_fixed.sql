-- =====================================================
-- قاعدة بيانات نظام إدارة مراكز الألعاب - Station System
-- =====================================================
-- تم إنشاؤه: 20 يونيو 2025
-- الإصدار: 2.0 - محسن ومنظم
-- المطور: فريق التطوير
-- الوصف: نظام شامل لإدارة مراكز الألعاب والكافتيريا والموظفين
-- =====================================================

-- إعدادات قاعدة البيانات
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- إعدادات الترميز
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- إنشاء قاعدة البيانات
-- =====================================================
CREATE DATABASE IF NOT EXISTS `station` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `station`;

-- =====================================================
-- الجداول الأساسية - Core Tables
-- =====================================================

-- جدول المديرين
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(255) NOT NULL UNIQUE,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`admin_id`),
  INDEX `idx_admin_username` (`username`),
  INDEX `idx_admin_email` (`email`),
  INDEX `idx_admin_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المديرين';

-- جدول إعدادات النظام
CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  INDEX `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات النظام العامة';

-- جدول خطط الاشتراك
CREATE TABLE `subscription_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL UNIQUE,
  `plan_name_ar` varchar(100) NOT NULL,
  `plan_description` text DEFAULT NULL,
  `plan_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `plan_duration_days` int(11) NOT NULL DEFAULT 30,
  `max_devices` int(11) DEFAULT 5,
  `max_employees` int(11) DEFAULT 3,
  `max_customers` int(11) DEFAULT 100,
  `max_products` int(11) DEFAULT 50,
  `features` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`plan_id`),
  INDEX `idx_plan_name` (`plan_name`),
  INDEX `idx_plan_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='خطط الاشتراك';

-- جدول العملاء (أصحاب المحلات)
CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `is_active` tinyint(1) DEFAULT 1,
  `backup_enabled` tinyint(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`client_id`),
  INDEX `idx_client_email` (`email`),
  INDEX `idx_client_phone` (`phone`),
  INDEX `idx_client_active` (`is_active`),
  INDEX `idx_client_subscription` (`subscription_plan`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العملاء (أصحاب المحلات)';

-- =====================================================
-- جداول إدارة الأجهزة والغرف
-- =====================================================

-- جدول الغرف
CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_description` text DEFAULT NULL,
  `capacity` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`room_id`),
  INDEX `idx_room_client` (`client_id`),
  INDEX `idx_room_active` (`is_active`),
  CONSTRAINT `fk_room_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الغرف';

-- جدول الأجهزة
CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC','Nintendo') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 0.00,
  `single_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الفردي',
  `multi_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الجماعي',
  `status` enum('available','occupied','maintenance','inactive') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `specifications` json DEFAULT NULL COMMENT 'مواصفات الجهاز',
  `maintenance_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`device_id`),
  INDEX `idx_device_client` (`client_id`),
  INDEX `idx_device_status` (`status`),
  INDEX `idx_device_type` (`device_type`),
  INDEX `idx_device_room` (`room_id`),
  CONSTRAINT `fk_device_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_device_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأجهزة';

-- =====================================================
-- جداول إدارة العملاء والجلسات
-- =====================================================

-- جدول العملاء (زوار المحل)
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `address` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `total_visits` int(11) DEFAULT 0,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `last_visit` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`customer_id`),
  INDEX `idx_customer_client` (`client_id`),
  INDEX `idx_customer_phone` (`phone`),
  INDEX `idx_customer_active` (`is_active`),
  CONSTRAINT `fk_customer_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول عملاء المحل';

-- جدول الجلسات
CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_type` enum('hourly','single','multi') DEFAULT 'hourly',
  `game_type` varchar(100) DEFAULT NULL,
  `players_count` int(11) DEFAULT 1,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` timestamp NULL DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `hourly_rate` decimal(8,2) DEFAULT 0.00,
  `time_cost` decimal(10,2) DEFAULT 0.00,
  `products_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`session_id`),
  INDEX `idx_session_client` (`client_id`),
  INDEX `idx_session_device` (`device_id`),
  INDEX `idx_session_customer` (`customer_id`),
  INDEX `idx_session_status` (`payment_status`),
  INDEX `idx_session_date` (`start_time`),
  CONSTRAINT `fk_session_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_session_device` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_session_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول جلسات اللعب';

-- =====================================================
-- جداول الكافتيريا والمنتجات
-- =====================================================

-- جدول فئات المنتجات
CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-tag',
  `color` varchar(7) DEFAULT '#007bff',
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`category_id`),
  INDEX `idx_category_client` (`client_id`),
  INDEX `idx_category_active` (`is_active`),
  CONSTRAINT `fk_category_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='فئات المنتجات';

-- جدول منتجات الكافتيريا
CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `stock_quantity` int(11) DEFAULT 0 COMMENT 'الكمية المتوفرة',
  `min_stock_level` int(11) DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
  `max_stock_level` int(11) DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
  `unit` varchar(20) DEFAULT 'قطعة' COMMENT 'وحدة القياس',
  `barcode` varchar(100) DEFAULT NULL COMMENT 'الباركود',
  `supplier` varchar(200) DEFAULT NULL COMMENT 'المورد',
  `status` enum('available','low_stock','out_of_stock','discontinued') DEFAULT 'available',
  `image_url` varchar(500) DEFAULT NULL,
  `last_restock_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_item_client` (`client_id`),
  INDEX `idx_item_category` (`category_id`),
  INDEX `idx_item_status` (`status`),
  INDEX `idx_item_barcode` (`barcode`),
  CONSTRAINT `fk_item_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_item_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='منتجات الكافتيريا';

-- جدول منتجات الجلسة
CREATE TABLE `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_session_product_session` (`session_id`),
  INDEX `idx_session_product_product` (`product_id`),
  CONSTRAINT `fk_session_product_session` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_session_product_product` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='منتجات الجلسات';

-- =====================================================
-- جداول إدارة الموظفين
-- =====================================================

-- جدول الموظفين
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text DEFAULT NULL,
  `role` enum('manager','cashier','waiter','cleaner','technician') NOT NULL DEFAULT 'cashier',
  `salary` decimal(10,2) NOT NULL DEFAULT 0.00,
  `hire_date` date NOT NULL,
  `birth_date` date DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `emergency_phone` varchar(20) DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'صلاحيات مخصصة',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_username_client` (`client_id`, `username`),
  INDEX `idx_employee_client` (`client_id`),
  INDEX `idx_employee_role` (`role`),
  INDEX `idx_employee_active` (`is_active`),
  CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الموظفين';

-- جدول الصلاحيات
CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(100) NOT NULL UNIQUE,
  `permission_label` varchar(200) NOT NULL,
  `permission_category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`permission_id`),
  INDEX `idx_permission_category` (`permission_category`),
  INDEX `idx_permission_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات النظام';

-- جدول صلاحيات الموظفين
CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_permission` (`employee_id`, `permission_id`),
  INDEX `idx_emp_perm_employee` (`employee_id`),
  INDEX `idx_emp_perm_permission` (`permission_id`),
  CONSTRAINT `fk_emp_perm_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_emp_perm_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات الموظفين';

-- =====================================================
-- جداول الفواتير والمالية
-- =====================================================

-- جدول الفواتير
CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `session_id` int(11) DEFAULT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `time_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `products_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `payment_status` enum('pending','paid','cancelled','refunded') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`invoice_id`),
  UNIQUE KEY `uk_invoice_number_client` (`client_id`, `invoice_number`),
  INDEX `idx_invoice_client` (`client_id`),
  INDEX `idx_invoice_session` (`session_id`),
  INDEX `idx_invoice_customer` (`customer_id`),
  INDEX `idx_invoice_status` (`payment_status`),
  INDEX `idx_invoice_date` (`created_at`),
  CONSTRAINT `fk_invoice_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invoice_session` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_invoice_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الفواتير';

-- جدول إعدادات الفواتير
CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `company_logo` varchar(500) DEFAULT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `show_barcode` tinyint(1) DEFAULT 0,
  `auto_print` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invoice_settings_client` (`client_id`),
  CONSTRAINT `fk_invoice_settings_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الفواتير';

-- =====================================================
-- جداول المصروفات والإيرادات
-- =====================================================

-- جدول أنواع المصروفات
CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-money-bill',
  `color` varchar(7) DEFAULT '#dc3545',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_expense_type_client` (`client_id`),
  INDEX `idx_expense_type_active` (`is_active`),
  CONSTRAINT `fk_expense_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع المصروفات';

-- جدول المصروفات
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_expense_client` (`client_id`),
  INDEX `idx_expense_type` (`expense_type_id`),
  INDEX `idx_expense_date` (`expense_date`),
  CONSTRAINT `fk_expense_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_expense_type` FOREIGN KEY (`expense_type_id`) REFERENCES `expense_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المصروفات';

-- جدول أنواع الإيرادات
CREATE TABLE `income_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-coins',
  `color` varchar(7) DEFAULT '#28a745',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_income_type_client` (`client_id`),
  INDEX `idx_income_type_active` (`is_active`),
  CONSTRAINT `fk_income_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الإيرادات';

-- جدول الإيرادات الإضافية
CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_income_client` (`client_id`),
  INDEX `idx_income_type` (`income_type_id`),
  INDEX `idx_income_date` (`income_date`),
  CONSTRAINT `fk_income_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_income_type` FOREIGN KEY (`income_type_id`) REFERENCES `income_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الإيرادات الإضافية';

-- =====================================================
-- جداول إدارة المخزون
-- =====================================================

-- جدول حركات المخزون
CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','expired','damaged','returned') NOT NULL,
  `quantity` int(11) NOT NULL,
  `previous_quantity` int(11) NOT NULL,
  `new_quantity` int(11) NOT NULL,
  `unit_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `reference_type` enum('purchase','sale','session','order','manual','system') DEFAULT 'manual',
  `reference_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_movement_client` (`client_id`),
  INDEX `idx_movement_product` (`product_id`),
  INDEX `idx_movement_type` (`movement_type`),
  INDEX `idx_movement_date` (`created_at`),
  CONSTRAINT `fk_movement_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_movement_product` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حركات المخزون';

-- =====================================================
-- جداول الإعدادات والتخصيص
-- =====================================================

-- جدول إعدادات العملاء
CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `uk_business_setting` (`client_id`, `setting_key`),
  INDEX `idx_business_setting_client` (`client_id`),
  CONSTRAINT `fk_business_setting_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات العملاء';

-- جدول إعدادات الثيم
CREATE TABLE `client_theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `primary_color` varchar(7) DEFAULT '#0d6efd',
  `secondary_color` varchar(7) DEFAULT '#6c757d',
  `accent_color` varchar(7) DEFAULT '#20c997',
  `background_color` varchar(7) DEFAULT '#ffffff',
  `text_color` varchar(7) DEFAULT '#212529',
  `header_style` enum('top','sidebar') DEFAULT 'top',
  `sidebar_position` enum('right','left') DEFAULT 'right',
  `theme_mode` enum('light','dark','auto') DEFAULT 'light',
  `font_family` varchar(100) DEFAULT 'Cairo, sans-serif',
  `font_size` enum('small','medium','large') DEFAULT 'medium',
  `custom_css` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_theme_client` (`client_id`),
  CONSTRAINT `fk_theme_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الثيم';

-- =====================================================
-- البيانات الأساسية - Initial Data
-- =====================================================

-- إدراج المدير الرئيسي
INSERT INTO `admins` (`username`, `email`, `password_hash`, `full_name`, `role`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'super_admin', 1);

-- إدراج الإعدادات الأساسية
INSERT INTO `admin_settings` (`setting_key`, `setting_value`, `setting_description`) VALUES
('system_name', 'نظام إدارة مراكز الألعاب', 'اسم النظام'),
('system_version', '2.0', 'إصدار النظام'),
('backup_enabled', '1', 'تفعيل النسخ الاحتياطي'),
('system_maintenance', '0', 'وضع الصيانة'),
('max_backup_files', '10', 'الحد الأقصى لملفات النسخ الاحتياطي'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
('language', 'ar', 'اللغة الافتراضية'),
('currency', 'SAR', 'العملة الافتراضية'),
('date_format', 'Y-m-d', 'تنسيق التاريخ'),
('time_format', 'H:i', 'تنسيق الوقت');

-- إدراج خطط الاشتراك
INSERT INTO `subscription_plans` (`plan_name`, `plan_name_ar`, `plan_description`, `plan_price`, `plan_duration_days`, `max_devices`, `max_employees`, `max_customers`, `max_products`) VALUES
('basic', 'الخطة الأساسية', 'خطة مناسبة للمحلات الصغيرة', 99.00, 30, 5, 3, 100, 50),
('premium', 'الخطة المتقدمة', 'خطة مناسبة للمحلات المتوسطة', 199.00, 30, 15, 10, 500, 200),
('enterprise', 'خطة المؤسسات', 'خطة مناسبة للمحلات الكبيرة', 399.00, 30, -1, -1, -1, -1);

-- إدراج الصلاحيات الأساسية
INSERT INTO `permissions` (`permission_name`, `permission_label`, `permission_category`, `description`) VALUES
('dashboard_view', 'عرض لوحة التحكم', 'dashboard', 'صلاحية عرض لوحة التحكم الرئيسية'),
('devices_view', 'عرض الأجهزة', 'devices', 'صلاحية عرض قائمة الأجهزة'),
('devices_add', 'إضافة أجهزة', 'devices', 'صلاحية إضافة أجهزة جديدة'),
('devices_edit', 'تعديل الأجهزة', 'devices', 'صلاحية تعديل بيانات الأجهزة'),
('devices_delete', 'حذف الأجهزة', 'devices', 'صلاحية حذف الأجهزة'),
('sessions_view', 'عرض الجلسات', 'sessions', 'صلاحية عرض جلسات اللعب'),
('sessions_start', 'بدء الجلسات', 'sessions', 'صلاحية بدء جلسات جديدة'),
('sessions_end', 'إنهاء الجلسات', 'sessions', 'صلاحية إنهاء الجلسات'),
('customers_view', 'عرض العملاء', 'customers', 'صلاحية عرض قائمة العملاء'),
('customers_add', 'إضافة عملاء', 'customers', 'صلاحية إضافة عملاء جدد'),
('customers_edit', 'تعديل العملاء', 'customers', 'صلاحية تعديل بيانات العملاء'),
('customers_delete', 'حذف العملاء', 'customers', 'صلاحية حذف العملاء'),
('products_view', 'عرض المنتجات', 'products', 'صلاحية عرض منتجات الكافتيريا'),
('products_add', 'إضافة منتجات', 'products', 'صلاحية إضافة منتجات جديدة'),
('products_edit', 'تعديل المنتجات', 'products', 'صلاحية تعديل المنتجات'),
('products_delete', 'حذف المنتجات', 'products', 'صلاحية حذف المنتجات'),
('invoices_view', 'عرض الفواتير', 'invoices', 'صلاحية عرض الفواتير'),
('invoices_create', 'إنشاء فواتير', 'invoices', 'صلاحية إنشاء فواتير جديدة'),
('reports_view', 'عرض التقارير', 'reports', 'صلاحية عرض التقارير المالية'),
('settings_view', 'عرض الإعدادات', 'settings', 'صلاحية عرض إعدادات النظام'),
('settings_edit', 'تعديل الإعدادات', 'settings', 'صلاحية تعديل إعدادات النظام');

-- =====================================================
-- إنهاء المعاملة
-- =====================================================

COMMIT;

-- استعادة إعدادات الترميز
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
