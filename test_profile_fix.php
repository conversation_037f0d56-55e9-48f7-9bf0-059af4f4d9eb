<?php
/**
 * اختبار إصلاح صفحة الملف الشخصي - PlayGood
 * هذا الملف لاختبار أن مشكلة employee_id تم حلها
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح صفحة الملف الشخصي</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .test-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container py-4'>
    <div class='card'>
        <div class='card-header bg-success text-white'>
            <h3 class='mb-0'><i class='fas fa-user-check me-2'></i>اختبار إصلاح صفحة الملف الشخصي</h3>
        </div>
        <div class='card-body'>";

try {
    // 1. اختبار الاستعلامات المحدثة
    echo "<div class='test-section'>
            <h5><i class='fas fa-database me-2'></i>اختبار الاستعلامات المحدثة</h5>";
    
    // اختبار استعلام جلب بيانات الموظف
    try {
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ? LIMIT 1");
        $stmt->execute([1]);
        $employee = $stmt->fetch();
        
        if ($employee) {
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>
                    استعلام جلب بيانات الموظف يعمل بشكل صحيح
                    <br><small>تم العثور على الموظف: {$employee['name']}</small>
                  </div>";
        } else {
            echo "<div class='test-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    لا يوجد موظف بمعرف 1 للاختبار
                  </div>";
        }
    } catch (PDOException $e) {
        echo "<div class='test-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام جلب بيانات الموظف: {$e->getMessage()}
              </div>";
    }
    
    // اختبار استعلام تحديث بيانات الموظف
    try {
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE id = ? LIMIT 1");
        $stmt->execute([1]);
        
        if ($stmt->fetch()) {
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>
                    استعلام تحديث بيانات الموظف جاهز للعمل
                  </div>";
        }
    } catch (PDOException $e) {
        echo "<div class='test-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام تحديث بيانات الموظف: {$e->getMessage()}
              </div>";
    }
    
    // اختبار استعلام تحديث كلمة المرور
    try {
        $stmt = $pdo->prepare("SELECT password_hash FROM employees WHERE id = ? LIMIT 1");
        $stmt->execute([1]);
        
        if ($stmt->fetch()) {
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>
                    استعلام تحديث كلمة المرور يعمل بشكل صحيح
                  </div>";
        }
    } catch (PDOException $e) {
        echo "<div class='test-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام تحديث كلمة المرور: {$e->getMessage()}
              </div>";
    }
    echo "</div>";
    
    // 2. اختبار محاكاة جلسة موظف
    echo "<div class='test-section'>
            <h5><i class='fas fa-user-cog me-2'></i>اختبار محاكاة جلسة موظف</h5>";
    
    // محاكاة جلسة موظف للاختبار
    $test_employee_id = 1;
    $_SESSION['employee_id'] = $test_employee_id;
    $_SESSION['employee_name'] = 'موظف تجريبي';
    $_SESSION['employee_role'] = 'manager';
    $_SESSION['client_id'] = 1;
    
    echo "<div class='test-info'>
            <i class='fas fa-info-circle me-2'></i>
            تم إنشاء جلسة تجريبية للموظف بمعرف: $test_employee_id
          </div>";
    
    // اختبار جلب بيانات الموظف مع الجلسة
    try {
        $stmt = $pdo->prepare("
            SELECT e.*, c.business_name as client_business_name, e.role
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.id = ?
        ");
        $stmt->execute([$test_employee_id]);
        $employee_data = $stmt->fetch();
        
        if ($employee_data) {
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>
                    استعلام جلب بيانات الموظف مع معلومات العميل يعمل
                    <br><small>الموظف: {$employee_data['name']} - الدور: {$employee_data['role']}</small>
                  </div>";
        } else {
            echo "<div class='test-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    لم يتم العثور على بيانات الموظف أو العميل
                  </div>";
        }
    } catch (PDOException $e) {
        echo "<div class='test-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام بيانات الموظف والعميل: {$e->getMessage()}
              </div>";
    }
    echo "</div>";
    
    // 3. اختبار الملفات المحدثة
    echo "<div class='test-section'>
            <h5><i class='fas fa-file-check me-2'></i>اختبار الملفات المحدثة</h5>";
    
    // فحص ملف profile.php
    if (file_exists('client/profile.php')) {
        $profile_content = file_get_contents('client/profile.php');
        
        // تحقق من الإصلاحات
        $has_correct_update = strpos($profile_content, 'WHERE id = ?') !== false;
        $has_correct_password_field = strpos($profile_content, 'password_hash') !== false;
        
        if ($has_correct_update && $has_correct_password_field) {
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>
                    ملف profile.php تم تحديثه بشكل صحيح
                    <br><small>• يستخدم 'id' في استعلامات التحديث</small>
                    <br><small>• يستخدم 'password_hash' لكلمات المرور</small>
                  </div>";
        } else {
            echo "<div class='test-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    ملف profile.php قد يحتاج مراجعة إضافية
                  </div>";
        }
    } else {
        echo "<div class='test-error'>
                <i class='fas fa-times me-2'></i>ملف profile.php غير موجود
              </div>";
    }
    echo "</div>";
    
    // 4. اختبار التوافق مع نظام الصلاحيات
    echo "<div class='test-section'>
            <h5><i class='fas fa-shield-alt me-2'></i>اختبار التوافق مع نظام الصلاحيات</h5>";
    
    // تحقق من وجود جداول الصلاحيات
    $permissions_tables = ['permissions', 'pages', 'employee_permissions', 'employee_pages'];
    $all_tables_exist = true;
    
    foreach ($permissions_tables as $table) {
        try {
            $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<div class='test-success'>
                    <i class='fas fa-check me-2'></i>جدول $table متوفر
                  </div>";
        } catch (PDOException $e) {
            $all_tables_exist = false;
            echo "<div class='test-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>جدول $table غير متوفر
                  </div>";
        }
    }
    
    if ($all_tables_exist) {
        echo "<div class='test-success'>
                <i class='fas fa-thumbs-up me-2'></i>
                <strong>جميع جداول الصلاحيات متوفرة - النظام متكامل!</strong>
              </div>";
    }
    echo "</div>";
    
    // 5. تنظيف الجلسة التجريبية
    unset($_SESSION['employee_id']);
    unset($_SESSION['employee_name']);
    unset($_SESSION['employee_role']);
    unset($_SESSION['client_id']);
    
    echo "<div class='test-section'>
            <h5><i class='fas fa-check-circle me-2'></i>نتيجة الاختبار النهائية</h5>";
    
    echo "<div class='test-success'>
            <i class='fas fa-trophy me-2'></i>
            <strong>تم إصلاح مشكلة employee_id بنجاح!</strong>
            <br><br>
            <strong>الإصلاحات المطبقة:</strong>
            <br>• تحديث استعلامات التحديث لتستخدم 'id' بدلاً من 'employee_id'
            <br>• تحديث استعلامات كلمة المرور لتستخدم 'password_hash'
            <br>• التأكد من التوافق مع نظام الصلاحيات الجديد
            <br>• اختبار جميع الاستعلامات والتأكد من عملها
          </div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-error'>
            <i class='fas fa-exclamation-triangle me-2'></i>خطأ عام في الاختبار: {$e->getMessage()}
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='client/profile.php' class='btn btn-success me-2' target='_blank'>
                <i class='fas fa-user me-2'></i>اختبار صفحة الملف الشخصي
            </a>
            <a href='client/employee_permissions.php?id=1' class='btn btn-primary me-2' target='_blank'>
                <i class='fas fa-shield-alt me-2'></i>اختبار صفحة الصلاحيات
            </a>
            <a href='client/employees.php' class='btn btn-secondary'>
                <i class='fas fa-users me-2'></i>صفحة الموظفين
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
