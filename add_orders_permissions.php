<?php
require_once 'config/database.php';

echo "<h2>إضافة صلاحيات الأوردرات لنظام الموظفين</h2>";

try {
    echo "<p>جاري إضافة صلاحيات الأوردرات...</p>";
    
    // إضافة صلاحيات الأوردرات
    $pdo->exec("
        INSERT IGNORE INTO permissions (permission_name, permission_label, permission_description, category) VALUES
        ('manage_orders', 'إدارة الأوردرات', 'إنشاء وتعديل وحذف الأوردرات المستقلة', 'orders'),
        ('view_orders', 'عرض الأوردرات', 'عرض قائمة الأوردرات فقط', 'orders')
    ");
    echo "<p style='color: green;'>✓ تم إضافة صلاحيات الأوردرات</p>";
    
    // إضافة صفحة الأوردرات
    $pdo->exec("
        INSERT IGNORE INTO pages (page_name, page_label, page_url, page_icon, category) VALUES
        ('orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders')
    ");
    echo "<p style='color: green;'>✓ تم إضافة صفحة الأوردرات</p>";
    
    // تحديث صلاحيات الأدوار التقليدية
    echo "<p>جاري تحديث ملف employee-auth.php...</p>";
    
    $auth_file = 'client/includes/employee-auth.php';
    $auth_content = file_get_contents($auth_file);
    
    // إضافة صلاحيات الأوردرات للمدير
    if (strpos($auth_content, 'manage_orders') === false) {
        $auth_content = str_replace(
            "'manage_employees',
            'manage_settings'",
            "'manage_employees',
            'manage_orders',
            'manage_settings'"
        , $auth_content);
        
        // إضافة صلاحيات الأوردرات للكاشير
        $auth_content = str_replace(
            "'manage_sessions',
            'manage_cafeteria',
            'view_reports'",
            "'manage_sessions',
            'manage_cafeteria',
            'manage_orders',
            'view_reports'"
        , $auth_content);
        
        // إضافة صلاحيات عرض الأوردرات للويتر
        $auth_content = str_replace(
            "'manage_cafeteria',
            'view_sessions'",
            "'manage_cafeteria',
            'view_orders',
            'view_sessions'"
        , $auth_content);
        
        file_put_contents($auth_file, $auth_content);
        echo "<p style='color: green;'>✓ تم تحديث صلاحيات الأدوار التقليدية</p>";
    } else {
        echo "<p style='color: orange;'>- صلاحيات الأوردرات موجودة مسبقاً في الأدوار التقليدية</p>";
    }
    
    // التحقق من النتائج
    echo "<h3>التحقق من النتائج:</h3>";
    
    // عرض الصلاحيات المضافة
    $stmt = $pdo->query("SELECT * FROM permissions WHERE category = 'orders'");
    $orders_permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>صلاحيات الأوردرات:</strong></p>";
    echo "<ul>";
    foreach ($orders_permissions as $permission) {
        echo "<li>{$permission['permission_label']} ({$permission['permission_name']})</li>";
    }
    echo "</ul>";
    
    // عرض الصفحات المضافة
    $stmt = $pdo->query("SELECT * FROM pages WHERE category = 'orders'");
    $orders_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>صفحات الأوردرات:</strong></p>";
    echo "<ul>";
    foreach ($orders_pages as $page) {
        echo "<li>{$page['page_label']} ({$page['page_name']})</li>";
    }
    echo "</ul>";
    
    // إضافة صلاحيات الأوردرات لجميع المديرين الحاليين (الذين يستخدمون الصلاحيات المخصصة)
    echo "<h3>تحديث صلاحيات المديرين الحاليين:</h3>";
    
    $stmt = $pdo->query("
        SELECT e.id, e.name, e.role 
        FROM employees e 
        WHERE e.role = 'manager' AND e.custom_permissions = 1 AND e.is_active = 1
    ");
    $managers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($managers)) {
        // جلب معرفات صلاحيات الأوردرات
        $stmt = $pdo->query("SELECT permission_id FROM permissions WHERE permission_name IN ('manage_orders', 'view_orders')");
        $permission_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // جلب معرف صفحة الأوردرات
        $stmt = $pdo->query("SELECT page_id FROM pages WHERE page_name = 'orders'");
        $page_id = $stmt->fetchColumn();
        
        foreach ($managers as $manager) {
            // إضافة الصلاحيات
            foreach ($permission_ids as $permission_id) {
                $stmt = $pdo->prepare("
                    INSERT IGNORE INTO employee_permissions (employee_id, permission_id) 
                    VALUES (?, ?)
                ");
                $stmt->execute([$manager['id'], $permission_id]);
            }
            
            // إضافة الصفحة
            if ($page_id) {
                $stmt = $pdo->prepare("
                    INSERT IGNORE INTO employee_pages (employee_id, page_id) 
                    VALUES (?, ?)
                ");
                $stmt->execute([$manager['id'], $page_id]);
            }
            
            echo "<p style='color: green;'>✓ تم تحديث صلاحيات المدير: {$manager['name']}</p>";
        }
    } else {
        echo "<p style='color: orange;'>- لا يوجد مديرين يستخدمون الصلاحيات المخصصة</p>";
    }
    
    echo "<h2 style='color: green;'>✅ تم إضافة صلاحيات الأوردرات بنجاح!</h2>";
    echo "<p><strong>الصلاحيات المضافة:</strong></p>";
    echo "<ul>";
    echo "<li><strong>manage_orders:</strong> إدارة الأوردرات (للمديرين والكاشيرين)</li>";
    echo "<li><strong>view_orders:</strong> عرض الأوردرات (للويترز)</li>";
    echo "</ul>";
    
    echo "<p><a href='client/orders.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الأوردرات</a></p>";
    echo "<p><a href='client/employees.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة صلاحيات الموظفين</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ أثناء إضافة الصلاحيات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
