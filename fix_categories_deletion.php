<?php
/**
 * إصلاح مشكلة حذف التصنيفات في صفحة الكافتيريا
 * يحل مشكلة القيود المرجعية التي تمنع حذف التصنيفات
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشكلة حذف التصنيفات - PlayGood</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".container { max-width: 800px; margin: 20px auto; padding: 20px; }";
echo ".card { border: none; box-shadow: 0 0 15px rgba(0,0,0,0.1); }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h1 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح مشكلة حذف التصنيفات</h1>";
echo "</div>";
echo "<div class='card-body'>";

$fixes_applied = 0;
$errors = [];

try {
    echo "<h2><i class='fas fa-database me-2'></i>1. فحص بنية قاعدة البيانات</h2>";
    
    // فحص جدول categories
    echo "<h5>فحص جدول categories:</h5>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>جدول categories موجود</p>";
        
        // فحص هيكل الجدول
        $columns = $pdo->query("DESCRIBE categories")->fetchAll();
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول categories غير موجود!</p>";
        $errors[] = "جدول categories غير موجود";
    }
    
    // فحص جدول cafeteria_items
    echo "<h5>فحص جدول cafeteria_items:</h5>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'cafeteria_items'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>جدول cafeteria_items موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول cafeteria_items غير موجود!</p>";
        $errors[] = "جدول cafeteria_items غير موجود";
    }
    
    echo "<h2><i class='fas fa-link me-2'></i>2. فحص القيود المرجعية</h2>";
    
    // فحص القيود المرجعية الحالية
    $constraints = $pdo->query("
        SELECT
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
        AND (TABLE_NAME = 'cafeteria_items' OR REFERENCED_TABLE_NAME = 'categories')
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ")->fetchAll();
    
    echo "<h5>القيود المرجعية الموجودة:</h5>";
    if (count($constraints) > 0) {
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>اسم القيد</th><th>الجدول</th><th>العمود</th><th>الجدول المرجعي</th><th>العمود المرجعي</th></tr></thead>";
        echo "<tbody>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>" . $constraint['CONSTRAINT_NAME'] . "</td>";
            echo "<td>" . $constraint['TABLE_NAME'] . "</td>";
            echo "<td>" . $constraint['COLUMN_NAME'] . "</td>";
            echo "<td>" . $constraint['REFERENCED_TABLE_NAME'] . "</td>";
            echo "<td>" . $constraint['REFERENCED_COLUMN_NAME'] . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد قيود مرجعية</p>";
    }
    
    echo "<h2><i class='fas fa-wrench me-2'></i>3. إصلاح القيود المرجعية</h2>";
    
    // البحث عن القيد المرجعي المشكل
    $problematic_constraint = null;
    foreach ($constraints as $constraint) {
        if ($constraint['TABLE_NAME'] == 'cafeteria_items' &&
            $constraint['REFERENCED_TABLE_NAME'] == 'categories') {
            $problematic_constraint = $constraint['CONSTRAINT_NAME'];
            break;
        }
    }
    
    if ($problematic_constraint) {
        echo "<h5>إزالة القيد المرجعي المشكل:</h5>";
        try {
            $pdo->exec("ALTER TABLE cafeteria_items DROP FOREIGN KEY $problematic_constraint");
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم إزالة القيد المرجعي '$problematic_constraint'</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في إزالة القيد: " . $e->getMessage() . "</p>";
            $errors[] = $e->getMessage();
        }
        
        echo "<h5>إضافة قيد مرجعي محسن:</h5>";
        try {
            // إضافة قيد جديد يسمح بـ SET NULL عند حذف التصنيف
            $pdo->exec("
                ALTER TABLE cafeteria_items 
                ADD CONSTRAINT fk_cafeteria_category 
                FOREIGN KEY (category_id) REFERENCES categories(category_id) 
                ON DELETE SET NULL ON UPDATE CASCADE
            ");
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم إضافة قيد مرجعي محسن يسمح بحذف التصنيفات</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
            // قد يكون العمود category_id غير موجود، لذا سنتجاهل هذا الخطأ
        }
    } else {
        echo "<p class='success'><i class='fas fa-check me-2'></i>لا توجد قيود مرجعية مشكلة</p>";
    }
    
    echo "<h2><i class='fas fa-database me-2'></i>4. فحص بنية البيانات</h2>";
    
    // فحص كيفية ربط المنتجات بالتصنيفات
    echo "<h5>فحص طريقة ربط المنتجات بالتصنيفات:</h5>";
    $sample_items = $pdo->query("SELECT id, name, category, category_id FROM cafeteria_items LIMIT 5")->fetchAll();
    
    if (count($sample_items) > 0) {
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>معرف المنتج</th><th>اسم المنتج</th><th>التصنيف (نص)</th><th>معرف التصنيف</th></tr></thead>";
        echo "<tbody>";
        foreach ($sample_items as $item) {
            echo "<tr>";
            echo "<td>" . $item['id'] . "</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . htmlspecialchars($item['category'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($item['category_id'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // تحديد طريقة الربط المستخدمة
        $uses_category_name = false;
        $uses_category_id = false;
        
        foreach ($sample_items as $item) {
            if (!empty($item['category'])) $uses_category_name = true;
            if (!empty($item['category_id'])) $uses_category_id = true;
        }
        
        if ($uses_category_name && !$uses_category_id) {
            echo "<p class='success'><i class='fas fa-info me-2'></i>النظام يستخدم أسماء التصنيفات (category) وليس معرفات التصنيفات</p>";
            echo "<p class='success'><i class='fas fa-check me-2'></i>هذا يعني أن حذف التصنيفات يجب أن يعمل بشكل طبيعي</p>";
        } elseif ($uses_category_id) {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>النظام يستخدم معرفات التصنيفات - قد تحتاج لتحديث البيانات</p>";
        }
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد منتجات في الكافتيريا</p>";
    }
    
    echo "<h2><i class='fas fa-test-tube me-2'></i>5. اختبار حذف التصنيفات</h2>";
    
    // إنشاء تصنيف تجريبي
    echo "<h5>إنشاء تصنيف تجريبي:</h5>";
    try {
        $test_category_name = 'تصنيف تجريبي ' . date('H:i:s');
        $stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
        $stmt->execute([$test_category_name]);
        $test_category_id = $pdo->lastInsertId();
        
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إنشاء تصنيف تجريبي: '$test_category_name' (معرف: $test_category_id)</p>";
        
        // محاولة حذف التصنيف التجريبي
        echo "<h5>اختبار حذف التصنيف:</h5>";
        $stmt = $pdo->prepare("DELETE FROM categories WHERE category_id = ? AND client_id = 1");
        $stmt->execute([$test_category_id]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم حذف التصنيف التجريبي بنجاح!</p>";
            echo "<p class='success'><i class='fas fa-thumbs-up me-2'></i><strong>مشكلة حذف التصنيفات تم حلها!</strong></p>";
            $fixes_applied++;
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في حذف التصنيف التجريبي</p>";
            $errors[] = "فشل في حذف التصنيف التجريبي";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في اختبار حذف التصنيف: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
} catch (Exception $e) {
    echo "<h2 class='error'><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ</h2>";
    echo "<p class='error'>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getFile() . " في السطر " . $e->getLine() . "</p>";
    $errors[] = $e->getMessage();
}

// ملخص النتائج
echo "<div class='mt-4 p-3 border rounded'>";
echo "<h3><i class='fas fa-clipboard-check me-2'></i>ملخص النتائج</h3>";
echo "<p><strong>الإصلاحات المطبقة:</strong> $fixes_applied</p>";
echo "<p><strong>الأخطاء:</strong> " . count($errors) . "</p>";

if (count($errors) > 0) {
    echo "<h5 class='error'>الأخطاء:</h5>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li class='error'>$error</li>";
    }
    echo "</ul>";
}

if ($fixes_applied > 0 && count($errors) == 0) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>تم الإصلاح بنجاح!</strong> يمكنك الآن حذف التصنيفات في صفحة الكافتيريا.";
    echo "</div>";
} elseif (count($errors) > 0) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "<strong>تم الإصلاح جزئياً.</strong> قد تحتاج لمراجعة الأخطاء أعلاه.";
    echo "</div>";
}

echo "<h3><i class='fas fa-arrow-right me-2'></i>الخطوات التالية</h3>";
echo "<p><a href='client/cafeteria.php' class='btn btn-primary'><i class='fas fa-coffee me-2'></i>اختبار صفحة الكافتيريا</a></p>";
echo "<p><a href='client/dashboard.php' class='btn btn-success'><i class='fas fa-tachometer-alt me-2'></i>العودة للوحة التحكم</a></p>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
