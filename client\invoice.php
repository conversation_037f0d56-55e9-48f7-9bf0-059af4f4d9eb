<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../includes/billing_helper.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

$page_title = "فاتورة الجلسة";
$active_page = "sessions";

// التحقق من وجود معرف الفاتورة أو الجلسة
$invoice_id = isset($_GET['id']) ? intval($_GET['id']) : null;
$session_id = isset($_GET['session_id']) ? intval($_GET['session_id']) : null;

if (!$invoice_id && !$session_id) {
    $_SESSION['error'] = "معرف الفاتورة أو الجلسة مطلوب";
    header('Location: sessions.php');
    exit;
}

try {
    // جلب بيانات الفاتورة والجلسة
    if ($invoice_id) {
        // جلب من جدول الفواتير
        $stmt = $pdo->prepare("
            SELECT
                i.*,
                s.session_id,
                s.start_time,
                s.end_time,
                s.notes,
                d.device_name,
                d.device_type,
                d.hourly_rate,
                r.room_name,
                c.name as customer_name,
                c.phone as customer_phone,
                c.email as customer_email,
                cl.business_name,
                cl.address as business_address,
                cl.phone as business_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            LEFT JOIN clients cl ON d.client_id = cl.client_id
            WHERE i.invoice_id = ? AND d.client_id = ?
        ");
        $stmt->execute([$invoice_id, $client_id]);
    } else {
        // إنشاء فاتورة من بيانات الجلسة مباشرة
        $stmt = $pdo->prepare("
            SELECT
                s.session_id,
                s.start_time,
                s.end_time,
                s.notes,
                s.total_cost,
                d.device_name,
                d.device_type,
                d.hourly_rate,
                r.room_name,
                c.name as customer_name,
                c.phone as customer_phone,
                c.email as customer_email,
                cl.business_name,
                cl.address as business_address,
                cl.phone as business_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            LEFT JOIN clients cl ON d.client_id = cl.client_id
            WHERE s.session_id = ? AND d.client_id = ? AND s.status = 'completed'
        ");
        $stmt->execute([$session_id, $client_id]);
    }

    $invoice_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$invoice_data) {
        $_SESSION['error'] = "لم يتم العثور على الفاتورة";
        header('Location: sessions.php');
        exit;
    }

    // جلب منتجات الجلسة
    $products_stmt = $pdo->prepare("
        SELECT
            sp.*,
            ci.name as product_name,
            ci.category,
            (sp.quantity * sp.price) as total_price
        FROM session_products sp
        JOIN cafeteria_items ci ON sp.product_id = ci.id
        WHERE sp.session_id = ?
        ORDER BY ci.category, ci.name
    ");
    $products_stmt->execute([$invoice_data['session_id']]);
    $session_products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب إعدادات الفاتورة
    $settings_stmt = $pdo->prepare("
        SELECT * FROM invoice_settings WHERE client_id = ?
    ");
    $settings_stmt->execute([$client_id]);
    $invoice_settings = $settings_stmt->fetch(PDO::FETCH_ASSOC);

    // إعدادات افتراضية إذا لم توجد
    if (!$invoice_settings) {
        $invoice_settings = [
            'header_color' => '#dc3545',
            'footer_text' => 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
            'footer_color' => '#000000',
            'company_address' => 'شارع فريد - الأحساء',
            'company_phone' => '01026362111',
            'show_qr_code' => true
        ];
    }

    // حساب التكاليف بناءً على إعدادات العميل
    $duration_minutes = $invoice_data['duration_minutes'];
    $time_cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $invoice_data['hourly_rate']);
    $products_cost = array_sum(array_column($session_products, 'total_price'));
    $total_cost = $time_cost + $products_cost;

    // رقم الفاتورة
    $invoice_number = isset($invoice_data['invoice_number'])
        ? $invoice_data['invoice_number']
        : date('Ymd') . str_pad($invoice_data['session_id'], 4, '0', STR_PAD_LEFT);

} catch (PDOException $e) {
    $_SESSION['error'] = "حدث خطأ في جلب بيانات الفاتورة: " . $e->getMessage();
    header('Location: sessions.php');
    exit;
}

require_once 'includes/header.php';
?>

<!-- إضافة خط Cairo العربي وملفات CSS و JS المخصصة -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
<link href="assets/css/receipt-style.css" rel="stylesheet">
<link href="assets/css/thermal-print.css" rel="stylesheet" media="print">
<script src="assets/js/receipt-enhanced.js" defer></script>

<style>
:root {
    --header-color: <?php echo $invoice_settings['header_color']; ?>;
    --footer-color: <?php echo $invoice_settings['footer_color']; ?>;
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --border-color: #bdc3c7;
    --light-bg: #ecf0f1;
}

/* تصميم الفاتورة الاحترافي - شكل ريسيت */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    direction: rtl;
    margin: 0;
    padding: 0;
}

.invoice-wrapper {
    min-height: 100vh;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.invoice-container {
    max-width: 400px;
    width: 100%;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    position: relative;
}

/* رأس الفاتورة */
.receipt-header {
    background: linear-gradient(135deg, var(--header-color) 0%, #2c3e50 100%);
    color: white;
    padding: 25px 20px;
    text-align: center;
    position: relative;
}

.receipt-header::before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 20px;
    background: white;
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
}

.receipt-title {
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.receipt-number {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
}

/* معلومات الشركة */
.company-section {
    text-align: center;
    padding: 30px 20px 20px;
    border-bottom: 2px dashed var(--border-color);
}

.company-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--header-color) 0%, var(--accent-color) 100%);
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.company-name {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0 0 5px 0;
}

.company-details {
    font-size: 12px;
    color: #7f8c8d;
    line-height: 1.4;
}

/* تفاصيل الفاتورة */
.receipt-details {
    padding: 20px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ecf0f1;
    font-size: 14px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--secondary-color);
    font-weight: 600;
    flex: 1;
}

.detail-value {
    color: var(--primary-color);
    font-weight: bold;
    text-align: left;
    direction: ltr;
}

/* قسم المنتجات */
.products-section {
    margin: 20px 0;
    padding: 15px;
    background: var(--light-bg);
    border-radius: 10px;
}

.products-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0 0 15px 0;
    text-align: center;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #d5dbdb;
    font-size: 13px;
}

.product-item:last-child {
    border-bottom: none;
}

.product-name {
    color: var(--secondary-color);
    flex: 1;
}

.product-qty {
    color: var(--accent-color);
    font-weight: bold;
    margin: 0 10px;
}

.product-price {
    color: var(--primary-color);
    font-weight: bold;
}

/* المجموع الكلي */
.total-section {
    background: linear-gradient(135deg, var(--header-color) 0%, #2c3e50 100%);
    color: white;
    padding: 20px;
    margin: 20px 0;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.total-label {
    font-size: 16px;
    margin: 0 0 5px 0;
}

.total-amount {
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* تذييل الفاتورة */
.receipt-footer {
    text-align: center;
    padding: 20px;
    border-top: 2px dashed var(--border-color);
}

.footer-text {
    color: var(--footer-color);
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 15px 0;
    line-height: 1.4;
}

.qr-section {
    margin-top: 20px;
}

.qr-code {
    width: 80px;
    height: 80px;
    background: #000;
    margin: 0 auto 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
    text-align: center;
    line-height: 1.2;
}

.contact-info {
    font-size: 11px;
    color: #7f8c8d;
    line-height: 1.3;
}

/* أزرار الإجراءات */
.receipt-actions {
    padding: 20px;
    background: #f8f9fa;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    margin: 5px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.btn-print {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

/* تصميم الطباعة */
@media print {
    .no-print { display: none !important; }

    body {
        background: white !important;
        font-size: 12px !important;
        margin: 0 !important;
        padding: 0 !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    .invoice-wrapper {
        padding: 0 !important;
        background: white !important;
        min-height: auto !important;
    }

    .invoice-container {
        max-width: none !important;
        width: 100% !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
    }

    .receipt-header {
        background: var(--header-color) !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    .total-section {
        background: var(--header-color) !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    .company-logo {
        background: var(--header-color) !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    .receipt-actions {
        display: none !important;
    }

    .products-section {
        background: #f8f9fa !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .invoice-wrapper {
        padding: 10px;
    }

    .invoice-container {
        max-width: 100%;
    }

    .receipt-title {
        font-size: 20px;
    }

    .total-amount {
        font-size: 24px;
    }

    .action-btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}
</style>

<!-- أزرار التحكم -->
<div class="no-print" style="padding: 20px; background: #f8f9fa;">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="sessions.php" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-1"></i>العودة للجلسات
                </a>
                <a href="invoices.php" class="btn btn-info">
                    <i class="fas fa-list me-1"></i>جميع الفواتير
                </a>
            </div>
            <div>
                <button onclick="window.print()" class="btn btn-primary me-2">
                    <i class="fas fa-print me-1"></i>طباعة عادية
                </button>
                <button onclick="printThermal()" class="btn btn-info me-2">
                    <i class="fas fa-receipt me-1"></i>طباعة حرارية
                </button>
                <button onclick="openInvoiceSettings()" class="btn btn-warning me-2">
                    <i class="fas fa-cog me-1"></i>إعدادات الفاتورة
                </button>
                <button onclick="shareReceipt()" class="btn btn-secondary me-2">
                    <i class="fas fa-share-alt me-1"></i>مشاركة
                </button>
                <?php if (isset($invoice_data['id'])): ?>
                    <button onclick="showPaymentStatusModal(<?php echo $invoice_data['id']; ?>)" class="btn btn-success">
                        <i class="fas fa-credit-card me-1"></i>تحديث الدفع
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- الفاتورة الاحترافية -->
<div class="invoice-wrapper">
    <div class="invoice-container receipt-container receipt-animate receipt-shadow-medium receipt-transition">
        <!-- رأس الفاتورة -->
        <div class="receipt-header receipt-gradient-primary print-colors">
            <h1 class="receipt-title arabic-text">فاتورة مبيعات</h1>
            <p class="receipt-number number-display">رقم الفاتورة: <?php echo $invoice_number; ?></p>
        </div>

        <!-- معلومات الشركة -->
        <div class="company-section">
            <div class="company-logo receipt-gradient-accent print-colors">
                <i class="fas fa-gamepad"></i>
            </div>
            <h2 class="company-name arabic-text"><?php echo htmlspecialchars($invoice_data['business_name'] ?? 'مركز الألعاب'); ?></h2>
            <div class="company-details arabic-text">
                <?php if ($invoice_data['business_address']): ?>
                    <i class="fas fa-map-marker-alt receipt-icon"></i><?php echo htmlspecialchars($invoice_data['business_address']); ?><br>
                <?php endif; ?>
                <?php if ($invoice_data['business_phone']): ?>
                    <i class="fas fa-phone receipt-icon"></i>هاتف: <?php echo htmlspecialchars($invoice_data['business_phone']); ?><br>
                <?php endif; ?>
                <i class="fas fa-calendar receipt-icon"></i>التاريخ: <?php echo date('Y/m/d - H:i', strtotime($invoice_data['start_time'])); ?>
            </div>
        </div>

        <!-- تفاصيل الفاتورة -->
        <div class="receipt-details receipt-spacing">
            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-user receipt-icon"></i>العميل:</span>
                <span class="detail-value arabic-text"><?php echo $invoice_data['customer_name'] ? htmlspecialchars($invoice_data['customer_name']) : 'عميل غير مسجل'; ?></span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-gamepad receipt-icon"></i>نوع الجهاز:</span>
                <span class="detail-value arabic-text"><?php echo htmlspecialchars($invoice_data['device_type']); ?></span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-desktop receipt-icon"></i>اسم الجهاز:</span>
                <span class="detail-value arabic-text"><?php echo htmlspecialchars($invoice_data['device_name']); ?></span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-play receipt-icon"></i>وقت البدء:</span>
                <span class="detail-value number-display"><?php echo date('H:i', strtotime($invoice_data['start_time'])); ?></span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-stop receipt-icon"></i>وقت الانتهاء:</span>
                <span class="detail-value number-display"><?php echo date('H:i', strtotime($invoice_data['end_time'])); ?></span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-clock receipt-icon"></i>مدة اللعب:</span>
                <span class="detail-value number-display"><?php echo $duration_minutes; ?> دقيقة</span>
            </div>

            <div class="detail-row receipt-interactive">
                <span class="detail-label arabic-text"><i class="fas fa-coins receipt-icon"></i>تكلفة اللعب:</span>
                <span class="detail-value number-display receipt-status-paid"><?php echo number_format($time_cost, 2); ?> جنيه</span>
            </div>

            <!-- قسم المنتجات إذا وجدت -->
            <?php if (!empty($session_products)): ?>
                <div class="products-section receipt-borders receipt-shadow-light">
                    <h3 class="products-title arabic-text"><i class="fas fa-coffee receipt-icon"></i>المشروبات والمأكولات</h3>
                    <?php foreach ($session_products as $product): ?>
                        <div class="product-item receipt-interactive">
                            <span class="product-name arabic-text"><?php echo htmlspecialchars($product['product_name']); ?></span>
                            <span class="product-qty number-display">×<?php echo $product['quantity']; ?></span>
                            <span class="product-price number-display receipt-status-paid"><?php echo number_format($product['total_price'], 2); ?> جنيه</span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- المجموع الكلي -->
        <div class="total-section receipt-gradient-primary print-colors receipt-pulse">
            <p class="total-label arabic-text"><i class="fas fa-calculator receipt-icon"></i>المبلغ الإجمالي</p>
            <h2 class="total-amount number-display"><?php echo number_format($total_cost, 2); ?> جنيه</h2>
        </div>

        <!-- تذييل الفاتورة -->
        <div class="receipt-footer">
            <p class="footer-text arabic-text receipt-spacing" style="color: <?php echo $invoice_settings['footer_color']; ?>;">
                <i class="fas fa-heart receipt-icon"></i><?php echo htmlspecialchars($invoice_settings['footer_text']); ?>
            </p>

            <!-- رمز QR والمعلومات الإضافية -->
            <?php if ($invoice_settings['show_qr_code']): ?>
                <div class="qr-section">
                    <div class="qr-code receipt-shadow-light">
                        QR CODE<br>
                        ████████<br>
                        ██&nbsp;&nbsp;██&nbsp;&nbsp;██<br>
                        ██&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;██<br>
                        ██&nbsp;&nbsp;██&nbsp;&nbsp;██<br>
                        ████████
                    </div>
                    <div class="contact-info arabic-text">
                        <i class="fas fa-map-marker-alt receipt-icon"></i>العنوان: <?php echo htmlspecialchars($invoice_settings['company_address'] ?? 'شارع فريد - الأحساء'); ?><br>
                        <i class="fas fa-phone receipt-icon"></i>الهاتف: <?php echo htmlspecialchars($invoice_settings['company_phone'] ?? '01026362111'); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="receipt-actions no-print">
            <a href="https://wa.me/?text=فاتورة رقم <?php echo urlencode($invoice_number); ?> - المبلغ: <?php echo number_format($total_cost, 2); ?> جنيه"
               class="action-btn btn-whatsapp receipt-transition" target="_blank">
                <i class="fab fa-whatsapp"></i>
                مشاركة عبر واتساب
            </a>
            <button onclick="window.print()" class="action-btn btn-print receipt-transition">
                <i class="fas fa-print"></i>
                طباعة الفاتورة
            </button>
        </div>
    </div>
</div>

<style media="print">
    @page { size: auto; margin: 20mm; }
    .btn { display: none; }
    body { print-color-adjust: exact; -webkit-print-color-adjust: exact; }
</style>

<script>
// عرض نافذة تحديث حالة الدفع
function showPaymentStatusModal(invoiceId) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'paymentStatusModal';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث حالة الدفع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p class="mb-4">اختر حالة الدفع الجديدة:</p>
                        <div class="d-grid gap-3">
                            <button class="btn btn-warning btn-lg" onclick="setPaymentStatus(${invoiceId}, 'pending')">
                                <i class="fas fa-clock me-2"></i>معلق
                            </button>
                            <button class="btn btn-success btn-lg" onclick="setPaymentStatus(${invoiceId}, 'paid')">
                                <i class="fas fa-check-circle me-2"></i>تم الدفع
                            </button>
                            <button class="btn btn-danger btn-lg" onclick="setPaymentStatus(${invoiceId}, 'cancelled')">
                                <i class="fas fa-times-circle me-2"></i>ملغي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// عرض نافذة إعدادات الفاتورة
function openInvoiceSettings() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'invoiceSettingsModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعدادات الفاتورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="invoiceSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="headerColor" class="form-label">لون رأس الفاتورة</label>
                                    <input type="color" class="form-control form-control-color" id="headerColor"
                                           value="<?php echo $invoice_settings['header_color']; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="footerColor" class="form-label">لون نص التذييل</label>
                                    <input type="color" class="form-control form-control-color" id="footerColor"
                                           value="<?php echo $invoice_settings['footer_color']; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="footerText" class="form-label">نص التذييل</label>
                            <textarea class="form-control" id="footerText" rows="3"><?php echo htmlspecialchars($invoice_settings['footer_text']); ?></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="companyAddress" class="form-label">عنوان الشركة</label>
                                    <input type="text" class="form-control" id="companyAddress"
                                           value="<?php echo htmlspecialchars($invoice_settings['company_address'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="companyPhone" class="form-label">هاتف الشركة</label>
                                    <input type="text" class="form-control" id="companyPhone"
                                           value="<?php echo htmlspecialchars($invoice_settings['company_phone'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showQrCode"
                                       <?php echo $invoice_settings['show_qr_code'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="showQrCode">
                                    إظهار رمز QR
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveInvoiceSettings()">حفظ الإعدادات</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// حفظ إعدادات الفاتورة
function saveInvoiceSettings() {
    const settings = {
        header_color: document.getElementById('headerColor').value,
        footer_color: document.getElementById('footerColor').value,
        footer_text: document.getElementById('footerText').value,
        company_address: document.getElementById('companyAddress').value,
        company_phone: document.getElementById('companyPhone').value,
        show_qr_code: document.getElementById('showQrCode').checked
    };

    fetch('api/update_invoice_settings.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + (data.error || 'فشل في حفظ الإعدادات'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function setPaymentStatus(invoiceId, status) {
    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('paymentStatusModal'));
    if (modal) modal.hide();

    fetch('api/update_payment_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            invoice_id: invoiceId,
            payment_status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث حالة الدفع بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + (data.error || 'فشل في تحديث حالة الدفع'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// تحسين مظهر الطباعة
window.addEventListener('beforeprint', function() {
    // إضافة كلاس للطباعة
    document.body.classList.add('print-optimized');

    // إخفاء العناصر غير المرغوب فيها عند الطباعة
    const noprint = document.querySelectorAll('.no-print');
    noprint.forEach(el => el.style.display = 'none');

    // تحسين الألوان للطباعة
    const receiptContainer = document.querySelector('.receipt-container');
    if (receiptContainer) {
        receiptContainer.classList.add('print-colors');
    }
});

window.addEventListener('afterprint', function() {
    // إزالة كلاس الطباعة
    document.body.classList.remove('print-optimized');

    // إظهار العناصر مرة أخرى بعد الطباعة
    const noprint = document.querySelectorAll('.no-print');
    noprint.forEach(el => el.style.display = '');

    // إزالة كلاس الألوان
    const receiptContainer = document.querySelector('.receipt-container');
    if (receiptContainer) {
        receiptContainer.classList.remove('print-colors');
    }
});

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات التحميل
    const receiptContainer = document.querySelector('.receipt-container');
    if (receiptContainer) {
        receiptContainer.classList.add('receipt-loading');

        // إزالة تأثير التحميل بعد ثانية واحدة
        setTimeout(() => {
            receiptContainer.classList.remove('receipt-loading');
        }, 1000);
    }

    // إضافة تأثير النقر للعناصر التفاعلية
    const interactiveElements = document.querySelectorAll('.receipt-interactive');
    interactiveElements.forEach(element => {
        element.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // تحسين أزرار الإجراءات
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
});

// إضافة Font Awesome للأيقونات إذا لم تكن موجودة
if (!document.querySelector('link[href*="font-awesome"]')) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
    document.head.appendChild(link);
}

// وظيفة لحفظ الفاتورة كصورة (اختيارية)
function saveAsImage() {
    const receiptContainer = document.querySelector('.receipt-container');
    if (receiptContainer) {
        // يمكن إضافة مكتبة html2canvas هنا لحفظ الفاتورة كصورة
        console.log('حفظ الفاتورة كصورة - يتطلب مكتبة html2canvas');
    }
}

// وظيفة للطباعة الحرارية
function printThermal() {
    // إضافة كلاسات الطباعة الحرارية
    const receiptContainer = document.querySelector('.receipt-container');
    if (receiptContainer) {
        // حفظ الكلاسات الأصلية
        const originalClasses = receiptContainer.className;

        // إضافة كلاسات الطباعة الحرارية
        receiptContainer.className = 'thermal-receipt';

        // تحويل العناصر للطباعة الحرارية
        convertToThermalFormat();

        // طباعة
        window.print();

        // استعادة الكلاسات الأصلية
        setTimeout(() => {
            receiptContainer.className = originalClasses;
            restoreOriginalFormat();
        }, 1000);
    }
}

// تحويل التنسيق للطباعة الحرارية
function convertToThermalFormat() {
    // تحويل الرأس
    const header = document.querySelector('.receipt-header');
    if (header) {
        header.className = 'thermal-header';
        const title = header.querySelector('.receipt-title');
        const number = header.querySelector('.receipt-number');
        if (title) title.className = 'thermal-title';
        if (number) number.className = 'thermal-number';
    }

    // تحويل معلومات الشركة
    const company = document.querySelector('.company-section');
    if (company) {
        company.className = 'thermal-company';
        const name = company.querySelector('.company-name');
        const details = company.querySelector('.company-details');
        if (name) name.className = 'thermal-company-name';
        if (details) details.className = 'thermal-company-details';
    }

    // تحويل التفاصيل
    const details = document.querySelector('.receipt-details');
    if (details) {
        details.className = 'thermal-details';
        const rows = details.querySelectorAll('.detail-row');
        rows.forEach(row => {
            row.className = 'thermal-row';
            const label = row.querySelector('.detail-label');
            const value = row.querySelector('.detail-value');
            if (label) label.className = 'thermal-label thermal-arabic';
            if (value) value.className = 'thermal-value thermal-number-display';
        });
    }

    // تحويل المنتجات
    const products = document.querySelector('.products-section');
    if (products) {
        products.className = 'thermal-products';
        const title = products.querySelector('.products-title');
        if (title) title.className = 'thermal-products-title';

        const items = products.querySelectorAll('.product-item');
        items.forEach(item => {
            item.className = 'thermal-product';
            const name = item.querySelector('.product-name');
            const qty = item.querySelector('.product-qty');
            const price = item.querySelector('.product-price');
            if (name) name.className = 'thermal-product-name thermal-arabic';
            if (qty) qty.className = 'thermal-product-qty';
            if (price) price.className = 'thermal-product-price thermal-number-display';
        });
    }

    // تحويل المجموع
    const total = document.querySelector('.total-section');
    if (total) {
        total.className = 'thermal-total';
        const label = total.querySelector('.total-label');
        const amount = total.querySelector('.total-amount');
        if (label) label.className = 'thermal-total-label thermal-arabic';
        if (amount) amount.className = 'thermal-total-amount thermal-number-display';
    }

    // تحويل التذييل
    const footer = document.querySelector('.receipt-footer');
    if (footer) {
        footer.className = 'thermal-footer';
        const text = footer.querySelector('.footer-text');
        if (text) text.className = 'thermal-footer-text thermal-arabic';

        const qr = footer.querySelector('.qr-section');
        if (qr) {
            qr.className = 'thermal-qr';
            const qrCode = qr.querySelector('.qr-code');
            const contact = qr.querySelector('.contact-info');
            if (qrCode) qrCode.className = 'thermal-qr-code';
            if (contact) contact.className = 'thermal-contact thermal-arabic';
        }
    }
}

// استعادة التنسيق الأصلي
function restoreOriginalFormat() {
    location.reload(); // إعادة تحميل الصفحة لاستعادة التنسيق الأصلي
}

// وظيفة لمشاركة الفاتورة
function shareReceipt() {
    const receiptNumber = '<?php echo $invoice_number; ?>';
    const totalAmount = '<?php echo number_format($total_cost, 2); ?>';
    const customerName = '<?php echo $invoice_data['customer_name'] ? htmlspecialchars($invoice_data['customer_name']) : 'عميل غير مسجل'; ?>';

    const shareText = `فاتورة رقم: ${receiptNumber}
العميل: ${customerName}
المبلغ الإجمالي: ${totalAmount} جنيه
التاريخ: <?php echo date('Y/m/d H:i', strtotime($invoice_data['start_time'])); ?>

شكراً لاختياركم خدماتنا`;

    if (navigator.share) {
        navigator.share({
            title: `فاتورة رقم ${receiptNumber}`,
            text: shareText,
            url: window.location.href
        }).catch(console.error);
    } else {
        // نسخ النص إلى الحافظة
        navigator.clipboard.writeText(shareText).then(() => {
            alert('تم نسخ تفاصيل الفاتورة إلى الحافظة');
        }).catch(() => {
            // طريقة بديلة للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = shareText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ تفاصيل الفاتورة إلى الحافظة');
        });
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>