<?php
session_start();
require_once '../config/database.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات (Super Admin فقط)
requireAdminRole('super_admin');

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_POST['action'] === 'toggle_permission') {
            $admin_id = intval($_POST['admin_id']);
            $page_id = intval($_POST['page_id']);
            $is_enabled = $_POST['is_enabled'] === 'true' ? 1 : 0;
            
            // التحقق من إمكانية إدارة هذا الإدمن
            if (!canManageAdmin($admin_id)) {
                echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإدارة هذا المدير']);
                exit;
            }
            
            // التحقق من وجود الصلاحية
            $stmt = $pdo->prepare("SELECT id FROM admin_page_permissions WHERE admin_id = ? AND page_id = ?");
            $stmt->execute([$admin_id, $page_id]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // تحديث الصلاحية الموجودة
                $stmt = $pdo->prepare("UPDATE admin_page_permissions SET is_enabled = ?, granted_by = ? WHERE admin_id = ? AND page_id = ?");
                $stmt->execute([$is_enabled, $_SESSION['admin_id'], $admin_id, $page_id]);
            } else {
                // إضافة صلاحية جديدة
                $stmt = $pdo->prepare("INSERT INTO admin_page_permissions (admin_id, page_id, is_enabled, granted_by) VALUES (?, ?, ?, ?)");
                $stmt->execute([$admin_id, $page_id, $is_enabled, $_SESSION['admin_id']]);
            }
            
            // تسجيل النشاط
            logAdminActivity('toggle_admin_permission', "Admin ID: $admin_id, Page ID: $page_id, Enabled: $is_enabled");
            
            echo json_encode(['success' => true]);
            exit;
        }
        
        if ($_POST['action'] === 'reset_permissions') {
            $admin_id = intval($_POST['admin_id']);
            
            if (!canManageAdmin($admin_id)) {
                echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإدارة هذا المدير']);
                exit;
            }
            
            // حذف جميع الصلاحيات المخصصة
            $stmt = $pdo->prepare("DELETE FROM admin_page_permissions WHERE admin_id = ?");
            $stmt->execute([$admin_id]);
            
            // إعادة منح الصلاحيات الافتراضية
            $stmt = $pdo->prepare("CALL GrantDefaultPermissionsToAdmin(?)");
            $stmt->execute([$admin_id]);
            
            logAdminActivity('reset_admin_permissions', "Admin ID: $admin_id");
            
            echo json_encode(['success' => true]);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
        exit;
    }
}

// جلب قائمة المديرين
$stmt = $pdo->query("
    SELECT admin_id, username, full_name, email, role, is_active, created_at, last_login
    FROM admins 
    WHERE is_active = 1 
    ORDER BY role DESC, full_name
");
$admins = $stmt->fetchAll();

// جلب الإدمن المحدد للتعديل
$selected_admin_id = isset($_GET['admin_id']) ? intval($_GET['admin_id']) : null;
$selected_admin = null;

if ($selected_admin_id) {
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE admin_id = ? AND is_active = 1");
    $stmt->execute([$selected_admin_id]);
    $selected_admin = $stmt->fetch();
    
    // التحقق من إمكانية إدارة هذا الإدمن
    if ($selected_admin && !canManageAdmin($selected_admin_id)) {
        $selected_admin = null;
        $selected_admin_id = null;
        $_SESSION['error_message'] = 'ليس لديك صلاحية لإدارة هذا المدير';
    }
}

// جلب صلاحيات الإدمن المحدد
$permissions = [];
if ($selected_admin_id) {
    $stmt = $pdo->prepare("
        SELECT 
            ap.page_id,
            ap.page_name,
            ap.page_label,
            ap.page_icon,
            ap.category,
            ap.description,
            ap.is_default,
            ap.required_role,
            COALESCE(app.is_enabled, ap.is_default) as has_permission,
            app.granted_at,
            app.updated_at
        FROM admin_pages ap
        LEFT JOIN admin_page_permissions app ON ap.page_id = app.page_id AND app.admin_id = ?
        WHERE ap.is_active = TRUE
        ORDER BY ap.category, ap.page_label
    ");
    $stmt->execute([$selected_admin_id]);
    $permissions = $stmt->fetchAll();
}

// تجميع الصلاحيات حسب الفئة
$grouped_permissions = [];
foreach ($permissions as $permission) {
    $grouped_permissions[$permission['category']][] = $permission;
}

// أسماء الفئات
$category_names = [
    'main' => 'الصفحات الأساسية',
    'clients' => 'إدارة العملاء',
    'reports' => 'التقارير والإحصائيات',
    'settings' => 'الإعدادات',
    'admin' => 'إدارة النظام',
    'system' => 'النظام'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صلاحيات المديرين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .permission-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        .permission-card.enabled {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .permission-card.disabled {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .permission-card.default {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .admin-card {
            cursor: pointer;
            transition: all 0.3s;
        }
        .admin-card:hover {
            background-color: #f8f9fa;
        }
        .admin-card.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .role-badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 20px;
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 1rem;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-user-cog me-2"></i>
                        إدارة صلاحيات المديرين
                    </h1>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لوحة التحكم
                    </a>
                </div>

                <!-- رسائل التنبيه -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- قائمة المديرين -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    قائمة المديرين
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <?php foreach ($admins as $admin): ?>
                                    <?php if ($admin['admin_id'] != $_SESSION['admin_id']): // لا يمكن إدارة النفس ?>
                                        <div class="admin-card p-3 border-bottom <?php echo $selected_admin_id == $admin['admin_id'] ? 'selected' : ''; ?>"
                                             onclick="selectAdmin(<?php echo $admin['admin_id']; ?>)">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($admin['full_name']); ?></h6>
                                                    <small class="text-muted">@<?php echo htmlspecialchars($admin['username']); ?></small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="role-badge badge <?php echo $admin['role'] === 'super_admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                                        <?php echo $admin['role'] === 'super_admin' ? 'مدير عام' : 'مدير'; ?>
                                                    </span>
                                                    <?php if ($admin['last_login']): ?>
                                                        <div class="small text-muted mt-1">
                                                            آخر دخول: <?php echo date('Y-m-d', strtotime($admin['last_login'])); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- صلاحيات الإدمن المحدد -->
                    <div class="col-lg-8">
                        <?php if ($selected_admin): ?>
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user-shield me-2"></i>
                                        صلاحيات: <?php echo htmlspecialchars($selected_admin['full_name']); ?>
                                    </h5>
                                    <div>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="resetPermissions(<?php echo $selected_admin_id; ?>)">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($grouped_permissions)): ?>
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                                            <p>لا توجد صلاحيات متاحة</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($grouped_permissions as $category => $category_permissions): ?>
                                            <div class="mb-4">
                                                <div class="category-header">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-folder me-2"></i>
                                                        <?php echo $category_names[$category] ?? $category; ?>
                                                    </h6>
                                                </div>
                                                <div class="border border-top-0 rounded-bottom p-3">
                                                    <div class="row">
                                                        <?php foreach ($category_permissions as $permission): ?>
                                                            <div class="col-md-6 mb-3">
                                                                <div class="permission-card p-3 <?php 
                                                                    if ($permission['required_role'] === 'super_admin' && $selected_admin['role'] !== 'super_admin') {
                                                                        echo 'disabled';
                                                                    } elseif ($permission['has_permission']) {
                                                                        echo 'enabled';
                                                                    } else {
                                                                        echo 'disabled';
                                                                    }
                                                                ?>">
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <div class="flex-grow-1">
                                                                            <div class="d-flex align-items-center mb-2">
                                                                                <i class="<?php echo $permission['page_icon']; ?> me-2"></i>
                                                                                <strong><?php echo htmlspecialchars($permission['page_label']); ?></strong>
                                                                            </div>
                                                                            <?php if ($permission['description']): ?>
                                                                                <small class="text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                                            <?php endif; ?>
                                                                            
                                                                            <?php if ($permission['required_role'] !== 'any'): ?>
                                                                                <div class="mt-2">
                                                                                    <span class="badge bg-info">
                                                                                        يتطلب: <?php echo $permission['required_role'] === 'super_admin' ? 'مدير عام' : 'مدير'; ?>
                                                                                    </span>
                                                                                </div>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                        <div class="ms-3">
                                                                            <?php if ($permission['required_role'] === 'super_admin' && $selected_admin['role'] !== 'super_admin'): ?>
                                                                                <span class="badge bg-secondary">غير متاح</span>
                                                                            <?php else: ?>
                                                                                <label class="switch">
                                                                                    <input type="checkbox" 
                                                                                           <?php echo $permission['has_permission'] ? 'checked' : ''; ?>
                                                                                           onchange="togglePermission(<?php echo $selected_admin_id; ?>, <?php echo $permission['page_id']; ?>, this.checked)">
                                                                                    <span class="slider"></span>
                                                                                </label>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">اختر مديراً لإدارة صلاحياته</h5>
                                    <p class="text-muted">قم بالنقر على أحد المديرين من القائمة الجانبية لعرض وتعديل صلاحياته</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectAdmin(adminId) {
            window.location.href = 'admin_permissions.php?admin_id=' + adminId;
        }

        function togglePermission(adminId, pageId, isEnabled) {
            fetch('admin_permissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=toggle_permission&admin_id=${adminId}&page_id=${pageId}&is_enabled=${isEnabled}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الصفحة لتحديث الواجهة
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                    // إعادة تعيين حالة المفتاح
                    event.target.checked = !isEnabled;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
                event.target.checked = !isEnabled;
            });
        }

        function resetPermissions(adminId) {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الصلاحيات للقيم الافتراضية؟')) {
                fetch('admin_permissions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=reset_permissions&admin_id=${adminId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }
    </script>
</body>
</html>
