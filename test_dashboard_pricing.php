<?php
/**
 * اختبار نظام التسعير في Dashboard - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🎮 اختبار نظام التسعير في Dashboard</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص الأجهزة وأسعارها
    echo "<h2>1. فحص الأجهزة المتاحة وأسعارها</h2>";
    
    $devices = $pdo->query("
        SELECT device_id, device_name, device_type, status, 
               hourly_rate, single_rate, multi_rate
        FROM devices 
        WHERE client_id = 1 AND status = 'available'
        ORDER BY device_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>اسم الجهاز</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>السعر الفردي</th>";
        echo "<th style='padding: 8px;'>السعر الزوجي</th>";
        echo "<th style='padding: 8px;'>الفرق</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        foreach ($devices as $device) {
            $single_rate = $device['single_rate'] ?? $device['hourly_rate'];
            $multi_rate = $device['multi_rate'] ?? $device['hourly_rate'];
            $difference = $multi_rate - $single_rate;
            $percentage = $single_rate > 0 ? (($difference / $single_rate) * 100) : 0;
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$device['device_id']}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td style='padding: 8px;'>{$device['device_type']}</td>";
            echo "<td style='padding: 8px; color: blue; font-weight: bold;'>" . number_format($single_rate, 2) . " ج.م</td>";
            echo "<td style='padding: 8px; color: red; font-weight: bold;'>" . number_format($multi_rate, 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>+" . number_format($difference, 2) . " ج.م (" . number_format($percentage, 1) . "%)</td>";
            echo "<td style='padding: 8px; color: green;'>{$device['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>إجمالي الأجهزة المتاحة: " . count($devices) . "</strong></p>";
        
    } else {
        echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة</p>";
        echo "<p><a href='fix_device_pricing.php'>انقر هنا لإضافة أجهزة تجريبية</a></p>";
    }
    
    // 2. محاكاة بيانات الأزرار في Dashboard
    echo "<h2>2. محاكاة بيانات أزرار بدء الجلسة</h2>";
    
    if (count($devices) > 0) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>بيانات الأزرار التي سيتم إرسالها إلى JavaScript:</h4>";
        
        foreach ($devices as $device) {
            $single_rate = $device['single_rate'] ?? $device['hourly_rate'];
            $multi_rate = $device['multi_rate'] ?? $device['hourly_rate'];
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; background: white;'>";
            echo "<strong>" . htmlspecialchars($device['device_name']) . "</strong><br>";
            echo "<code style='background: #e9ecef; padding: 2px 5px; font-size: 12px;'>";
            echo "data-device-id=\"{$device['device_id']}\"<br>";
            echo "data-device-name=\"" . htmlspecialchars($device['device_name']) . "\"<br>";
            echo "data-device-type=\"{$device['device_type']}\"<br>";
            echo "data-hourly-rate=\"{$device['hourly_rate']}\"<br>";
            echo "data-single-rate=\"{$single_rate}\"<br>";
            echo "data-multi-rate=\"{$multi_rate}\"";
            echo "</code>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // 3. اختبار حساب التكلفة
    echo "<h2>3. اختبار حساب التكلفة</h2>";
    
    if (count($devices) > 0) {
        $test_device = $devices[0];
        $single_rate = $test_device['single_rate'] ?? $test_device['hourly_rate'];
        $multi_rate = $test_device['multi_rate'] ?? $test_device['hourly_rate'];
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>اختبار مع الجهاز: " . htmlspecialchars($test_device['device_name']) . "</h4>";
        
        $test_scenarios = [
            ['type' => 'open', 'game' => 'single', 'duration' => 1],
            ['type' => 'open', 'game' => 'multiplayer', 'duration' => 1],
            ['type' => 'timed', 'game' => 'single', 'duration' => 1],
            ['type' => 'timed', 'game' => 'single', 'duration' => 2],
            ['type' => 'timed', 'game' => 'multiplayer', 'duration' => 1],
            ['type' => 'timed', 'game' => 'multiplayer', 'duration' => 2],
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>نوع الجلسة</th>";
        echo "<th style='padding: 8px;'>نوع اللعب</th>";
        echo "<th style='padding: 8px;'>المدة</th>";
        echo "<th style='padding: 8px;'>السعر المستخدم</th>";
        echo "<th style='padding: 8px;'>التكلفة المحسوبة</th>";
        echo "</tr>";
        
        foreach ($test_scenarios as $scenario) {
            $session_type = $scenario['type'];
            $game_type = $scenario['game'];
            $duration = $scenario['duration'];
            
            $selected_rate = $game_type === 'multiplayer' ? $multi_rate : $single_rate;
            
            if ($session_type === 'timed') {
                $cost = $duration * $selected_rate;
                $cost_description = $duration . " ساعة × " . number_format($selected_rate, 2) . " ج.م";
            } else {
                $cost = $selected_rate;
                $cost_description = "سعر الساعة";
            }
            
            $session_type_text = $session_type === 'timed' ? 'محددة الوقت' : 'مفتوحة';
            $game_type_text = $game_type === 'multiplayer' ? 'زوجي' : 'فردي';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>$session_type_text</td>";
            echo "<td style='padding: 8px; color: " . ($game_type === 'multiplayer' ? 'red' : 'blue') . ";'>$game_type_text</td>";
            echo "<td style='padding: 8px;'>$duration ساعة</td>";
            echo "<td style='padding: 8px;'>" . number_format($selected_rate, 2) . " ج.م/ساعة</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . number_format($cost, 2) . " ج.م<br><small>($cost_description)</small></td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
    // 4. فحص التحديثات في Dashboard
    echo "<h2>4. فحص التحديثات في Dashboard</h2>";
    
    $dashboard_file = 'client/dashboard.php';
    if (file_exists($dashboard_file)) {
        $dashboard_content = file_get_contents($dashboard_file);
        
        $checks = [
            'نوع اللعب في النافذة المنبثقة' => strpos($dashboard_content, 'name="game_type"') !== false,
            'أسعار فردي وزوجي في الأزرار' => strpos($dashboard_content, 'data-single-rate') !== false && strpos($dashboard_content, 'data-multi-rate') !== false,
            'تحديث دالة updateEstimatedCost' => strpos($dashboard_content, 'gameType') !== false,
            'مستمع أحداث نوع اللعب' => strpos($dashboard_content, 'game_type') !== false && strpos($dashboard_content, 'addEventListener') !== false,
            'عرض الأسعار في البطاقات' => strpos($dashboard_content, 'فردي') !== false && strpos($dashboard_content, 'زوجي') !== false,
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>التحديث</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        foreach ($checks as $check_name => $check_result) {
            $status = $check_result ? '✅ موجود' : '❌ مفقود';
            $color = $check_result ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>$check_name</td>";
            echo "<td style='padding: 8px; color: $color; font-weight: bold;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $all_checks_passed = array_reduce($checks, function($carry, $item) {
            return $carry && $item;
        }, true);
        
        if ($all_checks_passed) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3 style='color: #155724; margin: 0;'>🎉 جميع التحديثات موجودة!</h3>";
            echo "<p style='margin: 10px 0 0 0;'>تم تطبيق جميع التحديثات بنجاح في Dashboard</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3 style='color: #721c24; margin: 0;'>⚠️ بعض التحديثات مفقودة</h3>";
            echo "<p style='margin: 10px 0 0 0;'>تحقق من الملف وتأكد من تطبيق جميع التحديثات</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ ملف Dashboard غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ حدث خطأ</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<div style='margin: 20px 0; text-align: center;'>";
echo "<a href='fix_device_pricing.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح أسعار الأجهزة</a>";
echo "<a href='test_game_pricing.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار Sessions</a>";
echo "<a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>انتقل لـ Dashboard</a>";
echo "</div>";
?>
