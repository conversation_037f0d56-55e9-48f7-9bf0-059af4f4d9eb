<?php
// إصلاح بنية جدول session_products
session_start();
require_once 'config/database.php';

echo "<h1>إصلاح بنية جدول session_products</h1>";

try {
    // فحص بنية الجدول الحالية
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>البنية الحالية:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_unit_price = false;
    $has_total_price = false;
    $has_price = false;
    $has_total = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'unit_price') $has_unit_price = true;
        if ($column['Field'] === 'total_price') $has_total_price = true;
        if ($column['Field'] === 'price') $has_price = true;
        if ($column['Field'] === 'total') $has_total = true;
    }
    echo "</table>";
    
    echo "<h2>تحليل البنية:</h2>";
    echo "<ul>";
    echo "<li>unit_price: " . ($has_unit_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>total_price: " . ($has_total_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>price: " . ($has_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>total: " . ($has_total ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "</ul>";
    
    $needs_fix = false;
    $fixes_applied = [];
    
    // إضافة الأعمدة المفقودة
    if (!$has_unit_price) {
        echo "<p style='color: orange;'>⚠️ إضافة عمود unit_price...</p>";
        $pdo->exec("ALTER TABLE session_products ADD COLUMN unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER quantity");
        $fixes_applied[] = "تم إضافة عمود unit_price";
        $needs_fix = true;
    }
    
    if (!$has_total_price) {
        echo "<p style='color: orange;'>⚠️ إضافة عمود total_price...</p>";
        $pdo->exec("ALTER TABLE session_products ADD COLUMN total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER unit_price");
        $fixes_applied[] = "تم إضافة عمود total_price";
        $needs_fix = true;
    }
    
    // إذا كان هناك عمود price ولكن لا يوجد unit_price، نسخ البيانات
    if ($has_price && !$has_unit_price && $needs_fix) {
        echo "<p style='color: blue;'>ℹ️ نسخ البيانات من price إلى unit_price...</p>";
        $pdo->exec("UPDATE session_products SET unit_price = price WHERE unit_price = 0 AND price > 0");
        $fixes_applied[] = "تم نسخ البيانات من price إلى unit_price";
    }
    
    // إذا كان هناك عمود total ولكن لا يوجد total_price، نسخ البيانات
    if ($has_total && !$has_total_price && $needs_fix) {
        echo "<p style='color: blue;'>ℹ️ نسخ البيانات من total إلى total_price...</p>";
        $pdo->exec("UPDATE session_products SET total_price = total WHERE total_price = 0 AND total > 0");
        $fixes_applied[] = "تم نسخ البيانات من total إلى total_price";
    }
    
    // حساب total_price للسجلات التي لا تحتوي على قيمة
    echo "<p style='color: blue;'>ℹ️ حساب total_price للسجلات المفقودة...</p>";
    $updated = $pdo->exec("UPDATE session_products SET total_price = unit_price * quantity WHERE total_price = 0 AND unit_price > 0");
    if ($updated > 0) {
        $fixes_applied[] = "تم حساب total_price لـ $updated سجل";
    }
    
    // إصلاح السجلات التي لا تحتوي على unit_price
    echo "<p style='color: blue;'>ℹ️ إصلاح السجلات التي لا تحتوي على أسعار...</p>";
    $stmt = $pdo->query("
        SELECT sp.id, sp.product_id, sp.quantity, sp.unit_price, ci.price as current_price
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        WHERE sp.unit_price = 0 AND ci.price > 0
    ");
    $records_to_fix = $stmt->fetchAll();
    
    $fixed_records = 0;
    foreach ($records_to_fix as $record) {
        $new_unit_price = $record['current_price'];
        $new_total_price = $new_unit_price * $record['quantity'];
        
        $update_stmt = $pdo->prepare("UPDATE session_products SET unit_price = ?, total_price = ? WHERE id = ?");
        $update_stmt->execute([$new_unit_price, $new_total_price, $record['id']]);
        $fixed_records++;
    }
    
    if ($fixed_records > 0) {
        $fixes_applied[] = "تم إصلاح $fixed_records سجل بأسعار من جدول المنتجات";
    }
    
    echo "<h2>ملخص الإصلاحات:</h2>";
    if (count($fixes_applied) > 0) {
        echo "<ul style='color: green;'>";
        foreach ($fixes_applied as $fix) {
            echo "<li>✅ $fix</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ الجدول في حالة جيدة، لا يحتاج إصلاحات</p>";
    }
    
    // اختبار نهائي
    echo "<h2>اختبار نهائي:</h2>";
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN unit_price > 0 THEN 1 END) as records_with_price,
            COUNT(CASE WHEN total_price > 0 THEN 1 END) as records_with_total
        FROM session_products
    ");
    $test_result = $stmt->fetch();
    
    echo "<p>إجمالي السجلات: {$test_result['total_records']}</p>";
    echo "<p>السجلات التي تحتوي على أسعار: {$test_result['records_with_price']}</p>";
    echo "<p>السجلات التي تحتوي على إجماليات: {$test_result['records_with_total']}</p>";
    
    if ($test_result['total_records'] > 0) {
        $price_percentage = round(($test_result['records_with_price'] / $test_result['total_records']) * 100, 2);
        $total_percentage = round(($test_result['records_with_total'] / $test_result['total_records']) * 100, 2);
        
        echo "<p>نسبة السجلات الصحيحة (الأسعار): <strong>$price_percentage%</strong></p>";
        echo "<p>نسبة السجلات الصحيحة (الإجماليات): <strong>$total_percentage%</strong></p>";
        
        if ($price_percentage >= 100 && $total_percentage >= 100) {
            echo "<p style='color: green; font-weight: bold;'>🎉 جميع السجلات صحيحة!</p>";
        } elseif ($price_percentage >= 90 && $total_percentage >= 90) {
            echo "<p style='color: orange; font-weight: bold;'>⚠️ معظم السجلات صحيحة</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ يوجد مشاكل في البيانات</p>";
        }
    }
    
    // عرض عينة من البيانات المصلحة
    echo "<h2>عينة من البيانات بعد الإصلاح:</h2>";
    $stmt = $pdo->query("
        SELECT 
            sp.id,
            sp.quantity,
            sp.unit_price,
            sp.total_price,
            ci.name as product_name
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        ORDER BY sp.id DESC
        LIMIT 5
    ");
    $sample_data = $stmt->fetchAll();
    
    if (count($sample_data) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th><th>الحالة</th></tr>";
        
        foreach ($sample_data as $row) {
            $status = ($row['unit_price'] > 0 && $row['total_price'] > 0) ? "✅ صحيح" : "❌ خطأ";
            $status_color = ($row['unit_price'] > 0 && $row['total_price'] > 0) ? "green" : "red";
            
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>" . htmlspecialchars($row['product_name'] ?? 'غير معروف') . "</td>";
            echo "<td>{$row['quantity']}</td>";
            echo "<td>{$row['unit_price']}</td>";
            echo "<td>{$row['total_price']}</td>";
            echo "<td style='color: $status_color;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
