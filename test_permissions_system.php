<?php
/**
 * اختبار نظام الصلاحيات المحسن - PlayGood
 * هذا الملف لاختبار جميع ميزات نظام الصلاحيات الجديد
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نظام الصلاحيات المحسن</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .test-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container py-4'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-shield-alt me-2'></i>اختبار نظام الصلاحيات المحسن</h3>
        </div>
        <div class='card-body'>";

try {
    // 1. اختبار وجود الجداول المطلوبة
    echo "<div class='test-section'>
            <h5><i class='fas fa-database me-2'></i>اختبار وجود الجداول</h5>";
    
    $required_tables = ['permissions', 'pages', 'employee_permissions', 'employee_pages'];
    $tables_status = [];
    
    foreach ($required_tables as $table) {
        try {
            $check = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            $tables_status[$table] = true;
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>جدول $table موجود ويعمل بشكل صحيح
                  </div>";
        } catch (PDOException $e) {
            $tables_status[$table] = false;
            echo "<div class='test-result test-error'>
                    <i class='fas fa-times me-2'></i>جدول $table غير موجود أو به مشكلة: {$e->getMessage()}
                  </div>";
        }
    }
    echo "</div>";
    
    // 2. اختبار البيانات الأساسية
    echo "<div class='test-section'>
            <h5><i class='fas fa-list me-2'></i>اختبار البيانات الأساسية</h5>";
    
    if ($tables_status['permissions']) {
        $permissions_count = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
        echo "<div class='test-result test-info'>
                <i class='fas fa-key me-2'></i>عدد الصلاحيات المتاحة: $permissions_count
              </div>";
        
        // عرض الصلاحيات حسب الفئة
        $categories = $pdo->query("
            SELECT category, COUNT(*) as count 
            FROM permissions 
            GROUP BY category 
            ORDER BY category
        ")->fetchAll();
        
        foreach ($categories as $category) {
            echo "<div class='test-result test-success'>
                    <i class='fas fa-folder me-2'></i>فئة {$category['category']}: {$category['count']} صلاحية
                  </div>";
        }
    }
    
    if ($tables_status['pages']) {
        $pages_count = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
        echo "<div class='test-result test-info'>
                <i class='fas fa-file me-2'></i>عدد الصفحات المتاحة: $pages_count
              </div>";
        
        // عرض الصفحات حسب الفئة
        $page_categories = $pdo->query("
            SELECT category, COUNT(*) as count 
            FROM pages 
            GROUP BY category 
            ORDER BY category
        ")->fetchAll();
        
        foreach ($page_categories as $category) {
            echo "<div class='test-result test-success'>
                    <i class='fas fa-folder-open me-2'></i>فئة {$category['category']}: {$category['count']} صفحة
                  </div>";
        }
    }
    echo "</div>";
    
    // 3. اختبار الموظفين والصلاحيات المخصصة
    echo "<div class='test-section'>
            <h5><i class='fas fa-users me-2'></i>اختبار الموظفين والصلاحيات المخصصة</h5>";
    
    try {
        $employees = $pdo->query("
            SELECT e.*, 
                   COUNT(ep.permission_id) as custom_permissions_count,
                   COUNT(epg.page_id) as custom_pages_count
            FROM employees e
            LEFT JOIN employee_permissions ep ON e.id = ep.employee_id
            LEFT JOIN employee_pages epg ON e.id = epg.employee_id
            GROUP BY e.id
            ORDER BY e.name
        ")->fetchAll();
        
        if (empty($employees)) {
            echo "<div class='test-result test-info'>
                    <i class='fas fa-info-circle me-2'></i>لا يوجد موظفين في النظام حالياً
                  </div>";
        } else {
            foreach ($employees as $employee) {
                $status_class = $employee['custom_permissions'] ? 'test-success' : 'test-info';
                $permission_type = $employee['custom_permissions'] ? 'صلاحيات مخصصة' : 'صلاحيات الدور';
                
                echo "<div class='test-result $status_class'>
                        <i class='fas fa-user me-2'></i>
                        <strong>{$employee['name']}</strong> ({$employee['role']}) - $permission_type
                        <br><small>الصلاحيات المخصصة: {$employee['custom_permissions_count']} | الصفحات المخصصة: {$employee['custom_pages_count']}</small>
                      </div>";
            }
        }
    } catch (PDOException $e) {
        echo "<div class='test-result test-error'>
                <i class='fas fa-exclamation-triangle me-2'></i>خطأ في جلب بيانات الموظفين: {$e->getMessage()}
              </div>";
    }
    echo "</div>";
    
    // 4. اختبار دوال التحقق من الصلاحيات
    echo "<div class='test-section'>
            <h5><i class='fas fa-code me-2'></i>اختبار دوال التحقق من الصلاحيات</h5>";
    
    // تحقق من وجود الدوال المطلوبة
    $required_functions = [
        'employeeHasPermission',
        'employeeHasCustomPermission', 
        'employeeCanAccessPage',
        'getEmployeeCustomPermissions'
    ];
    
    foreach ($required_functions as $function) {
        if (function_exists($function)) {
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>دالة $function متوفرة
                  </div>";
        } else {
            echo "<div class='test-result test-error'>
                    <i class='fas fa-times me-2'></i>دالة $function غير متوفرة
                  </div>";
        }
    }
    echo "</div>";
    
    // 5. اختبار الملفات المحسنة
    echo "<div class='test-section'>
            <h5><i class='fas fa-file-code me-2'></i>اختبار الملفات المحسنة</h5>";
    
    $improved_files = [
        'client/employees.php' => 'صفحة إدارة الموظفين',
        'client/includes/employee-auth.php' => 'ملف دوال الصلاحيات',
        'client/includes/sidebar.php' => 'القائمة الجانبية',
        'client/assets/css/permissions.css' => 'ملف CSS الصلاحيات'
    ];
    
    foreach ($improved_files as $file => $description) {
        if (file_exists($file)) {
            $file_size = number_format(filesize($file) / 1024, 2);
            echo "<div class='test-result test-success'>
                    <i class='fas fa-file me-2'></i>$description ($file) - حجم الملف: {$file_size} KB
                  </div>";
        } else {
            echo "<div class='test-result test-error'>
                    <i class='fas fa-file-times me-2'></i>$description ($file) غير موجود
                  </div>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-result test-error'>
            <i class='fas fa-exclamation-triangle me-2'></i>خطأ عام في الاختبار: {$e->getMessage()}
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='client/employees.php' class='btn btn-primary me-2'>
                <i class='fas fa-users me-2'></i>اختبار صفحة الموظفين
            </a>
            <a href='setup_employee_permissions.php' class='btn btn-secondary'>
                <i class='fas fa-cog me-2'></i>إعادة إعداد النظام
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
