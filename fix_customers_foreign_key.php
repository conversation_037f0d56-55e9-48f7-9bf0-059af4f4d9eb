<?php
/**
 * إصلاح مشكلة المفتاح الخارجي في جدول العملاء - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔧 إصلاح مشكلة المفتاح الخارجي - جدول العملاء</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص المفاتيح الخارجية الموجودة
    echo "<h2>1. فحص المفاتيح الخارجية في جدول customers</h2>";
    
    $foreign_keys_query = "
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'customers' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ";
    
    $fk_result = $pdo->query($foreign_keys_query);
    $foreign_keys = $fk_result->fetchAll();
    
    if (count($foreign_keys) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>اسم القيد</th>";
        echo "<th style='padding: 10px;'>العمود</th>";
        echo "<th style='padding: 10px;'>الجدول المرجعي</th>";
        echo "<th style='padding: 10px;'>العمود المرجعي</th>";
        echo "</tr>";
        
        foreach ($foreign_keys as $fk) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $fk['CONSTRAINT_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['COLUMN_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['REFERENCED_TABLE_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['REFERENCED_COLUMN_NAME'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: blue;'>ℹ️ لا توجد مفاتيح خارجية في جدول customers</p>";
    }
    
    // حذف المفاتيح الخارجية المشكلة
    echo "<h2>2. حذف المفاتيح الخارجية المشكلة</h2>";
    
    $constraints_to_remove = [];
    foreach ($foreign_keys as $fk) {
        if ($fk['REFERENCED_TABLE_NAME'] === 'users') {
            $constraints_to_remove[] = $fk['CONSTRAINT_NAME'];
        }
    }
    
    if (count($constraints_to_remove) > 0) {
        foreach ($constraints_to_remove as $constraint_name) {
            try {
                $drop_fk_sql = "ALTER TABLE customers DROP FOREIGN KEY `$constraint_name`";
                $pdo->exec($drop_fk_sql);
                echo "<p style='color: green;'>✅ تم حذف المفتاح الخارجي: $constraint_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في حذف المفتاح الخارجي $constraint_name: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ لا توجد مفاتيح خارجية تحتاج لحذف</p>";
    }
    
    // فحص هيكل جدول customers
    echo "<h2>3. فحص هيكل جدول customers</h2>";
    
    $columns_check = $pdo->query("DESCRIBE customers");
    $existing_columns = [];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم العمود</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    while ($column = $columns_check->fetch()) {
        $existing_columns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 10px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // تعديل عمود created_by ليقبل NULL
    echo "<h2>4. تعديل عمود created_by</h2>";
    
    if (in_array('created_by', $existing_columns)) {
        try {
            $modify_column_sql = "ALTER TABLE customers MODIFY COLUMN created_by INT NULL DEFAULT NULL";
            $pdo->exec($modify_column_sql);
            echo "<p style='color: green;'>✅ تم تعديل عمود created_by ليقبل NULL</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في تعديل عمود created_by: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ عمود created_by غير موجود</p>";
    }
    
    // إضافة الأعمدة المطلوبة إذا لم تكن موجودة
    echo "<h2>5. إضافة الأعمدة المطلوبة</h2>";
    
    $required_columns = [
        'customer_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(255) NOT NULL',
        'phone' => 'VARCHAR(20) NOT NULL',
        'email' => 'VARCHAR(255) NULL',
        'notes' => 'TEXT NULL',
        'client_id' => 'INT NOT NULL DEFAULT 1',
        'created_by' => 'INT NULL DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                if ($column_name === 'customer_id') {
                    // تخطي إضافة PRIMARY KEY إذا كان موجود
                    continue;
                }
                
                $sql = "ALTER TABLE customers ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ تم إضافة عمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود</p>";
        }
    }
    
    // اختبار إضافة عميل تجريبي
    echo "<h2>6. اختبار إضافة عميل تجريبي</h2>";
    
    try {
        $test_stmt = $pdo->prepare("
            INSERT INTO customers (name, phone, email, notes, client_id, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $test_data = [
            'عميل تجريبي - ' . date('Y-m-d H:i:s'),
            '01234567890',
            '<EMAIL>',
            'عميل تجريبي للاختبار',
            1,
            NULL  // استخدام NULL بدلاً من رقم المستخدم
        ];
        
        $test_stmt->execute($test_data);
        $customer_id = $pdo->lastInsertId();
        
        echo "<p style='color: green;'>✅ تم إضافة عميل تجريبي بنجاح - ID: $customer_id</p>";
        
        // حذف العميل التجريبي
        $delete_stmt = $pdo->prepare("DELETE FROM customers WHERE customer_id = ?");
        $delete_stmt->execute([$customer_id]);
        echo "<p style='color: blue;'>ℹ️ تم حذف العميل التجريبي</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار إضافة العميل: " . $e->getMessage() . "</p>";
    }
    
    // فحص المفاتيح الخارجية بعد الإصلاح
    echo "<h2>7. فحص المفاتيح الخارجية بعد الإصلاح</h2>";
    
    $fk_result_after = $pdo->query($foreign_keys_query);
    $foreign_keys_after = $fk_result_after->fetchAll();
    
    if (count($foreign_keys_after) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>اسم القيد</th>";
        echo "<th style='padding: 10px;'>العمود</th>";
        echo "<th style='padding: 10px;'>الجدول المرجعي</th>";
        echo "<th style='padding: 10px;'>العمود المرجعي</th>";
        echo "</tr>";
        
        foreach ($foreign_keys_after as $fk) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $fk['CONSTRAINT_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['COLUMN_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['REFERENCED_TABLE_NAME'] . "</td>";
            echo "<td style='padding: 10px;'>" . $fk['REFERENCED_COLUMN_NAME'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: green;'>✅ لا توجد مفاتيح خارجية مشكلة</p>";
    }
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 ملخص الإصلاح</h3>";
    echo "<ul>";
    echo "<li>المفاتيح الخارجية المحذوفة: " . count($constraints_to_remove) . "</li>";
    echo "<li>عمود created_by: " . (in_array('created_by', $existing_columns) ? "تم تعديله ✅" : "غير موجود ❌") . "</li>";
    echo "<li>اختبار الإضافة: " . (isset($customer_id) ? "نجح ✅" : "فشل ❌") . "</li>";
    echo "<li>المفاتيح الخارجية المتبقية: " . count($foreign_keys_after) . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر إضافة العملاء الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/customers.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة العملاء</a>";
echo "<a href='check_customers_table.php' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>فحص جدول العملاء</a>";
echo "</div>";

echo "</div>";
?>
