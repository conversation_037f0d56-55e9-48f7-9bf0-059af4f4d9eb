<?php
/**
 * دوال التحقق من صلاحيات النسخ الاحتياطي
 * للتحكم في إمكانية وصول العملاء لميزة النسخ الاحتياطي
 */

require_once __DIR__ . '/../config/database.php';

/**
 * التحقق من تفعيل صلاحية النسخ الاحتياطي للعملاء (الإعداد العام)
 * @return bool true إذا كانت الميزة مفعلة عموماً، false إذا كانت معطلة
 */
function isBackupEnabledGlobally() {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM admin_settings WHERE setting_key = 'backup_enabled'");
        $stmt->execute();
        $result = $stmt->fetch();

        // إذا لم توجد الإعداد، افتراضياً مفعل
        return $result ? ($result['setting_value'] == '1') : true;
    } catch (Exception $e) {
        // في حالة عدم وجود الجدول، افتراضياً مفعل
        return true;
    }
}

/**
 * التحقق من تفعيل صلاحية النسخ الاحتياطي لعميل محدد
 * @param int $client_id معرف العميل
 * @return bool true إذا كانت الميزة مفعلة للعميل، false إذا كانت معطلة
 */
function isBackupEnabledForClient($client_id = null) {
    global $pdo;

    // التحقق من الإعداد العام أولاً
    if (!isBackupEnabledGlobally()) {
        return false;
    }

    // إذا لم يتم تمرير معرف العميل، استخدم العميل الحالي من الجلسة
    if ($client_id === null) {
        $client_id = $_SESSION['client_id'] ?? null;
    }

    if (!$client_id) {
        return false;
    }

    try {
        // التحقق من وجود حقل backup_enabled في جدول العملاء
        $stmt = $pdo->prepare("SHOW COLUMNS FROM clients LIKE 'backup_enabled'");
        $stmt->execute();
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            // إذا لم يوجد الحقل، افتراضياً مفعل (للتوافق مع النظام القديم)
            return true;
        }

        // التحقق من صلاحية العميل المحدد
        $stmt = $pdo->prepare("SELECT backup_enabled FROM clients WHERE client_id = ? AND is_active = 1");
        $stmt->execute([$client_id]);
        $result = $stmt->fetch();

        return $result ? ($result['backup_enabled'] == 1) : false;
    } catch (Exception $e) {
        // في حالة الخطأ، افتراضياً مفعل
        return true;
    }
}

/**
 * دالة للتوافق مع النظام القديم - تتحقق من صلاحية العميل الحالي
 * @return bool
 */
function isBackupEnabled() {
    return isBackupEnabledForClient();
}

/**
 * التحقق من وضع الصيانة
 * @return bool true إذا كان النظام في وضع الصيانة
 */
function isSystemInMaintenance() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM admin_settings WHERE setting_key = 'system_maintenance'");
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result ? ($result['setting_value'] == '1') : false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * الحصول على الحد الأقصى لعدد ملفات النسخ الاحتياطي
 * @return int عدد الملفات المسموح بها
 */
function getMaxBackupFiles() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM admin_settings WHERE setting_key = 'max_backup_files'");
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result ? intval($result['setting_value']) : 10;
    } catch (Exception $e) {
        return 10;
    }
}

/**
 * الحصول على جميع إعدادات الأدمن
 * @return array مصفوفة بجميع الإعدادات
 */
function getAdminSettings() {
    global $pdo;
    
    $settings = [];
    
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM admin_settings");
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (Exception $e) {
        // إعدادات افتراضية
        $settings = [
            'backup_enabled' => '1',
            'system_maintenance' => '0',
            'max_backup_files' => '10'
        ];
    }
    
    return $settings;
}

/**
 * تحديث إعداد معين
 * @param string $key مفتاح الإعداد
 * @param string $value قيمة الإعداد
 * @return bool true في حالة النجاح
 */
function updateAdminSetting($key, $value) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$key, $value]);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * رسالة تنبيه عند تعطيل النسخ الاحتياطي
 * @return string HTML للرسالة
 */
function getBackupDisabledMessage() {
    return '
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> تم تعطيل ميزة النسخ الاحتياطي من قبل إدارة النظام.
        يرجى التواصل مع الدعم الفني لمزيد من المعلومات.
    </div>';
}

/**
 * رسالة وضع الصيانة
 * @return string HTML للرسالة
 */
function getMaintenanceModeMessage() {
    return '
    <div class="alert alert-info" role="alert">
        <i class="fas fa-tools me-2"></i>
        <strong>وضع الصيانة:</strong> النظام حالياً في وضع الصيانة.
        بعض الميزات قد تكون غير متاحة مؤقتاً.
    </div>';
}

/**
 * تفعيل/تعطيل النسخ الاحتياطي لعميل محدد
 * @param int $client_id معرف العميل
 * @param bool $enabled حالة التفعيل (true/false)
 * @return bool true في حالة النجاح
 */
function setClientBackupPermission($client_id, $enabled) {
    global $pdo;

    try {
        // التأكد من وجود حقل backup_enabled
        ensureBackupColumnExists();

        $stmt = $pdo->prepare("UPDATE clients SET backup_enabled = ? WHERE client_id = ?");
        return $stmt->execute([$enabled ? 1 : 0, $client_id]);
    } catch (Exception $e) {
        error_log('خطأ في تحديث صلاحية النسخ الاحتياطي: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على حالة النسخ الاحتياطي لعميل محدد
 * @param int $client_id معرف العميل
 * @return bool|null true إذا مفعل، false إذا معطل، null في حالة الخطأ
 */
function getClientBackupStatus($client_id) {
    global $pdo;

    try {
        // التحقق من وجود الحقل
        $stmt = $pdo->prepare("SHOW COLUMNS FROM clients LIKE 'backup_enabled'");
        $stmt->execute();
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            return true; // افتراضياً مفعل إذا لم يوجد الحقل
        }

        $stmt = $pdo->prepare("SELECT backup_enabled FROM clients WHERE client_id = ?");
        $stmt->execute([$client_id]);
        $result = $stmt->fetch();

        return $result ? ($result['backup_enabled'] == 1) : null;
    } catch (Exception $e) {
        return null;
    }
}

/**
 * التأكد من وجود حقل backup_enabled في جدول العملاء
 * @return bool true في حالة النجاح
 */
function ensureBackupColumnExists() {
    global $pdo;

    try {
        // التحقق من وجود الحقل
        $stmt = $pdo->prepare("SHOW COLUMNS FROM clients LIKE 'backup_enabled'");
        $stmt->execute();
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            // إضافة الحقل إذا لم يكن موجوداً
            $pdo->exec("ALTER TABLE clients ADD COLUMN backup_enabled TINYINT(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي للعميل'");
        }

        return true;
    } catch (Exception $e) {
        error_log('خطأ في إنشاء حقل backup_enabled: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على قائمة العملاء مع حالة النسخ الاحتياطي
 * @return array قائمة العملاء مع حالة النسخ الاحتياطي
 */
function getClientsWithBackupStatus() {
    global $pdo;

    try {
        // التأكد من وجود الحقل
        ensureBackupColumnExists();

        $stmt = $pdo->query("
            SELECT
                client_id,
                business_name,
                owner_name,
                email,
                phone,
                is_active,
                backup_enabled,
                created_at
            FROM clients
            ORDER BY business_name ASC
        ");

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log('خطأ في جلب قائمة العملاء: ' . $e->getMessage());
        return [];
    }
}

/**
 * رسالة تعطيل النسخ الاحتياطي للعميل المحدد
 * @param int $client_id معرف العميل (اختياري)
 * @return string HTML للرسالة
 */
function getClientBackupDisabledMessage($client_id = null) {
    return '
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> تم تعطيل ميزة النسخ الاحتياطي لحسابكم من قبل إدارة النظام.
        يرجى التواصل مع الدعم الفني لإعادة تفعيل هذه الميزة.
    </div>';
}
