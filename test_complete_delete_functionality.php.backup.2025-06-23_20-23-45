<?php
/**
 * اختبار شامل لوظيفة حذف المنتجات من الجلسات
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار شامل لوظيفة حذف المنتجات من الجلسات</h1>";
echo "<hr>";

try {
    echo "<h2>1. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $pdo->exec("INSERT INTO sessions (device_id, start_time, status, client_id) VALUES (1, NOW(), 'active', 1)");
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: green;'>✅ تم العثور على جلسة نشطة: $session_id</p>";
    }
    
    // البحث عن منتجات أو إنشاؤها
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 3");
    $products = $stmt->fetchAll();
    
    if (count($products) < 2) {
        // إنشاء منتجات تجريبية
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES 
                   ('كوكاكولا', 3.50, 'مشروبات', 1),
                   ('شيبس', 2.00, 'وجبات خفيفة', 1),
                   ('قهوة', 5.00, 'مشروبات ساخنة', 1)");
        
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items ORDER BY id DESC LIMIT 3");
        $products = $stmt->fetchAll();
        echo "<p style='color: green;'>✅ تم إنشاء منتجات تجريبية</p>";
    } else {
        echo "<p style='color: green;'>✅ تم العثور على منتجات موجودة</p>";
    }
    
    // عرض المنتجات
    echo "<p><strong>المنتجات المتاحة:</strong></p>";
    echo "<ul>";
    foreach ($products as $product) {
        echo "<li>ID: {$product['id']}, الاسم: {$product['name']}, السعر: {$product['price']} ج.م</li>";
    }
    echo "</ul>";
    
    echo "<h2>2. إضافة منتجات للجلسة</h2>";
    
    // حذف أي منتجات موجودة مسبقاً
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ?")->execute([$session_id]);
    
    // إضافة منتجات متنوعة
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    
    // إضافة منتج 1 - عدة مرات
    $stmt->execute([$session_id, $products[0]['id'], 2, $products[0]['price']]);
    $stmt->execute([$session_id, $products[0]['id'], 1, $products[0]['price']]);
    
    // إضافة منتج 2 - مرة واحدة
    $stmt->execute([$session_id, $products[1]['id'], 3, $products[1]['price']]);
    
    // إضافة منتج 3 - مرة واحدة
    if (isset($products[2])) {
        $stmt->execute([$session_id, $products[2]['id'], 1, $products[2]['price']]);
    }
    
    echo "<p style='color: green;'>✅ تم إضافة منتجات متنوعة للجلسة</p>";
    
    // عرض المنتجات الحالية
    $stmt = $pdo->prepare("
        SELECT sp.*, ci.name as product_name 
        FROM session_products sp 
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id 
        WHERE sp.session_id = ? 
        ORDER BY sp.product_id, sp.id
    ");
    $stmt->execute([$session_id]);
    $session_products = $stmt->fetchAll();
    
    echo "<p><strong>المنتجات الحالية في الجلسة:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>اسم المنتج</th><th>معرف المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>";
    foreach ($session_products as $sp) {
        $total = $sp['quantity'] * $sp['price'];
        echo "<tr>";
        echo "<td>{$sp['id']}</td>";
        echo "<td>{$sp['product_name']}</td>";
        echo "<td>{$sp['product_id']}</td>";
        echo "<td>{$sp['quantity']}</td>";
        echo "<td>{$sp['price']}</td>";
        echo "<td>{$total}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. اختبار API جلب المنتجات</h2>";
    
    $get_api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/get_session_products.php?session_id=' . $session_id;
    echo "<p>🔗 رابط API جلب المنتجات: <a href='$get_api_url' target='_blank'>$get_api_url</a></p>";
    
    // اختبار API جلب المنتجات
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $get_api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $get_response = curl_exec($curl);
    $get_http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p><strong>كود الاستجابة HTTP:</strong> $get_http_code</p>";
    
    $get_result = json_decode($get_response, true);
    if ($get_result && $get_result['success']) {
        echo "<p style='color: green;'>✅ نجح جلب المنتجات!</p>";
        echo "<p><strong>عدد المنتجات:</strong> " . count($get_result['products']) . "</p>";
        
        // عرض المنتجات كما تظهر في الواجهة
        echo "<p><strong>المنتجات كما تظهر في الواجهة:</strong></p>";
        echo "<ul>";
        foreach ($get_result['products'] as $product) {
            echo "<li>{$product['product_name']} - الكمية: {$product['quantity']} - السعر: {$product['price']} - الإجمالي: {$product['total']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ فشل في جلب المنتجات</p>";
        echo "<pre>" . htmlspecialchars($get_response) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لحذف المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار JavaScript - محاكاة modal تعديل الجلسة</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>منتجات الجلسة</h5>
                    </div>
                    <div class="card-body">
                        <div id="edit_session_products">
                            <div class="text-center p-3">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2">جاري تحميل المنتجات...</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-primary btn-sm" onclick="loadEditSessionProducts(<?php echo $session_id; ?>)">
                            <i class="fas fa-refresh me-2"></i>إعادة تحميل المنتجات
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>سجل الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            <!-- سيتم ملء السجل هنا -->
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-secondary btn-sm" onclick="clearLog()">
                            <i class="fas fa-trash me-2"></i>مسح السجل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    
    // دالة تسجيل الأحداث
    function log(message, type = 'info') {
        const logDiv = document.getElementById('test-log');
        const timestamp = new Date().toLocaleTimeString('ar-EG');
        const colors = {
            'info': '#007bff',
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107'
        };
        
        logDiv.innerHTML += `<div style="color: ${colors[type]}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function clearLog() {
        document.getElementById('test-log').innerHTML = '';
    }
    
    // تحميل المنتجات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        log('بدء اختبار تحميل المنتجات...', 'info');
        loadEditSessionProducts(testSessionId);
    });
    </script>
    
    <!-- تضمين دوال JavaScript من ملف sessions.php -->
    <script>
    <?php
    // استخراج دوال JavaScript من ملف sessions.php
    $sessions_content = file_get_contents('client/sessions.php');
    
    // استخراج دالة loadEditSessionProducts
    preg_match('/function loadEditSessionProducts\(.*?\n}\n/s', $sessions_content, $matches);
    if (isset($matches[0])) {
        echo $matches[0];
    }
    
    // استخراج دالة deleteEditSessionProduct
    preg_match('/function deleteEditSessionProduct\(.*?\n}\n/s', $sessions_content, $matches);
    if (isset($matches[0])) {
        echo $matches[0];
    }
    ?>
    </script>
</body>
</html>
