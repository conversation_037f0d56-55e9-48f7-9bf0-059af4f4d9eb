<?php
/**
 * إصلاح مشاكل عرض المبالغ في صفحة الماليات - PlayGood
 * يصلح: خلط البيانات، أنواع البيانات الخاطئة، القيم الفارغة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشاكل عرض المبالغ في صفحة الماليات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    $fixes_applied = 0;
    
    // 1. إصلاح المصروفات - التأكد من أن المبالغ أرقام
    echo "<h2>1. إصلاح بيانات المصروفات</h2>";
    
    try {
        // البحث عن مصروفات بمبالغ غير صحيحة
        $problematic_expenses = $pdo->prepare("
            SELECT id, amount, expense_type_id
            FROM expenses 
            WHERE client_id = ? 
            AND (amount IS NULL OR amount = '' OR amount = '0' OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$')
        ");
        $problematic_expenses->execute([$client_id]);
        $bad_expenses = $problematic_expenses->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_expenses)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_expenses) . " مصروف بمبالغ غير صحيحة</p>";
            
            foreach ($bad_expenses as $expense) {
                // تعيين قيمة افتراضية أو حذف السجل
                if (empty($expense['amount']) || $expense['amount'] === '0') {
                    // حذف المصروفات الفارغة
                    $delete_stmt = $pdo->prepare("DELETE FROM expenses WHERE id = ?");
                    $delete_stmt->execute([$expense['id']]);
                    echo "<p style='color: red;'>🗑️ تم حذف مصروف فارغ (ID: {$expense['id']})</p>";
                    $fixes_applied++;
                } else {
                    // محاولة تصحيح المبلغ
                    $cleaned_amount = preg_replace('/[^0-9.]/', '', $expense['amount']);
                    if (is_numeric($cleaned_amount) && $cleaned_amount > 0) {
                        $update_stmt = $pdo->prepare("UPDATE expenses SET amount = ? WHERE id = ?");
                        $update_stmt->execute([$cleaned_amount, $expense['id']]);
                        echo "<p style='color: blue;'>🔧 تم تصحيح مبلغ مصروف (ID: {$expense['id']}) من '{$expense['amount']}' إلى '$cleaned_amount'</p>";
                        $fixes_applied++;
                    }
                }
            }
        } else {
            echo "<p style='color: green;'>✅ جميع مبالغ المصروفات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح المصروفات: " . $e->getMessage() . "</p>";
    }
    
    // 2. إصلاح الإيرادات الإضافية
    echo "<h2>2. إصلاح بيانات الإيرادات الإضافية</h2>";
    
    try {
        // البحث عن إيرادات بمبالغ غير صحيحة
        $problematic_income = $pdo->prepare("
            SELECT id, amount, income_type_id
            FROM additional_income 
            WHERE client_id = ? 
            AND (amount IS NULL OR amount = '' OR amount = '0' OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$')
        ");
        $problematic_income->execute([$client_id]);
        $bad_income = $problematic_income->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_income)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_income) . " إيراد بمبالغ غير صحيحة</p>";
            
            foreach ($bad_income as $income) {
                if (empty($income['amount']) || $income['amount'] === '0') {
                    // حذف الإيرادات الفارغة
                    $delete_stmt = $pdo->prepare("DELETE FROM additional_income WHERE id = ?");
                    $delete_stmt->execute([$income['id']]);
                    echo "<p style='color: red;'>🗑️ تم حذف إيراد فارغ (ID: {$income['id']})</p>";
                    $fixes_applied++;
                } else {
                    // محاولة تصحيح المبلغ
                    $cleaned_amount = preg_replace('/[^0-9.]/', '', $income['amount']);
                    if (is_numeric($cleaned_amount) && $cleaned_amount > 0) {
                        $update_stmt = $pdo->prepare("UPDATE additional_income SET amount = ? WHERE id = ?");
                        $update_stmt->execute([$cleaned_amount, $income['id']]);
                        echo "<p style='color: blue;'>🔧 تم تصحيح مبلغ إيراد (ID: {$income['id']}) من '{$income['amount']}' إلى '$cleaned_amount'</p>";
                        $fixes_applied++;
                    }
                }
            }
        } else {
            echo "<p style='color: green;'>✅ جميع مبالغ الإيرادات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح الإيرادات: " . $e->getMessage() . "</p>";
    }
    
    // 3. إصلاح تكاليف الجلسات
    echo "<h2>3. إصلاح تكاليف الجلسات</h2>";
    
    try {
        // البحث عن جلسات بتكاليف غير صحيحة
        $problematic_sessions = $pdo->prepare("
            SELECT s.session_id, s.total_cost, d.device_name, c.name as customer_name
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = ? 
            AND s.status = 'completed'
            AND (s.total_cost IS NULL OR s.total_cost = '' OR s.total_cost = '0' OR NOT s.total_cost REGEXP '^[0-9]+\.?[0-9]*$')
        ");
        $problematic_sessions->execute([$client_id]);
        $bad_sessions = $problematic_sessions->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_sessions)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_sessions) . " جلسة بتكاليف غير صحيحة</p>";
            
            foreach ($bad_sessions as $session) {
                // إعادة حساب التكلفة
                $recalc_stmt = $pdo->prepare("
                    SELECT 
                        TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
                        d.single_rate, d.hourly_rate
                    FROM sessions s
                    JOIN devices d ON s.device_id = d.device_id
                    WHERE s.session_id = ?
                ");
                $recalc_stmt->execute([$session['session_id']]);
                $session_data = $recalc_stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($session_data) {
                    $duration_minutes = $session_data['duration_minutes'] ?? 0;
                    $hourly_rate = $session_data['single_rate'] ?? $session_data['hourly_rate'] ?? 0;
                    
                    if ($duration_minutes > 0 && $hourly_rate > 0) {
                        $new_cost = ceil($duration_minutes / 60) * $hourly_rate;
                        
                        $update_stmt = $pdo->prepare("UPDATE sessions SET total_cost = ? WHERE session_id = ?");
                        $update_stmt->execute([$new_cost, $session['session_id']]);
                        
                        echo "<p style='color: blue;'>🔧 تم إعادة حساب تكلفة الجلسة (ID: {$session['session_id']}) = $new_cost ج.م</p>";
                        $fixes_applied++;
                    }
                }
            }
        } else {
            echo "<p style='color: green;'>✅ جميع تكاليف الجلسات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح تكاليف الجلسات: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

// ملخص الإصلاحات
echo "<h2>📊 ملخص الإصلاحات</h2>";
echo "<div style='background: " . ($fixes_applied > 0 ? "#d4edda" : "#fff3cd") . "; border: 1px solid " . ($fixes_applied > 0 ? "#c3e6cb" : "#ffeaa7") . "; color: " . ($fixes_applied > 0 ? "#155724" : "#856404") . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>" . ($fixes_applied > 0 ? "✅ تم تطبيق الإصلاحات" : "ℹ️ لا توجد إصلاحات مطلوبة") . "</h4>";
echo "<p><strong>عدد الإصلاحات المطبقة:</strong> $fixes_applied</p>";
if ($fixes_applied > 0) {
    echo "<p>تم إصلاح المشاكل بنجاح. يمكنك الآن زيارة صفحة الماليات للتحقق من النتائج.</p>";
} else {
    echo "<p>لم يتم العثور على مشاكل تحتاج إصلاح في البيانات المالية.</p>";
}
echo "</div>";

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔄 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>قم بزيارة صفحة الماليات للتحقق من النتائج</li>";
echo "<li>إذا استمرت المشكلة، تحقق من ملفات JavaScript</li>";
echo "<li>راجع كود عرض البيانات في صفحة الماليات</li>";
echo "<li>تأكد من أن النماذج تحفظ البيانات بالتنسيق الصحيح</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
