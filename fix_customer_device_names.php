<?php
require_once 'config/database.php';

echo "<h1>إصلاح ربط أسماء العملاء والأجهزة في الفواتير</h1>";

try {
    // 1. فحص المشكلة الحالية
    echo "<h2>1. فحص المشكلة الحالية</h2>";
    
    $current_invoices = $pdo->query("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            s.customer_id,
            s.device_id,
            c.name as customer_name,
            d.device_name,
            d.device_type
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الفواتير: <strong>" . count($current_invoices) . "</strong></p>";
    
    $missing_customers = 0;
    $missing_devices = 0;
    
    foreach ($current_invoices as $invoice) {
        if (empty($invoice['customer_name'])) $missing_customers++;
        if (empty($invoice['device_name'])) $missing_devices++;
    }
    
    echo "<p style='color: " . ($missing_customers > 0 ? 'red' : 'green') . ";'>فواتير بدون أسماء عملاء: <strong>$missing_customers</strong></p>";
    echo "<p style='color: " . ($missing_devices > 0 ? 'red' : 'green') . ";'>فواتير بدون أسماء أجهزة: <strong>$missing_devices</strong></p>";
    
    // 2. إنشاء عملاء افتراضيين للجلسات التي لا تحتوي على عملاء
    echo "<h2>2. إنشاء عملاء افتراضيين</h2>";
    
    // جلب الجلسات بدون عملاء
    $sessions_without_customers = $pdo->query("
        SELECT DISTINCT s.session_id, s.device_id, d.device_name, d.client_id
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE s.customer_id IS NULL AND s.status = 'completed'
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الجلسات بدون عملاء: <strong>" . count($sessions_without_customers) . "</strong></p>";
    
    if (count($sessions_without_customers) > 0) {
        $created_customers = 0;
        
        foreach ($sessions_without_customers as $session) {
            // إنشاء عميل افتراضي لكل جلسة
            $customer_name = "عميل الجلسة " . $session['session_id'];
            $customer_phone = "000000" . str_pad($session['session_id'], 4, '0', STR_PAD_LEFT);
            
            try {
                // التحقق من عدم وجود عميل بنفس الرقم
                $check_stmt = $pdo->prepare("SELECT customer_id FROM customers WHERE phone = ? AND client_id = ?");
                $check_stmt->execute([$customer_phone, $session['client_id']]);
                
                if (!$check_stmt->fetch()) {
                    // إنشاء العميل
                    $insert_customer = $pdo->prepare("
                        INSERT INTO customers (client_id, name, phone, email, notes, created_by)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $insert_customer->execute([
                        $session['client_id'],
                        $customer_name,
                        $customer_phone,
                        null,
                        "عميل تم إنشاؤه تلقائياً للجلسة " . $session['session_id'],
                        1
                    ]);
                    
                    $customer_id = $pdo->lastInsertId();
                    
                    // ربط العميل بالجلسة
                    $update_session = $pdo->prepare("UPDATE sessions SET customer_id = ? WHERE session_id = ?");
                    $update_session->execute([$customer_id, $session['session_id']]);
                    
                    $created_customers++;
                    echo "<p style='color: green;'>✅ تم إنشاء عميل للجلسة {$session['session_id']}: $customer_name</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء عميل للجلسة {$session['session_id']}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p style='color: green; font-weight: bold;'>✅ تم إنشاء $created_customers عميل جديد</p>";
    }
    
    // 3. التحقق من أسماء الأجهزة
    echo "<h2>3. التحقق من أسماء الأجهزة</h2>";
    
    $devices_check = $pdo->query("
        SELECT device_id, device_name, device_type, client_id
        FROM devices
        WHERE device_name IS NULL OR device_name = '' OR device_name = 'NULL'
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices_check) > 0) {
        echo "<p style='color: orange;'>⚠️ توجد " . count($devices_check) . " أجهزة بدون أسماء</p>";
        
        foreach ($devices_check as $device) {
            $new_name = $device['device_type'] . "_" . $device['device_id'];
            $update_device = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
            $update_device->execute([$new_name, $device['device_id']]);
            echo "<p style='color: green;'>✅ تم تحديث اسم الجهاز {$device['device_id']} إلى: $new_name</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ جميع الأجهزة لديها أسماء صحيحة</p>";
    }
    
    // 4. اختبار الاستعلام المحدث
    echo "<h2>4. اختبار النتائج بعد الإصلاح</h2>";
    
    $updated_invoices = $pdo->query("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            s.customer_id,
            s.device_id,
            c.name as customer_name,
            c.phone as customer_phone,
            d.device_name,
            d.device_type
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
        ORDER BY i.invoice_id DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Invoice ID</th><th>Invoice Number</th><th>Session ID</th><th>Customer Name</th><th>Customer Phone</th><th>Device Name</th><th>Device Type</th>";
    echo "</tr>";
    
    foreach ($updated_invoices as $invoice) {
        $customer_color = !empty($invoice['customer_name']) ? 'green' : 'red';
        $device_color = !empty($invoice['device_name']) ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>" . $invoice['invoice_id'] . "</td>";
        echo "<td>" . $invoice['invoice_number'] . "</td>";
        echo "<td>" . $invoice['session_id'] . "</td>";
        echo "<td style='color: $customer_color;'>" . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . htmlspecialchars($invoice['customer_phone'] ?? 'غير محدد') . "</td>";
        echo "<td style='color: $device_color;'>" . htmlspecialchars($invoice['device_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . htmlspecialchars($invoice['device_type'] ?? 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. إحصائيات نهائية
    echo "<h2>5. الإحصائيات النهائية</h2>";
    
    $final_missing_customers = 0;
    $final_missing_devices = 0;
    
    foreach ($updated_invoices as $invoice) {
        if (empty($invoice['customer_name'])) $final_missing_customers++;
        if (empty($invoice['device_name'])) $final_missing_devices++;
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 النتائج النهائية:</h4>";
    echo "<ul>";
    echo "<li><strong>إجمالي الفواتير:</strong> " . count($updated_invoices) . "</li>";
    echo "<li><strong>فواتير بأسماء عملاء:</strong> " . (count($updated_invoices) - $final_missing_customers) . "</li>";
    echo "<li><strong>فواتير بأسماء أجهزة:</strong> " . (count($updated_invoices) - $final_missing_devices) . "</li>";
    echo "<li><strong>فواتير بدون أسماء عملاء:</strong> <span style='color: " . ($final_missing_customers > 0 ? 'red' : 'green') . ";'>$final_missing_customers</span></li>";
    echo "<li><strong>فواتير بدون أسماء أجهزة:</strong> <span style='color: " . ($final_missing_devices > 0 ? 'red' : 'green') . ";'>$final_missing_devices</span></li>";
    echo "</ul>";
    echo "</div>";
    
    if ($final_missing_customers == 0 && $final_missing_devices == 0) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 تم إصلاح جميع المشاكل بنجاح!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='test_invoice_fix.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الفواتير</a>";
echo "</div>";
?>
