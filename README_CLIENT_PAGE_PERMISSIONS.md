# نظام صلاحيات الصفحات للعملاء - PlayGood

## نظرة عامة
تم إضافة نظام شامل لإدارة صلاحيات الصفحات للعملاء، مما يتيح للأدمن التحكم الكامل في الصفحات التي يمكن لكل عميل الوصول إليها أو تعطيل الوصول إليها.

## الميزات الرئيسية

### 1. إدارة شاملة للصلاحيات
- تحديد الصفحات المتاحة لكل عميل بشكل فردي
- صفحات افتراضية تُمنح تلقائياً للعملاء الجدد
- إمكانية إعادة تعيين الصلاحيات إلى الإعدادات الافتراضية

### 2. واجهة سهلة الاستخدام
- صفحة إدارة متقدمة في لوحة تحكم الأدمن
- تجميع الصفحات حسب الفئات
- مفاتيح تبديل سهلة لتفعيل/تعطيل الصفحات

### 3. نظام أمان متقدم
- فحص تلقائي للصلاحيات عند الوصول للصفحات
- إعادة توجيه آمنة للصفحات غير المسموحة
- حماية من الوصول المباشر للصفحات

## الملفات المضافة/المعدلة

### ملفات قاعدة البيانات
- `create_client_page_permissions_system.sql` - إنشاء جداول النظام
- `setup_client_page_permissions.php` - أداة إعداد النظام

### ملفات الأدمن
- `admin/client_permissions.php` - صفحة إدارة صلاحيات العملاء
- `admin/includes/sidebar.php` - إضافة رابط صلاحيات العملاء

### ملفات العميل
- `client/includes/auth.php` - نظام التحقق من الصلاحيات المحدث
- `client/includes/header.php` - القائمة الجانبية المحدثة

### الجداول الجديدة
- `client_pages` - الصفحات المتاحة للعملاء
- `client_page_permissions` - صلاحيات الصفحات لكل عميل

## الصفحات المتاحة

### الصفحات الأساسية (افتراضية)
- **لوحة التحكم** - الصفحة الرئيسية وإحصائيات المحل
- **الملف الشخصي** - إدارة بيانات الحساب الشخصي
- **الأجهزة** - إضافة وإدارة أجهزة الألعاب
- **الجلسات** - بدء وإنهاء جلسات اللعب
- **العملاء** - إدارة بيانات عملاء المحل
- **التقارير** - تقارير مالية وإحصائية
- **الفواتير** - إدارة وطباعة الفواتير
- **الإعدادات** - إعدادات النظام والمحل

### الصفحات الاختيارية
- **الغرف** - تنظيم الأجهزة في غرف
- **الكافتيريا** - إدارة منتجات الكافتيريا
- **الأوردرات** - إدارة الطلبات المستقلة
- **الموظفين** - إدارة موظفي المحل
- **الحضور** - تسجيل حضور وانصراف الموظفين
- **الورديات** - تنظيم ورديات العمل
- **المالية** - إدارة المصروفات والإيرادات
- **المخزون** - إدارة مخزون المنتجات
- **الحجوزات** - إدارة حجوزات العملاء
- **إعدادات الفواتير** - تخصيص شكل ومحتوى الفواتير

## طريقة التشغيل

### 1. إعداد النظام
```
http://localhost/playgood/setup_client_page_permissions.php
```

### 2. الوصول لإدارة الصلاحيات
```
http://localhost/playgood/admin/client_permissions.php
```

## كيفية الاستخدام

### 1. إدارة صلاحيات عميل
1. اختر العميل من القائمة المنسدلة
2. استخدم مفاتيح التبديل لتفعيل/تعطيل الصفحات
3. التغييرات تُحفظ تلقائياً

### 2. إعادة تعيين الصلاحيات
1. اختر العميل
2. اضغط على "إعادة تعيين للافتراضي"
3. سيتم حذف جميع الصلاحيات المخصصة وإعادة الصفحات الافتراضية

### 3. فهم الشارات
- **🏠 افتراضي** - صفحة متاحة افتراضياً للعملاء الجدد
- **⚙️ اختياري** - صفحة تحتاج موافقة الأدمن للوصول إليها

## الدوال المتاحة

### في ملف `client/includes/auth.php`

#### `hasPagePermission($page_name)`
```php
// التحقق من صلاحية الوصول لصفحة معينة
if (hasPagePermission('devices')) {
    // العميل لديه صلاحية الوصول لصفحة الأجهزة
}
```

#### `getAllowedPages()`
```php
// جلب قائمة الصفحات المسموحة للعميل الحالي
$allowed_pages = getAllowedPages();
foreach ($allowed_pages as $page) {
    echo $page['page_label'];
}
```

#### `checkCurrentPagePermission()`
```php
// فحص تلقائي للصفحة الحالية (يتم استدعاؤها تلقائياً)
```

## الأمان والحماية

### 1. فحص تلقائي للصلاحيات
- يتم فحص صلاحية كل صفحة تلقائياً عند الوصول إليها
- إعادة توجيه آمنة للصفحات غير المسموحة

### 2. الصفحات المستثناة
الصفحات التالية مستثناة من فحص الصلاحيات:
- `login.php` - صفحة تسجيل الدخول
- `logout.php` - صفحة تسجيل الخروج
- `profile.php` - الملف الشخصي

### 3. التوافق مع النظام القديم
- إذا لم يكن النظام مفعل، يُسمح بالوصول لجميع الصفحات
- في حالة حدوث خطأ، يُسمح بالوصول (للأمان)

## قاعدة البيانات

### جدول `client_pages`
```sql
- page_id (INT, PRIMARY KEY)
- page_name (VARCHAR) - اسم الصفحة
- page_label (VARCHAR) - التسمية العربية
- page_url (VARCHAR) - رابط الصفحة
- page_icon (VARCHAR) - أيقونة الصفحة
- category (VARCHAR) - فئة الصفحة
- description (TEXT) - وصف الصفحة
- is_active (BOOLEAN) - هل الصفحة نشطة
- is_default (BOOLEAN) - هل الصفحة افتراضية
```

### جدول `client_page_permissions`
```sql
- id (INT, PRIMARY KEY)
- client_id (INT, FOREIGN KEY)
- page_id (INT, FOREIGN KEY)
- is_enabled (BOOLEAN) - هل الصلاحية مفعلة
- granted_by (INT) - معرف الأدمن الذي منح الصلاحية
- granted_at (TIMESTAMP) - تاريخ منح الصلاحية
- updated_at (TIMESTAMP) - تاريخ آخر تحديث
```

## المشاهدات (Views)

### `client_page_permissions_detailed`
عرض مفصل لصلاحيات جميع العملاء مع معلومات الصفحات

## الإجراءات المخزنة

### `GrantDefaultPermissionsToClient(client_id)`
منح الصلاحيات الافتراضية لعميل محدد

## المشغلات (Triggers)

### `after_client_insert`
منح الصلاحيات الافتراضية تلقائياً عند إضافة عميل جديد

## التطوير المستقبلي

### ميزات مقترحة
- إضافة مجموعات صلاحيات جاهزة
- تصدير/استيراد إعدادات الصلاحيات
- سجل تغييرات الصلاحيات
- إشعارات عند تغيير الصلاحيات
- صلاحيات مؤقتة بتاريخ انتهاء

### تحسينات تقنية
- تخزين مؤقت للصلاحيات
- API لإدارة الصلاحيات
- واجهة AJAX محسنة
- دعم الصلاحيات المتقدمة (قراءة/كتابة/حذف)

## الدعم والمساعدة

### في حالة حدوث مشاكل
1. تأكد من تشغيل ملف الإعداد أولاً
2. تحقق من صلاحيات قاعدة البيانات
3. راجع سجلات الأخطاء في المتصفح
4. تأكد من تحديث ملفات النظام

### اختبار النظام
```
http://localhost/playgood/setup_client_page_permissions.php
```

يعرض هذا الملف حالة النظام ويختبر جميع المكونات.
