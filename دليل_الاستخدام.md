# دليل استخدام قاعدة البيانات المحسنة - Station System

## نظرة عامة
هذا الدليل يوضح كيفية استخدام قاعدة البيانات المحسنة لنظام إدارة مراكز الألعاب.

## الملفات المتوفرة

### 1. `station_fixed.sql`
الملف الرئيسي المحسن الذي يحتوي على:
- بنية قاعدة البيانات الكاملة
- جميع الجداول مع الفهارس والقيود
- البيانات الأساسية (مدير النظام، خطط الاشتراك، الصلاحيات)

### 2. `station_views_procedures.sql`
ملف إضافي يحتوي على:
- Views مفيدة للتقارير والإحصائيات
- إجراءات مخزنة للعمليات المعقدة
- وظائف مساعدة للنظام

### 3. `تقرير_إصلاح_قاعدة_البيانات.md`
تقرير مفصل عن المشاكل المُصلحة والتحسينات المطبقة

## خطوات التطبيق

### الخطوة 1: النسخ الاحتياطي
```sql
-- عمل نسخة احتياطية من قاعدة البيانات الحالية
mysqldump -u username -p station > backup_station_$(date +%Y%m%d_%H%M%S).sql
```

### الخطوة 2: تطبيق قاعدة البيانات الجديدة
```sql
-- تطبيق الملف الرئيسي
mysql -u username -p < station_fixed.sql

-- تطبيق Views والإجراءات المخزنة
mysql -u username -p < station_views_procedures.sql
```

### الخطوة 3: التحقق من التطبيق
```sql
-- التحقق من الجداول
SHOW TABLES;

-- التحقق من Views
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- التحقق من الإجراءات المخزنة
SHOW PROCEDURE STATUS WHERE Db = 'station';
```

## استخدام Views المتوفرة

### 1. عرض تفاصيل الجلسات
```sql
-- عرض جميع الجلسات مع التفاصيل
SELECT * FROM sessions_detailed 
WHERE client_id = 1 
ORDER BY start_time DESC;
```

### 2. إحصائيات الأجهزة
```sql
-- عرض إحصائيات الأجهزة
SELECT * FROM devices_stats 
WHERE client_id = 1 
ORDER BY total_revenue DESC;
```

### 3. إحصائيات العملاء
```sql
-- عرض أفضل العملاء
SELECT * FROM customers_stats 
WHERE client_id = 1 
ORDER BY total_spent DESC 
LIMIT 10;
```

### 4. تفاصيل المخزون
```sql
-- عرض المنتجات منخفضة المخزون
SELECT * FROM inventory_detailed 
WHERE client_id = 1 
AND stock_status IN ('نفد المخزون', 'مخزون منخفض');
```

### 5. الملخص المالي الشهري
```sql
-- عرض الإيرادات والمصروفات الشهرية
SELECT 
    year, month, type,
    SUM(total_amount) as total
FROM monthly_financial_summary 
WHERE client_id = 1 
GROUP BY year, month, type 
ORDER BY year DESC, month DESC;
```

## استخدام الإجراءات المخزنة

### 1. التحقق من حدود العميل (محسن)
```sql
-- التحقق من حد الأجهزة
CALL CheckClientLimit(1, 'devices', 'max_devices', @limit, @usage, @can_add, @message);
SELECT @limit AS limit_value, @usage AS current_usage, @can_add AS can_add, @message AS message;

-- التحقق من حد المنتجات
CALL CheckClientLimit(1, 'products', 'max_products', @limit, @usage, @can_add, @message);
SELECT @limit, @usage, @can_add, @message;

-- التحقق من حد الموظفين
CALL CheckClientLimit(1, 'employees', 'max_employees', @limit, @usage, @can_add, @message);
SELECT @limit, @usage, @can_add, @message;
```

### 2. منح الصلاحيات الافتراضية للعميل الجديد
```sql
-- منح الصلاحيات الافتراضية
CALL GrantDefaultPermissionsToClient(1, @success, @message);
SELECT @success, @message;
```

### 3. الحصول على إحصائيات العميل
```sql
-- استدعاء إجراء الإحصائيات
CALL GetClientStatistics(1, '2025-06-01', '2025-06-30');
```

### 2. بدء جلسة جديدة
```sql
-- بدء جلسة جديدة
CALL StartNewSession(
    1,          -- معرف العميل
    16,         -- معرف الجهاز
    43,         -- معرف العميل (زائر المحل)
    'hourly',   -- نوع الجلسة
    'FIFA',     -- نوع اللعبة
    2,          -- عدد اللاعبين
    @session_id, @success, @message
);

-- عرض النتيجة
SELECT @session_id, @success, @message;
```

### 3. إنهاء الجلسة
```sql
-- إنهاء الجلسة
CALL EndSession(
    35,         -- معرف الجلسة
    1,          -- معرف العميل
    @success, @message, @total_cost
);

-- عرض النتيجة
SELECT @success, @message, @total_cost;
```

### 4. تحديث المخزون
```sql
-- إضافة مخزون جديد
CALL UpdateInventory(
    13,         -- معرف المنتج
    1,          -- معرف العميل
    'in',       -- نوع الحركة (إدخال)
    50,         -- الكمية
    3.00,       -- سعر الوحدة
    'purchase', -- نوع المرجع
    NULL,       -- معرف المرجع
    'شراء جديد', -- ملاحظات
    1,          -- المستخدم المنفذ
    @success, @message
);

-- عرض النتيجة
SELECT @success, @message;
```

## الاستعلامات المفيدة

### 1. إحصائيات يومية
```sql
-- إيرادات اليوم
SELECT 
    COUNT(*) as sessions_count,
    SUM(total_cost) as daily_revenue
FROM sessions 
WHERE client_id = 1 
AND DATE(start_time) = CURDATE()
AND payment_status = 'paid';
```

### 2. الأجهزة المتاحة
```sql
-- عرض الأجهزة المتاحة
SELECT device_id, device_name, device_type, hourly_rate
FROM devices 
WHERE client_id = 1 
AND status = 'available'
ORDER BY device_name;
```

### 3. المنتجات الأكثر مبيعاً
```sql
-- المنتجات الأكثر مبيعاً
SELECT 
    ci.name,
    SUM(sp.quantity) as total_sold,
    SUM(sp.total_price) as total_revenue
FROM session_products sp
JOIN cafeteria_items ci ON sp.product_id = ci.id
JOIN sessions s ON sp.session_id = s.session_id
WHERE s.client_id = 1
AND DATE(s.start_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY ci.id, ci.name
ORDER BY total_sold DESC
LIMIT 10;
```

### 4. تقرير الموظفين
```sql
-- عرض الموظفين النشطين
SELECT 
    name, role, phone, hire_date,
    DATEDIFF(CURDATE(), hire_date) as days_employed
FROM employees 
WHERE client_id = 1 
AND is_active = 1
ORDER BY hire_date;
```

## نصائح للأداء

### 1. استخدام الفهارس
- تم إضافة فهارس محسنة لجميع الجداول
- استخدم `EXPLAIN` لفحص خطة تنفيذ الاستعلامات

### 2. تحسين الاستعلامات
```sql
-- استخدم LIMIT عند عدم الحاجة لجميع النتائج
SELECT * FROM sessions_detailed 
WHERE client_id = 1 
ORDER BY start_time DESC 
LIMIT 50;

-- استخدم WHERE لتقليل البيانات المسترجعة
SELECT * FROM inventory_detailed 
WHERE client_id = 1 
AND status = 'available';
```

### 3. النسخ الاحتياطي المنتظم
```bash
# إعداد نسخ احتياطي يومي
0 2 * * * mysqldump -u username -p station > /backup/station_$(date +\%Y\%m\%d).sql
```

## استكشاف الأخطاء

### 1. مشاكل الاتصال
```sql
-- التحقق من حالة قاعدة البيانات
SHOW DATABASES;
USE station;
SHOW TABLES;
```

### 2. مشاكل الصلاحيات
```sql
-- التحقق من صلاحيات المستخدم
SHOW GRANTS FOR 'username'@'localhost';
```

### 3. مشاكل الأداء
```sql
-- عرض الاستعلامات البطيئة
SHOW PROCESSLIST;

-- فحص حالة الجداول
CHECK TABLE sessions, devices, customers;
```

## الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملف `تقرير_إصلاح_قاعدة_البيانات.md`
2. تحقق من logs قاعدة البيانات
3. استخدم `EXPLAIN` لفحص الاستعلامات البطيئة
4. تواصل مع فريق التطوير للمساعدة التقنية

---
**ملاحظة**: تأكد من عمل نسخة احتياطية قبل تطبيق أي تغييرات على قاعدة البيانات الإنتاجية.
