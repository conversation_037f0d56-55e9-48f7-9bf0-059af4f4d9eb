<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';
require_once 'includes/auth.php';

// حماية الصفحة بالنظام الجديد
protectAdminPage($pdo);

// التحقق من تسجيل الدخول وصلاحية الوصول لهذه الصفحة (النظام القديم كـ backup)
checkAdminSession();

// إحصائيات أساسية
$stats = [];

try {
    // إجمالي العملاء
    $stmt = $pdo->query("SELECT COUNT(*) as total_clients FROM clients");
    $stats['total_clients'] = $stmt->fetch()['total_clients'];

    // العملاء النشطين
    $stmt = $pdo->query("SELECT COUNT(*) as active_clients FROM clients WHERE is_active = 1");
    $stats['active_clients'] = $stmt->fetch()['active_clients'];

    // العملاء غير النشطين
    $stmt = $pdo->query("SELECT COUNT(*) as inactive_clients FROM clients WHERE is_active = 0");
    $stats['inactive_clients'] = $stmt->fetch()['inactive_clients'];

    // إجمالي الأجهزة
    $stmt = $pdo->query("SELECT COUNT(*) as total_devices FROM devices");
    $stats['total_devices'] = $stmt->fetch()['total_devices'];

    // العملاء الجدد هذا الشهر
    $stmt = $pdo->query("SELECT COUNT(*) as new_clients FROM clients WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
    $stats['new_clients'] = $stmt->fetch()['new_clients'];

    // إجمالي الجلسات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) as active_sessions FROM sessions WHERE status = 'active'");
    $stats['active_sessions'] = $stmt->fetch()['active_sessions'];

    // إجمالي الإيرادات هذا الشهر
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(s.total_cost), 0) as month_revenue
        FROM sessions s
        WHERE s.status = 'completed'
        AND MONTH(s.start_time) = MONTH(NOW())
        AND YEAR(s.start_time) = YEAR(NOW())
    ");
    $stats['month_revenue'] = $stmt->fetch()['month_revenue'];

    // متوسط الإيرادات لكل عميل
    $stmt = $pdo->query("
        SELECT COALESCE(AVG(client_revenue), 0) as avg_client_revenue
        FROM (
            SELECT c.client_id, COALESCE(SUM(s.total_cost), 0) as client_revenue
            FROM clients c
            LEFT JOIN devices d ON c.client_id = d.client_id
            LEFT JOIN sessions s ON d.device_id = s.device_id AND s.status = 'completed'
            WHERE MONTH(s.start_time) = MONTH(NOW()) AND YEAR(s.start_time) = YEAR(NOW())
            GROUP BY c.client_id
        ) as client_revenues
    ");
    $stats['avg_client_revenue'] = $stmt->fetch()['avg_client_revenue'];

    // أحدث العملاء المسجلين
    $stmt = $pdo->query("
        SELECT client_id, business_name, owner_name, email, phone, city, subscription_plan, created_at, is_active
        FROM clients
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $recent_clients = $stmt->fetchAll();

    // العملاء الأكثر نشاطاً (حسب عدد الجلسات)
    $stmt = $pdo->query("
        SELECT c.client_id, c.business_name, c.owner_name, c.city, c.subscription_plan,
               COUNT(s.session_id) as total_sessions,
               COALESCE(SUM(s.total_cost), 0) as total_revenue,
               COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active_sessions
        FROM clients c
        LEFT JOIN devices d ON c.client_id = d.client_id
        LEFT JOIN sessions s ON d.device_id = s.device_id
        WHERE c.is_active = 1
        GROUP BY c.client_id
        ORDER BY total_sessions DESC
        LIMIT 10
    ");
    $top_clients = $stmt->fetchAll();

    // إحصائيات خطط الاشتراك
    $stmt = $pdo->query("
        SELECT subscription_plan,
               COUNT(*) as count,
               COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count
        FROM clients
        GROUP BY subscription_plan
    ");
    $subscription_stats = $stmt->fetchAll();

    // العملاء حسب المدن
    $stmt = $pdo->query("
        SELECT city, COUNT(*) as count
        FROM clients
        WHERE city IS NOT NULL AND city != ''
        GROUP BY city
        ORDER BY count DESC
        LIMIT 10
    ");
    $city_stats = $stmt->fetchAll();

    // العملاء الذين انتهت اشتراكاتهم أو ستنتهي قريباً
    $stmt = $pdo->query("
        SELECT client_id, business_name, owner_name, email, phone, subscription_end, subscription_plan
        FROM clients
        WHERE subscription_end IS NOT NULL
        AND subscription_end <= DATE_ADD(NOW(), INTERVAL 30 DAY)
        AND is_active = 1
        ORDER BY subscription_end ASC
        LIMIT 10
    ");
    $expiring_subscriptions = $stmt->fetchAll();

} catch (Exception $e) {
    // في حالة حدوث خطأ، تعيين قيم افتراضية
    $stats = [
        'total_clients' => 0,
        'active_clients' => 0,
        'inactive_clients' => 0,
        'total_devices' => 0,
        'new_clients' => 0,
        'active_sessions' => 0,
        'month_revenue' => 0,
        'avg_client_revenue' => 0
    ];
    $recent_clients = [];
    $top_clients = [];
    $subscription_stats = [];
    $city_stats = [];
    $expiring_subscriptions = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js" rel="preload" as="script">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .stats-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .stats-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stats-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .stats-card-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 1.5rem;
        }
        .nav-link {
            border-radius: 10px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
        }
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        .alert {
            border-radius: 15px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- المحتوى الرئيسي -->
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-dashboard me-2"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>

                <!-- الإحصائيات الأساسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['total_clients']); ?></h3>
                                <p class="mb-0">إجمالي العملاء</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['active_clients']); ?></h3>
                                <p class="mb-0">العملاء النشطين</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-info">
                            <div class="card-body text-center">
                                <i class="fas fa-gamepad fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['total_devices']); ?></h3>
                                <p class="mb-0">إجمالي الأجهزة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['new_clients']); ?></h3>
                                <p class="mb-0">عملاء جدد هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات إضافية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-user-times fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['inactive_clients']); ?></h3>
                                <p class="mb-0">العملاء غير النشطين</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-play fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['active_sessions']); ?></h3>
                                <p class="mb-0">الجلسات النشطة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['month_revenue'], 2); ?> ج.م</h3>
                                <p class="mb-0">إيرادات هذا الشهر</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3><?php echo number_format($stats['avg_client_revenue'], 2); ?> ج.م</h3>
                                <p class="mb-0">متوسط إيرادات العميل</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتحليلات -->
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    توزيع خطط الاشتراك
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="subscriptionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    العملاء حسب المدن
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="cityChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جداول البيانات -->
                <div class="row mb-4">
                    <!-- أحدث العملاء -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-plus me-2"></i>
                                    أحدث العملاء المسجلين
                                </h5>
                                <a href="clients.php" class="btn btn-sm btn-outline-primary">
                                    عرض الكل
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم المحل</th>
                                                <th>المالك</th>
                                                <th>الخطة</th>
                                                <th>الحالة</th>
                                                <th>تاريخ التسجيل</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recent_clients)): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center text-muted">لا توجد عملاء مسجلين</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($recent_clients as $client): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($client['business_name']); ?></strong>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($client['owner_name']); ?></td>
                                                        <td>
                                                            <?php
                                                            $plan_badges = [
                                                                'basic' => 'bg-secondary',
                                                                'premium' => 'bg-warning',
                                                                'enterprise' => 'bg-success'
                                                            ];
                                                            $plan_names = [
                                                                'basic' => 'أساسية',
                                                                'premium' => 'مميزة',
                                                                'enterprise' => 'مؤسسية'
                                                            ];
                                                            ?>
                                                            <span class="badge <?php echo $plan_badges[$client['subscription_plan']]; ?>">
                                                                <?php echo $plan_names[$client['subscription_plan']]; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge <?php echo $client['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo $client['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?php echo date('Y-m-d', strtotime($client['created_at'])); ?>
                                                            </small>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- العملاء الأكثر نشاطاً -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-star me-2"></i>
                                    العملاء الأكثر نشاطاً
                                </h5>
                                <a href="reports.php" class="btn btn-sm btn-outline-primary">
                                    تقرير مفصل
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم المحل</th>
                                                <th>عدد الجلسات</th>
                                                <th>الإيرادات</th>
                                                <th>الجلسات النشطة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($top_clients)): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted">لا توجد بيانات متاحة</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($top_clients as $client): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($client['business_name']); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($client['owner_name']); ?></small>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary">
                                                                <?php echo number_format($client['total_sessions']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <strong class="text-success">
                                                                <?php echo number_format($client['total_revenue'], 2); ?> ج.م
                                                            </strong>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-warning">
                                                                <?php echo number_format($client['active_sessions']); ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنبيهات الاشتراكات -->
                <?php if (!empty($expiring_subscriptions)): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيه: اشتراكات ستنتهي قريباً
                            </h5>
                            <p>يوجد <?php echo count($expiring_subscriptions); ?> عميل ستنتهي اشتراكاتهم خلال الـ 30 يوم القادمة:</p>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم المحل</th>
                                            <th>المالك</th>
                                            <th>تاريخ انتهاء الاشتراك</th>
                                            <th>الخطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($expiring_subscriptions as $client): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($client['business_name']); ?></td>
                                                <td><?php echo htmlspecialchars($client['owner_name']); ?></td>
                                                <td>
                                                    <span class="text-danger">
                                                        <?php echo date('Y-m-d', strtotime($client['subscription_end'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $client['subscription_plan']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="clients.php?edit=<?php echo $client['client_id']; ?>"
                                                       class="btn btn-sm btn-outline-primary">
                                                        تجديد الاشتراك
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة إضافية -->
                <div class="row mb-4">
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    إحصائيات خطط الاشتراك
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($subscription_stats as $plan): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo ucfirst($plan['subscription_plan']); ?></span>
                                        <div>
                                            <span class="badge bg-primary me-1"><?php echo $plan['count']; ?></span>
                                            <span class="badge bg-success"><?php echo $plan['active_count']; ?> نشط</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 6px;">
                                        <div class="progress-bar"
                                             style="width: <?php echo $stats['total_clients'] > 0 ? ($plan['count'] / $stats['total_clients']) * 100 : 0; ?>%">
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    أهم المدن
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach (array_slice($city_stats, 0, 5) as $city): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo htmlspecialchars($city['city']); ?></span>
                                        <span class="badge bg-info"><?php echo $city['count']; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>
                                    أدوات سريعة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="clients.php" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-users me-2"></i>
                                        إدارة العملاء
                                    </a>
                                    <a href="reports.php" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-chart-line me-2"></i>
                                        التقارير المالية
                                    </a>
                                    <a href="settings.php" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-cog me-2"></i>
                                        إعدادات النظام
                                    </a>
                                    <button class="btn btn-outline-info btn-sm" onclick="refreshData()">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        تحديث البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <script>
        // بيانات خطط الاشتراك للرسم البياني
        const subscriptionData = {
            labels: [
                <?php foreach ($subscription_stats as $plan): ?>
                    '<?php echo ucfirst($plan['subscription_plan']); ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                data: [
                    <?php foreach ($subscription_stats as $plan): ?>
                        <?php echo $plan['count']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: [
                    '#667eea',
                    '#764ba2',
                    '#f093fb',
                    '#f5576c',
                    '#4facfe'
                ],
                borderWidth: 0
            }]
        };

        // بيانات المدن للرسم البياني
        const cityData = {
            labels: [
                <?php foreach (array_slice($city_stats, 0, 5) as $city): ?>
                    '<?php echo htmlspecialchars($city['city']); ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'عدد العملاء',
                data: [
                    <?php foreach (array_slice($city_stats, 0, 5) as $city): ?>
                        <?php echo $city['count']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 2,
                borderRadius: 5
            }]
        };

        // إعداد الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني دائري لخطط الاشتراك
            const subscriptionCtx = document.getElementById('subscriptionChart');
            if (subscriptionCtx) {
                new Chart(subscriptionCtx, {
                    type: 'doughnut',
                    data: subscriptionData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            // رسم بياني عمودي للمدن
            const cityCtx = document.getElementById('cityChart');
            if (cityCtx) {
                new Chart(cityCtx, {
                    type: 'bar',
                    data: cityData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        });

        // دالة تحديث البيانات
        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
            button.disabled = true;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // تحديث الوقت كل دقيقة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            const timeElement = document.querySelector('.text-muted');
            if (timeElement && timeElement.innerHTML.includes('fas fa-calendar')) {
                timeElement.innerHTML = '<i class="fas fa-calendar me-1"></i>' + timeString;
            }
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);

        // تأثيرات بصرية للكروت
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // إضافة تأثير loading للروابط
        document.querySelectorAll('a[href]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#') && !this.target) {
                    const icon = this.querySelector('i');
                    if (icon && !icon.classList.contains('fa-external-link-alt')) {
                        icon.className = 'fas fa-spinner fa-spin me-2';
                    }
                }
            });
        });

        // إشعارات تفاعلية
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // عرض إشعار ترحيب
        setTimeout(() => {
            showNotification('مرحباً بك في لوحة التحكم! جميع البيانات محدثة.', 'success');
        }, 1000);
    </script>
</body>
</html>