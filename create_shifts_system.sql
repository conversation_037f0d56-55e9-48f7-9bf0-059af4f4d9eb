-- إنشاء نظام الورديات المتقدم - PlayGood
-- تشغيل هذا الملف لإضافة نظام الورديات الشامل

-- 1. إن<PERSON><PERSON>ء جدول قوالب الورديات
CREATE TABLE IF NOT EXISTS shift_templates (
    template_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_duration INT DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
    is_overnight BOOLEAN DEFAULT FALSE COMMENT 'وردية ليلية تمتد لليوم التالي',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- 2. إنشاء جدول الورديات
CREATE TABLE IF NOT EXISTS shifts (
    shift_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    template_id INT NULL,
    shift_name VARCHAR(100) NOT NULL,
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_duration INT DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
    is_overnight BOOLEAN DEFAULT FALSE,
    max_employees INT DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
    min_employees INT DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
    status ENUM('scheduled', 'active', 'completed', 'cancelled') DEFAULT 'scheduled',
    notes TEXT,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES shift_templates(template_id) ON DELETE SET NULL,
    INDEX idx_shifts_date (shift_date),
    INDEX idx_shifts_status (status),
    INDEX idx_shifts_client (client_id)
);

-- 3. إنشاء جدول تخصيص الموظفين للورديات
CREATE TABLE IF NOT EXISTS employee_shifts (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL,
    employee_id INT NOT NULL,
    role_in_shift ENUM('supervisor', 'regular', 'backup') DEFAULT 'regular',
    is_mandatory BOOLEAN DEFAULT FALSE COMMENT 'هل الحضور إجباري',
    assigned_by INT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('assigned', 'confirmed', 'declined', 'cancelled') DEFAULT 'assigned',
    notes TEXT,
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_shift (shift_id, employee_id)
);

-- 4. إنشاء جدول حضور الورديات
CREATE TABLE IF NOT EXISTS shift_attendance (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    shift_id INT NOT NULL,
    employee_id INT NOT NULL,
    check_in_time TIMESTAMP NULL,
    check_out_time TIMESTAMP NULL,
    break_start_time TIMESTAMP NULL,
    break_end_time TIMESTAMP NULL,
    actual_hours DECIMAL(4,2) DEFAULT 0.00,
    overtime_hours DECIMAL(4,2) DEFAULT 0.00,
    break_hours DECIMAL(4,2) DEFAULT 0.00,
    status ENUM('absent', 'present', 'late', 'early_leave', 'overtime') DEFAULT 'absent',
    late_minutes INT DEFAULT 0,
    early_leave_minutes INT DEFAULT 0,
    notes TEXT,
    recorded_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assignment_id) REFERENCES employee_shifts(assignment_id) ON DELETE CASCADE,
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_attendance_date (check_in_time),
    INDEX idx_attendance_employee (employee_id),
    INDEX idx_attendance_shift (shift_id)
);

-- 5. إنشاء جدول إعدادات الورديات
CREATE TABLE IF NOT EXISTS shift_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    setting_name VARCHAR(100) NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    UNIQUE KEY unique_client_setting (client_id, setting_name)
);

-- 6. إدراج الإعدادات الافتراضية للورديات
INSERT IGNORE INTO shift_settings (client_id, setting_name, setting_value, description) VALUES
(1, 'grace_period_minutes', '15', 'فترة السماح للتأخير بالدقائق'),
(1, 'overtime_threshold_minutes', '30', 'الحد الأدنى للعمل الإضافي بالدقائق'),
(1, 'auto_checkout_hours', '12', 'تسجيل خروج تلقائي بعد عدد ساعات'),
(1, 'break_duration_default', '30', 'مدة الاستراحة الافتراضية بالدقائق'),
(1, 'notification_before_shift', '60', 'إشعار قبل بداية الوردية بالدقائق'),
(1, 'allow_early_checkin', '30', 'السماح بتسجيل الدخول المبكر بالدقائق');

-- 7. إدراج قوالب الورديات الافتراضية
INSERT IGNORE INTO shift_templates (client_id, template_name, start_time, end_time, break_duration, description) VALUES
(1, 'الوردية الصباحية', '08:00:00', '16:00:00', 60, 'وردية صباحية من 8 صباحاً إلى 4 عصراً'),
(1, 'الوردية المسائية', '16:00:00', '00:00:00', 60, 'وردية مسائية من 4 عصراً إلى 12 منتصف الليل'),
(1, 'الوردية الليلية', '00:00:00', '08:00:00', 60, 'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً');

-- 8. إضافة صلاحيات الورديات إلى نظام الصلاحيات
INSERT IGNORE INTO permissions (permission_name, permission_label, permission_description, category) VALUES
('manage_shifts', 'إدارة الورديات', 'إنشاء وتعديل وحذف الورديات', 'shifts'),
('view_shifts', 'عرض الورديات', 'عرض جدول الورديات فقط', 'shifts'),
('manage_attendance', 'إدارة الحضور', 'تسجيل حضور وانصراف الموظفين', 'shifts'),
('view_attendance', 'عرض الحضور', 'عرض سجلات الحضور والغياب', 'shifts'),
('assign_shifts', 'تخصيص الورديات', 'تخصيص الموظفين للورديات', 'shifts'),
('view_shift_reports', 'تقارير الورديات', 'عرض تقارير الحضور والورديات', 'shifts');

-- 9. إضافة صفحات الورديات إلى نظام الصفحات
INSERT IGNORE INTO pages (page_name, page_label, page_url, page_icon, category) VALUES
('shifts', 'الورديات', 'shifts.php', 'fas fa-clock', 'shifts'),
('attendance', 'الحضور والانصراف', 'attendance.php', 'fas fa-user-check', 'shifts'),
('shift_reports', 'تقارير الورديات', 'shift_reports.php', 'fas fa-chart-line', 'shifts');

-- 10. إنشاء views مفيدة للورديات

-- عرض تفاصيل الورديات مع معلومات الموظفين
CREATE OR REPLACE VIEW shifts_detailed AS
SELECT
    s.shift_id,
    s.client_id,
    s.shift_name,
    s.shift_date,
    s.start_time,
    s.end_time,
    s.break_duration,
    s.is_overnight,
    s.max_employees,
    s.min_employees,
    s.status as shift_status,
    s.notes as shift_notes,
    st.template_name,
    COUNT(es.assignment_id) as assigned_employees,
    COUNT(CASE WHEN es.status = 'confirmed' THEN 1 END) as confirmed_employees,
    COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_employees,
    COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_employees
FROM shifts s
LEFT JOIN shift_templates st ON s.template_id = st.template_id
LEFT JOIN employee_shifts es ON s.shift_id = es.shift_id
LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
GROUP BY s.shift_id;

-- عرض حضور الموظفين مع تفاصيل الوردية
CREATE OR REPLACE VIEW attendance_detailed AS
SELECT
    sa.attendance_id,
    sa.shift_id,
    sa.employee_id,
    e.name as employee_name,
    e.role as employee_role,
    s.shift_name,
    s.shift_date,
    s.start_time as scheduled_start,
    s.end_time as scheduled_end,
    sa.check_in_time,
    sa.check_out_time,
    sa.break_start_time,
    sa.break_end_time,
    sa.actual_hours,
    sa.overtime_hours,
    sa.break_hours,
    sa.status as attendance_status,
    sa.late_minutes,
    sa.early_leave_minutes,
    sa.notes as attendance_notes,
    es.role_in_shift,
    es.is_mandatory
FROM shift_attendance sa
JOIN employees e ON sa.employee_id = e.id
JOIN shifts s ON sa.shift_id = s.shift_id
JOIN employee_shifts es ON sa.assignment_id = es.assignment_id;

-- عرض إحصائيات الحضور الشهرية
CREATE OR REPLACE VIEW monthly_attendance_stats AS
SELECT
    e.id as employee_id,
    e.name as employee_name,
    e.client_id,
    YEAR(s.shift_date) as year,
    MONTH(s.shift_date) as month,
    COUNT(sa.attendance_id) as total_shifts,
    COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_days,
    COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_days,
    COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late_days,
    SUM(sa.actual_hours) as total_hours,
    SUM(sa.overtime_hours) as total_overtime,
    AVG(sa.late_minutes) as avg_late_minutes,
    (COUNT(CASE WHEN sa.status = 'present' THEN 1 END) * 100.0 / COUNT(sa.attendance_id)) as attendance_percentage
FROM employees e
JOIN employee_shifts es ON e.id = es.employee_id
JOIN shifts s ON es.shift_id = s.shift_id
LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
GROUP BY e.id, YEAR(s.shift_date), MONTH(s.shift_date);

-- 11. إنشاء دوال مساعدة لحساب ساعات العمل (بدلاً من stored procedures)

-- إنشاء جدول مؤقت لحساب الساعات (يمكن استخدامه في PHP)
CREATE TABLE IF NOT EXISTS temp_attendance_calculations (
    attendance_id INT PRIMARY KEY,
    calculated_hours DECIMAL(4,2) DEFAULT 0.00,
    calculated_overtime DECIMAL(4,2) DEFAULT 0.00,
    calculated_break DECIMAL(4,2) DEFAULT 0.00,
    late_minutes INT DEFAULT 0,
    early_leave_minutes INT DEFAULT 0,
    calculated_status VARCHAR(20) DEFAULT 'absent',
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء trigger لحساب الساعات تلقائياً عند تحديث وقت الانصراف
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS calculate_attendance_hours
AFTER UPDATE ON shift_attendance
FOR EACH ROW
BEGIN
    DECLARE scheduled_start TIME;
    DECLARE scheduled_end TIME;
    DECLARE break_duration INT;
    DECLARE calculated_hours DECIMAL(4,2) DEFAULT 0.00;
    DECLARE calculated_overtime DECIMAL(4,2) DEFAULT 0.00;
    DECLARE calculated_break DECIMAL(4,2) DEFAULT 0.00;
    DECLARE late_mins INT DEFAULT 0;
    DECLARE early_mins INT DEFAULT 0;
    DECLARE attendance_stat VARCHAR(20) DEFAULT 'absent';

    -- جلب بيانات الوردية المجدولة
    SELECT s.start_time, s.end_time, s.break_duration
    INTO scheduled_start, scheduled_end, break_duration
    FROM shifts s
    WHERE s.shift_id = NEW.shift_id;

    -- حساب الساعات إذا كان هناك تسجيل دخول وخروج
    IF NEW.check_in_time IS NOT NULL AND NEW.check_out_time IS NOT NULL THEN
        -- حساب إجمالي الساعات
        SET calculated_hours = TIMESTAMPDIFF(MINUTE, NEW.check_in_time, NEW.check_out_time) / 60.0;

        -- طرح وقت الاستراحة
        IF NEW.break_start_time IS NOT NULL AND NEW.break_end_time IS NOT NULL THEN
            SET calculated_break = TIMESTAMPDIFF(MINUTE, NEW.break_start_time, NEW.break_end_time) / 60.0;
        ELSE
            SET calculated_break = break_duration / 60.0;
        END IF;

        SET calculated_hours = calculated_hours - calculated_break;

        -- حساب الساعات الإضافية (أكثر من 8 ساعات)
        IF calculated_hours > 8 THEN
            SET calculated_overtime = calculated_hours - 8;
        END IF;

        -- حساب التأخير
        SET late_mins = GREATEST(0, TIMESTAMPDIFF(MINUTE,
            TIMESTAMP(DATE(NEW.check_in_time), scheduled_start), NEW.check_in_time));

        -- حساب الخروج المبكر
        SET early_mins = GREATEST(0, TIMESTAMPDIFF(MINUTE,
            NEW.check_out_time, TIMESTAMP(DATE(NEW.check_out_time), scheduled_end)));

        -- تحديد حالة الحضور
        IF late_mins > 15 THEN
            SET attendance_stat = 'late';
        ELSEIF early_mins > 15 THEN
            SET attendance_stat = 'early_leave';
        ELSEIF calculated_overtime > 0.5 THEN
            SET attendance_stat = 'overtime';
        ELSE
            SET attendance_stat = 'present';
        END IF;

        -- تحديث السجل بالقيم المحسوبة
        UPDATE shift_attendance SET
            actual_hours = calculated_hours,
            overtime_hours = calculated_overtime,
            break_hours = calculated_break,
            late_minutes = late_mins,
            early_leave_minutes = early_mins,
            status = attendance_stat
        WHERE attendance_id = NEW.attendance_id;
    END IF;
END$$
DELIMITER ;

-- تم الانتهاء من إنشاء نظام الورديات
SELECT 'تم إنشاء نظام الورديات المتقدم بنجاح!' as message;
