-- إصلاح مشكلة حذف التصنيفات في صفحة الكافتيريا
-- PlayGood System - Categories Deletion Fix

-- 1. فحص القيود المرجعية الحالية
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND (TABLE_NAME = 'cafeteria_items' OR REFERENCED_TABLE_NAME = 'categories')
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 2. إزالة القيد المرجعي المشكل (إذا كان موجوداً)
-- تحقق من اسم القيد أولاً من الاستعلام أعلاه
SET @constraint_name = (
    SELECT CONSTRAINT_NAME 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'cafeteria_items' 
    AND REFERENCED_TABLE_NAME = 'categories'
    AND REFERENCED_COLUMN_NAME = 'category_id'
    LIMIT 1
);

-- إزالة القيد إذا كان موجوداً
SET @sql = IF(@constraint_name IS NOT NULL, 
    CONCAT('ALTER TABLE cafeteria_items DROP FOREIGN KEY ', @constraint_name), 
    'SELECT "No constraint to drop" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. التأكد من أن عمود category_id يقبل NULL
ALTER TABLE cafeteria_items MODIFY COLUMN category_id INT NULL;

-- 4. إضافة قيد مرجعي محسن (اختياري - فقط إذا كنت تريد استخدام معرفات التصنيفات)
-- هذا القيد يسمح بـ SET NULL عند حذف التصنيف
-- ALTER TABLE cafeteria_items 
-- ADD CONSTRAINT fk_cafeteria_category 
-- FOREIGN KEY (category_id) REFERENCES categories(category_id) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 5. فحص البيانات الحالية
SELECT 
    'cafeteria_items' as table_name,
    COUNT(*) as total_items,
    COUNT(category_id) as items_with_category_id,
    COUNT(category) as items_with_category_name
FROM cafeteria_items;

-- 6. عرض عينة من البيانات
SELECT 
    id,
    name,
    category as category_name,
    category_id,
    client_id
FROM cafeteria_items 
LIMIT 10;

-- 7. فحص التصنيفات
SELECT 
    category_id,
    name,
    client_id,
    created_at
FROM categories 
ORDER BY name;

-- 8. التحقق من وجود منتجات مرتبطة بكل تصنيف
SELECT 
    c.category_id,
    c.name as category_name,
    COUNT(ci.id) as products_count
FROM categories c
LEFT JOIN cafeteria_items ci ON c.name = ci.category AND c.client_id = ci.client_id
GROUP BY c.category_id, c.name
ORDER BY c.name;

-- ملاحظات مهمة:
-- 1. النظام الحالي يستخدم أسماء التصنيفات (category) وليس معرفات التصنيفات (category_id)
-- 2. هذا يعني أن حذف التصنيفات يجب أن يعمل بدون مشاكل
-- 3. إذا كانت هناك مشكلة، فهي على الأرجح في الكود وليس في قاعدة البيانات
-- 4. تأكد من أن الكود يتحقق من وجود منتجات مرتبطة بالتصنيف قبل الحذف

-- اختبار حذف تصنيف (تجريبي)
-- INSERT INTO categories (name, client_id) VALUES ('تصنيف تجريبي', 1);
-- SET @test_id = LAST_INSERT_ID();
-- DELETE FROM categories WHERE category_id = @test_id;
-- SELECT ROW_COUNT() as deleted_rows;
