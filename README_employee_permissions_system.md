# نظام إدارة صلاحيات الموظفين المتقدم - PlayGood

## نظرة عامة
تم تطوير نظام متقدم لإدارة صلاحيات الموظفين يتيح تحديد صلاحيات مخصصة لكل موظف والتحكم في الصفحات التي يمكنه الوصول إليها.

## المميزات الجديدة

### 1. **نظام الصلاحيات المرن**
- **صلاحيات الدور التقليدية**: النظام الأساسي المعتمد على الأدوار (مدير، كاشير، ويتر، عامل نظافة)
- **صلاحيات مخصصة**: إمكانية تحديد صلاحيات فردية لكل موظف

### 2. **إدارة الصفحات**
- تحديد الصفحات المسموح للموظف بالوصول إليها
- تجميع الصفحات حسب الفئات (الأجهزة، الجلسات، الكافتيريا، إلخ)

### 3. **واجهة سهلة الاستخدام**
- نافذة منبثقة لإدارة الصلاحيات
- تجميع الصلاحيات والصفحات حسب الفئات
- مفتاح تبديل لتفعيل/إلغاء الصلاحيات المخصصة

## الصلاحيات المتاحة

### إدارة الأجهزة
- `manage_devices`: إدارة الأجهزة (إضافة، تعديل، حذف)
- `view_devices`: عرض الأجهزة فقط

### إدارة الجلسات
- `manage_sessions`: إدارة الجلسات (بدء، إنهاء، تعديل)
- `view_sessions`: عرض الجلسات فقط

### إدارة الغرف
- `manage_rooms`: إدارة الغرف (إضافة، تعديل، حذف)
- `view_rooms`: عرض الغرف فقط

### إدارة العملاء
- `manage_customers`: إدارة العملاء (إضافة، تعديل، حذف)
- `view_customers`: عرض العملاء فقط

### إدارة الكافتيريا
- `manage_cafeteria`: إدارة منتجات وطلبات الكافتيريا
- `view_cafeteria`: عرض منتجات الكافتيريا فقط

### إدارة الموظفين
- `manage_employees`: إدارة الموظفين (إضافة، تعديل، حذف)
- `view_employees`: عرض الموظفين فقط

### التقارير والمالية
- `view_reports`: عرض التقارير والإحصائيات
- `view_finances`: عرض البيانات المالية
- `manage_finances`: إدارة المصروفات والإيرادات

### الفواتير
- `manage_invoices`: إنشاء وتعديل الفواتير
- `view_invoices`: عرض الفواتير فقط

### الإعدادات
- `manage_settings`: تعديل إعدادات النظام
- `view_profile`: عرض وتعديل الملف الشخصي

## الصفحات المتاحة

### الصفحات الأساسية
- لوحة التحكم (`dashboard.php`)
- الملف الشخصي (`profile.php`)

### إدارة الأجهزة والغرف
- إدارة الأجهزة (`devices.php`)
- إدارة الغرف (`rooms.php`)

### إدارة الجلسات والعملاء
- إدارة الجلسات (`sessions.php`)
- إدارة العملاء (`customers.php`)

### الكافتيريا والموظفين
- إدارة الكافتيريا (`cafeteria.php`)
- إدارة الموظفين (`employees.php`)

### التقارير والمالية
- التقارير (`reports.php`)
- المالية (`finances.php`)
- الفواتير (`invoices.php`)

### الإعدادات
- الإعدادات (`settings.php`)

## كيفية الاستخدام

### 1. إعداد النظام
```bash
# افتح المتصفح واذهب إلى:
http://localhost/playgood/setup_employee_permissions.php
```

### 2. إضافة موظف جديد
1. اذهب إلى صفحة إدارة الموظفين
2. املأ بيانات الموظف الأساسية
3. اختر الدور المناسب
4. اضغط "إضافة الموظف"

### 3. إدارة صلاحيات الموظف
1. في قائمة الموظفين، اضغط على زر الصلاحيات (🛡️)
2. فعّل "استخدام صلاحيات مخصصة" إذا كنت تريد صلاحيات فردية
3. حدد الصلاحيات المطلوبة من كل فئة
4. حدد الصفحات المسموح بالوصول إليها
5. اضغط "حفظ الصلاحيات"

### 4. أنواع الصلاحيات

#### صلاحيات الدور التقليدية
- يتم تحديدها تلقائياً حسب دور الموظف
- محددة مسبقاً في ملف `employee-auth.php`
- جميع الصفحات متاحة حسب الدور

#### الصلاحيات المخصصة
- يتم تحديدها يدوياً لكل موظف
- مرونة كاملة في اختيار الصلاحيات
- تحديد دقيق للصفحات المسموح بالوصول إليها

## الملفات المضافة/المعدلة

### ملفات قاعدة البيانات
- `create_employee_permissions_system.sql` - إنشاء جداول النظام
- `setup_employee_permissions.php` - أداة إعداد النظام

### ملفات النظام
- `client/employees.php` - صفحة إدارة الموظفين المحدثة
- `client/includes/employee-auth.php` - نظام التحقق من الصلاحيات المحدث

### الجداول الجديدة
- `permissions` - الصلاحيات المتاحة
- `pages` - الصفحات المتاحة
- `employee_permissions` - صلاحيات الموظفين
- `employee_pages` - صفحات الموظفين

## الدوال الجديدة

### في `employee-auth.php`
- `employeeHasCustomPermission($permission)` - التحقق من الصلاحيات المخصصة
- `employeeCanAccessPage($page_name)` - التحقق من الوصول للصفحة
- `getEmployeeCustomPermissions($employee_id)` - جلب صلاحيات الموظف
- `getEmployeeCustomPages($employee_id)` - جلب صفحات الموظف

## أمثلة الاستخدام

### في الكود
```php
// التحقق من الصلاحيات المخصصة
if (employeeHasCustomPermission('manage_devices')) {
    // السماح بإدارة الأجهزة
}

// التحقق من الوصول للصفحة
if (employeeCanAccessPage('devices')) {
    // السماح بالوصول لصفحة الأجهزة
}
```

### في الواجهة
```php
// عرض زر حسب الصلاحية
<?php if (employeeHasCustomPermission('manage_customers')): ?>
    <button>إضافة عميل</button>
<?php endif; ?>
```

## الأمان والحماية
- جميع الصلاحيات مرتبطة بمعرف العميل
- التحقق من الصلاحيات في كل عملية
- حماية من SQL Injection
- تسجيل الأخطاء في ملف السجل

## الدعم والصيانة
- النظام متوافق مع النظام الحالي
- يمكن التبديل بين النظامين بسهولة
- إمكانية إضافة صلاحيات وصفحات جديدة
- نظام مرن وقابل للتوسع
