<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات - فقط المديرين يمكنهم إدارة الموظفين
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_employees')) {
    header('Location: dashboard.php?error=no_permission');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الموظف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: employees.php?error=invalid_employee');
    exit;
}

$employee_id = (int)$_GET['id'];

// جلب بيانات الموظف
try {
    $employee_query = $pdo->prepare("
        SELECT * FROM employees 
        WHERE id = ? AND client_id = ?
    ");
    $employee_query->execute([$employee_id, $client_id]);
    $employee = $employee_query->fetch();
    
    if (!$employee) {
        header('Location: employees.php?error=employee_not_found');
        exit;
    }
} catch (PDOException $e) {
    header('Location: employees.php?error=database_error');
    exit;
}

// معالجة تحديث بيانات الموظف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_employee'])) {
    try {
        $stmt = $pdo->prepare("
            UPDATE employees SET 
                name = ?, 
                phone = ?, 
                role = ?, 
                salary = ?, 
                hire_date = ?
            WHERE id = ? AND client_id = ?
        ");
        
        $stmt->execute([
            $_POST['name'],
            $_POST['phone'],
            $_POST['role'],
            $_POST['salary'],
            $_POST['hire_date'],
            $employee_id,
            $client_id
        ]);
        
        $_SESSION['success'] = "تم تحديث بيانات الموظف بنجاح";
        header('Location: employees.php');
        exit;
        
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تحديث بيانات الموظف: " . $e->getMessage();
    }
}

$page_title = "تعديل بيانات الموظف (صفحة منفصلة) - " . $employee['name'];
$active_page = "employees";

require_once 'includes/header.php';
?>

<style>
.employee-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.employee-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

.employee-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 20px;
}

.form-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
}

.section-body {
    padding: 25px;
}

.save-section {
    position: sticky;
    bottom: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
    padding: 20px;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .employee-header {
        text-align: center;
    }
    
    .employee-avatar {
        margin: 0 auto 15px;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- مؤشر الصفحة المنفصلة -->
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-external-link-alt fa-2x me-3"></i>
            <div>
                <h6 class="alert-heading mb-1">
                    <i class="fas fa-star me-1"></i>صفحة تعديل بيانات الموظف المنفصلة
                </h6>
                <p class="mb-0">
                    تم تطوير هذه الصفحة لتكون منفصلة بدلاً من النوافذ المنبثقة لتوفير مساحة أكبر وتجربة أفضل في تعديل البيانات
                </p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- رأس الصفحة مع معلومات الموظف -->
    <div class="employee-header">
        <div class="d-flex align-items-center">
            <div class="employee-avatar">
                <i class="fas fa-user-edit"></i>
            </div>
            <div class="flex-grow-1">
                <h2 class="mb-2">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات: <?php echo htmlspecialchars($employee['name']); ?>
                </h2>
                <p class="mb-2 opacity-75">
                    <i class="fas fa-info-circle me-1"></i>
                    صفحة منفصلة لتعديل جميع بيانات الموظف بشكل مفصل ومنظم
                </p>
                <div class="row">
                    <div class="col-md-4">
                        <small class="opacity-75">الوظيفة الحالية:</small><br>
                        <span class="badge bg-light text-dark fs-6">
                            <?php echo match($employee['role']) {
                                'manager' => 'مدير',
                                'cashier' => 'كاشير',
                                'waiter' => 'ويتر',
                                'cleaner' => 'عامل نظافة',
                                default => $employee['role']
                            }; ?>
                        </span>
                    </div>
                    <div class="col-md-4">
                        <small class="opacity-75">تاريخ التعيين:</small><br>
                        <span><?php echo date('Y-m-d', strtotime($employee['hire_date'])); ?></span>
                    </div>
                    <div class="col-md-4">
                        <small class="opacity-75">الراتب الحالي:</small><br>
                        <span><?php echo number_format($employee['salary'], 2); ?> ج.م</span>
                    </div>
                </div>
            </div>
            <div>
                <a href="employees.php" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
                </a>
                <div class="mt-2">
                    <small class="text-light opacity-75">
                        <i class="fas fa-external-link-alt me-1"></i>
                        صفحة منفصلة - لا حاجة لنوافذ منبثقة
                    </small>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="editEmployeeForm">
        <input type="hidden" name="employee_id" value="<?php echo $employee_id; ?>">
        
        <!-- قسم البيانات الأساسية -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>البيانات الأساسية
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user me-1"></i>اسم الموظف
                            </label>
                            <input type="text" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($employee['name']); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-phone me-1"></i>رقم الهاتف
                            </label>
                            <input type="tel" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($employee['phone']); ?>" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الوظيفة والراتب -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>بيانات الوظيفة
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user-tag me-1"></i>الوظيفة
                            </label>
                            <select name="role" class="form-select" required>
                                <option value="manager" <?php echo $employee['role'] == 'manager' ? 'selected' : ''; ?>>مدير</option>
                                <option value="cashier" <?php echo $employee['role'] == 'cashier' ? 'selected' : ''; ?>>كاشير</option>
                                <option value="waiter" <?php echo $employee['role'] == 'waiter' ? 'selected' : ''; ?>>ويتر</option>
                                <option value="cleaner" <?php echo $employee['role'] == 'cleaner' ? 'selected' : ''; ?>>عامل نظافة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-money-bill-wave me-1"></i>الراتب
                            </label>
                            <input type="number" name="salary" class="form-control"
                                   value="<?php echo $employee['salary']; ?>" required step="0.01">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-1"></i>تاريخ التعيين
                            </label>
                            <input type="date" name="hire_date" class="form-control"
                                   value="<?php echo $employee['hire_date']; ?>" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم بيانات تسجيل الدخول -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>بيانات تسجيل الدخول
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user-circle me-1"></i>اسم المستخدم الحالي
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control bg-light"
                                       value="<?php echo htmlspecialchars($employee['username']); ?>" readonly>
                                <a href="change_username.php?id=<?php echo $employee_id; ?>"
                                   class="btn btn-outline-primary" title="تغيير اسم المستخدم">
                                    <i class="fas fa-edit me-1"></i>تغيير
                                </a>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                لتغيير اسم المستخدم، اضغط على زر "تغيير" للانتقال لصفحة منفصلة
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-clock me-1"></i>آخر تسجيل دخول
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-history"></i>
                                </span>
                                <input type="text" class="form-control bg-light"
                                       value="<?php echo $employee['last_login'] ? date('Y-m-d H:i', strtotime($employee['last_login'])) : 'لم يسجل دخول بعد'; ?>" readonly>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تاريخ ووقت آخر تسجيل دخول للموظف
                            </small>
                        </div>
                    </div>
                </div>

                <!-- تنبيه حول تغيير اسم المستخدم -->
                <div class="alert alert-info" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-lightbulb fa-2x me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">
                                <i class="fas fa-star me-1"></i>تغيير اسم المستخدم - صفحة منفصلة
                            </h6>
                            <p class="mb-0">
                                لتغيير اسم المستخدم للموظف، يتم استخدام صفحة منفصلة مخصصة لهذا الغرض مع التحقق من عدم التكرار وإجراءات الأمان المطلوبة.
                                <br>
                                <strong>ملاحظة:</strong> سيحتاج الموظف لتسجيل الدخول مرة أخرى بعد تغيير اسم المستخدم.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الحفظ -->
        <div class="save-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات - صفحة منفصلة
                    </h6>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        سيتم تطبيق التغييرات فوراً بعد الحفظ
                        <br>
                        <i class="fas fa-check-circle me-1"></i>
                        <strong>مزايا الصفحة المنفصلة:</strong> مساحة أكبر، تنظيم أفضل، وسهولة في الاستخدام
                    </small>
                </div>
                <div>
                    <a href="employees.php" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" name="edit_employee" class="btn btn-success btn-lg">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// إخفاء الرسائل تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
