<?php
require_once '../config/database.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول وصلاحية الوصول لهذه الصفحة
checkAdminSession();

// معالجة الفلاتر
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$client_filter = $_GET['client_id'] ?? '';
$report_type = $_GET['report_type'] ?? 'overview';

try {
    // إحصائيات عامة
    $overview_query = $pdo->prepare("
        SELECT
            COUNT(DISTINCT c.client_id) as total_clients,
            COUNT(DISTINCT CASE WHEN c.is_active = 1 THEN c.client_id END) as active_clients,
            COUNT(DISTINCT d.device_id) as total_devices,
            COUNT(DISTINCT s.session_id) as total_sessions,
            COUNT(DISTINCT CASE WHEN s.status = 'completed' THEN s.session_id END) as completed_sessions,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as avg_session_cost
        FROM clients c
        LEFT JOIN devices d ON c.client_id = d.client_id
        LEFT JOIN sessions s ON d.device_id = s.device_id
            AND DATE(s.start_time) BETWEEN ? AND ?
        " . ($client_filter ? "WHERE c.client_id = ?" : "")
    );

    $params = [$start_date, $end_date];
    if ($client_filter) {
        $params[] = $client_filter;
    }

    $overview_query->execute($params);
    $overview = $overview_query->fetch();

    // تقرير العملاء التفصيلي
    $clients_query = $pdo->prepare("
        SELECT
            c.client_id,
            c.business_name,
            c.owner_name,
            c.email,
            c.phone,
            c.city,
            c.subscription_plan,
            c.is_active,
            COUNT(DISTINCT d.device_id) as devices_count,
            COUNT(DISTINCT s.session_id) as sessions_count,
            COUNT(DISTINCT CASE WHEN s.status = 'completed' THEN s.session_id END) as completed_sessions,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as avg_revenue,
            MAX(s.start_time) as last_session_date
        FROM clients c
        LEFT JOIN devices d ON c.client_id = d.client_id
        LEFT JOIN sessions s ON d.device_id = s.device_id
            AND DATE(s.start_time) BETWEEN ? AND ?
        " . ($client_filter ? "WHERE c.client_id = ?" : "") . "
        GROUP BY c.client_id
        ORDER BY total_revenue DESC
    ");

    $clients_query->execute($params);
    $clients_report = $clients_query->fetchAll();

    // إحصائيات الأجهزة
    $devices_query = $pdo->prepare("
        SELECT
            d.device_type,
            COUNT(d.device_id) as devices_count,
            COUNT(DISTINCT s.session_id) as sessions_count,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as avg_revenue
        FROM devices d
        LEFT JOIN sessions s ON d.device_id = s.device_id
            AND DATE(s.start_time) BETWEEN ? AND ?
        " . ($client_filter ? "WHERE d.client_id = ?" : "") . "
        GROUP BY d.device_type
        ORDER BY total_revenue DESC
    ");

    $devices_query->execute($params);
    $devices_report = $devices_query->fetchAll();

    // الإيرادات الشهرية
    $monthly_query = $pdo->prepare("
        SELECT
            DATE_FORMAT(s.start_time, '%Y-%m') as month,
            COUNT(DISTINCT s.session_id) as sessions_count,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as revenue
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE DATE(s.start_time) BETWEEN DATE_SUB(?, INTERVAL 11 MONTH) AND ?
        " . ($client_filter ? "AND d.client_id = ?" : "") . "
        GROUP BY DATE_FORMAT(s.start_time, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ");

    $monthly_params = [$end_date, $end_date];
    if ($client_filter) {
        $monthly_params[] = $client_filter;
    }

    $monthly_query->execute($monthly_params);
    $monthly_report = $monthly_query->fetchAll();

    // قائمة العملاء للفلتر
    $clients_list_query = $pdo->query("SELECT client_id, business_name FROM clients ORDER BY business_name");
    $clients_list = $clients_list_query->fetchAll();

} catch (Exception $e) {
    $error = "حدث خطأ في جلب البيانات: " . $e->getMessage();
    $overview = [];
    $clients_report = [];
    $devices_report = [];
    $monthly_report = [];
    $clients_list = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .stats-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .stats-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stats-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .filter-box {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 1.5rem;
        }
        .nav-link {
            border-radius: 10px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
        }
        @media print {
            .no-print { display: none !important; }
            .card { box-shadow: none !important; border: 1px solid #ddd !important; }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <div class="no-print">
        <?php include 'includes/sidebar.php'; ?>
    </div>

    <div class="container-fluid mt-4">
        <!-- المحتوى الرئيسي -->
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                    <h1 class="h2">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h1>
                    <div>
                        <button class="btn btn-success" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>
                            تصدير التقرير
                        </button>
                        <button class="btn btn-info" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>

                <!-- فلاتر التقرير -->
                <div class="filter-box no-print">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">العميل</label>
                            <select class="form-select" name="client_id">
                                <option value="">جميع العملاء</option>
                                <?php foreach ($clients_list as $client): ?>
                                    <option value="<?php echo $client['client_id']; ?>"
                                            <?php echo $client_filter == $client['client_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($client['business_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> تطبيق الفلاتر
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3><?php echo number_format($overview['total_clients'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي العملاء</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-play fa-2x mb-2"></i>
                                <h3><?php echo number_format($overview['total_sessions'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي الجلسات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-info">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h3><?php echo number_format($overview['total_revenue'] ?? 0, 2); ?> ج.م</h3>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3><?php echo number_format($overview['avg_session_cost'] ?? 0, 2); ?> ج.م</h3>
                                <p class="mb-0">متوسط تكلفة الجلسة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير العملاء التفصيلي -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    تقرير العملاء التفصيلي
                                    <small class="text-muted">(من <?php echo $start_date; ?> إلى <?php echo $end_date; ?>)</small>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم المحل</th>
                                                <th>المالك</th>
                                                <th>المدينة</th>
                                                <th>الخطة</th>
                                                <th>عدد الأجهزة</th>
                                                <th>عدد الجلسات</th>
                                                <th>الجلسات المكتملة</th>
                                                <th>إجمالي الإيرادات</th>
                                                <th>متوسط الإيرادات</th>
                                                <th>آخر جلسة</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($clients_report)): ?>
                                                <tr>
                                                    <td colspan="11" class="text-center text-muted py-4">
                                                        لا توجد بيانات للفترة المحددة
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($clients_report as $client): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($client['business_name']); ?></strong>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($client['owner_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($client['city'] ?? 'غير محدد'); ?></td>
                                                        <td>
                                                            <?php
                                                            $plan_badges = [
                                                                'basic' => 'bg-secondary',
                                                                'premium' => 'bg-warning',
                                                                'enterprise' => 'bg-success'
                                                            ];
                                                            $plan_names = [
                                                                'basic' => 'أساسية',
                                                                'premium' => 'مميزة',
                                                                'enterprise' => 'مؤسسية'
                                                            ];
                                                            ?>
                                                            <span class="badge <?php echo $plan_badges[$client['subscription_plan']]; ?>">
                                                                <?php echo $plan_names[$client['subscription_plan']]; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-info">
                                                                <?php echo number_format($client['devices_count']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary">
                                                                <?php echo number_format($client['sessions_count']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-success">
                                                                <?php echo number_format($client['completed_sessions']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <strong class="text-success">
                                                                <?php echo number_format($client['total_revenue'], 2); ?> ج.م
                                                            </strong>
                                                        </td>
                                                        <td>
                                                            <?php echo number_format($client['avg_revenue'], 2); ?> ج.م
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?php echo $client['last_session_date'] ? date('Y-m-d', strtotime($client['last_session_date'])) : 'لا توجد'; ?>
                                                            </small>
                                                        </td>
                                                        <td>
                                                            <span class="badge <?php echo $client['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo $client['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير إضافية -->
                <div class="row mb-4">
                    <!-- تقرير الأجهزة -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-gamepad me-2"></i>
                                    إحصائيات الأجهزة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>نوع الجهاز</th>
                                                <th>عدد الأجهزة</th>
                                                <th>عدد الجلسات</th>
                                                <th>الإيرادات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($devices_report as $device): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fas fa-gamepad me-1"></i>
                                                        <?php echo htmlspecialchars($device['device_type']); ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            <?php echo number_format($device['devices_count']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?php echo number_format($device['sessions_count']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success">
                                                            <?php echo number_format($device['total_revenue'], 2); ?> ج.م
                                                        </strong>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإيرادات الشهرية -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    الإيرادات الشهرية (آخر 12 شهر)
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الشهر</th>
                                                <th>عدد الجلسات</th>
                                                <th>الإيرادات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($monthly_report as $month): ?>
                                                <tr>
                                                    <td>
                                                        <?php
                                                        $date = DateTime::createFromFormat('Y-m', $month['month']);
                                                        echo $date ? $date->format('Y/m') : $month['month'];
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?php echo number_format($month['sessions_count']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success">
                                                            <?php echo number_format($month['revenue'], 2); ?> ج.م
                                                        </strong>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص التقرير -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>
                                    ملخص التقرير
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>معلومات التقرير:</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>الفترة:</strong> من <?php echo $start_date; ?> إلى <?php echo $end_date; ?></li>
                                            <li><strong>العميل:</strong>
                                                <?php
                                                if ($client_filter) {
                                                    $selected_client = array_filter($clients_list, function($c) use ($client_filter) {
                                                        return $c['client_id'] == $client_filter;
                                                    });
                                                    echo $selected_client ? reset($selected_client)['business_name'] : 'غير محدد';
                                                } else {
                                                    echo 'جميع العملاء';
                                                }
                                                ?>
                                            </li>
                                            <li><strong>تاريخ التقرير:</strong> <?php echo date('Y-m-d H:i'); ?></li>
                                            <li><strong>المُعد بواسطة:</strong> <?php echo htmlspecialchars($_SESSION['admin_name']); ?></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>الإحصائيات الرئيسية:</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>إجمالي العملاء:</strong> <?php echo number_format($overview['total_clients'] ?? 0); ?></li>
                                            <li><strong>العملاء النشطين:</strong> <?php echo number_format($overview['active_clients'] ?? 0); ?></li>
                                            <li><strong>إجمالي الجلسات:</strong> <?php echo number_format($overview['total_sessions'] ?? 0); ?></li>
                                            <li><strong>الجلسات المكتملة:</strong> <?php echo number_format($overview['completed_sessions'] ?? 0); ?></li>
                                            <li><strong>إجمالي الإيرادات:</strong> <?php echo number_format($overview['total_revenue'] ?? 0, 2); ?> ج.م</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // دالة طباعة التقرير
        function printReport() {
            window.print();
        }

        // دالة تصدير التقرير
        function exportReport() {
            // تحويل الجدول إلى CSV
            const tables = document.querySelectorAll('table');
            let csvContent = "data:text/csv;charset=utf-8,\uFEFF";

            tables.forEach((table, index) => {
                const rows = table.querySelectorAll('tr');
                const tableData = [];

                rows.forEach(row => {
                    const cols = row.querySelectorAll('th, td');
                    const rowData = [];
                    cols.forEach(col => {
                        // إزالة HTML tags والحصول على النص فقط
                        let text = col.textContent || col.innerText || '';
                        text = text.replace(/"/g, '""'); // escape quotes
                        rowData.push('"' + text + '"');
                    });
                    tableData.push(rowData.join(','));
                });

                if (index > 0) csvContent += "\n\n";
                csvContent += tableData.join('\n');
            });

            // إنشاء رابط التحميل
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "تقرير_العملاء_" + new Date().toISOString().split('T')[0] + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // تحديث تلقائي للبيانات كل 5 دقائق
        setInterval(function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (!urlParams.get('auto_refresh')) {
                urlParams.set('auto_refresh', '1');
                // window.location.search = urlParams.toString();
            }
        }, 300000); // 5 minutes

        // إضافة تأثيرات بصرية
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // تحسين عرض الجداول على الشاشات الصغيرة
        function adjustTableDisplay() {
            const tables = document.querySelectorAll('.table-responsive');
            tables.forEach(table => {
                if (window.innerWidth < 768) {
                    table.style.fontSize = '0.8rem';
                } else {
                    table.style.fontSize = '1rem';
                }
            });
        }

        window.addEventListener('resize', adjustTableDisplay);
        adjustTableDisplay();

        // إضافة loading state للأزرار
        document.querySelectorAll('button[type="submit"]').forEach(button => {
            button.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });
    </script>
</body>
</html>