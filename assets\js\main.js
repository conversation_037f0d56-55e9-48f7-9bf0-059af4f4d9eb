// Main JavaScript file for PlayGood system

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-refresh dashboard every 5 minutes
    if (window.location.pathname.includes('dashboard.php')) {
        setInterval(function() {
            refreshDashboardStats();
        }, 300000); // 5 minutes
    }

    // Initialize form validation
    initializeFormValidation();
    
    // Initialize session management
    initializeSessionManagement();
}

// Dashboard functions
function refreshDashboardStats() {
    // Show loading indicator
    showLoadingIndicator();
    
    // Reload the page to get fresh data
    window.location.reload();
}

function showLoadingIndicator() {
    const refreshBtn = document.getElementById('refreshStats');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
        refreshBtn.disabled = true;
    }
}

// Session management functions
function startSession(deviceId) {
    if (confirm('هل تريد بدء جلسة جديدة على هذا الجهاز؟')) {
        showLoadingIndicator();
        window.location.href = 'sessions.php?action=start&device_id=' + deviceId;
    }
}

function endSession(sessionId) {
    if (confirm('هل تريد إنهاء هذه الجلسة؟')) {
        showLoadingIndicator();
        window.location.href = 'sessions.php?action=end&session_id=' + sessionId;
    }
}

function pauseSession(sessionId) {
    if (confirm('هل تريد إيقاف هذه الجلسة مؤقتاً؟')) {
        showLoadingIndicator();
        window.location.href = 'sessions.php?action=pause&session_id=' + sessionId;
    }
}

function resumeSession(sessionId) {
    if (confirm('هل تريد استئناف هذه الجلسة؟')) {
        showLoadingIndicator();
        window.location.href = 'sessions.php?action=resume&session_id=' + sessionId;
    }
}

// Form validation
function initializeFormValidation() {
    // Add custom validation styles
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

function formatTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
        return hours + ' ساعة ' + mins + ' دقيقة';
    } else {
        return mins + ' دقيقة';
    }
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            alertDiv.remove();
        }, 5000);
    }
}

// Device management functions
function changeDeviceStatus(deviceId, status) {
    const statusText = {
        'available': 'متاح',
        'occupied': 'مشغول',
        'maintenance': 'صيانة'
    };
    
    if (confirm(`هل تريد تغيير حالة الجهاز إلى "${statusText[status]}"؟`)) {
        showLoadingIndicator();
        
        fetch('api/device-status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                device_id: deviceId,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم تحديث حالة الجهاز بنجاح', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showAlert('حدث خطأ في تحديث حالة الجهاز', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

// Customer management functions
function searchCustomers(query) {
    if (query.length < 2) {
        document.getElementById('customerResults').innerHTML = '';
        return;
    }
    
    fetch(`api/search-customers.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displayCustomerResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function displayCustomerResults(customers) {
    const resultsDiv = document.getElementById('customerResults');
    if (!resultsDiv) return;
    
    if (customers.length === 0) {
        resultsDiv.innerHTML = '<div class="text-muted">لا توجد نتائج</div>';
        return;
    }
    
    let html = '<div class="list-group">';
    customers.forEach(customer => {
        html += `
            <a href="#" class="list-group-item list-group-item-action" 
               onclick="selectCustomer(${customer.id}, '${customer.name}', '${customer.phone}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${customer.name}</h6>
                    <small>${customer.phone}</small>
                </div>
            </a>
        `;
    });
    html += '</div>';
    
    resultsDiv.innerHTML = html;
}

function selectCustomer(id, name, phone) {
    document.getElementById('customer_id').value = id;
    document.getElementById('customer_name').value = name;
    document.getElementById('customer_phone').value = phone;
    document.getElementById('customerResults').innerHTML = '';
}

// Print functions
function printInvoice(sessionId) {
    window.open(`invoice.php?session_id=${sessionId}`, '_blank');
}

function printReport() {
    window.print();
}

// Session management initialization
function initializeSessionManagement() {
    // Update session timers every minute
    setInterval(updateSessionTimers, 60000);
    
    // Initialize session timers on page load
    updateSessionTimers();
}

function updateSessionTimers() {
    const sessionRows = document.querySelectorAll('[data-session-start]');
    
    sessionRows.forEach(row => {
        const startTime = new Date(row.dataset.sessionStart);
        const now = new Date();
        const diffMinutes = Math.floor((now - startTime) / (1000 * 60));
        
        const durationCell = row.querySelector('.session-duration');
        if (durationCell) {
            durationCell.textContent = formatTime(diffMinutes);
        }
        
        const costCell = row.querySelector('.session-cost');
        const hourlyRate = parseFloat(row.dataset.hourlyRate || 0);
        if (costCell && hourlyRate > 0) {
            const currentCost = (diffMinutes / 60) * hourlyRate;
            costCell.textContent = formatCurrency(currentCost);
        }
    });
}

// Export functions for global access
window.startSession = startSession;
window.endSession = endSession;
window.pauseSession = pauseSession;
window.resumeSession = resumeSession;
window.changeDeviceStatus = changeDeviceStatus;
window.searchCustomers = searchCustomers;
window.selectCustomer = selectCustomer;
window.printInvoice = printInvoice;
window.printReport = printReport;
window.refreshDashboardStats = refreshDashboardStats;
