<?php
/**
 * اختبار نهائي لنظام صلاحيات العملاء
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نظام الصلاحيات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 1000px; margin: 2rem auto; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h1 class='h3 mb-0'><i class='fas fa-check-circle me-2'></i>اختبار نهائي لنظام الصلاحيات</h1>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<h2>1. حالة النظام</h2>";
    
    // التحقق من الجداول
    $tables_status = [];
    $required_tables = ['client_pages', 'client_page_permissions'];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        $tables_status[$table] = $exists;
        
        if ($exists) {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<p class='success'>✅ $table: موجود ($count سجل)</p>";
        } else {
            echo "<p class='error'>❌ $table: غير موجود</p>";
        }
    }
    
    echo "<h2>2. اختبار الدوال</h2>";
    
    // محاكاة جلسة عميل
    session_start();
    $test_client = $pdo->query("SELECT client_id FROM clients WHERE is_active = 1 LIMIT 1")->fetch();
    
    if ($test_client) {
        $_SESSION['client_id'] = $test_client['client_id'];
        
        // تضمين ملف الصلاحيات
        require_once __DIR__ . '/client/includes/auth.php';
        
        echo "<p class='info'>🧪 اختبار العميل رقم: {$test_client['client_id']}</p>";
        
        // اختبار الصفحات المختلفة
        $test_pages = [
            'dashboard' => 'لوحة التحكم',
            'sessions' => 'الجلسات',
            'devices' => 'الأجهزة',
            'customers' => 'العملاء',
            'employees' => 'الموظفين',
            'cafeteria' => 'الكافتيريا',
            'orders' => 'الأوردرات',
            'reports' => 'التقارير',
            'settings' => 'الإعدادات'
        ];
        
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>الصفحة</th><th>الحالة</th><th>النتيجة</th></tr></thead>";
        echo "<tbody>";
        
        $allowed_count = 0;
        $total_count = count($test_pages);
        
        foreach ($test_pages as $page => $label) {
            $has_permission = hasPagePermission($page);
            $status_class = $has_permission ? 'success' : 'danger';
            $status_text = $has_permission ? 'مسموح' : 'غير مسموح';
            $icon = $has_permission ? '✅' : '❌';
            
            if ($has_permission) {
                $allowed_count++;
            }
            
            echo "<tr>";
            echo "<td>$label ($page)</td>";
            echo "<td><span class='badge bg-$status_class'>$status_text</span></td>";
            echo "<td>$icon</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        
        echo "<div class='alert alert-info'>";
        echo "<h4>📊 ملخص النتائج:</h4>";
        echo "<p><strong>الصفحات المسموحة:</strong> $allowed_count من $total_count</p>";
        echo "<p><strong>نسبة النجاح:</strong> " . round(($allowed_count / $total_count) * 100, 1) . "%</p>";
        echo "</div>";
        
        // اختبار دالة getAllowedPages
        echo "<h3>الصفحات المسموحة (من دالة getAllowedPages):</h3>";
        $allowed_pages = getAllowedPages();
        
        if (!empty($allowed_pages)) {
            echo "<div class='row'>";
            foreach ($allowed_pages as $page) {
                echo "<div class='col-md-4 mb-2'>";
                echo "<div class='card border-success'>";
                echo "<div class='card-body p-2'>";
                echo "<small><i class='{$page['page_icon']} me-1'></i>{$page['page_label']}</small>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
            echo "</div>";
        } else {
            echo "<p class='warning'>⚠️ لا توجد صفحات مسموحة</p>";
        }
        
    } else {
        echo "<p class='error'>❌ لا يوجد عملاء في النظام للاختبار</p>";
    }
    
    echo "<h2>3. إحصائيات النظام</h2>";
    
    // إحصائيات الصفحات
    $pages_stats = $pdo->query("
        SELECT 
            COUNT(*) as total_pages,
            SUM(is_default) as default_pages,
            SUM(is_active) as active_pages
        FROM client_pages
    ")->fetch();
    
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$pages_stats['total_pages']}</h3>";
    echo "<p>إجمالي الصفحات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$pages_stats['default_pages']}</h3>";
    echo "<p>الصفحات الافتراضية</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$pages_stats['active_pages']}</h3>";
    echo "<p>الصفحات النشطة</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // إحصائيات الصلاحيات
    $permissions_stats = $pdo->query("
        SELECT 
            COUNT(*) as total_permissions,
            COUNT(DISTINCT client_id) as clients_with_permissions,
            SUM(is_enabled) as enabled_permissions
        FROM client_page_permissions
    ")->fetch();
    
    echo "<div class='row mt-3'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-warning text-dark'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$permissions_stats['total_permissions']}</h3>";
    echo "<p>إجمالي الصلاحيات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-secondary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$permissions_stats['clients_with_permissions']}</h3>";
    echo "<p>العملاء لديهم صلاحيات</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>{$permissions_stats['enabled_permissions']}</h3>";
    echo "<p>الصلاحيات المفعلة</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<h2>4. التوصيات</h2>";
    
    $recommendations = [];
    
    if ($pages_stats['total_pages'] == 0) {
        $recommendations[] = "قم بتشغيل ملف الإصلاح لإضافة الصفحات الافتراضية";
    }
    
    if ($permissions_stats['total_permissions'] == 0) {
        $recommendations[] = "قم بمنح الصلاحيات الافتراضية للعملاء";
    }
    
    if ($permissions_stats['clients_with_permissions'] == 0) {
        $recommendations[] = "تأكد من وجود عملاء في النظام ومنحهم الصلاحيات";
    }
    
    if (empty($recommendations)) {
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 النظام يعمل بشكل صحيح!</h4>";
        echo "<p>جميع المكونات تعمل كما هو متوقع.</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ توصيات للتحسين:</h4>";
        echo "<ul>";
        foreach ($recommendations as $rec) {
            echo "<li>$rec</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='mt-4'>";
    echo "<a href='client/dashboard.php' class='btn btn-primary me-2'>لوحة تحكم العميل</a>";
    echo "<a href='admin/client_permissions.php' class='btn btn-success me-2'>إدارة الصلاحيات</a>";
    echo "<a href='fix_client_permissions.php' class='btn btn-warning'>إصلاح النظام</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء الاختبار</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
