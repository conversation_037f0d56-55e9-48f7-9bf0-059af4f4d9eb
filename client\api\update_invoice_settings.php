<?php
/**
 * API لتحديث إعدادات الفاتورة
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل معلومات التشخيص
error_log("Invoice settings API called");
error_log("Session data: " . print_r($_SESSION, true));
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    error_log("Access denied - no client_id or employee_id in session");
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول - يرجى تسجيل الدخول']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة غير مسموحة']);
    exit;
}

try {
    require_once '../../config/database.php';
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    // تحديد معرف العميل
    if (isset($_SESSION['employee_id'])) {
        $client_id = $_SESSION['client_id'];
    } else {
        $client_id = $_SESSION['client_id'];
    }

    error_log("Using client_id: " . $client_id);
    
    // التحقق من صحة البيانات
    $header_color = $input['header_color'] ?? '#dc3545';
    $footer_color = $input['footer_color'] ?? '#000000';
    $footer_text = $input['footer_text'] ?? 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى';
    $company_address = $input['company_address'] ?? '';
    $company_phone = $input['company_phone'] ?? '';
    $show_qr_code = isset($input['show_qr_code']) ? (bool)$input['show_qr_code'] : true;
    $billing_method = $input['billing_method'] ?? 'actual_time';
    
    // التحقق من صحة الألوان (hex colors)
    if (!preg_match('/^#[a-f0-9]{6}$/i', $header_color)) {
        throw new Exception('لون رأس الفاتورة غير صحيح');
    }
    
    if (!preg_match('/^#[a-f0-9]{6}$/i', $footer_color)) {
        throw new Exception('لون نص التذييل غير صحيح');
    }
    
    // التحقق من طول النص
    if (strlen($footer_text) > 500) {
        throw new Exception('نص التذييل طويل جداً (الحد الأقصى 500 حرف)');
    }

    // التحقق من صحة طريقة الحساب
    if (!in_array($billing_method, ['actual_time', 'hourly_rounding', 'first_minute_full_hour'])) {
        throw new Exception('طريقة الحساب غير صحيحة');
    }
    
    // التحقق من وجود إعدادات سابقة
    $check_stmt = $pdo->prepare("SELECT id FROM invoice_settings WHERE client_id = ?");
    $check_stmt->execute([$client_id]);
    $existing = $check_stmt->fetch();
    
    if ($existing) {
        // تحديث الإعدادات الموجودة
        $update_stmt = $pdo->prepare("
            UPDATE invoice_settings
            SET header_color = ?,
                footer_color = ?,
                footer_text = ?,
                company_address = ?,
                company_phone = ?,
                show_qr_code = ?,
                billing_method = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE client_id = ?
        ");

        $update_stmt->execute([
            $header_color,
            $footer_color,
            $footer_text,
            $company_address,
            $company_phone,
            $show_qr_code ? 1 : 0,
            $billing_method,
            $client_id
        ]);
        
        $message = 'تم تحديث إعدادات الفاتورة بنجاح';
        
    } else {
        // إنشاء إعدادات جديدة
        $insert_stmt = $pdo->prepare("
            INSERT INTO invoice_settings (
                client_id,
                header_color,
                footer_color,
                footer_text,
                company_address,
                company_phone,
                show_qr_code,
                billing_method
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $insert_stmt->execute([
            $client_id,
            $header_color,
            $footer_color,
            $footer_text,
            $company_address,
            $company_phone,
            $show_qr_code ? 1 : 0,
            $billing_method
        ]);
        
        $message = 'تم حفظ إعدادات الفاتورة بنجاح';
    }
    
    // تسجيل العملية في سجل النشاط (إذا كان موجود)
    try {
        $log_stmt = $pdo->prepare("
            INSERT INTO activity_log (
                user_id, 
                action, 
                description, 
                ip_address, 
                user_agent
            ) VALUES (?, 'update_invoice_settings', ?, ?, ?)
        ");
        
        $log_stmt->execute([
            $_SESSION['employee_id'] ?? $_SESSION['client_id'],
            'تحديث إعدادات الفاتورة',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // تجاهل أخطاء السجل
    }
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'settings' => [
            'header_color' => $header_color,
            'footer_color' => $footer_color,
            'footer_text' => $footer_text,
            'company_address' => $company_address,
            'company_phone' => $company_phone,
            'show_qr_code' => $show_qr_code,
            'billing_method' => $billing_method
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Invoice settings update error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'message' => $e->getMessage()
        ]
    ]);

} catch (Exception $e) {
    error_log("General error in invoice settings: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
