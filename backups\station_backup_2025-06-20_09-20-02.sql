-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: station
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `additional_income`
--

DROP TABLE IF EXISTS `additional_income`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_additional_income_client` (`client_id`),
  KEY `idx_additional_income_type` (`income_type_id`),
  KEY `idx_additional_income_date` (`income_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `additional_income`
--

LOCK TABLES `additional_income` WRITE;
/*!40000 ALTER TABLE `additional_income` DISABLE KEYS */;
INSERT INTO `additional_income` VALUES (1,1,15,100.00,'','2025-06-13','',1,'2025-06-12 21:04:08','2025-06-12 21:04:08'),(2,1,15,100.00,'','2025-06-13','',1,'2025-06-12 21:05:55','2025-06-12 21:05:55');
/*!40000 ALTER TABLE `additional_income` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_settings`
--

DROP TABLE IF EXISTS `admin_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_settings`
--

LOCK TABLES `admin_settings` WRITE;
/*!40000 ALTER TABLE `admin_settings` DISABLE KEYS */;
INSERT INTO `admin_settings` VALUES (1,'backup_enabled','1','تفعيل صلاحية النسخ الاحتياطي للعملاء','2025-06-16 10:51:34','2025-06-16 11:10:36'),(2,'system_maintenance','0','وضع الصيانة للنظام','2025-06-16 10:51:34','2025-06-16 10:51:34'),(3,'max_backup_files','10','الحد الأقصى لعدد ملفات النسخ الاحتياطي المحفوظة','2025-06-16 10:51:34','2025-06-16 10:51:34'),(61,'timezone','Asia/Riyadh',NULL,'2025-06-17 19:23:07','2025-06-17 19:23:07'),(62,'language','ar',NULL,'2025-06-17 19:23:07','2025-06-17 19:23:07'),(63,'currency','EGP',NULL,'2025-06-17 19:23:07','2025-06-17 19:23:07'),(64,'date_format','Y-m-d',NULL,'2025-06-17 19:23:07','2025-06-17 19:23:07');
/*!40000 ALTER TABLE `admin_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (1,'admin','<EMAIL>','$2a$12$vFAjvx8m93Bb76ogrJq4yuu1Pw38jFvhaRibQBQ0cmZ5IleS3jm8G','مدير النظام','super_admin','2025-06-08 02:28:08','2025-06-18 18:20:51',1);
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `attendance_detailed`
--

DROP TABLE IF EXISTS `attendance_detailed`;
/*!50001 DROP VIEW IF EXISTS `attendance_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `attendance_detailed` AS SELECT
 1 AS `attendance_id`,
  1 AS `shift_id`,
  1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `shift_name`,
  1 AS `shift_date`,
  1 AS `scheduled_start`,
  1 AS `scheduled_end`,
  1 AS `check_in_time`,
  1 AS `check_out_time`,
  1 AS `break_start_time`,
  1 AS `break_end_time`,
  1 AS `actual_hours`,
  1 AS `overtime_hours`,
  1 AS `break_hours`,
  1 AS `attendance_status`,
  1 AS `late_minutes`,
  1 AS `early_leave_minutes`,
  1 AS `attendance_notes`,
  1 AS `role_in_shift`,
  1 AS `is_mandatory` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `business_settings`
--

DROP TABLE IF EXISTS `business_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_key`),
  CONSTRAINT `business_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_settings`
--

LOCK TABLES `business_settings` WRITE;
/*!40000 ALTER TABLE `business_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cafeteria_items`
--

DROP TABLE IF EXISTS `cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `stock_quantity` int(11) DEFAULT 0 COMMENT 'الكمية المتوفرة',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `min_stock_level` int(11) DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
  `max_stock_level` int(11) DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
  `status` enum('available','low_stock','out_of_stock','discontinued') DEFAULT 'available' COMMENT 'حالة المنتج',
  `barcode` varchar(100) DEFAULT NULL COMMENT 'الباركود',
  `supplier` varchar(200) DEFAULT NULL COMMENT 'المورد',
  `last_restock_date` timestamp NULL DEFAULT NULL COMMENT 'تاريخ آخر تجديد للمخزن',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'تاريخ آخر تحديث',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_product_per_client` (`name`,`category`,`client_id`),
  KEY `category_id` (`category_id`),
  KEY `idx_cafeteria_search` (`name`,`category`,`client_id`),
  KEY `idx_stock_status` (`status`,`stock_quantity`),
  KEY `idx_low_stock` (`stock_quantity`,`min_stock_level`),
  KEY `idx_barcode` (`barcode`),
  KEY `idx_supplier` (`supplier`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cafeteria_items`
--

LOCK TABLES `cafeteria_items` WRITE;
/*!40000 ALTER TABLE `cafeteria_items` DISABLE KEYS */;
INSERT INTO `cafeteria_items` VALUES (13,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-09 04:38:25',NULL,1,50,3.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(14,'شاي بحليب',8.00,'مشروبات ساخنة','شاي بحليب','2025-06-09 04:38:42',NULL,1,50,4.80,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(15,'قهوة',10.00,'مشروبات ساخنة','','2025-06-09 05:06:50',NULL,1,50,6.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(16,'نسكافية',15.00,'مشروبات ساخنة','','2025-06-09 05:07:01',NULL,1,50,9.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(17,'قهوة تركي',25.00,'مشروبات ساخنة','','2025-06-09 05:07:13',NULL,1,50,15.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(18,'قهوة فرنساوي',35.00,'مشروبات ساخنة','','2025-06-09 05:07:23',NULL,1,50,21.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(19,'بيبسي',12.00,'مشروبات ساقعة','','2025-06-09 05:07:35',NULL,1,50,8.00,5,100,'available','','',NULL,'2025-06-16 12:51:19'),(20,'سبيرو سباتس ابيض',15.00,'مشروبات ساقعة','','2025-06-09 05:07:48',NULL,1,50,9.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(21,'سبيرو سباتس اسود',15.00,'مشروبات ساقعة','','2025-06-09 05:07:59',NULL,1,50,9.00,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(24,'كافى مكس',343.00,'مشروبات76','','2025-06-13 06:53:10',NULL,2,50,205.80,5,100,'available',NULL,NULL,NULL,'2025-06-16 12:48:19'),(25,'شاي فتله',10.00,'مشروبات ساخنة','','2025-06-14 10:42:20',NULL,1,50,6.00,5,100,'available','','',NULL,'2025-06-16 13:58:02');
/*!40000 ALTER TABLE `cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER IF NOT EXISTS log_product_usage_insert

AFTER INSERT ON cafeteria_items

FOR EACH ROW

BEGIN

    DECLARE v_current_count INT;

    SELECT COUNT(*) INTO v_current_count FROM cafeteria_items WHERE client_id = NEW.client_id;

    

    INSERT INTO plan_usage_log (client_id, feature_type, feature_name, action, new_count)

    VALUES (NEW.client_id, 'products', 'max_products', 'add', v_current_count);

END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
INSERT INTO `categories` VALUES (7,'مشروبات ساقعة',1),(8,'مشروبات ساخنة',1),(9,'مأكولات',1),(10,'مقبلات',1),(12,'تسالي',1),(25,'مشروبات76',2),(26,'مشروبات76',2),(27,'245',2),(28,'10',4);
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `client_limits_usage`
--

DROP TABLE IF EXISTS `client_limits_usage`;
/*!50001 DROP VIEW IF EXISTS `client_limits_usage`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `client_limits_usage` AS SELECT
 1 AS `client_id`,
  1 AS `business_name`,
  1 AS `subscription_plan`,
  1 AS `feature_type`,
  1 AS `feature_name`,
  1 AS `feature_limit`,
  1 AS `current_usage`,
  1 AS `usage_status` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `client_page_permissions`
--

DROP TABLE IF EXISTS `client_page_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_page_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client_page` (`client_id`,`page_id`),
  KEY `page_id` (`page_id`),
  CONSTRAINT `client_page_permissions_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `client_page_permissions_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `client_pages` (`page_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_page_permissions`
--

LOCK TABLES `client_page_permissions` WRITE;
/*!40000 ALTER TABLE `client_page_permissions` DISABLE KEYS */;
INSERT INTO `client_page_permissions` VALUES (1,2,1,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(2,3,1,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(3,1,1,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(4,4,1,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(5,2,2,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(6,3,2,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(7,1,2,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(8,4,2,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(9,2,3,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(10,3,3,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(11,1,3,0,1,'2025-06-16 14:08:40','2025-06-18 18:54:16'),(12,4,3,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(13,2,5,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(14,3,5,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(15,1,5,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(16,4,5,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(17,2,6,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(18,3,6,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(19,1,6,0,1,'2025-06-16 14:08:40','2025-06-18 18:54:15'),(20,4,6,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(21,2,12,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(22,3,12,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(23,1,12,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(24,4,12,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(25,2,14,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(26,3,14,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(27,1,14,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(28,4,14,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(29,2,17,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(30,3,17,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(31,1,17,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(32,4,17,1,NULL,'2025-06-16 14:08:40','2025-06-16 14:08:40'),(64,1,7,1,1,'2025-06-16 14:10:06','2025-06-18 18:54:12'),(65,1,4,1,1,'2025-06-16 14:10:10','2025-06-16 14:10:10'),(66,1,9,1,1,'2025-06-16 14:10:11','2025-06-16 14:10:11'),(67,1,11,1,1,'2025-06-16 14:10:12','2025-06-16 14:10:12'),(68,1,10,1,1,'2025-06-16 14:10:13','2025-06-16 14:10:13'),(69,1,13,1,1,'2025-06-16 14:10:16','2025-06-16 14:10:16'),(70,1,15,1,1,'2025-06-16 14:10:17','2025-06-16 14:10:17'),(71,1,8,1,1,'2025-06-16 14:10:21','2025-06-16 14:10:21'),(72,1,16,1,1,'2025-06-16 14:10:23','2025-06-16 14:10:23'),(73,1,18,1,1,'2025-06-16 14:10:27','2025-06-16 14:10:27'),(74,2,8,1,1,'2025-06-16 14:24:09','2025-06-16 14:24:09'),(75,2,15,1,1,'2025-06-16 14:24:12','2025-06-16 14:24:12'),(76,2,13,1,1,'2025-06-16 14:24:14','2025-06-16 14:24:14'),(77,2,9,1,1,'2025-06-16 14:24:19','2025-06-16 14:24:19'),(78,2,4,0,1,'2025-06-17 19:45:15','2025-06-17 19:45:15'),(79,4,7,1,1,'2025-06-17 19:47:02','2025-06-18 18:21:15');
/*!40000 ALTER TABLE `client_page_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `client_page_permissions_detailed`
--

DROP TABLE IF EXISTS `client_page_permissions_detailed`;
/*!50001 DROP VIEW IF EXISTS `client_page_permissions_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `client_page_permissions_detailed` AS SELECT
 1 AS `client_id`,
  1 AS `business_name`,
  1 AS `owner_name`,
  1 AS `client_active`,
  1 AS `page_id`,
  1 AS `page_name`,
  1 AS `page_label`,
  1 AS `page_url`,
  1 AS `page_icon`,
  1 AS `category`,
  1 AS `description`,
  1 AS `is_default`,
  1 AS `has_permission`,
  1 AS `granted_at`,
  1 AS `updated_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `client_pages`
--

DROP TABLE IF EXISTS `client_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `page_name` (`page_name`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_pages`
--

LOCK TABLES `client_pages` WRITE;
/*!40000 ALTER TABLE `client_pages` DISABLE KEYS */;
INSERT INTO `client_pages` VALUES (1,'dashboard','لوحة التحكم','dashboard.php','fas fa-tachometer-alt','main','الصفحة الرئيسية وإحصائيات المحل',1,1,'2025-06-16 14:08:40'),(2,'profile','الملف الشخصي','profile.php','fas fa-user','main','إدارة بيانات الحساب الشخصي',1,1,'2025-06-16 14:08:40'),(3,'devices','إدارة الأجهزة','devices.php','fas fa-gamepad','devices','إضافة وإدارة أجهزة الألعاب',1,1,'2025-06-16 14:08:40'),(4,'rooms','إدارة الغرف','rooms.php','fas fa-door-open','devices','تنظيم الأجهزة في غرف',1,0,'2025-06-16 14:08:40'),(5,'sessions','إدارة الجلسات','sessions.php','fas fa-play-circle','sessions','بدء وإنهاء جلسات اللعب',1,1,'2025-06-16 14:08:40'),(6,'customers','إدارة العملاء','customers.php','fas fa-users','customers','إدارة بيانات عملاء المحل',1,1,'2025-06-16 14:08:40'),(7,'cafeteria','إدارة الكافتيريا','cafeteria.php','fas fa-coffee','cafeteria','إدارة منتجات الكافتيريا',1,0,'2025-06-16 14:08:40'),(8,'orders','إدارة الأوردرات','orders.php','fas fa-shopping-cart','orders','إدارة الطلبات المستقلة',1,0,'2025-06-16 14:08:40'),(9,'employees','إدارة الموظفين','employees.php','fas fa-user-tie','employees','إدارة موظفي المحل',1,0,'2025-06-16 14:08:40'),(10,'attendance','نظام الحضور','attendance.php','fas fa-clock','employees','تسجيل حضور وانصراف الموظفين',1,0,'2025-06-16 14:08:40'),(11,'shifts','إدارة الورديات','shifts.php','fas fa-calendar-alt','employees','تنظيم ورديات العمل',1,0,'2025-06-16 14:08:40'),(12,'reports','التقارير','reports.php','fas fa-chart-bar','reports','تقارير مالية وإحصائية',1,1,'2025-06-16 14:08:40'),(13,'finances','المالية','finances.php','fas fa-money-bill-wave','finances','إدارة المصروفات والإيرادات',1,0,'2025-06-16 14:08:40'),(14,'invoices','الفواتير','invoices.php','fas fa-file-invoice','invoices','إدارة وطباعة الفواتير',1,1,'2025-06-16 14:08:40'),(15,'inventory','إدارة المخزون','inventory.php','fas fa-boxes','inventory','إدارة مخزون المنتجات',1,0,'2025-06-16 14:08:40'),(16,'reservations','الحجوزات','reservations.php','fas fa-calendar-check','reservations','إدارة حجوزات العملاء',1,0,'2025-06-16 14:08:40'),(17,'settings','الإعدادات','settings.php','fas fa-cog','settings','إعدادات النظام والمحل',1,1,'2025-06-16 14:08:40'),(18,'invoice_settings','إعدادات الفواتير','invoice_settings.php','fas fa-file-alt','settings','تخصيص شكل ومحتوى الفواتير',1,0,'2025-06-16 14:08:40');
/*!40000 ALTER TABLE `client_pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `client_plan_details`
--

DROP TABLE IF EXISTS `client_plan_details`;
/*!50001 DROP VIEW IF EXISTS `client_plan_details`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `client_plan_details` AS SELECT
 1 AS `client_id`,
  1 AS `business_name`,
  1 AS `owner_name`,
  1 AS `subscription_plan`,
  1 AS `plan_name_ar`,
  1 AS `plan_description`,
  1 AS `plan_price`,
  1 AS `plan_duration_days`,
  1 AS `subscription_start`,
  1 AS `subscription_end`,
  1 AS `days_remaining`,
  1 AS `subscription_status` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `client_plan_limits`
--

DROP TABLE IF EXISTS `client_plan_limits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_plan_limits` (
  `limit_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `custom_limit` int(11) DEFAULT -1 COMMENT 'حد مخصص يتجاوز حد الخطة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الحد المخصص',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الحد المخصص',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`limit_id`),
  UNIQUE KEY `unique_client_feature` (`client_id`,`feature_type`,`feature_name`),
  CONSTRAINT `client_plan_limits_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_plan_limits`
--

LOCK TABLES `client_plan_limits` WRITE;
/*!40000 ALTER TABLE `client_plan_limits` DISABLE KEYS */;
/*!40000 ALTER TABLE `client_plan_limits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `client_plan_summary`
--

DROP TABLE IF EXISTS `client_plan_summary`;
/*!50001 DROP VIEW IF EXISTS `client_plan_summary`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `client_plan_summary` AS SELECT
 1 AS `client_id`,
  1 AS `business_name`,
  1 AS `subscription_plan`,
  1 AS `plan_name_ar`,
  1 AS `plan_price`,
  1 AS `subscription_end`,
  1 AS `status_ar` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `client_theme_settings`
--

DROP TABLE IF EXISTS `client_theme_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `primary_color` varchar(7) DEFAULT '#0d6efd',
  `secondary_color` varchar(7) DEFAULT '#6c757d',
  `accent_color` varchar(7) DEFAULT '#20c997',
  `header_style` enum('top','sidebar') DEFAULT 'top',
  `sidebar_position` enum('right','left') DEFAULT 'right',
  `theme_mode` enum('light','dark','auto') DEFAULT 'light',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client` (`client_id`),
  CONSTRAINT `client_theme_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_theme_settings`
--

LOCK TABLES `client_theme_settings` WRITE;
/*!40000 ALTER TABLE `client_theme_settings` DISABLE KEYS */;
INSERT INTO `client_theme_settings` VALUES (1,1,'#0d6efd','#000000','#214363','top','right','light','2025-06-16 15:24:58','2025-06-17 16:48:08');
/*!40000 ALTER TABLE `client_theme_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `name` varchar(255) NOT NULL DEFAULT 'مركز الألعاب',
  `password` varchar(255) DEFAULT NULL,
  `backup_enabled` tinyint(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي للعميل',
  PRIMARY KEY (`client_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'playgood','احمد','<EMAIL>','***********','$2y$10$txuFt9nn1DVao4bRaz2KoOQYi6tZAPh0nlymJpTLsB3XfN5v9b6J.','الجيزة','الجيزة','basic','2025-06-08','2025-07-08',1,'2025-06-08 02:35:48','2025-06-16 11:12:50','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL,1),(2,'ريدوكس','محمد','<EMAIL>','01125454417','$2y$10$nbG/.9myzQn1ux2Hhuv2mOzFHiV/w1IT9p2tRadhGPsCi4dOtBH8.','7567\r\n33','7657','basic','2025-06-09','2025-07-09',0,'2025-06-09 03:31:49','2025-06-14 11:59:40','gaming_center','','من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL,1),(3,'egypto','تامر','<EMAIL>','***********','$2y$10$fmrMFKeY2BJjVHfutQpFT.3HTM1PjOZ1uWsn.PSy6kIBBQj6hZAnm','cairo','cairo','basic','2025-06-09','2025-07-09',1,'2025-06-09 05:52:31','2025-06-09 05:52:31','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL,1),(4,'PlayStation','tarek','<EMAIL>','2***********','$2y$10$kyPCoB5hoMRIhR66ms/JQ.ZrU1vsOfP1Ud4ZsMWaEiP0Dzj/oKF7.','مصر - الجيزة','الجيزة','basic','2025-06-12','2025-07-12',1,'2025-06-12 00:06:24','2025-06-16 11:12:20','gaming_center','','من 9 صباحاً إلى 12 منتصف الليل','online store',NULL,1);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`customer_id`),
  UNIQUE KEY `idx_phone_client` (`phone`,`client_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_customers_client_id` (`client_id`),
  KEY `idx_customers_phone` (`phone`),
  KEY `idx_customers_client` (`client_id`),
  CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (43,1,'عمر سيد','***********',NULL,NULL,'2025-06-16 02:14:13',NULL,NULL,NULL),(44,1,'عميل تجريبي 1','01000000001',NULL,NULL,'2025-06-16 03:53:57',NULL,NULL,NULL),(45,1,'عميل تجريبي 2','01000000002',NULL,NULL,'2025-06-16 03:53:57',NULL,NULL,NULL),(46,1,'عميل تجريبي 3','01000000003',NULL,NULL,'2025-06-16 03:53:57',NULL,NULL,NULL);
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL,
  `single_rate` decimal(10,2) DEFAULT NULL,
  `multi_rate` decimal(10,2) DEFAULT NULL,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `room_id` (`room_id`),
  KEY `idx_devices_client_status` (`client_id`,`status`),
  CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `devices_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES (16,1,'جهاز 1','PS5',15.00,15.00,25.00,'available',NULL,'2025-06-12 16:20:51','2025-06-16 04:31:45'),(17,2,'434','PS4',6.00,6.00,6.00,'occupied',NULL,'2025-06-12 18:17:12','2025-06-12 22:54:37'),(18,1,'جهاز 2','PS4',10.00,10.00,20.00,'available',NULL,'2025-06-12 20:10:16','2025-06-16 04:46:55'),(19,1,'جهاز 3','PS4',10.00,10.00,15.00,'available',NULL,'2025-06-13 08:28:54','2025-06-16 02:15:40'),(22,1,'جهاز 4','PS4',15.00,15.00,20.00,'available',NULL,'2025-06-16 11:36:52','2025-06-16 11:36:52'),(23,1,'جهاز 5','Xbox',25.00,25.00,30.00,'available',NULL,'2025-06-16 11:54:42','2025-06-16 11:57:23');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER IF NOT EXISTS log_device_usage_insert

AFTER INSERT ON devices

FOR EACH ROW

BEGIN

    DECLARE v_current_count INT;

    SELECT COUNT(*) INTO v_current_count FROM devices WHERE client_id = NEW.client_id;

    

    INSERT INTO plan_usage_log (client_id, feature_type, feature_name, action, new_count)

    VALUES (NEW.client_id, 'devices', 'max_devices', 'add', v_current_count);

END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `employee_pages`
--

DROP TABLE IF EXISTS `employee_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_page` (`employee_id`,`page_id`),
  KEY `idx_employee_pages_employee` (`employee_id`),
  KEY `idx_employee_pages_page` (`page_id`),
  CONSTRAINT `employee_pages_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_pages_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `pages` (`page_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_pages`
--

LOCK TABLES `employee_pages` WRITE;
/*!40000 ALTER TABLE `employee_pages` DISABLE KEYS */;
INSERT INTO `employee_pages` VALUES (12,3,2,NULL,'2025-06-14 12:22:59'),(13,3,1,NULL,'2025-06-14 12:22:59'),(14,3,5,NULL,'2025-06-14 12:22:59'),(15,3,14,NULL,'2025-06-14 12:22:59'),(16,3,13,NULL,'2025-06-14 12:22:59');
/*!40000 ALTER TABLE `employee_pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `employee_pages_detailed`
--

DROP TABLE IF EXISTS `employee_pages_detailed`;
/*!50001 DROP VIEW IF EXISTS `employee_pages_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `employee_pages_detailed` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `custom_permissions`,
  1 AS `page_id`,
  1 AS `page_name`,
  1 AS `page_label`,
  1 AS `page_url`,
  1 AS `page_icon`,
  1 AS `page_category`,
  1 AS `granted_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `employee_permissions`
--

DROP TABLE IF EXISTS `employee_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_permission` (`employee_id`,`permission_id`),
  KEY `idx_employee_permissions_employee` (`employee_id`),
  KEY `idx_employee_permissions_permission` (`permission_id`),
  CONSTRAINT `employee_permissions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_permissions`
--

LOCK TABLES `employee_permissions` WRITE;
/*!40000 ALTER TABLE `employee_permissions` DISABLE KEYS */;
INSERT INTO `employee_permissions` VALUES (13,3,19,NULL,'2025-06-14 12:22:59'),(14,3,3,NULL,'2025-06-14 12:22:59'),(15,3,4,NULL,'2025-06-14 12:22:59'),(16,3,24,NULL,'2025-06-14 12:22:59'),(17,4,9,NULL,'2025-06-17 17:38:48'),(18,4,10,NULL,'2025-06-17 17:38:48'),(19,4,7,NULL,'2025-06-17 17:38:48'),(20,4,8,NULL,'2025-06-17 17:38:48'),(21,4,2,NULL,'2025-06-17 17:38:48'),(22,4,17,NULL,'2025-06-17 17:38:48'),(23,4,28,NULL,'2025-06-17 17:38:48'),(24,4,3,NULL,'2025-06-17 17:38:48'),(25,4,23,NULL,'2025-06-17 17:38:48'),(26,4,21,NULL,'2025-06-17 17:38:48');
/*!40000 ALTER TABLE `employee_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `employee_permissions_detailed`
--

DROP TABLE IF EXISTS `employee_permissions_detailed`;
/*!50001 DROP VIEW IF EXISTS `employee_permissions_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `employee_permissions_detailed` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `custom_permissions`,
  1 AS `permission_id`,
  1 AS `permission_name`,
  1 AS `permission_label`,
  1 AS `permission_category`,
  1 AS `granted_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `employee_shifts`
--

DROP TABLE IF EXISTS `employee_shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_shifts` (
  `assignment_id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `role_in_shift` enum('supervisor','regular','backup') DEFAULT 'regular',
  `is_mandatory` tinyint(1) DEFAULT 0 COMMENT 'هل الحضور إجباري',
  `assigned_by` int(11) DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('assigned','confirmed','declined','cancelled') DEFAULT 'assigned',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`assignment_id`),
  UNIQUE KEY `unique_employee_shift` (`shift_id`,`employee_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `employee_shifts_ibfk_1` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `employee_shifts_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_shifts`
--

LOCK TABLES `employee_shifts` WRITE;
/*!40000 ALTER TABLE `employee_shifts` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees`
--

DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner') NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `hire_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'هل يستخدم صلاحيات مخصصة أم صلاحيات الدور',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `fk_employee_client` (`client_id`),
  CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees`
--

LOCK TABLES `employees` WRITE;
/*!40000 ALTER TABLE `employees` DISABLE KEYS */;
INSERT INTO `employees` VALUES (3,1,'sayed','01026362111','cashier',5000.00,'2025-06-01','2025-06-14 11:18:27','2025-06-14 12:34:11','sayed871','$2y$10$Dw6dvOyB/5jL.4MQikQjpe1pezD2IRDQq.h0JzNzIIVF.XBNdbmjO','2025-06-14 12:34:11',1,'<EMAIL>','شارع فريد - الأحساء',1),(4,1,'omer','***********','cashier',5000.00,'2025-06-17','2025-06-17 17:19:46','2025-06-17 17:38:48','omer897','$2y$10$aaH4w539R88Xpgwssw4KR.5OxZCnPHvY94BzNk.zpAPUSrA4mrHOW','2025-06-17 17:33:07',1,NULL,NULL,1);
/*!40000 ALTER TABLE `employees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expense_types`
--

DROP TABLE IF EXISTS `expense_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expense_types_client` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expense_types`
--

LOCK TABLES `expense_types` WRITE;
/*!40000 ALTER TABLE `expense_types` DISABLE KEYS */;
INSERT INTO `expense_types` VALUES (1,2,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(2,2,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(3,2,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(4,2,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(5,2,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(6,2,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(7,2,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(8,2,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(9,3,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(10,3,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(11,3,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(12,3,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(13,3,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(14,3,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(15,3,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(16,3,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(17,1,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(18,1,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(19,1,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(20,1,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(21,1,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(22,1,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(23,1,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(24,1,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(25,4,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(26,4,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(27,4,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(28,4,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(29,4,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(30,4,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(31,4,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(32,4,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 05:28:46','2025-06-12 05:28:46');
/*!40000 ALTER TABLE `expense_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expenses`
--

DROP TABLE IF EXISTS `expenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expenses_client` (`client_id`),
  KEY `idx_expenses_type` (`expense_type_id`),
  KEY `idx_expenses_date` (`expense_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expenses`
--

LOCK TABLES `expenses` WRITE;
/*!40000 ALTER TABLE `expenses` DISABLE KEYS */;
INSERT INTO `expenses` VALUES (2,1,19,1000.00,'','2025-06-13','',1,'2025-06-12 20:46:55','2025-06-12 20:46:55');
/*!40000 ALTER TABLE `expenses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_types`
--

DROP TABLE IF EXISTS `income_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_income_types_client` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_types`
--

LOCK TABLES `income_types` WRITE;
/*!40000 ALTER TABLE `income_types` DISABLE KEYS */;
INSERT INTO `income_types` VALUES (1,2,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(2,2,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(3,2,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(4,2,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(5,2,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(6,3,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(7,3,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(8,3,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(9,3,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(10,3,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(11,1,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(12,1,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(13,1,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(14,1,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(15,1,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(16,4,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(17,4,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(18,4,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(19,4,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 05:28:46','2025-06-12 05:28:46'),(20,4,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 05:28:46','2025-06-12 05:28:46');
/*!40000 ALTER TABLE `income_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_movements`
--

DROP TABLE IF EXISTS `inventory_movements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','expired','damaged') NOT NULL COMMENT 'نوع الحركة',
  `quantity` int(11) NOT NULL COMMENT 'الكمية',
  `previous_quantity` int(11) NOT NULL COMMENT 'الكمية السابقة',
  `new_quantity` int(11) NOT NULL COMMENT 'الكمية الجديدة',
  `unit_cost` decimal(10,2) DEFAULT 0.00 COMMENT 'تكلفة الوحدة',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT 'التكلفة الإجمالية',
  `reference_type` enum('purchase','sale','session','order','manual','system') DEFAULT 'manual' COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `client_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL COMMENT 'المستخدم الذي أجرى العملية',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_inventory_movements_product` (`product_id`),
  KEY `idx_inventory_movements_client` (`client_id`),
  KEY `idx_inventory_movements_date` (`created_at`),
  CONSTRAINT `inventory_movements_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_movements`
--

LOCK TABLES `inventory_movements` WRITE;
/*!40000 ALTER TABLE `inventory_movements` DISABLE KEYS */;
INSERT INTO `inventory_movements` VALUES (1,13,'in',50,0,50,3.00,150.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(2,14,'in',50,0,50,4.80,240.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(3,15,'in',50,0,50,6.00,300.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(4,16,'in',50,0,50,9.00,450.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(5,17,'in',50,0,50,15.00,750.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(6,18,'in',50,0,50,21.00,1050.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(7,19,'in',50,0,50,7.20,360.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(8,20,'in',50,0,50,9.00,450.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(9,21,'in',50,0,50,9.00,450.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19'),(11,24,'in',50,0,50,205.80,10290.00,'manual',NULL,'تحديث تلقائي للمخزن',2,NULL,'2025-06-16 12:48:19'),(12,25,'in',50,0,50,6.00,300.00,'manual',NULL,'تحديث تلقائي للمخزن',1,NULL,'2025-06-16 12:48:19');
/*!40000 ALTER TABLE `inventory_movements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `inventory_overview`
--

DROP TABLE IF EXISTS `inventory_overview`;
/*!50001 DROP VIEW IF EXISTS `inventory_overview`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `inventory_overview` AS SELECT
 1 AS `id`,
  1 AS `name`,
  1 AS `selling_price`,
  1 AS `cost_price`,
  1 AS `stock_quantity`,
  1 AS `min_stock_level`,
  1 AS `max_stock_level`,
  1 AS `status`,
  1 AS `barcode`,
  1 AS `supplier`,
  1 AS `category`,
  1 AS `description`,
  1 AS `last_restock_date`,
  1 AS `created_at`,
  1 AS `updated_at`,
  1 AS `client_id`,
  1 AS `category_name`,
  1 AS `category_icon`,
  1 AS `category_color`,
  1 AS `stock_status_text`,
  1 AS `stock_status_class`,
  1 AS `profit_margin`,
  1 AS `profit_percentage` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `invoice_settings`
--

DROP TABLE IF EXISTS `invoice_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_settings`
--

LOCK TABLES `invoice_settings` WRITE;
/*!40000 ALTER TABLE `invoice_settings` DISABLE KEYS */;
INSERT INTO `invoice_settings` VALUES (1,1,'#2316d4','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي','#000000','شارع احمد يحيي','***********',0,'2025-06-10 13:53:56','2025-06-10 13:58:43'),(2,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-11 23:45:45','2025-06-11 23:45:45'),(3,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-11 23:52:09','2025-06-11 23:52:09'),(4,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 00:20:45','2025-06-12 00:20:45');
/*!40000 ALTER TABLE `invoice_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `time_cost` decimal(10,2) NOT NULL,
  `products_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`invoice_id`),
  KEY `session_id` (`session_id`),
  KEY `idx_invoices_payment_status` (`payment_status`),
  KEY `idx_invoices_client_id` (`client_id`),
  CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (20,35,'202506140035',15.00,5.00,20.00,1,'pending','2025-06-14 10:35:13',1),(21,36,'202506140036',0.00,10.00,10.00,1,'pending','2025-06-14 11:22:12',1),(22,37,'202506160037',10.00,5.00,15.00,1,'pending','2025-06-16 02:15:40',1),(23,38,'202506160038',15.00,0.00,15.00,1,'pending','2025-06-16 03:27:04',1),(24,39,'202506160039',0.00,20.00,20.00,1,'pending','2025-06-16 04:31:45',1),(25,40,'202506160040',0.00,35.00,35.00,1,'pending','2025-06-16 04:46:55',1),(26,41,'202506160041',25.00,15.00,40.00,1,'pending','2025-06-16 11:57:23',1);
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `monthly_attendance_stats`
--

DROP TABLE IF EXISTS `monthly_attendance_stats`;
/*!50001 DROP VIEW IF EXISTS `monthly_attendance_stats`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `monthly_attendance_stats` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `client_id`,
  1 AS `year`,
  1 AS `month`,
  1 AS `total_shifts`,
  1 AS `present_days`,
  1 AS `absent_days`,
  1 AS `late_days`,
  1 AS `total_hours`,
  1 AS `total_overtime`,
  1 AS `avg_late_minutes`,
  1 AS `attendance_percentage` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_order_items_order_id` (`order_id`),
  KEY `idx_order_items_product_id` (`product_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES (5,3,13,1,5.00,5.00,'2025-06-17 16:59:58');
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_order_number` (`client_id`,`order_number`),
  KEY `customer_id` (`customer_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES (3,1,43,NULL,'ORD-20250617-4572',5.00,'completed','cash','','2025-06-17 16:59:58','2025-06-17 17:00:03',1,0.00,0.00);
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pages`
--

DROP TABLE IF EXISTS `pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `page_name` (`page_name`),
  KEY `idx_pages_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pages`
--

LOCK TABLES `pages` WRITE;
/*!40000 ALTER TABLE `pages` DISABLE KEYS */;
INSERT INTO `pages` VALUES (1,'dashboard','لوحة التحكم','dashboard.php','fas fa-tachometer-alt','main',1,'2025-06-12 13:25:00'),(2,'profile','الملف الشخصي','profile.php','fas fa-user','main',1,'2025-06-12 13:25:00'),(3,'devices','إدارة الأجهزة','devices.php','fas fa-gamepad','devices',1,'2025-06-12 13:25:00'),(4,'rooms','إدارة الغرف','rooms.php','fas fa-door-open','devices',1,'2025-06-12 13:25:00'),(5,'sessions','إدارة الجلسات','sessions.php','fas fa-play-circle','sessions',1,'2025-06-12 13:25:00'),(6,'customers','إدارة العملاء','customers.php','fas fa-users','customers',1,'2025-06-12 13:25:00'),(7,'cafeteria','إدارة الكافتيريا','cafeteria.php','fas fa-coffee','cafeteria',1,'2025-06-12 13:25:00'),(8,'employees','إدارة الموظفين','employees.php','fas fa-user-tie','employees',1,'2025-06-12 13:25:00'),(9,'reports','التقارير','reports.php','fas fa-chart-bar','reports',1,'2025-06-12 13:25:00'),(10,'finances','المالية','finances.php','fas fa-money-bill-wave','finances',1,'2025-06-12 13:25:00'),(11,'invoices','الفواتير','invoices.php','fas fa-file-invoice','invoices',1,'2025-06-12 13:25:00'),(12,'settings','الإعدادات','settings.php','fas fa-cog','settings',1,'2025-06-12 13:25:00'),(13,'shifts','الورديات','shifts.php','fas fa-clock','shifts',1,'2025-06-14 12:05:01'),(14,'attendance','الحضور والانصراف','attendance.php','fas fa-user-check','shifts',1,'2025-06-14 12:05:01'),(15,'shift_reports','تقارير الورديات','shift_reports.php','fas fa-chart-line','shifts',1,'2025-06-14 12:05:01'),(17,'orders','إدارة الأوردرات','orders.php','fas fa-shopping-cart','orders',1,'2025-06-16 12:19:08');
/*!40000 ALTER TABLE `pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_status_log`
--

DROP TABLE IF EXISTS `payment_status_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_status_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `old_status` enum('pending','paid','cancelled') DEFAULT NULL,
  `new_status` enum('pending','paid','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_payment_log_invoice` (`invoice_id`),
  KEY `idx_payment_log_date` (`changed_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_status_log`
--

LOCK TABLES `payment_status_log` WRITE;
/*!40000 ALTER TABLE `payment_status_log` DISABLE KEYS */;
INSERT INTO `payment_status_log` VALUES (2,16,'pending','cancelled',1,'2025-06-14 05:40:49'),(3,15,'pending','cancelled',1,'2025-06-14 05:40:51'),(4,14,'pending','cancelled',1,'2025-06-14 05:40:53');
/*!40000 ALTER TABLE `payment_status_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(100) NOT NULL,
  `permission_label` varchar(200) NOT NULL,
  `permission_description` text DEFAULT NULL,
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `permission_name` (`permission_name`),
  KEY `idx_permissions_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'manage_devices','إدارة الأجهزة','إضافة وتعديل وحذف الأجهزة','devices',1,'2025-06-12 13:25:00'),(2,'view_devices','عرض الأجهزة','عرض قائمة الأجهزة فقط','devices',1,'2025-06-12 13:25:00'),(3,'manage_sessions','إدارة الجلسات','بدء وإنهاء وتعديل الجلسات','sessions',1,'2025-06-12 13:25:00'),(4,'view_sessions','عرض الجلسات','عرض قائمة الجلسات فقط','sessions',1,'2025-06-12 13:25:00'),(5,'manage_rooms','إدارة الغرف','إضافة وتعديل وحذف الغرف','rooms',1,'2025-06-12 13:25:00'),(6,'view_rooms','عرض الغرف','عرض قائمة الغرف فقط','rooms',1,'2025-06-12 13:25:00'),(7,'manage_customers','إدارة العملاء','إضافة وتعديل وحذف العملاء','customers',1,'2025-06-12 13:25:00'),(8,'view_customers','عرض العملاء','عرض قائمة العملاء فقط','customers',1,'2025-06-12 13:25:00'),(9,'manage_cafeteria','إدارة الكافتيريا','إدارة منتجات وطلبات الكافتيريا','cafeteria',1,'2025-06-12 13:25:00'),(10,'view_cafeteria','عرض الكافتيريا','عرض منتجات الكافتيريا فقط','cafeteria',1,'2025-06-12 13:25:00'),(11,'manage_employees','إدارة الموظفين','إضافة وتعديل وحذف الموظفين','employees',1,'2025-06-12 13:25:00'),(12,'view_employees','عرض الموظفين','عرض قائمة الموظفين فقط','employees',1,'2025-06-12 13:25:00'),(13,'view_reports','عرض التقارير','عرض التقارير والإحصائيات','reports',1,'2025-06-12 13:25:00'),(14,'view_finances','عرض المالية','عرض البيانات المالية','finances',1,'2025-06-12 13:25:00'),(15,'manage_finances','إدارة المالية','إدارة المصروفات والإيرادات','finances',1,'2025-06-12 13:25:00'),(16,'manage_invoices','إدارة الفواتير','إنشاء وتعديل الفواتير','invoices',1,'2025-06-12 13:25:00'),(17,'view_invoices','عرض الفواتير','عرض الفواتير فقط','invoices',1,'2025-06-12 13:25:00'),(18,'manage_settings','إدارة الإعدادات','تعديل إعدادات النظام','settings',1,'2025-06-12 13:25:00'),(19,'view_profile','عرض الملف الشخصي','عرض وتعديل الملف الشخصي','profile',1,'2025-06-12 13:25:00'),(20,'manage_shifts','إدارة الورديات','إنشاء وتعديل وحذف الورديات','shifts',1,'2025-06-14 12:05:01'),(21,'view_shifts','عرض الورديات','عرض جدول الورديات فقط','shifts',1,'2025-06-14 12:05:01'),(22,'manage_attendance','إدارة الحضور','تسجيل حضور وانصراف الموظفين','shifts',1,'2025-06-14 12:05:01'),(23,'view_attendance','عرض الحضور','عرض سجلات الحضور والغياب','shifts',1,'2025-06-14 12:05:01'),(24,'assign_shifts','تخصيص الورديات','تخصيص الموظفين للورديات','shifts',1,'2025-06-14 12:05:01'),(25,'view_shift_reports','تقارير الورديات','عرض تقارير الحضور والورديات','shifts',1,'2025-06-14 12:05:01'),(27,'manage_orders','إدارة الأوردرات','إنشاء وتعديل وحذف الأوردرات المستقلة','orders',1,'2025-06-16 12:19:08'),(28,'view_orders','عرض الأوردرات','عرض قائمة الأوردرات فقط','orders',1,'2025-06-16 12:19:08');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `plan_features`
--

DROP TABLE IF EXISTS `plan_features`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plan_features` (
  `feature_id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `feature_name_ar` varchar(100) NOT NULL,
  `feature_limit` int(11) DEFAULT -1 COMMENT '-1 = غير محدود، 0 = غير مسموح، >0 = الحد الأقصى',
  `is_enabled` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`feature_id`),
  UNIQUE KEY `unique_plan_feature` (`plan_id`,`feature_type`,`feature_name`),
  CONSTRAINT `plan_features_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`plan_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `plan_features`
--

LOCK TABLES `plan_features` WRITE;
/*!40000 ALTER TABLE `plan_features` DISABLE KEYS */;
INSERT INTO `plan_features` VALUES (1,1,'devices','max_devices','عدد الأجهزة',5,1,'2025-06-17 18:50:02'),(2,1,'products','max_products','عدد المنتجات',20,1,'2025-06-17 18:50:02'),(3,1,'employees','max_employees','عدد الموظفين',3,1,'2025-06-17 18:50:02'),(4,1,'customers','max_customers','عدد العملاء',100,1,'2025-06-17 18:50:02'),(5,1,'sessions','max_daily_sessions','الجلسات اليومية',50,1,'2025-06-17 18:50:02'),(6,1,'storage','max_storage_mb','مساحة التخزين (ميجا)',100,1,'2025-06-17 18:50:02'),(7,1,'pages','dashboard','لوحة التحكم',1,1,'2025-06-17 18:50:02'),(8,1,'pages','devices','إدارة الأجهزة',1,1,'2025-06-17 18:50:02'),(9,1,'pages','customers','إدارة العملاء',1,1,'2025-06-17 18:50:02'),(10,1,'pages','sessions','إدارة الجلسات',1,1,'2025-06-17 18:50:02'),(11,1,'pages','invoices','الفواتير',1,1,'2025-06-17 18:50:02'),(12,1,'pages','cafeteria','الكافتيريا',0,1,'2025-06-17 18:50:02'),(13,1,'pages','employees','إدارة الموظفين',0,1,'2025-06-17 18:50:02'),(14,1,'pages','reports','التقارير',0,1,'2025-06-17 18:50:02'),(15,1,'pages','inventory','إدارة المخزون',0,1,'2025-06-17 18:50:02'),(16,2,'devices','max_devices','عدد الأجهزة',15,1,'2025-06-17 18:50:02'),(17,2,'products','max_products','عدد المنتجات',100,1,'2025-06-17 18:50:02'),(18,2,'employees','max_employees','عدد الموظفين',10,1,'2025-06-17 18:50:02'),(19,2,'customers','max_customers','عدد العملاء',500,1,'2025-06-17 18:50:02'),(20,2,'sessions','max_daily_sessions','الجلسات اليومية',200,1,'2025-06-17 18:50:02'),(21,2,'storage','max_storage_mb','مساحة التخزين (ميجا)',500,1,'2025-06-17 18:50:02'),(22,2,'pages','dashboard','لوحة التحكم',1,1,'2025-06-17 18:50:02'),(23,2,'pages','devices','إدارة الأجهزة',1,1,'2025-06-17 18:50:02'),(24,2,'pages','customers','إدارة العملاء',1,1,'2025-06-17 18:50:02'),(25,2,'pages','sessions','إدارة الجلسات',1,1,'2025-06-17 18:50:02'),(26,2,'pages','invoices','الفواتير',1,1,'2025-06-17 18:50:02'),(27,2,'pages','cafeteria','الكافتيريا',1,1,'2025-06-17 18:50:02'),(28,2,'pages','employees','إدارة الموظفين',1,1,'2025-06-17 18:50:02'),(29,2,'pages','reports','التقارير',1,1,'2025-06-17 18:50:02'),(30,2,'pages','inventory','إدارة المخزون',0,1,'2025-06-17 18:50:02'),(31,2,'pages','rooms','إدارة الغرف',1,1,'2025-06-17 18:50:02'),(32,2,'pages','orders','إدارة الأوردرات',1,1,'2025-06-17 18:50:02'),(33,3,'devices','max_devices','عدد الأجهزة',50,1,'2025-06-17 18:50:02'),(34,3,'products','max_products','عدد المنتجات',500,1,'2025-06-17 18:50:02'),(35,3,'employees','max_employees','عدد الموظفين',25,1,'2025-06-17 18:50:02'),(36,3,'customers','max_customers','عدد العملاء',2000,1,'2025-06-17 18:50:02'),(37,3,'sessions','max_daily_sessions','الجلسات اليومية',1000,1,'2025-06-17 18:50:02'),(38,3,'storage','max_storage_mb','مساحة التخزين (ميجا)',2000,1,'2025-06-17 18:50:02'),(39,3,'pages','dashboard','لوحة التحكم',1,1,'2025-06-17 18:50:02'),(40,3,'pages','devices','إدارة الأجهزة',1,1,'2025-06-17 18:50:02'),(41,3,'pages','customers','إدارة العملاء',1,1,'2025-06-17 18:50:02'),(42,3,'pages','sessions','إدارة الجلسات',1,1,'2025-06-17 18:50:02'),(43,3,'pages','invoices','الفواتير',1,1,'2025-06-17 18:50:02'),(44,3,'pages','cafeteria','الكافتيريا',1,1,'2025-06-17 18:50:02'),(45,3,'pages','employees','إدارة الموظفين',1,1,'2025-06-17 18:50:02'),(46,3,'pages','reports','التقارير',1,1,'2025-06-17 18:50:02'),(47,3,'pages','inventory','إدارة المخزون',1,1,'2025-06-17 18:50:02'),(48,3,'pages','rooms','إدارة الغرف',1,1,'2025-06-17 18:50:02'),(49,3,'pages','orders','إدارة الأوردرات',1,1,'2025-06-17 18:50:02'),(50,3,'pages','attendance','نظام الحضور',1,1,'2025-06-17 18:50:02'),(51,3,'pages','shifts','إدارة الورديات',1,1,'2025-06-17 18:50:02'),(52,3,'pages','finance','الإدارة المالية',1,1,'2025-06-17 18:50:02'),(53,3,'pages','reservations','إدارة الحجوزات',1,1,'2025-06-17 18:50:02'),(54,4,'devices','max_devices','عدد الأجهزة',-1,1,'2025-06-17 18:50:03'),(55,4,'products','max_products','عدد المنتجات',-1,1,'2025-06-17 18:50:03'),(56,4,'employees','max_employees','عدد الموظفين',-1,1,'2025-06-17 18:50:03'),(57,4,'customers','max_customers','عدد العملاء',-1,1,'2025-06-17 18:50:03'),(58,4,'sessions','max_daily_sessions','الجلسات اليومية',-1,1,'2025-06-17 18:50:03'),(59,4,'storage','max_storage_mb','مساحة التخزين (ميجا)',-1,1,'2025-06-17 18:50:03'),(60,4,'pages','dashboard','لوحة التحكم',1,1,'2025-06-17 18:50:03'),(61,4,'pages','devices','إدارة الأجهزة',1,1,'2025-06-17 18:50:03'),(62,4,'pages','customers','إدارة العملاء',1,1,'2025-06-17 18:50:03'),(63,4,'pages','sessions','إدارة الجلسات',1,1,'2025-06-17 18:50:03'),(64,4,'pages','invoices','الفواتير',1,1,'2025-06-17 18:50:03'),(65,4,'pages','cafeteria','الكافتيريا',1,1,'2025-06-17 18:50:03'),(66,4,'pages','employees','إدارة الموظفين',1,1,'2025-06-17 18:50:03'),(67,4,'pages','reports','التقارير',1,1,'2025-06-17 18:50:03'),(68,4,'pages','inventory','إدارة المخزون',1,1,'2025-06-17 18:50:03'),(69,4,'pages','rooms','إدارة الغرف',1,1,'2025-06-17 18:50:03'),(70,4,'pages','orders','إدارة الأوردرات',1,1,'2025-06-17 18:50:03'),(71,4,'pages','attendance','نظام الحضور',1,1,'2025-06-17 18:50:03'),(72,4,'pages','shifts','إدارة الورديات',1,1,'2025-06-17 18:50:03'),(73,4,'pages','finance','الإدارة المالية',1,1,'2025-06-17 18:50:03'),(74,4,'pages','reservations','إدارة الحجوزات',1,1,'2025-06-17 18:50:03'),(75,4,'pages','invoice_settings','إعدادات الفواتير',1,1,'2025-06-17 18:50:03');
/*!40000 ALTER TABLE `plan_features` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `plan_usage_log`
--

DROP TABLE IF EXISTS `plan_usage_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plan_usage_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `action` enum('add','remove','update') NOT NULL,
  `old_count` int(11) DEFAULT 0,
  `new_count` int(11) DEFAULT 0,
  `limit_exceeded` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`),
  KEY `idx_client_feature` (`client_id`,`feature_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `plan_usage_log_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `plan_usage_log`
--

LOCK TABLES `plan_usage_log` WRITE;
/*!40000 ALTER TABLE `plan_usage_log` DISABLE KEYS */;
INSERT INTO `plan_usage_log` VALUES (1,1,'devices','max_devices','add',0,6,0,'2025-06-17 19:44:02'),(2,4,'products','max_products','add',0,1,0,'2025-06-17 19:46:31'),(3,1,'products','max_products','add',0,11,0,'2025-06-18 18:34:36');
/*!40000 ALTER TABLE `plan_usage_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_categories`
--

DROP TABLE IF EXISTS `product_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL COMMENT 'التصنيف الأب للتصنيفات الفرعية',
  `icon` varchar(50) DEFAULT 'fas fa-box' COMMENT 'أيقونة التصنيف',
  `color` varchar(7) DEFAULT '#007bff' COMMENT 'لون التصنيف',
  `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
  `is_active` tinyint(1) DEFAULT 1,
  `client_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_category_per_client` (`name`,`client_id`),
  KEY `parent_id` (`parent_id`),
  KEY `idx_categories_client` (`client_id`),
  CONSTRAINT `product_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_categories`
--

LOCK TABLES `product_categories` WRITE;
/*!40000 ALTER TABLE `product_categories` DISABLE KEYS */;
INSERT INTO `product_categories` VALUES (1,'مشروبات ساخنة','الشاي والقهوة والمشروبات الساخنة',NULL,'fas fa-coffee','#8B4513',0,1,1,'2025-06-16 12:48:18'),(2,'مشروبات باردة','العصائر والمياه والمشروبات الباردة',NULL,'fas fa-glass-whiskey','#4169E1',0,1,1,'2025-06-16 12:48:18'),(3,'وجبات خفيفة','الساندويتشات والوجبات السريعة',NULL,'fas fa-hamburger','#FF6347',0,1,1,'2025-06-16 12:48:18'),(4,'حلويات','الكيك والحلويات والآيس كريم',NULL,'fas fa-birthday-cake','#FF69B4',0,1,1,'2025-06-16 12:48:18'),(5,'أكسسوارات الألعاب','يدات التحكم والسماعات والكابلات',NULL,'fas fa-gamepad','#32CD32',0,1,1,'2025-06-16 12:48:18'),(6,'قطع غيار','قطع غيار الأجهزة والصيانة',NULL,'fas fa-tools','#FF8C00',0,1,1,'2025-06-16 12:48:18');
/*!40000 ALTER TABLE `product_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_device_associations`
--

DROP TABLE IF EXISTS `product_device_associations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_device_associations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `device_part` enum('controller','headset','cable','accessory','other') DEFAULT 'other' COMMENT 'نوع الجزء',
  `quantity_needed` int(11) DEFAULT 1 COMMENT 'الكمية المطلوبة لكل جهاز',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_product_device` (`product_id`,`device_id`,`device_part`),
  KEY `device_id` (`device_id`),
  CONSTRAINT `product_device_associations_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_device_associations_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_device_associations`
--

LOCK TABLES `product_device_associations` WRITE;
/*!40000 ALTER TABLE `product_device_associations` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_device_associations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  PRIMARY KEY (`product_id`),
  KEY `category_id` (`category_id`),
  KEY `section_id` (`section_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rooms`
--

DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_type` enum('VIP','regular','private') DEFAULT 'regular',
  `capacity` int(11) DEFAULT 1,
  `special_rate` decimal(8,2) DEFAULT 0.00,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`room_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rooms`
--

LOCK TABLES `rooms` WRITE;
/*!40000 ALTER TABLE `rooms` DISABLE KEYS */;
/*!40000 ALTER TABLE `rooms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sections`
--

DROP TABLE IF EXISTS `sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sections`
--

LOCK TABLES `sections` WRITE;
/*!40000 ALTER TABLE `sections` DISABLE KEYS */;
/*!40000 ALTER TABLE `sections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `session_products`
--

DROP TABLE IF EXISTS `session_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL COMMENT 'سعر المنتج وقت الإضافة',
  `total` decimal(10,2) GENERATED ALWAYS AS (`price` * `quantity`) STORED COMMENT 'إجمالي السعر',
  `created_by` int(11) DEFAULT NULL COMMENT 'معرف المستخدم الذي أضاف المنتج',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`),
  CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `session_products`
--

LOCK TABLES `session_products` WRITE;
/*!40000 ALTER TABLE `session_products` DISABLE KEYS */;
INSERT INTO `session_products` VALUES (46,35,13,1,'2025-06-14 10:34:58',5.00,5.00,NULL,NULL),(47,36,25,1,'2025-06-14 11:22:03',10.00,10.00,NULL,NULL),(48,37,13,1,'2025-06-16 02:10:06',5.00,5.00,NULL,NULL),(49,39,25,1,'2025-06-16 04:31:05',10.00,10.00,NULL,NULL),(50,39,25,1,'2025-06-16 04:31:24',10.00,10.00,NULL,NULL),(51,40,18,1,'2025-06-16 04:46:46',35.00,35.00,NULL,NULL),(52,41,25,1,'2025-06-16 11:55:12',10.00,10.00,NULL,NULL),(53,41,13,1,'2025-06-16 11:55:33',5.00,5.00,NULL,NULL);
/*!40000 ALTER TABLE `session_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_id` int(11) DEFAULT NULL,
  `client_id` int(11) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `game_type` enum('single','multiplayer') DEFAULT 'single' COMMENT 'نوع اللعب: فردي أو زوجي',
  PRIMARY KEY (`session_id`),
  KEY `customer_id` (`customer_id`),
  KEY `idx_sessions_client_id` (`client_id`),
  KEY `idx_sessions_status` (`status`),
  KEY `idx_sessions_device_id` (`device_id`),
  KEY `idx_sessions_start_time` (`start_time`),
  KEY `idx_sessions_device_status` (`device_id`,`status`),
  KEY `idx_sessions_client_device` (`client_id`,`device_id`),
  KEY `idx_sessions_game_type` (`game_type`),
  CONSTRAINT `sessions_device_fk` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `sessions_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES (35,16,'2025-06-14 16:22:48','2025-06-14 16:35:13','completed',1,1,'2025-06-14 10:22:48','2025-06-14 10:35:13',NULL,1,20.00,NULL,'','single'),(36,16,'2025-06-14 17:21:54','2025-06-14 17:22:12','completed',1,1,'2025-06-14 11:21:54','2025-06-14 11:22:12',NULL,1,10.00,NULL,'','single'),(37,19,'2025-06-16 08:09:50','2025-06-16 08:15:40','completed',1,1,'2025-06-16 02:09:50','2025-06-16 02:15:40',43,1,15.00,'2025-06-16 01:39:50','','single'),(38,16,'2025-06-16 08:27:40','2025-06-16 09:27:04','completed',1,1,'2025-06-16 02:27:40','2025-06-16 03:27:04',43,1,15.00,'2025-06-16 01:57:40','','single'),(39,16,'2025-06-16 10:30:55','2025-06-16 10:31:45','completed',1,1,'2025-06-16 04:30:55','2025-06-16 04:31:45',43,1,20.00,NULL,'','single'),(40,18,'2025-06-16 10:46:42','2025-06-16 10:46:55','completed',1,1,'2025-06-16 04:46:42','2025-06-16 04:46:55',43,1,35.00,NULL,'','single'),(41,23,'2025-06-16 17:55:05','2025-06-16 17:57:23','completed',1,1,'2025-06-16 11:55:05','2025-06-16 11:57:23',43,1,40.00,NULL,'','single');
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_attendance`
--

DROP TABLE IF EXISTS `shift_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_attendance` (
  `attendance_id` int(11) NOT NULL AUTO_INCREMENT,
  `assignment_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  `break_start_time` timestamp NULL DEFAULT NULL,
  `break_end_time` timestamp NULL DEFAULT NULL,
  `actual_hours` decimal(4,2) DEFAULT 0.00,
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `break_hours` decimal(4,2) DEFAULT 0.00,
  `status` enum('absent','present','late','early_leave','overtime') DEFAULT 'absent',
  `late_minutes` int(11) DEFAULT 0,
  `early_leave_minutes` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `recorded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`attendance_id`),
  KEY `assignment_id` (`assignment_id`),
  KEY `idx_attendance_date` (`check_in_time`),
  KEY `idx_attendance_employee` (`employee_id`),
  KEY `idx_attendance_shift` (`shift_id`),
  CONSTRAINT `shift_attendance_ibfk_1` FOREIGN KEY (`assignment_id`) REFERENCES `employee_shifts` (`assignment_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_attendance`
--

LOCK TABLES `shift_attendance` WRITE;
/*!40000 ALTER TABLE `shift_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_notifications`
--

DROP TABLE IF EXISTS `shift_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `notification_type` enum('shift_start_reminder','shift_end_reminder','shift_started','shift_ended','late_warning','absence_alert') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `scheduled_time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `sent_time` timestamp NULL DEFAULT NULL,
  `is_sent` tinyint(1) DEFAULT 0,
  `is_read` tinyint(1) DEFAULT 0,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`notification_id`),
  KEY `shift_id` (`shift_id`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_employee_sent` (`employee_id`,`is_sent`),
  KEY `idx_client_type` (`client_id`,`notification_type`),
  CONSTRAINT `shift_notifications_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_notifications_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shift_notifications_ibfk_3` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_notifications`
--

LOCK TABLES `shift_notifications` WRITE;
/*!40000 ALTER TABLE `shift_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_settings`
--

DROP TABLE IF EXISTS `shift_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_name`),
  CONSTRAINT `shift_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_settings`
--

LOCK TABLES `shift_settings` WRITE;
/*!40000 ALTER TABLE `shift_settings` DISABLE KEYS */;
INSERT INTO `shift_settings` VALUES (1,1,'grace_period_minutes','15','فترة السماح للتأخير بالدقائق','2025-06-14 12:05:01','2025-06-14 12:05:01'),(2,1,'overtime_threshold_minutes','30','الحد الأدنى للعمل الإضافي بالدقائق','2025-06-14 12:05:01','2025-06-14 12:05:01'),(3,1,'auto_checkout_hours','12','تسجيل خروج تلقائي بعد عدد ساعات','2025-06-14 12:05:01','2025-06-14 12:05:01'),(4,1,'break_duration_default','30','مدة الاستراحة الافتراضية بالدقائق','2025-06-14 12:05:01','2025-06-14 12:05:01'),(5,1,'notification_before_shift','60','إشعار قبل بداية الوردية بالدقائق','2025-06-14 12:05:01','2025-06-14 12:05:01'),(6,1,'allow_early_checkin','30','السماح بتسجيل الدخول المبكر بالدقائق','2025-06-14 12:05:01','2025-06-14 12:05:01');
/*!40000 ALTER TABLE `shift_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_templates`
--

DROP TABLE IF EXISTS `shift_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_templates` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0 COMMENT 'وردية ليلية تمتد لليوم التالي',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`template_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `shift_templates_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_templates`
--

LOCK TABLES `shift_templates` WRITE;
/*!40000 ALTER TABLE `shift_templates` DISABLE KEYS */;
INSERT INTO `shift_templates` VALUES (1,1,'الوردية الصباحية','08:00:00','16:00:00',60,0,'وردية صباحية من 8 صباحاً إلى 4 عصراً',1,'2025-06-14 12:05:01','2025-06-14 12:05:01'),(2,1,'الوردية المسائية','16:00:00','00:00:00',60,0,'وردية مسائية من 4 عصراً إلى 12 منتصف الليل',1,'2025-06-14 12:05:01','2025-06-14 12:05:01'),(3,1,'الوردية الليلية','00:00:00','08:00:00',60,0,'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً',1,'2025-06-14 12:05:01','2025-06-14 12:05:01'),(4,1,'الوردية الصباحية','08:00:00','16:00:00',60,0,'وردية صباحية من 8 صباحاً إلى 4 عصراً',1,'2025-06-14 12:10:54','2025-06-14 12:10:54'),(5,1,'الوردية المسائية','16:00:00','00:00:00',60,0,'وردية مسائية من 4 عصراً إلى 12 منتصف الليل',1,'2025-06-14 12:10:54','2025-06-14 12:10:54'),(6,1,'الوردية الليلية','00:00:00','08:00:00',60,0,'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً',1,'2025-06-14 12:10:54','2025-06-14 12:10:54');
/*!40000 ALTER TABLE `shift_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shifts`
--

DROP TABLE IF EXISTS `shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shifts` (
  `shift_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `shift_name` varchar(100) NOT NULL,
  `shift_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0,
  `max_employees` int(11) DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
  `min_employees` int(11) DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
  `status` enum('scheduled','active','completed','cancelled') DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`shift_id`),
  KEY `template_id` (`template_id`),
  KEY `idx_shifts_date` (`shift_date`),
  KEY `idx_shifts_status` (`status`),
  KEY `idx_shifts_client` (`client_id`),
  CONSTRAINT `shifts_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `shifts_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `shift_templates` (`template_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shifts`
--

LOCK TABLES `shifts` WRITE;
/*!40000 ALTER TABLE `shifts` DISABLE KEYS */;
INSERT INTO `shifts` VALUES (3,1,NULL,'وردية 1','2025-06-17','20:37:00','12:40:00',0,0,1,1,'scheduled','',1,'2025-06-17 17:36:33','2025-06-17 17:36:33');
/*!40000 ALTER TABLE `shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `shifts_detailed`
--

DROP TABLE IF EXISTS `shifts_detailed`;
/*!50001 DROP VIEW IF EXISTS `shifts_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `shifts_detailed` AS SELECT
 1 AS `shift_id`,
  1 AS `client_id`,
  1 AS `shift_name`,
  1 AS `shift_date`,
  1 AS `start_time`,
  1 AS `end_time`,
  1 AS `break_duration`,
  1 AS `is_overnight`,
  1 AS `max_employees`,
  1 AS `min_employees`,
  1 AS `shift_status`,
  1 AS `shift_notes`,
  1 AS `template_name`,
  1 AS `assigned_employees`,
  1 AS `confirmed_employees`,
  1 AS `present_employees`,
  1 AS `absent_employees` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `subscription_plans`
--

DROP TABLE IF EXISTS `subscription_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscription_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL,
  `plan_name_ar` varchar(100) NOT NULL,
  `plan_description` text DEFAULT NULL,
  `plan_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `plan_duration_days` int(11) NOT NULL DEFAULT 30,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `plan_name` (`plan_name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscription_plans`
--

LOCK TABLES `subscription_plans` WRITE;
/*!40000 ALTER TABLE `subscription_plans` DISABLE KEYS */;
INSERT INTO `subscription_plans` VALUES (1,'basic','الخطة الأساسية','خطة مناسبة للمحلات الصغيرة مع الميزات الأساسية',99.00,30,1,1,1,'2025-06-17 18:50:02','2025-06-17 18:50:02'),(2,'premium','الخطة المميزة','خطة متقدمة للمحلات المتوسطة مع ميزات إضافية',199.00,30,1,0,2,'2025-06-17 18:50:02','2025-06-17 18:50:02'),(3,'enterprise','الخطة المؤسسية','خطة شاملة للمحلات الكبيرة مع جميع الميزات',399.00,30,1,0,3,'2025-06-17 18:50:02','2025-06-17 18:50:02'),(4,'unlimited','الخطة اللامحدودة','خطة بلا حدود لأصحاب السلاسل التجارية',799.00,30,1,0,4,'2025-06-17 18:50:02','2025-06-17 18:50:02');
/*!40000 ALTER TABLE `subscription_plans` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_cafeteria_items`
--

DROP TABLE IF EXISTS `trial_cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_cafeteria_items_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_cafeteria_items`
--

LOCK TABLES `trial_cafeteria_items` WRITE;
/*!40000 ALTER TABLE `trial_cafeteria_items` DISABLE KEYS */;
INSERT INTO `trial_cafeteria_items` VALUES (26,8,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-20 05:40:32'),(27,8,'قهوة',10.00,'مشروبات ساخنة','قهوة تركي','2025-06-20 05:40:32'),(28,8,'بيبسي',12.00,'مشروبات باردة','مشروب غازي','2025-06-20 05:40:32'),(29,8,'ساندويتش',25.00,'مأكولات','ساندويتش جبن','2025-06-20 05:40:32'),(30,8,'asd',10.00,'مشروبات ساخنة','','2025-06-20 05:41:09');
/*!40000 ALTER TABLE `trial_cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_clients`
--

DROP TABLE IF EXISTS `trial_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_clients` (
  `trial_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `trial_start` timestamp NOT NULL DEFAULT current_timestamp(),
  `trial_end` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`trial_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_clients`
--

LOCK TABLES `trial_clients` WRITE;
/*!40000 ALTER TABLE `trial_clients` DISABLE KEYS */;
INSERT INTO `trial_clients` VALUES (8,'test','محمود','<EMAIL>','***********','$2y$10$19ZcG3aqmp0rdBcYdphswuYMMKP/eYQCkvVRqH./WCSD6/zgqgjma','2025-06-20 05:40:32','2025-06-20 10:40:32',1,'2025-06-20 05:40:32');
/*!40000 ALTER TABLE `trial_clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_customers`
--

DROP TABLE IF EXISTS `trial_customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`customer_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_customers_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_customers`
--

LOCK TABLES `trial_customers` WRITE;
/*!40000 ALTER TABLE `trial_customers` DISABLE KEYS */;
INSERT INTO `trial_customers` VALUES (25,8,'أحمد محمد','01234567890','<EMAIL>','2025-06-20 05:40:32'),(26,8,'فاطمة علي','01234567891','<EMAIL>','2025-06-20 05:40:32'),(27,8,'محمد حسن','01234567892','<EMAIL>','2025-06-20 05:40:32'),(28,8,'سارة أحمد','01234567893','<EMAIL>','2025-06-20 05:40:32');
/*!40000 ALTER TABLE `trial_customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_devices`
--

DROP TABLE IF EXISTS `trial_devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 15.00,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_devices_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_devices`
--

LOCK TABLES `trial_devices` WRITE;
/*!40000 ALTER TABLE `trial_devices` DISABLE KEYS */;
INSERT INTO `trial_devices` VALUES (25,8,'جهاز PS5 - تجريبي','PS5',20.00,'available','2025-06-20 05:40:32'),(26,8,'جهاز PS4 - تجريبي','PS4',15.00,'available','2025-06-20 05:40:32'),(27,8,'جهاز Xbox - تجريبي','Xbox',18.00,'available','2025-06-20 05:40:32'),(28,8,'جهاز PC - تجريبي','PC',25.00,'occupied','2025-06-20 05:40:32');
/*!40000 ALTER TABLE `trial_devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_sessions`
--

DROP TABLE IF EXISTS `trial_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` datetime DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`session_id`),
  KEY `trial_id` (`trial_id`),
  KEY `device_id` (`device_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `trial_sessions_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `trial_devices` (`device_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `trial_customers` (`customer_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_sessions`
--

LOCK TABLES `trial_sessions` WRITE;
/*!40000 ALTER TABLE `trial_sessions` DISABLE KEYS */;
INSERT INTO `trial_sessions` VALUES (4,8,28,25,'2025-06-20 05:40:53',NULL,0,0.00,'active','2025-06-20 05:40:53');
/*!40000 ALTER TABLE `trial_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `attendance_detailed`
--

/*!50001 DROP VIEW IF EXISTS `attendance_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8 */;
/*!50001 SET character_set_results     = utf8 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `attendance_detailed` AS select 1 AS `attendance_id`,1 AS `shift_id`,1 AS `employee_id`,1 AS `employee_name`,1 AS `employee_role`,1 AS `shift_name`,1 AS `shift_date`,1 AS `scheduled_start`,1 AS `scheduled_end`,1 AS `check_in_time`,1 AS `check_out_time`,1 AS `break_start_time`,1 AS `break_end_time`,1 AS `actual_hours`,1 AS `overtime_hours`,1 AS `break_hours`,1 AS `attendance_status`,1 AS `late_minutes`,1 AS `early_leave_minutes`,1 AS `attendance_notes`,1 AS `role_in_shift`,1 AS `is_mandatory` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `client_limits_usage`
--

/*!50001 DROP VIEW IF EXISTS `client_limits_usage`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `client_limits_usage` AS select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`subscription_plan` AS `subscription_plan`,'devices' AS `feature_type`,'max_devices' AS `feature_name`,coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) AS `feature_limit`,count(`d`.`device_id`) AS `current_usage`,case when coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) = -1 then 'unlimited' when count(`d`.`device_id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) then 'limit_reached' when count(`d`.`device_id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) * 0.8 then 'warning' else 'normal' end AS `usage_status` from ((((`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) left join `plan_features` `pf` on(`sp`.`plan_id` = `pf`.`plan_id` and `pf`.`feature_type` = 'devices' and `pf`.`feature_name` = 'max_devices')) left join `client_plan_limits` `cpl` on(`c`.`client_id` = `cpl`.`client_id` and `cpl`.`feature_type` = 'devices' and `cpl`.`feature_name` = 'max_devices')) left join `devices` `d` on(`c`.`client_id` = `d`.`client_id`)) where `c`.`is_active` = 1 group by `c`.`client_id` union all select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`subscription_plan` AS `subscription_plan`,'products' AS `feature_type`,'max_products' AS `feature_name`,coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) AS `feature_limit`,count(`ci`.`id`) AS `current_usage`,case when coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) = -1 then 'unlimited' when count(`ci`.`id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) then 'limit_reached' when count(`ci`.`id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) * 0.8 then 'warning' else 'normal' end AS `usage_status` from ((((`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) left join `plan_features` `pf` on(`sp`.`plan_id` = `pf`.`plan_id` and `pf`.`feature_type` = 'products' and `pf`.`feature_name` = 'max_products')) left join `client_plan_limits` `cpl` on(`c`.`client_id` = `cpl`.`client_id` and `cpl`.`feature_type` = 'products' and `cpl`.`feature_name` = 'max_products')) left join `cafeteria_items` `ci` on(`c`.`client_id` = `ci`.`client_id`)) where `c`.`is_active` = 1 group by `c`.`client_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `client_page_permissions_detailed`
--

/*!50001 DROP VIEW IF EXISTS `client_page_permissions_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `client_page_permissions_detailed` AS select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`owner_name` AS `owner_name`,`c`.`is_active` AS `client_active`,`cp`.`page_id` AS `page_id`,`cp`.`page_name` AS `page_name`,`cp`.`page_label` AS `page_label`,`cp`.`page_url` AS `page_url`,`cp`.`page_icon` AS `page_icon`,`cp`.`category` AS `category`,`cp`.`description` AS `description`,`cp`.`is_default` AS `is_default`,coalesce(`cpp`.`is_enabled`,`cp`.`is_default`) AS `has_permission`,`cpp`.`granted_at` AS `granted_at`,`cpp`.`updated_at` AS `updated_at` from ((`clients` `c` join `client_pages` `cp`) left join `client_page_permissions` `cpp` on(`c`.`client_id` = `cpp`.`client_id` and `cp`.`page_id` = `cpp`.`page_id`)) where `cp`.`is_active` = 1 order by `c`.`client_id`,`cp`.`category`,`cp`.`page_label` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `client_plan_details`
--

/*!50001 DROP VIEW IF EXISTS `client_plan_details`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `client_plan_details` AS select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`owner_name` AS `owner_name`,`c`.`subscription_plan` AS `subscription_plan`,`sp`.`plan_name_ar` AS `plan_name_ar`,`sp`.`plan_description` AS `plan_description`,`sp`.`plan_price` AS `plan_price`,`sp`.`plan_duration_days` AS `plan_duration_days`,`c`.`subscription_start` AS `subscription_start`,`c`.`subscription_end` AS `subscription_end`,to_days(`c`.`subscription_end`) - to_days(curdate()) AS `days_remaining`,case when `c`.`subscription_end` < curdate() then 'expired' when to_days(`c`.`subscription_end`) - to_days(curdate()) <= 7 then 'expiring_soon' else 'active' end AS `subscription_status` from (`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) where `c`.`is_active` = 1 */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `client_plan_summary`
--

/*!50001 DROP VIEW IF EXISTS `client_plan_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `client_plan_summary` AS select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`subscription_plan` AS `subscription_plan`,`sp`.`plan_name_ar` AS `plan_name_ar`,`sp`.`plan_price` AS `plan_price`,`c`.`subscription_end` AS `subscription_end`,case when `c`.`subscription_end` < curdate() then 'منتهية' when to_days(`c`.`subscription_end`) - to_days(curdate()) <= 7 then 'تنتهي قريباً' else 'نشطة' end AS `status_ar` from (`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) where `c`.`is_active` = 1 */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `employee_pages_detailed`
--

/*!50001 DROP VIEW IF EXISTS `employee_pages_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `employee_pages_detailed` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`role` AS `employee_role`,`e`.`custom_permissions` AS `custom_permissions`,`pg`.`page_id` AS `page_id`,`pg`.`page_name` AS `page_name`,`pg`.`page_label` AS `page_label`,`pg`.`page_url` AS `page_url`,`pg`.`page_icon` AS `page_icon`,`pg`.`category` AS `page_category`,`epg`.`granted_at` AS `granted_at` from ((`employees` `e` left join `employee_pages` `epg` on(`e`.`id` = `epg`.`employee_id`)) left join `pages` `pg` on(`epg`.`page_id` = `pg`.`page_id`)) where `e`.`is_active` = 1 and (`pg`.`is_active` = 1 or `pg`.`is_active` is null) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `employee_permissions_detailed`
--

/*!50001 DROP VIEW IF EXISTS `employee_permissions_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `employee_permissions_detailed` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`role` AS `employee_role`,`e`.`custom_permissions` AS `custom_permissions`,`p`.`permission_id` AS `permission_id`,`p`.`permission_name` AS `permission_name`,`p`.`permission_label` AS `permission_label`,`p`.`category` AS `permission_category`,`ep`.`granted_at` AS `granted_at` from ((`employees` `e` left join `employee_permissions` `ep` on(`e`.`id` = `ep`.`employee_id`)) left join `permissions` `p` on(`ep`.`permission_id` = `p`.`permission_id`)) where `e`.`is_active` = 1 and (`p`.`is_active` = 1 or `p`.`is_active` is null) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `inventory_overview`
--

/*!50001 DROP VIEW IF EXISTS `inventory_overview`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `inventory_overview` AS select `ci`.`id` AS `id`,`ci`.`name` AS `name`,`ci`.`price` AS `selling_price`,`ci`.`cost_price` AS `cost_price`,`ci`.`stock_quantity` AS `stock_quantity`,`ci`.`min_stock_level` AS `min_stock_level`,`ci`.`max_stock_level` AS `max_stock_level`,`ci`.`status` AS `status`,`ci`.`barcode` AS `barcode`,`ci`.`supplier` AS `supplier`,`ci`.`category` AS `category`,`ci`.`description` AS `description`,`ci`.`last_restock_date` AS `last_restock_date`,`ci`.`created_at` AS `created_at`,`ci`.`updated_at` AS `updated_at`,`ci`.`client_id` AS `client_id`,`pc`.`name` AS `category_name`,`pc`.`icon` AS `category_icon`,`pc`.`color` AS `category_color`,case when `ci`.`stock_quantity` <= 0 then 'نفذ' when `ci`.`stock_quantity` <= `ci`.`min_stock_level` then 'ناقص' when `ci`.`stock_quantity` >= `ci`.`max_stock_level` then 'مكتمل' else 'متوفر' end AS `stock_status_text`,case when `ci`.`stock_quantity` <= 0 then 'danger' when `ci`.`stock_quantity` <= `ci`.`min_stock_level` then 'warning' when `ci`.`stock_quantity` >= `ci`.`max_stock_level` then 'info' else 'success' end AS `stock_status_class`,`ci`.`price` - `ci`.`cost_price` AS `profit_margin`,round((`ci`.`price` - `ci`.`cost_price`) / `ci`.`price` * 100,2) AS `profit_percentage` from (`cafeteria_items` `ci` left join `product_categories` `pc` on(`ci`.`category` = `pc`.`name` and `ci`.`client_id` = `pc`.`client_id`)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `monthly_attendance_stats`
--

/*!50001 DROP VIEW IF EXISTS `monthly_attendance_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `monthly_attendance_stats` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`client_id` AS `client_id`,year(`s`.`shift_date`) AS `year`,month(`s`.`shift_date`) AS `month`,count(`sa`.`attendance_id`) AS `total_shifts`,count(case when `sa`.`status` = 'present' then 1 end) AS `present_days`,count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_days`,count(case when `sa`.`status` = 'late' then 1 end) AS `late_days`,sum(`sa`.`actual_hours`) AS `total_hours`,sum(`sa`.`overtime_hours`) AS `total_overtime`,avg(`sa`.`late_minutes`) AS `avg_late_minutes`,count(case when `sa`.`status` = 'present' then 1 end) * 100.0 / count(`sa`.`attendance_id`) AS `attendance_percentage` from (((`employees` `e` join `employee_shifts` `es` on(`e`.`id` = `es`.`employee_id`)) join `shifts` `s` on(`es`.`shift_id` = `s`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) group by `e`.`id`,year(`s`.`shift_date`),month(`s`.`shift_date`) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `shifts_detailed`
--

/*!50001 DROP VIEW IF EXISTS `shifts_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `shifts_detailed` AS select `s`.`shift_id` AS `shift_id`,`s`.`client_id` AS `client_id`,`s`.`shift_name` AS `shift_name`,`s`.`shift_date` AS `shift_date`,`s`.`start_time` AS `start_time`,`s`.`end_time` AS `end_time`,`s`.`break_duration` AS `break_duration`,`s`.`is_overnight` AS `is_overnight`,`s`.`max_employees` AS `max_employees`,`s`.`min_employees` AS `min_employees`,`s`.`status` AS `shift_status`,`s`.`notes` AS `shift_notes`,`st`.`template_name` AS `template_name`,count(`es`.`assignment_id`) AS `assigned_employees`,count(case when `es`.`status` = 'confirmed' then 1 end) AS `confirmed_employees`,count(case when `sa`.`status` = 'present' then 1 end) AS `present_employees`,count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_employees` from (((`shifts` `s` left join `shift_templates` `st` on(`s`.`template_id` = `st`.`template_id`)) left join `employee_shifts` `es` on(`s`.`shift_id` = `es`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) group by `s`.`shift_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-20 10:20:03
