<?php
/**
 * إصلاح أسعار الأجهزة - PlayGood
 * التأكد من وجود أسعار صحيحة لجميع الأجهزة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>💰 إصلاح أسعار الأجهزة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص الأجهزة الحالية وأسعارها
    echo "<h2>1. فحص الأجهزة الحالية وأسعارها</h2>";
    
    try {
        $devices = $pdo->query("
            SELECT device_id, device_name, device_type, status, single_rate, multi_rate
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الأجهزة: <strong>" . count($devices) . "</strong></p>";
        
        if (count($devices) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "<th style='padding: 8px;'>السعر الفردي</th>";
            echo "<th style='padding: 8px;'>السعر الجماعي</th>";
            echo "<th style='padding: 8px;'>الإجراء</th>";
            echo "</tr>";
            
            $devices_to_fix = [];
            
            foreach ($devices as $device) {
                $needs_fix = false;
                $single_rate = $device['single_rate'] ?? 0;
                $multi_rate = $device['multi_rate'] ?? 0;
                
                if ($single_rate <= 0 || $multi_rate <= 0) {
                    $needs_fix = true;
                    $devices_to_fix[] = $device;
                }
                
                echo "<tr" . ($needs_fix ? " style='background: #ffe6e6;'" : "") . ">";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
                echo "<td style='padding: 8px;'>" . $device['status'] . "</td>";
                echo "<td style='padding: 8px; " . ($single_rate <= 0 ? "color: red;" : "color: green;") . "'>" . $single_rate . " ج.م</td>";
                echo "<td style='padding: 8px; " . ($multi_rate <= 0 ? "color: red;" : "color: green;") . "'>" . $multi_rate . " ج.م</td>";
                echo "<td style='padding: 8px;'>" . ($needs_fix ? "❌ يحتاج إصلاح" : "✅ صحيح") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>الأجهزة التي تحتاج إصلاح:</strong> " . count($devices_to_fix) . "</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أجهزة في النظام</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 2. إصلاح الأسعار المفقودة أو الخاطئة
    echo "<h2>2. إصلاح الأسعار المفقودة أو الخاطئة</h2>";
    
    try {
        // أسعار افتراضية حسب نوع الجهاز
        $default_rates = [
            'PS5' => ['single' => 25.00, 'multi' => 40.00],
            'PS4' => ['single' => 20.00, 'multi' => 35.00],
            'PC' => ['single' => 30.00, 'multi' => 50.00],
            'XBOX' => ['single' => 25.00, 'multi' => 40.00],
            'SWITCH' => ['single' => 20.00, 'multi' => 30.00],
            'VR' => ['single' => 35.00, 'multi' => 55.00]
        ];
        
        $fixed_count = 0;
        
        // البحث عن الأجهزة التي تحتاج إصلاح
        $devices_to_fix = $pdo->query("
            SELECT device_id, device_name, device_type, single_rate, multi_rate
            FROM devices 
            WHERE client_id = 1 
            AND (single_rate IS NULL OR single_rate <= 0 OR multi_rate IS NULL OR multi_rate <= 0)
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($devices_to_fix) > 0) {
            echo "<p style='color: orange;'>⚠️ تم العثور على " . count($devices_to_fix) . " جهاز يحتاج إصلاح الأسعار</p>";
            
            $update_stmt = $pdo->prepare("
                UPDATE devices 
                SET single_rate = ?, multi_rate = ? 
                WHERE device_id = ?
            ");
            
            foreach ($devices_to_fix as $device) {
                $device_type = strtoupper($device['device_type']);
                
                // تحديد الأسعار الافتراضية
                if (isset($default_rates[$device_type])) {
                    $single_rate = $default_rates[$device_type]['single'];
                    $multi_rate = $default_rates[$device_type]['multi'];
                } else {
                    // أسعار افتراضية عامة
                    $single_rate = 25.00;
                    $multi_rate = 40.00;
                }
                
                try {
                    $update_stmt->execute([$single_rate, $multi_rate, $device['device_id']]);
                    $fixed_count++;
                    
                    echo "<p style='color: green;'>✅ تم إصلاح الجهاز: " . htmlspecialchars($device['device_name']) . 
                         " → فردي: $single_rate ج.م، جماعي: $multi_rate ج.م</p>";
                         
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إصلاح الجهاز " . htmlspecialchars($device['device_name']) . ": " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p style='color: blue;'>📊 تم إصلاح <strong>$fixed_count</strong> جهاز من أصل " . count($devices_to_fix) . "</p>";
            
        } else {
            echo "<p style='color: green;'>✅ جميع الأجهزة لديها أسعار صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح الأسعار: " . $e->getMessage() . "</p>";
    }
    
    // 3. إضافة أجهزة تجريبية بأسعار صحيحة
    echo "<h2>3. إضافة أجهزة تجريبية (إذا لزم الأمر)</h2>";
    
    try {
        $device_count = $pdo->query("SELECT COUNT(*) FROM devices WHERE client_id = 1")->fetchColumn();
        
        if ($device_count < 3) {
            echo "<p style='color: orange;'>⚠️ عدد الأجهزة قليل ($device_count)، سيتم إضافة أجهزة تجريبية</p>";
            
            $sample_devices = [
                ['PlayStation 5 - الرئيسي', 'PS5', 'available', 25.00, 40.00],
                ['PlayStation 4 - الثانوي', 'PS4', 'available', 20.00, 35.00],
                ['Gaming PC - المتقدم', 'PC', 'available', 30.00, 50.00],
                ['Xbox Series X', 'XBOX', 'available', 25.00, 40.00],
                ['Nintendo Switch', 'SWITCH', 'available', 20.00, 30.00]
            ];
            
            $insert_stmt = $pdo->prepare("
                INSERT INTO devices (device_name, device_type, status, single_rate, multi_rate, client_id) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $added_count = 0;
            foreach ($sample_devices as $device) {
                try {
                    $insert_stmt->execute([
                        $device[0], $device[1], $device[2], 
                        $device[3], $device[4], 1
                    ]);
                    $added_count++;
                    echo "<p style='color: green;'>✅ تم إضافة: {$device[0]} (فردي: {$device[3]} ج.م، جماعي: {$device[4]} ج.م)</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة {$device[0]}: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p style='color: blue;'>📊 تم إضافة <strong>$added_count</strong> جهاز جديد</p>";
            
        } else {
            echo "<p style='color: green;'>✅ عدد الأجهزة كافي ($device_count أجهزة)</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 4. فحص نهائي للأسعار
    echo "<h2>4. فحص نهائي للأسعار</h2>";
    
    try {
        $final_devices = $pdo->query("
            SELECT device_id, device_name, device_type, single_rate, multi_rate
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #d4edda;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>اسم الجهاز</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>السعر الفردي</th>";
        echo "<th style='padding: 8px;'>السعر الجماعي</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        $all_good = true;
        
        foreach ($final_devices as $device) {
            $single_rate = $device['single_rate'] ?? 0;
            $multi_rate = $device['multi_rate'] ?? 0;
            $is_good = $single_rate > 0 && $multi_rate > 0;
            
            if (!$is_good) $all_good = false;
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
            echo "<td style='padding: 8px; color: " . ($single_rate > 0 ? "green" : "red") . ";'>" . $single_rate . " ج.م</td>";
            echo "<td style='padding: 8px; color: " . ($multi_rate > 0 ? "green" : "red") . ";'>" . $multi_rate . " ج.م</td>";
            echo "<td style='padding: 8px;'>" . ($is_good ? "✅ ممتاز" : "❌ مشكلة") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if ($all_good) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>🎉 تم الإصلاح بنجاح!</h4>";
            echo "<p>جميع الأجهزة لديها أسعار صحيحة الآن. يجب أن تعمل التكلفة في صفحة الجلسات بشكل صحيح.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>⚠️ لا تزال هناك مشاكل</h4>";
            echo "<p>بعض الأجهزة لا تزال بدون أسعار صحيحة. يرجى المراجعة اليدوية.</p>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الفحص النهائي: " . $e->getMessage() . "</p>";
    }
    
    // 5. اختبار حساب التكلفة
    echo "<h2>5. اختبار حساب التكلفة</h2>";
    
    try {
        // محاكاة حساب التكلفة
        $test_duration = 90; // 90 دقيقة
        $test_device = $pdo->query("
            SELECT device_id, device_name, single_rate 
            FROM devices 
            WHERE client_id = 1 AND single_rate > 0 
            LIMIT 1
        ")->fetch(PDO::FETCH_ASSOC);
        
        if ($test_device) {
            $hourly_rate = $test_device['single_rate'];
            $calculated_cost = ceil($test_duration / 60) * $hourly_rate;
            
            echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h5>🧮 مثال على حساب التكلفة:</h5>";
            echo "<p><strong>الجهاز:</strong> " . htmlspecialchars($test_device['device_name']) . "</p>";
            echo "<p><strong>المدة:</strong> $test_duration دقيقة</p>";
            echo "<p><strong>السعر/ساعة:</strong> $hourly_rate ج.م</p>";
            echo "<p><strong>الحساب:</strong> ceil($test_duration ÷ 60) × $hourly_rate = ceil(" . ($test_duration/60) . ") × $hourly_rate = " . ceil($test_duration/60) . " × $hourly_rate</p>";
            echo "<p><strong>النتيجة:</strong> <span style='background: yellow; padding: 5px; font-weight: bold;'>$calculated_cost ج.م</span></p>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار الحساب: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background: #cce5ff; border: 1px solid #99d6ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بعد تشغيل هذا الإصلاح:</h4>";
echo "<ol>";
echo "<li><strong>اختبر صفحة الجلسات:</strong> يجب أن تظهر التكلفة بالأرقام</li>";
echo "<li><strong>شغل ملف التشخيص المتقدم:</strong> للتأكد من صحة الحسابات</li>";
echo "<li><strong>إذا استمرت المشكلة:</strong> تحقق من JavaScript أو CSS</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='debug_cost_issue.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>التشخيص المتقدم</a>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>
