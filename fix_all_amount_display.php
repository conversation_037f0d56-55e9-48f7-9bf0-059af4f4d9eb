<?php
/**
 * إصلاح شامل لمشكلة عرض المبالغ في جميع أنحاء النظام
 * يطبق الحل على جميع الملفات التي تعرض مبالغ مالية
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<h1>🔧 إصلاح شامل لمشكلة عرض المبالغ</h1>";

// 1. إضافة ملف الدوال المساعدة لجميع الملفات
echo "<h2>1. تطبيق ملف الدوال المساعدة</h2>";

$files_to_fix = [
    'client/finances.php',
    'client/invoices.php', 
    'client/invoice.php',
    'client/dashboard.php',
    'client/sessions.php',
    'client/reports.php'
];

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // التحقق من وجود require للدوال المساعدة
        if (strpos($content, "require_once '../includes/format_helpers.php'") === false && 
            strpos($content, "require_once 'includes/format_helpers.php'") === false) {
            
            // إضافة require للدوال المساعدة
            $helper_path = (strpos($file, 'client/') === 0) ? '../includes/format_helpers.php' : 'includes/format_helpers.php';
            
            // البحث عن أول require_once وإضافة الدوال المساعدة بعدها
            $pattern = '/(require_once\s+[\'"][^\'"]+(database|auth)[\'"];)/';
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, "$1\nrequire_once '$helper_path';", $content, 1);
                
                file_put_contents($file, $content);
                echo "<p style='color: green;'>✅ تم إضافة الدوال المساعدة إلى $file</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ لم يتم العثور على مكان مناسب لإضافة الدوال في $file</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ الدوال المساعدة موجودة بالفعل في $file</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ الملف $file غير موجود</p>";
    }
}

// 2. إصلاح استخدام number_format في الملفات
echo "<h2>2. إصلاح استخدام number_format</h2>";

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $original_content = $content;
        
        // استبدال number_format بـ formatAmount
        $patterns = [
            // استبدال number_format($var, 2) بـ formatAmount($var)
            '/number_format\s*\(\s*([^,\)]+)\s*,\s*2\s*\)/' => 'formatAmount($1)',
            
            // استبدال number_format($var) بـ formatAmount($var)
            '/number_format\s*\(\s*([^,\)]+)\s*\)/' => 'formatAmount($1)',
            
            // إصلاح الحالات المعقدة
            '/number_format\s*\(\s*floatval\s*\(\s*([^)]+)\s*\)\s*,\s*2\s*\)/' => 'formatAmount($1)',
        ];
        
        $changes_made = 0;
        foreach ($patterns as $pattern => $replacement) {
            $new_content = preg_replace($pattern, $replacement, $content);
            if ($new_content !== $content) {
                $changes_made++;
                $content = $new_content;
            }
        }
        
        if ($content !== $original_content) {
            file_put_contents($file, $content);
            echo "<p style='color: green;'>✅ تم إصلاح $changes_made استخدام لـ number_format في $file</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ لا توجد تغييرات مطلوبة في $file</p>";
        }
    }
}

// 3. إصلاح ملف JavaScript الرئيسي
echo "<h2>3. تحسين ملف JavaScript</h2>";

$js_file = 'assets/js/main.js';
if (file_exists($js_file)) {
    $js_content = file_get_contents($js_file);
    
    // إضافة دالة محسنة لتنسيق المبالغ
    $enhanced_format_function = '
// دالة محسنة لتنسيق المبالغ - تحل مشكلة عرض الأسماء بدل المبالغ
function formatAmountSafe(amount) {
    // التحقق من نوع البيانات
    if (typeof amount === "string") {
        // إزالة أي نص غير رقمي
        amount = amount.replace(/[^0-9.-]/g, "");
    }
    
    // تحويل إلى رقم
    const numAmount = parseFloat(amount) || 0;
    
    // تنسيق الرقم
    try {
        return new Intl.NumberFormat("ar-EG", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(numAmount);
    } catch (error) {
        return numAmount.toFixed(2);
    }
}

// دالة لإصلاح عرض المبالغ في الصفحة
function fixAmountDisplayOnPage() {
    // البحث عن عناصر المبالغ
    const selectors = [
        ".stats-number",
        ".amount-display", 
        ".session-cost",
        ".total-cost",
        "[data-amount]"
    ];
    
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            const text = element.textContent || element.innerText;
            
            // البحث عن رقم في النص
            const numberMatch = text.match(/([0-9]+\.?[0-9]*)/);
            if (numberMatch) {
                const amount = parseFloat(numberMatch[1]);
                if (!isNaN(amount) && amount > 0) {
                    element.textContent = formatAmountSafe(amount) + " ج.م";
                }
            }
        });
    });
}
';
    
    // إضافة الدالة إذا لم تكن موجودة
    if (strpos($js_content, 'formatAmountSafe') === false) {
        $js_content .= $enhanced_format_function;
        
        // إضافة استدعاء الدالة عند تحميل الصفحة
        $js_content .= '
// تشغيل إصلاح المبالغ عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function() {
    fixAmountDisplayOnPage();
    
    // إعادة تشغيل الإصلاح كل 5 ثوان للتأكد
    setInterval(fixAmountDisplayOnPage, 5000);
});
';
        
        file_put_contents($js_file, $js_content);
        echo "<p style='color: green;'>✅ تم تحسين ملف JavaScript</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ ملف JavaScript محسن بالفعل</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف JavaScript غير موجود</p>";
}

// 4. إنشاء ملف CSS لتحسين عرض المبالغ
echo "<h2>4. إنشاء ملف CSS محسن</h2>";

$css_content = '
/* تحسينات عرض المبالغ المالية */
.amount-display, .stats-number, .session-cost, .total-cost {
    font-family: "Arial", sans-serif;
    font-weight: bold;
    direction: ltr;
    text-align: center;
}

.amount-display::before {
    content: "";
    display: inline-block;
    width: 0;
}

/* إخفاء النصوص غير المرغوب فيها */
.amount-display:not([data-amount]) {
    position: relative;
}

.amount-display:not([data-amount])::after {
    content: attr(data-fallback);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق خاص للمبالغ الكبيرة */
.stats-number {
    font-size: 1.8rem;
    line-height: 1.2;
}

/* تنسيق المبالغ في الجداول */
.table .amount-display {
    min-width: 80px;
    text-align: right;
}
';

$css_file = 'assets/css/amount-fixes.css';
if (!file_exists(dirname($css_file))) {
    mkdir(dirname($css_file), 0755, true);
}

file_put_contents($css_file, $css_content);
echo "<p style='color: green;'>✅ تم إنشاء ملف CSS محسن: $css_file</p>";

// 5. تحديث ملف header.php لتضمين CSS الجديد
echo "<h2>5. تحديث ملف header.php</h2>";

$header_files = ['client/includes/header.php', 'includes/header.php'];
foreach ($header_files as $header_file) {
    if (file_exists($header_file)) {
        $header_content = file_get_contents($header_file);
        
        if (strpos($header_content, 'amount-fixes.css') === false) {
            // إضافة CSS قبل إغلاق head
            $css_link = '<link rel="stylesheet" href="../assets/css/amount-fixes.css">';
            if (strpos($header_file, 'client/') === false) {
                $css_link = '<link rel="stylesheet" href="assets/css/amount-fixes.css">';
            }
            
            $header_content = str_replace('</head>', "    $css_link\n</head>", $header_content);
            file_put_contents($header_file, $header_content);
            echo "<p style='color: green;'>✅ تم تحديث $header_file</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ $header_file محدث بالفعل</p>";
        }
        break; // استخدام أول ملف موجود فقط
    }
}

echo "<h2>✅ تم الانتهاء من الإصلاح الشامل</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📋 ملخص الإصلاحات:</h3>";
echo "<ul>";
echo "<li>✅ تم إضافة ملف الدوال المساعدة لجميع الصفحات</li>";
echo "<li>✅ تم استبدال جميع استخدامات number_format بـ formatAmount</li>";
echo "<li>✅ تم تحسين ملف JavaScript لإصلاح عرض المبالغ</li>";
echo "<li>✅ تم إنشاء ملف CSS محسن للمبالغ</li>";
echo "<li>✅ تم تحديث ملف header.php</li>";
echo "</ul>";
echo "<p><strong>الآن يجب أن تعمل جميع المبالغ بشكل صحيح في النظام!</strong></p>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/finances.php' class='btn btn-primary' style='margin: 5px; padding: 10px 20px; text-decoration: none; background: #007bff; color: white; border-radius: 5px;'>اختبار صفحة الماليات</a>";
echo "<a href='client/dashboard.php' class='btn btn-success' style='margin: 5px; padding: 10px 20px; text-decoration: none; background: #28a745; color: white; border-radius: 5px;'>العودة للوحة التحكم</a>";
echo "</div>";
?>
