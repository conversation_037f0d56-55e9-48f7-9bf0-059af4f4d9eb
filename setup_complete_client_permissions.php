<?php
/**
 * إعداد نظام صلاحيات العملاء الشامل - جميع صفحات الموقع
 * يضيف جميع الصفحات الموجودة فعلياً في مجلد client إلى نظام الصلاحيات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام صلاحيات العملاء الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .step { margin: 1rem 0; padding: 1rem; border-radius: 10px; }
        .step.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .step.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .step.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .step.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .progress { height: 25px; border-radius: 15px; }
        .table { background: white; border-radius: 10px; overflow: hidden; }
        .badge { font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-shield-alt me-2"></i>إعداد نظام صلاحيات العملاء الشامل</h3>
                        <p class="mb-0">إضافة جميع صفحات الموقع لنظام التحكم في الصلاحيات</p>
                    </div>
                    <div class="card-body">

<?php
try {
    echo "<div class='step info'>
            <h5><i class='fas fa-info-circle me-2'></i>بدء عملية الإعداد الشامل</h5>
            <p>جاري إضافة جميع صفحات الموقع الموجودة فعلياً إلى نظام الصلاحيات...</p>
          </div>";

    // 1. التحقق من وجود الجداول المطلوبة
    echo "<div class='step info'>
            <h6><i class='fas fa-database me-2'></i>الخطوة 1: التحقق من قاعدة البيانات</h6>";
    
    $tables_check = [
        'client_pages' => "SHOW TABLES LIKE 'client_pages'",
        'client_page_permissions' => "SHOW TABLES LIKE 'client_page_permissions'",
        'clients' => "SHOW TABLES LIKE 'clients'"
    ];
    
    foreach ($tables_check as $table => $query) {
        $result = $pdo->query($query);
        if ($result->rowCount() > 0) {
            echo "<div class='alert alert-success py-2 mb-2'>✅ جدول $table موجود</div>";
        } else {
            echo "<div class='alert alert-danger py-2 mb-2'>❌ جدول $table غير موجود</div>";
            throw new Exception("جدول $table مطلوب لتشغيل النظام");
        }
    }
    echo "</div>";

    // 2. إضافة جميع الصفحات
    echo "<div class='step info'>
            <h6><i class='fas fa-plus-circle me-2'></i>الخطوة 2: إضافة جميع صفحات الموقع</h6>";

    // قائمة شاملة بجميع الصفحات الموجودة فعلياً
    $all_pages = [
        // الصفحات الأساسية
        ['dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', 1],
        ['profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', 1],
        
        // إدارة الأجهزة والغرف
        ['devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', 1],
        ['rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف', 0],
        
        // إدارة الجلسات
        ['sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', 1],
        ['check_active_sessions', 'فحص الجلسات النشطة', 'check_active_sessions.php', 'fas fa-search', 'sessions', 'فحص حالة الجلسات النشطة', 0],
        
        // إدارة العملاء
        ['customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', 1],
        ['customer_details', 'تفاصيل العميل', 'customer_details.php', 'fas fa-user-circle', 'customers', 'عرض تفاصيل عميل محدد', 0],
        ['edit_customer', 'تعديل العميل', 'edit_customer.php', 'fas fa-user-edit', 'customers', 'تعديل بيانات العميل', 0],
        ['delete_customer', 'حذف العميل', 'delete_customer.php', 'fas fa-user-times', 'customers', 'حذف عميل من النظام', 0],
        
        // الكافتيريا والمنتجات
        ['cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا والمشروبات', 1],
        
        // إدارة الأوردرات والطلبات
        ['orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات والأوردرات المستقلة', 0],
        
        // إدارة الموظفين
        ['employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة بيانات الموظفين', 0],
        ['employee_permissions', 'صلاحيات الموظفين', 'employee_permissions.php', 'fas fa-user-shield', 'permissions', 'إدارة صلاحيات الموظفين', 0],
        ['employee-login', 'تسجيل دخول الموظف', 'employee-login.php', 'fas fa-sign-in-alt', 'employees', 'صفحة تسجيل دخول الموظفين', 0],
        
        // الحضور والانصراف
        ['attendance', 'الحضور والانصراف', 'attendance.php', 'fas fa-user-check', 'attendance', 'تسجيل حضور وانصراف الموظفين', 0],
        ['quick_attendance', 'الحضور السريع', 'quick_attendance.php', 'fas fa-clock', 'attendance', 'تسجيل حضور سريع للموظفين', 0],
        
        // إدارة الورديات
        ['shifts', 'إدارة الورديات', 'shifts.php', 'fas fa-calendar-alt', 'shifts', 'تنظيم ورديات العمل', 0],
        ['shift_reports', 'تقارير الورديات', 'shift_reports.php', 'fas fa-calendar-check', 'shifts', 'تقارير مفصلة عن الورديات', 0],
        
        // الإدارة المالية
        ['finances', 'الإدارة المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة الأمور المالية العامة', 0],
        
        // التقارير والإحصائيات
        ['reports', 'التقارير والإحصائيات', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير شاملة عن أداء المحل', 1],
        
        // الفواتير والمحاسبة
        ['invoices', 'إدارة الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إنشاء وإدارة الفواتير', 1],
        ['invoice', 'عرض الفاتورة', 'invoice.php', 'fas fa-file-invoice-dollar', 'invoices', 'عرض فاتورة محددة', 0],
        ['invoice_settings', 'إعدادات الفواتير', 'invoice_settings.php', 'fas fa-cog', 'invoices', 'تخصيص شكل ومحتوى الفواتير', 0],
        
        // إدارة المخزون
        ['inventory', 'إدارة المخزون', 'inventory.php', 'fas fa-boxes', 'inventory', 'إدارة مخزون المنتجات', 0],
        
        // الحجوزات والمواعيد
        ['reservations', 'الحجوزات', 'reservations.php', 'fas fa-calendar-check', 'reservations', 'إدارة حجوزات العملاء', 0],
        
        // الإعدادات والتخصيص
        ['settings', 'الإعدادات العامة', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والتخصيص', 1],
        
        // النسخ الاحتياطي
        ['backup_handler', 'النسخ الاحتياطي', 'backup_handler.php', 'fas fa-database', 'backup', 'إدارة النسخ الاحتياطية', 0]
    ];

    $insert_stmt = $pdo->prepare("
        INSERT IGNORE INTO client_pages (page_name, page_label, page_url, page_icon, category, description, is_default) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    $added_count = 0;
    $existing_count = 0;

    foreach ($all_pages as $page) {
        $result = $insert_stmt->execute($page);
        if ($insert_stmt->rowCount() > 0) {
            $added_count++;
            echo "<div class='alert alert-success py-1 mb-1'>✅ تم إضافة: {$page[1]} ({$page[0]})</div>";
        } else {
            $existing_count++;
        }
    }

    echo "<div class='alert alert-info mt-3'>
            <strong>📊 ملخص العملية:</strong><br>
            • تم إضافة $added_count صفحة جديدة<br>
            • $existing_count صفحة كانت موجودة مسبقاً<br>
            • إجمالي الصفحات المعالجة: " . count($all_pages) . "
          </div>";
    echo "</div>";

    // 3. عرض الإحصائيات
    echo "<div class='step success'>
            <h6><i class='fas fa-chart-pie me-2'></i>الخطوة 3: إحصائيات النظام</h6>";

    $stats_query = "
        SELECT 
            category,
            COUNT(*) as total_pages,
            SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_pages,
            SUM(CASE WHEN is_default = 0 THEN 1 ELSE 0 END) as optional_pages
        FROM client_pages 
        WHERE is_active = 1 
        GROUP BY category 
        ORDER BY total_pages DESC
    ";
    
    $stats = $pdo->query($stats_query)->fetchAll();
    
    echo "<div class='table-responsive'>
            <table class='table table-striped'>
                <thead class='table-dark'>
                    <tr>
                        <th>الفئة</th>
                        <th>إجمالي الصفحات</th>
                        <th>الصفحات الافتراضية</th>
                        <th>الصفحات الاختيارية</th>
                    </tr>
                </thead>
                <tbody>";
    
    $total_pages = 0;
    $total_default = 0;
    $total_optional = 0;
    
    foreach ($stats as $stat) {
        $total_pages += $stat['total_pages'];
        $total_default += $stat['default_pages'];
        $total_optional += $stat['optional_pages'];
        
        echo "<tr>
                <td><strong>{$stat['category']}</strong></td>
                <td><span class='badge bg-primary'>{$stat['total_pages']}</span></td>
                <td><span class='badge bg-success'>{$stat['default_pages']}</span></td>
                <td><span class='badge bg-warning'>{$stat['optional_pages']}</span></td>
              </tr>";
    }
    
    echo "<tr class='table-info'>
            <td><strong>الإجمالي</strong></td>
            <td><span class='badge bg-primary'>$total_pages</span></td>
            <td><span class='badge bg-success'>$total_default</span></td>
            <td><span class='badge bg-warning'>$total_optional</span></td>
          </tr>";
    
    echo "</tbody></table></div></div>";

    // 4. منح الصلاحيات الافتراضية للعملاء الموجودين
    echo "<div class='step info'>
            <h6><i class='fas fa-users-cog me-2'></i>الخطوة 4: منح الصلاحيات الافتراضية للعملاء الموجودين</h6>";

    $grant_permissions_query = "
        INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
        SELECT c.client_id, cp.page_id, TRUE
        FROM clients c
        CROSS JOIN client_pages cp
        WHERE cp.is_default = TRUE AND cp.is_active = TRUE
    ";
    
    $granted_permissions = $pdo->exec($grant_permissions_query);
    echo "<div class='alert alert-success'>✅ تم منح $granted_permissions صلاحية افتراضية للعملاء الموجودين</div>";
    echo "</div>";

    // 5. رسالة النجاح النهائية
    echo "<div class='step success text-center'>
            <h4><i class='fas fa-check-circle me-2'></i>تم إعداد النظام بنجاح!</h4>
            <p class='mb-3'>تم إضافة جميع صفحات الموقع إلى نظام صلاحيات العملاء</p>
            <div class='d-grid gap-2 d-md-flex justify-content-md-center'>
                <a href='admin/client_permissions.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-shield-alt me-2'></i>إدارة صلاحيات العملاء
                </a>
                <a href='admin/dashboard.php' class='btn btn-secondary btn-lg'>
                    <i class='fas fa-home me-2'></i>العودة للوحة التحكم
                </a>
            </div>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ في العملية</h5>
            <p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p><strong>الحل المقترح:</strong> تأكد من وجود قاعدة البيانات وتشغيل ملف إعداد النظام الأساسي أولاً</p>
          </div>";
}
?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
