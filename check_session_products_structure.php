<?php
// فحص بنية جدول session_products
session_start();
require_once 'config/database.php';

echo "<h1>فحص بنية جدول session_products</h1>";

try {
    // فحص بنية الجدول
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>أعمدة الجدول:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_price = false;
    $has_unit_price = false;
    $has_total = false;
    $has_total_price = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        // تحديد الأعمدة الموجودة
        if ($column['Field'] === 'price') $has_price = true;
        if ($column['Field'] === 'unit_price') $has_unit_price = true;
        if ($column['Field'] === 'total') $has_total = true;
        if ($column['Field'] === 'total_price') $has_total_price = true;
    }
    echo "</table>";
    
    echo "<h2>تحليل الأعمدة:</h2>";
    echo "<ul>";
    echo "<li>price: " . ($has_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>unit_price: " . ($has_unit_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>total: " . ($has_total ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>total_price: " . ($has_total_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "</ul>";
    
    // فحص البيانات الموجودة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM session_products");
    $count = $stmt->fetch()['count'];
    echo "<p>عدد السجلات في الجدول: <strong>$count</strong></p>";
    
    if ($count > 0) {
        echo "<h2>عينة من البيانات:</h2>";
        $stmt = $pdo->query("SELECT * FROM session_products LIMIT 5");
        $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        if (count($sample_data) > 0) {
            // عرض رؤوس الأعمدة
            echo "<tr>";
            foreach (array_keys($sample_data[0]) as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";
            
            // عرض البيانات
            foreach ($sample_data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
        }
        echo "</table>";
    }
    
    // اقتراح الحل
    echo "<h2>الحل المقترح:</h2>";
    if ($has_unit_price && $has_total_price) {
        echo "<p style='color: green;'>✅ الجدول يستخدم unit_price و total_price - يجب تحديث get_session_products.php</p>";
    } elseif ($has_price && $has_total) {
        echo "<p style='color: blue;'>ℹ️ الجدول يستخدم price و total - الكود الحالي صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ بنية الجدول غير متسقة - يحتاج إصلاح</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
