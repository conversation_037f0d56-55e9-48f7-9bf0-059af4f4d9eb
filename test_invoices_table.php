<?php
/**
 * اختبار جدول الفواتير
 * للتحقق من وجود جدول invoices ومعالجة المشاكل
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<h1>اختبار جدول الفواتير - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    echo "<h2>1. فحص وجود جدول invoices</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'invoices'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول invoices غير موجود</p>";
        
        echo "<h3>إنشاء جدول invoices:</h3>";
        
        $create_invoices_sql = "
            CREATE TABLE invoices (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                invoice_number VARCHAR(50) NOT NULL UNIQUE,
                time_cost DECIMAL(10,2) DEFAULT 0.00,
                products_cost DECIMAL(10,2) DEFAULT 0.00,
                total_cost DECIMAL(10,2) NOT NULL,
                payment_status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
                client_id INT NOT NULL,
                created_by INT NULL,
                updated_by INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_invoices_session (session_id),
                INDEX idx_invoices_client (client_id),
                INDEX idx_invoices_status (payment_status),
                INDEX idx_invoices_number (invoice_number)
            )
        ";
        
        try {
            $pdo->exec($create_invoices_sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول invoices بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ فشل في إنشاء جدول invoices: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ جدول invoices موجود</p>";
    }
    
    echo "<h2>2. فحص هيكل جدول invoices</h2>";
    
    try {
        $describe_stmt = $pdo->query("DESCRIBE invoices");
        $columns = $describe_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>اسم العمود</th>";
        echo "<th style='padding: 10px;'>النوع</th>";
        echo "<th style='padding: 10px;'>Null</th>";
        echo "<th style='padding: 10px;'>Key</th>";
        echo "<th style='padding: 10px;'>Default</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
            echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
            echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
            echo "<td style='padding: 10px;'>" . $column['Key'] . "</td>";
            echo "<td style='padding: 10px;'>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ فشل في فحص هيكل الجدول: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. فحص البيانات الموجودة</h2>";
    
    try {
        $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM invoices");
        $count = $count_stmt->fetch()['count'];
        
        echo "<p style='color: blue;'>ℹ️ عدد الفواتير الموجودة: $count</p>";
        
        if ($count > 0) {
            echo "<h3>عينة من الفواتير:</h3>";
            $sample_stmt = $pdo->query("SELECT * FROM invoices ORDER BY created_at DESC LIMIT 5");
            $sample_invoices = $sample_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>رقم الفاتورة</th>";
            echo "<th style='padding: 10px;'>معرف الجلسة</th>";
            echo "<th style='padding: 10px;'>المبلغ الإجمالي</th>";
            echo "<th style='padding: 10px;'>حالة الدفع</th>";
            echo "<th style='padding: 10px;'>تاريخ الإنشاء</th>";
            echo "</tr>";
            
            foreach ($sample_invoices as $invoice) {
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . $invoice['id'] . "</td>";
                echo "<td style='padding: 10px;'>" . $invoice['invoice_number'] . "</td>";
                echo "<td style='padding: 10px;'>" . $invoice['session_id'] . "</td>";
                echo "<td style='padding: 10px;'>" . number_format($invoice['total_cost'], 2) . " ج.م</td>";
                echo "<td style='padding: 10px;'>" . $invoice['payment_status'] . "</td>";
                echo "<td style='padding: 10px;'>" . $invoice['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ فشل في فحص البيانات: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. إنشاء فاتورة تجريبية</h2>";
    
    if (isset($_GET['create_test_invoice'])) {
        try {
            // البحث عن جلسة مكتملة
            $session_stmt = $pdo->query("
                SELECT s.*, d.client_id 
                FROM sessions s 
                JOIN devices d ON s.device_id = d.device_id 
                WHERE s.status = 'completed' 
                ORDER BY s.end_time DESC 
                LIMIT 1
            ");
            $session = $session_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session) {
                $invoice_number = date('Ymd') . str_pad($session['session_id'], 4, '0', STR_PAD_LEFT);
                
                $insert_stmt = $pdo->prepare("
                    INSERT INTO invoices (
                        session_id, 
                        invoice_number, 
                        time_cost, 
                        products_cost, 
                        total_cost, 
                        payment_status, 
                        client_id
                    ) VALUES (?, ?, 100.00, 20.00, 120.00, 'pending', ?)
                ");
                
                $insert_stmt->execute([
                    $session['session_id'],
                    $invoice_number,
                    $session['client_id']
                ]);
                
                echo "<p style='color: green;'>✅ تم إنشاء فاتورة تجريبية برقم: $invoice_number</p>";
                echo "<p><a href='?' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث الصفحة</a></p>";
                
            } else {
                echo "<p style='color: orange;'>⚠️ لا توجد جلسات مكتملة لإنشاء فاتورة تجريبية</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ فشل في إنشاء فاتورة تجريبية: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p><a href='?create_test_invoice=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء فاتورة تجريبية</a></p>";
    }
    
    echo "<h2>5. اختبار صفحة الفواتير</h2>";
    
    try {
        // محاولة تشغيل نفس الاستعلام المستخدم في invoices.php
        $test_query = $pdo->prepare("
            SELECT 
                i.*,
                s.session_id,
                s.start_time,
                s.end_time,
                d.device_name,
                d.device_type,
                r.room_name,
                c.name as customer_name,
                c.phone as customer_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY i.created_at DESC
            LIMIT 5
        ");
        
        $test_query->execute();
        $test_invoices = $test_query->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✅ استعلام صفحة الفواتير يعمل بنجاح</p>";
        echo "<p>عدد الفواتير المسترجعة: " . count($test_invoices) . "</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ فشل في اختبار استعلام الفواتير: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. روابط مفيدة</h2>";
    echo "<p><a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a></p>";
    echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>سكريپت الإصلاح الشامل</a></p>";
    echo "<p><a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ عام</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getFile() . " في السطر " . $e->getLine() . "</p>";
}

echo "</div>";
?>
