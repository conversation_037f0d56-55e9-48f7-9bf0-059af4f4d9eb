<?php
session_start();
require_once 'includes/trial-auth.php';

// إعادة توجيه إذا كان مسجل دخول بالفعل
if (isTrialLoggedIn() && isTrialSessionValid()) {
    header('Location: trial-dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $business_name = trim($_POST['business_name'] ?? '');
    $owner_name = trim($_POST['owner_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // التحقق من البيانات
    if (empty($business_name) || empty($owner_name) || empty($email) || empty($phone) || empty($password)) {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } else {
        $result = trialRegister($business_name, $owner_name, $email, $phone, $password);
        
        if ($result['success']) {
            header('Location: trial-dashboard.php');
            exit;
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل التجريبي - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .trial-register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }

        .trial-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .trial-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .trial-header p {
            margin: 0;
            opacity: 0.9;
        }

        .trial-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .trial-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-trial {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-trial:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .trial-features {
            background: #f8f9fa;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 12px;
        }

        .trial-features h6 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .feature-item i {
            color: #48bb78;
            margin-left: 0.5rem;
            width: 16px;
        }

        .trial-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #718096;
            font-size: 0.9rem;
        }

        .trial-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .trial-footer a:hover {
            text-decoration: underline;
        }

        .trial-timer {
            background: var(--success-gradient);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
        }

        @media (max-width: 576px) {
            .trial-register-container {
                margin: 10px;
            }
            
            .trial-header {
                padding: 1.5rem;
            }
            
            .trial-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="trial-register-container">
        <div class="trial-header">
            <div class="trial-badge">
                <i class="fas fa-gift me-2"></i>
                تجربة مجانية لمدة 3 ساعات
            </div>
            <h1>
                <i class="fas fa-gamepad me-2"></i>
                PlayGood
            </h1>
            <p>ابدأ تجربتك المجانية الآن</p>
        </div>

        <div class="trial-timer">
            <i class="fas fa-clock me-2"></i>
            مدة التجربة: 3 ساعات كاملة مع جميع المميزات
        </div>

        <form method="POST" class="trial-form">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="form-group">
                <label for="business_name" class="form-label">اسم المحل/المركز</label>
                <input type="text" class="form-control" id="business_name" name="business_name" 
                       value="<?php echo htmlspecialchars($_POST['business_name'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="owner_name" class="form-label">اسم المالك</label>
                <input type="text" class="form-control" id="owner_name" name="owner_name" 
                       value="<?php echo htmlspecialchars($_POST['owner_name'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="phone" class="form-label">رقم الهاتف</label>
                <input type="tel" class="form-control" id="phone" name="phone" 
                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       minlength="6" required>
            </div>

            <div class="form-group">
                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                       minlength="6" required>
            </div>

            <div class="trial-features">
                <h6>ما ستحصل عليه في التجربة المجانية:</h6>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>إدارة كاملة للأجهزة والعملاء</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>تتبع الجلسات والحجوزات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>إدارة الكافتيريا والمنتجات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>تقارير مالية أساسية</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>واجهة عربية سهلة الاستخدام</span>
                </div>
            </div>

            <button type="submit" class="btn-trial">
                <i class="fas fa-rocket me-2"></i>
                ابدأ التجربة المجانية الآن
            </button>
        </form>

        <div class="trial-footer">
            لديك حساب تجريبي بالفعل؟ 
            <a href="trial-login.php">تسجيل الدخول</a>
            <br><br>
            <a href="index.php">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
