<?php
require_once 'config/database.php';

echo "<h2>تحديث قاعدة البيانات لدعم الأوردرات المستقلة</h2>";

try {
    echo "<p>جاري تحديث قاعدة البيانات...</p>";
    
    // إنشاء جدول order_items
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_order_items_order_id (order_id),
            INDEX idx_order_items_product_id (product_id),
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ تم إنشاء جدول order_items</p>";
    
    // تحديث جدول orders لإضافة الأعمدة الجديدة
    try {
        $pdo->exec("ALTER TABLE orders ADD COLUMN created_by INT DEFAULT NULL");
        echo "<p style='color: green;'>✓ تم إضافة عمود created_by</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>- عمود created_by موجود مسبقاً</p>";
        } else {
            throw $e;
        }
    }
    
    try {
        $pdo->exec("ALTER TABLE orders ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00");
        echo "<p style='color: green;'>✓ تم إضافة عمود discount_amount</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>- عمود discount_amount موجود مسبقاً</p>";
        } else {
            throw $e;
        }
    }
    
    try {
        $pdo->exec("ALTER TABLE orders ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00");
        echo "<p style='color: green;'>✓ تم إضافة عمود tax_amount</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>- عمود tax_amount موجود مسبقاً</p>";
        } else {
            throw $e;
        }
    }
    
    // التحقق من وجود الجداول والأعمدة
    echo "<h3>التحقق من البنية:</h3>";
    
    // فحص جدول orders
    $stmt = $pdo->query("DESCRIBE orders");
    $orders_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>أعمدة جدول orders:</strong> " . implode(', ', $orders_columns) . "</p>";
    
    // فحص جدول order_items
    $stmt = $pdo->query("DESCRIBE order_items");
    $order_items_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>أعمدة جدول order_items:</strong> " . implode(', ', $order_items_columns) . "</p>";
    
    // إضافة بيانات تجريبية
    echo "<h3>إضافة بيانات تجريبية:</h3>";
    
    // التحقق من وجود منتجات في الكافتيريا
    $stmt = $pdo->query("SELECT COUNT(*) FROM cafeteria_items WHERE client_id = 1");
    $products_count = $stmt->fetchColumn();
    
    if ($products_count > 0) {
        // إنشاء أوردر تجريبي
        $order_number = 'ORD-' . date('Ymd') . '-TEST';
        
        $stmt = $pdo->prepare("
            INSERT INTO orders (client_id, order_number, total_amount, status, payment_method, notes, created_by) 
            VALUES (1, ?, 50.00, 'pending', 'cash', 'أوردر تجريبي للاختبار', 1)
        ");
        $stmt->execute([$order_number]);
        $order_id = $pdo->lastInsertId();
        
        // إضافة منتج للأوردر التجريبي
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 1");
        $product = $stmt->fetch();
        
        if ($product) {
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price, total_price) 
                VALUES (?, ?, 2, ?, ?)
            ");
            $total_price = $product['price'] * 2;
            $stmt->execute([$order_id, $product['id'], $product['price'], $total_price]);
            
            // تحديث مجموع الأوردر
            $stmt = $pdo->prepare("UPDATE orders SET total_amount = ? WHERE id = ?");
            $stmt->execute([$total_price, $order_id]);
            
            echo "<p style='color: green;'>✓ تم إنشاء أوردر تجريبي برقم: $order_number</p>";
            echo "<p>- المنتج: {$product['name']} × 2 = {$total_price} ر.س</p>";
        }
    } else {
        echo "<p style='color: orange;'>- لا توجد منتجات في الكافتيريا لإنشاء أوردر تجريبي</p>";
    }
    
    echo "<h2 style='color: green;'>✅ تم تحديث قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='client/orders.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الأوردرات</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ أثناء التحديث:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
