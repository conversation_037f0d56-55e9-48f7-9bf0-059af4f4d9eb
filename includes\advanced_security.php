<?php
class AdvancedSecurity {
    
    private static $instance = null;
    private $blocked_patterns = [];
    private $rate_limits = [];
    
    private function __construct() {
        $this->initializeSecurityPatterns();
        $this->applySecurityHeaders();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function initializeSecurityPatterns() {
        $this->blocked_patterns = [
            'sql_injection' => [
                '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
                '/(\bOR\b|\bAND\b)\s*\d+\s*=\s*\d+/i',
                '/[\'\"]\s*;\s*\w+/i',
                '/\b(EXEC|EXECUTE|SP_|XP_)\b/i'
            ],
            'xss_injection' => [
                '/<script[^>]*>.*?<\/script>/is',
                '/javascript\s*:/i',
                '/on\w+\s*=/i',
                '/<iframe[^>]*>.*?<\/iframe>/is',
                '/eval\s*\(/i',
                '/expression\s*\(/i'
            ],
            'path_traversal' => [
                '/\.\.\//',
                '/\.\.\\\\/',
                '/\.\.\%2f/i',
                '/\.\.\%5c/i',
                '/\%2e\%2e\%2f/i'
            ],
            'command_injection' => [
                '/;\s*(ls|cat|pwd|whoami|id|uname)/i',
                '/\|\s*(ls|cat|pwd|whoami|id|uname)/i',
                '/`[^`]*`/',
                '/\$\([^)]*\)/',
                '/&&\s*\w+/',
                '/\|\|\s*\w+/'
            ]
        ];
    }
    
    private function applySecurityHeaders() {
        if (!headers_sent()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: DENY');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
            header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
            
            $csp = "default-src 'self'; " .
                   "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " .
                   "style-src 'self' 'unsafe-inline' fonts.googleapis.com; " .
                   "font-src 'self' fonts.gstatic.com; " .
                   "img-src 'self' data: blob:; " .
                   "connect-src 'self'; " .
                   "frame-ancestors 'none'; " .
                   "base-uri 'self'; " .
                   "form-action 'self'";
            
            header("Content-Security-Policy: $csp");
        }
    }
    
    public function scanRequest() {
        $request_data = array_merge($_GET, $_POST, $_COOKIE);
        $threats_detected = [];
        
        foreach ($request_data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            
            foreach ($this->blocked_patterns as $threat_type => $patterns) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $value) || preg_match($pattern, $key)) {
                        $threats_detected[] = [
                            'type' => $threat_type,
                            'pattern' => $pattern,
                            'field' => $key,
                            'value' => substr($value, 0, 100)
                        ];
                    }
                }
            }
        }
        
        if (!empty($threats_detected)) {
            $this->handleThreatDetection($threats_detected);
        }
        
        return empty($threats_detected);
    }
    
    private function handleThreatDetection($threats) {
        $ip = $this->getClientIp();
        $severity = count($threats) > 3 ? 'critical' : (count($threats) > 1 ? 'high' : 'medium');
        
        $log_message = sprintf(
            "[%s] THREAT DETECTED - IP: %s - Severity: %s - Threats: %s",
            date('Y-m-d H:i:s'),
            $ip,
            $severity,
            json_encode($threats)
        );
        
        error_log($log_message, 3, __DIR__ . '/../logs/security_threats.log');
        
        if ($severity === 'critical') {
            $this->blockIpTemporary($ip, 3600);
        }
        
        http_response_code(403);
        die('طلب مرفوض لأسباب أمنية');
    }
    
    public function checkRateLimit($action = 'general', $limit = 60, $window = 3600) {
        $ip = $this->getClientIp();
        $key = $action . '_' . $ip;
        $current_time = time();
        
        if (!isset($this->rate_limits[$key])) {
            $this->rate_limits[$key] = ['count' => 0, 'window_start' => $current_time];
        }
        
        $rate_data = &$this->rate_limits[$key];
        
        if ($current_time - $rate_data['window_start'] > $window) {
            $rate_data['count'] = 0;
            $rate_data['window_start'] = $current_time;
        }
        
        $rate_data['count']++;
        
        if ($rate_data['count'] > $limit) {
            $this->blockIpTemporary($ip, $window);
            http_response_code(429);
            die('تم تجاوز الحد المسموح من الطلبات');
        }
        
        return true;
    }
    
    private function blockIpTemporary($ip, $duration) {
        $blocked_until = date('Y-m-d H:i:s', time() + $duration);
        $block_file = __DIR__ . '/../temp/blocked_ips.json';
        
        if (!is_dir(dirname($block_file))) {
            mkdir(dirname($block_file), 0755, true);
        }
        
        $blocked_ips = [];
        if (file_exists($block_file)) {
            $blocked_ips = json_decode(file_get_contents($block_file), true) ?: [];
        }
        
        $blocked_ips[$ip] = [
            'blocked_until' => $blocked_until,
            'reason' => 'Security threat detected',
            'blocked_at' => date('Y-m-d H:i:s')
        ];
        
        file_put_contents($block_file, json_encode($blocked_ips, JSON_PRETTY_PRINT));
    }
    
    public function isIpBlocked() {
        $ip = $this->getClientIp();
        $block_file = __DIR__ . '/../temp/blocked_ips.json';
        
        if (!file_exists($block_file)) {
            return false;
        }
        
        $blocked_ips = json_decode(file_get_contents($block_file), true) ?: [];
        
        if (isset($blocked_ips[$ip])) {
            $blocked_until = strtotime($blocked_ips[$ip]['blocked_until']);
            if (time() < $blocked_until) {
                return true;
            } else {
                unset($blocked_ips[$ip]);
                file_put_contents($block_file, json_encode($blocked_ips, JSON_PRETTY_PRINT));
            }
        }
        
        return false;
    }
    
    private function getClientIp() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 
                   'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }
    
    public function validateFileUpload($file) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
        $max_size = 5 * 1024 * 1024;
        
        if ($file['size'] > $max_size) {
            return false;
        }
        
        if (!in_array($file['type'], $allowed_types)) {
            return false;
        }
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        return in_array($mime_type, $allowed_types);
    }
}

$advanced_security = AdvancedSecurity::getInstance();

if ($advanced_security->isIpBlocked()) {
    http_response_code(403);
    die('تم حظر وصولك مؤقتاً');
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    $advanced_security->checkRateLimit('post_requests', 30, 300);
}

$advanced_security->scanRequest();
