# ===== BACKUPS DIRECTORY PROTECTION =====
# Complete access denial to backup files
Require all denied

# Block all files
<Files "*">
    Require all denied
</Files>

# Block specific backup file types
<FilesMatch "\.(sql|zip|tar|gz|bak|backup|old)$">
    Require all denied
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable script execution
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Log access attempts
<IfModule mod_log_config.c>
    LogFormat "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-agent}i\"" combined
    CustomLog logs/backup_access.log combined
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
</IfModule>
