# ===== QUARANTINE DIRECTORY - COMPLETE ACCESS DENIAL =====
# Block all access to quarantined files
Require all denied

# Block all file types
<Files "*">
    Require all denied
</Files>

# Disable all script execution
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Disable directory browsing
Options -Indexes

# Additional security
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
</IfModule>

# Log access attempts to quarantine
<IfModule mod_log_config.c>
    LogFormat "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-agent}i\"" combined
    CustomLog logs/quarantine_access.log combined
</IfModule>
