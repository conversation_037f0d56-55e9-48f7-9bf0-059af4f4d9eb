<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayGood - منصة إدارة مراكز الألعاب الاحترافية</title>
    <meta name="description" content="نظام إدارة مراكز الألعاب الأكثر تطوراً في المنطقة - إدارة شاملة للأجهزة والعملاء والجلسات والماليات">
    <meta name="keywords" content="إدارة مراكز الألعاب, نظام إدارة الألعاب, PlayGood, إدارة الأجهزة, إدارة العملاء">

    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Custom Styles for Landing Page -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        /* Enhanced Navbar */
        .navbar-modern {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .navbar-modern.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .navbar-brand-modern {
            font-weight: 900;
            font-size: 1.8rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-btn-modern {
            padding: 0.6rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-decoration: none;
            margin: 0 0.3rem;
        }

        .nav-btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .nav-btn-outline {
            color: #667eea;
            border-color: #667eea;
            background: transparent;
        }

        .nav-btn-outline:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        /* Hero Section Styles */
        .hero-section-modern {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }

        .hero-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
            animation: particleFloat 20s ease-in-out infinite;
        }

        @keyframes particleFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }

        .hero-gradient-overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.9) 50%,
                rgba(102, 126, 234, 0.05) 100%);
        }

        .hero-content {
            z-index: 2;
            position: relative;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: var(--primary-gradient);
            color: white;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 2rem;
        }

        .hero-title-main {
            display: block;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .hero-title-accent {
            display: block;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .hero-title-accent::after {
            content: '';
            position: absolute;
            bottom: -10px;
            right: 0;
            width: 100px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .hero-description {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #4a5568;
            font-weight: 400;
        }

        .hero-features {
            display: grid;
            gap: 1rem;
        }

        .hero-feature-item {
            display: flex;
            align-items: center;
            font-size: 1rem;
            color: #2d3748;
            font-weight: 500;
        }

        .hero-feature-item i {
            color: #48bb78;
            margin-left: 0.75rem;
            font-size: 1.1rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-hero {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 200px;
        }

        .btn-hero-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-hero-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-hero-outline {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }

        .btn-hero-outline:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-hero-subtitle {
            font-size: 0.8rem;
            font-weight: 400;
            opacity: 0.9;
            margin-top: 0.2rem;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(102, 126, 234, 0.1);
        }

        .hero-stat-item {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 2rem;
            font-weight: 900;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }

        .hero-stat-label {
            font-size: 0.9rem;
            color: #718096;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        /* Dashboard Mockup Styles */
        .hero-visual {
            position: relative;
            z-index: 1;
        }

        .hero-dashboard-preview {
            position: relative;
            transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
            transition: transform 0.3s ease;
        }

        .hero-dashboard-preview:hover {
            transform: perspective(1000px) rotateY(-2deg) rotateX(2deg) scale(1.02);
        }

        .dashboard-mockup {
            background: white;
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
            overflow: hidden;
            position: relative;
        }

        .mockup-header {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e2e8f0;
        }

        .mockup-controls {
            display: flex;
            gap: 0.5rem;
        }

        .control-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .control-dot.red { background: #fc8181; }
        .control-dot.yellow { background: #f6e05e; }
        .control-dot.green { background: #68d391; }

        .mockup-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
        }

        .mockup-content {
            display: flex;
            height: 400px;
        }

        .mockup-sidebar {
            width: 200px;
            background: #f7fafc;
            padding: 1rem 0;
            border-left: 1px solid #e2e8f0;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #4a5568;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .sidebar-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .sidebar-item.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-right: 3px solid #667eea;
        }

        .sidebar-item i {
            margin-left: 0.75rem;
            width: 16px;
            text-align: center;
        }

        .mockup-main {
            flex: 1;
            padding: 1.5rem;
            background: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .stat-icon.blue { background: linear-gradient(135deg, #4299e1, #3182ce); }
        .stat-icon.green { background: linear-gradient(135deg, #48bb78, #38a169); }
        .stat-icon.purple { background: linear-gradient(135deg, #9f7aea, #805ad5); }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2d3748;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #718096;
            margin-top: 0.25rem;
        }

        .chart-area {
            background: #f7fafc;
            border-radius: 12px;
            padding: 1rem;
            height: 150px;
        }

        .chart-placeholder {
            height: 100%;
            display: flex;
            align-items: end;
            justify-content: center;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            gap: 8px;
            height: 100%;
        }

        .chart-bar {
            width: 20px;
            background: var(--primary-gradient);
            border-radius: 4px 4px 0 0;
            animation: chartGrow 2s ease-out;
            animation-delay: calc(var(--i, 0) * 0.1s);
        }

        @keyframes chartGrow {
            from { height: 0; }
            to { height: var(--height, 50%); }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            color: #667eea;
            font-size: 1.5rem;
            animation: float 6s ease-in-out infinite;
        }

        .element-1 {
            top: 10%;
            right: -30px;
            animation-delay: 0s;
        }

        .element-2 {
            top: 30%;
            left: -30px;
            animation-delay: 1.5s;
        }

        .element-3 {
            bottom: 30%;
            right: -20px;
            animation-delay: 3s;
        }

        .element-4 {
            bottom: 10%;
            left: -20px;
            animation-delay: 4.5s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
            75% { transform: translateY(-15px) rotate(270deg); }
        }

        /* Features Section Styles */
        .features-section-modern {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
        }

        .features-section-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .section-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #2d3748;
            line-height: 1.2;
        }

        .title-accent {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-description {
            font-size: 1.1rem;
            color: #4a5568;
            line-height: 1.7;
            max-width: 600px;
            margin: 0 auto;
        }

        .feature-card-modern {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .feature-card-modern:hover {
            transform: translateY(-8px);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .feature-icon-wrapper {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .feature-icon.blue { background: linear-gradient(135deg, #4299e1, #3182ce); }
        .feature-icon.green { background: linear-gradient(135deg, #48bb78, #38a169); }
        .feature-icon.purple { background: linear-gradient(135deg, #9f7aea, #805ad5); }
        .feature-icon.orange { background: linear-gradient(135deg, #ed8936, #dd6b20); }
        .feature-icon.red { background: linear-gradient(135deg, #f56565, #e53e3e); }
        .feature-icon.teal { background: linear-gradient(135deg, #38b2ac, #319795); }

        .feature-icon-bg {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 90px;
            height: 90px;
            border-radius: 22px;
            background: rgba(102, 126, 234, 0.1);
            z-index: 1;
            transition: all 0.3s ease;
        }

        .feature-card-modern:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-card-modern:hover .feature-icon-bg {
            transform: scale(1.2) rotate(-5deg);
            background: rgba(102, 126, 234, 0.15);
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .feature-description {
            color: #4a5568;
            line-height: 1.7;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .feature-list li i {
            color: #48bb78;
            margin-left: 0.75rem;
            font-size: 0.8rem;
        }

        .feature-hover-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(102, 126, 234, 0.02) 0%,
                rgba(118, 75, 162, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .feature-card-modern:hover .feature-hover-effect {
            opacity: 1;
        }

        /* Responsive Design for Features */
        @media (max-width: 768px) {
            .section-title {
                font-size: 2rem;
            }

            .feature-card-modern {
                padding: 1.5rem;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .feature-icon-bg {
                width: 80px;
                height: 80px;
            }
        }

        /* Statistics Section */
        .stats-section {
            background: var(--primary-gradient);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        .stats-section .section-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .stats-section .section-title {
            color: white;
        }

        .stat-card-modern {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card-modern:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .stat-icon-modern {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .stat-icon-modern.blue { background: rgba(66, 153, 225, 0.3); }
        .stat-icon-modern.green { background: rgba(72, 187, 120, 0.3); }
        .stat-icon-modern.purple { background: rgba(159, 122, 234, 0.3); }
        .stat-icon-modern.orange { background: rgba(237, 137, 54, 0.3); }

        .stat-number-modern {
            font-size: 3rem;
            font-weight: 900;
            color: white;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .stat-label-modern {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .stat-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }

        /* Testimonials Section */
        .testimonials-section {
            background: #f8fafc;
            position: relative;
        }

        .testimonial-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            height: 100%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .testimonial-content {
            margin-bottom: 2rem;
        }

        .testimonial-stars {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 1rem;
        }

        .testimonial-stars i {
            color: #fbbf24;
            font-size: 1rem;
        }

        .testimonial-text {
            font-size: 1rem;
            line-height: 1.7;
            color: #4a5568;
            font-style: italic;
            margin: 0;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .author-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }

        .author-title {
            font-size: 0.9rem;
            color: #718096;
        }

        /* Footer Styles */
        .footer-modern {
            background: #1a202c;
            color: white;
            padding: 4rem 0 2rem;
            position: relative;
        }

        .footer-logo {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            margin-bottom: 1rem;
        }

        .footer-description {
            color: #a0aec0;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .footer-social {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .footer-title {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: white;
        }

        .footer-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-menu li {
            margin-bottom: 0.75rem;
        }

        .footer-menu a {
            color: #a0aec0;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-menu a:hover {
            color: #667eea;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #a0aec0;
        }

        .contact-item i {
            color: #667eea;
            width: 20px;
        }

        .footer-bottom {
            border-top: 1px solid #2d3748;
            padding-top: 2rem;
            margin-top: 3rem;
        }

        .footer-copyright {
            color: #a0aec0;
            margin: 0;
        }

        .footer-links-inline {
            display: flex;
            gap: 2rem;
        }

        .footer-links-inline a {
            color: #a0aec0;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links-inline a:hover {
            color: #667eea;
        }

        /* Responsive Design Enhancements */
        @media (max-width: 992px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .btn-hero {
                min-width: auto;
                margin-bottom: 1rem;
            }

            .hero-stats {
                justify-content: center;
                text-align: center;
            }

            .dashboard-mockup {
                margin-top: 3rem;
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-description {
                font-size: 1rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .mockup-content {
                flex-direction: column;
                height: auto;
            }

            .mockup-sidebar {
                width: 100%;
                border-left: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-number-modern {
                font-size: 2rem;
            }

            .testimonial-card {
                padding: 1.5rem;
            }

            .footer-links-inline {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 1.75rem;
            }

            .hero-badge {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }

            .floating-element {
                display: none;
            }

            .feature-card-modern {
                padding: 1.25rem;
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .feature-icon-bg {
                width: 70px;
                height: 70px;
            }

            .stat-card-modern {
                padding: 1.5rem;
            }

            .stat-icon-modern {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }

        /* Loading Animation */
        body:not(.loaded) {
            overflow: hidden;
        }

        body:not(.loaded)::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        body:not(.loaded)::after {
            content: '';
            position: fixed;
            top: 50%;
            left: 50%;
            width: 50px;
            height: 50px;
            margin: -25px 0 0 -25px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 10000;
        }

        /* Smooth transitions for all elements */
        * {
            transition: all 0.3s ease;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #667eea);
        }

        /* Pricing Section Styles */
        .pricing-section {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .pricing-toggle-wrapper {
            margin-bottom: 3rem;
        }

        .pricing-toggle {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: white;
            padding: 0.5rem;
            border-radius: 50px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .toggle-label {
            font-weight: 600;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            position: relative;
        }

        .toggle-label.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .discount-badge {
            background: #48bb78;
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-right: 0.5rem;
            font-weight: 700;
        }

        .toggle-switch {
            width: 60px;
            height: 30px;
            background: #e2e8f0;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--primary-gradient);
        }

        .toggle-slider {
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            right: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(-30px);
        }

        .pricing-card {
            background: white;
            border-radius: 24px;
            padding: 0;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pricing-card.popular {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .pricing-card.popular:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .popular-badge {
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            color: white;
            padding: 0.5rem 2rem;
            border-radius: 0 0 15px 15px;
            font-size: 0.85rem;
            font-weight: 700;
            z-index: 2;
        }

        .pricing-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            position: relative;
        }

        .plan-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1rem;
        }

        .plan-badge.trial {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .plan-badge.basic {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .plan-badge.pro {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
            color: white;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .plan-price {
            margin-bottom: 1rem;
        }

        .currency {
            font-size: 1.2rem;
            color: #718096;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .amount {
            font-size: 3rem;
            font-weight: 900;
            color: #2d3748;
            line-height: 1;
            margin: 0 0.25rem;
        }

        .period {
            font-size: 1rem;
            color: #718096;
            font-weight: 500;
        }

        .plan-description {
            color: #4a5568;
            font-size: 0.95rem;
            line-height: 1.6;
            margin: 0;
        }

        .pricing-features {
            padding: 0 2rem;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f7fafc;
            font-size: 0.9rem;
        }

        .feature-item:last-child {
            border-bottom: none;
        }

        .feature-item i {
            width: 20px;
            margin-left: 0.75rem;
            font-size: 0.8rem;
        }

        .feature-item:not(.limited) i {
            color: #48bb78;
        }

        .feature-item.limited {
            opacity: 0.5;
        }

        .feature-item.limited i {
            color: #e53e3e;
        }

        .pricing-footer {
            padding: 2rem;
            text-align: center;
        }

        .pricing-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 1rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .trial-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .trial-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
            color: white;
        }

        .basic-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
        }

        .basic-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
            color: white;
        }

        .pro-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
            color: white;
            box-shadow: 0 4px 15px rgba(159, 122, 234, 0.3);
        }

        .pro-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(159, 122, 234, 0.4);
            color: white;
        }

        .pricing-note {
            font-size: 0.8rem;
            color: #718096;
            margin: 0;
        }

        .pricing-info {
            background: white;
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            margin-top: 3rem;
        }

        .info-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .pricing-info h5 {
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .pricing-info p {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
        }

        /* Responsive Design for Pricing */
        @media (max-width: 992px) {
            .pricing-card.popular {
                transform: none;
                margin-bottom: 2rem;
            }

            .pricing-card.popular:hover {
                transform: translateY(-8px);
            }
        }

        @media (max-width: 768px) {
            .pricing-toggle {
                flex-direction: column;
                gap: 0.5rem;
                padding: 1rem;
            }

            .toggle-switch {
                order: -1;
            }

            .pricing-card {
                margin-bottom: 2rem;
            }

            .pricing-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .pricing-features {
                padding: 0 1.5rem;
            }

            .pricing-footer {
                padding: 1.5rem;
            }

            .amount {
                font-size: 2.5rem;
            }

            .pricing-info {
                padding: 2rem 1rem;
            }

            .info-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .pricing-toggle {
                padding: 0.75rem;
            }

            .toggle-label {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .discount-badge {
                font-size: 0.6rem;
                padding: 0.15rem 0.4rem;
            }

            .plan-name {
                font-size: 1.25rem;
            }

            .amount {
                font-size: 2rem;
            }

            .pricing-btn {
                padding: 0.875rem 1.5rem;
                font-size: 0.9rem;
            }

            .feature-item {
                font-size: 0.85rem;
                padding: 0.6rem 0;
            }
        }

        /* Print styles */
        @media print {
            .navbar-modern,
            .floating-element,
            .footer-modern {
                display: none !important;
            }

            .hero-section-modern {
                padding-top: 0;
            }

            * {
                color: black !important;
                background: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand navbar-brand-modern" href="#">
                <i class="fas fa-gamepad me-2"></i>
                PlayGood
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stats">الإحصائيات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">آراء العملاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">الأسعار</a>
                    </li>
                </ul>

                <div class="d-flex">
                    <a href="trial-register.php" class="nav-btn-modern nav-btn-outline" style="background: linear-gradient(135deg, #48bb78, #38a169); color: white; border-color: transparent;">
                        <i class="fas fa-gift me-1"></i>
                        تجربة مجانية
                    </a>
                    <a href="client/login" class="nav-btn-modern nav-btn-outline">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل دخول
                    </a>
                    <a href="register" class="nav-btn-modern nav-btn-primary">
                        <i class="fas fa-rocket me-1"></i>
                        ابدأ مجاناً
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section Enhanced -->
    <section class="hero-section-modern">
        <div class="hero-background">
            <div class="hero-particles"></div>
            <div class="hero-gradient-overlay"></div>
        </div>

        <div class="container">
            <div class="row align-items-center min-vh-100 py-5">
                <div class="col-lg-6 hero-content">
                    <div class="hero-badge mb-4">
                        <i class="fas fa-star me-2"></i>
                        النظام الأول في المنطقة
                    </div>

                    <h1 class="hero-title mb-4">
                        <span class="hero-title-main">أدر مركز الألعاب</span>
                        <span class="hero-title-accent">بذكاء واحترافية</span>
                    </h1>

                    <p class="hero-description mb-5">
                        منصة PlayGood الاحترافية تقدم حلولاً متكاملة لإدارة مراكز الألعاب بكفاءة عالية.
                        تتبع الأجهزة، إدارة العملاء، مراقبة الجلسات، وإدارة الماليات - كل ذلك في مكان واحد.
                    </p>

                    <div class="hero-features mb-5">
                        <div class="hero-feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>إدارة شاملة للأجهزة والصيانة</span>
                        </div>
                        <div class="hero-feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>تتبع دقيق للعملاء والجلسات</span>
                        </div>
                        <div class="hero-feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>تقارير مالية تفصيلية ومتقدمة</span>
                        </div>
                        <div class="hero-feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>واجهة عربية سهلة الاستخدام</span>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="trial-register.php" class="btn-hero btn-hero-primary" style="background: linear-gradient(135deg, #48bb78, #38a169);">
                            <i class="fas fa-gift me-2"></i>
                            تجربة مجانية فورية
                            <span class="btn-hero-subtitle">3 ساعات كاملة</span>
                        </a>
                        <a href="register" class="btn-hero btn-hero-outline">
                            <i class="fas fa-rocket me-2"></i>
                            اشتراك كامل
                            <span class="btn-hero-subtitle">30 يوم مجاناً</span>
                        </a>
                    </div>

                    <div class="hero-stats mt-5">
                        <div class="hero-stat-item">
                            <div class="hero-stat-number">300+</div>
                            <div class="hero-stat-label">مركز ألعاب</div>
                        </div>
                        <div class="hero-stat-item">
                            <div class="hero-stat-number">8K+</div>
                            <div class="hero-stat-label">جهاز مُدار</div>
                        </div>
                        <div class="hero-stat-item">
                            <div class="hero-stat-number">99.9%</div>
                            <div class="hero-stat-label">وقت التشغيل</div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 hero-visual">
                    <div class="hero-dashboard-preview">
                        <div class="dashboard-mockup">
                            <div class="mockup-header">
                                <div class="mockup-controls">
                                    <span class="control-dot red"></span>
                                    <span class="control-dot yellow"></span>
                                    <span class="control-dot green"></span>
                                </div>
                                <div class="mockup-title">PlayGood Dashboard</div>
                            </div>
                            <div class="mockup-content">
                                <div class="mockup-sidebar">
                                    <div class="sidebar-item active">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>لوحة التحكم</span>
                                    </div>
                                    <div class="sidebar-item">
                                        <i class="fas fa-desktop"></i>
                                        <span>الأجهزة</span>
                                    </div>
                                    <div class="sidebar-item">
                                        <i class="fas fa-users"></i>
                                        <span>العملاء</span>
                                    </div>
                                    <div class="sidebar-item">
                                        <i class="fas fa-clock"></i>
                                        <span>الجلسات</span>
                                    </div>
                                </div>
                                <div class="mockup-main">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-icon blue">
                                                <i class="fas fa-desktop"></i>
                                            </div>
                                            <div class="stat-info">
                                                <div class="stat-number">24</div>
                                                <div class="stat-label">أجهزة نشطة</div>
                                            </div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-icon green">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div class="stat-info">
                                                <div class="stat-number">156</div>
                                                <div class="stat-label">عميل اليوم</div>
                                            </div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-icon purple">
                                                <i class="fas fa-chart-line"></i>
                                            </div>
                                            <div class="stat-info">
                                                <div class="stat-number">7,350</div>
                                                <div class="stat-label">ج.م اليوم</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chart-area">
                                        <div class="chart-placeholder">
                                            <div class="chart-bars">
                                                <div class="chart-bar" style="height: 60%"></div>
                                                <div class="chart-bar" style="height: 80%"></div>
                                                <div class="chart-bar" style="height: 45%"></div>
                                                <div class="chart-bar" style="height: 90%"></div>
                                                <div class="chart-bar" style="height: 70%"></div>
                                                <div class="chart-bar" style="height: 85%"></div>
                                                <div class="chart-bar" style="height: 95%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="floating-element element-1">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="floating-element element-2">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="floating-element element-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="floating-element element-4">
                            <i class="fas fa-cog"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Features Section -->
    <section id="features" class="features-section-modern py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <div class="section-badge mb-3">
                        <i class="fas fa-star me-2"></i>
                        مميزات استثنائية
                    </div>
                    <h2 class="section-title mb-4">
                        لماذا يختار أصحاب مراكز الألعاب
                        <span class="title-accent">PlayGood؟</span>
                    </h2>
                    <p class="section-description">
                        نقدم حلولاً تقنية متطورة تساعدك على إدارة مركز الألعاب بكفاءة عالية وتحقيق أقصى استفادة من استثمارك
                    </p>
                </div>
            </div>

            <div class="row g-4">
                <!-- Feature 1 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon blue">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">إدارة الأجهزة الذكية</h4>
                            <p class="feature-description">
                                تتبع حالة جميع الأجهزة في الوقت الفعلي، جدولة الصيانة الدورية،
                                ومراقبة الأداء لضمان تجربة مثالية للعملاء
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> مراقبة الحالة في الوقت الفعلي</li>
                                <li><i class="fas fa-check"></i> تنبيهات الصيانة التلقائية</li>
                                <li><i class="fas fa-check"></i> تقارير الأداء التفصيلية</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon green">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">إدارة العملاء المتقدمة</h4>
                            <p class="feature-description">
                                نظام شامل لإدارة بيانات العملاء، تتبع تاريخ الزيارات،
                                وإنشاء برامج ولاء لزيادة رضا العملاء
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> تقارير عملاء تفصيلية</li>
                                <li><i class="fas fa-check"></i>  خطط العملاء VIP </li>
                                <li><i class="fas fa-check"></i> تحليل سلوك العملاء</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon purple">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">تقارير وتحليلات ذكية</h4>
                            <p class="feature-description">
                                احصل على رؤى عميقة حول أداء مركزك من خلال تقارير تفاعلية
                                وتحليلات متقدمة تساعدك في اتخاذ قرارات مدروسة
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> تقارير مالية شاملة</li>
                                <li><i class="fas fa-check"></i> تحليل الأداء التشغيلي</li>
                                <li><i class="fas fa-check"></i> تقارير الإيرادات</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon orange">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">إدارة الجلسات والحجوزات</h4>
                            <p class="feature-description">
                                نظام متطور لإدارة جلسات اللعب، الحجوزات المسبقة،
                                والتحكم في أوقات الذروة بكفاءة عالية
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> حجوزات مسبقة </li>
                                <li><i class="fas fa-check"></i> إدارة أوقات الذروة</li>
                                <li><i class="fas fa-check"></i> تتبع الجلسات النشطة</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon red">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">إدارة الكافتيريا</h4>
                            <p class="feature-description">
                                نظام نقاط البيع المتكامل لإدارة المشروبات والوجبات الخفيفة
                                مع ربط مباشر بنظام إدارة المركز
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> مبيعات متعددة</li>
                                <li><i class="fas fa-check"></i> إدارة المخزون</li>
                                <li><i class="fas fa-check"></i> تقارير المبيعات</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card feature-card-modern">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon teal">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">الأمان وإدارة الصلاحيات</h4>
                            <p class="feature-description">
                                نظام أمان متقدم مع إدارة دقيقة للصلاحيات، تشفير البيانات،
                                ونسخ احتياطية تلقائية لحماية معلوماتك
                            </p>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> تشفير البيانات المتقدم</li>
                                <li><i class="fas fa-check"></i> إدارة صلاحيات الموظفين</li>
                                <li><i class="fas fa-check"></i> نسخ احتياطية تلقائية</li>
                            </ul>
                        </div>
                        <div class="feature-hover-effect"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section id="stats" class="stats-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 mx-auto text-center mb-5">
                    <div class="section-badge mb-3">
                        <i class="fas fa-chart-bar me-2"></i>
                        أرقام مثيرة للإعجاب
                    </div>
                    <h2 class="section-title mb-4">
                        نفخر بثقة
                        <span class="title-accent">عملائنا</span>
                    </h2>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern blue">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="stat-number-modern" data-target="300">0</div>
                        <div class="stat-label-modern">مركز ألعاب</div>
                        <div class="stat-description">يثق بنا في إدارة أعمالهم</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern green">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <div class="stat-number-modern" data-target="8000">0</div>
                        <div class="stat-label-modern">جهاز مُدار</div>
                        <div class="stat-description">تحت إدارة نظامنا المتطور</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern purple">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number-modern" data-target="180000">0</div>
                        <div class="stat-label-modern">عميل نشط</div>
                        <div class="stat-description">يستخدمون خدماتنا يومياً</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern orange">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number-modern" data-target="99.9">0</div>
                        <div class="stat-label-modern">% وقت التشغيل</div>
                        <div class="stat-description">موثوقية عالية على مدار الساعة</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonials-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <div class="section-badge mb-3">
                        <i class="fas fa-quote-right me-2"></i>
                        آراء عملائنا
                    </div>
                    <h2 class="section-title mb-4">
                        ماذا يقول
                        <span class="title-accent">عملاؤنا؟</span>
                    </h2>
                    <p class="section-description">
                        نفخر بثقة عملائنا وآرائهم الإيجابية حول خدماتنا المتميزة
                    </p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p class="testimonial-text">
                                "PlayGood غيّر طريقة إدارتي لمركز الألعاب بالكامل. النظام سهل الاستخدام
                                والتقارير مفصلة جداً. زادت أرباحي بنسبة 40% خلال 6 أشهر فقط!"
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="author-info">
                                <div class="author-name">أحمد محمود</div>
                                <div class="author-title">مالك مركز الألعاب الذهبي - المعادي</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p class="testimonial-text">
                                "الدعم الفني ممتاز والنظام مستقر جداً. لم أواجه أي مشاكل تقنية منذ
                                بدء الاستخدام. أنصح كل أصحاب مراكز الألعاب بتجربة PlayGood."
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="author-info">
                                <div class="author-name">سارة أحمد</div>
                                <div class="author-title">مديرة مركز الألعاب المتطور - مدينة نصر</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p class="testimonial-text">
                                "التقارير المالية والإحصائيات ساعدتني كثيراً في اتخاذ قرارات صحيحة
                                لتطوير مركزي. النظام يوفر كل ما أحتاجه في مكان واحد."
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="author-info">
                                <div class="author-name">محمد خالد</div>
                                <div class="author-title">مالك سلسلة مراكز الألعاب - الجيزة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <div class="section-badge mb-3">
                        <i class="fas fa-tags me-2"></i>
                        خطط مرنة ومناسبة
                    </div>
                    <h2 class="section-title mb-4">
                        اختر الخطة التي
                        <span class="title-accent">تناسب احتياجاتك</span>
                    </h2>
                    <p class="section-description">
                        خطط أسعار شفافة ومرنة تناسب جميع أحجام مراكز الألعاب - من المراكز الصغيرة إلى السلاسل الكبيرة
                    </p>
                </div>
            </div>

            <!-- Pricing Toggle -->
            <div class="pricing-toggle-wrapper text-center mb-5">
                <div class="pricing-toggle">
                    <span class="toggle-label monthly active">شهري</span>
                    <div class="toggle-switch" id="pricingToggle">
                        <div class="toggle-slider"></div>
                    </div>
                    <span class="toggle-label yearly">
                        سنوي
                        <span class="discount-badge">وفر 20%</span>
                    </span>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- Trial Plan -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card trial-card">
                        <div class="pricing-header">
                            <div class="plan-badge trial">تجربة مجانية</div>
                            <h3 class="plan-name">خطة التجربة</h3>
                            <div class="plan-price">
                                <span class="currency">مجاناً</span>
                                <span class="period">لمدة 30 يوم</span>
                            </div>
                            <p class="plan-description">
                                جرب جميع المميزات مجاناً لمدة شهر كامل
                            </p>
                        </div>

                        <div class="pricing-features">
                            <ul class="features-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>حتى 5 أجهزة</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>إدارة العملاء الأساسية</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>تقارير أساسية</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>دعم فني عبر البريد</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>جميع المميزات الأساسية</span>
                                </li>
                                <li class="feature-item limited">
                                    <i class="fas fa-times"></i>
                                    <span>تقارير متقدمة</span>
                                </li>
                                <li class="feature-item limited">
                                    <i class="fas fa-times"></i>
                                    <span>إدارة الموظفين</span>
                                </li>
                            </ul>
                        </div>

                        <div class="pricing-footer">
                            <a href="register" class="pricing-btn trial-btn">
                                <i class="fas fa-rocket me-2"></i>
                                ابدأ التجربة المجانية
                            </a>
                            <p class="pricing-note">لا يتطلب بطاقة ائتمان</p>
                        </div>
                    </div>
                </div>

                <!-- Basic Plan -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card basic-card">
                        <div class="pricing-header">
                            <div class="plan-badge basic">الأساسية</div>
                            <h3 class="plan-name">خطة البداية</h3>
                            <div class="plan-price">
                                <span class="amount monthly-price">899</span>
                                <span class="amount yearly-price" style="display: none;">8,630</span>
                                <span class="currency">ج.م</span>
                                <span class="period monthly-period">/شهر</span>
                                <span class="period yearly-period" style="display: none;">/سنة</span>
                            </div>
                            <p class="plan-description">
                                مثالية للمراكز الصغيرة والمتوسطة
                            </p>
                        </div>

                        <div class="pricing-features">
                            <ul class="features-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>حتى 20 جهاز</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>إدارة عملاء غير محدودة</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>تقارير مالية شاملة</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>إدارة الكافتيريا</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>دعم فني 24/7</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>نسخ احتياطية يومية</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>تطبيق الهاتف المحمول</span>
                                </li>
                            </ul>
                        </div>

                        <div class="pricing-footer">
                            <a href="register" class="pricing-btn basic-btn">
                                <i class="fas fa-play me-2"></i>
                                ابدأ الآن
                            </a>
                            <p class="pricing-note">يمكن الإلغاء في أي وقت</p>
                        </div>
                    </div>
                </div>

                <!-- Pro Plan -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card pro-card popular">
                        <div class="popular-badge">الأكثر شعبية</div>
                        <div class="pricing-header">
                            <div class="plan-badge pro">الاحترافية</div>
                            <h3 class="plan-name">خطة الأعمال</h3>
                            <div class="plan-price">
                                <span class="amount monthly-price">1,799</span>
                                <span class="amount yearly-price" style="display: none;">17,270</span>
                                <span class="currency">ج.م</span>
                                <span class="period monthly-period">/شهر</span>
                                <span class="period yearly-period" style="display: none;">/سنة</span>
                            </div>
                            <p class="plan-description">
                                للمراكز الكبيرة والسلاسل التجارية
                            </p>
                        </div>

                        <div class="pricing-features">
                            <ul class="features-list">
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>أجهزة غير محدودة</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>إدارة متعددة الفروع</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>تحليلات متقدمة وذكية</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>إدارة الموظفين والصلاحيات</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>API للتكامل مع الأنظمة</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>تدريب مخصص للفريق</span>
                                </li>
                                <li class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>مدير حساب مخصص</span>
                                </li>
                            </ul>
                        </div>

                        <div class="pricing-footer">
                            <a href="register" class="pricing-btn pro-btn">
                                <i class="fas fa-crown me-2"></i>
                                ابدأ الخطة الاحترافية
                            </a>
                            <p class="pricing-note">أفضل قيمة للمال</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="row mt-5">
                <div class="col-lg-10 mx-auto">
                    <div class="pricing-info">
                        <div class="row g-4">
                            <div class="col-md-4 text-center">
                                <div class="info-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h5>ضمان استرداد المال</h5>
                                <p>ضمان استرداد كامل خلال 30 يوم من بدء الاشتراك</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="info-icon">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <h5>دعم فني متميز</h5>
                                <p>فريق دعم فني متخصص متاح 24/7 لمساعدتك</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="info-icon">
                                    <i class="fas fa-sync-alt"></i>
                                </div>
                                <h5>تحديثات مجانية</h5>
                                <p>جميع التحديثات والمميزات الجديدة مجانية مدى الحياة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer-modern">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="footer-brand">
                        <h3 class="footer-logo">
                            <i class="fas fa-gamepad me-2"></i>
                            PlayGood
                        </h3>
                        <p class="footer-description">
                            منصة إدارة مراكز الألعاب الأكثر تطوراً في المنطقة.
                            نساعدك على تحقيق أقصى استفادة من استثمارك.
                        </p>
                        <div class="footer-social">
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-links">
                        <h5 class="footer-title">المنصة</h5>
                        <ul class="footer-menu">
                            <li><a href="#features">المميزات</a></li>
                            <li><a href="#pricing">الأسعار</a></li>
                            <li><a href="#testimonials">آراء العملاء</a></li>
                            <li><a href="#">الدعم الفني</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-links">
                        <h5 class="footer-title">الحساب</h5>
                        <ul class="footer-menu">
                            <li><a href="client/login">تسجيل الدخول</a></li>
                            <li><a href="register">إنشاء حساب</a></li>
                            <li><a href="client/reset-password">استرداد كلمة المرور</a></li>
                            <li><a href="client/profile">تحديث الحساب</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="footer-contact">
                        <h5 class="footer-title">تواصل معنا</h5>
                        <div class="contact-info">
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+201023412847</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>القاهرة، جمهورية مصر العربية</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="footer-copyright">
                            جميع الحقوق محفوظة &copy; 2025 PlayGood
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-links-inline">
                            <a href="#">سياسة الخصوصية</a>
                            <a href="#">شروط الاستخدام</a>
                            <a href="#">اتفاقية الخدمة</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Counter animation for statistics
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target.toString().includes('.') ? target.toFixed(1) : Math.floor(target).toLocaleString();
                    clearInterval(timer);
                } else {
                    element.textContent = start.toString().includes('.') ? start.toFixed(1) : Math.floor(start).toLocaleString();
                }
            }, 16);
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate counters
                    if (entry.target.classList.contains('stat-number-modern')) {
                        const target = parseInt(entry.target.getAttribute('data-target'));
                        animateCounter(entry.target, target);
                        observer.unobserve(entry.target);
                    }

                    // Add fade-in animation
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state for animated elements
            const animatedElements = document.querySelectorAll('.feature-card-modern, .stat-card-modern, .testimonial-card, .stat-number-modern');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            // Parallax effect for hero section
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.hero-particles');
                parallaxElements.forEach(el => {
                    el.style.transform = `translateY(${scrolled * 0.5}px)`;
                });
            });

            // Add hover effects to feature cards
            const featureCards = document.querySelectorAll('.feature-card-modern');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add typing effect to hero title
            const heroTitle = document.querySelector('.hero-title-accent');
            if (heroTitle) {
                const text = heroTitle.textContent;
                heroTitle.textContent = '';
                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        heroTitle.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    }
                };
                setTimeout(typeWriter, 1000);
            }

            // Add floating animation to dashboard elements
            const floatingElements = document.querySelectorAll('.floating-element');
            floatingElements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.5}s`;
            });

            // Add chart bar animation
            const chartBars = document.querySelectorAll('.chart-bar');
            chartBars.forEach((bar, index) => {
                bar.style.setProperty('--i', index);
                bar.style.setProperty('--height', bar.style.height);
            });

            // Mobile menu toggle enhancement
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler && navbarCollapse) {
                navbarToggler.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            }

            // Add scroll-triggered animations
            const scrollElements = document.querySelectorAll('.hero-feature-item');
            scrollElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateX(-30px)';
                el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;

                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateX(0)';
                }, 500 + (index * 100));
            });

            // Add testimonial card hover effects
            const testimonialCards = document.querySelectorAll('.testimonial-card');
            testimonialCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.querySelector('.testimonial-stars').style.transform = 'scale(1.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.querySelector('.testimonial-stars').style.transform = 'scale(1)';
                });
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });

        // Add custom cursor effect for interactive elements
        const interactiveElements = document.querySelectorAll('.btn-hero, .feature-card-modern, .testimonial-card');
        interactiveElements.forEach(el => {
            el.addEventListener('mouseenter', function() {
                document.body.style.cursor = 'pointer';
            });

            el.addEventListener('mouseleave', function() {
                document.body.style.cursor = 'default';
            });
        });

        // Pricing toggle functionality
        const pricingToggle = document.getElementById('pricingToggle');
        const monthlyLabels = document.querySelectorAll('.toggle-label.monthly');
        const yearlyLabels = document.querySelectorAll('.toggle-label.yearly');
        const monthlyPrices = document.querySelectorAll('.monthly-price');
        const yearlyPrices = document.querySelectorAll('.yearly-price');
        const monthlyPeriods = document.querySelectorAll('.monthly-period');
        const yearlyPeriods = document.querySelectorAll('.yearly-period');

        let isYearly = false;

        function togglePricing() {
            isYearly = !isYearly;

            // Toggle switch animation
            pricingToggle.classList.toggle('active', isYearly);

            // Toggle labels
            monthlyLabels.forEach(label => label.classList.toggle('active', !isYearly));
            yearlyLabels.forEach(label => label.classList.toggle('active', isYearly));

            // Toggle prices with animation
            if (isYearly) {
                monthlyPrices.forEach(price => {
                    price.style.opacity = '0';
                    price.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        price.style.display = 'none';
                    }, 150);
                });

                monthlyPeriods.forEach(period => {
                    period.style.opacity = '0';
                    period.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        period.style.display = 'none';
                    }, 150);
                });

                setTimeout(() => {
                    yearlyPrices.forEach(price => {
                        price.style.display = 'inline';
                        setTimeout(() => {
                            price.style.opacity = '1';
                            price.style.transform = 'translateY(0)';
                        }, 50);
                    });

                    yearlyPeriods.forEach(period => {
                        period.style.display = 'inline';
                        setTimeout(() => {
                            period.style.opacity = '1';
                            period.style.transform = 'translateY(0)';
                        }, 50);
                    });
                }, 150);
            } else {
                yearlyPrices.forEach(price => {
                    price.style.opacity = '0';
                    price.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        price.style.display = 'none';
                    }, 150);
                });

                yearlyPeriods.forEach(period => {
                    period.style.opacity = '0';
                    period.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        period.style.display = 'none';
                    }, 150);
                });

                setTimeout(() => {
                    monthlyPrices.forEach(price => {
                        price.style.display = 'inline';
                        setTimeout(() => {
                            price.style.opacity = '1';
                            price.style.transform = 'translateY(0)';
                        }, 50);
                    });

                    monthlyPeriods.forEach(period => {
                        period.style.display = 'inline';
                        setTimeout(() => {
                            period.style.opacity = '1';
                            period.style.transform = 'translateY(0)';
                        }, 50);
                    });
                }, 150);
            }
        }

        // Add event listeners for pricing toggle
        if (pricingToggle) {
            pricingToggle.addEventListener('click', togglePricing);

            monthlyLabels.forEach(label => {
                label.addEventListener('click', () => {
                    if (isYearly) togglePricing();
                });
            });

            yearlyLabels.forEach(label => {
                label.addEventListener('click', () => {
                    if (!isYearly) togglePricing();
                });
            });
        }

        // Initialize pricing elements with transitions
        document.addEventListener('DOMContentLoaded', function() {
            const priceElements = document.querySelectorAll('.monthly-price, .yearly-price, .monthly-period, .yearly-period');
            priceElements.forEach(el => {
                el.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            });

            // Add hover effects to pricing cards
            const pricingCards = document.querySelectorAll('.pricing-card');
            pricingCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('popular')) {
                        this.style.transform = 'translateY(-8px) scale(1.02)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('popular')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    } else {
                        this.style.transform = 'scale(1.05)';
                    }
                });
            });

            // Add pulse animation to popular badge
            const popularBadge = document.querySelector('.popular-badge');
            if (popularBadge) {
                setInterval(() => {
                    popularBadge.style.transform = 'translateX(-50%) scale(1.05)';
                    setTimeout(() => {
                        popularBadge.style.transform = 'translateX(-50%) scale(1)';
                    }, 200);
                }, 3000);
            }

            // Add feature item animations
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-20px)';
                item.style.transition = `opacity 0.4s ease ${index * 0.05}s, transform 0.4s ease ${index * 0.05}s`;

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, 500 + (index * 50));
            });
        });
    </script>
</body>
</html>