<?php
/**
 * سكريپت تشخيص وإصلاح مشكلة عرض البيانات الخاطئة
 * يتحقق من البيانات في قاعدة البيانات ويصلح المشاكل
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص وإصلاح مشكلة عرض البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        .issue-card { border-left: 4px solid #dc3545; }
        .success-card { border-left-color: #28a745; }
        .warning-card { border-left-color: #ffc107; }
        .info-card { border-left-color: #17a2b8; }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card issue-card'>
                <div class='card-header bg-danger text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-bug me-2'></i>
                        تشخيص وإصلاح مشكلة عرض البيانات
                    </h3>
                </div>
                <div class='card-body'>";

try {
    echo "<h5>1. فحص بيانات الجلسات النشطة</h5>";
    
    // فحص الجلسات النشطة
    $active_sessions_query = $pdo->query("
        SELECT 
            s.session_id,
            s.customer_id,
            d.device_name,
            d.device_type,
            c.name as customer_name,
            c.phone as customer_phone
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'active'
        LIMIT 10
    ");
    $active_sessions = $active_sessions_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($active_sessions)) {
        echo "<div class='table-responsive'>
                <table class='table table-sm table-bordered'>
                    <thead class='table-light'>
                        <tr>
                            <th>Session ID</th>
                            <th>Customer ID</th>
                            <th>Device Name</th>
                            <th>Device Type</th>
                            <th>Customer Name</th>
                            <th>Customer Phone</th>
                            <th>المشكلة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($active_sessions as $session) {
            $issues = [];
            
            // فحص المشاكل
            if ($session['device_name'] === $session['customer_name']) {
                $issues[] = "اسم الجهاز = اسم العميل";
            }
            if (empty($session['device_name']) || $session['device_name'] === 'NULL') {
                $issues[] = "اسم الجهاز فارغ";
            }
            if (preg_match('/^[A-Za-z0-9\s]+$/', $session['customer_name'])) {
                $issues[] = "اسم العميل بالإنجليزية (قد يكون اسم جهاز)";
            }
            
            $issue_class = !empty($issues) ? 'table-danger' : 'table-success';
            $issue_text = !empty($issues) ? implode(', ', $issues) : 'لا توجد مشاكل';
            
            echo "<tr class='$issue_class'>
                    <td>{$session['session_id']}</td>
                    <td>{$session['customer_id']}</td>
                    <td>" . htmlspecialchars($session['device_name']) . "</td>
                    <td>" . htmlspecialchars($session['device_type']) . "</td>
                    <td>" . htmlspecialchars($session['customer_name']) . "</td>
                    <td>" . htmlspecialchars($session['customer_phone']) . "</td>
                    <td>$issue_text</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-info'>لا توجد جلسات نشطة حالياً</div>";
    }
    
    echo "<h5>2. فحص بيانات الفواتير</h5>";
    
    // فحص الفواتير
    $invoices_query = $pdo->query("
        SELECT
            i.invoice_id,
            i.session_id,
            i.invoice_number,
            d.device_name,
            d.device_type,
            c.name as customer_name
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        ORDER BY i.created_at DESC
        LIMIT 10
    ");
    $invoices = $invoices_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($invoices)) {
        echo "<div class='table-responsive'>
                <table class='table table-sm table-bordered'>
                    <thead class='table-light'>
                        <tr>
                            <th>Invoice ID</th>
                            <th>Session ID</th>
                            <th>Invoice Number</th>
                            <th>Device Name</th>
                            <th>Device Type</th>
                            <th>Customer Name</th>
                            <th>المشكلة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($invoices as $invoice) {
            $issues = [];
            
            // فحص المشاكل
            if ($invoice['device_name'] === $invoice['customer_name']) {
                $issues[] = "اسم الجهاز = اسم العميل";
            }
            if (empty($invoice['device_name']) || $invoice['device_name'] === 'NULL') {
                $issues[] = "اسم الجهاز فارغ";
            }
            if (preg_match('/^[A-Za-z0-9\s]+$/', $invoice['customer_name'])) {
                $issues[] = "اسم العميل بالإنجليزية (قد يكون اسم جهاز)";
            }
            
            $issue_class = !empty($issues) ? 'table-danger' : 'table-success';
            $issue_text = !empty($issues) ? implode(', ', $issues) : 'لا توجد مشاكل';
            
            echo "<tr class='$issue_class'>
                    <td>{$invoice['id']}</td>
                    <td>{$invoice['session_id']}</td>
                    <td>" . htmlspecialchars($invoice['invoice_number']) . "</td>
                    <td>" . htmlspecialchars($invoice['device_name']) . "</td>
                    <td>" . htmlspecialchars($invoice['device_type']) . "</td>
                    <td>" . htmlspecialchars($invoice['customer_name']) . "</td>
                    <td>$issue_text</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-info'>لا توجد فواتير</div>";
    }
    
    echo "<h5>3. فحص جدول الأجهزة</h5>";
    
    // فحص الأجهزة المشبوهة
    $suspicious_devices_query = $pdo->query("
        SELECT device_id, device_name, device_type
        FROM devices
        WHERE device_name REGEXP '^[أ-ي]+$'
        OR device_name IN (SELECT name FROM customers WHERE name IS NOT NULL)
        OR LENGTH(device_name) < 3
        LIMIT 10
    ");
    $suspicious_devices = $suspicious_devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($suspicious_devices)) {
        echo "<div class='alert alert-warning'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>أجهزة تحتاج مراجعة:</h6>
              </div>";
        
        echo "<div class='table-responsive'>
                <table class='table table-sm table-bordered'>
                    <thead class='table-light'>
                        <tr>
                            <th>Device ID</th>
                            <th>Device Name</th>
                            <th>Device Type</th>
                            <th>المشكلة المحتملة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($suspicious_devices as $device) {
            $issue = '';
            if (preg_match('/^[أ-ي]+$/', $device['device_name'])) {
                $issue = 'اسم عربي (قد يكون اسم عميل)';
            } elseif (strlen($device['device_name']) < 3) {
                $issue = 'اسم قصير جداً';
            } else {
                $issue = 'قد يكون اسم عميل';
            }
            
            echo "<tr class='table-warning'>
                    <td>{$device['device_id']}</td>
                    <td>" . htmlspecialchars($device['device_name']) . "</td>
                    <td>" . htmlspecialchars($device['device_type']) . "</td>
                    <td>$issue</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>جميع أسماء الأجهزة تبدو صحيحة
              </div>";
    }
    
    echo "<h5>4. فحص جدول العملاء</h5>";
    
    // فحص العملاء المشبوهين
    $suspicious_customers_query = $pdo->query("
        SELECT customer_id, name, phone
        FROM customers
        WHERE name REGEXP '^[A-Za-z0-9\\s]+$'
        OR name IN (SELECT device_name FROM devices WHERE device_name IS NOT NULL)
        OR LENGTH(name) < 2
        LIMIT 10
    ");
    $suspicious_customers = $suspicious_customers_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($suspicious_customers)) {
        echo "<div class='alert alert-warning'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>عملاء تحتاج مراجعة:</h6>
              </div>";
        
        echo "<div class='table-responsive'>
                <table class='table table-sm table-bordered'>
                    <thead class='table-light'>
                        <tr>
                            <th>Customer ID</th>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>المشكلة المحتملة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($suspicious_customers as $customer) {
            $issue = '';
            if (preg_match('/^[A-Za-z0-9\s]+$/', $customer['name'])) {
                $issue = 'اسم بالإنجليزية (قد يكون اسم جهاز)';
            } elseif (strlen($customer['name']) < 2) {
                $issue = 'اسم قصير جداً';
            } else {
                $issue = 'قد يكون اسم جهاز';
            }
            
            echo "<tr class='table-warning'>
                    <td>{$customer['customer_id']}</td>
                    <td>" . htmlspecialchars($customer['name']) . "</td>
                    <td>" . htmlspecialchars($customer['phone']) . "</td>
                    <td>$issue</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>جميع أسماء العملاء تبدو صحيحة
              </div>";
    }
    
    echo "<h5>5. الحلول المقترحة</h5>";
    
    echo "<div class='row g-3'>
            <div class='col-md-6'>
                <div class='card warning-card'>
                    <div class='card-header bg-warning text-dark'>
                        <h6 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح أسماء الأجهزة</h6>
                    </div>
                    <div class='card-body'>
                        <p>إذا كانت أسماء الأجهزة تحتوي على أسماء عملاء:</p>
                        <a href='fix_device_names.php' class='btn btn-warning btn-sm'>
                            <i class='fas fa-wrench me-1'></i>إصلاح أسماء الأجهزة
                        </a>
                    </div>
                </div>
            </div>
            <div class='col-md-6'>
                <div class='card info-card'>
                    <div class='card-header bg-info text-white'>
                        <h6 class='mb-0'><i class='fas fa-users me-2'></i>تنظيف بيانات العملاء</h6>
                    </div>
                    <div class='card-body'>
                        <p>إذا كانت أسماء العملاء تحتوي على أسماء أجهزة:</p>
                        <a href='fix_customer_names.php' class='btn btn-info btn-sm'>
                            <i class='fas fa-user-edit me-1'></i>تنظيف أسماء العملاء
                        </a>
                    </div>
                </div>
            </div>
          </div>";
    
    echo "<div class='alert alert-info mt-4'>
            <h6><i class='fas fa-lightbulb me-2'></i>نصائح لتجنب المشكلة مستقبلاً:</h6>
            <ul class='mb-0'>
                <li>تأكد من إدخال أسماء الأجهزة بالإنجليزية (مثل: PS5-1, Xbox-2)</li>
                <li>تأكد من إدخال أسماء العملاء بالعربية</li>
                <li>تجنب استخدام نفس الاسم للجهاز والعميل</li>
                <li>استخدم أسماء وصفية للأجهزة (مثل: PlayStation 5 - الصالة الرئيسية)</li>
            </ul>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/sessions.php' class='btn btn-primary me-2'>
                <i class='fas fa-play-circle me-1'></i>اختبار صفحة الجلسات
            </a>
            <a href='client/invoices.php' class='btn btn-success me-2'>
                <i class='fas fa-file-invoice me-1'></i>اختبار صفحة الفواتير
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
