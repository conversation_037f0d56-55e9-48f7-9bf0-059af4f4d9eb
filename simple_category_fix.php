<?php
/**
 * إصلاح بسيط لمشكلة حذف التصنيفات
 * PlayGood System - Simple Category Deletion Fix
 */

require_once 'config/database.php';

// تعيين ترميز UTF-8
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة حذف التصنيفات - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .container { max-width: 800px; margin: 20px auto; padding: 20px; }
    </style>
</head>
<body>

<div class="container">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h1 class="mb-0"><i class="fas fa-tools me-2"></i>إصلاح مشكلة حذف التصنيفات</h1>
        </div>
        <div class="card-body">

<?php
$fixes_applied = 0;
$errors = [];

try {
    echo "<h2><i class='fas fa-wrench me-2'></i>تطبيق الإصلاحات</h2>";
    
    // الخطوة 1: إزالة القيود المرجعية الموجودة
    echo "<h5>1. إزالة القيود المرجعية المشكلة:</h5>";
    
    try {
        // محاولة إزالة القيد الشائع
        $pdo->exec("ALTER TABLE cafeteria_items DROP FOREIGN KEY cafeteria_items_ibfk_1");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إزالة القيد المرجعي cafeteria_items_ibfk_1</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "check that column/key exists") !== false) {
            echo "<p class='warning'><i class='fas fa-info-circle me-2'></i>القيد المرجعي غير موجود (هذا طبيعي)</p>";
        } else {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
        }
    }
    
    // الخطوة 2: التأكد من أن عمود category_id يقبل NULL
    echo "<h5>2. تعديل عمود category_id:</h5>";
    try {
        $pdo->exec("ALTER TABLE cafeteria_items MODIFY COLUMN category_id INT NULL");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم تعديل عمود category_id ليقبل NULL</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
    }
    
    // الخطوة 3: إضافة قيد مرجعي محسن (اختياري)
    echo "<h5>3. إضافة قيد مرجعي محسن:</h5>";
    try {
        $pdo->exec("
            ALTER TABLE cafeteria_items 
            ADD CONSTRAINT fk_cafeteria_category_safe 
            FOREIGN KEY (category_id) REFERENCES categories(category_id) 
            ON DELETE SET NULL ON UPDATE CASCADE
        ");
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إضافة قيد مرجعي محسن</p>";
        $fixes_applied++;
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "Duplicate key name") !== false) {
            echo "<p class='warning'><i class='fas fa-info-circle me-2'></i>القيد المرجعي موجود مسبقاً</p>";
        } else {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
        }
    }
    
    // الخطوة 4: اختبار الحذف
    echo "<h5>4. اختبار وظيفة الحذف:</h5>";
    try {
        // إنشاء تصنيف تجريبي
        $test_name = 'تصنيف تجريبي ' . date('H:i:s');
        $stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
        $stmt->execute([$test_name]);
        $test_id = $pdo->lastInsertId();
        
        echo "<p class='success'><i class='fas fa-plus me-2'></i>تم إنشاء تصنيف تجريبي: $test_name</p>";
        
        // محاولة حذف التصنيف التجريبي
        $stmt = $pdo->prepare("DELETE FROM categories WHERE category_id = ? AND client_id = 1");
        $stmt->execute([$test_id]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم حذف التصنيف التجريبي بنجاح! المشكلة تم حلها.</p>";
            $fixes_applied++;
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في حذف التصنيف التجريبي</p>";
            $errors[] = "فشل في حذف التصنيف التجريبي";
        }
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في اختبار الحذف: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // عرض معلومات إضافية
    echo "<h5>5. معلومات إضافية:</h5>";
    
    // عد التصنيفات
    $categories_count = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
    echo "<p><i class='fas fa-tags me-2'></i>عدد التصنيفات الموجودة: $categories_count</p>";
    
    // عد المنتجات
    $items_count = $pdo->query("SELECT COUNT(*) FROM cafeteria_items")->fetchColumn();
    echo "<p><i class='fas fa-coffee me-2'></i>عدد منتجات الكافتيريا: $items_count</p>";
    
    // فحص المنتجات بدون تصنيف
    $no_category_count = $pdo->query("SELECT COUNT(*) FROM cafeteria_items WHERE category IS NULL OR category = ''")->fetchColumn();
    if ($no_category_count > 0) {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>يوجد $no_category_count منتج بدون تصنيف</p>";
    }

} catch (Exception $e) {
    echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ عام: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

// ملخص النتائج
echo "<hr>";
echo "<h2><i class='fas fa-clipboard-check me-2'></i>ملخص النتائج</h2>";

if ($fixes_applied > 0) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>تم تطبيق $fixes_applied إصلاح بنجاح!</h5>";
    echo "<p>يجب أن تعمل وظيفة حذف التصنيفات الآن بشكل طبيعي.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>لم يتم تطبيق أي إصلاحات جديدة</h5>";
    echo "<p>قد تكون المشكلة محلولة مسبقاً.</p>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تحذيرات:</h5>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-lightbulb me-2'></i>الخطوات التالية:</h5>";
echo "<ol>";
echo "<li>اذهب إلى صفحة الكافتيريا وجرب حذف تصنيف فارغ</li>";
echo "<li>تأكد من عدم وجود منتجات مرتبطة بالتصنيف قبل الحذف</li>";
echo "<li>إذا استمرت المشكلة، تحقق من سجلات أخطاء PHP</li>";
echo "</ol>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='client/cafeteria.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-coffee me-2'></i>اذهب إلى صفحة الكافتيريا";
echo "</a>";
echo "<a href='simple_category_fix.php' class='btn btn-secondary'>";
echo "<i class='fas fa-redo me-2'></i>إعادة تشغيل الإصلاح";
echo "</a>";
echo "</div>";

?>

        </div>
    </div>
</div>

</body>
</html>
