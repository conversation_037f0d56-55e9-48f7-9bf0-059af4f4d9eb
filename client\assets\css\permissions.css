/* تحسينات واجهة إدارة الصلاحيات */

.permissions-modal .modal-dialog {
    max-width: 900px;
}

.permissions-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.permissions-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #dee2e6;
}

.permission-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.permission-category:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.permission-category-header {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    padding: 12px 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.permission-category-header .category-icon {
    font-size: 1.1em;
}

.permission-category-body {
    padding: 16px;
}

.permission-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.permission-item:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.permission-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
}

.permission-item .form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.permission-item .form-check-label {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    width: 100%;
}

.permission-item .permission-description {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

.permission-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.permission-controls .btn {
    font-size: 0.85em;
    padding: 6px 12px;
}

.custom-permissions-toggle {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.custom-permissions-toggle .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.custom-permissions-toggle .form-check-label {
    font-weight: 600;
    color: #856404;
}

.permissions-preview {
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 6px;
    padding: 12px;
    margin-top: 15px;
}

.permissions-preview h6 {
    color: #155724;
    margin-bottom: 8px;
}

.permissions-preview .badge {
    margin: 2px;
    font-size: 0.75em;
}

.permission-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.permission-stat {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px 15px;
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.permission-stat .stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #007bff;
}

.permission-stat .stat-label {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 2px;
}

.employee-permission-badge {
    font-size: 0.75em;
    padding: 4px 8px;
    border-radius: 12px;
}

.employee-permission-badge.custom {
    background-color: #17a2b8;
    color: white;
}

.employee-permission-badge.role {
    background-color: #6c757d;
    color: white;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .permissions-modal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .permission-category-body .row > div {
        margin-bottom: 10px;
    }
    
    .permission-controls {
        justify-content: center;
    }
    
    .permission-stats {
        justify-content: center;
    }
    
    .permission-stat {
        min-width: 100px;
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات إضافية */
.permission-search {
    margin-bottom: 15px;
}

.permission-search input {
    border-radius: 20px;
    padding-left: 40px;
}

.permission-search .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.no-permissions-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-permissions-message i {
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.5;
}
