/* ملف CSS حديث ومتقدم لصفحات المصادقة - PlayGood */
/* تصميم عصري مع تأثيرات متقدمة ودعم RTL كامل */

/* متغيرات CSS متقدمة */
:root {
    --auth-primary: #667eea;
    --auth-secondary: #764ba2;
    --auth-accent: #f093fb;
    --auth-success: #10b981;
    --auth-warning: #f59e0b;
    --auth-danger: #ef4444;
    --auth-glass: rgba(255, 255, 255, 0.15);
    --auth-glass-border: rgba(255, 255, 255, 0.25);
    --auth-shadow-light: rgba(255, 255, 255, 0.15);
    --auth-shadow-dark: rgba(0, 0, 0, 0.15);
    --auth-text-primary: #1f2937;
    --auth-text-light: rgba(255, 255, 255, 0.95);
    --auth-border-radius: 28px;
    --auth-transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --auth-hover-scale: 1.05;
    --auth-focus-scale: 1.02;
}

/* تأثيرات الخلفية المتقدمة */
.auth-page {
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #667eea 75%,
        #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* تأثير تحريك الخلفية */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* طبقة إضافية للعمق */
.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* جسيمات متحركة في الخلفية */
.auth-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 2;
    pointer-events: none;
}

.auth-particles::before,
.auth-particles::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.auth-particles::before {
    top: -150px;
    left: -150px;
    animation-delay: 0s;
}

.auth-particles::after {
    bottom: -150px;
    right: -150px;
    animation-delay: -10s;
    background: rgba(255, 255, 255, 0.05);
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
    100% { transform: translateY(0px) rotate(360deg); }
}

.auth-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.2) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes backgroundFloat {
    0%, 100% { 
        transform: translateX(0) translateY(0) rotate(0deg);
        opacity: 0.6;
    }
    25% { 
        transform: translateX(-20px) translateY(-20px) rotate(1deg);
        opacity: 0.8;
    }
    50% { 
        transform: translateX(20px) translateY(20px) rotate(-1deg);
        opacity: 0.7;
    }
    75% { 
        transform: translateX(-10px) translateY(10px) rotate(0.5deg);
        opacity: 0.9;
    }
}

/* تأثيرات الجسيمات المتحركة */
.auth-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
        radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 80px 80px, 120px 120px, 60px 60px, 100px 100px;
    animation: particleMove 25s linear infinite;
    pointer-events: none;
    z-index: 2;
}

@keyframes particleMove {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-15px) translateX(10px); }
    50% { transform: translateY(-8px) translateX(-8px); }
    75% { transform: translateY(-20px) translateX(5px); }
    100% { transform: translateY(0) translateX(0); }
}

/* تحسينات البطاقة المتقدمة */
.auth-card-enhanced {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(40px) saturate(200%);
    border: 2px solid var(--auth-glass-border);
    border-radius: var(--auth-border-radius);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px var(--auth-shadow-light) inset,
        0 4px 0 rgba(255, 255, 255, 0.3) inset,
        0 0 60px rgba(102, 126, 234, 0.15);
    transition: var(--auth-transition);
    overflow: visible;
    animation: cardFloat 6s ease-in-out infinite;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.01); }
}

.auth-card-enhanced::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, 
        var(--auth-primary), 
        var(--auth-secondary), 
        var(--auth-accent), 
        var(--auth-primary));
    background-size: 300% 300%;
    border-radius: calc(var(--auth-border-radius) + 2px);
    z-index: -1;
    opacity: 0;
    animation: borderGlow 4s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { 
        opacity: 0.3; 
        background-position: 0% 50%; 
    }
    50% { 
        opacity: 0.6; 
        background-position: 100% 50%; 
    }
}

.auth-card-enhanced:hover::before {
    opacity: 0.8;
}

/* تأثيرات النماذج المتقدمة */
.auth-form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.auth-form-floating .auth-form-control {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9),
        rgba(255, 255, 255, 0.8));
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 16px;
    padding: 1rem 3.5rem 1rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    transition: var(--auth-transition);
    position: relative;
    overflow: hidden;
    min-height: 48px;
}

.auth-form-floating .auth-form-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(102, 126, 234, 0.1), 
        transparent);
    transition: left 0.8s ease;
}

.auth-form-floating .auth-form-control:hover::before {
    left: 100%;
}

.auth-form-floating .auth-form-control:focus {
    border-color: var(--auth-primary);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.12),
        0 8px 20px rgba(102, 126, 234, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset;
    transform: translateY(-2px) scale(1.01);
    background: rgba(255, 255, 255, 1);
}

/* تأثيرات الأيقونات المحسنة */
.auth-form-icon-enhanced {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--auth-primary);
    font-size: 1.1rem;
    transition: var(--auth-transition);
    z-index: 3;
}

.auth-form-floating:hover .auth-form-icon-enhanced {
    transform: translateY(-50%) scale(1.2) rotate(5deg);
    color: var(--auth-secondary);
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.auth-form-floating .auth-form-control:focus + .auth-form-icon-enhanced {
    transform: translateY(-50%) scale(1.3) rotate(-5deg);
    color: var(--auth-accent);
    text-shadow: 0 0 15px rgba(240, 147, 251, 0.6);
}

/* تأثيرات الأزرار المتقدمة */
.auth-btn-modern {
    position: relative;
    width: 100%;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    background: linear-gradient(135deg,
        var(--auth-primary) 0%,
        var(--auth-secondary) 50%,
        var(--auth-accent) 100%);
    background-size: 300% 300%;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 1px 0 rgba(255, 255, 255, 0.3) inset;
    transition: var(--auth-transition);
    cursor: pointer;
    overflow: hidden;
    animation: buttonPulse 3s ease-in-out infinite;
    margin-top: 1rem;
}

@keyframes buttonPulse {
    0%, 100% { 
        background-position: 0% 50%;
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset,
            0 4px 0 rgba(255, 255, 255, 0.3) inset;
    }
    50% { 
        background-position: 100% 50%;
        box-shadow:
            0 20px 50px rgba(118, 75, 162, 0.5),
            0 0 0 1px rgba(255, 255, 255, 0.3) inset,
            0 4px 0 rgba(255, 255, 255, 0.4) inset;
    }
}

.auth-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.8s ease;
}

.auth-btn-modern:hover::before {
    left: 100%;
}

.auth-btn-modern:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        0 1px 0 rgba(255, 255, 255, 0.4) inset,
        0 0 30px rgba(102, 126, 234, 0.2);
}

.auth-btn-modern:active {
    transform: translateY(-2px) scale(1.01);
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* تحسينات الروابط */
.auth-link-modern {
    position: relative;
    color: var(--auth-primary);
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    transition: var(--auth-transition);
    display: inline-block;
}

.auth-link-modern::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--auth-primary), var(--auth-accent));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.auth-link-modern:hover::before {
    width: 100%;
}

.auth-link-modern:hover {
    color: var(--auth-secondary);
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    text-decoration: none;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .auth-card-enhanced {
        margin: 1rem;
        border-radius: 18px;
    }

    .auth-form-floating {
        margin-bottom: 1.2rem;
    }

    .auth-form-floating .auth-form-control {
        padding: 0.9rem 3rem 0.9rem 0.9rem;
        font-size: 0.95rem;
        border-radius: 14px;
        min-height: 44px;
    }

    .auth-form-icon-enhanced {
        right: 0.9rem;
        font-size: 1rem;
    }

    .auth-btn-modern {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        letter-spacing: 0.3px;
        border-radius: 14px;
    }
}

/* تأثيرات التحميل */
.auth-loading {
    position: relative;
    color: transparent !important;
}

.auth-loading::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    top: 50%;
    left: 50%;
    margin-left: -12px;
    margin-top: -12px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تأثيرات إضافية متقدمة */

/* تأثير الضوء المتحرك */
.auth-spotlight {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-card-enhanced:hover .auth-spotlight {
    opacity: 1;
}

/* تأثير الموجات */
.auth-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تأثير النجوم المتحركة */
.auth-stars {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: starsMove 20s linear infinite;
    pointer-events: none;
    opacity: 0.6;
}

@keyframes starsMove {
    from { transform: translateY(0); }
    to { transform: translateY(-100px); }
}

/* تأثير الضباب */
.auth-fog {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(ellipse at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 50%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    animation: fogMove 15s ease-in-out infinite;
    pointer-events: none;
}

@keyframes fogMove {
    0%, 100% {
        transform: translateX(0) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateX(20px) scale(1.1);
        opacity: 0.6;
    }
}

/* تحسينات الأداء */
.auth-page,
.auth-card-enhanced,
.auth-form-control,
.auth-btn-modern {
    will-change: transform;
}

/* تأثيرات الهولوجرام */
.auth-hologram {
    position: relative;
    overflow: hidden;
}

.auth-hologram::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: hologramScan 3s linear infinite;
    pointer-events: none;
}

@keyframes hologramScan {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* تأثير الكريستال */
.auth-crystal {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%,
        rgba(255, 255, 255, 0.1) 100%);
    background-size: 200% 200%;
    animation: crystalShine 4s ease-in-out infinite;
}

@keyframes crystalShine {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* تأثيرات الإضاءة الجانبية */
.auth-side-glow {
    position: relative;
}

.auth-side-glow::before,
.auth-side-glow::after {
    content: '';
    position: absolute;
    top: 20%;
    width: 4px;
    height: 60%;
    background: linear-gradient(to bottom,
        transparent,
        rgba(102, 126, 234, 0.6),
        transparent);
    animation: sideGlow 2s ease-in-out infinite alternate;
}

.auth-side-glow::before {
    left: -2px;
}

.auth-side-glow::after {
    right: -2px;
}

@keyframes sideGlow {
    0% { opacity: 0.3; transform: scaleY(0.8); }
    100% { opacity: 1; transform: scaleY(1.2); }
}

/* تحسينات الاستجابة للأجهزة المحمولة */
@media (max-width: 576px) {
    .auth-particles,
    .auth-stars,
    .auth-fog {
        display: none; /* إخفاء التأثيرات المعقدة على الأجهزة الصغيرة لتحسين الأداء */
    }

    .auth-card-enhanced {
        animation: none; /* تقليل الحركة على الأجهزة المحمولة */
    }

    .auth-hologram::before {
        animation-duration: 5s; /* إبطاء الحركة */
    }
}

/* تحسينات الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .auth-particles {
        background-image:
            radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    }

    .auth-stars {
        background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.2), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.3), transparent);
    }
}

/* تحسينات الأيقونة المتقدمة */
.auth-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg,
        var(--auth-primary) 0%,
        var(--auth-secondary) 30%,
        var(--auth-accent) 60%,
        var(--auth-primary) 100%);
    background-size: 300% 300%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow:
        0 25px 50px rgba(102, 126, 234, 0.4),
        0 0 0 4px rgba(255, 255, 255, 0.2),
        0 0 0 8px rgba(102, 126, 234, 0.1);
    transition: var(--auth-transition);
    animation: iconPulse 12s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.auth-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    animation: iconShine 4s ease-in-out infinite;
}

.auth-icon::after {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.2),
        transparent 50%,
        rgba(255, 255, 255, 0.1));
    pointer-events: none;
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        background-position: 0% 50%;
    }
    50% {
        transform: scale(1.02);
        background-position: 100% 50%;
    }
}

@keyframes iconShine {
    0%, 100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 1;
    }
}

.auth-icon:hover {
    transform: scale(1.05);
    animation-play-state: paused;
}

/* تأثيرات متقدمة للعناوين */
.auth-title {
    font-size: 2.2rem;
    font-weight: 900;
    color: var(--auth-text-primary);
    margin-bottom: 0.8rem;
    text-align: center;
    background: linear-gradient(135deg,
        var(--auth-primary) 0%,
        var(--auth-secondary) 50%,
        var(--auth-accent) 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 10s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
    position: relative;
}

.auth-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg,
        var(--auth-primary),
        var(--auth-accent));
    border-radius: 2px;
    animation: titleUnderline 4s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% {
        background-position: 0% 50%;
        filter: brightness(1) contrast(1);
    }
    50% {
        background-position: 100% 50%;
        filter: brightness(1.3) contrast(1.2);
    }
}

@keyframes titleUnderline {
    0%, 100% {
        width: 60px;
        opacity: 0.7;
    }
    50% {
        width: 80px;
        opacity: 1;
    }
}

.auth-subtitle {
    font-size: 1.1rem;
    font-weight: 500;
    color: rgba(102, 126, 234, 0.8);
    margin-bottom: 0;
    text-align: center;
    animation: subtitleFade 4s ease-in-out infinite;
}

@keyframes subtitleFade {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}
