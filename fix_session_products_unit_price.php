<?php
require_once 'config/database.php';

echo "<h1>إصلاح مشكلة unit_price في جدول session_products</h1>";

try {
    // التحقق من بنية الجدول الحالية
    echo "<h2>1. فحص بنية جدول session_products</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_unit_price = false;
    $has_price = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'unit_price') {
            $has_unit_price = true;
        }
        if ($column['Field'] === 'price') {
            $has_price = true;
        }
    }
    echo "</table>";
    
    echo "<h2>2. تحليل المشكلة</h2>";
    echo "<p>يحتوي الجدول على:</p>";
    echo "<ul>";
    echo "<li>حقل unit_price: " . ($has_unit_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>حقل price: " . ($has_price ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "</ul>";
    
    // إصلاح المشكلة
    echo "<h2>3. إصلاح المشكلة</h2>";
    
    if ($has_unit_price && !$has_price) {
        // الحالة 1: يوجد unit_price فقط - نحتاج لإضافة price أو تعديل الكود
        echo "<p style='color: orange;'>⚠️ الجدول يحتوي على unit_price فقط</p>";
        echo "<p>سأقوم بإضافة حقل price مع قيمة افتراضية:</p>";
        
        $pdo->exec("ALTER TABLE session_products ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00 AFTER quantity");
        echo "<p style='color: green;'>✅ تم إضافة حقل price</p>";
        
        // نسخ البيانات من unit_price إلى price
        $pdo->exec("UPDATE session_products SET price = unit_price WHERE price = 0");
        echo "<p style='color: green;'>✅ تم نسخ البيانات من unit_price إلى price</p>";
        
    } elseif ($has_price && !$has_unit_price) {
        // الحالة 2: يوجد price فقط - نحتاج لإضافة unit_price مع قيمة افتراضية
        echo "<p style='color: orange;'>⚠️ الجدول يحتوي على price فقط</p>";
        echo "<p>سأقوم بإضافة حقل unit_price مع قيمة افتراضية:</p>";
        
        $pdo->exec("ALTER TABLE session_products ADD COLUMN unit_price DECIMAL(10,2) DEFAULT 0.00 AFTER quantity");
        echo "<p style='color: green;'>✅ تم إضافة حقل unit_price</p>";
        
        // نسخ البيانات من price إلى unit_price
        $pdo->exec("UPDATE session_products SET unit_price = price WHERE unit_price = 0");
        echo "<p style='color: green;'>✅ تم نسخ البيانات من price إلى unit_price</p>";
        
    } elseif ($has_unit_price && $has_price) {
        // الحالة 3: يوجد كلا الحقلين
        echo "<p style='color: blue;'>ℹ️ الجدول يحتوي على كلا الحقلين</p>";
        echo "<p>سأتأكد من أن unit_price له قيمة افتراضية:</p>";
        
        $pdo->exec("ALTER TABLE session_products MODIFY COLUMN unit_price DECIMAL(10,2) DEFAULT 0.00");
        echo "<p style='color: green;'>✅ تم تعديل unit_price ليحتوي على قيمة افتراضية</p>";
        
    } else {
        // الحالة 4: لا يوجد أي من الحقلين
        echo "<p style='color: red;'>❌ لا يوجد أي من الحقلين!</p>";
        echo "<p>سأقوم بإضافة كلا الحقلين:</p>";
        
        $pdo->exec("ALTER TABLE session_products ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00 AFTER quantity");
        $pdo->exec("ALTER TABLE session_products ADD COLUMN unit_price DECIMAL(10,2) DEFAULT 0.00 AFTER price");
        echo "<p style='color: green;'>✅ تم إضافة كلا الحقلين</p>";
    }
    
    // التحقق من وجود total_price
    $has_total_price = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'total_price') {
            $has_total_price = true;
            break;
        }
    }
    
    if (!$has_total_price) {
        echo "<p>إضافة حقل total_price:</p>";
        $pdo->exec("ALTER TABLE session_products ADD COLUMN total_price DECIMAL(10,2) DEFAULT 0.00 AFTER unit_price");
        echo "<p style='color: green;'>✅ تم إضافة حقل total_price</p>";
    }
    
    echo "<h2>4. التحقق من البنية الجديدة</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $new_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($new_columns as $column) {
        $color = '';
        if (in_array($column['Field'], ['price', 'unit_price', 'total_price'])) {
            $color = 'background-color: #d4edda;'; // أخضر فاتح
        }
        
        echo "<tr style='$color'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>5. اختبار إضافة منتج</h2>";
    
    // البحث عن جلسة نشطة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة - سأنشئ واحدة للاختبار</p>";
        
        // البحث عن جهاز
        $stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
        $device = $stmt->fetch();
        
        if ($device) {
            $pdo->exec("INSERT INTO sessions (device_id, status, start_time) VALUES ({$device['device_id']}, 'active', NOW())");
            $session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة</p>";
            exit;
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام الجلسة الموجودة: $session_id</p>";
    }
    
    // البحث عن منتج
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات - سأنشئ واحد للاختبار</p>";
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)");
        $product_id = $pdo->lastInsertId();
        $product_price = 5.00;
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_price = $product['price'];
        echo "<p style='color: blue;'>ℹ️ استخدام المنتج الموجود: {$product['name']} ($product_id)</p>";
    }
    
    // اختبار إضافة منتج بالطريقة الجديدة
    try {
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $quantity = 2;
        $total = $product_price * $quantity;
        $stmt->execute([$session_id, $product_id, $quantity, $product_price, $total]);
        
        $test_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إضافة منتج بنجاح برقم: $test_id</p>";
        echo "<p>التفاصيل: الكمية = $quantity، السعر = $product_price، الإجمالي = $total</p>";
        
        // حذف السجل التجريبي
        $pdo->exec("DELETE FROM session_products WHERE id = $test_id");
        echo "<p style='color: blue;'>ℹ️ تم حذف السجل التجريبي</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل في إضافة المنتج: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. الخلاصة</h2>";
    echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>تم إصلاح المشكلة بنجاح!</h3>";
    echo "<p><strong>المشكلة:</strong> حقل unit_price في جدول session_products لم يكن له قيمة افتراضية</p>";
    echo "<p><strong>الحل:</strong> تم إضافة قيمة افتراضية للحقل وتوحيد بنية الجدول</p>";
    echo "<p><strong>النتيجة:</strong> يمكن الآن إضافة المنتجات بدون أخطاء</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ حدث خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?>
