<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة المخزون
    if (!hasPagePermission('inventory')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة المخزون';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('inventory')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_cafeteria')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// Initialize variables
$page_title = "إدارة المخزن";
$active_page = "inventory";
$messages = [];

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // إضافة منتج جديد
    if (isset($_POST['add_product'])) {
        try {
            $name = trim($_POST['name']);
            $category = trim($_POST['category']);
            
            // التحقق من عدم وجود منتج بنفس الاسم
            $check_duplicate = $pdo->prepare('SELECT id FROM cafeteria_items WHERE name = ? AND client_id = ?');
            $check_duplicate->execute([$name, $client_id]);
            
            if ($check_duplicate->rowCount() > 0) {
                $messages['error'] = "يوجد منتج بنفس الاسم مسبقاً";
            } else {
                $stmt = $pdo->prepare('INSERT INTO cafeteria_items 
                    (name, price, cost_price, category, description, stock_quantity, min_stock_level, max_stock_level, barcode, supplier, client_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
                $stmt->execute([
                    $name,
                    floatval($_POST['price']),
                    floatval($_POST['cost_price']),
                    $category,
                    trim($_POST['description']),
                    intval($_POST['stock_quantity']),
                    intval($_POST['min_stock_level']),
                    intval($_POST['max_stock_level']),
                    trim($_POST['barcode']),
                    trim($_POST['supplier']),
                    $client_id
                ]);
                $_SESSION['success'] = "تم إضافة المنتج بنجاح";
                header('Location: inventory.php');
                exit;
            }
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء إضافة المنتج";
        }
    }
    
    // تحديث المخزن
    if (isset($_POST['update_stock'])) {
        try {
            $product_id = intval($_POST['product_id']);
            $new_quantity = intval($_POST['new_quantity']);
            $movement_type = $_POST['movement_type'];
            $notes = trim($_POST['notes']);

            $stmt = $pdo->prepare('UPDATE cafeteria_items SET stock_quantity = ? WHERE id = ? AND client_id = ?');
            $stmt->execute([$new_quantity, $product_id, $client_id]);

            $_SESSION['success'] = "تم تحديث المخزن بنجاح";
            header('Location: inventory.php');
            exit;
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء تحديث المخزن";
        }
    }

    // تعديل منتج
    if (isset($_POST['edit_product'])) {
        try {
            $product_id = intval($_POST['edit_id']);
            $name = trim($_POST['name']);
            $category = trim($_POST['category']);

            // التحقق من عدم وجود منتج آخر بنفس الاسم
            $check_duplicate = $pdo->prepare('SELECT id FROM cafeteria_items WHERE name = ? AND client_id = ? AND id != ?');
            $check_duplicate->execute([$name, $client_id, $product_id]);

            if ($check_duplicate->rowCount() > 0) {
                $messages['error'] = "يوجد منتج آخر بنفس الاسم";
            } else {
                $stmt = $pdo->prepare('UPDATE cafeteria_items SET
                    name = ?, price = ?, cost_price = ?, category = ?, description = ?,
                    min_stock_level = ?, max_stock_level = ?, barcode = ?, supplier = ?
                    WHERE id = ? AND client_id = ?');
                $stmt->execute([
                    $name,
                    floatval($_POST['price']),
                    floatval($_POST['cost_price']),
                    $category,
                    trim($_POST['description']),
                    intval($_POST['min_stock_level']),
                    intval($_POST['max_stock_level']),
                    trim($_POST['barcode']),
                    trim($_POST['supplier']),
                    $product_id,
                    $client_id
                ]);
                $_SESSION['success'] = "تم تحديث المنتج بنجاح";
                header('Location: inventory.php');
                exit;
            }
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء تحديث المنتج";
        }
    }
}

// معالجة الحذف
if (isset($_GET['delete'])) {
    try {
        $stmt = $pdo->prepare('DELETE FROM cafeteria_items WHERE id = ? AND client_id = ?');
        $stmt->execute([intval($_GET['delete']), $client_id]);
        $_SESSION['success'] = "تم حذف المنتج بنجاح";
        header('Location: inventory.php');
        exit;
    } catch (PDOException $e) {
        $messages['error'] = "حدث خطأ أثناء حذف المنتج";
    }
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? trim($_GET['category']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$where_conditions = ['client_id = ?'];
$params = [$client_id];

if (!empty($search)) {
    $where_conditions[] = '(name LIKE ? OR barcode LIKE ? OR supplier LIKE ?)';
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($category_filter)) {
    $where_conditions[] = 'category = ?';
    $params[] = $category_filter;
}

if (!empty($status_filter)) {
    switch ($status_filter) {
        case 'low_stock':
            $where_conditions[] = 'stock_quantity <= min_stock_level AND stock_quantity > 0';
            break;
        case 'out_of_stock':
            $where_conditions[] = 'stock_quantity <= 0';
            break;
        case 'available':
            $where_conditions[] = 'stock_quantity > min_stock_level';
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// جلب إجمالي عدد المنتجات
$count_sql = "SELECT COUNT(*) FROM cafeteria_items WHERE $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_products = $count_stmt->fetchColumn();
$total_pages = ceil($total_products / $limit);

// جلب المنتجات
$sql = "SELECT *, 
        CASE 
            WHEN stock_quantity <= 0 THEN 'نفذ'
            WHEN stock_quantity <= min_stock_level THEN 'ناقص'
            WHEN stock_quantity >= max_stock_level THEN 'مكتمل'
            ELSE 'متوفر'
        END as stock_status_text,
        CASE 
            WHEN stock_quantity <= 0 THEN 'danger'
            WHEN stock_quantity <= min_stock_level THEN 'warning'
            WHEN stock_quantity >= max_stock_level THEN 'info'
            ELSE 'success'
        END as stock_status_class,
        (price - cost_price) as profit_margin
        FROM cafeteria_items 
        WHERE $where_clause 
        ORDER BY name ASC 
        LIMIT $limit OFFSET $offset";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب التصنيفات
$categories_stmt = $pdo->prepare('SELECT DISTINCT category FROM cafeteria_items WHERE client_id = ? ORDER BY category');
$categories_stmt->execute([$client_id]);
$categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);

// إحصائيات سريعة
$stats_sql = "SELECT 
    COUNT(*) as total_products,
    SUM(CASE WHEN stock_quantity <= 0 THEN 1 ELSE 0 END) as out_of_stock,
    SUM(CASE WHEN stock_quantity <= min_stock_level AND stock_quantity > 0 THEN 1 ELSE 0 END) as low_stock,
    SUM(stock_quantity * cost_price) as total_inventory_value
    FROM cafeteria_items WHERE client_id = ?";
$stats_stmt = $pdo->prepare($stats_sql);
$stats_stmt->execute([$client_id]);
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid"><br>
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= number_format($stats['total_products']) ?></h4>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= number_format($stats['out_of_stock']) ?></h4>
                            <p class="mb-0">منتجات نفذت</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= number_format($stats['low_stock']) ?></h4>
                            <p class="mb-0">منتجات ناقصة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= number_format($stats['total_inventory_value'], 2) ?> ج.م</h4>
                            <p class="mb-0">قيمة المخزن</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" 
                           placeholder="البحث بالاسم أو الباركود أو المورد">
                </div>
                <div class="col-md-3">
                    <label class="form-label">التصنيف</label>
                    <select class="form-select" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= htmlspecialchars($category) ?>" 
                                    <?= $category_filter === $category ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة المخزن</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="available" <?= $status_filter === 'available' ? 'selected' : '' ?>>متوفر</option>
                        <option value="low_stock" <?= $status_filter === 'low_stock' ? 'selected' : '' ?>>ناقص</option>
                        <option value="out_of_stock" <?= $status_filter === 'out_of_stock' ? 'selected' : '' ?>>نفذ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>إدارة المخزن</h2>
        <div>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addProductModal">
                <i class="fas fa-plus"></i> إضافة منتج
            </button>
            <button class="btn btn-info" onclick="exportInventory()">
                <i class="fas fa-download"></i> تصدير
            </button>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (!empty($messages['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?= $messages['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- جدول المنتجات -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>المنتج</th>
                            <th>التصنيف</th>
                            <th>الكمية</th>
                            <th>الحالة</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>الربح</th>
                            <th>المورد</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($products)): ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد منتجات</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($product['name']) ?></strong>
                                            <?php if (!empty($product['barcode'])): ?>
                                                <br><small class="text-muted">
                                                    <i class="fas fa-barcode"></i> <?= htmlspecialchars($product['barcode']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($product['category']) ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2"><?= number_format($product['stock_quantity']) ?></span>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="updateStock(<?= $product['id'] ?>, '<?= htmlspecialchars($product['name']) ?>', <?= $product['stock_quantity'] ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            الحد الأدنى: <?= $product['min_stock_level'] ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $product['stock_status_class'] ?>">
                                            <?= $product['stock_status_text'] ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($product['cost_price'], 2) ?> ج.م</td>
                                    <td><?= number_format($product['price'], 2) ?> ج.م</td>
                                    <td>
                                        <span class="text-success">
                                            <?= number_format($product['profit_margin'], 2) ?> ج.م
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($product['supplier'] ?? '-') ?></td>
                                    <td><?= date('Y-m-d', strtotime($product['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="editProduct(<?= htmlspecialchars(json_encode($product)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteProduct(<?= $product['id'] ?>, '<?= htmlspecialchars($product['name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="صفحات المنتجات">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal إضافة منتج -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة منتج جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التصنيف *</label>
                                <input type="text" class="form-control" name="category" list="categories" required>
                                <datalist id="categories">
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= htmlspecialchars($category) ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">سعر التكلفة *</label>
                                <input type="number" class="form-control" name="cost_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">سعر البيع *</label>
                                <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الكمية الحالية *</label>
                                <input type="number" class="form-control" name="stock_quantity" min="0" value="0" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحد الأدنى</label>
                                <input type="number" class="form-control" name="min_stock_level" min="0" value="5">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى</label>
                                <input type="number" class="form-control" name="max_stock_level" min="0" value="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الباركود</label>
                                <input type="text" class="form-control" name="barcode">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المورد</label>
                                <input type="text" class="form-control" name="supplier">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_product" class="btn btn-success">إضافة المنتج</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تحديث المخزن -->
<div class="modal fade" id="updateStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث المخزن</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="product_id" id="update_product_id">
                    <div class="mb-3">
                        <label class="form-label">المنتج</label>
                        <input type="text" class="form-control" id="update_product_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الكمية الحالية</label>
                        <input type="number" class="form-control" id="current_quantity" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الكمية الجديدة *</label>
                        <input type="number" class="form-control" name="new_quantity" id="new_quantity" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الحركة</label>
                        <select class="form-select" name="movement_type">
                            <option value="in">إضافة للمخزن</option>
                            <option value="out">خصم من المخزن</option>
                            <option value="adjustment">تعديل المخزن</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_stock" class="btn btn-primary">تحديث المخزن</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل المنتج -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="edit_id" id="edit_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" name="name" id="edit_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التصنيف *</label>
                                <input type="text" class="form-control" name="category" id="edit_category" list="categories" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">سعر التكلفة *</label>
                                <input type="number" class="form-control" name="cost_price" id="edit_cost_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">سعر البيع *</label>
                                <input type="number" class="form-control" name="price" id="edit_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الكمية الحالية</label>
                                <input type="number" class="form-control" name="stock_quantity" id="edit_stock_quantity" min="0" readonly>
                                <small class="text-muted">استخدم زر تحديث المخزن لتغيير الكمية</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحد الأدنى</label>
                                <input type="number" class="form-control" name="min_stock_level" id="edit_min_stock_level" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى</label>
                                <input type="number" class="form-control" name="max_stock_level" id="edit_max_stock_level" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الباركود</label>
                                <input type="text" class="form-control" name="barcode" id="edit_barcode">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المورد</label>
                                <input type="text" class="form-control" name="supplier" id="edit_supplier">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" id="edit_description" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_product" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديث المخزن
function updateStock(productId, productName, currentQuantity) {
    document.getElementById('update_product_id').value = productId;
    document.getElementById('update_product_name').value = productName;
    document.getElementById('current_quantity').value = currentQuantity;
    document.getElementById('new_quantity').value = currentQuantity;

    new bootstrap.Modal(document.getElementById('updateStockModal')).show();
}

// تعديل المنتج
function editProduct(product) {
    document.getElementById('edit_id').value = product.id;
    document.getElementById('edit_name').value = product.name;
    document.getElementById('edit_category').value = product.category;
    document.getElementById('edit_cost_price').value = product.cost_price;
    document.getElementById('edit_price').value = product.price;
    document.getElementById('edit_stock_quantity').value = product.stock_quantity;
    document.getElementById('edit_min_stock_level').value = product.min_stock_level;
    document.getElementById('edit_max_stock_level').value = product.max_stock_level;
    document.getElementById('edit_barcode').value = product.barcode || '';
    document.getElementById('edit_supplier').value = product.supplier || '';
    document.getElementById('edit_description').value = product.description || '';

    new bootstrap.Modal(document.getElementById('editProductModal')).show();
}

// حذف المنتج
function deleteProduct(productId, productName) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
        window.location.href = `inventory.php?delete=${productId}`;
    }
}

// تصدير المخزن
function exportInventory() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`api/export_inventory.php?${params.toString()}`, '_blank');
}

// حساب الربح تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const costInputs = document.querySelectorAll('input[name="cost_price"]');
    const priceInputs = document.querySelectorAll('input[name="price"]');

    function calculateProfit(costInput, priceInput) {
        const cost = parseFloat(costInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const profit = price - cost;
        const profitPercentage = price > 0 ? ((profit / price) * 100).toFixed(2) : 0;

        // عرض الربح إذا كان هناك عنصر لعرضه
        const profitDisplay = costInput.closest('.modal-body')?.querySelector('.profit-display');
        if (profitDisplay) {
            profitDisplay.innerHTML = `الربح: ${profit.toFixed(2)} ج.م (${profitPercentage}%)`;
            profitDisplay.className = `profit-display ${profit >= 0 ? 'text-success' : 'text-danger'}`;
        }
    }

    // إضافة مستمعي الأحداث
    costInputs.forEach(input => {
        input.addEventListener('input', function() {
            const priceInput = this.closest('.modal-body')?.querySelector('input[name="price"]');
            if (priceInput) calculateProfit(this, priceInput);
        });
    });

    priceInputs.forEach(input => {
        input.addEventListener('input', function() {
            const costInput = this.closest('.modal-body')?.querySelector('input[name="cost_price"]');
            if (costInput) calculateProfit(costInput, this);
        });
    });
});

// تحديث تلقائي للصفحة كل 5 دقائق لعرض التحديثات
setInterval(function() {
    // تحديث الإحصائيات فقط دون إعادة تحميل الصفحة
    fetch('api/get_inventory_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الإحصائيات في الصفحة
                document.querySelector('.bg-primary h4').textContent = data.stats.total_products;
                document.querySelector('.bg-danger h4').textContent = data.stats.out_of_stock;
                document.querySelector('.bg-warning h4').textContent = data.stats.low_stock;
                document.querySelector('.bg-success h4').textContent = data.stats.total_inventory_value + ' ج.م';
            }
        })
        .catch(error => console.log('خطأ في تحديث الإحصائيات:', error));
}, 300000); // كل 5 دقائق
</script>

<?php include 'includes/footer.php'; ?>
