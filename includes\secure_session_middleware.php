<?php
/**
 * Middleware للتحقق من الجلسات الآمنة
 * يجب تضمين هذا الملف في بداية كل صفحة محمية
 * PlayGood Gaming Center Management System
 */

// منع الوصول المباشر
if (!defined('SECURE_SESSION_MIDDLEWARE')) {
    define('SECURE_SESSION_MIDDLEWARE', true);
}

// تضمين نظام الحماية
require_once __DIR__ . '/session_security.php';

/**
 * فئة Middleware للجلسات الآمنة
 */
class SecureSessionMiddleware {
    
    private static $instance = null;
    private $sessionSecurity;
    private $pdo;
    
    private function __construct($pdo) {
        $this->pdo = $pdo;
        $this->sessionSecurity = new SessionSecurityManager($pdo);
        $this->initSecureSession();
    }
    
    /**
     * الحصول على مثيل واحد من الكلاس (Singleton)
     */
    public static function getInstance($pdo) {
        if (self::$instance === null) {
            self::$instance = new self($pdo);
        }
        return self::$instance;
    }
    
    /**
     * تهيئة الجلسة الآمنة
     */
    private function initSecureSession() {
        // إعدادات الجلسة الآمنة
        if (session_status() === PHP_SESSION_NONE) {
            // إعدادات أمان متقدمة
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
            ini_set('session.use_only_cookies', 1);
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.gc_maxlifetime', 3600); // ساعة واحدة
            ini_set('session.gc_probability', 1);
            ini_set('session.gc_divisor', 100);
            
            // منع تثبيت الجلسة (Session Fixation)
            ini_set('session.use_trans_sid', 0);
            ini_set('session.use_strict_mode', 1);
            
            // تشفير معرف الجلسة
            ini_set('session.hash_function', 'sha256');
            ini_set('session.hash_bits_per_character', 6);
            
            session_start();
            
            // تجديد معرف الجلسة دورياً
            if (!isset($_SESSION['last_regeneration']) || 
                time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        }
    }
    
    /**
     * التحقق من صحة الجلسة للمدير
     */
    public function validateAdminSession() {
        if (!isset($_SESSION['admin_id'])) {
            $this->redirectToLogin('/admin/login.php');
            return false;
        }
        
        if (!$this->sessionSecurity->validateSession()) {
            $this->destroySessionAndRedirect('/admin/login.php', 'تم اكتشاف نشاط مشبوه. يرجى تسجيل الدخول مرة أخرى.');
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة الجلسة للعميل
     */
    public function validateClientSession() {
        if (!isset($_SESSION['client_id'])) {
            $this->redirectToLogin('/client/login.php');
            return false;
        }
        
        if (!$this->sessionSecurity->validateSession()) {
            $this->destroySessionAndRedirect('/client/login.php', 'تم اكتشاف نشاط مشبوه. يرجى تسجيل الدخول مرة أخرى.');
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة الجلسة للموظف
     */
    public function validateEmployeeSession() {
        if (!isset($_SESSION['employee_id'])) {
            $this->redirectToLogin('/client/employee-login.php');
            return false;
        }
        
        if (!$this->sessionSecurity->validateSession()) {
            $this->destroySessionAndRedirect('/client/employee-login.php', 'تم اكتشاف نشاط مشبوه. يرجى تسجيل الدخول مرة أخرى.');
            return false;
        }
        
        return true;
    }
    
    /**
     * إنشاء جلسة آمنة جديدة
     */
    public function createSecureSession($user_id, $user_type) {
        return $this->sessionSecurity->createSecureSession($user_id, $user_type);
    }
    
    /**
     * إنهاء الجلسة الآمنة
     */
    public function destroySecureSession() {
        return $this->sessionSecurity->destroySecureSession();
    }
    
    /**
     * إعادة التوجيه لصفحة تسجيل الدخول
     */
    private function redirectToLogin($login_url) {
        // للطلبات AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'انتهت صلاحية الجلسة',
                'redirect' => $login_url
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // للطلبات العادية
        header('Location: ' . $login_url);
        exit;
    }
    
    /**
     * إنهاء الجلسة وإعادة التوجيه مع رسالة
     */
    private function destroySessionAndRedirect($login_url, $message) {
        $this->destroySecureSession();
        
        // للطلبات AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $message,
                'redirect' => $login_url
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // للطلبات العادية
        session_start();
        $_SESSION['security_error'] = $message;
        header('Location: ' . $login_url);
        exit;
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanupExpiredSessions() {
        $this->sessionSecurity->cleanupExpiredSessions();
    }
    
    /**
     * الحصول على إحصائيات الأمان
     */
    public function getSecurityStats() {
        return $this->sessionSecurity->getSecurityStats();
    }
    
    /**
     * التحقق من وجود محاولات اختراق من IP معين
     */
    public function checkSuspiciousActivity($ip_address = null) {
        if ($ip_address === null) {
            $ip_address = $this->getClientIP();
        }
        
        try {
            // فحص محاولات الاختراق في آخر ساعة
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM session_security_logs 
                WHERE ip_address = ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute([$ip_address]);
            $attempts = $stmt->fetchColumn();
            
            // إذا كان هناك أكثر من 5 محاولات في الساعة
            if ($attempts > 5) {
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("خطأ في فحص النشاط المشبوه: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على IP الحقيقي للعميل
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * التحقق من جلسة المدير
 */
function requireAdminSession($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    return $middleware->validateAdminSession();
}

/**
 * التحقق من جلسة العميل
 */
function requireClientSession($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    return $middleware->validateClientSession();
}

/**
 * التحقق من جلسة الموظف
 */
function requireEmployeeSession($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    return $middleware->validateEmployeeSession();
}

/**
 * إنشاء جلسة آمنة
 */
function createSecureUserSession($pdo, $user_id, $user_type) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    return $middleware->createSecureSession($user_id, $user_type);
}

/**
 * إنهاء الجلسة الآمنة
 */
function destroySecureUserSession($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    return $middleware->destroySecureSession();
}

/**
 * تنظيف الجلسات المنتهية الصلاحية
 */
function cleanupExpiredUserSessions($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    $middleware->cleanupExpiredSessions();
}

// تشغيل تنظيف الجلسات تلقائياً (مرة واحدة كل 100 طلب)
if (mt_rand(1, 100) === 1 && isset($pdo)) {
    cleanupExpiredUserSessions($pdo);
}
