<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "تفاصيل العميل";
$active_page = "customers";

// التحقق من معرف العميل
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($customer_id <= 0) {
    $_SESSION['error'] = 'معرف العميل غير صحيح';
    header('Location: customers.php');
    exit;
}

try {
    // جلب بيانات العميل
    $customer_stmt = $pdo->prepare("
        SELECT c.*, 
               COUNT(DISTINCT s.session_id) as total_sessions,
               COUNT(DISTINCT CASE WHEN s.status = 'completed' THEN s.session_id END) as completed_sessions,
               COUNT(DISTINCT CASE WHEN s.status = 'active' THEN s.session_id END) as active_sessions,
               COALESCE(SUM(CASE WHEN i.invoice_id IS NOT NULL THEN i.total_cost ELSE 0 END), 0) as total_spent,
               MAX(s.created_at) as last_visit
        FROM customers c
        LEFT JOIN sessions s ON c.customer_id = s.customer_id
        LEFT JOIN invoices i ON s.session_id = i.session_id AND i.payment_status = 'paid'
        WHERE c.customer_id = ? AND c.client_id = ?
        GROUP BY c.customer_id
    ");
    $customer_stmt->execute([$customer_id, $client_id]);
    $customer = $customer_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$customer) {
        $_SESSION['error'] = 'العميل غير موجود';
        header('Location: customers.php');
        exit;
    }
    
    // جلب آخر الجلسات
    $sessions_stmt = $pdo->prepare("
        SELECT s.*, d.device_name, d.device_type, r.room_name,
               i.invoice_id, i.total_cost, i.payment_status,
               TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN invoices i ON s.session_id = i.session_id
        WHERE s.customer_id = ?
        ORDER BY s.created_at DESC
        LIMIT 10
    ");
    $sessions_stmt->execute([$customer_id]);
    $recent_sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'خطأ في جلب بيانات العميل';
    header('Location: customers.php');
    exit;
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- معلومات العميل الأساسية -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>معلومات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                            <?php echo strtoupper(substr($customer['name'], 0, 1)); ?>
                        </div>
                        <h4><?php echo htmlspecialchars($customer['name']); ?></h4>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong><i class="fas fa-phone me-2 text-success"></i>الهاتف:</strong>
                        <div><?php echo htmlspecialchars($customer['phone']); ?></div>
                    </div>
                    
                    <?php if ($customer['email']): ?>
                    <div class="info-item mb-3">
                        <strong><i class="fas fa-envelope me-2 text-info"></i>البريد الإلكتروني:</strong>
                        <div><?php echo htmlspecialchars($customer['email']); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="info-item mb-3">
                        <strong><i class="fas fa-calendar me-2 text-warning"></i>تاريخ التسجيل:</strong>
                        <div><?php echo date('Y-m-d', strtotime($customer['created_at'])); ?></div>
                    </div>
                    
                    <?php if ($customer['last_visit']): ?>
                    <div class="info-item mb-3">
                        <strong><i class="fas fa-clock me-2 text-secondary"></i>آخر زيارة:</strong>
                        <div><?php echo date('Y-m-d H:i', strtotime($customer['last_visit'])); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($customer['notes']): ?>
                    <div class="info-item mb-3">
                        <strong><i class="fas fa-sticky-note me-2 text-warning"></i>ملاحظات:</strong>
                        <div class="text-muted"><?php echo nl2br(htmlspecialchars($customer['notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="d-grid gap-2">
                        <a href="edit_customer.php?id=<?php echo $customer_id; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل البيانات
                        </a>
                        <a href="customers.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الإحصائيات والجلسات -->
        <div class="col-md-8">
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-play-circle fa-2x mb-2"></i>
                            <h4><?php echo $customer['total_sessions']; ?></h4>
                            <small>إجمالي الجلسات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h4><?php echo $customer['completed_sessions']; ?></h4>
                            <small>جلسات مكتملة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h4><?php echo $customer['active_sessions']; ?></h4>
                            <small>جلسات نشطة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-money-bill fa-2x mb-2"></i>
                            <h4><?php echo number_format($customer['total_spent'], 0); ?></h4>
                            <small>إجمالي المصروفات (ج.م)</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- آخر الجلسات -->
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>آخر الجلسات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_sessions)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الجهاز</th>
                                        <th>المدة</th>
                                        <th>التكلفة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_sessions as $session): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d H:i', strtotime($session['start_time'])); ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($session['device_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($session['device_type']); ?></small>
                                            </td>
                                            <td>
                                                <?php 
                                                if ($session['duration_minutes']) {
                                                    echo floor($session['duration_minutes'] / 60) . 'س ' . ($session['duration_minutes'] % 60) . 'د';
                                                } else {
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($session['total_cost']): ?>
                                                    <?php echo number_format($session['total_cost'], 2); ?> ج.م
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'active' => 'success',
                                                    'completed' => 'primary',
                                                    'cancelled' => 'danger'
                                                ];
                                                $status_texts = [
                                                    'active' => 'نشطة',
                                                    'completed' => 'مكتملة',
                                                    'cancelled' => 'ملغية'
                                                ];
                                                $class = $status_classes[$session['status']] ?? 'secondary';
                                                $text = $status_texts[$session['status']] ?? $session['status'];
                                                ?>
                                                <span class="badge bg-<?php echo $class; ?>"><?php echo $text; ?></span>
                                            </td>
                                            <td>
                                                <?php if ($session['invoice_id']): ?>
                                                    <a href="invoice.php?id=<?php echo $session['invoice_id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جلسات</h5>
                            <p class="text-muted">لم يقم هذا العميل بأي جلسات بعد</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
