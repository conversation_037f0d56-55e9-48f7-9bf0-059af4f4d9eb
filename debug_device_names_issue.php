<?php
require_once 'config/database.php';

echo "<h1>تشخيص مشكلة أسماء الأجهزة في الفواتير</h1>";

try {
    // 1. فحص البيانات الفعلية في الفواتير
    echo "<h2>1. البيانات الفعلية في الفواتير</h2>";
    
    $invoices_query = $pdo->prepare("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            s.customer_id,
            s.device_id,
            c.name as customer_name,
            d.device_name,
            d.device_type,
            d.device_id as actual_device_id
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = 1
        ORDER BY i.created_at DESC
    ");
    
    $invoices_query->execute();
    $invoices = $invoices_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Invoice ID</th><th>Session ID</th><th>Customer ID</th><th>Device ID</th><th>Customer Name</th><th>Device Name</th><th>Device Type</th>";
    echo "</tr>";
    
    foreach ($invoices as $invoice) {
        $customer_color = !empty($invoice['customer_name']) ? 'green' : 'red';
        $device_color = !empty($invoice['device_name']) ? 'green' : 'red';
        
        // التحقق من تطابق أسماء العملاء والأجهزة
        $name_conflict = ($invoice['customer_name'] === $invoice['device_name']) ? 'background: #ffcccc;' : '';
        
        echo "<tr style='$name_conflict'>";
        echo "<td>" . $invoice['invoice_id'] . "</td>";
        echo "<td>" . $invoice['session_id'] . "</td>";
        echo "<td>" . ($invoice['customer_id'] ?? 'NULL') . "</td>";
        echo "<td>" . $invoice['device_id'] . "</td>";
        echo "<td style='color: $customer_color;'>" . htmlspecialchars($invoice['customer_name'] ?? 'NULL') . "</td>";
        echo "<td style='color: $device_color;'>" . htmlspecialchars($invoice['device_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($invoice['device_type'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. فحص جدول الأجهزة مباشرة
    echo "<h2>2. فحص جدول الأجهزة مباشرة</h2>";
    
    $devices_query = $pdo->query("
        SELECT device_id, device_name, device_type, client_id
        FROM devices 
        WHERE client_id = 1
        ORDER BY device_id
    ");
    $devices = $devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Device ID</th><th>Device Name</th><th>Device Type</th><th>Client ID</th>";
    echo "</tr>";
    
    foreach ($devices as $device) {
        echo "<tr>";
        echo "<td>" . $device['device_id'] . "</td>";
        echo "<td>" . htmlspecialchars($device['device_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($device['device_type'] ?? 'NULL') . "</td>";
        echo "<td>" . $device['client_id'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. فحص جدول العملاء مباشرة
    echo "<h2>3. فحص جدول العملاء مباشرة</h2>";
    
    $customers_query = $pdo->query("
        SELECT customer_id, name, phone, client_id
        FROM customers 
        WHERE client_id = 1
        ORDER BY customer_id
    ");
    $customers = $customers_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Customer ID</th><th>Customer Name</th><th>Phone</th><th>Client ID</th>";
    echo "</tr>";
    
    foreach ($customers as $customer) {
        echo "<tr>";
        echo "<td>" . $customer['customer_id'] . "</td>";
        echo "<td>" . htmlspecialchars($customer['name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($customer['phone'] ?? 'NULL') . "</td>";
        echo "<td>" . $customer['client_id'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. تحليل المشكلة
    echo "<h2>4. تحليل المشكلة</h2>";
    
    $issues = [];
    
    foreach ($invoices as $invoice) {
        if ($invoice['customer_name'] === $invoice['device_name']) {
            $issues[] = "الفاتورة {$invoice['invoice_id']}: اسم العميل واسم الجهاز متطابقان ({$invoice['customer_name']})";
        }
        
        if (empty($invoice['device_name'])) {
            $issues[] = "الفاتورة {$invoice['invoice_id']}: اسم الجهاز فارغ";
        }
        
        if (empty($invoice['customer_name'])) {
            $issues[] = "الفاتورة {$invoice['invoice_id']}: اسم العميل فارغ";
        }
    }
    
    if (count($issues) > 0) {
        echo "<div style='background: #ffeeee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: red;'>❌ المشاكل المكتشفة:</h4>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #eeffee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: green;'>✅ لا توجد مشاكل في البيانات</h4>";
        echo "</div>";
    }
    
    // 5. اقتراح الحل
    echo "<h2>5. الحل المقترح</h2>";
    
    if (count($issues) > 0) {
        echo "<p>بناءً على التحليل، يبدو أن هناك مشكلة في البيانات. سأقوم بإصلاحها:</p>";
        
        // إصلاح أسماء الأجهزة إذا كانت تطابق أسماء العملاء
        foreach ($invoices as $invoice) {
            if ($invoice['customer_name'] === $invoice['device_name'] && !empty($invoice['device_type'])) {
                $new_device_name = $invoice['device_type'] . "_" . $invoice['device_id'];
                
                $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
                $result = $update_stmt->execute([$new_device_name, $invoice['device_id']]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ تم تحديث اسم الجهاز {$invoice['device_id']} من '{$invoice['device_name']}' إلى '$new_device_name'</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في تحديث اسم الجهاز {$invoice['device_id']}</p>";
                }
            }
        }
        
        echo "<p><strong>يرجى تحديث الصفحة لرؤية التغييرات.</strong></p>";
    } else {
        echo "<p style='color: green;'>البيانات تبدو صحيحة. إذا كانت المشكلة لا تزال موجودة، فقد تكون في طريقة العرض.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='test_names_display.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الأسماء</a>";
echo "</div>";
?>
