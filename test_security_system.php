<?php
/**
 * اختبار شامل لنظام الأمان
 * PlayGood Gaming Center Management System
 */

require_once 'config/database.php';
require_once 'includes/session_security.php';
require_once 'config/security_config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار النظام الأمني - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .test-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; }
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
    </style>
</head>
<body>
<div class='container py-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-10'>
            <div class='test-card p-4 shadow'>
                <h1 class='text-center mb-4'>
                    <i class='fas fa-shield-alt text-primary'></i>
                    اختبار النظام الأمني الشامل
                </h1>";

$tests = [];
$total_tests = 0;
$passed_tests = 0;

// اختبار 1: فحص جداول قاعدة البيانات
echo "<h3><i class='fas fa-database'></i> اختبار قاعدة البيانات</h3>";
try {
    $tables_to_check = ['secure_sessions', 'session_security_logs'];
    foreach ($tables_to_check as $table) {
        $total_tests++;
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='test-pass'><i class='fas fa-check'></i> جدول $table موجود</p>";
            $passed_tests++;
        } else {
            echo "<p class='test-fail'><i class='fas fa-times'></i> جدول $table غير موجود</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار 2: فحص إعدادات PHP
echo "<h3><i class='fas fa-cog'></i> اختبار إعدادات PHP</h3>";
$php_settings = [
    'session.cookie_httponly' => '1',
    'session.use_only_cookies' => '1',
    'session.cookie_samesite' => 'Strict',
    'session.use_strict_mode' => '1'
];

foreach ($php_settings as $setting => $expected) {
    $total_tests++;
    $actual = ini_get($setting);
    if ($actual == $expected) {
        echo "<p class='test-pass'><i class='fas fa-check'></i> $setting = $actual ✓</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-fail'><i class='fas fa-times'></i> $setting = $actual (متوقع: $expected)</p>";
    }
}

// اختبار 3: فحص الكلاسات والدوال
echo "<h3><i class='fas fa-code'></i> اختبار الكلاسات والدوال</h3>";
$classes_to_check = ['SessionSecurityManager', 'SecurityConfig'];
foreach ($classes_to_check as $class) {
    $total_tests++;
    if (class_exists($class)) {
        echo "<p class='test-pass'><i class='fas fa-check'></i> كلاس $class موجود</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-fail'><i class='fas fa-times'></i> كلاس $class غير موجود</p>";
    }
}

// اختبار 4: فحص الملفات المطلوبة
echo "<h3><i class='fas fa-file-code'></i> اختبار الملفات</h3>";
$files_to_check = [
    'includes/session_security.php',
    'includes/secure_session_middleware.php',
    'includes/auth_guard.php',
    'config/security_config.php'
];

foreach ($files_to_check as $file) {
    $total_tests++;
    if (file_exists($file)) {
        echo "<p class='test-pass'><i class='fas fa-check'></i> ملف $file موجود</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-fail'><i class='fas fa-times'></i> ملف $file غير موجود</p>";
    }
}

// اختبار 5: فحص Security Headers
echo "<h3><i class='fas fa-shield-alt'></i> اختبار Security Headers</h3>";
$expected_headers = [
    'X-Frame-Options' => 'DENY',
    'X-Content-Type-Options' => 'nosniff',
    'X-XSS-Protection' => '1; mode=block'
];

foreach ($expected_headers as $header => $expected_value) {
    $total_tests++;
    $headers = getallheaders();
    if (isset($headers[$header])) {
        echo "<p class='test-pass'><i class='fas fa-check'></i> Header $header موجود</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-warning'><i class='fas fa-exclamation-triangle'></i> Header $header غير موجود (قد يكون طبيعي في بيئة التطوير)</p>";
    }
}

// اختبار 6: فحص وظائف التشفير
echo "<h3><i class='fas fa-lock'></i> اختبار وظائف التشفير</h3>";
$total_tests++;
try {
    $test_password = 'TestPassword123!';
    $hashed = SecurityConfig::hashPassword($test_password);
    if (SecurityConfig::verifyPassword($test_password, $hashed)) {
        echo "<p class='test-pass'><i class='fas fa-check'></i> تشفير كلمات المرور يعمل بشكل صحيح</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في تشفير كلمات المرور</p>";
    }
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في اختبار التشفير: " . $e->getMessage() . "</p>";
}

// اختبار 7: فحص توليد الرموز الآمنة
$total_tests++;
try {
    $token = SecurityConfig::generateSecureToken(32);
    if (strlen($token) === 64 && ctype_xdigit($token)) { // 32 bytes = 64 hex chars
        echo "<p class='test-pass'><i class='fas fa-check'></i> توليد الرموز الآمنة يعمل بشكل صحيح</p>";
        $passed_tests++;
    } else {
        echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في توليد الرموز الآمنة</p>";
    }
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في اختبار توليد الرموز: " . $e->getMessage() . "</p>";
}

// اختبار 8: فحص SessionSecurityManager
echo "<h3><i class='fas fa-user-shield'></i> اختبار مدير الأمان</h3>";
$total_tests++;
try {
    $sessionManager = new SessionSecurityManager($pdo);
    echo "<p class='test-pass'><i class='fas fa-check'></i> تم إنشاء مدير الأمان بنجاح</p>";
    $passed_tests++;
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في إنشاء مدير الأمان: " . $e->getMessage() . "</p>";
}

// اختبار 9: فحص تنظيف المدخلات
$total_tests++;
$test_input = "<script>alert('xss')</script>";
$cleaned = SecurityConfig::sanitizeInput($test_input);
if ($cleaned !== $test_input && !strpos($cleaned, '<script>')) {
    echo "<p class='test-pass'><i class='fas fa-check'></i> تنظيف المدخلات يعمل بشكل صحيح</p>";
    $passed_tests++;
} else {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في تنظيف المدخلات</p>";
}

// اختبار 10: فحص التحقق من البريد الإلكتروني
$total_tests++;
$valid_email = SecurityConfig::validateEmail('<EMAIL>');
$invalid_email = SecurityConfig::validateEmail('invalid-email');
if ($valid_email && !$invalid_email) {
    echo "<p class='test-pass'><i class='fas fa-check'></i> التحقق من البريد الإلكتروني يعمل بشكل صحيح</p>";
    $passed_tests++;
} else {
    echo "<p class='test-fail'><i class='fas fa-times'></i> خطأ في التحقق من البريد الإلكتروني</p>";
}

// النتيجة النهائية
$success_rate = ($passed_tests / $total_tests) * 100;
echo "<hr>
<div class='text-center'>
    <h2>النتيجة النهائية</h2>
    <div class='row'>
        <div class='col-md-4'>
            <div class='card bg-primary text-white'>
                <div class='card-body'>
                    <h3>$total_tests</h3>
                    <p>إجمالي الاختبارات</p>
                </div>
            </div>
        </div>
        <div class='col-md-4'>
            <div class='card bg-success text-white'>
                <div class='card-body'>
                    <h3>$passed_tests</h3>
                    <p>اختبارات ناجحة</p>
                </div>
            </div>
        </div>
        <div class='col-md-4'>
            <div class='card bg-info text-white'>
                <div class='card-body'>
                    <h3>" . number_format($success_rate, 1) . "%</h3>
                    <p>معدل النجاح</p>
                </div>
            </div>
        </div>
    </div>
</div>";

if ($success_rate >= 90) {
    echo "<div class='alert alert-success mt-4'>
        <h4><i class='fas fa-check-circle'></i> ممتاز!</h4>
        <p>نظام الأمان يعمل بشكل ممتاز. جميع الاختبارات الأساسية نجحت.</p>
    </div>";
} elseif ($success_rate >= 70) {
    echo "<div class='alert alert-warning mt-4'>
        <h4><i class='fas fa-exclamation-triangle'></i> جيد</h4>
        <p>نظام الأمان يعمل بشكل جيد، لكن هناك بعض النقاط التي تحتاج تحسين.</p>
    </div>";
} else {
    echo "<div class='alert alert-danger mt-4'>
        <h4><i class='fas fa-times-circle'></i> يحتاج تحسين</h4>
        <p>نظام الأمان يحتاج إلى مراجعة وإصلاح. يرجى مراجعة الاختبارات الفاشلة.</p>
    </div>";
}

echo "<div class='text-center mt-4'>
    <a href='security_test.php' class='btn btn-primary me-2'>
        <i class='fas fa-shield-alt'></i> اختبار الأمان التفاعلي
    </a>
    <a href='admin/security_monitor.php' class='btn btn-success me-2'>
        <i class='fas fa-monitor-waveform'></i> مراقبة الأمان
    </a>
    <a href='admin/login.php' class='btn btn-info'>
        <i class='fas fa-sign-in-alt'></i> دخول المدير
    </a>
</div>";

echo "</div></div></div></div></body></html>";
?>
