<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة المنسدلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            min-width: 250px;
            border-radius: 10px;
            padding: 0.5rem 0;
            z-index: 1050;
        }
        
        .dropdown-menu.show {
            display: block !important;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
        }
        
        .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(-2px);
        }
        
        .dropdown-header {
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            margin: -0.5rem 0 0.5rem 0;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-gamepad me-2"></i>مركز الألعاب
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                       id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="user-avatar me-2">أ</div>
                        <div class="d-flex flex-column">
                            <span class="fw-bold">أحمد محمد</span>
                            <small class="text-light opacity-75">صاحب المحل</small>
                        </div>
                    </a>
                    
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <div class="dropdown-header text-center">
                                <div class="user-avatar mx-auto mb-2">أ</div>
                                <h6 class="mb-0">مركز الألعاب</h6>
                                <small class="text-muted">أحمد محمد</small>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i>إعدادات المحل
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار القائمة المنسدلة</h5>
                    </div>
                    <div class="card-body">
                        <p>انقر على أيقونة المستخدم في الأعلى لاختبار القائمة المنسدلة.</p>
                        
                        <div class="alert alert-info">
                            <h6>خطوات الاختبار:</h6>
                            <ol>
                                <li>انقر على أيقونة المستخدم في الـ navbar</li>
                                <li>يجب أن تظهر القائمة المنسدلة</li>
                                <li>جرب النقر على الروابط</li>
                                <li>انقر خارج القائمة لإغلاقها</li>
                            </ol>
                        </div>
                        
                        <div id="testResults">
                            <h6>نتائج الاختبار:</h6>
                            <div id="bootstrapStatus" class="mb-2"></div>
                            <div id="dropdownStatus" class="mb-2"></div>
                            <div id="clickStatus" class="mb-2"></div>
                        </div>
                        
                        <button class="btn btn-primary" onclick="testDropdown()">
                            <i class="fas fa-test me-2"></i>اختبار القائمة يدوياً
                        </button>
                        
                        <button class="btn btn-success" onclick="fixDropdown()">
                            <i class="fas fa-wrench me-2"></i>إصلاح القائمة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkBootstrap();
            checkDropdownElements();
            initializeDropdown();
        });
        
        function checkBootstrap() {
            const statusDiv = document.getElementById('bootstrapStatus');
            if (typeof bootstrap !== 'undefined') {
                statusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> Bootstrap محمل بنجاح</span>';
            } else {
                statusDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> Bootstrap غير محمل</span>';
            }
        }
        
        function checkDropdownElements() {
            const statusDiv = document.getElementById('dropdownStatus');
            const dropdownToggle = document.querySelector('[data-bs-toggle="dropdown"]');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            
            if (dropdownToggle && dropdownMenu) {
                statusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> عناصر القائمة المنسدلة موجودة</span>';
            } else {
                statusDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> عناصر القائمة المنسدلة مفقودة</span>';
            }
        }
        
        function initializeDropdown() {
            const dropdownToggle = document.querySelector('[data-bs-toggle="dropdown"]');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            
            if (dropdownToggle && dropdownMenu) {
                // إضافة event listener للنقر
                dropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const clickStatusDiv = document.getElementById('clickStatus');
                    clickStatusDiv.innerHTML = '<span class="text-info"><i class="fas fa-mouse-pointer"></i> تم النقر على القائمة</span>';
                    
                    if (typeof bootstrap !== 'undefined') {
                        // استخدام Bootstrap
                        const dropdown = new bootstrap.Dropdown(dropdownToggle);
                        if (dropdownMenu.classList.contains('show')) {
                            dropdown.hide();
                        } else {
                            dropdown.show();
                        }
                    } else {
                        // حل بديل
                        if (dropdownMenu.classList.contains('show')) {
                            dropdownMenu.classList.remove('show');
                        } else {
                            dropdownMenu.classList.add('show');
                        }
                    }
                });
                
                // إغلاق القائمة عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
                        dropdownMenu.classList.remove('show');
                    }
                });
            }
        }
        
        function testDropdown() {
            const dropdownMenu = document.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.toggle('show');
                
                const clickStatusDiv = document.getElementById('clickStatus');
                clickStatusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> تم تشغيل القائمة يدوياً</span>';
            }
        }
        
        function fixDropdown() {
            const dropdownToggle = document.querySelector('[data-bs-toggle="dropdown"]');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            
            if (dropdownToggle && dropdownMenu) {
                // إزالة جميع event listeners القديمة
                const newToggle = dropdownToggle.cloneNode(true);
                dropdownToggle.parentNode.replaceChild(newToggle, dropdownToggle);
                
                // إضافة event listener جديد
                newToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    dropdownMenu.classList.toggle('show');
                    
                    const clickStatusDiv = document.getElementById('clickStatus');
                    clickStatusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> تم إصلاح القائمة وتشغيلها</span>';
                });
                
                alert('تم إصلاح القائمة المنسدلة! جرب النقر عليها الآن.');
            }
        }
    </script>
</body>
</html>
