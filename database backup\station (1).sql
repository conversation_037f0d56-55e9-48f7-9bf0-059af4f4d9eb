-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 21, 2025 at 08:29 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `station`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `CheckClientLimit` (IN `p_client_id` INT, IN `p_feature_type` VARCHAR(50), IN `p_feature_name` VARCHAR(100), OUT `p_limit_value` INT, OUT `p_current_usage` INT, OUT `p_can_add` BOOLEAN, OUT `p_message` VARCHAR(255))   BEGIN
    DECLARE v_plan_limit INT DEFAULT 0;
    DECLARE v_custom_limit INT DEFAULT NULL;
    DECLARE v_current_count INT DEFAULT 0;
    DECLARE v_client_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        SET p_can_add = FALSE;
        SET p_message = 'حدث خطأ أثناء التحقق من الحدود';
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
    END;

    -- التحقق من وجود العميل
    SELECT COUNT(*) INTO v_client_exists
    FROM clients
    WHERE client_id = p_client_id AND is_active = 1;

    IF v_client_exists = 0 THEN
        SET p_limit_value = 0;
        SET p_current_usage = 0;
        SET p_can_add = FALSE;
        SET p_message = 'العميل غير موجود أو غير نشط';
    ELSE
        -- جلب حد الخطة الأساسية من جدول subscription_plans
        SELECT
            CASE p_feature_type
                WHEN 'devices' THEN sp.max_devices
                WHEN 'employees' THEN sp.max_employees
                WHEN 'customers' THEN sp.max_customers
                WHEN 'products' THEN sp.max_products
                ELSE 0
            END INTO v_plan_limit
        FROM clients c
        JOIN subscription_plans sp ON c.subscription_plan = sp.plan_name
        WHERE c.client_id = p_client_id;

        -- جلب الحد المخصص إن وجد (من جدول client_plan_limits إذا كان موجوداً)
        SELECT custom_limit INTO v_custom_limit
        FROM client_plan_limits
        WHERE client_id = p_client_id
        AND feature_type = p_feature_type
        AND feature_name = p_feature_name
        AND is_active = 1
        AND (expires_at IS NULL OR expires_at > NOW())
        LIMIT 1;

        -- تحديد الحد النهائي
        SET p_limit_value = COALESCE(v_custom_limit, v_plan_limit, 0);

        -- حساب الاستخدام الحالي
        CASE p_feature_type
            WHEN 'devices' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM devices
                WHERE client_id = p_client_id AND status != 'inactive';
            WHEN 'products' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM cafeteria_items
                WHERE client_id = p_client_id AND status != 'discontinued';
            WHEN 'employees' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM employees
                WHERE client_id = p_client_id AND is_active = 1;
            WHEN 'customers' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM customers
                WHERE client_id = p_client_id AND is_active = 1;
            ELSE
                SET v_current_count = 0;
        END CASE;

        SET p_current_usage = v_current_count;

        -- تحديد إمكانية الإضافة
        IF p_limit_value = -1 THEN
            SET p_can_add = TRUE;
            SET p_message = 'غير محدود - يمكن الإضافة';
        ELSEIF p_limit_value = 0 THEN
            SET p_can_add = FALSE;
            SET p_message = 'غير مسموح بالإضافة';
        ELSEIF v_current_count < p_limit_value THEN
            SET p_can_add = TRUE;
            SET p_message = CONCAT('يمكن إضافة ', (p_limit_value - v_current_count), ' عنصر إضافي');
        ELSE
            SET p_can_add = FALSE;
            SET p_message = CONCAT('تم الوصول للحد الأقصى (', p_limit_value, ')');
        END IF;
    END IF;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `CleanupOldData` (IN `p_client_id` INT, IN `p_days_old` INT, OUT `p_success` BOOLEAN, OUT `p_message` VARCHAR(255))   BEGIN
    DECLARE v_deleted_sessions INT DEFAULT 0;
    DECLARE v_deleted_movements INT DEFAULT 0;
    DECLARE v_cutoff_date DATE;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء تنظيف البيانات';
    END;

    START TRANSACTION;

    SET v_cutoff_date = DATE_SUB(CURDATE(), INTERVAL p_days_old DAY);

    -- حذف الجلسات القديمة المدفوعة
    DELETE FROM sessions
    WHERE client_id = p_client_id
    AND DATE(start_time) < v_cutoff_date
    AND payment_status = 'paid';

    SET v_deleted_sessions = ROW_COUNT();

    -- حذف حركات المخزون القديمة
    DELETE FROM inventory_movements
    WHERE client_id = p_client_id
    AND DATE(created_at) < v_cutoff_date;

    SET v_deleted_movements = ROW_COUNT();

    SET p_success = TRUE;
    SET p_message = CONCAT('تم حذف ', v_deleted_sessions, ' جلسة و ', v_deleted_movements, ' حركة مخزون');
    COMMIT;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `EndSession` (IN `p_session_id` INT, IN `p_client_id` INT, OUT `p_success` BOOLEAN, OUT `p_message` VARCHAR(255), OUT `p_total_cost` DECIMAL(10,2))   BEGIN
    DECLARE v_device_id INT;
    DECLARE v_start_time TIMESTAMP;
    DECLARE v_hourly_rate DECIMAL(8,2);
    DECLARE v_duration_minutes INT;
    DECLARE v_time_cost DECIMAL(10,2);
    DECLARE v_products_cost DECIMAL(10,2);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء إنهاء الجلسة';
    END;
    
    START TRANSACTION;
    
    -- جلب بيانات الجلسة
    SELECT device_id, start_time, hourly_rate, products_cost
    INTO v_device_id, v_start_time, v_hourly_rate, v_products_cost
    FROM sessions 
    WHERE session_id = p_session_id AND client_id = p_client_id AND end_time IS NULL;
    
    IF v_device_id IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'الجلسة غير موجودة أو منتهية بالفعل';
        ROLLBACK;
    ELSE
        -- حساب المدة والتكلفة
        SET v_duration_minutes = TIMESTAMPDIFF(MINUTE, v_start_time, NOW());
        SET v_time_cost = (v_duration_minutes / 60.0) * v_hourly_rate;
        SET p_total_cost = v_time_cost + COALESCE(v_products_cost, 0);
        
        -- تحديث الجلسة
        UPDATE sessions 
        SET 
            end_time = NOW(),
            duration_minutes = v_duration_minutes,
            time_cost = v_time_cost,
            total_cost = p_total_cost
        WHERE session_id = p_session_id;
        
        -- تحديث حالة الجهاز
        UPDATE devices 
        SET status = 'available' 
        WHERE device_id = v_device_id;
        
        SET p_success = TRUE;
        SET p_message = 'تم إنهاء الجلسة بنجاح';
        COMMIT;
    END IF;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `GetClientStatistics` (IN `p_client_id` INT, IN `p_start_date` DATE, IN `p_end_date` DATE)   BEGIN
    DECLARE v_total_sessions INT DEFAULT 0;
    DECLARE v_total_revenue DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_total_expenses DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_net_profit DECIMAL(10,2) DEFAULT 0.00;
    
    -- حساب عدد الجلسات والإيرادات
    SELECT 
        COUNT(*),
        COALESCE(SUM(total_cost), 0)
    INTO v_total_sessions, v_total_revenue
    FROM sessions 
    WHERE client_id = p_client_id 
    AND DATE(start_time) BETWEEN p_start_date AND p_end_date
    AND payment_status = 'paid';
    
    -- حساب المصروفات
    SELECT COALESCE(SUM(amount), 0)
    INTO v_total_expenses
    FROM expenses 
    WHERE client_id = p_client_id 
    AND expense_date BETWEEN p_start_date AND p_end_date;
    
    -- حساب صافي الربح
    SET v_net_profit = v_total_revenue - v_total_expenses;
    
    -- إرجاع النتائج
    SELECT 
        p_client_id AS client_id,
        p_start_date AS start_date,
        p_end_date AS end_date,
        v_total_sessions AS total_sessions,
        v_total_revenue AS total_revenue,
        v_total_expenses AS total_expenses,
        v_net_profit AS net_profit;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `GrantDefaultPermissionsToClient` (IN `p_client_id` INT, OUT `p_success` BOOLEAN, OUT `p_message` VARCHAR(255))   BEGIN
    DECLARE v_client_exists INT DEFAULT 0;
    DECLARE v_permissions_count INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء منح الصلاحيات';
    END;

    START TRANSACTION;

    -- التحقق من وجود العميل
    SELECT COUNT(*) INTO v_client_exists
    FROM clients
    WHERE client_id = p_client_id;

    IF v_client_exists = 0 THEN
        SET p_success = FALSE;
        SET p_message = 'العميل غير موجود';
        ROLLBACK;
    ELSE
        -- منح الصلاحيات الافتراضية (إذا كان جدول client_page_permissions موجود)
        INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
        SELECT p_client_id, page_id, TRUE
        FROM client_pages
        WHERE is_default = TRUE AND is_active = TRUE;

        -- حساب عدد الصلاحيات الممنوحة
        SELECT ROW_COUNT() INTO v_permissions_count;

        SET p_success = TRUE;
        SET p_message = CONCAT('تم منح ', v_permissions_count, ' صلاحية افتراضية');
        COMMIT;
    END IF;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `StartNewSession` (IN `p_client_id` INT, IN `p_device_id` INT, IN `p_customer_id` INT, IN `p_session_type` ENUM('hourly','single','multi'), IN `p_game_type` VARCHAR(100), IN `p_players_count` INT, OUT `p_session_id` INT, OUT `p_success` BOOLEAN, OUT `p_message` VARCHAR(255))   BEGIN
    DECLARE v_device_status VARCHAR(20);
    DECLARE v_hourly_rate DECIMAL(8,2);
    DECLARE v_single_rate DECIMAL(10,2);
    DECLARE v_multi_rate DECIMAL(10,2);
    DECLARE v_session_rate DECIMAL(10,2);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء بدء الجلسة';
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
    END;
    
    START TRANSACTION;
    
    -- التحقق من حالة الجهاز
    SELECT status, hourly_rate, single_rate, multi_rate
    INTO v_device_status, v_hourly_rate, v_single_rate, v_multi_rate
    FROM devices 
    WHERE device_id = p_device_id AND client_id = p_client_id;
    
    IF v_device_status IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'الجهاز غير موجود';
        ROLLBACK;
    ELSEIF v_device_status != 'available' THEN
        SET p_success = FALSE;
        SET p_message = 'الجهاز غير متاح حالياً';
        ROLLBACK;
    ELSE
        -- تحديد السعر حسب نوع الجلسة
        CASE p_session_type
            WHEN 'hourly' THEN SET v_session_rate = v_hourly_rate;
            WHEN 'single' THEN SET v_session_rate = v_single_rate;
            WHEN 'multi' THEN SET v_session_rate = v_multi_rate;
            ELSE SET v_session_rate = v_hourly_rate;
        END CASE;
        
        -- إنشاء الجلسة
        INSERT INTO sessions (
            client_id, device_id, customer_id, session_type, 
            game_type, players_count, hourly_rate, start_time
        ) VALUES (
            p_client_id, p_device_id, p_customer_id, p_session_type,
            p_game_type, p_players_count, v_session_rate, NOW()
        );
        
        SET p_session_id = LAST_INSERT_ID();
        
        -- تحديث حالة الجهاز
        UPDATE devices 
        SET status = 'occupied' 
        WHERE device_id = p_device_id;
        
        SET p_success = TRUE;
        SET p_message = 'تم بدء الجلسة بنجاح';
        COMMIT;
    END IF;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateInventory` (IN `p_product_id` INT, IN `p_client_id` INT, IN `p_movement_type` ENUM('in','out','adjustment'), IN `p_quantity` INT, IN `p_unit_cost` DECIMAL(10,2), IN `p_reference_type` ENUM('purchase','sale','session','order','manual','system'), IN `p_reference_id` INT, IN `p_notes` TEXT, IN `p_created_by` INT, OUT `p_success` BOOLEAN, OUT `p_message` VARCHAR(255))   BEGIN
    DECLARE v_current_stock INT DEFAULT 0;
    DECLARE v_new_stock INT DEFAULT 0;
    DECLARE v_total_cost DECIMAL(10,2) DEFAULT 0.00;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء تحديث المخزون';
    END;
    
    START TRANSACTION;
    
    -- جلب المخزون الحالي
    SELECT stock_quantity INTO v_current_stock
    FROM cafeteria_items 
    WHERE id = p_product_id AND client_id = p_client_id;
    
    IF v_current_stock IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'المنتج غير موجود';
        ROLLBACK;
    ELSE
        -- حساب المخزون الجديد
        CASE p_movement_type
            WHEN 'in' THEN SET v_new_stock = v_current_stock + p_quantity;
            WHEN 'out' THEN SET v_new_stock = v_current_stock - p_quantity;
            WHEN 'adjustment' THEN SET v_new_stock = p_quantity;
        END CASE;
        
        -- التحقق من عدم وجود مخزون سالب
        IF v_new_stock < 0 THEN
            SET p_success = FALSE;
            SET p_message = 'لا يمكن أن يكون المخزون سالباً';
            ROLLBACK;
        ELSE
            SET v_total_cost = p_quantity * p_unit_cost;
            
            -- تسجيل حركة المخزون
            INSERT INTO inventory_movements (
                client_id, product_id, movement_type, quantity,
                previous_quantity, new_quantity, unit_cost, total_cost,
                reference_type, reference_id, notes, created_by
            ) VALUES (
                p_client_id, p_product_id, p_movement_type, p_quantity,
                v_current_stock, v_new_stock, p_unit_cost, v_total_cost,
                p_reference_type, p_reference_id, p_notes, p_created_by
            );
            
            -- تحديث المخزون
            UPDATE cafeteria_items 
            SET 
                stock_quantity = v_new_stock,
                last_restock_date = CASE WHEN p_movement_type = 'in' THEN NOW() ELSE last_restock_date END,
                status = CASE 
                    WHEN v_new_stock <= 0 THEN 'out_of_stock'
                    WHEN v_new_stock <= min_stock_level THEN 'low_stock'
                    ELSE 'available'
                END
            WHERE id = p_product_id;
            
            SET p_success = TRUE;
            SET p_message = 'تم تحديث المخزون بنجاح';
            COMMIT;
        END IF;
    END IF;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `additional_income`
--

CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الإيرادات الإضافية';

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المديرين';

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `email`, `password_hash`, `full_name`, `role`, `is_active`, `created_at`, `last_login`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'super_admin', 1, '2025-06-20 08:34:30', '2025-06-21 05:18:36');

-- --------------------------------------------------------

--
-- Table structure for table `admin_pages`
--

CREATE TABLE `admin_pages` (
  `page_id` int(11) NOT NULL,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للمديرين الجدد',
  `required_role` enum('super_admin','admin','any') DEFAULT 'any' COMMENT 'الدور المطلوب للوصول للصفحة',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الصفحات المتاحة في لوحة تحكم الإدمن';

--
-- Dumping data for table `admin_pages`
--

INSERT INTO `admin_pages` (`page_id`, `page_name`, `page_label`, `page_url`, `page_icon`, `category`, `description`, `is_active`, `is_default`, `required_role`, `created_at`) VALUES
(12, 'dashboard', 'لوحة التحكم الرئيسية', 'dashboard.php', 'fas fa-dashboard', 'main', 'لوحة التحكم الرئيسية مع الإحصائيات والمعلومات العامة', 1, 1, 'any', '2025-06-20 12:31:58'),
(13, 'profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة الملف الشخصي للإدمن وتعديل البيانات', 1, 1, 'any', '2025-06-20 12:31:58'),
(14, 'clients', 'إدارة العملاء', 'clients.php', 'fas fa-users', 'clients', 'عرض وإدارة قائمة العملاء وإضافة عملاء جدد', 1, 1, 'any', '2025-06-20 12:31:58'),
(15, 'client_permissions', 'صلاحيات العملاء', 'client_permissions.php', 'fas fa-user-shield', 'clients', 'إدارة صلاحيات الوصول للصفحات للعملاء', 1, 1, 'admin', '2025-06-20 12:31:58'),
(16, 'client_devices', 'أجهزة العملاء', 'client_devices.php', 'fas fa-desktop', 'clients', 'إدارة وعرض أجهزة العملاء المختلفة', 1, 1, 'any', '2025-06-20 12:31:58'),
(17, 'reports', 'التقارير والإحصائيات', 'reports.php', 'fas fa-chart-bar', 'reports', 'عرض التقارير المالية والإحصائيات المفصلة', 1, 1, 'any', '2025-06-20 12:31:58'),
(18, 'settings', 'إعدادات النظام', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام العامة والتحكم في الميزات', 1, 1, 'admin', '2025-06-20 12:31:58'),
(19, 'admin_permissions', 'صلاحيات المديرين', 'admin_permissions.php', 'fas fa-user-cog', 'admin', 'إدارة صلاحيات المديرين للوصول للصفحات', 1, 0, 'super_admin', '2025-06-20 12:31:58'),
(20, 'download_backup', 'تحميل النسخ الاحتياطية', 'download_backup.php', 'fas fa-download', 'system', 'تحميل ملفات النسخ الاحتياطية', 1, 0, 'admin', '2025-06-20 12:31:58'),
(21, 'test_client_devices', 'اختبار أجهزة العملاء', 'test_client_devices.php', 'fas fa-vial', 'testing', 'صفحة اختبار وظائف أجهزة العملاء', 1, 0, 'super_admin', '2025-06-20 12:31:58'),
(22, 'ensure_device_columns', 'صيانة أعمدة الأجهزة', 'ensure_device_columns.php', 'fas fa-tools', 'maintenance', 'صفحة صيانة وإصلاح أعمدة جدول الأجهزة', 1, 0, 'super_admin', '2025-06-20 12:31:58'),
(23, 'system_logs', 'سجلات النظام', 'system_logs.php', 'fas fa-file-alt', 'admin', 'عرض سجلات النظام والأنشطة (للتطوير المستقبلي)', 1, 0, 'super_admin', '2025-06-20 12:31:58'),
(24, 'admins', 'إدارة المديرين', 'admins.php', 'fas fa-user-tie', 'admin', 'إضافة وإدارة المديرين (للتطوير المستقبلي)', 1, 0, 'super_admin', '2025-06-20 12:31:58'),
(25, 'backup', 'إدارة النسخ الاحتياطية', 'backup.php', 'fas fa-hdd', 'system', 'إنشاء وإدارة النسخ الاحتياطية (للتطوير المستقبلي)', 1, 0, 'admin', '2025-06-20 12:31:58');

-- --------------------------------------------------------

--
-- Table structure for table `admin_page_permissions`
--

CREATE TABLE `admin_page_permissions` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول صلاحيات الوصول لصفحات الإدمن';

--
-- Dumping data for table `admin_page_permissions`
--

INSERT INTO `admin_page_permissions` (`id`, `admin_id`, `page_id`, `is_enabled`, `granted_by`, `granted_at`, `updated_at`) VALUES
(16, 1, 12, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(17, 1, 13, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(18, 1, 14, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(19, 1, 15, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(20, 1, 16, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(21, 1, 17, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(22, 1, 18, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(23, 1, 19, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(24, 1, 20, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(25, 1, 21, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(26, 1, 22, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(27, 1, 23, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(28, 1, 24, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58'),
(29, 1, 25, 1, NULL, '2025-06-20 12:31:58', '2025-06-20 12:31:58');

-- --------------------------------------------------------

--
-- Stand-in structure for view `admin_page_permissions_detailed`
-- (See below for the actual view)
--
CREATE TABLE `admin_page_permissions_detailed` (
`admin_id` int(11)
,`username` varchar(50)
,`full_name` varchar(100)
,`role` enum('super_admin','admin')
,`admin_active` tinyint(1)
,`page_id` int(11)
,`page_name` varchar(100)
,`page_label` varchar(200)
,`page_url` varchar(255)
,`page_icon` varchar(50)
,`category` varchar(50)
,`description` text
,`is_default` tinyint(1)
,`required_role` enum('super_admin','admin','any')
,`has_permission` int(4)
,`granted_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `admin_settings`
--

CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات النظام العامة';

--
-- Dumping data for table `admin_settings`
--

INSERT INTO `admin_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_description`, `created_at`, `updated_at`) VALUES
(1, 'system_name', 'نظام إدارة مراكز الألعاب', 'اسم النظام', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(2, 'system_version', '2.0', 'إصدار النظام', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(3, 'backup_enabled', '1', 'تفعيل النسخ الاحتياطي', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(4, 'system_maintenance', '0', 'وضع الصيانة', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(5, 'max_backup_files', '10', 'الحد الأقصى لملفات النسخ الاحتياطي', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(6, 'timezone', 'Asia/Riyadh', 'المنطقة الزمنية', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(7, 'language', 'ar', 'اللغة الافتراضية', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(8, 'currency', 'SAR', 'العملة الافتراضية', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(9, 'date_format', 'Y-m-d', 'تنسيق التاريخ', '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(10, 'time_format', 'H:i', 'تنسيق الوقت', '2025-06-20 08:34:30', '2025-06-20 08:34:30');

-- --------------------------------------------------------

--
-- Table structure for table `business_settings`
--

CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات العملاء';

-- --------------------------------------------------------

--
-- Table structure for table `cafeteria_items`
--

CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `category` varchar(100) DEFAULT NULL,
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `stock_quantity` int(11) DEFAULT 0 COMMENT 'الكمية المتوفرة',
  `min_stock_level` int(11) DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
  `max_stock_level` int(11) DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
  `unit` varchar(20) DEFAULT 'قطعة' COMMENT 'وحدة القياس',
  `barcode` varchar(100) DEFAULT NULL COMMENT 'الباركود',
  `supplier` varchar(200) DEFAULT NULL COMMENT 'المورد',
  `status` enum('available','low_stock','out_of_stock','discontinued') DEFAULT 'available',
  `image_url` varchar(500) DEFAULT NULL,
  `last_restock_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='منتجات الكافتيريا';

--
-- Dumping data for table `cafeteria_items`
--

INSERT INTO `cafeteria_items` (`id`, `client_id`, `category_id`, `name`, `description`, `price`, `category`, `cost_price`, `stock_quantity`, `min_stock_level`, `max_stock_level`, `unit`, `barcode`, `supplier`, `status`, `image_url`, `last_restock_date`, `created_at`, `updated_at`) VALUES
(2, 1, NULL, 'tea', '', 10.00, '1', 0.00, 0, 5, 100, 'قطعة', NULL, NULL, 'available', NULL, NULL, '2025-06-21 05:23:01', '2025-06-21 05:23:01');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-tag',
  `color` varchar(7) DEFAULT '#007bff',
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='فئات المنتجات';

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`category_id`, `client_id`, `name`, `description`, `icon`, `color`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(22, 1, '1', NULL, 'fas fa-tag', '#007bff', 0, 1, '2025-06-20 09:20:40', '2025-06-20 09:20:40');

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `is_active` tinyint(1) DEFAULT 1,
  `backup_enabled` tinyint(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العملاء (أصحاب المحلات)';

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`client_id`, `business_name`, `owner_name`, `email`, `phone`, `password_hash`, `address`, `city`, `subscription_plan`, `subscription_start`, `subscription_end`, `business_type`, `description`, `working_hours`, `is_active`, `backup_enabled`, `created_at`, `updated_at`) VALUES
(1, 'ahmedeltarek', 'ahmed', '<EMAIL>', '***********', '$2y$10$xLGf1BOu.yt9o.6ciFeXYeMkNuYu3z8tmJRQSPBU6YmFZ0wHgmOBy', '5', 'الجيزة', 'basic', '2025-06-20', '2025-07-20', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 1, 0, '2025-06-20 09:18:20', '2025-06-20 10:53:48');

-- --------------------------------------------------------

--
-- Table structure for table `client_pages`
--

CREATE TABLE `client_pages` (
  `page_id` int(11) NOT NULL,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صفحات النظام المتاحة للعملاء';

--
-- Dumping data for table `client_pages`
--

INSERT INTO `client_pages` (`page_id`, `page_name`, `page_label`, `page_url`, `page_icon`, `category`, `description`, `is_active`, `is_default`, `created_at`) VALUES
(1, 'dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', 1, 1, '2025-06-20 08:34:49'),
(2, 'profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', 1, 1, '2025-06-20 08:34:49'),
(3, 'devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', 1, 1, '2025-06-20 08:34:49'),
(4, 'sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', 1, 1, '2025-06-20 08:34:49'),
(5, 'customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', 1, 1, '2025-06-20 08:34:49'),
(6, 'cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا', 1, 0, '2025-06-20 08:34:49'),
(7, 'invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إدارة وطباعة الفواتير', 1, 1, '2025-06-20 08:34:49'),
(8, 'reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير مالية وإحصائية', 1, 1, '2025-06-20 08:34:49'),
(9, 'settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والمحل', 1, 1, '2025-06-20 08:34:49'),
(13, 'rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف', 1, 0, '2025-06-21 06:26:23'),
(15, 'check_active_sessions', 'فحص الجلسات النشطة', 'check_active_sessions.php', 'fas fa-search', 'sessions', 'فحص حالة الجلسات النشطة', 1, 0, '2025-06-21 06:26:23'),
(17, 'customer_details', 'تفاصيل العميل', 'customer_details.php', 'fas fa-user-circle', 'customers', 'عرض تفاصيل عميل محدد', 1, 0, '2025-06-21 06:26:23'),
(18, 'edit_customer', 'تعديل العميل', 'edit_customer.php', 'fas fa-user-edit', 'customers', 'تعديل بيانات العميل', 1, 0, '2025-06-21 06:26:23'),
(19, 'delete_customer', 'حذف العميل', 'delete_customer.php', 'fas fa-user-times', 'customers', 'حذف عميل من النظام', 1, 0, '2025-06-21 06:26:24'),
(21, 'orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات والأوردرات المستقلة', 1, 0, '2025-06-21 06:26:24'),
(22, 'employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة بيانات الموظفين', 1, 0, '2025-06-21 06:26:24'),
(23, 'employee_permissions', 'صلاحيات الموظفين', 'employee_permissions.php', 'fas fa-user-shield', 'permissions', 'إدارة صلاحيات الموظفين', 1, 0, '2025-06-21 06:26:24'),
(24, 'employee-login', 'تسجيل دخول الموظف', 'employee-login.php', 'fas fa-sign-in-alt', 'employees', 'صفحة تسجيل دخول الموظفين', 1, 0, '2025-06-21 06:26:24'),
(25, 'attendance', 'الحضور والانصراف', 'attendance.php', 'fas fa-user-check', 'attendance', 'تسجيل حضور وانصراف الموظفين', 1, 0, '2025-06-21 06:26:24'),
(26, 'quick_attendance', 'الحضور السريع', 'quick_attendance.php', 'fas fa-clock', 'attendance', 'تسجيل حضور سريع للموظفين', 1, 0, '2025-06-21 06:26:24'),
(27, 'shifts', 'إدارة الورديات', 'shifts.php', 'fas fa-calendar-alt', 'shifts', 'تنظيم ورديات العمل', 1, 0, '2025-06-21 06:26:24'),
(28, 'shift_reports', 'تقارير الورديات', 'shift_reports.php', 'fas fa-calendar-check', 'shifts', 'تقارير مفصلة عن الورديات', 1, 0, '2025-06-21 06:26:24'),
(29, 'finances', 'الإدارة المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة الأمور المالية العامة', 1, 0, '2025-06-21 06:26:24'),
(32, 'invoice', 'عرض الفاتورة', 'invoice.php', 'fas fa-file-invoice-dollar', 'invoices', 'عرض فاتورة محددة', 1, 0, '2025-06-21 06:26:24'),
(33, 'invoice_settings', 'إعدادات الفواتير', 'invoice_settings.php', 'fas fa-cog', 'invoices', 'تخصيص شكل ومحتوى الفواتير', 1, 0, '2025-06-21 06:26:24'),
(34, 'inventory', 'إدارة المخزون', 'inventory.php', 'fas fa-boxes', 'inventory', 'إدارة مخزون المنتجات', 1, 0, '2025-06-21 06:26:24'),
(35, 'reservations', 'الحجوزات', 'reservations.php', 'fas fa-calendar-check', 'reservations', 'إدارة حجوزات العملاء', 1, 0, '2025-06-21 06:26:24'),
(37, 'backup_handler', 'النسخ الاحتياطي', 'backup_handler.php', 'fas fa-database', 'backup', 'إدارة النسخ الاحتياطية', 1, 0, '2025-06-21 06:26:24');

-- --------------------------------------------------------

--
-- Table structure for table `client_page_permissions`
--

CREATE TABLE `client_page_permissions` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات صفحات العملاء';

--
-- Dumping data for table `client_page_permissions`
--

INSERT INTO `client_page_permissions` (`id`, `client_id`, `page_id`, `is_enabled`, `granted_by`, `granted_at`, `updated_at`) VALUES
(2, 1, 1, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(3, 1, 2, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(4, 1, 3, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(5, 1, 4, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(6, 1, 5, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(7, 1, 7, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(8, 1, 8, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(9, 1, 9, 1, 1, '2025-06-20 14:44:21', '2025-06-20 14:44:21'),
(10, 1, 6, 1, 1, '2025-06-21 05:22:44', '2025-06-21 05:22:44'),
(12, 1, 13, 1, 1, '2025-06-21 06:27:29', '2025-06-21 06:27:29'),
(13, 1, 29, 0, 1, '2025-06-21 06:27:48', '2025-06-21 06:28:03');

-- --------------------------------------------------------

--
-- Table structure for table `client_plan_limits`
--

CREATE TABLE `client_plan_limits` (
  `limit_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `custom_limit` int(11) DEFAULT -1 COMMENT 'حد مخصص يتجاوز حد الخطة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الحد المخصص',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الحد المخصص',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حدود العملاء المخصصة';

-- --------------------------------------------------------

--
-- Table structure for table `client_theme_settings`
--

CREATE TABLE `client_theme_settings` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `primary_color` varchar(7) DEFAULT '#0d6efd',
  `secondary_color` varchar(7) DEFAULT '#6c757d',
  `accent_color` varchar(7) DEFAULT '#20c997',
  `background_color` varchar(7) DEFAULT '#ffffff',
  `text_color` varchar(7) DEFAULT '#212529',
  `header_style` enum('top','sidebar') DEFAULT 'top',
  `sidebar_position` enum('right','left') DEFAULT 'right',
  `theme_mode` enum('light','dark','auto') DEFAULT 'light',
  `font_family` varchar(100) DEFAULT 'Cairo, sans-serif',
  `font_size` enum('small','medium','large') DEFAULT 'medium',
  `custom_css` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الثيم';

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `address` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `total_visits` int(11) DEFAULT 0,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `last_visit` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول عملاء المحل';

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`customer_id`, `client_id`, `name`, `phone`, `email`, `date_of_birth`, `gender`, `address`, `notes`, `total_visits`, `total_spent`, `last_visit`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'mazen', '***********', NULL, NULL, NULL, NULL, NULL, 0, 0.00, NULL, 1, '2025-06-21 05:16:53', '2025-06-21 05:16:53');

-- --------------------------------------------------------

--
-- Stand-in structure for view `customers_stats`
-- (See below for the actual view)
--
CREATE TABLE `customers_stats` (
`customer_id` int(11)
,`client_id` int(11)
,`business_name` varchar(200)
,`name` varchar(100)
,`phone` varchar(20)
,`email` varchar(100)
,`total_sessions` bigint(21)
,`total_spent` decimal(32,2)
,`avg_session_cost` decimal(14,6)
,`last_visit` timestamp
,`first_visit` timestamp
,`registration_date` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `devices`
--

CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC','Nintendo') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 0.00,
  `single_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الفردي',
  `multi_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الجماعي',
  `status` enum('available','occupied','maintenance','inactive') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `specifications` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'مواصفات الجهاز' CHECK (json_valid(`specifications`)),
  `maintenance_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأجهزة';

--
-- Dumping data for table `devices`
--

INSERT INTO `devices` (`device_id`, `client_id`, `device_name`, `device_type`, `hourly_rate`, `single_rate`, `multi_rate`, `status`, `room_id`, `specifications`, `maintenance_notes`, `created_at`, `updated_at`) VALUES
(1, 1, 'جهاز 1', 'PS4', 10.00, 10.00, 20.00, 'occupied', NULL, NULL, NULL, '2025-06-20 17:19:03', '2025-06-21 05:22:26'),
(2, 1, 'جهاز تجريبي', 'PS5', 15.00, 10.00, 20.00, 'occupied', NULL, NULL, NULL, '2025-06-21 05:34:05', '2025-06-21 05:55:45');

-- --------------------------------------------------------

--
-- Stand-in structure for view `devices_stats`
-- (See below for the actual view)
--
CREATE TABLE `devices_stats` (
`device_id` int(11)
,`client_id` int(11)
,`business_name` varchar(200)
,`device_name` varchar(100)
,`device_type` enum('PS4','PS5','Xbox','PC','Nintendo')
,`status` enum('available','occupied','maintenance','inactive')
,`hourly_rate` decimal(8,2)
,`total_sessions` bigint(21)
,`total_minutes` decimal(32,0)
,`total_revenue` decimal(32,2)
,`avg_session_duration` decimal(14,4)
,`last_used` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text DEFAULT NULL,
  `role` enum('manager','cashier','waiter','cleaner','technician') NOT NULL DEFAULT 'cashier',
  `salary` decimal(10,2) NOT NULL DEFAULT 0.00,
  `hire_date` date NOT NULL,
  `birth_date` date DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `emergency_phone` varchar(20) DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'صلاحيات مخصصة',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الموظفين';

-- --------------------------------------------------------

--
-- Table structure for table `employee_permissions`
--

CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات الموظفين';

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المصروفات';

-- --------------------------------------------------------

--
-- Table structure for table `expense_types`
--

CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-money-bill',
  `color` varchar(7) DEFAULT '#dc3545',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع المصروفات';

-- --------------------------------------------------------

--
-- Table structure for table `income_types`
--

CREATE TABLE `income_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-coins',
  `color` varchar(7) DEFAULT '#28a745',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الإيرادات';

-- --------------------------------------------------------

--
-- Stand-in structure for view `inventory_detailed`
-- (See below for the actual view)
--
CREATE TABLE `inventory_detailed` (
`id` int(11)
,`client_id` int(11)
,`business_name` varchar(200)
,`name` varchar(100)
,`description` text
,`price` decimal(10,2)
,`cost_price` decimal(10,2)
,`profit_per_unit` decimal(11,2)
,`profit_percentage` decimal(17,2)
,`stock_quantity` int(11)
,`min_stock_level` int(11)
,`max_stock_level` int(11)
,`stock_status` varchar(11)
,`status` enum('available','low_stock','out_of_stock','discontinued')
,`category_name` varchar(255)
,`barcode` varchar(100)
,`supplier` varchar(200)
,`last_restock_date` timestamp
,`created_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `inventory_movements`
--

CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','expired','damaged','returned') NOT NULL,
  `quantity` int(11) NOT NULL,
  `previous_quantity` int(11) NOT NULL,
  `new_quantity` int(11) NOT NULL,
  `unit_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `reference_type` enum('purchase','sale','session','order','manual','system') DEFAULT 'manual',
  `reference_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حركات المخزون';

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `session_id` int(11) DEFAULT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `time_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `products_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `payment_status` enum('pending','paid','cancelled','refunded') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الفواتير';

-- --------------------------------------------------------

--
-- Table structure for table `invoice_settings`
--

CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `company_logo` varchar(500) DEFAULT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `show_barcode` tinyint(1) DEFAULT 0,
  `auto_print` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الفواتير';

-- --------------------------------------------------------

--
-- Stand-in structure for view `monthly_financial_summary`
-- (See below for the actual view)
--
CREATE TABLE `monthly_financial_summary` (
`client_id` int(11)
,`year` int(4)
,`month` int(2)
,`type` varchar(7)
,`total_amount` decimal(32,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL,
  `permission_name` varchar(100) NOT NULL,
  `permission_label` varchar(200) NOT NULL,
  `permission_category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات النظام';

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`permission_id`, `permission_name`, `permission_label`, `permission_category`, `description`, `is_active`, `created_at`) VALUES
(1, 'dashboard_view', 'عرض لوحة التحكم', 'dashboard', 'صلاحية عرض لوحة التحكم الرئيسية', 1, '2025-06-20 08:34:30'),
(2, 'devices_view', 'عرض الأجهزة', 'devices', 'صلاحية عرض قائمة الأجهزة', 1, '2025-06-20 08:34:30'),
(3, 'devices_add', 'إضافة أجهزة', 'devices', 'صلاحية إضافة أجهزة جديدة', 1, '2025-06-20 08:34:30'),
(4, 'devices_edit', 'تعديل الأجهزة', 'devices', 'صلاحية تعديل بيانات الأجهزة', 1, '2025-06-20 08:34:30'),
(5, 'devices_delete', 'حذف الأجهزة', 'devices', 'صلاحية حذف الأجهزة', 1, '2025-06-20 08:34:30'),
(6, 'sessions_view', 'عرض الجلسات', 'sessions', 'صلاحية عرض جلسات اللعب', 1, '2025-06-20 08:34:30'),
(7, 'sessions_start', 'بدء الجلسات', 'sessions', 'صلاحية بدء جلسات جديدة', 1, '2025-06-20 08:34:30'),
(8, 'sessions_end', 'إنهاء الجلسات', 'sessions', 'صلاحية إنهاء الجلسات', 1, '2025-06-20 08:34:30'),
(9, 'customers_view', 'عرض العملاء', 'customers', 'صلاحية عرض قائمة العملاء', 1, '2025-06-20 08:34:30'),
(10, 'customers_add', 'إضافة عملاء', 'customers', 'صلاحية إضافة عملاء جدد', 1, '2025-06-20 08:34:30'),
(11, 'customers_edit', 'تعديل العملاء', 'customers', 'صلاحية تعديل بيانات العملاء', 1, '2025-06-20 08:34:30'),
(12, 'customers_delete', 'حذف العملاء', 'customers', 'صلاحية حذف العملاء', 1, '2025-06-20 08:34:30'),
(13, 'products_view', 'عرض المنتجات', 'products', 'صلاحية عرض منتجات الكافتيريا', 1, '2025-06-20 08:34:30'),
(14, 'products_add', 'إضافة منتجات', 'products', 'صلاحية إضافة منتجات جديدة', 1, '2025-06-20 08:34:30'),
(15, 'products_edit', 'تعديل المنتجات', 'products', 'صلاحية تعديل المنتجات', 1, '2025-06-20 08:34:30'),
(16, 'products_delete', 'حذف المنتجات', 'products', 'صلاحية حذف المنتجات', 1, '2025-06-20 08:34:30'),
(17, 'invoices_view', 'عرض الفواتير', 'invoices', 'صلاحية عرض الفواتير', 1, '2025-06-20 08:34:30'),
(18, 'invoices_create', 'إنشاء فواتير', 'invoices', 'صلاحية إنشاء فواتير جديدة', 1, '2025-06-20 08:34:30'),
(19, 'reports_view', 'عرض التقارير', 'reports', 'صلاحية عرض التقارير المالية', 1, '2025-06-20 08:34:30'),
(20, 'settings_view', 'عرض الإعدادات', 'settings', 'صلاحية عرض إعدادات النظام', 1, '2025-06-20 08:34:30'),
(21, 'settings_edit', 'تعديل الإعدادات', 'settings', 'صلاحية تعديل إعدادات النظام', 1, '2025-06-20 08:34:30');

-- --------------------------------------------------------

--
-- Table structure for table `rooms`
--

CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_description` text DEFAULT NULL,
  `capacity` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الغرف';

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `device_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_type` enum('hourly','single','multi') DEFAULT 'hourly',
  `game_type` varchar(100) DEFAULT NULL,
  `players_count` int(11) DEFAULT 1,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` timestamp NULL DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `hourly_rate` decimal(8,2) DEFAULT 0.00,
  `time_cost` decimal(10,2) DEFAULT 0.00,
  `products_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `updated_by` int(11) DEFAULT NULL,
  `expected_end_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول جلسات اللعب';

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`session_id`, `client_id`, `device_id`, `customer_id`, `session_type`, `game_type`, `players_count`, `start_time`, `end_time`, `duration_minutes`, `hourly_rate`, `time_cost`, `products_cost`, `total_cost`, `payment_status`, `notes`, `created_by`, `created_at`, `updated_at`, `status`, `updated_by`, `expected_end_time`) VALUES
(2, 1, 1, 1, 'hourly', 'single', 1, '2025-06-21 05:22:26', NULL, 0, 0.00, 0.00, 0.00, 0.00, 'pending', '', 1, '2025-06-21 05:22:26', '2025-06-21 06:18:38', 'active', 1, NULL),
(7, 1, 2, 1, 'hourly', 'multiplayer', 1, '2025-06-21 05:55:45', NULL, 0, 0.00, 0.00, 0.00, 0.00, 'pending', '', 1, '2025-06-21 05:55:45', '2025-06-21 06:00:42', 'active', 1, NULL);

-- --------------------------------------------------------

--
-- Stand-in structure for view `sessions_detailed`
-- (See below for the actual view)
--
CREATE TABLE `sessions_detailed` (
`session_id` int(11)
,`client_id` int(11)
,`business_name` varchar(200)
,`device_id` int(11)
,`device_name` varchar(100)
,`device_type` enum('PS4','PS5','Xbox','PC','Nintendo')
,`customer_id` int(11)
,`customer_name` varchar(100)
,`customer_phone` varchar(20)
,`session_type` enum('hourly','single','multi')
,`game_type` varchar(100)
,`players_count` int(11)
,`start_time` timestamp
,`end_time` timestamp
,`duration_minutes` int(11)
,`hourly_rate` decimal(8,2)
,`time_cost` decimal(10,2)
,`products_cost` decimal(10,2)
,`total_cost` decimal(10,2)
,`payment_status` enum('pending','paid','cancelled')
,`notes` text
,`created_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `session_products`
--

CREATE TABLE `session_products` (
  `id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL,
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='منتجات الجلسات';

--
-- Dumping data for table `session_products`
--

INSERT INTO `session_products` (`id`, `session_id`, `product_id`, `quantity`, `unit_price`, `total_price`, `notes`, `created_at`, `price`, `created_by`) VALUES
(13, 7, 2, 1, 0.00, 0.00, NULL, '2025-06-21 05:55:51', 10.00, NULL),
(17, 2, 2, 1, 0.00, 0.00, NULL, '2025-06-21 06:18:29', 10.00, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `plan_id` int(11) NOT NULL,
  `plan_name` varchar(100) NOT NULL,
  `plan_name_ar` varchar(100) NOT NULL,
  `plan_description` text DEFAULT NULL,
  `plan_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `plan_duration_days` int(11) NOT NULL DEFAULT 30,
  `max_devices` int(11) DEFAULT 5,
  `max_employees` int(11) DEFAULT 3,
  `max_customers` int(11) DEFAULT 100,
  `max_products` int(11) DEFAULT 50,
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='خطط الاشتراك';

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`plan_id`, `plan_name`, `plan_name_ar`, `plan_description`, `plan_price`, `plan_duration_days`, `max_devices`, `max_employees`, `max_customers`, `max_products`, `features`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'basic', 'الخطة الأساسية', 'خطة مناسبة للمحلات الصغيرة', 99.00, 30, 5, 3, 100, 50, NULL, 1, '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(2, 'premium', 'الخطة المتقدمة', 'خطة مناسبة للمحلات المتوسطة', 199.00, 30, 15, 10, 500, 200, NULL, 1, '2025-06-20 08:34:30', '2025-06-20 08:34:30'),
(3, 'enterprise', 'خطة المؤسسات', 'خطة مناسبة للمحلات الكبيرة', 399.00, 30, -1, -1, -1, -1, NULL, 1, '2025-06-20 08:34:30', '2025-06-20 08:34:30');

-- --------------------------------------------------------

--
-- Structure for view `admin_page_permissions_detailed`
--
DROP TABLE IF EXISTS `admin_page_permissions_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `admin_page_permissions_detailed`  AS SELECT `a`.`admin_id` AS `admin_id`, `a`.`username` AS `username`, `a`.`full_name` AS `full_name`, `a`.`role` AS `role`, `a`.`is_active` AS `admin_active`, `ap`.`page_id` AS `page_id`, `ap`.`page_name` AS `page_name`, `ap`.`page_label` AS `page_label`, `ap`.`page_url` AS `page_url`, `ap`.`page_icon` AS `page_icon`, `ap`.`category` AS `category`, `ap`.`description` AS `description`, `ap`.`is_default` AS `is_default`, `ap`.`required_role` AS `required_role`, CASE WHEN `a`.`role` = 'super_admin' THEN 1 WHEN `ap`.`required_role` = 'super_admin' AND `a`.`role` <> 'super_admin' THEN 0 WHEN `ap`.`required_role` = 'admin' AND `a`.`role` not in ('super_admin','admin') THEN 0 ELSE coalesce(`app`.`is_enabled`,`ap`.`is_default`) END AS `has_permission`, `app`.`granted_at` AS `granted_at`, `app`.`updated_at` AS `updated_at` FROM ((`admins` `a` join `admin_pages` `ap`) left join `admin_page_permissions` `app` on(`a`.`admin_id` = `app`.`admin_id` and `ap`.`page_id` = `app`.`page_id`)) WHERE `ap`.`is_active` = 1 AND `a`.`is_active` = 1 ORDER BY `a`.`admin_id` ASC, `ap`.`category` ASC, `ap`.`page_label` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `customers_stats`
--
DROP TABLE IF EXISTS `customers_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `customers_stats`  AS SELECT `c`.`customer_id` AS `customer_id`, `c`.`client_id` AS `client_id`, `cl`.`business_name` AS `business_name`, `c`.`name` AS `name`, `c`.`phone` AS `phone`, `c`.`email` AS `email`, count(`s`.`session_id`) AS `total_sessions`, coalesce(sum(`s`.`total_cost`),0) AS `total_spent`, coalesce(avg(`s`.`total_cost`),0) AS `avg_session_cost`, max(`s`.`start_time`) AS `last_visit`, min(`s`.`start_time`) AS `first_visit`, `c`.`created_at` AS `registration_date` FROM ((`customers` `c` left join `clients` `cl` on(`c`.`client_id` = `cl`.`client_id`)) left join `sessions` `s` on(`c`.`customer_id` = `s`.`customer_id`)) GROUP BY `c`.`customer_id`, `c`.`client_id`, `cl`.`business_name`, `c`.`name`, `c`.`phone`, `c`.`email`, `c`.`created_at` ;

-- --------------------------------------------------------

--
-- Structure for view `devices_stats`
--
DROP TABLE IF EXISTS `devices_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `devices_stats`  AS SELECT `d`.`device_id` AS `device_id`, `d`.`client_id` AS `client_id`, `cl`.`business_name` AS `business_name`, `d`.`device_name` AS `device_name`, `d`.`device_type` AS `device_type`, `d`.`status` AS `status`, `d`.`hourly_rate` AS `hourly_rate`, count(`s`.`session_id`) AS `total_sessions`, coalesce(sum(`s`.`duration_minutes`),0) AS `total_minutes`, coalesce(sum(`s`.`total_cost`),0) AS `total_revenue`, coalesce(avg(`s`.`duration_minutes`),0) AS `avg_session_duration`, max(`s`.`start_time`) AS `last_used` FROM ((`devices` `d` left join `clients` `cl` on(`d`.`client_id` = `cl`.`client_id`)) left join `sessions` `s` on(`d`.`device_id` = `s`.`device_id`)) GROUP BY `d`.`device_id`, `d`.`client_id`, `cl`.`business_name`, `d`.`device_name`, `d`.`device_type`, `d`.`status`, `d`.`hourly_rate` ;

-- --------------------------------------------------------

--
-- Structure for view `inventory_detailed`
--
DROP TABLE IF EXISTS `inventory_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `inventory_detailed`  AS SELECT `ci`.`id` AS `id`, `ci`.`client_id` AS `client_id`, `cl`.`business_name` AS `business_name`, `ci`.`name` AS `name`, `ci`.`description` AS `description`, `ci`.`price` AS `price`, `ci`.`cost_price` AS `cost_price`, `ci`.`price`- `ci`.`cost_price` AS `profit_per_unit`, CASE WHEN `ci`.`cost_price` > 0 THEN round((`ci`.`price` - `ci`.`cost_price`) / `ci`.`cost_price` * 100,2) ELSE 0 END AS `profit_percentage`, `ci`.`stock_quantity` AS `stock_quantity`, `ci`.`min_stock_level` AS `min_stock_level`, `ci`.`max_stock_level` AS `max_stock_level`, CASE WHEN `ci`.`stock_quantity` <= 0 THEN 'نفد المخزون' WHEN `ci`.`stock_quantity` <= `ci`.`min_stock_level` THEN 'مخزون منخفض' WHEN `ci`.`stock_quantity` >= `ci`.`max_stock_level` THEN 'مخزون مرتفع' ELSE 'مخزون طبيعي' END AS `stock_status`, `ci`.`status` AS `status`, `cat`.`name` AS `category_name`, `ci`.`barcode` AS `barcode`, `ci`.`supplier` AS `supplier`, `ci`.`last_restock_date` AS `last_restock_date`, `ci`.`created_at` AS `created_at`, `ci`.`updated_at` AS `updated_at` FROM ((`cafeteria_items` `ci` left join `clients` `cl` on(`ci`.`client_id` = `cl`.`client_id`)) left join `categories` `cat` on(`ci`.`category_id` = `cat`.`category_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `monthly_financial_summary`
--
DROP TABLE IF EXISTS `monthly_financial_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `monthly_financial_summary`  AS SELECT `income_data`.`client_id` AS `client_id`, year(`income_data`.`income_date`) AS `year`, month(`income_data`.`income_date`) AS `month`, 'income' AS `type`, sum(`income_data`.`amount`) AS `total_amount` FROM (select `sessions`.`client_id` AS `client_id`,`sessions`.`created_at` AS `income_date`,`sessions`.`total_cost` AS `amount` from `sessions` where `sessions`.`payment_status` = 'paid' union all select `additional_income`.`client_id` AS `client_id`,`additional_income`.`income_date` AS `income_date`,`additional_income`.`amount` AS `amount` from `additional_income`) AS `income_data` GROUP BY `income_data`.`client_id`, year(`income_data`.`income_date`), month(`income_data`.`income_date`)union all select `expenses`.`client_id` AS `client_id`,year(`expenses`.`expense_date`) AS `year`,month(`expenses`.`expense_date`) AS `month`,'expense' AS `type`,sum(`expenses`.`amount`) AS `total_amount` from `expenses` group by `expenses`.`client_id`,year(`expenses`.`expense_date`),month(`expenses`.`expense_date`)  ;

-- --------------------------------------------------------

--
-- Structure for view `sessions_detailed`
--
DROP TABLE IF EXISTS `sessions_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `sessions_detailed`  AS SELECT `s`.`session_id` AS `session_id`, `s`.`client_id` AS `client_id`, `cl`.`business_name` AS `business_name`, `s`.`device_id` AS `device_id`, `d`.`device_name` AS `device_name`, `d`.`device_type` AS `device_type`, `s`.`customer_id` AS `customer_id`, `c`.`name` AS `customer_name`, `c`.`phone` AS `customer_phone`, `s`.`session_type` AS `session_type`, `s`.`game_type` AS `game_type`, `s`.`players_count` AS `players_count`, `s`.`start_time` AS `start_time`, `s`.`end_time` AS `end_time`, `s`.`duration_minutes` AS `duration_minutes`, `s`.`hourly_rate` AS `hourly_rate`, `s`.`time_cost` AS `time_cost`, `s`.`products_cost` AS `products_cost`, `s`.`total_cost` AS `total_cost`, `s`.`payment_status` AS `payment_status`, `s`.`notes` AS `notes`, `s`.`created_at` AS `created_at` FROM (((`sessions` `s` left join `clients` `cl` on(`s`.`client_id` = `cl`.`client_id`)) left join `devices` `d` on(`s`.`device_id` = `d`.`device_id`)) left join `customers` `c` on(`s`.`customer_id` = `c`.`customer_id`)) ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `additional_income`
--
ALTER TABLE `additional_income`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_income_client` (`client_id`),
  ADD KEY `idx_income_type` (`income_type_id`),
  ADD KEY `idx_income_date` (`income_date`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_admin_username` (`username`),
  ADD KEY `idx_admin_email` (`email`),
  ADD KEY `idx_admin_active` (`is_active`);

--
-- Indexes for table `admin_pages`
--
ALTER TABLE `admin_pages`
  ADD PRIMARY KEY (`page_id`),
  ADD UNIQUE KEY `page_name` (`page_name`),
  ADD KEY `idx_admin_pages_name` (`page_name`),
  ADD KEY `idx_admin_pages_category` (`category`),
  ADD KEY `idx_admin_pages_active` (`is_active`);

--
-- Indexes for table `admin_page_permissions`
--
ALTER TABLE `admin_page_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_admin_page` (`admin_id`,`page_id`),
  ADD KEY `granted_by` (`granted_by`),
  ADD KEY `idx_admin_page_permissions_admin` (`admin_id`),
  ADD KEY `idx_admin_page_permissions_page` (`page_id`);

--
-- Indexes for table `admin_settings`
--
ALTER TABLE `admin_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_setting_key` (`setting_key`);

--
-- Indexes for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `uk_business_setting` (`client_id`,`setting_key`),
  ADD KEY `idx_business_setting_client` (`client_id`);

--
-- Indexes for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_item_client` (`client_id`),
  ADD KEY `idx_item_category` (`category_id`),
  ADD KEY `idx_item_status` (`status`),
  ADD KEY `idx_item_barcode` (`barcode`),
  ADD KEY `idx_cafeteria_category` (`category`),
  ADD KEY `idx_cafeteria_client` (`client_id`),
  ADD KEY `idx_cafeteria_status` (`status`),
  ADD KEY `idx_cafeteria_items_client_id` (`client_id`),
  ADD KEY `idx_cafeteria_items_category` (`category`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `idx_category_client` (`client_id`),
  ADD KEY `idx_category_active` (`is_active`);

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`client_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_client_email` (`email`),
  ADD KEY `idx_client_phone` (`phone`),
  ADD KEY `idx_client_active` (`is_active`),
  ADD KEY `idx_client_subscription` (`subscription_plan`);

--
-- Indexes for table `client_pages`
--
ALTER TABLE `client_pages`
  ADD PRIMARY KEY (`page_id`),
  ADD UNIQUE KEY `uk_page_name` (`page_name`),
  ADD KEY `idx_page_active` (`is_active`),
  ADD KEY `idx_page_default` (`is_default`);

--
-- Indexes for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_client_page_permission` (`client_id`,`page_id`),
  ADD KEY `idx_client_page_perm_client` (`client_id`),
  ADD KEY `idx_client_page_perm_page` (`page_id`);

--
-- Indexes for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  ADD PRIMARY KEY (`limit_id`),
  ADD UNIQUE KEY `uk_client_feature_limit` (`client_id`,`feature_type`,`feature_name`),
  ADD KEY `idx_client_limits_client` (`client_id`),
  ADD KEY `idx_client_limits_active` (`is_active`);

--
-- Indexes for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_theme_client` (`client_id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`customer_id`),
  ADD KEY `idx_customer_client` (`client_id`),
  ADD KEY `idx_customer_phone` (`phone`),
  ADD KEY `idx_customer_active` (`is_active`);

--
-- Indexes for table `devices`
--
ALTER TABLE `devices`
  ADD PRIMARY KEY (`device_id`),
  ADD KEY `idx_device_client` (`client_id`),
  ADD KEY `idx_device_status` (`status`),
  ADD KEY `idx_device_type` (`device_type`),
  ADD KEY `idx_device_room` (`room_id`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_employee_username_client` (`client_id`,`username`),
  ADD KEY `idx_employee_client` (`client_id`),
  ADD KEY `idx_employee_role` (`role`),
  ADD KEY `idx_employee_active` (`is_active`);

--
-- Indexes for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_employee_permission` (`employee_id`,`permission_id`),
  ADD KEY `idx_emp_perm_employee` (`employee_id`),
  ADD KEY `idx_emp_perm_permission` (`permission_id`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expense_client` (`client_id`),
  ADD KEY `idx_expense_type` (`expense_type_id`),
  ADD KEY `idx_expense_date` (`expense_date`);

--
-- Indexes for table `expense_types`
--
ALTER TABLE `expense_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expense_type_client` (`client_id`),
  ADD KEY `idx_expense_type_active` (`is_active`);

--
-- Indexes for table `income_types`
--
ALTER TABLE `income_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_income_type_client` (`client_id`),
  ADD KEY `idx_income_type_active` (`is_active`);

--
-- Indexes for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_movement_client` (`client_id`),
  ADD KEY `idx_movement_product` (`product_id`),
  ADD KEY `idx_movement_type` (`movement_type`),
  ADD KEY `idx_movement_date` (`created_at`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`invoice_id`),
  ADD UNIQUE KEY `uk_invoice_number_client` (`client_id`,`invoice_number`),
  ADD KEY `idx_invoice_client` (`client_id`),
  ADD KEY `idx_invoice_session` (`session_id`),
  ADD KEY `idx_invoice_customer` (`customer_id`),
  ADD KEY `idx_invoice_status` (`payment_status`),
  ADD KEY `idx_invoice_date` (`created_at`);

--
-- Indexes for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_invoice_settings_client` (`client_id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `permission_name` (`permission_name`),
  ADD KEY `idx_permission_category` (`permission_category`),
  ADD KEY `idx_permission_active` (`is_active`);

--
-- Indexes for table `rooms`
--
ALTER TABLE `rooms`
  ADD PRIMARY KEY (`room_id`),
  ADD KEY `idx_room_client` (`client_id`),
  ADD KEY `idx_room_active` (`is_active`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `idx_session_client` (`client_id`),
  ADD KEY `idx_session_device` (`device_id`),
  ADD KEY `idx_session_customer` (`customer_id`),
  ADD KEY `idx_session_status` (`payment_status`),
  ADD KEY `idx_session_date` (`start_time`),
  ADD KEY `idx_sessions_status` (`status`),
  ADD KEY `idx_sessions_client_id` (`client_id`),
  ADD KEY `idx_sessions_device_id` (`device_id`),
  ADD KEY `idx_sessions_customer_id` (`customer_id`),
  ADD KEY `idx_sessions_start_time` (`start_time`);

--
-- Indexes for table `session_products`
--
ALTER TABLE `session_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_session_product_session` (`session_id`),
  ADD KEY `idx_session_product_product` (`product_id`),
  ADD KEY `idx_session_products_session_id` (`session_id`),
  ADD KEY `idx_session_products_product_id` (`product_id`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`plan_id`),
  ADD UNIQUE KEY `plan_name` (`plan_name`),
  ADD KEY `idx_plan_name` (`plan_name`),
  ADD KEY `idx_plan_active` (`is_active`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `additional_income`
--
ALTER TABLE `additional_income`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_pages`
--
ALTER TABLE `admin_pages`
  MODIFY `page_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `admin_page_permissions`
--
ALTER TABLE `admin_page_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `admin_settings`
--
ALTER TABLE `admin_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `business_settings`
--
ALTER TABLE `business_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `clients`
--
ALTER TABLE `clients`
  MODIFY `client_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `client_pages`
--
ALTER TABLE `client_pages`
  MODIFY `page_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  MODIFY `limit_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `devices`
--
ALTER TABLE `devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expense_types`
--
ALTER TABLE `expense_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `income_types`
--
ALTER TABLE `income_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `rooms`
--
ALTER TABLE `rooms`
  MODIFY `room_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sessions`
--
ALTER TABLE `sessions`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `session_products`
--
ALTER TABLE `session_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `additional_income`
--
ALTER TABLE `additional_income`
  ADD CONSTRAINT `fk_income_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_income_type` FOREIGN KEY (`income_type_id`) REFERENCES `income_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_page_permissions`
--
ALTER TABLE `admin_page_permissions`
  ADD CONSTRAINT `admin_page_permissions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_page_permissions_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `admin_pages` (`page_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_page_permissions_ibfk_3` FOREIGN KEY (`granted_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD CONSTRAINT `fk_business_setting_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  ADD CONSTRAINT `fk_item_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_item_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `fk_category_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  ADD CONSTRAINT `fk_client_page_perm_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_client_page_perm_page` FOREIGN KEY (`page_id`) REFERENCES `client_pages` (`page_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  ADD CONSTRAINT `fk_client_limits_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  ADD CONSTRAINT `fk_theme_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `fk_customer_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `devices`
--
ALTER TABLE `devices`
  ADD CONSTRAINT `fk_device_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_device_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE SET NULL;

--
-- Constraints for table `employees`
--
ALTER TABLE `employees`
  ADD CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  ADD CONSTRAINT `fk_emp_perm_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_emp_perm_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE;

--
-- Constraints for table `expenses`
--
ALTER TABLE `expenses`
  ADD CONSTRAINT `fk_expense_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_expense_type` FOREIGN KEY (`expense_type_id`) REFERENCES `expense_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `expense_types`
--
ALTER TABLE `expense_types`
  ADD CONSTRAINT `fk_expense_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `income_types`
--
ALTER TABLE `income_types`
  ADD CONSTRAINT `fk_income_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD CONSTRAINT `fk_movement_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_movement_product` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `fk_invoice_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_invoice_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_invoice_session` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL;

--
-- Constraints for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  ADD CONSTRAINT `fk_invoice_settings_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `rooms`
--
ALTER TABLE `rooms`
  ADD CONSTRAINT `fk_room_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `fk_session_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_session_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_session_device` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL;

--
-- Constraints for table `session_products`
--
ALTER TABLE `session_products`
  ADD CONSTRAINT `fk_session_product_product` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_session_product_session` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
