<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../includes/security.php';
require_once '../includes/intrusion_detection.php';

// التحقق من صلاحيات الإدمن
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = 'لوحة مراقبة الأمان';

// جلب إحصائيات الأمان
$threat_stats = $ids->getThreatStatistics(30);

// جلب آخر التهديدات
try {
    $recent_threats_stmt = $pdo->prepare("
        SELECT * FROM detected_threats 
        ORDER BY detected_at DESC 
        LIMIT 20
    ");
    $recent_threats_stmt->execute();
    $recent_threats = $recent_threats_stmt->fetchAll();
} catch (PDOException $e) {
    $recent_threats = [];
}

// جلب IPs المحظورة
try {
    $blocked_ips_stmt = $pdo->prepare("
        SELECT * FROM blocked_ips 
        WHERE permanent = TRUE OR blocked_until > NOW()
        ORDER BY blocked_at DESC 
        LIMIT 50
    ");
    $blocked_ips_stmt->execute();
    $blocked_ips = $blocked_ips_stmt->fetchAll();
} catch (PDOException $e) {
    $blocked_ips = [];
}

// جلب محاولات تسجيل الدخول الفاشلة
try {
    $failed_logins_stmt = $pdo->prepare("
        SELECT ip_address, username, user_type, COUNT(*) as attempts, 
               MAX(attempt_time) as last_attempt
        FROM login_attempts 
        WHERE success = FALSE AND attempt_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY ip_address, username, user_type
        ORDER BY attempts DESC, last_attempt DESC
        LIMIT 20
    ");
    $failed_logins_stmt->execute();
    $failed_logins = $failed_logins_stmt->fetchAll();
} catch (PDOException $e) {
    $failed_logins = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .security-card {
            border-left: 4px solid #dc3545;
            transition: all 0.3s ease;
        }
        .security-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .threat-level-critical { border-left-color: #dc3545; }
        .threat-level-high { border-left-color: #fd7e14; }
        .threat-level-medium { border-left-color: #ffc107; }
        .threat-level-low { border-left-color: #28a745; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .ip-blocked {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 5px 10px;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-shield-alt text-danger me-2"></i>
                        لوحة مراقبة الأمان
                    </h1>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><?php echo count($recent_threats); ?></h4>
                        <p class="mb-0">تهديدات اليوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-ban fa-2x mb-2"></i>
                        <h4><?php echo count($blocked_ips); ?></h4>
                        <p class="mb-0">IPs محظورة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-times fa-2x mb-2"></i>
                        <h4><?php echo count($failed_logins); ?></h4>
                        <p class="mb-0">محاولات دخول فاشلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h4><?php echo array_sum(array_column($threat_stats, 'count')); ?></h4>
                        <p class="mb-0">إجمالي التهديدات (30 يوم)</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- آخر التهديدات -->
            <div class="col-lg-6 mb-4">
                <div class="card security-card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            آخر التهديدات المكتشفة
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($recent_threats)): ?>
                            <p class="text-muted text-center">لا توجد تهديدات مكتشفة</p>
                        <?php else: ?>
                            <?php foreach ($recent_threats as $threat): ?>
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong class="text-<?php 
                                                echo $threat['threat_level'] === 'critical' ? 'danger' : 
                                                    ($threat['threat_level'] === 'high' ? 'warning' : 
                                                    ($threat['threat_level'] === 'medium' ? 'info' : 'success')); 
                                            ?>">
                                                <?php echo htmlspecialchars($threat['threat_type']); ?>
                                            </strong>
                                            <br>
                                            <small class="text-muted">
                                                IP: <?php echo htmlspecialchars($threat['ip_address']); ?>
                                            </small>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($threat['detected_at'])); ?>
                                        </small>
                                    </div>
                                    <p class="mb-0 small">
                                        <?php echo htmlspecialchars($threat['description']); ?>
                                    </p>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- IPs المحظورة -->
            <div class="col-lg-6 mb-4">
                <div class="card security-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-ban me-2"></i>
                            عناوين IP المحظورة
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($blocked_ips)): ?>
                            <p class="text-muted text-center">لا توجد عناوين IP محظورة</p>
                        <?php else: ?>
                            <?php foreach ($blocked_ips as $blocked_ip): ?>
                                <div class="ip-blocked mb-2">
                                    <strong><?php echo htmlspecialchars($blocked_ip['ip_address']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        السبب: <?php echo htmlspecialchars($blocked_ip['reason']); ?>
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <?php if ($blocked_ip['permanent']): ?>
                                            حظر دائم
                                        <?php else: ?>
                                            حتى: <?php echo date('Y-m-d H:i', strtotime($blocked_ip['blocked_until'])); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- محاولات تسجيل الدخول الفاشلة -->
        <div class="row">
            <div class="col-12">
                <div class="card security-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-times me-2"></i>
                            محاولات تسجيل الدخول الفاشلة (آخر 24 ساعة)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($failed_logins)): ?>
                            <p class="text-muted text-center">لا توجد محاولات دخول فاشلة</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>عنوان IP</th>
                                            <th>اسم المستخدم</th>
                                            <th>نوع المستخدم</th>
                                            <th>عدد المحاولات</th>
                                            <th>آخر محاولة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($failed_logins as $login): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($login['ip_address']); ?></td>
                                                <td><?php echo htmlspecialchars($login['username']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($login['user_type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger">
                                                        <?php echo $login['attempts']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i:s', strtotime($login['last_attempt'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
