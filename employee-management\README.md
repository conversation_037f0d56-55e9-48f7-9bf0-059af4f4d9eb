# Employee Management System

This project is an Employee Management System that allows users to manage employee records efficiently. 

## Features

- Add, update, and delete employee records.
- View employee details.
- Responsive design for better user experience.

## Project Structure

- `client/`: Contains the front-end files including HTML, CSS, and JavaScript.
- `server/`: Contains the back-end files including API and database configuration.
- `.htaccess`: Server configuration file for URL rewriting and access control.

## Setup Instructions

1. Clone the repository to your local machine.
2. Set up a local server environment (e.g., XAMPP).
3. Import the database schema provided in the `server/config/database.php`.
4. Update the database connection settings in `server/config/database.php`.
5. Access the application via your web browser at `http://localhost/employee-management/client/employees.php`.

## Technologies Used

- PHP
- MySQL
- HTML/CSS
- JavaScript

## Author

[Your Name] - [Your Contact Information]