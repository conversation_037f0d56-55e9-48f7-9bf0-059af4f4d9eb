<?php
session_start();
require_once 'includes/trial-auth.php';

// التحقق من تسجيل الدخول
requireTrialLogin();

$trial_id = $_SESSION['trial_id'];
$business_name = $_SESSION['trial_business_name'];
$owner_name = $_SESSION['trial_owner_name'];

// الحصول على الوقت المتبقي
$timeRemaining = getTrialTimeRemaining($trial_id);

// الحصول على الإحصائيات
$stats = getTrialStats($trial_id);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم التجريبية - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            --danger-gradient: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .trial-navbar {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .trial-timer {
            background: var(--warning-gradient);
            color: #2d3748;
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .trial-timer.danger {
            background: var(--danger-gradient);
            color: white;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .trial-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 1rem;
        }

        .main-content {
            padding: 2rem 0;
        }

        .welcome-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .welcome-card h2 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .welcome-card p {
            color: #718096;
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-icon.blue { background: linear-gradient(135deg, #4299e1, #3182ce); }
        .stat-icon.green { background: linear-gradient(135deg, #48bb78, #38a169); }
        .stat-icon.purple { background: linear-gradient(135deg, #9f7aea, #805ad5); }
        .stat-icon.orange { background: linear-gradient(135deg, #ed8936, #dd6b20); }
        .stat-icon.red { background: linear-gradient(135deg, #f56565, #e53e3e); }
        .stat-icon.teal { background: linear-gradient(135deg, #38b2ac, #319795); }

        .stat-number {
            font-size: 2rem;
            font-weight: 900;
            color: #2d3748;
            line-height: 1;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h5 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #718096;
            margin-bottom: 1.5rem;
        }

        .btn-feature {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-feature:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-feature.disabled {
            background: #e2e8f0;
            color: #a0aec0;
            cursor: not-allowed;
        }

        .btn-feature.disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .trial-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .trial-info h4 {
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .trial-info p {
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .btn-upgrade {
            background: white;
            color: #667eea;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 700;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-upgrade:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
            color: #667eea;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 1rem 0;
            }
            
            .welcome-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الوقت المتبقي -->
    <div class="trial-timer <?php echo $timeRemaining['total_seconds'] < 1800 ? 'danger' : ''; ?>" id="trialTimer">
        <i class="fas fa-clock me-2"></i>
        الوقت المتبقي للتجربة: <span id="timeDisplay"><?php echo $timeRemaining['hours']; ?>:<?php echo sprintf('%02d', $timeRemaining['minutes']); ?>:<?php echo sprintf('%02d', $timeRemaining['seconds']); ?></span>
    </div>

    <!-- شريط التنقل -->
    <nav class="trial-navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <span class="navbar-brand">
                        <i class="fas fa-gamepad me-2"></i>
                        PlayGood
                    </span>
                    <span class="trial-badge">تجربة مجانية</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-3">مرحباً، <?php echo htmlspecialchars($owner_name); ?></span>
                    <a href="trial-logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <!-- بطاقة الترحيب -->
        <div class="welcome-card">
            <h2>مرحباً بك في <?php echo htmlspecialchars($business_name); ?></h2>
            <p>استكشف جميع مميزات PlayGood خلال فترة التجربة المجانية</p>
        </div>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-desktop"></i>
                </div>
                <div class="stat-number"><?php echo $stats['devices']; ?></div>
                <div class="stat-label">الأجهزة المتاحة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $stats['customers']; ?></div>
                <div class="stat-label">العملاء</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo $stats['active_sessions']; ?></div>
                <div class="stat-label">الجلسات النشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number"><?php echo $stats['total_sessions']; ?></div>
                <div class="stat-label">إجمالي الجلسات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon red">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_revenue'], 2); ?></div>
                <div class="stat-label">الإيرادات (جنيه)</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon teal">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-number"><?php echo $stats['cafeteria_items']; ?></div>
                <div class="stat-label">منتجات الكافتيريا</div>
            </div>
        </div>

        <!-- المميزات المتاحة -->
        <div class="features-grid">
            <div class="feature-card">
                <h5>
                    <i class="fas fa-desktop me-2 text-primary"></i>
                    إدارة الأجهزة
                </h5>
                <p>تتبع حالة الأجهزة، إضافة أجهزة جديدة، وإدارة الصيانة</p>
                <a href="trial-devices.php" class="btn-feature">
                    <i class="fas fa-cog me-1"></i>
                    إدارة الأجهزة
                </a>
            </div>

            <div class="feature-card">
                <h5>
                    <i class="fas fa-users me-2 text-success"></i>
                    إدارة العملاء
                </h5>
                <p>إضافة عملاء جدد، تعديل بيانات العملاء، وتتبع تاريخ الزيارات</p>
                <a href="trial-customers.php" class="btn-feature">
                    <i class="fas fa-user-plus me-1"></i>
                    إدارة العملاء
                </a>
            </div>

            <div class="feature-card">
                <h5>
                    <i class="fas fa-clock me-2 text-warning"></i>
                    الجلسات والحجوزات
                </h5>
                <p>بدء جلسات جديدة، تتبع الجلسات النشطة، وإدارة الحجوزات</p>
                <a href="trial-sessions.php" class="btn-feature">
                    <i class="fas fa-play me-1"></i>
                    إدارة الجلسات
                </a>
            </div>

            <div class="feature-card">
                <h5>
                    <i class="fas fa-coffee me-2 text-info"></i>
                    الكافتيريا
                </h5>
                <p>إدارة منتجات الكافتيريا، الأسعار، والطلبات</p>
                <a href="trial-cafeteria.php" class="btn-feature">
                    <i class="fas fa-utensils me-1"></i>
                    إدارة الكافتيريا
                </a>
            </div>
        </div>

        <!-- معلومات الترقية -->
        <div class="trial-info">
            <h4>
                <i class="fas fa-star me-2"></i>
                أعجبك النظام؟
            </h4>
            <p>احصل على النسخة الكاملة مع جميع المميزات المتقدمة وبدون قيود زمنية</p>
            <a href="register.php" class="btn-upgrade">
                <i class="fas fa-rocket me-2"></i>
                ترقية للنسخة الكاملة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عداد الوقت المتبقي
        let totalSeconds = <?php echo $timeRemaining['total_seconds']; ?>;
        
        function updateTimer() {
            if (totalSeconds <= 0) {
                alert('انتهت صلاحية التجربة المجانية!');
                window.location.href = 'trial-login.php?error=trial_expired';
                return;
            }
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            document.getElementById('timeDisplay').textContent = 
                hours + ':' + 
                (minutes < 10 ? '0' : '') + minutes + ':' + 
                (seconds < 10 ? '0' : '') + seconds;
            
            // تغيير لون المؤقت عند اقتراب انتهاء الوقت
            const timerElement = document.getElementById('trialTimer');
            if (totalSeconds < 1800) { // أقل من 30 دقيقة
                timerElement.classList.add('danger');
            }
            
            totalSeconds--;
        }
        
        // تحديث المؤقت كل ثانية
        setInterval(updateTimer, 1000);
    </script>
</body>
</html>
