<?php
/**
 * سكريپت إعادة استيراد قاعدة البيانات من ملف station.sql
 * يقوم بحذف الجداول الموجودة وإعادة إنشائها من الملف
 */

require_once 'config/database.php';

// دالة تقسيم استعلامات SQL بطريقة محسنة
function splitSQLQueries($sql) {
    // إزالة التعليقات
    $sql = preg_replace('/--.*$/m', '', $sql);
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);

    // تقسيم الاستعلامات مع مراعاة النصوص المحاطة بعلامات اقتباس
    $queries = [];
    $current_query = '';
    $in_string = false;
    $string_char = '';
    $escaped = false;

    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];

        if ($escaped) {
            $current_query .= $char;
            $escaped = false;
            continue;
        }

        if ($char === '\\') {
            $current_query .= $char;
            $escaped = true;
            continue;
        }

        if (!$in_string && ($char === '"' || $char === "'")) {
            $in_string = true;
            $string_char = $char;
            $current_query .= $char;
        } elseif ($in_string && $char === $string_char) {
            $in_string = false;
            $current_query .= $char;
        } elseif (!$in_string && $char === ';') {
            $query = trim($current_query);
            if (!empty($query)) {
                $queries[] = $query;
            }
            $current_query = '';
        } else {
            $current_query .= $char;
        }
    }

    // إضافة الاستعلام الأخير إذا لم ينته بفاصلة منقوطة
    $query = trim($current_query);
    if (!empty($query)) {
        $queries[] = $query;
    }

    return array_filter($queries, function($query) {
        $query = trim($query);
        return !empty($query) && !preg_match('/^(--|\/\*)/', $query);
    });
}

// دالة للتحقق من الأخطاء التي يمكن تجاهلها
function isIgnorableError($errorMsg) {
    $ignorableErrors = [
        'Duplicate entry',
        'already exists',
        'Unknown database',
        'Table \'.*\' already exists',
        'Duplicate key name',
        'Multiple primary key defined'
    ];

    foreach ($ignorableErrors as $pattern) {
        if (preg_match('/' . $pattern . '/i', $errorMsg)) {
            return true;
        }
    }

    return false;
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعادة استيراد قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        .danger-zone { border: 2px solid #dc3545; border-radius: 10px; background: #fff5f5; }
        .warning-zone { border: 2px solid #ffc107; border-radius: 10px; background: #fffbf0; }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-danger text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-database me-2'></i>
                        إعادة استيراد قاعدة البيانات
                    </h3>
                </div>
                <div class='card-body'>";

// التحقق من وجود ملف SQL
$sql_file = 'station.sql';
if (!file_exists($sql_file)) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>ملف قاعدة البيانات غير موجود!</h5>
            <p>لم يتم العثور على ملف <code>station.sql</code> في المجلد الحالي.</p>
            <p>يرجى التأكد من وجود الملف وإعادة المحاولة.</p>
          </div>";
} else {
    echo "<div class='warning-zone p-4 mb-4'>
            <h5><i class='fas fa-exclamation-triangle me-2 text-warning'></i>تحذير مهم!</h5>
            <p class='mb-3'>هذا الإجراء سيقوم بـ:</p>
            <ul class='text-danger'>
                <li><strong>حذف جميع الجداول الموجودة</strong></li>
                <li><strong>حذف جميع البيانات الحالية</strong></li>
                <li><strong>إعادة إنشاء قاعدة البيانات من ملف station.sql</strong></li>
            </ul>
            <p class='text-warning mb-0'><strong>هذا الإجراء لا يمكن التراجع عنه!</strong></p>
          </div>";
    
    // إذا تم الضغط على زر التأكيد
    if (isset($_POST['confirm_import']) && $_POST['confirm_import'] === 'yes') {
        try {
            echo "<h5>جاري إعادة استيراد قاعدة البيانات...</h5>";
            
            // قراءة ملف SQL
            $sql_content = file_get_contents($sql_file);
            if ($sql_content === false) {
                throw new Exception('فشل في قراءة ملف SQL');
            }
            
            echo "<p class='text-info'><i class='fas fa-file-alt me-2'></i>تم قراءة ملف SQL بنجاح (" . number_format(strlen($sql_content)) . " حرف)</p>";
            
            // تحسين تقسيم الاستعلامات للتعامل مع الاستعلامات متعددة الأسطر
            $queries = splitSQLQueries($sql_content);
            
            echo "<p class='text-info'><i class='fas fa-list me-2'></i>تم العثور على " . count($queries) . " استعلام</p>";
            
            // إعدادات قاعدة البيانات المحسنة
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            $pdo->exec("SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO'");
            $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("SET CHARACTER SET utf8mb4");
            $pdo->exec("SET SESSION sql_mode = ''");
            echo "<p class='text-warning'><i class='fas fa-key me-2'></i>تم تعطيل فحص المفاتيح الخارجية وتحسين الإعدادات</p>";
            
            // الحصول على قائمة الجداول الموجودة
            $tables_result = $pdo->query("SHOW TABLES");
            $existing_tables = $tables_result->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($existing_tables)) {
                echo "<p class='text-warning'><i class='fas fa-trash me-2'></i>جاري حذف " . count($existing_tables) . " جدول موجود...</p>";
                
                // حذف الجداول الموجودة
                foreach ($existing_tables as $table) {
                    $pdo->exec("DROP TABLE IF EXISTS `$table`");
                    echo "<small class='text-muted'>- تم حذف جدول: $table</small><br>";
                }
            }
            
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم حذف جميع الجداول الموجودة</p>";
            
            // تنفيذ الاستعلامات
            $executed = 0;
            $errors = 0;
            
            echo "<p class='text-info'><i class='fas fa-cogs me-2'></i>جاري تنفيذ الاستعلامات...</p>";
            echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;'>";
            
            foreach ($queries as $index => $query) {
                $query = trim($query);
                if (empty($query)) continue;
                
                try {
                    $pdo->exec($query);
                    $executed++;
                    
                    // عرض تقدم العملية
                    if ($executed % 10 == 0) {
                        echo "<small class='text-success'>تم تنفيذ $executed استعلام...</small><br>";
                    }
                    
                } catch (PDOException $e) {
                    $errors++;
                    $errorMsg = $e->getMessage();

                    // تجاهل بعض الأخطاء غير المهمة
                    if (isIgnorableError($errorMsg)) {
                        echo "<small class='text-muted'><i class='fas fa-info-circle me-1'></i>تم تجاهل: " . substr($errorMsg, 0, 50) . "...</small><br>";
                        continue;
                    }

                    echo "<small class='text-danger'><i class='fas fa-exclamation-triangle me-1'></i>خطأ في الاستعلام " . ($index + 1) . ": " . htmlspecialchars(substr($errorMsg, 0, 100)) . "...</small><br>";

                    // زيادة حد الأخطاء المسموح به إلى 50
                    if ($errors > 50) {
                        echo "<small class='text-danger'><strong><i class='fas fa-stop me-1'></i>تم إيقاف العملية بسبب كثرة الأخطاء (أكثر من 50 خطأ)</strong></small><br>";
                        break;
                    }
                }
            }
            
            echo "</div>";
            
            // إعادة تفعيل فحص المفاتيح الخارجية
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            echo "<p class='text-success'><i class='fas fa-key me-2'></i>تم إعادة تفعيل فحص المفاتيح الخارجية</p>";
            
            if ($errors == 0) {
                echo "<div class='alert alert-success mt-4'>
                        <h4><i class='fas fa-check-circle me-2'></i>تم استيراد قاعدة البيانات بنجاح! 🎉</h4>
                        <p>تم تنفيذ $executed استعلام بنجاح بدون أخطاء.</p>
                      </div>";
                
                // تشغيل سكريپت الإصلاح التلقائي
                echo "<div class='alert alert-info'>
                        <h6><i class='fas fa-tools me-2'></i>تشغيل إصلاحات إضافية...</h6>
                      </div>";
                
                // إصلاح أسماء الأجهزة
                $device_fixes = [
                    ['old' => 'جهاز 1', 'new' => 'PlayStation 5 - Station 1'],
                    ['old' => 'جهاز 2', 'new' => 'PlayStation 4 - Station 2'],
                    ['old' => 'جهاز 3', 'new' => 'Xbox Series - Station 3'],
                    ['old' => 'جهاز 4', 'new' => 'Gaming PC - Station 4'],
                    ['old' => 'جهاز 5', 'new' => 'PlayStation 4 - Station 5'],
                    ['old' => 'ps 1', 'new' => 'PlayStation 4 - Station 1']
                ];
                
                $fix_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_name = ?");
                foreach ($device_fixes as $fix) {
                    $fix_stmt->execute([$fix['new'], $fix['old']]);
                    if ($fix_stmt->rowCount() > 0) {
                        echo "<small class='text-success'>تم إصلاح اسم الجهاز: {$fix['old']} → {$fix['new']}</small><br>";
                    }
                }
                
                echo "<div class='text-center mt-4'>
                        <a href='reset_and_fix_database.php' class='btn btn-warning me-2'>
                            <i class='fas fa-tools me-1'></i>تشغيل إصلاحات شاملة
                        </a>
                        <a href='client/dashboard.php' class='btn btn-primary me-2'>
                            <i class='fas fa-home me-1'></i>لوحة التحكم
                        </a>
                        <a href='client/sessions.php' class='btn btn-success'>
                            <i class='fas fa-play-circle me-1'></i>اختبار الجلسات
                        </a>
                      </div>";
                
            } else {
                echo "<div class='alert alert-warning mt-4'>
                        <h4><i class='fas fa-exclamation-triangle me-2'></i>تم الاستيراد مع بعض الأخطاء</h4>
                        <p>تم تنفيذ $executed استعلام بنجاح، لكن حدث $errors خطأ.</p>
                        <p>يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً إذا لزم الأمر.</p>
                      </div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>
                    <h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في استيراد قاعدة البيانات!</h5>
                    <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
                  </div>";
        }
        
    } else {
        // عرض نموذج التأكيد
        echo "<form method='POST'>
                <div class='danger-zone p-4 mb-4'>
                    <h5><i class='fas fa-skull-crossbones me-2 text-danger'></i>منطقة خطر!</h5>
                    <p>للمتابعة، يرجى كتابة <strong>\"نعم أريد حذف جميع البيانات\"</strong> في الحقل أدناه:</p>
                    <input type='text' class='form-control mb-3' id='confirmation' placeholder='اكتب النص المطلوب هنا...'>
                    <div class='form-check mb-3'>
                        <input class='form-check-input' type='checkbox' id='understand' required>
                        <label class='form-check-label text-danger' for='understand'>
                            <strong>أفهم أن هذا الإجراء سيحذف جميع البيانات الحالية ولا يمكن التراجع عنه</strong>
                        </label>
                    </div>
                    <button type='submit' name='confirm_import' value='yes' class='btn btn-danger' id='confirmBtn' disabled>
                        <i class='fas fa-database me-1'></i>تأكيد إعادة الاستيراد
                    </button>
                </div>
              </form>";
        
        echo "<script>
                document.getElementById('confirmation').addEventListener('input', function() {
                    const confirmBtn = document.getElementById('confirmBtn');
                    const checkbox = document.getElementById('understand');
                    const correctText = 'نعم أريد حذف جميع البيانات';
                    
                    if (this.value === correctText && checkbox.checked) {
                        confirmBtn.disabled = false;
                        confirmBtn.classList.remove('btn-danger');
                        confirmBtn.classList.add('btn-warning');
                    } else {
                        confirmBtn.disabled = true;
                        confirmBtn.classList.remove('btn-warning');
                        confirmBtn.classList.add('btn-danger');
                    }
                });
                
                document.getElementById('understand').addEventListener('change', function() {
                    const event = new Event('input');
                    document.getElementById('confirmation').dispatchEvent(event);
                });
              </script>";
    }
}

echo "<div class='text-center mt-4'>
        <a href='reset_and_fix_database.php' class='btn btn-primary me-2'>
            <i class='fas fa-tools me-1'></i>إصلاح البيانات الحالية
        </a>
        <a href='fix_data_display_issue.php' class='btn btn-info me-2'>
            <i class='fas fa-search me-1'></i>تشخيص المشاكل
        </a>
        <a href='client/dashboard.php' class='btn btn-secondary'>
            <i class='fas fa-home me-1'></i>العودة للوحة التحكم
        </a>
      </div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
