<?php
/**
 * الإصلاح النهائي لنظام تخصيص المظهر
 */

echo "<h1>🔧 الإصلاح النهائي لنظام تخصيص المظهر</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl; line-height: 1.6;'>";

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين معرف عميل افتراضي
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    $_SESSION['client_name'] = 'مركز الألعاب';
    $_SESSION['owner_name'] = 'صاحب المحل';
    echo "<p style='color: green;'>✅ تم تعيين جلسة افتراضية</p>";
}

// الاتصال بقاعدة البيانات
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// 1. إنشاء/تحديث جدول إعدادات المظهر
echo "<h2>1. إعداد قاعدة البيانات</h2>";
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS client_theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            primary_color VARCHAR(7) DEFAULT '#0d6efd',
            secondary_color VARCHAR(7) DEFAULT '#6c757d',
            accent_color VARCHAR(7) DEFAULT '#20c997',
            header_style ENUM('top', 'sidebar') DEFAULT 'top',
            sidebar_position ENUM('right', 'left') DEFAULT 'right',
            theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id)
        )
    ");
    echo "<p style='color: green;'>✅ جدول client_theme_settings جاهز</p>";
    
    // إدراج إعدادات تجريبية
    $stmt = $pdo->prepare("
        INSERT INTO client_theme_settings 
        (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        primary_color = VALUES(primary_color),
        secondary_color = VALUES(secondary_color),
        accent_color = VALUES(accent_color),
        header_style = VALUES(header_style),
        sidebar_position = VALUES(sidebar_position),
        theme_mode = VALUES(theme_mode),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    $stmt->execute([
        $_SESSION['client_id'], 
        '#dc3545', // أحمر
        '#6c757d', // رمادي
        '#fd7e14', // برتقالي
        'top', 
        'right', 
        'light'
    ]);
    
    echo "<p style='color: green;'>✅ إعدادات تجريبية محفوظة</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إعداد قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// 2. التحقق من ملفات النظام
echo "<h2>2. فحص ملفات النظام</h2>";
$system_files = [
    'client/api/theme-css.php' => 'ملف CSS الديناميكي',
    'client/api/save_theme_settings.php' => 'API حفظ الإعدادات',
    'client/api/get_theme_settings.php' => 'API جلب الإعدادات',
    'client/includes/header.php' => 'ملف الهيدر',
    'client/settings.php' => 'صفحة الإعدادات'
];

$missing_files = [];
foreach ($system_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description</p>";
    } else {
        echo "<p style='color: red;'>❌ $description غير موجود</p>";
        $missing_files[] = $file;
    }
}

// 3. اختبار ملف CSS الديناميكي
echo "<h2>3. اختبار CSS الديناميكي</h2>";
if (file_exists('client/api/theme-css.php')) {
    ob_start();
    include 'client/api/theme-css.php';
    $css_content = ob_get_clean();
    
    if (!empty($css_content)) {
        echo "<p style='color: green;'>✅ CSS الديناميكي يعمل (" . strlen($css_content) . " حرف)</p>";
        
        // حفظ CSS في ملف ثابت للاختبار
        $static_css = 'client/assets/css/custom-theme.css';
        if (!is_dir('client/assets/css')) {
            mkdir('client/assets/css', 0755, true);
        }
        file_put_contents($static_css, $css_content);
        echo "<p style='color: blue;'>ℹ️ تم حفظ CSS في: $static_css</p>";
        
    } else {
        echo "<p style='color: red;'>❌ CSS الديناميكي لا ينتج محتوى</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف CSS الديناميكي غير موجود</p>";
}

// 4. إصلاح ملف header.php
echo "<h2>4. إصلاح ملف header.php</h2>";
if (file_exists('client/includes/header.php')) {
    $header_content = file_get_contents('client/includes/header.php');
    
    if (strpos($header_content, 'theme-css.php') === false) {
        echo "<p style='color: orange;'>⚠️ رابط CSS المخصص غير موجود في header.php</p>";
        
        // إضافة رابط CSS
        $css_link = "\n    <!-- CSS مخصص للمظهر -->\n    <link rel=\"stylesheet\" href=\"api/theme-css.php?v=" . time() . "\" type=\"text/css\">";
        
        // البحث عن مكان إدراج CSS
        if (strpos($header_content, '</head>') !== false) {
            $header_content = str_replace('</head>', $css_link . "\n</head>", $header_content);
            
            if (file_put_contents('client/includes/header.php', $header_content)) {
                echo "<p style='color: green;'>✅ تم إضافة رابط CSS المخصص لملف header.php</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث ملف header.php</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لم يتم العثور على وسم </head> في ملف header.php</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ رابط CSS المخصص موجود في header.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}

// 5. إنشاء صفحة اختبار شاملة
echo "<h2>5. إنشاء صفحة اختبار</h2>";
$test_page_content = '<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["client_id"])) {
    $_SESSION["client_id"] = 1;
    $_SESSION["user_id"] = 1;
    $_SESSION["client_name"] = "مركز الألعاب";
    $_SESSION["owner_name"] = "صاحب المحل";
}

$page_title = "اختبار المظهر المخصص";
$active_page = "test";
require_once "includes/header.php";
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-palette me-2"></i>اختبار المظهر المخصص
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        إذا كنت ترى هذه الرسالة بألوان مخصصة، فإن النظام يعمل بشكل صحيح!
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <button class="btn btn-primary w-100 mb-2">زر أساسي</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success w-100 mb-2">زر نجاح</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-secondary w-100 mb-2">زر ثانوي</button>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="settings.php" class="btn btn-primary me-2">
                            <i class="fas fa-cog me-1"></i>انتقل للإعدادات
                        </a>
                        <a href="dashboard.php" class="btn btn-success">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once "includes/footer.php"; ?>
';

if (file_put_contents('client/theme-test-final.php', $test_page_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء صفحة اختبار: <a href='client/theme-test-final.php' target='_blank'>client/theme-test-final.php</a></p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء صفحة الاختبار</p>";
}

// 6. الخلاصة والتعليمات
echo "<h2>6. الخلاصة والتعليمات</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #28a745;'>";
echo "<h3 style='color: #155724;'>🎉 تم إصلاح النظام بنجاح!</h3>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li><strong>اختبر النظام:</strong> <a href='client/theme-test-final.php' target='_blank' style='color: #007bff;'>افتح صفحة الاختبار</a></li>";
echo "<li><strong>خصص الألوان:</strong> <a href='client/settings.php' target='_blank' style='color: #007bff;'>انتقل لصفحة الإعدادات</a></li>";
echo "<li><strong>امسح الذاكرة المؤقتة:</strong> اضغط Ctrl+F5 في المتصفح</li>";
echo "<li><strong>جرب الألوان المختلفة:</strong> في تبويب 'تخصيص المظهر'</li>";
echo "</ol>";

if (!empty($missing_files)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
    echo "<h4 style='color: #856404;'>⚠️ ملفات مفقودة:</h4>";
    echo "<ul>";
    foreach ($missing_files as $file) {
        echo "<li style='color: #856404;'>$file</li>";
    }
    echo "</ul>";
    echo "<p style='color: #856404;'>يرجى التأكد من وجود هذه الملفات لضمان عمل النظام بشكل كامل.</p>";
    echo "</div>";
}

echo "</div>";

// 7. معلومات إضافية
echo "<h2>7. معلومات إضافية</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 3px solid #6c757d;'>";
echo "<h4>🔧 استكشاف الأخطاء:</h4>";
echo "<ul>";
echo "<li><strong>إذا لم تظهر الألوان:</strong> امسح ذاكرة المتصفح (Ctrl+Shift+Delete)</li>";
echo "<li><strong>إذا ظهرت أخطاء:</strong> تحقق من وحدة تحكم المطور (F12)</li>";
echo "<li><strong>إذا لم تحفظ الإعدادات:</strong> تحقق من صلاحيات قاعدة البيانات</li>";
echo "<li><strong>للدعم:</strong> راجع ملف docs/theme-customization.md</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

h1, h2, h3 {
    color: #333;
    margin-bottom: 15px;
}

h1 {
    text-align: center;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h2 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-top: 30px;
}

p, li {
    margin-bottom: 8px;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

ol, ul {
    padding-right: 20px;
}

div[style*="background"] {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}
</style>
