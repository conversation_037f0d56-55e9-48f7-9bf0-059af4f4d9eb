<?php
/**
 * إصلاح مشاكل الجلسات والعملاء - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔧 إصلاح مشاكل الجلسات والعملاء</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص جدول العملاء
    echo "<h2>1. فحص جدول العملاء</h2>";
    
    try {
        $customers_check = $pdo->query("SELECT COUNT(*) as count FROM customers");
        $customers_count = $customers_check->fetch()['count'];
        echo "<p style='color: green;'>✅ جدول العملاء موجود - عدد العملاء: $customers_count</p>";
        
        // عرض بعض العملاء
        if ($customers_count > 0) {
            $sample_customers = $pdo->query("SELECT customer_id, name, phone FROM customers LIMIT 5")->fetchAll();
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>الاسم</th><th style='padding: 8px;'>الهاتف</th></tr>";
            foreach ($sample_customers as $customer) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا يوجد عملاء في النظام</p>";
            
            // إضافة عملاء تجريبيين
            echo "<h3>إضافة عملاء تجريبيين</h3>";
            $sample_customers = [
                ['أحمد محمد', '01234567890', '<EMAIL>'],
                ['فاطمة علي', '01987654321', '<EMAIL>'],
                ['محمد حسن', '01122334455', '<EMAIL>'],
                ['سارة أحمد', '01555666777', '<EMAIL>'],
                ['علي محمود', '01666777888', '<EMAIL>']
            ];
            
            $insert_customer = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, ?)");
            $added_count = 0;
            
            foreach ($sample_customers as $customer) {
                try {
                    $insert_customer->execute([$customer[0], $customer[1], $customer[2], 1]);
                    $added_count++;
                    echo "<p style='color: green;'>✅ تم إضافة العميل: " . $customer[0] . "</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة العميل " . $customer[0] . ": " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p style='color: blue;'>ℹ️ تم إضافة $added_count عميل تجريبي</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في جدول العملاء: " . $e->getMessage() . "</p>";
    }
    
    // 2. اختبار البحث عن العملاء
    echo "<h2>2. اختبار البحث عن العملاء</h2>";
    
    try {
        $search_test = $pdo->prepare("
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = 1
            AND (name LIKE ? OR phone LIKE ?)
            ORDER BY name ASC
            LIMIT 5
        ");
        
        $search_test->execute(['%أحمد%', '%012%']);
        $search_results = $search_test->fetchAll();
        
        if (count($search_results) > 0) {
            echo "<p style='color: green;'>✅ البحث عن العملاء يعمل - النتائج:</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>الاسم</th><th style='padding: 8px;'>الهاتف</th></tr>";
            foreach ($search_results as $customer) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لم يتم العثور على نتائج للبحث</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار البحث: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص جدول الجلسات
    echo "<h2>3. فحص جدول الجلسات</h2>";
    
    try {
        $sessions_check = $pdo->query("SELECT COUNT(*) as count FROM sessions");
        $sessions_count = $sessions_check->fetch()['count'];
        echo "<p style='color: green;'>✅ جدول الجلسات موجود - عدد الجلسات: $sessions_count</p>";
        
        // فحص هيكل جدول الجلسات
        $sessions_structure = $pdo->query("DESCRIBE sessions")->fetchAll();
        echo "<h4>هيكل جدول الجلسات:</h4>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th style='padding: 8px;'>العمود</th><th style='padding: 8px;'>النوع</th><th style='padding: 8px;'>Null</th></tr>";
        foreach ($sessions_structure as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في جدول الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 4. اختبار استعلام الجلسات مع العملاء
    echo "<h2>4. اختبار استعلام الجلسات مع العملاء</h2>";
    
    try {
        $sessions_with_customers = $pdo->prepare("
            SELECT s.session_id, s.start_time, s.customer_id,
                   d.device_name, d.device_type,
                   c.name as customer_name, c.phone as customer_phone
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1 AND s.status = 'active'
            ORDER BY s.start_time DESC
            LIMIT 5
        ");
        
        $sessions_with_customers->execute();
        $sessions_results = $sessions_with_customers->fetchAll();
        
        if (count($sessions_results) > 0) {
            echo "<p style='color: green;'>✅ استعلام الجلسات مع العملاء يعمل - النتائج:</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID الجلسة</th>";
            echo "<th style='padding: 8px;'>الجهاز</th>";
            echo "<th style='padding: 8px;'>العميل</th>";
            echo "<th style='padding: 8px;'>الهاتف</th>";
            echo "</tr>";
            foreach ($sessions_results as $session) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($session['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($session['customer_phone'] ?? '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: blue;'>ℹ️ لا توجد جلسات نشطة حالياً</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 5. اختبار API البحث عن العملاء
    echo "<h2>5. اختبار API البحث عن العملاء</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/search-customers.php?q=أحمد';
    
    echo "<p>رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    // محاولة استدعاء API
    try {
        $headers = [];
        if (isset($_SERVER['HTTP_COOKIE'])) {
            $headers[] = 'Cookie: ' . $_SERVER['HTTP_COOKIE'];
        }

        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => implode("\r\n", $headers)
            ]
        ]);

        $api_response = @file_get_contents($api_url, false, $context);
        
        if ($api_response !== false) {
            $api_data = json_decode($api_response, true);
            if (is_array($api_data)) {
                echo "<p style='color: green;'>✅ API البحث يعمل - عدد النتائج: " . count($api_data) . "</p>";
                if (count($api_data) > 0) {
                    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
                    echo json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ API يعمل لكن الاستجابة غير متوقعة: $api_response</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ فشل في الوصول لـ API</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار API: " . $e->getMessage() . "</p>";
    }
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 ملخص الفحص</h3>";
    echo "<ul>";
    echo "<li>جدول العملاء: " . (isset($customers_count) ? "موجود ($customers_count عميل) ✅" : "غير موجود ❌") . "</li>";
    echo "<li>البحث عن العملاء: " . (isset($search_results) && count($search_results) > 0 ? "يعمل ✅" : "لا يعمل ❌") . "</li>";
    echo "<li>جدول الجلسات: " . (isset($sessions_count) ? "موجود ($sessions_count جلسة) ✅" : "غير موجود ❌") . "</li>";
    echo "<li>استعلام الجلسات مع العملاء: " . (isset($sessions_results) ? "يعمل ✅" : "لا يعمل ❌") . "</li>";
    echo "<li>API البحث: " . (isset($api_data) && is_array($api_data) ? "يعمل ✅" : "لا يعمل ❌") . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر الجلسات الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/dashboard.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "<a href='client/sessions.php' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/customers.php' style='background: #17a2b8; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة العملاء</a>";
echo "</div>";

echo "</div>";
?>
