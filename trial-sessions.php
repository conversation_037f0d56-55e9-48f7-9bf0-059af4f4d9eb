<?php
session_start();
require_once 'includes/trial-auth.php';

// التحقق من تسجيل الدخول
requireTrialLogin();

$trial_id = $_SESSION['trial_id'];
$business_name = $_SESSION['trial_business_name'];

// الحصول على الوقت المتبقي
$timeRemaining = getTrialTimeRemaining($trial_id);

// الحصول على قائمة الجلسات النشطة
try {
    $stmt = $pdo->prepare("
        SELECT s.*, d.device_name, d.device_type, d.hourly_rate, c.name as customer_name
        FROM trial_sessions s
        JOIN trial_devices d ON s.device_id = d.device_id
        JOIN trial_customers c ON s.customer_id = c.customer_id
        WHERE s.trial_id = ? AND s.status = 'active'
        ORDER BY s.start_time DESC
    ");
    $stmt->execute([$trial_id]);
    $active_sessions = $stmt->fetchAll();
} catch (PDOException $e) {
    $active_sessions = [];
}

// الحصول على قائمة الجلسات المكتملة
try {
    $stmt = $pdo->prepare("
        SELECT s.*, d.device_name, d.device_type, c.name as customer_name
        FROM trial_sessions s
        JOIN trial_devices d ON s.device_id = d.device_id
        JOIN trial_customers c ON s.customer_id = c.customer_id
        WHERE s.trial_id = ? AND s.status = 'completed'
        ORDER BY s.end_time DESC
        LIMIT 10
    ");
    $stmt->execute([$trial_id]);
    $completed_sessions = $stmt->fetchAll();
} catch (PDOException $e) {
    $completed_sessions = [];
}

// الحصول على الأجهزة المتاحة
try {
    $stmt = $pdo->prepare("
        SELECT * FROM trial_devices 
        WHERE trial_id = ? AND status = 'available'
        ORDER BY device_name
    ");
    $stmt->execute([$trial_id]);
    $available_devices = $stmt->fetchAll();
} catch (PDOException $e) {
    $available_devices = [];
}

// الحصول على العملاء
try {
    $stmt = $pdo->prepare("
        SELECT * FROM trial_customers 
        WHERE trial_id = ? 
        ORDER BY name
    ");
    $stmt->execute([$trial_id]);
    $customers = $stmt->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// معالجة بدء جلسة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_session'])) {
    $device_id = intval($_POST['device_id'] ?? 0);
    $customer_id = intval($_POST['customer_id'] ?? 0);
    
    if ($device_id > 0 && $customer_id > 0) {
        try {
            $pdo->beginTransaction();
            
            // إنشاء الجلسة
            $stmt = $pdo->prepare("
                INSERT INTO trial_sessions (trial_id, device_id, customer_id) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$trial_id, $device_id, $customer_id]);
            
            // تحديث حالة الجهاز إلى مشغول
            $stmt = $pdo->prepare("
                UPDATE trial_devices 
                SET status = 'occupied' 
                WHERE device_id = ? AND trial_id = ?
            ");
            $stmt->execute([$device_id, $trial_id]);
            
            $pdo->commit();
            $success = 'تم بدء الجلسة بنجاح!';
            header('Location: trial-sessions.php?success=1');
            exit;
        } catch (PDOException $e) {
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء بدء الجلسة';
        }
    } else {
        $error = 'يرجى اختيار الجهاز والعميل';
    }
}

// معالجة إنهاء جلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['end_session'])) {
    $session_id = intval($_POST['session_id'] ?? 0);
    
    if ($session_id > 0) {
        try {
            $pdo->beginTransaction();
            
            // الحصول على بيانات الجلسة
            $stmt = $pdo->prepare("
                SELECT s.*, d.hourly_rate 
                FROM trial_sessions s
                JOIN trial_devices d ON s.device_id = d.device_id
                WHERE s.session_id = ? AND s.trial_id = ? AND s.status = 'active'
            ");
            $stmt->execute([$session_id, $trial_id]);
            $session = $stmt->fetch();
            
            if ($session) {
                // حساب المدة والتكلفة
                $start_time = new DateTime($session['start_time']);
                $end_time = new DateTime();
                $duration = $end_time->diff($start_time);
                $duration_minutes = ($duration->h * 60) + $duration->i;
                $total_cost = ($duration_minutes / 60) * $session['hourly_rate'];
                
                // تحديث الجلسة
                $stmt = $pdo->prepare("
                    UPDATE trial_sessions 
                    SET end_time = NOW(), duration_minutes = ?, total_cost = ?, status = 'completed'
                    WHERE session_id = ?
                ");
                $stmt->execute([$duration_minutes, $total_cost, $session_id]);
                
                // تحديث حالة الجهاز إلى متاح
                $stmt = $pdo->prepare("
                    UPDATE trial_devices 
                    SET status = 'available' 
                    WHERE device_id = ?
                ");
                $stmt->execute([$session['device_id']]);
                
                $pdo->commit();
                $success = 'تم إنهاء الجلسة بنجاح!';
                header('Location: trial-sessions.php?success=2');
                exit;
            }
        } catch (PDOException $e) {
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء إنهاء الجلسة';
        }
    }
}

if (isset($_GET['success'])) {
    if ($_GET['success'] == '1') {
        $success = 'تم بدء الجلسة بنجاح!';
    } elseif ($_GET['success'] == '2') {
        $success = 'تم إنهاء الجلسة بنجاح!';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الجلسات - التجربة المجانية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            --danger-gradient: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .trial-navbar {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .trial-timer {
            background: var(--warning-gradient);
            color: #2d3748;
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .trial-timer.danger {
            background: var(--danger-gradient);
            color: white;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .trial-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 1rem;
        }

        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .page-header h2 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }

        .session-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .session-card:hover {
            transform: translateY(-5px);
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .session-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .status-completed {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .session-info {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .session-duration {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }

        .add-session-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .sessions-tabs {
            margin-bottom: 2rem;
        }

        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
        }

        .nav-tabs .nav-link {
            border: none;
            color: #718096;
            font-weight: 600;
            padding: 1rem 1.5rem;
        }

        .nav-tabs .nav-link.active {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            background: none;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .session-card {
                padding: 1rem;
            }
            
            .add-session-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الوقت المتبقي -->
    <div class="trial-timer <?php echo $timeRemaining['total_seconds'] < 1800 ? 'danger' : ''; ?>" id="trialTimer">
        <i class="fas fa-clock me-2"></i>
        الوقت المتبقي للتجربة: <span id="timeDisplay"><?php echo $timeRemaining['hours']; ?>:<?php echo sprintf('%02d', $timeRemaining['minutes']); ?>:<?php echo sprintf('%02d', $timeRemaining['seconds']); ?></span>
    </div>

    <!-- شريط التنقل -->
    <nav class="trial-navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <span class="navbar-brand">
                        <i class="fas fa-gamepad me-2"></i>
                        PlayGood
                    </span>
                    <span class="trial-badge">تجربة مجانية</span>
                </div>
                <div class="d-flex align-items-center">
                    <a href="trial-dashboard.php" class="back-btn me-3">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="trial-logout.php" class="back-btn">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="trial-dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">إدارة الجلسات</li>
                </ol>
            </nav>
            <h2>
                <i class="fas fa-clock me-2 text-warning"></i>
                إدارة الجلسات
            </h2>
            <p class="text-muted mb-0">إدارة جلسات الألعاب في <?php echo htmlspecialchars($business_name); ?></p>
        </div>

        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- بدء جلسة جديدة -->
        <?php if (!empty($available_devices) && !empty($customers)): ?>
            <div class="add-session-card">
                <h4 class="mb-3">
                    <i class="fas fa-play me-2"></i>
                    بدء جلسة جديدة
                </h4>
                <form method="POST">
                    <div class="row">
                        <div class="col-md-5 mb-3">
                            <label for="device_id" class="form-label">اختر الجهاز</label>
                            <select class="form-control" id="device_id" name="device_id" required>
                                <option value="">اختر الجهاز</option>
                                <?php foreach ($available_devices as $device): ?>
                                    <option value="<?php echo $device['device_id']; ?>">
                                        <?php echo htmlspecialchars($device['device_name']); ?>
                                        (<?php echo htmlspecialchars($device['device_type']); ?>) -
                                        <?php echo number_format($device['hourly_rate'], 2); ?> جنيه/ساعة
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-5 mb-3">
                            <label for="customer_id" class="form-label">اختر العميل</label>
                            <select class="form-control" id="customer_id" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['customer_id']; ?>">
                                        <?php echo htmlspecialchars($customer['name']); ?> -
                                        <?php echo htmlspecialchars($customer['phone']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" name="start_session" class="btn btn-light w-100">
                                <i class="fas fa-play me-1"></i>
                                بدء الجلسة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        <?php elseif (empty($available_devices)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                لا توجد أجهزة متاحة حالياً. يرجى <a href="trial-devices.php">إضافة أجهزة</a> أو تحرير الأجهزة المشغولة.
            </div>
        <?php elseif (empty($customers)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                لا يوجد عملاء مضافين. يرجى <a href="trial-customers.php">إضافة عملاء</a> أولاً.
            </div>
        <?php endif; ?>

        <!-- تبويبات الجلسات -->
        <div class="sessions-tabs">
            <ul class="nav nav-tabs" id="sessionsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
                        <i class="fas fa-play me-2"></i>
                        الجلسات النشطة (<?php echo count($active_sessions); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
                        <i class="fas fa-check me-2"></i>
                        الجلسات المكتملة (<?php echo count($completed_sessions); ?>)
                    </button>
                </li>
            </ul>
        </div>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="sessionsTabsContent">
            <!-- الجلسات النشطة -->
            <div class="tab-pane fade show active" id="active" role="tabpanel">
                <?php if (empty($active_sessions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد جلسات نشطة حالياً</h5>
                        <p class="text-muted">ابدأ جلسة جديدة باستخدام النموذج أعلاه</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($active_sessions as $session): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="session-card">
                                    <div class="session-header">
                                        <span class="session-status status-active">نشطة</span>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="session_id" value="<?php echo $session['session_id']; ?>">
                                            <button type="submit" name="end_session" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من إنهاء الجلسة؟')">
                                                <i class="fas fa-stop me-1"></i>
                                                إنهاء
                                            </button>
                                        </form>
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-desktop me-2"></i>
                                        <?php echo htmlspecialchars($session['device_name']); ?> (<?php echo htmlspecialchars($session['device_type']); ?>)
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo htmlspecialchars($session['customer_name']); ?>
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-clock me-2"></i>
                                        بدأت: <?php echo date('H:i', strtotime($session['start_time'])); ?>
                                    </div>
                                    
                                    <div class="session-duration">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        <?php echo number_format($session['hourly_rate'], 2); ?> جنيه/ساعة
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- الجلسات المكتملة -->
            <div class="tab-pane fade" id="completed" role="tabpanel">
                <?php if (empty($completed_sessions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد جلسات مكتملة بعد</h5>
                        <p class="text-muted">ستظهر الجلسات المكتملة هنا</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($completed_sessions as $session): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="session-card">
                                    <div class="session-header">
                                        <span class="session-status status-completed">مكتملة</span>
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-desktop me-2"></i>
                                        <?php echo htmlspecialchars($session['device_name']); ?> (<?php echo htmlspecialchars($session['device_type']); ?>)
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo htmlspecialchars($session['customer_name']); ?>
                                    </div>
                                    
                                    <div class="session-info">
                                        <i class="fas fa-clock me-2"></i>
                                        المدة: <?php echo $session['duration_minutes']; ?> دقيقة
                                    </div>
                                    
                                    <div class="session-duration">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        التكلفة: <?php echo number_format($session['total_cost'], 2); ?> جنيه
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عداد الوقت المتبقي
        let totalSeconds = <?php echo $timeRemaining['total_seconds']; ?>;
        
        function updateTimer() {
            if (totalSeconds <= 0) {
                alert('انتهت صلاحية التجربة المجانية!');
                window.location.href = 'trial-login.php?error=trial_expired';
                return;
            }
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            document.getElementById('timeDisplay').textContent = 
                hours + ':' + 
                (minutes < 10 ? '0' : '') + minutes + ':' + 
                (seconds < 10 ? '0' : '') + seconds;
            
            const timerElement = document.getElementById('trialTimer');
            if (totalSeconds < 1800) {
                timerElement.classList.add('danger');
            }
            
            totalSeconds--;
        }
        
        setInterval(updateTimer, 1000);
    </script>
</body>
</html>
