<?php
// اختبار إصلاح حساب تكلفة المنتجات في الجلسات
session_start();
$_SESSION['client_id'] = 1; // محاكاة تسجيل الدخول

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح تكلفة المنتجات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <h1 class='text-center mb-4'><i class='fas fa-tools'></i> اختبار إصلاح تكلفة المنتجات</h1>";

try {
    echo "<div class='test-section'>
        <h2><i class='fas fa-database'></i> اختبار 1: التحقق من البيانات الأساسية</h2>";
    
    // البحث عن جلسة نشطة
    $stmt = $pdo->query("SELECT session_id, device_id, start_time FROM sessions WHERE status = 'active' AND client_id = 1 LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $stmt = $pdo->query("SELECT device_id FROM devices WHERE client_id = 1 LIMIT 1");
        $device = $stmt->fetch();
        
        if ($device) {
            $stmt = $pdo->prepare("INSERT INTO sessions (device_id, status, start_time, client_id) VALUES (?, 'active', NOW(), 1)");
            $stmt->execute([$device['device_id']]);
            $session_id = $pdo->lastInsertId();
            echo "<p class='success'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
        } else {
            throw new Exception("لا توجد أجهزة متاحة للاختبار");
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p class='success'>✅ تم العثور على جلسة نشطة: $session_id</p>";
    }
    
    // البحث عن منتج
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 AND price > 0 LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        // إنشاء منتج تجريبي
        $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)");
        $stmt->execute();
        $product_id = $pdo->lastInsertId();
        $product = ['id' => $product_id, 'name' => 'منتج تجريبي', 'price' => 5.00];
        echo "<p class='success'>✅ تم إنشاء منتج تجريبي: {$product['name']} - {$product['price']} ج.م</p>";
    } else {
        echo "<p class='success'>✅ تم العثور على منتج: {$product['name']} - {$product['price']} ج.م</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section'>
        <h2><i class='fas fa-plus-circle'></i> اختبار 2: إضافة منتج للجلسة</h2>";
    
    // حذف أي منتجات سابقة من الجلسة للاختبار
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ?")->execute([$session_id]);
    
    // إضافة منتج للجلسة عبر API
    $api_data = [
        'session_id' => $session_id,
        'product_id' => $product['id'],
        'quantity' => 2
    ];
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nCookie: " . session_name() . '=' . session_id(),
            'content' => json_encode($api_data)
        ]
    ]);
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php';
    $response = file_get_contents($api_url, false, $context);
    $result = json_decode($response, true);
    
    if ($result && $result['success']) {
        echo "<p class='success'>✅ تم إضافة المنتج بنجاح</p>";
        echo "<p class='info'>التكلفة الإجمالية: {$result['total_cost']} ج.م</p>";
        echo "<p class='info'>تكلفة المنتجات: {$result['products_cost']} ج.م</p>";
        echo "<p class='info'>تكلفة الوقت: {$result['time_cost']} ج.م</p>";
    } else {
        echo "<p class='error'>❌ فشل في إضافة المنتج: " . ($result['error'] ?? 'خطأ غير معروف') . "</p>";
        echo "<p class='info'>الاستجابة الخام: " . htmlspecialchars($response) . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section'>
        <h2><i class='fas fa-list'></i> اختبار 3: جلب منتجات الجلسة</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $session_id;
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . session_name() . '=' . session_id()
        ]
    ]);
    
    $response = file_get_contents($api_url, false, $context);
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "<p class='success'>✅ تم جلب منتجات الجلسة بنجاح</p>";
        echo "<p class='info'>عدد المنتجات: " . count($data['products']) . "</p>";
        
        if (count($data['products']) > 0) {
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr></thead>";
            echo "<tbody>";
            
            $total_products_cost = 0;
            foreach ($data['products'] as $product_item) {
                $item_total = $product_item['quantity'] * $product_item['price'];
                $total_products_cost += $item_total;
                
                echo "<tr>";
                echo "<td>{$product_item['product_name']}</td>";
                echo "<td>{$product_item['quantity']}</td>";
                echo "<td>" . number_format($product_item['price'], 2) . " ج.م</td>";
                echo "<td>" . number_format($item_total, 2) . " ج.م</td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
            echo "<p class='info'><strong>إجمالي تكلفة المنتجات: " . number_format($total_products_cost, 2) . " ج.م</strong></p>";
            
            if ($total_products_cost > 0) {
                echo "<p class='success'>✅ تكلفة المنتجات تحسب بشكل صحيح!</p>";
            } else {
                echo "<p class='error'>❌ مشكلة في حساب تكلفة المنتجات!</p>";
            }
        }
        
        // عرض التكلفة الإجمالية
        if (isset($data['total_cost'])) {
            echo "<p class='info'><strong>التكلفة الإجمالية للجلسة: {$data['total_cost']} ج.م</strong></p>";
        }
        
    } else {
        echo "<p class='error'>❌ فشل في جلب منتجات الجلسة: " . ($data['error'] ?? 'خطأ غير معروف') . "</p>";
        echo "<p class='info'>الاستجابة الخام: " . htmlspecialchars($response) . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section'>
        <h2><i class='fas fa-check-circle'></i> النتيجة النهائية</h2>";
    
    if ($result && $result['success'] && $data && $data['success'] && count($data['products']) > 0) {
        echo "<div class='alert alert-success'>";
        echo "<h4><i class='fas fa-thumbs-up'></i> تم إصلاح المشكلة بنجاح!</h4>";
        echo "<p>✅ تكلفة المنتجات تحسب وتعرض بشكل صحيح</p>";
        echo "<p>✅ التكلفة الإجمالية تشمل تكلفة الوقت والمنتجات</p>";
        echo "<p>✅ جميع APIs تعمل بشكل صحيح</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4><i class='fas fa-exclamation-triangle'></i> قد تحتاج لمراجعة إضافية</h4>";
        echo "<p>تحقق من الأخطاء أعلاه وتأكد من إعدادات قاعدة البيانات</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-times-circle'></i> خطأ في الاختبار</h4>";
    echo "<p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
