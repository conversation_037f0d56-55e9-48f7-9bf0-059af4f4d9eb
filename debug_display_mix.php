<?php
/**
 * تشخيص مشكلة خلط البيانات في العرض - PlayGood
 * المشكلة: اسم العميل يظهر في مكان التكلفة واسم الجهاز
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص مشكلة خلط البيانات في العرض</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص البيانات الخام من قاعدة البيانات
    echo "<h2>1. فحص البيانات الخام من قاعدة البيانات</h2>";
    
    try {
        // نفس الاستعلام المستخدم في sessions.php للجلسات النشطة
        $sessions_query = "
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   c.name as customer_name,
                   c.phone as customer_phone,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1 AND s.status = 'active'
            ORDER BY s.start_time DESC
        ";
        
        $sessions = $pdo->query($sessions_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات النشطة: <strong>" . count($sessions) . "</strong></p>";
        
        if (count($sessions) > 0) {
            foreach ($sessions as $index => $session) {
                echo "<div style='border: 2px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 10px; background: #f8f9fa;'>";
                echo "<h3 style='color: #007bff;'>🎮 جلسة رقم: " . $session['session_id'] . " (الفهرس: $index)</h3>";
                
                // عرض جميع البيانات الخام
                echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
                
                // البيانات الأساسية
                echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px;'>";
                echo "<h4 style='color: #1976d2;'>📊 البيانات الأساسية</h4>";
                echo "<p><strong>session_id:</strong> " . ($session['session_id'] ?? 'NULL') . "</p>";
                echo "<p><strong>device_id:</strong> " . ($session['device_id'] ?? 'NULL') . "</p>";
                echo "<p><strong>customer_id:</strong> " . ($session['customer_id'] ?? 'NULL') . "</p>";
                echo "<p><strong>start_time:</strong> " . ($session['start_time'] ?? 'NULL') . "</p>";
                echo "<p><strong>status:</strong> " . ($session['status'] ?? 'NULL') . "</p>";
                echo "</div>";
                
                // بيانات الجهاز
                echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px;'>";
                echo "<h4 style='color: #388e3c;'>🎮 بيانات الجهاز</h4>";
                echo "<p><strong>device_name:</strong> <span style='color: blue; font-weight: bold;'>" . htmlspecialchars($session['device_name'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>device_type:</strong> " . htmlspecialchars($session['device_type'] ?? 'NULL') . "</p>";
                echo "<p><strong>single_rate:</strong> " . ($session['single_rate'] ?? 'NULL') . "</p>";
                echo "<p><strong>multi_rate:</strong> " . ($session['multi_rate'] ?? 'NULL') . "</p>";
                echo "<p><strong>hourly_rate:</strong> " . ($session['hourly_rate'] ?? 'NULL') . "</p>";
                echo "</div>";
                
                // بيانات العميل
                echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px;'>";
                echo "<h4 style='color: #f57c00;'>👤 بيانات العميل</h4>";
                echo "<p><strong>customer_name:</strong> <span style='color: red; font-weight: bold;'>" . htmlspecialchars($session['customer_name'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>customer_phone:</strong> " . htmlspecialchars($session['customer_phone'] ?? 'NULL') . "</p>";
                echo "<p><strong>room_name:</strong> " . htmlspecialchars($session['room_name'] ?? 'NULL') . "</p>";
                echo "<p><strong>duration_minutes:</strong> " . ($session['duration_minutes'] ?? 'NULL') . "</p>";
                echo "</div>";
                
                echo "</div>";
                
                // محاكاة حساب التكلفة
                echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4 style='color: #7b1fa2;'>💰 محاكاة حساب التكلفة</h4>";
                
                $duration_minutes = $session['duration_minutes'] ?? 0;
                $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                
                echo "<p><strong>المدة:</strong> $duration_minutes دقيقة</p>";
                echo "<p><strong>السعر المستخدم:</strong> $hourly_rate ج.م/ساعة</p>";
                echo "<p><strong>التكلفة المحسوبة:</strong> <span style='background: yellow; padding: 5px; font-weight: bold;'>$time_cost ج.م</span></p>";
                
                // فحص نوع البيانات
                echo "<h5>🔍 فحص نوع البيانات:</h5>";
                echo "<p><strong>نوع device_name:</strong> " . gettype($session['device_name']) . "</p>";
                echo "<p><strong>نوع customer_name:</strong> " . gettype($session['customer_name']) . "</p>";
                echo "<p><strong>نوع التكلفة المحسوبة:</strong> " . gettype($time_cost) . "</p>";
                echo "<p><strong>هل التكلفة رقم؟</strong> " . (is_numeric($time_cost) ? "✅ نعم" : "❌ لا") . "</p>";
                
                echo "</div>";
                
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص الفواتير
    echo "<h2>2. فحص بيانات الفواتير</h2>";
    
    try {
        // نفس الاستعلام المستخدم في invoices.php
        $invoices_query = "
            SELECT 
                i.*,
                s.session_id,
                s.start_time,
                s.end_time,
                d.device_name,
                d.device_type,
                r.room_name,
                c.name as customer_name,
                c.phone as customer_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY i.created_at DESC
            LIMIT 3
        ";
        
        $invoices = $pdo->query($invoices_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الفواتير: <strong>" . count($invoices) . "</strong></p>";
        
        if (count($invoices) > 0) {
            foreach ($invoices as $index => $invoice) {
                echo "<div style='border: 2px solid #28a745; padding: 20px; margin: 20px 0; border-radius: 10px; background: #f8fff8;'>";
                echo "<h3 style='color: #28a745;'>🧾 فاتورة رقم: " . htmlspecialchars($invoice['invoice_number'] ?? 'غير محدد') . " (الفهرس: $index)</h3>";
                
                echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
                
                // بيانات الفاتورة
                echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px;'>";
                echo "<h4 style='color: #388e3c;'>📋 بيانات الفاتورة</h4>";
                echo "<p><strong>invoice_id:</strong> " . ($invoice['invoice_id'] ?? 'NULL') . "</p>";
                echo "<p><strong>session_id:</strong> " . ($invoice['session_id'] ?? 'NULL') . "</p>";
                echo "<p><strong>total_cost:</strong> " . ($invoice['total_cost'] ?? 'NULL') . "</p>";
                echo "<p><strong>payment_status:</strong> " . ($invoice['payment_status'] ?? 'NULL') . "</p>";
                echo "</div>";
                
                // البيانات المرتبطة
                echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px;'>";
                echo "<h4 style='color: #f57c00;'>🔗 البيانات المرتبطة</h4>";
                echo "<p><strong>device_name:</strong> <span style='color: blue; font-weight: bold;'>" . htmlspecialchars($invoice['device_name'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>customer_name:</strong> <span style='color: red; font-weight: bold;'>" . htmlspecialchars($invoice['customer_name'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>device_type:</strong> " . htmlspecialchars($invoice['device_type'] ?? 'NULL') . "</p>";
                echo "<p><strong>room_name:</strong> " . htmlspecialchars($invoice['room_name'] ?? 'NULL') . "</p>";
                echo "</div>";
                
                echo "</div>";
                
                // فحص التطابق
                echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4 style='color: #7b1fa2;'>🔍 فحص التطابق</h4>";
                
                $device_name = $invoice['device_name'] ?? '';
                $customer_name = $invoice['customer_name'] ?? '';
                
                echo "<p><strong>هل اسم الجهاز = اسم العميل؟</strong> " . ($device_name === $customer_name ? "❌ نعم (مشكلة!)" : "✅ لا (طبيعي)") . "</p>";
                echo "<p><strong>اسم الجهاز فارغ؟</strong> " . (empty($device_name) ? "❌ نعم" : "✅ لا") . "</p>";
                echo "<p><strong>اسم العميل فارغ؟</strong> " . (empty($customer_name) ? "❌ نعم" : "✅ لا") . "</p>";
                
                echo "</div>";
                
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد فواتير للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الفواتير: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص الجداول المرتبطة
    echo "<h2>3. فحص الجداول المرتبطة</h2>";
    
    try {
        // فحص جدول الجلسات
        echo "<h4>📊 جدول الجلسات:</h4>";
        $sessions_data = $pdo->query("
            SELECT session_id, device_id, customer_id, status, start_time
            FROM sessions 
            WHERE client_id = 1 
            ORDER BY session_id DESC 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sessions_data) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>session_id</th>";
            echo "<th style='padding: 8px;'>device_id</th>";
            echo "<th style='padding: 8px;'>customer_id</th>";
            echo "<th style='padding: 8px;'>status</th>";
            echo "</tr>";
            
            foreach ($sessions_data as $session) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . $session['device_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . ($session['customer_id'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px;'>" . $session['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // فحص جدول الأجهزة
        echo "<h4>🎮 جدول الأجهزة:</h4>";
        $devices_data = $pdo->query("
            SELECT device_id, device_name, device_type, single_rate
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($devices_data) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>device_id</th>";
            echo "<th style='padding: 8px;'>device_name</th>";
            echo "<th style='padding: 8px;'>device_type</th>";
            echo "<th style='padding: 8px;'>single_rate</th>";
            echo "</tr>";
            
            foreach ($devices_data as $device) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
                echo "<td style='padding: 8px;'>" . $device['single_rate'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // فحص جدول العملاء
        echo "<h4>👤 جدول العملاء:</h4>";
        $customers_data = $pdo->query("
            SELECT customer_id, name, phone
            FROM customers 
            WHERE client_id = 1 
            ORDER BY customer_id 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($customers_data) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>customer_id</th>";
            echo "<th style='padding: 8px;'>name</th>";
            echo "<th style='padding: 8px;'>phone</th>";
            echo "</tr>";
            
            foreach ($customers_data as $customer) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>التشخيص والحلول</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔍 ما يجب البحث عنه في النتائج أعلاه:</h4>";
echo "<ol>";
echo "<li><strong>في الجلسات:</strong> هل device_name مختلف عن customer_name؟</li>";
echo "<li><strong>في الفواتير:</strong> هل device_name = customer_name؟ (إذا نعم، فهذه هي المشكلة)</li>";
echo "<li><strong>في الجداول:</strong> هل البيانات صحيحة في كل جدول منفصل؟</li>";
echo "<li><strong>في الحسابات:</strong> هل التكلفة المحسوبة رقم أم نص؟</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/invoices.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الفواتير</a>";
echo "<a href='fix_device_rates.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إصلاح الأسعار</a>";
echo "</div>";

echo "</div>";
?>
