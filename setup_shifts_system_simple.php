<?php
/**
 * ملف تشغيل نظام الورديات المبسط - PlayGood
 * يقوم بإنشاء الجداول والبيانات الأساسية بدون stored procedures معقدة
 */

require_once 'config/database.php';

// بدء العملية
echo "<h1>🚀 تشغيل نظام الورديات المبسط - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";

try {
    echo "<h2>📋 إنشاء الجداول الأساسية:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // 1. إنشاء جدول قوالب الورديات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shift_templates (
            template_id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            template_name VARCHAR(100) NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            break_duration INT DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
            is_overnight BOOLEAN DEFAULT FALSE COMMENT 'وردية ليلية تمتد لليوم التالي',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>shift_templates</strong></p>";
    
    // 2. إنشاء جدول الورديات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shifts (
            shift_id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            template_id INT NULL,
            shift_name VARCHAR(100) NOT NULL,
            shift_date DATE NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            break_duration INT DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
            is_overnight BOOLEAN DEFAULT FALSE,
            max_employees INT DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
            min_employees INT DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
            status ENUM('scheduled', 'active', 'completed', 'cancelled') DEFAULT 'scheduled',
            notes TEXT,
            created_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
            FOREIGN KEY (template_id) REFERENCES shift_templates(template_id) ON DELETE SET NULL,
            INDEX idx_shifts_date (shift_date),
            INDEX idx_shifts_status (status),
            INDEX idx_shifts_client (client_id)
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>shifts</strong></p>";
    
    // 3. إنشاء جدول تخصيص الموظفين للورديات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employee_shifts (
            assignment_id INT AUTO_INCREMENT PRIMARY KEY,
            shift_id INT NOT NULL,
            employee_id INT NOT NULL,
            role_in_shift ENUM('supervisor', 'regular', 'backup') DEFAULT 'regular',
            is_mandatory BOOLEAN DEFAULT FALSE COMMENT 'هل الحضور إجباري',
            assigned_by INT NULL,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('assigned', 'confirmed', 'declined', 'cancelled') DEFAULT 'assigned',
            notes TEXT,
            FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
            FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
            UNIQUE KEY unique_employee_shift (shift_id, employee_id)
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>employee_shifts</strong></p>";
    
    // 4. إنشاء جدول حضور الورديات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shift_attendance (
            attendance_id INT AUTO_INCREMENT PRIMARY KEY,
            assignment_id INT NOT NULL,
            shift_id INT NOT NULL,
            employee_id INT NOT NULL,
            check_in_time TIMESTAMP NULL,
            check_out_time TIMESTAMP NULL,
            break_start_time TIMESTAMP NULL,
            break_end_time TIMESTAMP NULL,
            actual_hours DECIMAL(4,2) DEFAULT 0.00,
            overtime_hours DECIMAL(4,2) DEFAULT 0.00,
            break_hours DECIMAL(4,2) DEFAULT 0.00,
            status ENUM('absent', 'present', 'late', 'early_leave', 'overtime') DEFAULT 'absent',
            late_minutes INT DEFAULT 0,
            early_leave_minutes INT DEFAULT 0,
            notes TEXT,
            recorded_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (assignment_id) REFERENCES employee_shifts(assignment_id) ON DELETE CASCADE,
            FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
            FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
            INDEX idx_attendance_date (check_in_time),
            INDEX idx_attendance_employee (employee_id),
            INDEX idx_attendance_shift (shift_id)
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>shift_attendance</strong></p>";
    
    // 5. إنشاء جدول إعدادات الورديات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shift_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            setting_name VARCHAR(100) NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
            UNIQUE KEY unique_client_setting (client_id, setting_name)
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>shift_settings</strong></p>";
    
    echo "</div>";
    
    echo "<h2>📝 إدراج البيانات الأساسية:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // إدراج الإعدادات الافتراضية
    $pdo->exec("
        INSERT IGNORE INTO shift_settings (client_id, setting_name, setting_value, description) VALUES
        (1, 'grace_period_minutes', '15', 'فترة السماح للتأخير بالدقائق'),
        (1, 'overtime_threshold_minutes', '30', 'الحد الأدنى للعمل الإضافي بالدقائق'),
        (1, 'auto_checkout_hours', '12', 'تسجيل خروج تلقائي بعد عدد ساعات'),
        (1, 'break_duration_default', '30', 'مدة الاستراحة الافتراضية بالدقائق'),
        (1, 'notification_before_shift', '60', 'إشعار قبل بداية الوردية بالدقائق'),
        (1, 'allow_early_checkin', '30', 'السماح بتسجيل الدخول المبكر بالدقائق')
    ");
    echo "<p style='color: blue;'>📝 تم إدراج الإعدادات الافتراضية</p>";
    
    // إدراج قوالب الورديات الافتراضية
    $pdo->exec("
        INSERT IGNORE INTO shift_templates (client_id, template_name, start_time, end_time, break_duration, description) VALUES
        (1, 'الوردية الصباحية', '08:00:00', '16:00:00', 60, 'وردية صباحية من 8 صباحاً إلى 4 عصراً'),
        (1, 'الوردية المسائية', '16:00:00', '00:00:00', 60, 'وردية مسائية من 4 عصراً إلى 12 منتصف الليل'),
        (1, 'الوردية الليلية', '00:00:00', '08:00:00', 60, 'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً')
    ");
    echo "<p style='color: blue;'>📝 تم إدراج قوالب الورديات الافتراضية</p>";
    
    // إضافة صلاحيات الورديات
    $pdo->exec("
        INSERT IGNORE INTO permissions (permission_name, permission_label, permission_description, category) VALUES
        ('manage_shifts', 'إدارة الورديات', 'إنشاء وتعديل وحذف الورديات', 'shifts'),
        ('view_shifts', 'عرض الورديات', 'عرض جدول الورديات فقط', 'shifts'),
        ('manage_attendance', 'إدارة الحضور', 'تسجيل حضور وانصراف الموظفين', 'shifts'),
        ('view_attendance', 'عرض الحضور', 'عرض سجلات الحضور والغياب', 'shifts'),
        ('assign_shifts', 'تخصيص الورديات', 'تخصيص الموظفين للورديات', 'shifts'),
        ('view_shift_reports', 'تقارير الورديات', 'عرض تقارير الحضور والورديات', 'shifts')
    ");
    echo "<p style='color: blue;'>📝 تم إدراج صلاحيات الورديات</p>";
    
    // إضافة صفحات الورديات
    $pdo->exec("
        INSERT IGNORE INTO pages (page_name, page_label, page_url, page_icon, category) VALUES
        ('shifts', 'الورديات', 'shifts.php', 'fas fa-clock', 'shifts'),
        ('attendance', 'الحضور والانصراف', 'attendance.php', 'fas fa-user-check', 'shifts'),
        ('shift_reports', 'تقارير الورديات', 'shift_reports.php', 'fas fa-chart-line', 'shifts')
    ");
    echo "<p style='color: blue;'>📝 تم إدراج صفحات الورديات</p>";
    
    echo "</div>";
    
    echo "<h2>👁️ إنشاء Views مفيدة:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // إنشاء view للورديات المفصلة
    $pdo->exec("
        CREATE OR REPLACE VIEW shifts_detailed AS
        SELECT 
            s.shift_id,
            s.client_id,
            s.shift_name,
            s.shift_date,
            s.start_time,
            s.end_time,
            s.break_duration,
            s.is_overnight,
            s.max_employees,
            s.min_employees,
            s.status as shift_status,
            s.notes as shift_notes,
            st.template_name,
            COUNT(es.assignment_id) as assigned_employees,
            COUNT(CASE WHEN es.status = 'confirmed' THEN 1 END) as confirmed_employees,
            COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_employees,
            COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_employees
        FROM shifts s
        LEFT JOIN shift_templates st ON s.template_id = st.template_id
        LEFT JOIN employee_shifts es ON s.shift_id = es.shift_id
        LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
        GROUP BY s.shift_id
    ");
    echo "<p style='color: purple;'>👁️ تم إنشاء عرض: <strong>shifts_detailed</strong></p>";
    
    // إنشاء view للحضور المفصل
    $pdo->exec("
        CREATE OR REPLACE VIEW attendance_detailed AS
        SELECT 
            sa.attendance_id,
            sa.shift_id,
            sa.employee_id,
            e.name as employee_name,
            e.role as employee_role,
            s.shift_name,
            s.shift_date,
            s.start_time as scheduled_start,
            s.end_time as scheduled_end,
            sa.check_in_time,
            sa.check_out_time,
            sa.break_start_time,
            sa.break_end_time,
            sa.actual_hours,
            sa.overtime_hours,
            sa.break_hours,
            sa.status as attendance_status,
            sa.late_minutes,
            sa.early_leave_minutes,
            sa.notes as attendance_notes,
            es.role_in_shift,
            es.is_mandatory
        FROM shift_attendance sa
        JOIN employees e ON sa.employee_id = e.id
        JOIN shifts s ON sa.shift_id = s.shift_id
        JOIN employee_shifts es ON sa.assignment_id = es.assignment_id
    ");
    echo "<p style='color: purple;'>👁️ تم إنشاء عرض: <strong>attendance_detailed</strong></p>";
    
    echo "</div>";
    
    // التحقق من البيانات
    echo "<h2>🔍 التحقق من البيانات:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    $tables = ['shift_templates', 'shifts', 'employee_shifts', 'shift_attendance', 'shift_settings'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>📋 جدول <strong>$table</strong>: $count سجل</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ جدول <strong>$table</strong>: غير موجود</p>";
        }
    }
    
    echo "</div>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم تشغيل نظام الورديات بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام من خلال:</p>";
    echo "<ul>";
    echo "<li><a href='client/shifts.php' target='_blank'>صفحة إدارة الورديات</a></li>";
    echo "<li><a href='client/attendance.php' target='_blank'>صفحة الحضور والانصراف</a></li>";
    echo "<li><a href='client/shift_reports.php' target='_blank'>صفحة تقارير الورديات</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في التشغيل</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr style='margin: 30px 0;'>";
echo "<p style='text-align: center; color: #666;'>";
echo "نظام الورديات المبسط - PlayGood<br>";
echo "تم التطوير بواسطة Augment Agent<br>";
echo date('Y-m-d H:i:s');
echo "</p>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

div {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

p, li {
    line-height: 1.6;
}
</style>
