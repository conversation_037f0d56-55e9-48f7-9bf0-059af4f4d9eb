-- =====================================================
-- قاعدة بيانات نظام إدارة مراكز الألعاب - Station System
-- =====================================================
-- تم إنشاؤه: 20 يونيو 2025
-- الإصدار: 2.0
-- المطور: فريق التطوير
-- الوصف: نظام شامل لإدارة مراكز الألعاب والكافتيريا والموظفين
-- =====================================================

-- إعدادات قاعدة البيانات
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- إعدادات الترميز
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- إنشاء قاعدة البيانات
-- =====================================================
CREATE DATABASE IF NOT EXISTS `station` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `station`;

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `CheckClientLimit` (IN `p_client_id` INT, IN `p_feature_type` VARCHAR(50), IN `p_feature_name` VARCHAR(100), OUT `p_limit_value` INT, OUT `p_current_usage` INT, OUT `p_can_add` BOOLEAN)   BEGIN
    DECLARE v_plan_limit INT DEFAULT 0;
    DECLARE v_custom_limit INT DEFAULT NULL;
    DECLARE v_current_count INT DEFAULT 0;
    
    -- جلب حد الخطة الأساسية
    SELECT pf.feature_limit INTO v_plan_limit
    FROM clients c
    JOIN subscription_plans sp ON c.subscription_plan = sp.plan_name
    JOIN plan_features pf ON sp.plan_id = pf.plan_id
    WHERE c.client_id = p_client_id 
    AND pf.feature_type = p_feature_type 
    AND pf.feature_name = p_feature_name;
    
    -- جلب الحد المخصص إن وجد
    SELECT custom_limit INTO v_custom_limit
    FROM client_plan_limits
    WHERE client_id = p_client_id 
    AND feature_type = p_feature_type 
    AND feature_name = p_feature_name
    AND is_active = 1
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- تحديد الحد النهائي
    SET p_limit_value = COALESCE(v_custom_limit, v_plan_limit, 0);
    
    -- حساب الاستخدام الحالي
    IF p_feature_type = 'devices' THEN
        SELECT COUNT(*) INTO v_current_count FROM devices WHERE client_id = p_client_id;
    ELSEIF p_feature_type = 'products' THEN
        SELECT COUNT(*) INTO v_current_count FROM cafeteria_items WHERE client_id = p_client_id;
    ELSEIF p_feature_type = 'employees' THEN
        SELECT COUNT(*) INTO v_current_count FROM employees WHERE client_id = p_client_id;
    ELSEIF p_feature_type = 'customers' THEN
        SELECT COUNT(*) INTO v_current_count FROM customers WHERE client_id = p_client_id;
    END IF;
    
    SET p_current_usage = v_current_count;
    
    -- تحديد إمكانية الإضافة
    IF p_limit_value = -1 THEN
        SET p_can_add = TRUE; -- غير محدود
    ELSEIF p_limit_value = 0 THEN
        SET p_can_add = FALSE; -- غير مسموح
    ELSE
        SET p_can_add = (v_current_count < p_limit_value);
    END IF;
    
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `GrantDefaultPermissionsToClient` (IN `client_id` INT)   BEGIN
    INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
    SELECT client_id, page_id, TRUE
    FROM client_pages
    WHERE is_default = TRUE AND is_active = TRUE;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `additional_income`
--

CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `additional_income`
--

INSERT INTO `additional_income` (`id`, `client_id`, `income_type_id`, `amount`, `description`, `income_date`, `receipt_number`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 15, 100.00, '', '2025-06-13', '', 1, '2025-06-12 21:04:08', '2025-06-12 21:04:08'),
(2, 1, 15, 100.00, '', '2025-06-13', '', 1, '2025-06-12 21:05:55', '2025-06-12 21:05:55');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `email`, `password_hash`, `full_name`, `role`, `created_at`, `last_login`, `is_active`) VALUES
(1, 'admin', '<EMAIL>', '$2a$12$vFAjvx8m93Bb76ogrJq4yuu1Pw38jFvhaRibQBQ0cmZ5IleS3jm8G', 'مدير النظام', 'super_admin', '2025-06-08 02:28:08', '2025-06-18 18:20:51', 1);

-- --------------------------------------------------------

--
-- Table structure for table `admin_settings`
--

CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_settings`
--

INSERT INTO `admin_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_description`, `created_at`, `updated_at`) VALUES
(1, 'backup_enabled', '1', 'تفعيل صلاحية النسخ الاحتياطي للعملاء', '2025-06-16 10:51:34', '2025-06-16 11:10:36'),
(2, 'system_maintenance', '0', 'وضع الصيانة للنظام', '2025-06-16 10:51:34', '2025-06-16 10:51:34'),
(3, 'max_backup_files', '10', 'الحد الأقصى لعدد ملفات النسخ الاحتياطي المحفوظة', '2025-06-16 10:51:34', '2025-06-16 10:51:34'),
(61, 'timezone', 'Asia/Riyadh', NULL, '2025-06-17 19:23:07', '2025-06-17 19:23:07'),
(62, 'language', 'ar', NULL, '2025-06-17 19:23:07', '2025-06-17 19:23:07'),
(63, 'currency', 'EGP', NULL, '2025-06-17 19:23:07', '2025-06-17 19:23:07'),
(64, 'date_format', 'Y-m-d', NULL, '2025-06-17 19:23:07', '2025-06-17 19:23:07');

-- --------------------------------------------------------

--
-- Stand-in structure for view `attendance_detailed`
-- (See below for the actual view)
--
CREATE TABLE `attendance_detailed` (
`attendance_id` int(1)
,`shift_id` int(1)
,`employee_id` int(1)
,`employee_name` int(1)
,`employee_role` int(1)
,`shift_name` int(1)
,`shift_date` int(1)
,`scheduled_start` int(1)
,`scheduled_end` int(1)
,`check_in_time` int(1)
,`check_out_time` int(1)
,`break_start_time` int(1)
,`break_end_time` int(1)
,`actual_hours` int(1)
,`overtime_hours` int(1)
,`break_hours` int(1)
,`attendance_status` int(1)
,`late_minutes` int(1)
,`early_leave_minutes` int(1)
,`attendance_notes` int(1)
,`role_in_shift` int(1)
,`is_mandatory` int(1)
);

-- --------------------------------------------------------

--
-- Table structure for table `business_settings`
--

CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cafeteria_items`
--

CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `stock_quantity` int(11) DEFAULT 0 COMMENT 'الكمية المتوفرة',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `min_stock_level` int(11) DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
  `max_stock_level` int(11) DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
  `status` enum('available','low_stock','out_of_stock','discontinued') DEFAULT 'available' COMMENT 'حالة المنتج',
  `barcode` varchar(100) DEFAULT NULL COMMENT 'الباركود',
  `supplier` varchar(200) DEFAULT NULL COMMENT 'المورد',
  `last_restock_date` timestamp NULL DEFAULT NULL COMMENT 'تاريخ آخر تجديد للمخزن',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'تاريخ آخر تحديث'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cafeteria_items`
--

INSERT INTO `cafeteria_items` (`id`, `name`, `price`, `category`, `description`, `created_at`, `category_id`, `client_id`, `stock_quantity`, `cost_price`, `min_stock_level`, `max_stock_level`, `status`, `barcode`, `supplier`, `last_restock_date`, `updated_at`) VALUES
(13, 'شاي', 5.00, 'مشروبات ساخنة', 'شاي سادة', '2025-06-09 04:38:25', NULL, 1, 50, 3.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(14, 'شاي بحليب', 8.00, 'مشروبات ساخنة', 'شاي بحليب', '2025-06-09 04:38:42', NULL, 1, 50, 4.80, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(15, 'قهوة', 10.00, 'مشروبات ساخنة', '', '2025-06-09 05:06:50', NULL, 1, 50, 6.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(16, 'نسكافية', 15.00, 'مشروبات ساخنة', '', '2025-06-09 05:07:01', NULL, 1, 50, 9.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(17, 'قهوة تركي', 25.00, 'مشروبات ساخنة', '', '2025-06-09 05:07:13', NULL, 1, 50, 15.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(18, 'قهوة فرنساوي', 35.00, 'مشروبات ساخنة', '', '2025-06-09 05:07:23', NULL, 1, 50, 21.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(19, 'بيبسي', 12.00, 'مشروبات ساقعة', '', '2025-06-09 05:07:35', NULL, 1, 50, 8.00, 5, 100, 'available', '', '', NULL, '2025-06-16 12:51:19'),
(20, 'سبيرو سباتس ابيض', 15.00, 'مشروبات ساقعة', '', '2025-06-09 05:07:48', NULL, 1, 50, 9.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(21, 'سبيرو سباتس اسود', 15.00, 'مشروبات ساقعة', '', '2025-06-09 05:07:59', NULL, 1, 50, 9.00, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(24, 'كافى مكس', 343.00, 'مشروبات76', '', '2025-06-13 06:53:10', NULL, 2, 50, 205.80, 5, 100, 'available', NULL, NULL, NULL, '2025-06-16 12:48:19'),
(25, 'شاي فتله', 10.00, 'مشروبات ساخنة', '', '2025-06-14 10:42:20', NULL, 1, 50, 6.00, 5, 100, 'available', '', '', NULL, '2025-06-16 13:58:02');

--
-- Triggers `cafeteria_items`
--
DELIMITER $$
CREATE TRIGGER `log_product_usage_insert` AFTER INSERT ON `cafeteria_items` FOR EACH ROW BEGIN
    DECLARE v_current_count INT;
    SELECT COUNT(*) INTO v_current_count FROM cafeteria_items WHERE client_id = NEW.client_id;
    
    INSERT INTO plan_usage_log (client_id, feature_type, feature_name, action, new_count)
    VALUES (NEW.client_id, 'products', 'max_products', 'add', v_current_count);
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`category_id`, `name`, `client_id`) VALUES
(7, 'مشروبات ساقعة', 1),
(8, 'مشروبات ساخنة', 1),
(9, 'مأكولات', 1),
(10, 'مقبلات', 1),
(12, 'تسالي', 1),
(25, 'مشروبات76', 2),
(26, 'مشروبات76', 2),
(27, '245', 2),
(28, '10', 4);

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `name` varchar(255) NOT NULL DEFAULT 'مركز الألعاب',
  `password` varchar(255) DEFAULT NULL,
  `backup_enabled` tinyint(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي للعميل'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`client_id`, `business_name`, `owner_name`, `email`, `phone`, `password_hash`, `address`, `city`, `subscription_plan`, `subscription_start`, `subscription_end`, `is_active`, `created_at`, `updated_at`, `business_type`, `description`, `working_hours`, `name`, `password`, `backup_enabled`) VALUES
(1, 'playgood', 'احمد', '<EMAIL>', '***********', '$2y$10$txuFt9nn1DVao4bRaz2KoOQYi6tZAPh0nlymJpTLsB3XfN5v9b6J.', 'الجيزة', 'الجيزة', 'basic', '2025-06-08', '2025-07-08', 1, '2025-06-08 02:35:48', '2025-06-16 11:12:50', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL, 1),
(2, 'ريدوكس', 'محمد', '<EMAIL>', '***********', '$2y$10$nbG/.9myzQn1ux2Hhuv2mOzFHiV/w1IT9p2tRadhGPsCi4dOtBH8.', '7567\r\n33', '7657', 'basic', '2025-06-09', '2025-07-09', 0, '2025-06-09 03:31:49', '2025-06-14 11:59:40', 'gaming_center', '', 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL, 1),
(3, 'egypto', 'تامر', '<EMAIL>', '***********', '$2y$10$fmrMFKeY2BJjVHfutQpFT.3HTM1PjOZ1uWsn.PSy6kIBBQj6hZAnm', 'cairo', 'cairo', 'basic', '2025-06-09', '2025-07-09', 1, '2025-06-09 05:52:31', '2025-06-09 05:52:31', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL, 1),
(4, 'PlayStation', 'tarek', '<EMAIL>', '2***********', '$2y$10$kyPCoB5hoMRIhR66ms/JQ.ZrU1vsOfP1Ud4ZsMWaEiP0Dzj/oKF7.', 'مصر - الجيزة', 'الجيزة', 'basic', '2025-06-12', '2025-07-12', 1, '2025-06-12 00:06:24', '2025-06-16 11:12:20', 'gaming_center', '', 'من 9 صباحاً إلى 12 منتصف الليل', 'online store', NULL, 1);

-- --------------------------------------------------------

--
-- Stand-in structure for view `client_limits_usage`
-- (See below for the actual view)
--
CREATE TABLE `client_limits_usage` (
`client_id` int(11)
,`business_name` varchar(200)
,`subscription_plan` varchar(10)
,`feature_type` varchar(8)
,`feature_name` varchar(12)
,`feature_limit` int(11)
,`current_usage` bigint(21)
,`usage_status` varchar(13)
);

-- --------------------------------------------------------

--
-- Table structure for table `client_pages`
--

CREATE TABLE `client_pages` (
  `page_id` int(11) NOT NULL,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client_pages`
--

INSERT INTO `client_pages` (`page_id`, `page_name`, `page_label`, `page_url`, `page_icon`, `category`, `description`, `is_active`, `is_default`, `created_at`) VALUES
(1, 'dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', 1, 1, '2025-06-16 14:08:40'),
(2, 'profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', 1, 1, '2025-06-16 14:08:40'),
(3, 'devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', 1, 1, '2025-06-16 14:08:40'),
(4, 'rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف', 1, 0, '2025-06-16 14:08:40'),
(5, 'sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', 1, 1, '2025-06-16 14:08:40'),
(6, 'customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', 1, 1, '2025-06-16 14:08:40'),
(7, 'cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا', 1, 0, '2025-06-16 14:08:40'),
(8, 'orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات المستقلة', 1, 0, '2025-06-16 14:08:40'),
(9, 'employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة موظفي المحل', 1, 0, '2025-06-16 14:08:40'),
(10, 'attendance', 'نظام الحضور', 'attendance.php', 'fas fa-clock', 'employees', 'تسجيل حضور وانصراف الموظفين', 1, 0, '2025-06-16 14:08:40'),
(11, 'shifts', 'إدارة الورديات', 'shifts.php', 'fas fa-calendar-alt', 'employees', 'تنظيم ورديات العمل', 1, 0, '2025-06-16 14:08:40'),
(12, 'reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير مالية وإحصائية', 1, 1, '2025-06-16 14:08:40'),
(13, 'finances', 'المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة المصروفات والإيرادات', 1, 0, '2025-06-16 14:08:40'),
(14, 'invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إدارة وطباعة الفواتير', 1, 1, '2025-06-16 14:08:40'),
(15, 'inventory', 'إدارة المخزون', 'inventory.php', 'fas fa-boxes', 'inventory', 'إدارة مخزون المنتجات', 1, 0, '2025-06-16 14:08:40'),
(16, 'reservations', 'الحجوزات', 'reservations.php', 'fas fa-calendar-check', 'reservations', 'إدارة حجوزات العملاء', 1, 0, '2025-06-16 14:08:40'),
(17, 'settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والمحل', 1, 1, '2025-06-16 14:08:40'),
(18, 'invoice_settings', 'إعدادات الفواتير', 'invoice_settings.php', 'fas fa-file-alt', 'settings', 'تخصيص شكل ومحتوى الفواتير', 1, 0, '2025-06-16 14:08:40');

-- --------------------------------------------------------

--
-- Table structure for table `client_page_permissions`
--

CREATE TABLE `client_page_permissions` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client_page_permissions`
--

INSERT INTO `client_page_permissions` (`id`, `client_id`, `page_id`, `is_enabled`, `granted_by`, `granted_at`, `updated_at`) VALUES
(1, 2, 1, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(2, 3, 1, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(3, 1, 1, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(4, 4, 1, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(5, 2, 2, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(6, 3, 2, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(7, 1, 2, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(8, 4, 2, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(9, 2, 3, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(10, 3, 3, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(11, 1, 3, 0, 1, '2025-06-16 14:08:40', '2025-06-18 18:54:16'),
(12, 4, 3, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(13, 2, 5, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(14, 3, 5, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(15, 1, 5, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(16, 4, 5, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(17, 2, 6, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(18, 3, 6, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(19, 1, 6, 0, 1, '2025-06-16 14:08:40', '2025-06-18 18:54:15'),
(20, 4, 6, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(21, 2, 12, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(22, 3, 12, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(23, 1, 12, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(24, 4, 12, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(25, 2, 14, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(26, 3, 14, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(27, 1, 14, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(28, 4, 14, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(29, 2, 17, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(30, 3, 17, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(31, 1, 17, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(32, 4, 17, 1, NULL, '2025-06-16 14:08:40', '2025-06-16 14:08:40'),
(64, 1, 7, 1, 1, '2025-06-16 14:10:06', '2025-06-18 18:54:12'),
(65, 1, 4, 1, 1, '2025-06-16 14:10:10', '2025-06-16 14:10:10'),
(66, 1, 9, 1, 1, '2025-06-16 14:10:11', '2025-06-16 14:10:11'),
(67, 1, 11, 1, 1, '2025-06-16 14:10:12', '2025-06-16 14:10:12'),
(68, 1, 10, 1, 1, '2025-06-16 14:10:13', '2025-06-16 14:10:13'),
(69, 1, 13, 1, 1, '2025-06-16 14:10:16', '2025-06-16 14:10:16'),
(70, 1, 15, 1, 1, '2025-06-16 14:10:17', '2025-06-16 14:10:17'),
(71, 1, 8, 1, 1, '2025-06-16 14:10:21', '2025-06-16 14:10:21'),
(72, 1, 16, 1, 1, '2025-06-16 14:10:23', '2025-06-16 14:10:23'),
(73, 1, 18, 1, 1, '2025-06-16 14:10:27', '2025-06-16 14:10:27'),
(74, 2, 8, 1, 1, '2025-06-16 14:24:09', '2025-06-16 14:24:09'),
(75, 2, 15, 1, 1, '2025-06-16 14:24:12', '2025-06-16 14:24:12'),
(76, 2, 13, 1, 1, '2025-06-16 14:24:14', '2025-06-16 14:24:14'),
(77, 2, 9, 1, 1, '2025-06-16 14:24:19', '2025-06-16 14:24:19'),
(78, 2, 4, 0, 1, '2025-06-17 19:45:15', '2025-06-17 19:45:15'),
(79, 4, 7, 1, 1, '2025-06-17 19:47:02', '2025-06-18 18:21:15');

-- --------------------------------------------------------

--
-- Stand-in structure for view `client_page_permissions_detailed`
-- (See below for the actual view)
--
CREATE TABLE `client_page_permissions_detailed` (
`client_id` int(11)
,`business_name` varchar(200)
,`owner_name` varchar(100)
,`client_active` tinyint(1)
,`page_id` int(11)
,`page_name` varchar(100)
,`page_label` varchar(200)
,`page_url` varchar(255)
,`page_icon` varchar(50)
,`category` varchar(50)
,`description` text
,`is_default` tinyint(1)
,`has_permission` tinyint(4)
,`granted_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `client_plan_details`
-- (See below for the actual view)
--
CREATE TABLE `client_plan_details` (
`client_id` int(11)
,`business_name` varchar(200)
,`owner_name` varchar(100)
,`subscription_plan` enum('basic','premium','enterprise')
,`plan_name_ar` varchar(100)
,`plan_description` text
,`plan_price` decimal(10,2)
,`plan_duration_days` int(11)
,`subscription_start` date
,`subscription_end` date
,`days_remaining` int(7)
,`subscription_status` varchar(13)
);

-- --------------------------------------------------------

--
-- Table structure for table `client_plan_limits`
--

CREATE TABLE `client_plan_limits` (
  `limit_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `custom_limit` int(11) DEFAULT -1 COMMENT 'حد مخصص يتجاوز حد الخطة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الحد المخصص',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الحد المخصص',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `client_plan_summary`
-- (See below for the actual view)
--
CREATE TABLE `client_plan_summary` (
`client_id` int(11)
,`business_name` varchar(200)
,`subscription_plan` enum('basic','premium','enterprise')
,`plan_name_ar` varchar(100)
,`plan_price` decimal(10,2)
,`subscription_end` date
,`status_ar` varchar(12)
);

-- --------------------------------------------------------

--
-- Table structure for table `client_theme_settings`
--

CREATE TABLE `client_theme_settings` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `primary_color` varchar(7) DEFAULT '#0d6efd',
  `secondary_color` varchar(7) DEFAULT '#6c757d',
  `accent_color` varchar(7) DEFAULT '#20c997',
  `header_style` enum('top','sidebar') DEFAULT 'top',
  `sidebar_position` enum('right','left') DEFAULT 'right',
  `theme_mode` enum('light','dark','auto') DEFAULT 'light',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client_theme_settings`
--

INSERT INTO `client_theme_settings` (`id`, `client_id`, `primary_color`, `secondary_color`, `accent_color`, `header_style`, `sidebar_position`, `theme_mode`, `created_at`, `updated_at`) VALUES
(1, 1, '#0d6efd', '#000000', '#214363', 'top', 'right', 'light', '2025-06-16 15:24:58', '2025-06-17 16:48:08');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`customer_id`, `client_id`, `name`, `phone`, `email`, `notes`, `created_at`, `created_by`, `updated_at`, `updated_by`) VALUES
(43, 1, 'عمر سيد', '***********', NULL, NULL, '2025-06-16 02:14:13', NULL, NULL, NULL),
(44, 1, 'عميل تجريبي 1', '01000000001', NULL, NULL, '2025-06-16 03:53:57', NULL, NULL, NULL),
(45, 1, 'عميل تجريبي 2', '01000000002', NULL, NULL, '2025-06-16 03:53:57', NULL, NULL, NULL),
(46, 1, 'عميل تجريبي 3', '01000000003', NULL, NULL, '2025-06-16 03:53:57', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `devices`
--

CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL,
  `single_rate` decimal(10,2) DEFAULT NULL,
  `multi_rate` decimal(10,2) DEFAULT NULL,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `devices`
--

INSERT INTO `devices` (`device_id`, `client_id`, `device_name`, `device_type`, `hourly_rate`, `single_rate`, `multi_rate`, `status`, `room_id`, `created_at`, `updated_at`) VALUES
(16, 1, 'جهاز 1', 'PS5', 15.00, 15.00, 25.00, 'available', NULL, '2025-06-12 16:20:51', '2025-06-16 04:31:45'),
(17, 2, '434', 'PS4', 6.00, 6.00, 6.00, 'occupied', NULL, '2025-06-12 18:17:12', '2025-06-12 22:54:37'),
(18, 1, 'جهاز 2', 'PS4', 10.00, 10.00, 20.00, 'available', NULL, '2025-06-12 20:10:16', '2025-06-16 04:46:55'),
(19, 1, 'جهاز 3', 'PS4', 10.00, 10.00, 15.00, 'available', NULL, '2025-06-13 08:28:54', '2025-06-16 02:15:40'),
(22, 1, 'جهاز 4', 'PS4', 15.00, 15.00, 20.00, 'available', NULL, '2025-06-16 11:36:52', '2025-06-16 11:36:52'),
(23, 1, 'جهاز 5', 'Xbox', 25.00, 25.00, 30.00, 'available', NULL, '2025-06-16 11:54:42', '2025-06-16 11:57:23');

--
-- Triggers `devices`
--
DELIMITER $$
CREATE TRIGGER `log_device_usage_insert` AFTER INSERT ON `devices` FOR EACH ROW BEGIN
    DECLARE v_current_count INT;
    SELECT COUNT(*) INTO v_current_count FROM devices WHERE client_id = NEW.client_id;
    
    INSERT INTO plan_usage_log (client_id, feature_type, feature_name, action, new_count)
    VALUES (NEW.client_id, 'devices', 'max_devices', 'add', v_current_count);
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner') NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `hire_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'هل يستخدم صلاحيات مخصصة أم صلاحيات الدور'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`id`, `client_id`, `name`, `phone`, `role`, `salary`, `hire_date`, `created_at`, `updated_at`, `username`, `password_hash`, `last_login`, `is_active`, `email`, `address`, `custom_permissions`) VALUES
(3, 1, 'sayed', '01026362111', 'cashier', 5000.00, '2025-06-01', '2025-06-14 11:18:27', '2025-06-14 12:34:11', 'sayed871', '$2y$10$Dw6dvOyB/5jL.4MQikQjpe1pezD2IRDQq.h0JzNzIIVF.XBNdbmjO', '2025-06-14 12:34:11', 1, '<EMAIL>', 'شارع فريد - الأحساء', 1),
(4, 1, 'omer', '***********', 'cashier', 5000.00, '2025-06-17', '2025-06-17 17:19:46', '2025-06-20 07:30:45', 'omer897', '$2y$10$djEpQlDB6eGDKUpiOxPTbuY9cPhKVKabLLXNb8gN/WHFXN5jYa5ya', '2025-06-17 17:33:07', 1, NULL, NULL, 1);

-- --------------------------------------------------------

--
-- Table structure for table `employee_pages`
--

CREATE TABLE `employee_pages` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee_pages`
--

INSERT INTO `employee_pages` (`id`, `employee_id`, `page_id`, `granted_by`, `granted_at`) VALUES
(12, 3, 2, NULL, '2025-06-14 12:22:59'),
(13, 3, 1, NULL, '2025-06-14 12:22:59'),
(14, 3, 5, NULL, '2025-06-14 12:22:59'),
(15, 3, 14, NULL, '2025-06-14 12:22:59'),
(16, 3, 13, NULL, '2025-06-14 12:22:59');

-- --------------------------------------------------------

--
-- Stand-in structure for view `employee_pages_detailed`
-- (See below for the actual view)
--
CREATE TABLE `employee_pages_detailed` (
`employee_id` int(11)
,`employee_name` varchar(100)
,`employee_role` enum('manager','cashier','waiter','cleaner')
,`custom_permissions` tinyint(1)
,`page_id` int(11)
,`page_name` varchar(100)
,`page_label` varchar(200)
,`page_url` varchar(255)
,`page_icon` varchar(50)
,`page_category` varchar(50)
,`granted_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `employee_permissions`
--

CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee_permissions`
--

INSERT INTO `employee_permissions` (`id`, `employee_id`, `permission_id`, `granted_by`, `granted_at`) VALUES
(13, 3, 19, NULL, '2025-06-14 12:22:59'),
(14, 3, 3, NULL, '2025-06-14 12:22:59'),
(15, 3, 4, NULL, '2025-06-14 12:22:59'),
(16, 3, 24, NULL, '2025-06-14 12:22:59'),
(17, 4, 9, NULL, '2025-06-17 17:38:48'),
(18, 4, 10, NULL, '2025-06-17 17:38:48'),
(19, 4, 7, NULL, '2025-06-17 17:38:48'),
(20, 4, 8, NULL, '2025-06-17 17:38:48'),
(21, 4, 2, NULL, '2025-06-17 17:38:48'),
(22, 4, 17, NULL, '2025-06-17 17:38:48'),
(23, 4, 28, NULL, '2025-06-17 17:38:48'),
(24, 4, 3, NULL, '2025-06-17 17:38:48'),
(25, 4, 23, NULL, '2025-06-17 17:38:48'),
(26, 4, 21, NULL, '2025-06-17 17:38:48');

-- --------------------------------------------------------

--
-- Stand-in structure for view `employee_permissions_detailed`
-- (See below for the actual view)
--
CREATE TABLE `employee_permissions_detailed` (
`employee_id` int(11)
,`employee_name` varchar(100)
,`employee_role` enum('manager','cashier','waiter','cleaner')
,`custom_permissions` tinyint(1)
,`permission_id` int(11)
,`permission_name` varchar(100)
,`permission_label` varchar(200)
,`permission_category` varchar(50)
,`granted_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `employee_shifts`
--

CREATE TABLE `employee_shifts` (
  `assignment_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `role_in_shift` enum('supervisor','regular','backup') DEFAULT 'regular',
  `is_mandatory` tinyint(1) DEFAULT 0 COMMENT 'هل الحضور إجباري',
  `assigned_by` int(11) DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('assigned','confirmed','declined','cancelled') DEFAULT 'assigned',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`id`, `client_id`, `expense_type_id`, `amount`, `description`, `expense_date`, `receipt_number`, `created_by`, `created_at`, `updated_at`) VALUES
(2, 1, 19, 1000.00, '', '2025-06-13', '', 1, '2025-06-12 20:46:55', '2025-06-12 20:46:55');

-- --------------------------------------------------------

--
-- Table structure for table `expense_types`
--

CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expense_types`
--

INSERT INTO `expense_types` (`id`, `client_id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 2, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(2, 2, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(3, 2, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(4, 2, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(5, 2, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(6, 2, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(7, 2, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(8, 2, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(9, 3, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(10, 3, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(11, 3, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(12, 3, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(13, 3, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(14, 3, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(15, 3, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(16, 3, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(17, 1, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(18, 1, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(19, 1, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(20, 1, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(21, 1, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(22, 1, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(23, 1, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(24, 1, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(25, 4, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(26, 4, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(27, 4, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(28, 4, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(29, 4, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(30, 4, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(31, 4, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(32, 4, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46');

-- --------------------------------------------------------

--
-- Table structure for table `income_types`
--

CREATE TABLE `income_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `income_types`
--

INSERT INTO `income_types` (`id`, `client_id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 2, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(2, 2, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(3, 2, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(4, 2, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(5, 2, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(6, 3, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(7, 3, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(8, 3, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(9, 3, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(10, 3, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(11, 1, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(12, 1, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(13, 1, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(14, 1, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(15, 1, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(16, 4, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(17, 4, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(18, 4, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(19, 4, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46'),
(20, 4, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 05:28:46', '2025-06-12 05:28:46');

-- --------------------------------------------------------

--
-- Table structure for table `inventory_movements`
--

CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','expired','damaged') NOT NULL COMMENT 'نوع الحركة',
  `quantity` int(11) NOT NULL COMMENT 'الكمية',
  `previous_quantity` int(11) NOT NULL COMMENT 'الكمية السابقة',
  `new_quantity` int(11) NOT NULL COMMENT 'الكمية الجديدة',
  `unit_cost` decimal(10,2) DEFAULT 0.00 COMMENT 'تكلفة الوحدة',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT 'التكلفة الإجمالية',
  `reference_type` enum('purchase','sale','session','order','manual','system') DEFAULT 'manual' COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `client_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL COMMENT 'المستخدم الذي أجرى العملية',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `inventory_movements`
--

INSERT INTO `inventory_movements` (`id`, `product_id`, `movement_type`, `quantity`, `previous_quantity`, `new_quantity`, `unit_cost`, `total_cost`, `reference_type`, `reference_id`, `notes`, `client_id`, `created_by`, `created_at`) VALUES
(1, 13, 'in', 50, 0, 50, 3.00, 150.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(2, 14, 'in', 50, 0, 50, 4.80, 240.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(3, 15, 'in', 50, 0, 50, 6.00, 300.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(4, 16, 'in', 50, 0, 50, 9.00, 450.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(5, 17, 'in', 50, 0, 50, 15.00, 750.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(6, 18, 'in', 50, 0, 50, 21.00, 1050.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(7, 19, 'in', 50, 0, 50, 7.20, 360.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(8, 20, 'in', 50, 0, 50, 9.00, 450.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(9, 21, 'in', 50, 0, 50, 9.00, 450.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19'),
(11, 24, 'in', 50, 0, 50, 205.80, 10290.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 2, NULL, '2025-06-16 12:48:19'),
(12, 25, 'in', 50, 0, 50, 6.00, 300.00, 'manual', NULL, 'تحديث تلقائي للمخزن', 1, NULL, '2025-06-16 12:48:19');

-- --------------------------------------------------------

--
-- Stand-in structure for view `inventory_overview`
-- (See below for the actual view)
--
CREATE TABLE `inventory_overview` (
`id` int(11)
,`name` varchar(100)
,`selling_price` decimal(10,2)
,`cost_price` decimal(10,2)
,`stock_quantity` int(11)
,`min_stock_level` int(11)
,`max_stock_level` int(11)
,`status` enum('available','low_stock','out_of_stock','discontinued')
,`barcode` varchar(100)
,`supplier` varchar(200)
,`category` varchar(50)
,`description` text
,`last_restock_date` timestamp
,`created_at` timestamp
,`updated_at` timestamp
,`client_id` int(11)
,`category_name` varchar(100)
,`category_icon` varchar(50)
,`category_color` varchar(7)
,`stock_status_text` varchar(5)
,`stock_status_class` varchar(7)
,`profit_margin` decimal(11,2)
,`profit_percentage` decimal(17,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `time_cost` decimal(10,2) NOT NULL,
  `products_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`invoice_id`, `session_id`, `invoice_number`, `time_cost`, `products_cost`, `total_cost`, `client_id`, `payment_status`, `created_at`, `created_by`) VALUES
(20, 35, '202506140035', 15.00, 5.00, 20.00, 1, 'pending', '2025-06-14 10:35:13', 1),
(21, 36, '202506140036', 0.00, 10.00, 10.00, 1, 'pending', '2025-06-14 11:22:12', 1),
(22, 37, '202506160037', 10.00, 5.00, 15.00, 1, 'pending', '2025-06-16 02:15:40', 1),
(23, 38, '202506160038', 15.00, 0.00, 15.00, 1, 'pending', '2025-06-16 03:27:04', 1),
(24, 39, '202506160039', 0.00, 20.00, 20.00, 1, 'pending', '2025-06-16 04:31:45', 1),
(25, 40, '202506160040', 0.00, 35.00, 35.00, 1, 'pending', '2025-06-16 04:46:55', 1),
(26, 41, '202506160041', 25.00, 15.00, 40.00, 1, 'pending', '2025-06-16 11:57:23', 1);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_settings`
--

CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoice_settings`
--

INSERT INTO `invoice_settings` (`id`, `client_id`, `header_color`, `footer_text`, `footer_color`, `company_address`, `company_phone`, `show_qr_code`, `created_at`, `updated_at`) VALUES
(1, 1, '#2316d4', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي', '#000000', 'شارع احمد يحيي', '***********', 0, '2025-06-10 13:53:56', '2025-06-10 13:58:43'),
(2, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-11 23:45:45', '2025-06-11 23:45:45'),
(3, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-11 23:52:09', '2025-06-11 23:52:09'),
(4, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-12 00:20:45', '2025-06-12 00:20:45');

-- --------------------------------------------------------

--
-- Stand-in structure for view `monthly_attendance_stats`
-- (See below for the actual view)
--
CREATE TABLE `monthly_attendance_stats` (
`employee_id` int(11)
,`employee_name` varchar(100)
,`client_id` int(11)
,`year` int(4)
,`month` int(2)
,`total_shifts` bigint(21)
,`present_days` bigint(21)
,`absent_days` bigint(21)
,`late_days` bigint(21)
,`total_hours` decimal(26,2)
,`total_overtime` decimal(26,2)
,`avg_late_minutes` decimal(14,4)
,`attendance_percentage` decimal(28,5)
);

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `client_id`, `customer_id`, `session_id`, `order_number`, `total_amount`, `status`, `payment_method`, `notes`, `created_at`, `updated_at`, `created_by`, `discount_amount`, `tax_amount`) VALUES
(3, 1, 43, NULL, 'ORD-20250617-4572', 5.00, 'completed', 'cash', '', '2025-06-17 16:59:58', '2025-06-17 17:00:03', 1, 0.00, 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `price`, `total_price`, `created_at`) VALUES
(5, 3, 13, 1, 5.00, 5.00, '2025-06-17 16:59:58');

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `page_id` int(11) NOT NULL,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `pages`
--

INSERT INTO `pages` (`page_id`, `page_name`, `page_label`, `page_url`, `page_icon`, `category`, `is_active`, `created_at`) VALUES
(1, 'dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 1, '2025-06-12 13:25:00'),
(2, 'profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 1, '2025-06-12 13:25:00'),
(3, 'devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 1, '2025-06-12 13:25:00'),
(4, 'rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 1, '2025-06-12 13:25:00'),
(5, 'sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 1, '2025-06-12 13:25:00'),
(6, 'customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 1, '2025-06-12 13:25:00'),
(7, 'cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 1, '2025-06-12 13:25:00'),
(8, 'employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 1, '2025-06-12 13:25:00'),
(9, 'reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 1, '2025-06-12 13:25:00'),
(10, 'finances', 'المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 1, '2025-06-12 13:25:00'),
(11, 'invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 1, '2025-06-12 13:25:00'),
(12, 'settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 1, '2025-06-12 13:25:00'),
(13, 'shifts', 'الورديات', 'shifts.php', 'fas fa-clock', 'shifts', 1, '2025-06-14 12:05:01'),
(14, 'attendance', 'الحضور والانصراف', 'attendance.php', 'fas fa-user-check', 'shifts', 1, '2025-06-14 12:05:01'),
(15, 'shift_reports', 'تقارير الورديات', 'shift_reports.php', 'fas fa-chart-line', 'shifts', 1, '2025-06-14 12:05:01'),
(17, 'orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 1, '2025-06-16 12:19:08');

-- --------------------------------------------------------

--
-- Table structure for table `payment_status_log`
--

CREATE TABLE `payment_status_log` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `old_status` enum('pending','paid','cancelled') DEFAULT NULL,
  `new_status` enum('pending','paid','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_status_log`
--

INSERT INTO `payment_status_log` (`id`, `invoice_id`, `old_status`, `new_status`, `changed_by`, `changed_at`) VALUES
(2, 16, 'pending', 'cancelled', 1, '2025-06-14 05:40:49'),
(3, 15, 'pending', 'cancelled', 1, '2025-06-14 05:40:51'),
(4, 14, 'pending', 'cancelled', 1, '2025-06-14 05:40:53');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL,
  `permission_name` varchar(100) NOT NULL,
  `permission_label` varchar(200) NOT NULL,
  `permission_description` text DEFAULT NULL,
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`permission_id`, `permission_name`, `permission_label`, `permission_description`, `category`, `is_active`, `created_at`) VALUES
(1, 'manage_devices', 'إدارة الأجهزة', 'إضافة وتعديل وحذف الأجهزة', 'devices', 1, '2025-06-12 13:25:00'),
(2, 'view_devices', 'عرض الأجهزة', 'عرض قائمة الأجهزة فقط', 'devices', 1, '2025-06-12 13:25:00'),
(3, 'manage_sessions', 'إدارة الجلسات', 'بدء وإنهاء وتعديل الجلسات', 'sessions', 1, '2025-06-12 13:25:00'),
(4, 'view_sessions', 'عرض الجلسات', 'عرض قائمة الجلسات فقط', 'sessions', 1, '2025-06-12 13:25:00'),
(5, 'manage_rooms', 'إدارة الغرف', 'إضافة وتعديل وحذف الغرف', 'rooms', 1, '2025-06-12 13:25:00'),
(6, 'view_rooms', 'عرض الغرف', 'عرض قائمة الغرف فقط', 'rooms', 1, '2025-06-12 13:25:00'),
(7, 'manage_customers', 'إدارة العملاء', 'إضافة وتعديل وحذف العملاء', 'customers', 1, '2025-06-12 13:25:00'),
(8, 'view_customers', 'عرض العملاء', 'عرض قائمة العملاء فقط', 'customers', 1, '2025-06-12 13:25:00'),
(9, 'manage_cafeteria', 'إدارة الكافتيريا', 'إدارة منتجات وطلبات الكافتيريا', 'cafeteria', 1, '2025-06-12 13:25:00'),
(10, 'view_cafeteria', 'عرض الكافتيريا', 'عرض منتجات الكافتيريا فقط', 'cafeteria', 1, '2025-06-12 13:25:00'),
(11, 'manage_employees', 'إدارة الموظفين', 'إضافة وتعديل وحذف الموظفين', 'employees', 1, '2025-06-12 13:25:00'),
(12, 'view_employees', 'عرض الموظفين', 'عرض قائمة الموظفين فقط', 'employees', 1, '2025-06-12 13:25:00'),
(13, 'view_reports', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports', 1, '2025-06-12 13:25:00'),
(14, 'view_finances', 'عرض المالية', 'عرض البيانات المالية', 'finances', 1, '2025-06-12 13:25:00'),
(15, 'manage_finances', 'إدارة المالية', 'إدارة المصروفات والإيرادات', 'finances', 1, '2025-06-12 13:25:00'),
(16, 'manage_invoices', 'إدارة الفواتير', 'إنشاء وتعديل الفواتير', 'invoices', 1, '2025-06-12 13:25:00'),
(17, 'view_invoices', 'عرض الفواتير', 'عرض الفواتير فقط', 'invoices', 1, '2025-06-12 13:25:00'),
(18, 'manage_settings', 'إدارة الإعدادات', 'تعديل إعدادات النظام', 'settings', 1, '2025-06-12 13:25:00'),
(19, 'view_profile', 'عرض الملف الشخصي', 'عرض وتعديل الملف الشخصي', 'profile', 1, '2025-06-12 13:25:00'),
(20, 'manage_shifts', 'إدارة الورديات', 'إنشاء وتعديل وحذف الورديات', 'shifts', 1, '2025-06-14 12:05:01'),
(21, 'view_shifts', 'عرض الورديات', 'عرض جدول الورديات فقط', 'shifts', 1, '2025-06-14 12:05:01'),
(22, 'manage_attendance', 'إدارة الحضور', 'تسجيل حضور وانصراف الموظفين', 'shifts', 1, '2025-06-14 12:05:01'),
(23, 'view_attendance', 'عرض الحضور', 'عرض سجلات الحضور والغياب', 'shifts', 1, '2025-06-14 12:05:01'),
(24, 'assign_shifts', 'تخصيص الورديات', 'تخصيص الموظفين للورديات', 'shifts', 1, '2025-06-14 12:05:01'),
(25, 'view_shift_reports', 'تقارير الورديات', 'عرض تقارير الحضور والورديات', 'shifts', 1, '2025-06-14 12:05:01'),
(27, 'manage_orders', 'إدارة الأوردرات', 'إنشاء وتعديل وحذف الأوردرات المستقلة', 'orders', 1, '2025-06-16 12:19:08'),
(28, 'view_orders', 'عرض الأوردرات', 'عرض قائمة الأوردرات فقط', 'orders', 1, '2025-06-16 12:19:08');

-- --------------------------------------------------------

--
-- Table structure for table `plan_features`
--

CREATE TABLE `plan_features` (
  `feature_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `feature_name_ar` varchar(100) NOT NULL,
  `feature_limit` int(11) DEFAULT -1 COMMENT '-1 = غير محدود، 0 = غير مسموح، >0 = الحد الأقصى',
  `is_enabled` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plan_features`
--

INSERT INTO `plan_features` (`feature_id`, `plan_id`, `feature_type`, `feature_name`, `feature_name_ar`, `feature_limit`, `is_enabled`, `created_at`) VALUES
(1, 1, 'devices', 'max_devices', 'عدد الأجهزة', 5, 1, '2025-06-17 18:50:02'),
(2, 1, 'products', 'max_products', 'عدد المنتجات', 20, 1, '2025-06-17 18:50:02'),
(3, 1, 'employees', 'max_employees', 'عدد الموظفين', 3, 1, '2025-06-17 18:50:02'),
(4, 1, 'customers', 'max_customers', 'عدد العملاء', 100, 1, '2025-06-17 18:50:02'),
(5, 1, 'sessions', 'max_daily_sessions', 'الجلسات اليومية', 50, 1, '2025-06-17 18:50:02'),
(6, 1, 'storage', 'max_storage_mb', 'مساحة التخزين (ميجا)', 100, 1, '2025-06-17 18:50:02'),
(7, 1, 'pages', 'dashboard', 'لوحة التحكم', 1, 1, '2025-06-17 18:50:02'),
(8, 1, 'pages', 'devices', 'إدارة الأجهزة', 1, 1, '2025-06-17 18:50:02'),
(9, 1, 'pages', 'customers', 'إدارة العملاء', 1, 1, '2025-06-17 18:50:02'),
(10, 1, 'pages', 'sessions', 'إدارة الجلسات', 1, 1, '2025-06-17 18:50:02'),
(11, 1, 'pages', 'invoices', 'الفواتير', 1, 1, '2025-06-17 18:50:02'),
(12, 1, 'pages', 'cafeteria', 'الكافتيريا', 0, 1, '2025-06-17 18:50:02'),
(13, 1, 'pages', 'employees', 'إدارة الموظفين', 0, 1, '2025-06-17 18:50:02'),
(14, 1, 'pages', 'reports', 'التقارير', 0, 1, '2025-06-17 18:50:02'),
(15, 1, 'pages', 'inventory', 'إدارة المخزون', 0, 1, '2025-06-17 18:50:02'),
(16, 2, 'devices', 'max_devices', 'عدد الأجهزة', 15, 1, '2025-06-17 18:50:02'),
(17, 2, 'products', 'max_products', 'عدد المنتجات', 100, 1, '2025-06-17 18:50:02'),
(18, 2, 'employees', 'max_employees', 'عدد الموظفين', 10, 1, '2025-06-17 18:50:02'),
(19, 2, 'customers', 'max_customers', 'عدد العملاء', 500, 1, '2025-06-17 18:50:02'),
(20, 2, 'sessions', 'max_daily_sessions', 'الجلسات اليومية', 200, 1, '2025-06-17 18:50:02'),
(21, 2, 'storage', 'max_storage_mb', 'مساحة التخزين (ميجا)', 500, 1, '2025-06-17 18:50:02'),
(22, 2, 'pages', 'dashboard', 'لوحة التحكم', 1, 1, '2025-06-17 18:50:02'),
(23, 2, 'pages', 'devices', 'إدارة الأجهزة', 1, 1, '2025-06-17 18:50:02'),
(24, 2, 'pages', 'customers', 'إدارة العملاء', 1, 1, '2025-06-17 18:50:02'),
(25, 2, 'pages', 'sessions', 'إدارة الجلسات', 1, 1, '2025-06-17 18:50:02'),
(26, 2, 'pages', 'invoices', 'الفواتير', 1, 1, '2025-06-17 18:50:02'),
(27, 2, 'pages', 'cafeteria', 'الكافتيريا', 1, 1, '2025-06-17 18:50:02'),
(28, 2, 'pages', 'employees', 'إدارة الموظفين', 1, 1, '2025-06-17 18:50:02'),
(29, 2, 'pages', 'reports', 'التقارير', 1, 1, '2025-06-17 18:50:02'),
(30, 2, 'pages', 'inventory', 'إدارة المخزون', 0, 1, '2025-06-17 18:50:02'),
(31, 2, 'pages', 'rooms', 'إدارة الغرف', 1, 1, '2025-06-17 18:50:02'),
(32, 2, 'pages', 'orders', 'إدارة الأوردرات', 1, 1, '2025-06-17 18:50:02'),
(33, 3, 'devices', 'max_devices', 'عدد الأجهزة', 50, 1, '2025-06-17 18:50:02'),
(34, 3, 'products', 'max_products', 'عدد المنتجات', 500, 1, '2025-06-17 18:50:02'),
(35, 3, 'employees', 'max_employees', 'عدد الموظفين', 25, 1, '2025-06-17 18:50:02'),
(36, 3, 'customers', 'max_customers', 'عدد العملاء', 2000, 1, '2025-06-17 18:50:02'),
(37, 3, 'sessions', 'max_daily_sessions', 'الجلسات اليومية', 1000, 1, '2025-06-17 18:50:02'),
(38, 3, 'storage', 'max_storage_mb', 'مساحة التخزين (ميجا)', 2000, 1, '2025-06-17 18:50:02'),
(39, 3, 'pages', 'dashboard', 'لوحة التحكم', 1, 1, '2025-06-17 18:50:02'),
(40, 3, 'pages', 'devices', 'إدارة الأجهزة', 1, 1, '2025-06-17 18:50:02'),
(41, 3, 'pages', 'customers', 'إدارة العملاء', 1, 1, '2025-06-17 18:50:02'),
(42, 3, 'pages', 'sessions', 'إدارة الجلسات', 1, 1, '2025-06-17 18:50:02'),
(43, 3, 'pages', 'invoices', 'الفواتير', 1, 1, '2025-06-17 18:50:02'),
(44, 3, 'pages', 'cafeteria', 'الكافتيريا', 1, 1, '2025-06-17 18:50:02'),
(45, 3, 'pages', 'employees', 'إدارة الموظفين', 1, 1, '2025-06-17 18:50:02'),
(46, 3, 'pages', 'reports', 'التقارير', 1, 1, '2025-06-17 18:50:02'),
(47, 3, 'pages', 'inventory', 'إدارة المخزون', 1, 1, '2025-06-17 18:50:02'),
(48, 3, 'pages', 'rooms', 'إدارة الغرف', 1, 1, '2025-06-17 18:50:02'),
(49, 3, 'pages', 'orders', 'إدارة الأوردرات', 1, 1, '2025-06-17 18:50:02'),
(50, 3, 'pages', 'attendance', 'نظام الحضور', 1, 1, '2025-06-17 18:50:02'),
(51, 3, 'pages', 'shifts', 'إدارة الورديات', 1, 1, '2025-06-17 18:50:02'),
(52, 3, 'pages', 'finance', 'الإدارة المالية', 1, 1, '2025-06-17 18:50:02'),
(53, 3, 'pages', 'reservations', 'إدارة الحجوزات', 1, 1, '2025-06-17 18:50:02'),
(54, 4, 'devices', 'max_devices', 'عدد الأجهزة', -1, 1, '2025-06-17 18:50:03'),
(55, 4, 'products', 'max_products', 'عدد المنتجات', -1, 1, '2025-06-17 18:50:03'),
(56, 4, 'employees', 'max_employees', 'عدد الموظفين', -1, 1, '2025-06-17 18:50:03'),
(57, 4, 'customers', 'max_customers', 'عدد العملاء', -1, 1, '2025-06-17 18:50:03'),
(58, 4, 'sessions', 'max_daily_sessions', 'الجلسات اليومية', -1, 1, '2025-06-17 18:50:03'),
(59, 4, 'storage', 'max_storage_mb', 'مساحة التخزين (ميجا)', -1, 1, '2025-06-17 18:50:03'),
(60, 4, 'pages', 'dashboard', 'لوحة التحكم', 1, 1, '2025-06-17 18:50:03'),
(61, 4, 'pages', 'devices', 'إدارة الأجهزة', 1, 1, '2025-06-17 18:50:03'),
(62, 4, 'pages', 'customers', 'إدارة العملاء', 1, 1, '2025-06-17 18:50:03'),
(63, 4, 'pages', 'sessions', 'إدارة الجلسات', 1, 1, '2025-06-17 18:50:03'),
(64, 4, 'pages', 'invoices', 'الفواتير', 1, 1, '2025-06-17 18:50:03'),
(65, 4, 'pages', 'cafeteria', 'الكافتيريا', 1, 1, '2025-06-17 18:50:03'),
(66, 4, 'pages', 'employees', 'إدارة الموظفين', 1, 1, '2025-06-17 18:50:03'),
(67, 4, 'pages', 'reports', 'التقارير', 1, 1, '2025-06-17 18:50:03'),
(68, 4, 'pages', 'inventory', 'إدارة المخزون', 1, 1, '2025-06-17 18:50:03'),
(69, 4, 'pages', 'rooms', 'إدارة الغرف', 1, 1, '2025-06-17 18:50:03'),
(70, 4, 'pages', 'orders', 'إدارة الأوردرات', 1, 1, '2025-06-17 18:50:03'),
(71, 4, 'pages', 'attendance', 'نظام الحضور', 1, 1, '2025-06-17 18:50:03'),
(72, 4, 'pages', 'shifts', 'إدارة الورديات', 1, 1, '2025-06-17 18:50:03'),
(73, 4, 'pages', 'finance', 'الإدارة المالية', 1, 1, '2025-06-17 18:50:03'),
(74, 4, 'pages', 'reservations', 'إدارة الحجوزات', 1, 1, '2025-06-17 18:50:03'),
(75, 4, 'pages', 'invoice_settings', 'إعدادات الفواتير', 1, 1, '2025-06-17 18:50:03');

-- --------------------------------------------------------

--
-- Table structure for table `plan_usage_log`
--

CREATE TABLE `plan_usage_log` (
  `log_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `action` enum('add','remove','update') NOT NULL,
  `old_count` int(11) DEFAULT 0,
  `new_count` int(11) DEFAULT 0,
  `limit_exceeded` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plan_usage_log`
--

INSERT INTO `plan_usage_log` (`log_id`, `client_id`, `feature_type`, `feature_name`, `action`, `old_count`, `new_count`, `limit_exceeded`, `created_at`) VALUES
(1, 1, 'devices', 'max_devices', 'add', 0, 6, 0, '2025-06-17 19:44:02'),
(2, 4, 'products', 'max_products', 'add', 0, 1, 0, '2025-06-17 19:46:31'),
(3, 1, 'products', 'max_products', 'add', 0, 11, 0, '2025-06-18 18:34:36');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_categories`
--

CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL COMMENT 'التصنيف الأب للتصنيفات الفرعية',
  `icon` varchar(50) DEFAULT 'fas fa-box' COMMENT 'أيقونة التصنيف',
  `color` varchar(7) DEFAULT '#007bff' COMMENT 'لون التصنيف',
  `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
  `is_active` tinyint(1) DEFAULT 1,
  `client_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_categories`
--

INSERT INTO `product_categories` (`id`, `name`, `description`, `parent_id`, `icon`, `color`, `sort_order`, `is_active`, `client_id`, `created_at`) VALUES
(1, 'مشروبات ساخنة', 'الشاي والقهوة والمشروبات الساخنة', NULL, 'fas fa-coffee', '#8B4513', 0, 1, 1, '2025-06-16 12:48:18'),
(2, 'مشروبات باردة', 'العصائر والمياه والمشروبات الباردة', NULL, 'fas fa-glass-whiskey', '#4169E1', 0, 1, 1, '2025-06-16 12:48:18'),
(3, 'وجبات خفيفة', 'الساندويتشات والوجبات السريعة', NULL, 'fas fa-hamburger', '#FF6347', 0, 1, 1, '2025-06-16 12:48:18'),
(4, 'حلويات', 'الكيك والحلويات والآيس كريم', NULL, 'fas fa-birthday-cake', '#FF69B4', 0, 1, 1, '2025-06-16 12:48:18'),
(5, 'أكسسوارات الألعاب', 'يدات التحكم والسماعات والكابلات', NULL, 'fas fa-gamepad', '#32CD32', 0, 1, 1, '2025-06-16 12:48:18'),
(6, 'قطع غيار', 'قطع غيار الأجهزة والصيانة', NULL, 'fas fa-tools', '#FF8C00', 0, 1, 1, '2025-06-16 12:48:18');

-- --------------------------------------------------------

--
-- Table structure for table `product_device_associations`
--

CREATE TABLE `product_device_associations` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `device_part` enum('controller','headset','cable','accessory','other') DEFAULT 'other' COMMENT 'نوع الجزء',
  `quantity_needed` int(11) DEFAULT 1 COMMENT 'الكمية المطلوبة لكل جهاز',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rooms`
--

CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_type` enum('VIP','regular','private') DEFAULT 'regular',
  `capacity` int(11) DEFAULT 1,
  `special_rate` decimal(8,2) DEFAULT 0.00,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sections`
--

CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL,
  `device_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_id` int(11) DEFAULT NULL,
  `client_id` int(11) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `game_type` enum('single','multiplayer') DEFAULT 'single' COMMENT 'نوع اللعب: فردي أو زوجي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`session_id`, `device_id`, `start_time`, `end_time`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`, `customer_id`, `client_id`, `total_cost`, `expected_end_time`, `notes`, `game_type`) VALUES
(35, 16, '2025-06-14 16:22:48', '2025-06-14 16:35:13', 'completed', 1, 1, '2025-06-14 10:22:48', '2025-06-14 10:35:13', NULL, 1, 20.00, NULL, '', 'single'),
(36, 16, '2025-06-14 17:21:54', '2025-06-14 17:22:12', 'completed', 1, 1, '2025-06-14 11:21:54', '2025-06-14 11:22:12', NULL, 1, 10.00, NULL, '', 'single'),
(37, 19, '2025-06-16 08:09:50', '2025-06-16 08:15:40', 'completed', 1, 1, '2025-06-16 02:09:50', '2025-06-16 02:15:40', 43, 1, 15.00, '2025-06-16 01:39:50', '', 'single'),
(38, 16, '2025-06-16 08:27:40', '2025-06-16 09:27:04', 'completed', 1, 1, '2025-06-16 02:27:40', '2025-06-16 03:27:04', 43, 1, 15.00, '2025-06-16 01:57:40', '', 'single'),
(39, 16, '2025-06-16 10:30:55', '2025-06-16 10:31:45', 'completed', 1, 1, '2025-06-16 04:30:55', '2025-06-16 04:31:45', 43, 1, 20.00, NULL, '', 'single'),
(40, 18, '2025-06-16 10:46:42', '2025-06-16 10:46:55', 'completed', 1, 1, '2025-06-16 04:46:42', '2025-06-16 04:46:55', 43, 1, 35.00, NULL, '', 'single'),
(41, 23, '2025-06-16 17:55:05', '2025-06-16 17:57:23', 'completed', 1, 1, '2025-06-16 11:55:05', '2025-06-16 11:57:23', 43, 1, 40.00, NULL, '', 'single');

-- --------------------------------------------------------

--
-- Table structure for table `session_products`
--

CREATE TABLE `session_products` (
  `id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL COMMENT 'سعر المنتج وقت الإضافة',
  `total` decimal(10,2) GENERATED ALWAYS AS (`price` * `quantity`) STORED COMMENT 'إجمالي السعر',
  `created_by` int(11) DEFAULT NULL COMMENT 'معرف المستخدم الذي أضاف المنتج',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `session_products`
--

INSERT INTO `session_products` (`id`, `session_id`, `product_id`, `quantity`, `created_at`, `price`, `created_by`, `notes`) VALUES
(46, 35, 13, 1, '2025-06-14 10:34:58', 5.00, NULL, NULL),
(47, 36, 25, 1, '2025-06-14 11:22:03', 10.00, NULL, NULL),
(48, 37, 13, 1, '2025-06-16 02:10:06', 5.00, NULL, NULL),
(49, 39, 25, 1, '2025-06-16 04:31:05', 10.00, NULL, NULL),
(50, 39, 25, 1, '2025-06-16 04:31:24', 10.00, NULL, NULL),
(51, 40, 18, 1, '2025-06-16 04:46:46', 35.00, NULL, NULL),
(52, 41, 25, 1, '2025-06-16 11:55:12', 10.00, NULL, NULL),
(53, 41, 13, 1, '2025-06-16 11:55:33', 5.00, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `shifts`
--

CREATE TABLE `shifts` (
  `shift_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `shift_name` varchar(100) NOT NULL,
  `shift_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0,
  `max_employees` int(11) DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
  `min_employees` int(11) DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
  `status` enum('scheduled','active','completed','cancelled') DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shifts`
--

INSERT INTO `shifts` (`shift_id`, `client_id`, `template_id`, `shift_name`, `shift_date`, `start_time`, `end_time`, `break_duration`, `is_overnight`, `max_employees`, `min_employees`, `status`, `notes`, `created_by`, `created_at`, `updated_at`) VALUES
(3, 1, NULL, 'وردية 1', '2025-06-17', '20:37:00', '12:40:00', 0, 0, 1, 1, 'scheduled', '', 1, '2025-06-17 17:36:33', '2025-06-17 17:36:33');

-- --------------------------------------------------------

--
-- Stand-in structure for view `shifts_detailed`
-- (See below for the actual view)
--
CREATE TABLE `shifts_detailed` (
`shift_id` int(11)
,`client_id` int(11)
,`shift_name` varchar(100)
,`shift_date` date
,`start_time` time
,`end_time` time
,`break_duration` int(11)
,`is_overnight` tinyint(1)
,`max_employees` int(11)
,`min_employees` int(11)
,`shift_status` enum('scheduled','active','completed','cancelled')
,`shift_notes` text
,`template_name` varchar(100)
,`assigned_employees` bigint(21)
,`confirmed_employees` bigint(21)
,`present_employees` bigint(21)
,`absent_employees` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `shift_attendance`
--

CREATE TABLE `shift_attendance` (
  `attendance_id` int(11) NOT NULL,
  `assignment_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  `break_start_time` timestamp NULL DEFAULT NULL,
  `break_end_time` timestamp NULL DEFAULT NULL,
  `actual_hours` decimal(4,2) DEFAULT 0.00,
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `break_hours` decimal(4,2) DEFAULT 0.00,
  `status` enum('absent','present','late','early_leave','overtime') DEFAULT 'absent',
  `late_minutes` int(11) DEFAULT 0,
  `early_leave_minutes` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `recorded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `shift_notifications`
--

CREATE TABLE `shift_notifications` (
  `notification_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `notification_type` enum('shift_start_reminder','shift_end_reminder','shift_started','shift_ended','late_warning','absence_alert') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `scheduled_time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `sent_time` timestamp NULL DEFAULT NULL,
  `is_sent` tinyint(1) DEFAULT 0,
  `is_read` tinyint(1) DEFAULT 0,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `shift_settings`
--

CREATE TABLE `shift_settings` (
  `setting_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shift_settings`
--

INSERT INTO `shift_settings` (`setting_id`, `client_id`, `setting_name`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'grace_period_minutes', '15', 'فترة السماح للتأخير بالدقائق', '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(2, 1, 'overtime_threshold_minutes', '30', 'الحد الأدنى للعمل الإضافي بالدقائق', '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(3, 1, 'auto_checkout_hours', '12', 'تسجيل خروج تلقائي بعد عدد ساعات', '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(4, 1, 'break_duration_default', '30', 'مدة الاستراحة الافتراضية بالدقائق', '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(5, 1, 'notification_before_shift', '60', 'إشعار قبل بداية الوردية بالدقائق', '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(6, 1, 'allow_early_checkin', '30', 'السماح بتسجيل الدخول المبكر بالدقائق', '2025-06-14 12:05:01', '2025-06-14 12:05:01');

-- --------------------------------------------------------

--
-- Table structure for table `shift_templates`
--

CREATE TABLE `shift_templates` (
  `template_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0 COMMENT 'وردية ليلية تمتد لليوم التالي',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shift_templates`
--

INSERT INTO `shift_templates` (`template_id`, `client_id`, `template_name`, `start_time`, `end_time`, `break_duration`, `is_overnight`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'الوردية الصباحية', '08:00:00', '16:00:00', 60, 0, 'وردية صباحية من 8 صباحاً إلى 4 عصراً', 1, '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(2, 1, 'الوردية المسائية', '16:00:00', '00:00:00', 60, 0, 'وردية مسائية من 4 عصراً إلى 12 منتصف الليل', 1, '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(3, 1, 'الوردية الليلية', '00:00:00', '08:00:00', 60, 0, 'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً', 1, '2025-06-14 12:05:01', '2025-06-14 12:05:01'),
(4, 1, 'الوردية الصباحية', '08:00:00', '16:00:00', 60, 0, 'وردية صباحية من 8 صباحاً إلى 4 عصراً', 1, '2025-06-14 12:10:54', '2025-06-14 12:10:54'),
(5, 1, 'الوردية المسائية', '16:00:00', '00:00:00', 60, 0, 'وردية مسائية من 4 عصراً إلى 12 منتصف الليل', 1, '2025-06-14 12:10:54', '2025-06-14 12:10:54'),
(6, 1, 'الوردية الليلية', '00:00:00', '08:00:00', 60, 0, 'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً', 1, '2025-06-14 12:10:54', '2025-06-14 12:10:54');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `plan_id` int(11) NOT NULL,
  `plan_name` varchar(100) NOT NULL,
  `plan_name_ar` varchar(100) NOT NULL,
  `plan_description` text DEFAULT NULL,
  `plan_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `plan_duration_days` int(11) NOT NULL DEFAULT 30,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`plan_id`, `plan_name`, `plan_name_ar`, `plan_description`, `plan_price`, `plan_duration_days`, `is_active`, `is_default`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'basic', 'الخطة الأساسية', 'خطة مناسبة للمحلات الصغيرة مع الميزات الأساسية', 99.00, 30, 1, 1, 1, '2025-06-17 18:50:02', '2025-06-17 18:50:02'),
(2, 'premium', 'الخطة المميزة', 'خطة متقدمة للمحلات المتوسطة مع ميزات إضافية', 199.00, 30, 1, 0, 2, '2025-06-17 18:50:02', '2025-06-17 18:50:02'),
(3, 'enterprise', 'الخطة المؤسسية', 'خطة شاملة للمحلات الكبيرة مع جميع الميزات', 399.00, 30, 1, 0, 3, '2025-06-17 18:50:02', '2025-06-17 18:50:02'),
(4, 'unlimited', 'الخطة اللامحدودة', 'خطة بلا حدود لأصحاب السلاسل التجارية', 799.00, 30, 1, 0, 4, '2025-06-17 18:50:02', '2025-06-17 18:50:02');

-- --------------------------------------------------------

--
-- Table structure for table `trial_cafeteria_items`
--

CREATE TABLE `trial_cafeteria_items` (
  `id` int(11) NOT NULL,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trial_cafeteria_items`
--

INSERT INTO `trial_cafeteria_items` (`id`, `trial_id`, `name`, `price`, `category`, `description`, `created_at`) VALUES
(26, 8, 'شاي', 5.00, 'مشروبات ساخنة', 'شاي سادة', '2025-06-20 05:40:32'),
(27, 8, 'قهوة', 10.00, 'مشروبات ساخنة', 'قهوة تركي', '2025-06-20 05:40:32'),
(28, 8, 'بيبسي', 12.00, 'مشروبات باردة', 'مشروب غازي', '2025-06-20 05:40:32'),
(29, 8, 'ساندويتش', 25.00, 'مأكولات', 'ساندويتش جبن', '2025-06-20 05:40:32'),
(30, 8, 'asd', 10.00, 'مشروبات ساخنة', '', '2025-06-20 05:41:09');

-- --------------------------------------------------------

--
-- Table structure for table `trial_clients`
--

CREATE TABLE `trial_clients` (
  `trial_id` int(11) NOT NULL,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `trial_start` timestamp NOT NULL DEFAULT current_timestamp(),
  `trial_end` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trial_clients`
--

INSERT INTO `trial_clients` (`trial_id`, `business_name`, `owner_name`, `email`, `phone`, `password_hash`, `trial_start`, `trial_end`, `is_active`, `created_at`) VALUES
(8, 'test', 'محمود', '<EMAIL>', '***********', '$2y$10$19ZcG3aqmp0rdBcYdphswuYMMKP/eYQCkvVRqH./WCSD6/zgqgjma', '2025-06-20 05:40:32', '2025-06-20 10:40:32', 1, '2025-06-20 05:40:32');

-- --------------------------------------------------------

--
-- Table structure for table `trial_customers`
--

CREATE TABLE `trial_customers` (
  `customer_id` int(11) NOT NULL,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trial_customers`
--

INSERT INTO `trial_customers` (`customer_id`, `trial_id`, `name`, `phone`, `email`, `created_at`) VALUES
(25, 8, 'أحمد محمد', '***********', '<EMAIL>', '2025-06-20 05:40:32'),
(26, 8, 'فاطمة علي', '01234567891', '<EMAIL>', '2025-06-20 05:40:32'),
(27, 8, 'محمد حسن', '01234567892', '<EMAIL>', '2025-06-20 05:40:32'),
(28, 8, 'سارة أحمد', '01234567893', '<EMAIL>', '2025-06-20 05:40:32');

-- --------------------------------------------------------

--
-- Table structure for table `trial_devices`
--

CREATE TABLE `trial_devices` (
  `device_id` int(11) NOT NULL,
  `trial_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 15.00,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trial_devices`
--

INSERT INTO `trial_devices` (`device_id`, `trial_id`, `device_name`, `device_type`, `hourly_rate`, `status`, `created_at`) VALUES
(25, 8, 'جهاز PS5 - تجريبي', 'PS5', 20.00, 'available', '2025-06-20 05:40:32'),
(26, 8, 'جهاز PS4 - تجريبي', 'PS4', 15.00, 'available', '2025-06-20 05:40:32'),
(27, 8, 'جهاز Xbox - تجريبي', 'Xbox', 18.00, 'available', '2025-06-20 05:40:32'),
(28, 8, 'جهاز PC - تجريبي', 'PC', 25.00, 'occupied', '2025-06-20 05:40:32');

-- --------------------------------------------------------

--
-- Table structure for table `trial_sessions`
--

CREATE TABLE `trial_sessions` (
  `session_id` int(11) NOT NULL,
  `trial_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` datetime DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trial_sessions`
--

INSERT INTO `trial_sessions` (`session_id`, `trial_id`, `device_id`, `customer_id`, `start_time`, `end_time`, `duration_minutes`, `total_cost`, `status`, `created_at`) VALUES
(4, 8, 28, 25, '2025-06-20 05:40:53', NULL, 0, 0.00, 'active', '2025-06-20 05:40:53');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure for view `attendance_detailed`
--
DROP TABLE IF EXISTS `attendance_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `attendance_detailed`  AS SELECT 1 AS `attendance_id`, 1 AS `shift_id`, 1 AS `employee_id`, 1 AS `employee_name`, 1 AS `employee_role`, 1 AS `shift_name`, 1 AS `shift_date`, 1 AS `scheduled_start`, 1 AS `scheduled_end`, 1 AS `check_in_time`, 1 AS `check_out_time`, 1 AS `break_start_time`, 1 AS `break_end_time`, 1 AS `actual_hours`, 1 AS `overtime_hours`, 1 AS `break_hours`, 1 AS `attendance_status`, 1 AS `late_minutes`, 1 AS `early_leave_minutes`, 1 AS `attendance_notes`, 1 AS `role_in_shift`, 1 AS `is_mandatory` ;

-- --------------------------------------------------------

--
-- Structure for view `client_limits_usage`
--
DROP TABLE IF EXISTS `client_limits_usage`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `client_limits_usage`  AS SELECT `c`.`client_id` AS `client_id`, `c`.`business_name` AS `business_name`, `c`.`subscription_plan` AS `subscription_plan`, 'devices' AS `feature_type`, 'max_devices' AS `feature_name`, coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) AS `feature_limit`, count(`d`.`device_id`) AS `current_usage`, CASE WHEN coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) = -1 THEN 'unlimited' WHEN count(`d`.`device_id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) THEN 'limit_reached' WHEN count(`d`.`device_id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) * 0.8 THEN 'warning' ELSE 'normal' END AS `usage_status` FROM ((((`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) left join `plan_features` `pf` on(`sp`.`plan_id` = `pf`.`plan_id` and `pf`.`feature_type` = 'devices' and `pf`.`feature_name` = 'max_devices')) left join `client_plan_limits` `cpl` on(`c`.`client_id` = `cpl`.`client_id` and `cpl`.`feature_type` = 'devices' and `cpl`.`feature_name` = 'max_devices')) left join `devices` `d` on(`c`.`client_id` = `d`.`client_id`)) WHERE `c`.`is_active` = 1 GROUP BY `c`.`client_id`union all select `c`.`client_id` AS `client_id`,`c`.`business_name` AS `business_name`,`c`.`subscription_plan` AS `subscription_plan`,'products' AS `feature_type`,'max_products' AS `feature_name`,coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) AS `feature_limit`,count(`ci`.`id`) AS `current_usage`,case when coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) = -1 then 'unlimited' when count(`ci`.`id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) then 'limit_reached' when count(`ci`.`id`) >= coalesce(`cpl`.`custom_limit`,`pf`.`feature_limit`,0) * 0.8 then 'warning' else 'normal' end AS `usage_status` from ((((`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) left join `plan_features` `pf` on(`sp`.`plan_id` = `pf`.`plan_id` and `pf`.`feature_type` = 'products' and `pf`.`feature_name` = 'max_products')) left join `client_plan_limits` `cpl` on(`c`.`client_id` = `cpl`.`client_id` and `cpl`.`feature_type` = 'products' and `cpl`.`feature_name` = 'max_products')) left join `cafeteria_items` `ci` on(`c`.`client_id` = `ci`.`client_id`)) where `c`.`is_active` = 1 group by `c`.`client_id`  ;

-- --------------------------------------------------------

--
-- Structure for view `client_page_permissions_detailed`
--
DROP TABLE IF EXISTS `client_page_permissions_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `client_page_permissions_detailed`  AS SELECT `c`.`client_id` AS `client_id`, `c`.`business_name` AS `business_name`, `c`.`owner_name` AS `owner_name`, `c`.`is_active` AS `client_active`, `cp`.`page_id` AS `page_id`, `cp`.`page_name` AS `page_name`, `cp`.`page_label` AS `page_label`, `cp`.`page_url` AS `page_url`, `cp`.`page_icon` AS `page_icon`, `cp`.`category` AS `category`, `cp`.`description` AS `description`, `cp`.`is_default` AS `is_default`, coalesce(`cpp`.`is_enabled`,`cp`.`is_default`) AS `has_permission`, `cpp`.`granted_at` AS `granted_at`, `cpp`.`updated_at` AS `updated_at` FROM ((`clients` `c` join `client_pages` `cp`) left join `client_page_permissions` `cpp` on(`c`.`client_id` = `cpp`.`client_id` and `cp`.`page_id` = `cpp`.`page_id`)) WHERE `cp`.`is_active` = 1 ORDER BY `c`.`client_id` ASC, `cp`.`category` ASC, `cp`.`page_label` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `client_plan_details`
--
DROP TABLE IF EXISTS `client_plan_details`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `client_plan_details`  AS SELECT `c`.`client_id` AS `client_id`, `c`.`business_name` AS `business_name`, `c`.`owner_name` AS `owner_name`, `c`.`subscription_plan` AS `subscription_plan`, `sp`.`plan_name_ar` AS `plan_name_ar`, `sp`.`plan_description` AS `plan_description`, `sp`.`plan_price` AS `plan_price`, `sp`.`plan_duration_days` AS `plan_duration_days`, `c`.`subscription_start` AS `subscription_start`, `c`.`subscription_end` AS `subscription_end`, to_days(`c`.`subscription_end`) - to_days(curdate()) AS `days_remaining`, CASE WHEN `c`.`subscription_end` < curdate() THEN 'expired' WHEN to_days(`c`.`subscription_end`) - to_days(curdate()) <= 7 THEN 'expiring_soon' ELSE 'active' END AS `subscription_status` FROM (`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) WHERE `c`.`is_active` = 1 ;

-- --------------------------------------------------------

--
-- Structure for view `client_plan_summary`
--
DROP TABLE IF EXISTS `client_plan_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `client_plan_summary`  AS SELECT `c`.`client_id` AS `client_id`, `c`.`business_name` AS `business_name`, `c`.`subscription_plan` AS `subscription_plan`, `sp`.`plan_name_ar` AS `plan_name_ar`, `sp`.`plan_price` AS `plan_price`, `c`.`subscription_end` AS `subscription_end`, CASE WHEN `c`.`subscription_end` < curdate() THEN 'منتهية' WHEN to_days(`c`.`subscription_end`) - to_days(curdate()) <= 7 THEN 'تنتهي قريباً' ELSE 'نشطة' END AS `status_ar` FROM (`clients` `c` left join `subscription_plans` `sp` on(`c`.`subscription_plan` = `sp`.`plan_name`)) WHERE `c`.`is_active` = 1 ;

-- --------------------------------------------------------

--
-- Structure for view `employee_pages_detailed`
--
DROP TABLE IF EXISTS `employee_pages_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `employee_pages_detailed`  AS SELECT `e`.`id` AS `employee_id`, `e`.`name` AS `employee_name`, `e`.`role` AS `employee_role`, `e`.`custom_permissions` AS `custom_permissions`, `pg`.`page_id` AS `page_id`, `pg`.`page_name` AS `page_name`, `pg`.`page_label` AS `page_label`, `pg`.`page_url` AS `page_url`, `pg`.`page_icon` AS `page_icon`, `pg`.`category` AS `page_category`, `epg`.`granted_at` AS `granted_at` FROM ((`employees` `e` left join `employee_pages` `epg` on(`e`.`id` = `epg`.`employee_id`)) left join `pages` `pg` on(`epg`.`page_id` = `pg`.`page_id`)) WHERE `e`.`is_active` = 1 AND (`pg`.`is_active` = 1 OR `pg`.`is_active` is null) ;

-- --------------------------------------------------------

--
-- Structure for view `employee_permissions_detailed`
--
DROP TABLE IF EXISTS `employee_permissions_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `employee_permissions_detailed`  AS SELECT `e`.`id` AS `employee_id`, `e`.`name` AS `employee_name`, `e`.`role` AS `employee_role`, `e`.`custom_permissions` AS `custom_permissions`, `p`.`permission_id` AS `permission_id`, `p`.`permission_name` AS `permission_name`, `p`.`permission_label` AS `permission_label`, `p`.`category` AS `permission_category`, `ep`.`granted_at` AS `granted_at` FROM ((`employees` `e` left join `employee_permissions` `ep` on(`e`.`id` = `ep`.`employee_id`)) left join `permissions` `p` on(`ep`.`permission_id` = `p`.`permission_id`)) WHERE `e`.`is_active` = 1 AND (`p`.`is_active` = 1 OR `p`.`is_active` is null) ;

-- --------------------------------------------------------

--
-- Structure for view `inventory_overview`
--
DROP TABLE IF EXISTS `inventory_overview`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `inventory_overview`  AS SELECT `ci`.`id` AS `id`, `ci`.`name` AS `name`, `ci`.`price` AS `selling_price`, `ci`.`cost_price` AS `cost_price`, `ci`.`stock_quantity` AS `stock_quantity`, `ci`.`min_stock_level` AS `min_stock_level`, `ci`.`max_stock_level` AS `max_stock_level`, `ci`.`status` AS `status`, `ci`.`barcode` AS `barcode`, `ci`.`supplier` AS `supplier`, `ci`.`category` AS `category`, `ci`.`description` AS `description`, `ci`.`last_restock_date` AS `last_restock_date`, `ci`.`created_at` AS `created_at`, `ci`.`updated_at` AS `updated_at`, `ci`.`client_id` AS `client_id`, `pc`.`name` AS `category_name`, `pc`.`icon` AS `category_icon`, `pc`.`color` AS `category_color`, CASE WHEN `ci`.`stock_quantity` <= 0 THEN 'نفذ' WHEN `ci`.`stock_quantity` <= `ci`.`min_stock_level` THEN 'ناقص' WHEN `ci`.`stock_quantity` >= `ci`.`max_stock_level` THEN 'مكتمل' ELSE 'متوفر' END AS `stock_status_text`, CASE WHEN `ci`.`stock_quantity` <= 0 THEN 'danger' WHEN `ci`.`stock_quantity` <= `ci`.`min_stock_level` THEN 'warning' WHEN `ci`.`stock_quantity` >= `ci`.`max_stock_level` THEN 'info' ELSE 'success' END AS `stock_status_class`, `ci`.`price`- `ci`.`cost_price` AS `profit_margin`, round((`ci`.`price` - `ci`.`cost_price`) / `ci`.`price` * 100,2) AS `profit_percentage` FROM (`cafeteria_items` `ci` left join `product_categories` `pc` on(`ci`.`category` = `pc`.`name` and `ci`.`client_id` = `pc`.`client_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `monthly_attendance_stats`
--
DROP TABLE IF EXISTS `monthly_attendance_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `monthly_attendance_stats`  AS SELECT `e`.`id` AS `employee_id`, `e`.`name` AS `employee_name`, `e`.`client_id` AS `client_id`, year(`s`.`shift_date`) AS `year`, month(`s`.`shift_date`) AS `month`, count(`sa`.`attendance_id`) AS `total_shifts`, count(case when `sa`.`status` = 'present' then 1 end) AS `present_days`, count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_days`, count(case when `sa`.`status` = 'late' then 1 end) AS `late_days`, sum(`sa`.`actual_hours`) AS `total_hours`, sum(`sa`.`overtime_hours`) AS `total_overtime`, avg(`sa`.`late_minutes`) AS `avg_late_minutes`, count(case when `sa`.`status` = 'present' then 1 end) * 100.0 / count(`sa`.`attendance_id`) AS `attendance_percentage` FROM (((`employees` `e` join `employee_shifts` `es` on(`e`.`id` = `es`.`employee_id`)) join `shifts` `s` on(`es`.`shift_id` = `s`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) GROUP BY `e`.`id`, year(`s`.`shift_date`), month(`s`.`shift_date`) ;

-- --------------------------------------------------------

--
-- Structure for view `shifts_detailed`
--
DROP TABLE IF EXISTS `shifts_detailed`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `shifts_detailed`  AS SELECT `s`.`shift_id` AS `shift_id`, `s`.`client_id` AS `client_id`, `s`.`shift_name` AS `shift_name`, `s`.`shift_date` AS `shift_date`, `s`.`start_time` AS `start_time`, `s`.`end_time` AS `end_time`, `s`.`break_duration` AS `break_duration`, `s`.`is_overnight` AS `is_overnight`, `s`.`max_employees` AS `max_employees`, `s`.`min_employees` AS `min_employees`, `s`.`status` AS `shift_status`, `s`.`notes` AS `shift_notes`, `st`.`template_name` AS `template_name`, count(`es`.`assignment_id`) AS `assigned_employees`, count(case when `es`.`status` = 'confirmed' then 1 end) AS `confirmed_employees`, count(case when `sa`.`status` = 'present' then 1 end) AS `present_employees`, count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_employees` FROM (((`shifts` `s` left join `shift_templates` `st` on(`s`.`template_id` = `st`.`template_id`)) left join `employee_shifts` `es` on(`s`.`shift_id` = `es`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) GROUP BY `s`.`shift_id` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `additional_income`
--
ALTER TABLE `additional_income`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_additional_income_client` (`client_id`),
  ADD KEY `idx_additional_income_type` (`income_type_id`),
  ADD KEY `idx_additional_income_date` (`income_date`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `admin_settings`
--
ALTER TABLE `admin_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_client_setting` (`client_id`,`setting_key`);

--
-- Indexes for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_per_client` (`name`,`category`,`client_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `idx_cafeteria_search` (`name`,`category`,`client_id`),
  ADD KEY `idx_stock_status` (`status`,`stock_quantity`),
  ADD KEY `idx_low_stock` (`stock_quantity`,`min_stock_level`),
  ADD KEY `idx_barcode` (`barcode`),
  ADD KEY `idx_supplier` (`supplier`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`category_id`);

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`client_id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `client_pages`
--
ALTER TABLE `client_pages`
  ADD PRIMARY KEY (`page_id`),
  ADD UNIQUE KEY `page_name` (`page_name`);

--
-- Indexes for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_client_page` (`client_id`,`page_id`),
  ADD KEY `page_id` (`page_id`);

--
-- Indexes for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  ADD PRIMARY KEY (`limit_id`),
  ADD UNIQUE KEY `unique_client_feature` (`client_id`,`feature_type`,`feature_name`);

--
-- Indexes for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_client` (`client_id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`customer_id`),
  ADD UNIQUE KEY `idx_phone_client` (`phone`,`client_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_client_id` (`client_id`),
  ADD KEY `idx_customers_client_id` (`client_id`),
  ADD KEY `idx_customers_phone` (`phone`),
  ADD KEY `idx_customers_client` (`client_id`);

--
-- Indexes for table `devices`
--
ALTER TABLE `devices`
  ADD PRIMARY KEY (`device_id`),
  ADD KEY `room_id` (`room_id`),
  ADD KEY `idx_devices_client_status` (`client_id`,`status`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `fk_employee_client` (`client_id`);

--
-- Indexes for table `employee_pages`
--
ALTER TABLE `employee_pages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_employee_page` (`employee_id`,`page_id`),
  ADD KEY `idx_employee_pages_employee` (`employee_id`),
  ADD KEY `idx_employee_pages_page` (`page_id`);

--
-- Indexes for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_employee_permission` (`employee_id`,`permission_id`),
  ADD KEY `idx_employee_permissions_employee` (`employee_id`),
  ADD KEY `idx_employee_permissions_permission` (`permission_id`);

--
-- Indexes for table `employee_shifts`
--
ALTER TABLE `employee_shifts`
  ADD PRIMARY KEY (`assignment_id`),
  ADD UNIQUE KEY `unique_employee_shift` (`shift_id`,`employee_id`),
  ADD KEY `employee_id` (`employee_id`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expenses_client` (`client_id`),
  ADD KEY `idx_expenses_type` (`expense_type_id`),
  ADD KEY `idx_expenses_date` (`expense_date`);

--
-- Indexes for table `expense_types`
--
ALTER TABLE `expense_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expense_types_client` (`client_id`);

--
-- Indexes for table `income_types`
--
ALTER TABLE `income_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_income_types_client` (`client_id`);

--
-- Indexes for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_inventory_movements_product` (`product_id`),
  ADD KEY `idx_inventory_movements_client` (`client_id`),
  ADD KEY `idx_inventory_movements_date` (`created_at`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`invoice_id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `idx_invoices_payment_status` (`payment_status`),
  ADD KEY `idx_invoices_client_id` (`client_id`);

--
-- Indexes for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_order_number` (`client_id`,`order_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `session_id` (`session_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_items_order_id` (`order_id`),
  ADD KEY `idx_order_items_product_id` (`product_id`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`page_id`),
  ADD UNIQUE KEY `page_name` (`page_name`),
  ADD KEY `idx_pages_category` (`category`);

--
-- Indexes for table `payment_status_log`
--
ALTER TABLE `payment_status_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_payment_log_invoice` (`invoice_id`),
  ADD KEY `idx_payment_log_date` (`changed_at`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `permission_name` (`permission_name`),
  ADD KEY `idx_permissions_category` (`category`);

--
-- Indexes for table `plan_features`
--
ALTER TABLE `plan_features`
  ADD PRIMARY KEY (`feature_id`),
  ADD UNIQUE KEY `unique_plan_feature` (`plan_id`,`feature_type`,`feature_name`);

--
-- Indexes for table `plan_usage_log`
--
ALTER TABLE `plan_usage_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_client_feature` (`client_id`,`feature_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`product_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `section_id` (`section_id`);

--
-- Indexes for table `product_categories`
--
ALTER TABLE `product_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_category_per_client` (`name`,`client_id`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `idx_categories_client` (`client_id`);

--
-- Indexes for table `product_device_associations`
--
ALTER TABLE `product_device_associations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_device` (`product_id`,`device_id`,`device_part`),
  ADD KEY `device_id` (`device_id`);

--
-- Indexes for table `rooms`
--
ALTER TABLE `rooms`
  ADD PRIMARY KEY (`room_id`),
  ADD KEY `client_id` (`client_id`);

--
-- Indexes for table `sections`
--
ALTER TABLE `sections`
  ADD PRIMARY KEY (`section_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `idx_sessions_client_id` (`client_id`),
  ADD KEY `idx_sessions_status` (`status`),
  ADD KEY `idx_sessions_device_id` (`device_id`),
  ADD KEY `idx_sessions_start_time` (`start_time`),
  ADD KEY `idx_sessions_device_status` (`device_id`,`status`),
  ADD KEY `idx_sessions_client_device` (`client_id`,`device_id`),
  ADD KEY `idx_sessions_game_type` (`game_type`);

--
-- Indexes for table `session_products`
--
ALTER TABLE `session_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `shifts`
--
ALTER TABLE `shifts`
  ADD PRIMARY KEY (`shift_id`),
  ADD KEY `template_id` (`template_id`),
  ADD KEY `idx_shifts_date` (`shift_date`),
  ADD KEY `idx_shifts_status` (`status`),
  ADD KEY `idx_shifts_client` (`client_id`);

--
-- Indexes for table `shift_attendance`
--
ALTER TABLE `shift_attendance`
  ADD PRIMARY KEY (`attendance_id`),
  ADD KEY `assignment_id` (`assignment_id`),
  ADD KEY `idx_attendance_date` (`check_in_time`),
  ADD KEY `idx_attendance_employee` (`employee_id`),
  ADD KEY `idx_attendance_shift` (`shift_id`);

--
-- Indexes for table `shift_notifications`
--
ALTER TABLE `shift_notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `shift_id` (`shift_id`),
  ADD KEY `idx_scheduled_time` (`scheduled_time`),
  ADD KEY `idx_employee_sent` (`employee_id`,`is_sent`),
  ADD KEY `idx_client_type` (`client_id`,`notification_type`);

--
-- Indexes for table `shift_settings`
--
ALTER TABLE `shift_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_client_setting` (`client_id`,`setting_name`);

--
-- Indexes for table `shift_templates`
--
ALTER TABLE `shift_templates`
  ADD PRIMARY KEY (`template_id`),
  ADD KEY `client_id` (`client_id`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`plan_id`),
  ADD UNIQUE KEY `plan_name` (`plan_name`);

--
-- Indexes for table `trial_cafeteria_items`
--
ALTER TABLE `trial_cafeteria_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `trial_id` (`trial_id`);

--
-- Indexes for table `trial_clients`
--
ALTER TABLE `trial_clients`
  ADD PRIMARY KEY (`trial_id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `trial_customers`
--
ALTER TABLE `trial_customers`
  ADD PRIMARY KEY (`customer_id`),
  ADD KEY `trial_id` (`trial_id`);

--
-- Indexes for table `trial_devices`
--
ALTER TABLE `trial_devices`
  ADD PRIMARY KEY (`device_id`),
  ADD KEY `trial_id` (`trial_id`);

--
-- Indexes for table `trial_sessions`
--
ALTER TABLE `trial_sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `trial_id` (`trial_id`),
  ADD KEY `device_id` (`device_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `additional_income`
--
ALTER TABLE `additional_income`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_settings`
--
ALTER TABLE `admin_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=66;

--
-- AUTO_INCREMENT for table `business_settings`
--
ALTER TABLE `business_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `clients`
--
ALTER TABLE `clients`
  MODIFY `client_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `client_pages`
--
ALTER TABLE `client_pages`
  MODIFY `page_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=83;

--
-- AUTO_INCREMENT for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  MODIFY `limit_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `devices`
--
ALTER TABLE `devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `employee_pages`
--
ALTER TABLE `employee_pages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `employee_shifts`
--
ALTER TABLE `employee_shifts`
  MODIFY `assignment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `expense_types`
--
ALTER TABLE `expense_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `income_types`
--
ALTER TABLE `income_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `page_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `payment_status_log`
--
ALTER TABLE `payment_status_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `plan_features`
--
ALTER TABLE `plan_features`
  MODIFY `feature_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=205;

--
-- AUTO_INCREMENT for table `plan_usage_log`
--
ALTER TABLE `plan_usage_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `product_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_categories`
--
ALTER TABLE `product_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `product_device_associations`
--
ALTER TABLE `product_device_associations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rooms`
--
ALTER TABLE `rooms`
  MODIFY `room_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sections`
--
ALTER TABLE `sections`
  MODIFY `section_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sessions`
--
ALTER TABLE `sessions`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `session_products`
--
ALTER TABLE `session_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `shifts`
--
ALTER TABLE `shifts`
  MODIFY `shift_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `shift_attendance`
--
ALTER TABLE `shift_attendance`
  MODIFY `attendance_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `shift_notifications`
--
ALTER TABLE `shift_notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `shift_settings`
--
ALTER TABLE `shift_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `shift_templates`
--
ALTER TABLE `shift_templates`
  MODIFY `template_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `plan_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `trial_cafeteria_items`
--
ALTER TABLE `trial_cafeteria_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `trial_clients`
--
ALTER TABLE `trial_clients`
  MODIFY `trial_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `trial_customers`
--
ALTER TABLE `trial_customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `trial_devices`
--
ALTER TABLE `trial_devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `trial_sessions`
--
ALTER TABLE `trial_sessions`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD CONSTRAINT `business_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_page_permissions`
--
ALTER TABLE `client_page_permissions`
  ADD CONSTRAINT `client_page_permissions_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `client_page_permissions_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `client_pages` (`page_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_plan_limits`
--
ALTER TABLE `client_plan_limits`
  ADD CONSTRAINT `client_plan_limits_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `client_theme_settings`
--
ALTER TABLE `client_theme_settings`
  ADD CONSTRAINT `client_theme_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`);

--
-- Constraints for table `devices`
--
ALTER TABLE `devices`
  ADD CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `devices_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`);

--
-- Constraints for table `employees`
--
ALTER TABLE `employees`
  ADD CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `employee_pages`
--
ALTER TABLE `employee_pages`
  ADD CONSTRAINT `employee_pages_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `employee_pages_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `pages` (`page_id`) ON DELETE CASCADE;

--
-- Constraints for table `employee_permissions`
--
ALTER TABLE `employee_permissions`
  ADD CONSTRAINT `employee_permissions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `employee_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE;

--
-- Constraints for table `employee_shifts`
--
ALTER TABLE `employee_shifts`
  ADD CONSTRAINT `employee_shifts_ibfk_1` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `employee_shifts_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD CONSTRAINT `inventory_movements_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`);

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_features`
--
ALTER TABLE `plan_features`
  ADD CONSTRAINT `plan_features_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`plan_id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_usage_log`
--
ALTER TABLE `plan_usage_log`
  ADD CONSTRAINT `plan_usage_log_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  ADD CONSTRAINT `products_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`);

--
-- Constraints for table `product_categories`
--
ALTER TABLE `product_categories`
  ADD CONSTRAINT `product_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `product_device_associations`
--
ALTER TABLE `product_device_associations`
  ADD CONSTRAINT `product_device_associations_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_device_associations_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE;

--
-- Constraints for table `rooms`
--
ALTER TABLE `rooms`
  ADD CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_device_fk` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `sessions_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`);

--
-- Constraints for table `session_products`
--
ALTER TABLE `session_products`
  ADD CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`),
  ADD CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`);

--
-- Constraints for table `shifts`
--
ALTER TABLE `shifts`
  ADD CONSTRAINT `shifts_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `shifts_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `shift_templates` (`template_id`) ON DELETE SET NULL;

--
-- Constraints for table `shift_attendance`
--
ALTER TABLE `shift_attendance`
  ADD CONSTRAINT `shift_attendance_ibfk_1` FOREIGN KEY (`assignment_id`) REFERENCES `employee_shifts` (`assignment_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `shift_attendance_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `shift_attendance_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `shift_notifications`
--
ALTER TABLE `shift_notifications`
  ADD CONSTRAINT `shift_notifications_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `shift_notifications_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `shift_notifications_ibfk_3` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE;

--
-- Constraints for table `shift_settings`
--
ALTER TABLE `shift_settings`
  ADD CONSTRAINT `shift_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `shift_templates`
--
ALTER TABLE `shift_templates`
  ADD CONSTRAINT `shift_templates_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `trial_cafeteria_items`
--
ALTER TABLE `trial_cafeteria_items`
  ADD CONSTRAINT `trial_cafeteria_items_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE;

--
-- Constraints for table `trial_customers`
--
ALTER TABLE `trial_customers`
  ADD CONSTRAINT `trial_customers_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE;

--
-- Constraints for table `trial_devices`
--
ALTER TABLE `trial_devices`
  ADD CONSTRAINT `trial_devices_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE;

--
-- Constraints for table `trial_sessions`
--
ALTER TABLE `trial_sessions`
  ADD CONSTRAINT `trial_sessions_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `trial_sessions_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `trial_devices` (`device_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `trial_sessions_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `trial_customers` (`customer_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
