-- إنشاء نظام صلاحيات الموظفين المتقدم - PlayGood
-- تشغيل هذا الملف لإضافة نظام صلاحيات مخصص للموظفين

-- 1. إ<PERSON><PERSON><PERSON><PERSON> جدول الصلاحيات المتاحة
CREATE TABLE IF NOT EXISTS permissions (
    permission_id INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_label VARCHAR(200) NOT NULL,
    permission_description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الصفحات المتاحة
CREATE TABLE IF NOT EXISTS pages (
    page_id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) NOT NULL UNIQUE,
    page_label VARCHAR(200) NOT NULL,
    page_url VARCHAR(255) NOT NULL,
    page_icon VARCHAR(50) DEFAULT 'fas fa-file',
    category VARCHAR(50) DEFAULT 'general',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. إنشاء جدول صلاحيات الموظفين (Many-to-Many)
CREATE TABLE IF NOT EXISTS employee_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_by INT DEFAULT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_permission (employee_id, permission_id)
);

-- 4. إنشاء جدول صفحات الموظفين (Many-to-Many)
CREATE TABLE IF NOT EXISTS employee_pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    page_id INT NOT NULL,
    granted_by INT DEFAULT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES pages(page_id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_page (employee_id, page_id)
);

-- 5. إدراج الصلاحيات الأساسية
INSERT IGNORE INTO permissions (permission_name, permission_label, permission_description, category) VALUES
-- إدارة الأجهزة
('manage_devices', 'إدارة الأجهزة', 'إضافة وتعديل وحذف الأجهزة', 'devices'),
('view_devices', 'عرض الأجهزة', 'عرض قائمة الأجهزة فقط', 'devices'),

-- إدارة الجلسات
('manage_sessions', 'إدارة الجلسات', 'بدء وإنهاء وتعديل الجلسات', 'sessions'),
('view_sessions', 'عرض الجلسات', 'عرض قائمة الجلسات فقط', 'sessions'),

-- إدارة الغرف
('manage_rooms', 'إدارة الغرف', 'إضافة وتعديل وحذف الغرف', 'rooms'),
('view_rooms', 'عرض الغرف', 'عرض قائمة الغرف فقط', 'rooms'),

-- إدارة العملاء
('manage_customers', 'إدارة العملاء', 'إضافة وتعديل وحذف العملاء', 'customers'),
('view_customers', 'عرض العملاء', 'عرض قائمة العملاء فقط', 'customers'),

-- إدارة الكافتيريا
('manage_cafeteria', 'إدارة الكافتيريا', 'إدارة منتجات وطلبات الكافتيريا', 'cafeteria'),
('view_cafeteria', 'عرض الكافتيريا', 'عرض منتجات الكافتيريا فقط', 'cafeteria'),

-- إدارة الموظفين
('manage_employees', 'إدارة الموظفين', 'إضافة وتعديل وحذف الموظفين', 'employees'),
('view_employees', 'عرض الموظفين', 'عرض قائمة الموظفين فقط', 'employees'),

-- التقارير والمالية
('view_reports', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports'),
('view_finances', 'عرض المالية', 'عرض البيانات المالية', 'finances'),
('manage_finances', 'إدارة المالية', 'إدارة المصروفات والإيرادات', 'finances'),

-- الفواتير
('manage_invoices', 'إدارة الفواتير', 'إنشاء وتعديل الفواتير', 'invoices'),
('view_invoices', 'عرض الفواتير', 'عرض الفواتير فقط', 'invoices'),

-- الإعدادات
('manage_settings', 'إدارة الإعدادات', 'تعديل إعدادات النظام', 'settings'),
('view_profile', 'عرض الملف الشخصي', 'عرض وتعديل الملف الشخصي', 'profile');

-- 6. إدراج الصفحات المتاحة
INSERT IGNORE INTO pages (page_name, page_label, page_url, page_icon, category) VALUES
-- الصفحات الأساسية
('dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main'),
('profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main'),

-- إدارة الأجهزة والغرف
('devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices'),
('rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices'),

-- إدارة الجلسات والعملاء
('sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions'),
('customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers'),

-- الكافتيريا
('cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria'),

-- الموظفين
('employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees'),

-- التقارير والمالية
('reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports'),
('finances', 'المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances'),
('invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices'),

-- الإعدادات
('settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings');

-- 7. إضافة عمود للصلاحيات المخصصة في جدول الموظفين
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS custom_permissions BOOLEAN DEFAULT FALSE COMMENT 'هل يستخدم صلاحيات مخصصة أم صلاحيات الدور';

-- 8. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_employee_permissions_employee ON employee_permissions(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_permissions_permission ON employee_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_employee_pages_employee ON employee_pages(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_pages_page ON employee_pages(page_id);
CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category);
CREATE INDEX IF NOT EXISTS idx_pages_category ON pages(category);

-- 9. إنشاء view لعرض صلاحيات الموظفين بشكل مفصل
CREATE OR REPLACE VIEW employee_permissions_detailed AS
SELECT 
    e.id as employee_id,
    e.name as employee_name,
    e.role as employee_role,
    e.custom_permissions,
    p.permission_id,
    p.permission_name,
    p.permission_label,
    p.category as permission_category,
    ep.granted_at
FROM employees e
LEFT JOIN employee_permissions ep ON e.id = ep.employee_id
LEFT JOIN permissions p ON ep.permission_id = p.permission_id
WHERE e.is_active = 1 AND (p.is_active = 1 OR p.is_active IS NULL);

-- 10. إنشاء view لعرض صفحات الموظفين بشكل مفصل
CREATE OR REPLACE VIEW employee_pages_detailed AS
SELECT 
    e.id as employee_id,
    e.name as employee_name,
    e.role as employee_role,
    e.custom_permissions,
    pg.page_id,
    pg.page_name,
    pg.page_label,
    pg.page_url,
    pg.page_icon,
    pg.category as page_category,
    epg.granted_at
FROM employees e
LEFT JOIN employee_pages epg ON e.id = epg.employee_id
LEFT JOIN pages pg ON epg.page_id = pg.page_id
WHERE e.is_active = 1 AND (pg.is_active = 1 OR pg.is_active IS NULL);

-- تم الانتهاء من إنشاء نظام الصلاحيات
SELECT 'تم إنشاء نظام صلاحيات الموظفين المتقدم بنجاح!' as message;
