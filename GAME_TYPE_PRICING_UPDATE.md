# تحديث نظام التسعير للعب الفردي والزوجي - PlayGood

## نظرة عامة
تم تحديث نظام إدارة الجلسات ليدعم أسعار مختلفة للعب الفردي والزوجي، مما يتيح للعملاء تحديد نوع اللعب عند بدء الجلسة مع تطبيق السعر المناسب.

## التحديثات المنجزة

### 1. تحديث قاعدة البيانات
- جدول `devices` يحتوي على أعمدة:
  - `single_rate`: سعر اللعب الفردي (لاعب واحد)
  - `multi_rate`: سعر اللعب الزوجي (أكثر من لاعب)
  - `hourly_rate`: السعر العام (احتياطي)

- جدول `sessions` يحتوي على عمود:
  - `game_type`: نوع اللعب ('single' أو 'multiplayer')

### 2. تحديث واجهة بدء الجلسة
- إضافة خيار اختيار نوع اللعب (فردي/زوجي) في نافذة إضافة جلسة جديدة
- تحديث التكلفة المتوقعة تلقائياً عند تغيير نوع اللعب أو الجهاز
- عرض السعر المحدد لكل نوع لعب

### 3. تحديث حساب التكلفة
- **في الجلسات النشطة**: يتم حساب التكلفة الحالية بناءً على نوع اللعب المختار
- **عند إنهاء الجلسة**: يتم استخدام السعر المناسب (فردي أو زوجي) لحساب التكلفة النهائية
- **في عرض الجلسات**: يظهر نوع اللعب مع التكلفة المحسوبة بناءً عليه

### 4. تحديث واجهة المستخدم
- عرض نوع اللعب في بطاقات الجلسات النشطة مع أيقونات مميزة:
  - 👤 فردي (أزرق)
  - 👥 زوجي (أحمر)
- عرض السعر المحدد في التكلفة المتوقعة
- تحديث تلقائي للتكلفة عند تغيير أي من المعاملات

## الملفات المحدثة

### 1. `client/sessions.php`
- تحديث منطق حساب التكلفة في الجلسات النشطة
- تحديث منطق حساب التكلفة عند إنهاء الجلسة
- تحديث دالة `updateEstimatedCostAdd()` JavaScript
- إضافة مستمعي أحداث لتحديث التكلفة عند تغيير نوع اللعب

### 2. `client/dashboard.php` (جديد)
- إضافة خيار نوع اللعب في النافذة المنبثقة لبدء الجلسة
- تحديث عرض الأسعار في بطاقات الأجهزة (فردي/زوجي)
- تحديث دالة `updateEstimatedCost()` لدعم نوع اللعب
- إضافة بيانات الأسعار في أزرار بدء الجلسة
- إضافة مستمعي أحداث لتحديث التكلفة عند تغيير نوع اللعب

### 3. `fix_device_pricing.php` (جديد)
- فحص وإصلاح أسعار الأجهزة
- إضافة أسعار افتراضية للأجهزة التي تفتقر للأسعار
- إضافة أجهزة تجريبية إذا كان العدد قليل

### 4. `test_game_pricing.php` (جديد)
- اختبار شامل لنظام التسعير الجديد في Sessions
- إنشاء جلسات تجريبية لاختبار الأسعار
- عرض الجلسات النشطة مع التكلفة المحسوبة

### 5. `test_dashboard_pricing.php` (جديد)
- اختبار شامل لنظام التسعير الجديد في Dashboard
- فحص بيانات الأزرار وأسعار الأجهزة
- محاكاة حساب التكلفة لسيناريوهات مختلفة
- التحقق من تطبيق جميع التحديثات

## كيفية الاستخدام

### 1. إصلاح أسعار الأجهزة (مطلوب مرة واحدة)
```
http://localhost/playgood/fix_device_pricing.php
```
- يفحص جميع الأجهزة ويضيف أسعار افتراضية للفردي والزوجي
- يضيف أجهزة تجريبية إذا كان العدد قليل

### 2. اختبار النظام
```
http://localhost/playgood/test_game_pricing.php
```
- عرض الأجهزة وأسعارها
- إنشاء جلسات تجريبية لاختبار التسعير
- مراقبة الجلسات النشطة والتكلفة

### 3. اختبار Dashboard
```
http://localhost/playgood/test_dashboard_pricing.php
```
- فحص أسعار الأجهزة في Dashboard
- اختبار حساب التكلفة لسيناريوهات مختلفة
- التحقق من تطبيق جميع التحديثات

### 4. استخدام النظام العادي

#### أ) من خلال Dashboard:
```
http://localhost/playgood/client/dashboard.php
```
- بدء جلسة جديدة من بطاقات الأجهزة مع اختيار نوع اللعب
- عرض الأسعار المختلفة (فردي/زوجي) في بطاقات الأجهزة
- حساب التكلفة المتوقعة تلقائياً

#### ب) من خلال صفحة الجلسات:
```
http://localhost/playgood/client/sessions.php
```
- بدء جلسة جديدة مع اختيار نوع اللعب
- مراقبة الجلسات النشطة مع التكلفة الصحيحة
- إنهاء الجلسات مع حساب التكلفة النهائية

## الأسعار الافتراضية

| نوع الجهاز | السعر الفردي | السعر الزوجي |
|------------|--------------|--------------|
| PS5        | 25.00 ج.م    | 40.00 ج.م    |
| PS4        | 20.00 ج.م    | 35.00 ج.م    |
| PC         | 30.00 ج.م    | 50.00 ج.م    |
| Xbox       | 25.00 ج.م    | 40.00 ج.م    |

## المميزات الجديدة

### 1. تسعير ذكي
- أسعار مختلفة للعب الفردي والزوجي
- حساب تلقائي للتكلفة بناءً على نوع اللعب
- عرض واضح للسعر المطبق

### 2. واجهة محسنة
- اختيار سهل لنوع اللعب
- تحديث فوري للتكلفة المتوقعة
- عرض مرئي لنوع اللعب في الجلسات

### 3. مرونة في التسعير
- إمكانية تعديل أسعار كل جهاز بشكل منفصل
- دعم أسعار مختلفة لكل نوع جهاز
- احتياطي للسعر العام إذا لم تكن الأسعار المحددة متوفرة

## ملاحظات مهمة

1. **يجب تشغيل `fix_device_pricing.php` مرة واحدة** لإعداد الأسعار الأولية
2. **الجلسات القديمة** ستستخدم السعر الفردي كافتراضي
3. **يمكن تعديل الأسعار** من خلال إدارة الأجهزة
4. **النظام متوافق** مع الجلسات الموجودة مسبقاً

## استكشاف الأخطاء

### إذا لم تظهر الأسعار بشكل صحيح:
1. تأكد من تشغيل `fix_device_pricing.php`
2. تحقق من وجود قيم في `single_rate` و `multi_rate` في جدول `devices`
3. تأكد من أن JavaScript يعمل بشكل صحيح

### إذا لم تعمل التكلفة المتوقعة:
1. تحقق من وحدة التحكم في المتصفح للأخطاء
2. تأكد من أن الأجهزة لديها أسعار صحيحة
3. تحقق من أن نوع اللعب محدد بشكل صحيح

## التطوير المستقبلي

- إضافة تقارير مفصلة حسب نوع اللعب
- إمكانية تعديل الأسعار من واجهة الإدارة
- دعم أنواع لعب إضافية (VIP، Tournament، إلخ)
- تحليلات الاستخدام حسب نوع اللعب
