<?php
/**
 * إصلاح مشكلة عدم ظهور المنتجات في modal تعديل الجلسة
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>إصلاح مشكلة عدم ظهور المنتجات</h1>";
echo "<hr>";

try {
    echo "<h2>1. فحص وإصلاح جدول session_products</h2>";
    
    // فحص وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'session_products'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول session_products غير موجود - جاري الإنشاء...</p>";
        
        $create_table = "
            CREATE TABLE session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INT NULL DEFAULT NULL,
                notes TEXT NULL DEFAULT NULL,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($create_table);
        echo "<p style='color: green;'>✅ تم إنشاء جدول session_products</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول session_products موجود</p>";
    }
    
    // فحص أعمدة الجدول
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'session_id' => 'INT NOT NULL',
        'product_id' => 'INT NOT NULL',
        'quantity' => 'INT NOT NULL DEFAULT 1',
        'price' => 'DECIMAL(10,2) NOT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];
    
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $columns)) {
            echo "<p style='color: orange;'>⚠️ إضافة عمود $column...</p>";
            try {
                $pdo->exec("ALTER TABLE session_products ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود $column: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>2. إنشاء بيانات تجريبية للاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id, device_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة - سأنشئ واحدة...</p>";
        
        // البحث عن جهاز متاح
        $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
        $device = $stmt->fetch();
        
        if (!$device) {
            // إنشاء جهاز تجريبي
            $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, single_rate, multi_rate, status, client_id) 
                       VALUES ('جهاز تجريبي', 'PS5', 15.00, 10.00, 20.00, 'available', 1)");
            $device_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جهاز تجريبي: $device_id</p>";
        } else {
            $device_id = $device['device_id'];
        }
        
        // إنشاء جلسة تجريبية
        $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
        $stmt->execute([$device_id, 1, 1]);
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام جلسة موجودة: $session_id</p>";
    }
    
    // إنشاء منتجات تجريبية إذا لم تكن موجودة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cafeteria_items WHERE client_id = 1");
    $products_count = $stmt->fetch()['count'];
    
    if ($products_count == 0) {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات - سأنشئ بعض المنتجات التجريبية...</p>";
        
        $test_products = [
            ['كوكاكولا', 3.50, 'مشروبات'],
            ['شيبس', 2.00, 'وجبات خفيفة'],
            ['قهوة', 5.00, 'مشروبات ساخنة'],
            ['ساندويتش', 8.00, 'وجبات'],
            ['عصير', 4.00, 'مشروبات']
        ];
        
        foreach ($test_products as $product) {
            $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
            $stmt->execute([$product[0], $product[1], $product[2], 1]);
            echo "<p style='color: green;'>✅ تم إنشاء منتج: {$product[0]} - {$product[1]} ج.م</p>";
        }
    }
    
    // إضافة منتجات للجلسة التجريبية
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ?");
    $stmt->execute([$session_id]);
    $session_products_count = $stmt->fetch()['count'];
    
    if ($session_products_count == 0) {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات مضافة للجلسة - سأضيف بعض المنتجات...</p>";
        
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 3");
        $available_products = $stmt->fetchAll();
        
        foreach ($available_products as $product) {
            $quantity = rand(1, 3);
            $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
            $stmt->execute([$session_id, $product['id'], $quantity, $product['price']]);
            echo "<p style='color: green;'>✅ تم إضافة منتج للجلسة: {$product['name']} × $quantity</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ الجلسة تحتوي على $session_products_count منتج</p>";
    }
    
    echo "<h2>3. اختبار API جلب المنتجات</h2>";
    
    // اختبار API مباشرة
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/get_session_products.php?session_id=' . $session_id;
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";
    
    $json_response = json_decode($response, true);
    if ($json_response && isset($json_response['success']) && $json_response['success']) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ API يعمل بشكل صحيح!</strong></p>";
        echo "<p>عدد المنتجات المسترجعة: " . count($json_response['products']) . "</p>";
        
        if (count($json_response['products']) > 0) {
            echo "<h3>المنتجات المسترجعة:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th>اسم المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>";
            
            foreach ($json_response['products'] as $product) {
                echo "<tr>";
                echo "<td>{$product['product_name']}</td>";
                echo "<td>{$product['quantity']}</td>";
                echo "<td>{$product['price']} ج.م</td>";
                echo "<td>{$product['total']} ج.م</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ API أرجع خطأ</strong></p>";
        echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
    }
    
    echo "<h2>4. إنشاء ملف JavaScript محسن</h2>";
    
    // إنشاء ملف JavaScript محسن لتحميل المنتجات
    $js_content = "
// دالة محسنة لتحميل المنتجات في modal التعديل
function loadEditSessionProducts(sessionId) {
    console.log('Loading products for session:', sessionId);
    
    if (!sessionId) {
        console.error('Session ID is required');
        return;
    }
    
    const productsContainer = document.getElementById('edit_session_products');
    if (!productsContainer) {
        console.error('Products container not found: edit_session_products');
        return;
    }
    
    // إظهار مؤشر التحميل
    productsContainer.innerHTML = '<div class=\"text-center p-3\"><div class=\"spinner-border text-primary\" role=\"status\"></div><p class=\"mt-2\">جاري تحميل المنتجات...</p></div>';
    
    fetch(`api/get_session_products.php?session_id=\${sessionId}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: \${response.status}`);
            }
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    if (data.products && data.products.length > 0) {
                        // حساب إجمالي تكلفة المنتجات
                        const totalProductsCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);
                        
                        productsContainer.innerHTML = `
                            <div class=\"list-group\">
                                \${data.products.map(product => `
                                    <div class=\"list-group-item d-flex justify-content-between align-items-center\">
                                        <div>
                                            <h6 class=\"mb-0\">\${product.product_name}</h6>
                                            <small class=\"text-muted\">
                                                \${product.quantity} × \${product.price} ج.م
                                            </small>
                                        </div>
                                        <div class=\"d-flex align-items-center\">
                                            <span class=\"me-3 fw-bold\">\${product.total} ج.م</span>
                                            <button class=\"btn btn-danger btn-sm\"
                                                    onclick=\"deleteEditSessionProduct(\${sessionId}, \${product.product_id})\"
                                                    title=\"حذف المنتج\">
                                                <i class=\"fas fa-trash\"></i>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class=\"mt-2 p-2 bg-light rounded\">
                                <div class=\"d-flex justify-content-between\">
                                    <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                    <span class=\"fw-bold text-primary\">\${totalProductsCost.toFixed(2)} ج.م</span>
                                </div>
                            </div>
                        `;
                    } else {
                        productsContainer.innerHTML = `
                            <div class=\"text-center p-3\">
                                <i class=\"fas fa-coffee fa-2x text-muted mb-2\"></i>
                                <p class=\"text-muted mb-0\">لا توجد منتجات مضافة</p>
                                <small class=\"text-muted\">اضغط على \"إضافة منتج\" لإضافة منتجات للجلسة</small>
                            </div>
                        `;
                    }
                } else {
                    throw new Error(data.error || 'فشل في جلب المنتجات');
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                productsContainer.innerHTML = `
                    <div class=\"alert alert-danger\">
                        <strong>خطأ في تحليل البيانات:</strong> \${jsonError.message}
                        <details class=\"mt-2\">
                            <summary>تفاصيل الخطأ</summary>
                            <pre style=\"font-size: 12px; max-height: 200px; overflow-y: auto;\">\${responseText}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            productsContainer.innerHTML = `
                <div class=\"alert alert-danger\">
                    <strong>خطأ في تحميل المنتجات:</strong> \${error.message}
                    <button class=\"btn btn-sm btn-outline-primary mt-2\" onclick=\"loadEditSessionProducts(\${sessionId})\">
                        <i class=\"fas fa-redo\"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// التأكد من استدعاء الدالة عند فتح modal التعديل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع لحدث فتح modal التعديل
    const editModal = document.getElementById('editSessionModal');
    if (editModal) {
        editModal.addEventListener('shown.bs.modal', function() {
            const sessionId = document.getElementById('edit_session_id').value;
            if (sessionId) {
                console.log('Modal opened, loading products for session:', sessionId);
                loadEditSessionProducts(sessionId);
            }
        });
    }
});
";
    
    file_put_contents('assets/js/session-products-fix.js', $js_content);
    echo "<p style='color: green;'>✅ تم إنشاء ملف JavaScript محسن: assets/js/session-products-fix.js</p>";
    
    echo "<h2>5. تعليمات الإصلاح</h2>";
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h3>لإصلاح المشكلة نهائياً:</h3>";
    echo "<ol>";
    echo "<li><strong>أضف الملف JavaScript المحسن:</strong><br>";
    echo "<code>&lt;script src=\"assets/js/session-products-fix.js\"&gt;&lt;/script&gt;</code></li>";
    echo "<li><strong>تأكد من استدعاء loadEditSessionProducts عند فتح modal</strong></li>";
    echo "<li><strong>تحقق من console المتصفح للأخطاء</strong></li>";
    echo "<li><strong>اختبر API مباشرة:</strong> <a href='$api_url' target='_blank'>$api_url</a></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. اختبار مباشر</h2>";
    echo "<p>معرف الجلسة للاختبار: <strong>$session_id</strong></p>";
    echo "<button onclick=\"testLoadProducts($session_id)\" style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>اختبار تحميل المنتجات</button>";
    echo "<div id='test-result' style='margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white;'></div>";
    
    ?>
    <script>
    function testLoadProducts(sessionId) {
        const resultDiv = document.getElementById('test-result');
        resultDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</p>';
        
        fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
            .then(response => response.text())
            .then(responseText => {
                try {
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <p style="color: green;"><strong>✅ نجح الاختبار!</strong></p>
                            <p>عدد المنتجات: ${data.products.length}</p>
                            <p>التكلفة الإجمالية: ${data.total_cost} ج.م</p>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <p style="color: red;"><strong>❌ فشل الاختبار:</strong> ${data.error}</p>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ خطأ في JSON:</strong> ${error.message}</p>
                        <details>
                            <summary>الاستجابة الخام</summary>
                            <pre style="background: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<p style="color: red;"><strong>❌ خطأ في الشبكة:</strong> ${error.message}</p>`;
            });
    }
    </script>
    <?php

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
table {
    font-family: Arial, sans-serif;
    font-size: 14px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    font-weight: bold;
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
