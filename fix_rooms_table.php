<?php
/**
 * سكريپت إصلاح جدول الغرف
 * يتحقق من هيكل الجدول ويضيف الأعمدة المفقودة
 */

require_once 'config/database.php';

echo "<h1>إصلاح جدول الغرف - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    echo "<h2>1. فحص جدول rooms</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'rooms'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول rooms غير موجود</p>";
        
        // إنشاء الجدول
        $create_table_sql = "
            CREATE TABLE rooms (
                room_id INT AUTO_INCREMENT PRIMARY KEY,
                room_name VARCHAR(100) NOT NULL,
                description TEXT,
                client_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_rooms_client_id (client_id),
                INDEX idx_rooms_name (room_name)
            )
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول rooms</p>";
        
        // إضافة غرف تجريبية
        $sample_rooms = [
            ['الصالة الرئيسية', 'صالة الألعاب الرئيسية'],
            ['غرفة VIP', 'غرفة خاصة للعملاء المميزين'],
            ['قسم البلايستيشن', 'منطقة ألعاب البلايستيشن'],
            ['قسم الكمبيوتر', 'منطقة ألعاب الكمبيوتر'],
            ['الكافتيريا', 'منطقة المأكولات والمشروبات']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO rooms (room_name, description, client_id) VALUES (?, ?, 1)");
        foreach ($sample_rooms as $room) {
            $insert_stmt->execute($room);
        }
        echo "<p style='color: green;'>✅ تم إضافة 5 غرف تجريبية</p>";
        
    } else {
        echo "<p style='color: green;'>✅ جدول rooms موجود</p>";
    }
    
    echo "<h2>2. فحص هيكل الجدول</h2>";
    
    // فحص أعمدة الجدول
    $stmt = $pdo->query("DESCRIBE rooms");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $column_names = array_column($columns, 'Field');
    
    echo "<h3>الأعمدة الموجودة:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    // التحقق من الأعمدة المطلوبة
    $required_columns = [
        'room_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'room_name' => 'VARCHAR(100) NOT NULL',
        'description' => 'TEXT',
        'client_id' => 'INT NOT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    echo "<h3>فحص الأعمدة المطلوبة:</h3>";
    
    foreach ($required_columns as $column_name => $column_type) {
        if (in_array($column_name, $column_names)) {
            echo "<p style='color: green;'>✅ عمود $column_name موجود</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ عمود $column_name غير موجود - جاري الإضافة...</p>";
            
            try {
                switch ($column_name) {
                    case 'client_id':
                        $pdo->exec("ALTER TABLE rooms ADD COLUMN client_id INT NOT NULL DEFAULT 1");
                        echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
                        break;
                    case 'created_at':
                        $pdo->exec("ALTER TABLE rooms ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                        echo "<p style='color: green;'>✅ تم إضافة عمود created_at</p>";
                        break;
                    case 'updated_at':
                        $pdo->exec("ALTER TABLE rooms ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        echo "<p style='color: green;'>✅ تم إضافة عمود updated_at</p>";
                        break;
                    case 'description':
                        $pdo->exec("ALTER TABLE rooms ADD COLUMN description TEXT");
                        echo "<p style='color: green;'>✅ تم إضافة عمود description</p>";
                        break;
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>3. إضافة فهارس</h2>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_rooms_client_id ON rooms(client_id)" => "فهرس client_id",
        "CREATE INDEX IF NOT EXISTS idx_rooms_name ON rooms(room_name)" => "فهرس room_name"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ $description: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. تحديث البيانات</h2>";
    
    // التأكد من أن جميع الغرف لها client_id
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM rooms WHERE client_id IS NULL OR client_id = 0");
        $rooms_without_client = $stmt->fetchColumn();
        
        if ($rooms_without_client > 0) {
            $pdo->exec("UPDATE rooms SET client_id = 1 WHERE client_id IS NULL OR client_id = 0");
            echo "<p style='color: green;'>✅ تم تحديث $rooms_without_client غرفة بمعرف العميل الافتراضي</p>";
        } else {
            echo "<p style='color: green;'>✅ جميع الغرف لها معرف عميل صحيح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ تحديث البيانات: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. إضافة بيانات تجريبية (إذا كان الجدول فارغ)</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM rooms WHERE client_id = 1");
    $room_count = $stmt->fetchColumn();
    
    if ($room_count == 0) {
        echo "<p style='color: blue;'>ℹ️ لا توجد غرف للعميل - جاري إضافة بيانات تجريبية...</p>";
        
        $sample_rooms = [
            ['الصالة الرئيسية', 'صالة الألعاب الرئيسية مع أجهزة متنوعة'],
            ['غرفة VIP', 'غرفة خاصة للعملاء المميزين مع خدمات إضافية'],
            ['قسم البلايستيشن', 'منطقة مخصصة لألعاب البلايستيشن'],
            ['قسم الكمبيوتر', 'منطقة مخصصة لألعاب الكمبيوتر'],
            ['الكافتيريا', 'منطقة المأكولات والمشروبات'],
            ['غرفة الأطفال', 'منطقة آمنة ومناسبة للأطفال'],
            ['صالة البطولات', 'مخصصة للمسابقات والبطولات']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO rooms (room_name, description, client_id) VALUES (?, ?, 1)");
        
        foreach ($sample_rooms as $room) {
            try {
                $insert_stmt->execute($room);
                echo "<p style='color: green;'>✅ تم إضافة الغرفة: " . $room[0] . "</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة الغرفة " . $room[0] . ": " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ يوجد $room_count غرفة في النظام</p>";
    }
    
    echo "<h2>6. اختبار الاستعلامات</h2>";
    
    // اختبار استعلام الغرف
    try {
        $test_query = "
            SELECT r.*,
                   COUNT(d.device_id) as device_count,
                   COUNT(CASE WHEN d.status = 'available' THEN 1 END) as available_devices,
                   COUNT(CASE WHEN d.status = 'occupied' THEN 1 END) as occupied_devices
            FROM rooms r
            LEFT JOIN devices d ON r.room_id = d.room_id
            WHERE r.client_id = 1
            GROUP BY r.room_id, r.room_name, r.description
            ORDER BY r.room_name
            LIMIT 5
        ";
        $stmt = $pdo->query($test_query);
        $rooms = $stmt->fetchAll();
        
        echo "<p style='color: green;'>✅ استعلام الغرف يعمل بشكل صحيح</p>";
        echo "<p>تم جلب " . count($rooms) . " غرفة</p>";
        
        if (count($rooms) > 0) {
            echo "<h4>عينة من الغرف:</h4>";
            echo "<ul>";
            foreach ($rooms as $room) {
                echo "<li><strong>" . htmlspecialchars($room['room_name']) . "</strong> - " . $room['device_count'] . " جهاز</li>";
            }
            echo "</ul>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار استعلام الغرف: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>✅ تم الانتهاء من إصلاح جدول الغرف</h2>";
    echo "<p><a href='client/rooms.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة الغرف</a></p>";
    echo "<p><a href='client/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "</div>";
?>
