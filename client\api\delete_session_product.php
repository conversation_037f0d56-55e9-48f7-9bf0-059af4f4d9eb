<?php
// API مبسط لحذف المنتجات من الجلسات
header('Content-Type: application/json; charset=utf-8');

// بدء الجلسة
session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

// تضمين قاعدة البيانات
require_once '../../config/database.php';

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data || !isset($data['session_id']) || !isset($data['product_id'])) {
        throw new Exception('بيانات غير مكتملة');
    }

    $session_id = intval($data['session_id']);
    $product_id = intval($data['product_id']);

    // التحقق من وجود المنتج في الجلسة أولاً
    $check_stmt = $pdo->prepare("SELECT id, quantity FROM session_products WHERE session_id = ? AND product_id = ? ORDER BY id LIMIT 1");
    $check_stmt->execute([$session_id, $product_id]);
    $product_record = $check_stmt->fetch();

    if (!$product_record) {
        throw new Exception('المنتج غير موجود في هذه الجلسة');
    }

    $record_id = $product_record['id'];
    $current_quantity = $product_record['quantity'];

    if ($current_quantity > 1) {
        // إذا كانت الكمية أكثر من 1، قلل الكمية بـ 1
        $stmt = $pdo->prepare("UPDATE session_products SET quantity = quantity - 1 WHERE id = ?");
        $stmt->execute([$record_id]);
        $deleted_rows = 1; // نعتبر أنه تم "حذف" وحدة واحدة
    } else {
        // إذا كانت الكمية 1، احذف السجل كاملاً
        $stmt = $pdo->prepare("DELETE FROM session_products WHERE id = ?");
        $stmt->execute([$record_id]);
        $deleted_rows = $stmt->rowCount();
    }

    if ($deleted_rows == 0) {
        throw new Exception('فشل في حذف المنتج');
    }

    // حساب التكلفة الجديدة الكاملة (وقت + منتجات)
    $stmt = $pdo->prepare("
        SELECT
            s.session_id,
            s.start_time,
            d.single_rate,
            d.hourly_rate,
            COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost,
            TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN session_products sp ON s.session_id = sp.session_id
        WHERE s.session_id = ?
        GROUP BY s.session_id, s.start_time, d.single_rate, d.hourly_rate
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    // حساب التكلفة الكلية - استخدام single_rate أولاً
    $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
    $time_cost = $session['duration_minutes'] > 0 && $hourly_rate > 0 ? ceil($session['duration_minutes'] / 60) * $hourly_rate : 0;
    $total_cost = $time_cost + $session['products_cost'];

    // إرجاع النتيجة
    if ($current_quantity > 1) {
        $message = "تم تقليل الكمية بوحدة واحدة (الكمية الجديدة: " . ($current_quantity - 1) . ")";
    } else {
        $message = 'تم حذف المنتج بنجاح';
    }

    echo json_encode([
        'success' => true,
        'message' => $message,
        'total_cost' => number_format($total_cost, 2),
        'products_cost' => number_format($session['products_cost'], 2),
        'time_cost' => number_format($time_cost, 2),
        'session_id' => $session_id,
        'product_id' => $product_id,
        'deleted_rows' => $deleted_rows,
        'action' => $current_quantity > 1 ? 'quantity_reduced' : 'product_removed',
        'new_quantity' => $current_quantity > 1 ? ($current_quantity - 1) : 0
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}