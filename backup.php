<?php
$dbHost = 'localhost';
$dbName = 'station';
$dbUser = 'root';
$dbPass = '';
$backupDir = __DIR__ . '/backups';
$date = date('Y-m-d_H-i-s');
$backupFile = "{$backupDir}/{$dbName}_backup_{$date}.sql";

// أنشئ مجلد النسخ إن لم يكن موجودًا
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0777, true);
}

// أمر mysqldump
$command = "C:\\xampp\\mysql\\bin\\mysqldump.exe --user={$dbUser} --password={$dbPass} --host={$dbHost} {$dbName} > \"{$backupFile}\"";
exec($command, $output, $return_var);

// تحقق من نجاح العملية
if ($return_var === 0) {
    echo "✅ تم النسخ بنجاح: {$backupFile}\n";
} else {
    echo "❌ فشل النسخ. كود الخطأ: {$return_var}\n";
}
?>
