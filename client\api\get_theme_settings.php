<?php
/**
 * API لجلب إعدادات المظهر والألوان
 */

header('Content-Type: application/json; charset=utf-8');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مسموح بالوصول'
    ]);
    exit;
}

require_once __DIR__ . '/../../config/database.php';

$client_id = $_SESSION['client_id'];

try {
    // جلب إعدادات المظهر للعميل
    $theme_stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
    $theme_stmt->execute([$client_id]);
    $theme_settings = $theme_stmt->fetch(PDO::FETCH_ASSOC);

    // إعدادات افتراضية إذا لم توجد
    if (!$theme_settings) {
        $theme_settings = [
            'client_id' => $client_id,
            'primary_color' => '#0d6efd',
            'secondary_color' => '#6c757d',
            'accent_color' => '#20c997',
            'header_style' => 'top',
            'sidebar_position' => 'right',
            'theme_mode' => 'light',
            'created_at' => null,
            'updated_at' => null
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $theme_settings
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => [
            'client_id' => $client_id,
            'primary_color' => '#0d6efd',
            'secondary_color' => '#6c757d',
            'accent_color' => '#20c997',
            'header_style' => 'top',
            'sidebar_position' => 'right',
            'theme_mode' => 'light',
            'created_at' => null,
            'updated_at' => null
        ]
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'data' => [
            'client_id' => $client_id,
            'primary_color' => '#0d6efd',
            'secondary_color' => '#6c757d',
            'accent_color' => '#20c997',
            'header_style' => 'top',
            'sidebar_position' => 'right',
            'theme_mode' => 'light',
            'created_at' => null,
            'updated_at' => null
        ]
    ]);
}
?>
