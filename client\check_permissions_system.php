<?php
/**
 * ملف فحص نظام الصلاحيات
 * يتحقق من وجود الجداول والبيانات المطلوبة لنظام الصلاحيات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

echo "<h2>فحص نظام الصلاحيات</h2>";

try {
    // فحص الجداول المطلوبة
    $required_tables = [
        'permissions' => 'جدول الصلاحيات المتاحة',
        'pages' => 'جدول الصفحات المتاحة', 
        'employee_permissions' => 'جدول صلاحيات الموظفين',
        'employee_pages' => 'جدول صفحات الموظفين'
    ];
    
    echo "<h3>1. فحص الجداول:</h3>";
    foreach ($required_tables as $table => $description) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $description ($table) موجود<br>";
        } else {
            echo "❌ $description ($table) غير موجود<br>";
        }
    }
    
    // فحص العمود custom_permissions في جدول employees
    echo "<h3>2. فحص عمود الصلاحيات المخصصة:</h3>";
    $stmt = $pdo->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $has_custom_permissions = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'custom_permissions') {
            $has_custom_permissions = true;
            break;
        }
    }
    
    if ($has_custom_permissions) {
        echo "✅ عمود custom_permissions موجود في جدول employees<br>";
    } else {
        echo "❌ عمود custom_permissions غير موجود في جدول employees<br>";
        echo "<strong>يجب إضافة العمود:</strong><br>";
        echo "<code>ALTER TABLE employees ADD COLUMN custom_permissions BOOLEAN DEFAULT FALSE;</code><br>";
    }
    
    // فحص البيانات الأساسية
    echo "<h3>3. فحص البيانات الأساسية:</h3>";
    
    // فحص الصلاحيات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions WHERE is_active = 1");
    $permissions_count = $stmt->fetch()['count'];
    echo "عدد الصلاحيات المتاحة: $permissions_count<br>";
    
    if ($permissions_count > 0) {
        echo "✅ توجد صلاحيات في النظام<br>";
        
        // عرض الصلاحيات حسب الفئة
        $stmt = $pdo->query("SELECT category, COUNT(*) as count FROM permissions WHERE is_active = 1 GROUP BY category");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<strong>توزيع الصلاحيات:</strong><br>";
        foreach ($categories as $cat) {
            echo "- {$cat['category']}: {$cat['count']} صلاحية<br>";
        }
    } else {
        echo "❌ لا توجد صلاحيات في النظام<br>";
        echo "<strong>يجب تشغيل ملف:</strong> create_employee_permissions_system.sql<br>";
    }
    
    // فحص الصفحات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    echo "عدد الصفحات المتاحة: $pages_count<br>";
    
    if ($pages_count > 0) {
        echo "✅ توجد صفحات في النظام<br>";
    } else {
        echo "❌ لا توجد صفحات في النظام<br>";
    }
    
    // فحص الموظفين الذين يستخدمون صلاحيات مخصصة
    echo "<h3>4. فحص الموظفين:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees WHERE custom_permissions = 1");
    $custom_employees = $stmt->fetch()['count'];
    echo "عدد الموظفين الذين يستخدمون صلاحيات مخصصة: $custom_employees<br>";
    
    if ($custom_employees > 0) {
        $stmt = $pdo->query("
            SELECT e.name, e.role, COUNT(ep.permission_id) as permissions_count
            FROM employees e
            LEFT JOIN employee_permissions ep ON e.id = ep.employee_id
            WHERE e.custom_permissions = 1
            GROUP BY e.id, e.name, e.role
        ");
        $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<strong>تفاصيل الموظفين:</strong><br>";
        foreach ($employees as $emp) {
            echo "- {$emp['name']} ({$emp['role']}): {$emp['permissions_count']} صلاحية<br>";
        }
    }
    
    // اختبار الدوال
    echo "<h3>5. اختبار الدوال:</h3>";
    
    // تحميل ملف employee-auth.php
    require_once 'includes/employee-auth.php';
    
    // اختبار دالة employeeHasCustomPermission
    if (function_exists('employeeHasCustomPermission')) {
        echo "✅ دالة employeeHasCustomPermission موجودة<br>";
    } else {
        echo "❌ دالة employeeHasCustomPermission غير موجودة<br>";
    }
    
    // اختبار دالة employeeCanAccessPage
    if (function_exists('employeeCanAccessPage')) {
        echo "✅ دالة employeeCanAccessPage موجودة<br>";
    } else {
        echo "❌ دالة employeeCanAccessPage غير موجودة<br>";
    }
    
    // اختبار دالة getEmployeeCustomPermissions
    if (function_exists('getEmployeeCustomPermissions')) {
        echo "✅ دالة getEmployeeCustomPermissions موجودة<br>";
    } else {
        echo "❌ دالة getEmployeeCustomPermissions غير موجودة<br>";
    }
    
    echo "<h3>✅ انتهى الفحص</h3>";
    
    // إرشادات الإعداد
    echo "<h3>إرشادات الإعداد:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل ملف create_employee_permissions_system.sql</li>";
    echo "<li>تأكد من وجود عمود custom_permissions في جدول employees</li>";
    echo "<li>اختبر تعديل موظف وتفعيل الصلاحيات المخصصة</li>";
    echo "<li>تأكد من عمل دوال التحقق من الصلاحيات</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p>الرسالة: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='employees.php'>العودة لصفحة الموظفين</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

code {
    background: #e9ecef;
    padding: 5px 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

ol, ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
