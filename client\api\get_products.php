<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

try {
    // معاملات البحث والصفحات
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 12; // عدد المنتجات في الصفحة
    $offset = ($page - 1) * $limit;
    $search = trim($_GET['search'] ?? '');

    // بناء استعلام البحث
    $where_conditions = ['client_id = ?'];
    $params = [$client_id];

    if (!empty($search)) {
        $where_conditions[] = '(name LIKE ? OR category LIKE ? OR description LIKE ?)';
        $search_term = '%' . $search . '%';
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }

    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

    // عد إجمالي المنتجات
    $count_query = "SELECT COUNT(*) FROM cafeteria_items $where_clause";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_products = $count_stmt->fetchColumn();

    // حساب عدد الصفحات
    $total_pages = ceil($total_products / $limit);

    // جلب المنتجات
    $products_query = "
        SELECT id, name, price, category, description
        FROM cafeteria_items
        $where_clause
        ORDER BY category ASC, name ASC
        LIMIT $limit OFFSET $offset
    ";

    $products_stmt = $pdo->prepare($products_query);
    $products_stmt->execute($params);
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنسيق البيانات
    $formatted_products = array_map(function($product) {
        return [
            'id' => (int)$product['id'],
            'name' => htmlspecialchars($product['name']),
            'price' => (float)$product['price'], // إرسال السعر كرقم وليس نص منسق
            'price_formatted' => number_format((float)$product['price'], 2), // السعر المنسق للعرض
            'category' => htmlspecialchars($product['category'] ?? ''),
            'description' => htmlspecialchars($product['description'] ?? '')
        ];
    }, $products);

    // إضافة تنسيق مبسط للتوافق مع الكود الموجود
    echo json_encode([
        'success' => true,
        'products' => $formatted_products, // للتوافق مع الكود القديم
        'data' => [
            'products' => $formatted_products,
            'currentPage' => $page,
            'totalPages' => $total_pages,
            'totalProducts' => $total_products,
            'hasNextPage' => $page < $total_pages,
            'hasPrevPage' => $page > 1
        ],
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_products' => $total_products,
            'per_page' => $limit
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ غير متوقع'
    ]);
}