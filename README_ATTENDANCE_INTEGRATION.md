# ربط نظام الجلسات بالحضور والانصراف - PlayGood

تم تطبيق نظام ربط إنشاء الجلسات بحضور الموظفين بنجاح! الآن الموظفين لا يستطيعون إنشاء جلسات جديدة إلا بعد تسجيل الحضور في وردية نشطة.

## 🎯 الهدف من النظام

- **التحكم في الوصول**: منع الموظفين من إنشاء جلسات بدون تسجيل حضور
- **تتبع المسؤولية**: ربط كل جلسة بموظف مسجل حضور
- **الانضباط**: ضمان التزام الموظفين بأوقات العمل
- **الأمان**: منع الاستخدام غير المصرح به للنظام

## ✅ الميزات المطبقة

### 1. **التحقق من الحضور قبل إنشاء الجلسات**
- يتم التحقق تلقائياً من حضور الموظف قبل السماح بإنشاء جلسة
- رسائل تنبيه واضحة للموظفين غير المسجلين حضور
- توجيه مباشر لصفحة تسجيل الحضور

### 2. **حالات الحضور المختلفة**
- **لم يسجل حضور**: منع إنشاء الجلسات
- **مسجل حضور**: السماح بإنشاء الجلسات
- **في استراحة**: منع إنشاء الجلسات
- **منصرف**: منع إنشاء الجلسات

### 3. **واجهة مستخدم محسنة**
- تنبيهات ملونة لحالة الحضور
- تعطيل الأزرار عند عدم السماح
- رسائل توضيحية للموظفين
- روابط سريعة لتسجيل الحضور

### 4. **صفحة تسجيل الحضور السريع**
- واجهة بسيطة للموظفين لتسجيل الحضور
- عرض حالة الحضور الحالية
- أزرار كبيرة وواضحة للعمليات
- تحديث تلقائي للبيانات

## 📁 الملفات المحدثة

### الملفات الأساسية
- **`includes/shift_helpers.php`** - دوال التحقق من الحضور
- **`client/sessions.php`** - ربط الجلسات بالحضور
- **`client/attendance.php`** - تحديث نظام الحضور
- **`client/quick_attendance.php`** - صفحة تسجيل الحضور السريع
- **`client/includes/sidebar.php`** - إضافة رابط الحضور السريع

### الدوال الجديدة
```php
// التحقق من حضور الموظف
isEmployeeCheckedIn($pdo, $employee_id)

// التحقق من إمكانية إنشاء الجلسات
canEmployeeCreateSessions($pdo, $employee_id)

// حساب ساعات العمل
calculateAttendanceHours($pdo, $attendance_id)
```

## 🔧 كيفية عمل النظام

### 1. **عند محاولة إنشاء جلسة**
```php
// التحقق من إمكانية الموظف إنشاء جلسات
if (isset($_SESSION['employee_id']) && !$can_create_sessions) {
    $_SESSION['error'] = $attendance_message;
    header('Location: sessions.php');
    exit;
}
```

### 2. **في واجهة المستخدم**
```php
// تعطيل الأزرار حسب حالة الحضور
<?php echo (!$can_create_sessions && isset($_SESSION['employee_id'])) ? 'disabled' : ''; ?>
```

### 3. **في JavaScript**
```javascript
// منع إظهار النموذج إذا لم يكن مسجل حضور
<?php if (!$can_create_sessions && isset($_SESSION['employee_id'])): ?>
    Swal.fire({
        icon: 'warning',
        title: 'تسجيل الحضور مطلوب',
        text: 'يجب تسجيل الحضور في وردية نشطة أولاً'
    });
    return;
<?php endif; ?>
```

## 🎮 سيناريوهات الاستخدام

### السيناريو 1: موظف يحاول إنشاء جلسة بدون حضور
1. الموظف يضغط على "إضافة جلسة جديدة"
2. يظهر تنبيه: "يجب تسجيل الحضور أولاً"
3. يتم توجيهه لصفحة الحضور
4. بعد تسجيل الحضور، يمكنه إنشاء الجلسات

### السيناريو 2: موظف مسجل حضور
1. يظهر تنبيه أخضر: "مسجل حضور ويمكن إنشاء جلسات"
2. جميع أزرار إنشاء الجلسات متاحة
3. يمكن إنشاء جلسات بشكل طبيعي

### السيناريو 3: موظف في استراحة
1. يظهر تنبيه تحذيري: "لا يمكن إنشاء جلسات أثناء الاستراحة"
2. أزرار إنشاء الجلسات معطلة
3. يجب إنهاء الاستراحة أولاً

## 🔐 الاستثناءات

### أصحاب الحسابات (Clients)
- **لا يخضعون لقيود الحضور**
- يمكنهم إنشاء جلسات في أي وقت
- النظام يطبق فقط على الموظفين

### الموظفين بصلاحيات خاصة
- يمكن إضافة استثناءات للمديرين
- صلاحيات خاصة لتجاوز قيود الحضور (مستقبلاً)

## 📱 واجهة تسجيل الحضور السريع

### الميزات
- **واجهة بسيطة ومباشرة**
- **أزرار كبيرة وواضحة**
- **عرض حالة الحضور الحالية**
- **تحديث تلقائي كل 30 ثانية**
- **روابط سريعة للصفحات الأخرى**

### العمليات المتاحة
- تسجيل الحضور
- بدء الاستراحة
- انتهاء الاستراحة
- تسجيل الانصراف

## 🚀 التشغيل والاستخدام

### 1. تشغيل النظام
```bash
# تأكد من تشغيل نظام الورديات أولاً
http://localhost/playgood/setup_shifts_system_simple.php
```

### 2. إعداد الورديات
- أنشئ ورديات للموظفين
- خصص الموظفين للورديات المناسبة

### 3. تسجيل الحضور
- الموظفين يسجلون حضورهم عبر:
  - صفحة الحضور الرئيسية: `attendance.php`
  - صفحة الحضور السريع: `quick_attendance.php`

### 4. إنشاء الجلسات
- بعد تسجيل الحضور، يمكن إنشاء الجلسات بشكل طبيعي

## 🔧 الإعدادات المتقدمة

### تخصيص رسائل التنبيه
```php
// في ملف shift_helpers.php
'message' => 'يجب تسجيل الحضور في وردية نشطة أولاً لإنشاء جلسات جديدة'
```

### إضافة استثناءات
```php
// يمكن إضافة شروط إضافية في canEmployeeCreateSessions()
if ($employee_role == 'manager') {
    return ['can_create' => true, 'reason' => 'manager_exception'];
}
```

## 📊 التقارير والمراقبة

### تتبع الاستخدام
- ربط كل جلسة بموظف مسجل حضور
- تقارير الحضور والجلسات
- إحصائيات الاستخدام

### المراقبة
- سجلات الحضور والانصراف
- أوقات إنشاء الجلسات
- ربط الأنشطة بالموظفين

## 🛠️ الصيانة والتطوير

### إضافة ميزات جديدة
- إشعارات تلقائية للموظفين
- تكامل مع أنظمة البصمة
- تطبيق موبايل للحضور

### الأمان
- تشفير بيانات الحضور
- سجلات مراجعة شاملة
- حماية من التلاعب

---

## 📞 الدعم

للحصول على المساعدة:
1. راجع سجلات الأخطاء في PHP
2. تحقق من صلاحيات الموظفين
3. تأكد من إعداد الورديات بشكل صحيح

تم تطوير هذا النظام لضمان الانضباط والمسؤولية في استخدام نظام إدارة الجلسات! 🎯
