# إصلاح مشكلة حذف التصنيفات في صفحة الكافتيريا

## المشكلة
عند محاولة حذف تصنيف في صفحة الكافتيريا، لا يتم الحذف بسبب وجود قيود مرجعية (Foreign Key Constraints) في قاعدة البيانات تمنع حذف التصنيفات المرتبطة بمنتجات.

## السبب
- يوجد قيد مرجعي بين جدول `cafeteria_items` وجدول `categories`
- القيد المرجعي مضبوط على `RESTRICT` أو `NO ACTION` مما يمنع حذف التصنيف
- النظام يستخدم أسماء التصنيفات (`category`) وليس معرفات التصنيفات (`category_id`) في الربط

## الحل

### الطريقة الأولى: تشغيل سكريبت الإصلاح التلقائي
1. افتح المتصفح واذهب إلى: `http://localhost/playgood/fix_category_deletion_issue.php`
2. سيقوم السكريبت بفحص المشكلة وإصلاحها تلقائياً
3. اتبع التعليمات المعروضة على الشاشة

### الطريقة الثانية: الإصلاح اليدوي
إذا كنت تفضل الإصلاح اليدوي، قم بتنفيذ الاستعلامات التالية في قاعدة البيانات:

```sql
-- 1. فحص القيود المرجعية الحالية
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME,
    DELETE_RULE
FROM information_schema.REFERENTIAL_CONSTRAINTS rc
JOIN information_schema.KEY_COLUMN_USAGE kcu 
    ON rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
WHERE rc.CONSTRAINT_SCHEMA = DATABASE()
AND TABLE_NAME = 'cafeteria_items' 
AND REFERENCED_TABLE_NAME = 'categories';

-- 2. إزالة القيد المرجعي المشكل (استبدل CONSTRAINT_NAME بالاسم الفعلي)
ALTER TABLE cafeteria_items DROP FOREIGN KEY cafeteria_items_ibfk_1;

-- 3. التأكد من أن عمود category_id يقبل NULL
ALTER TABLE cafeteria_items MODIFY COLUMN category_id INT NULL;

-- 4. إضافة قيد مرجعي محسن
ALTER TABLE cafeteria_items 
ADD CONSTRAINT fk_cafeteria_category_safe 
FOREIGN KEY (category_id) REFERENCES categories(category_id) 
ON DELETE SET NULL ON UPDATE CASCADE;
```

## التحسينات المطبقة

### في ملف `client/cafeteria.php`:
1. **تحسين التحقق من صحة البيانات**: التأكد من صحة معرف التصنيف
2. **استخدام المعاملات**: استخدام `beginTransaction()` و `commit()` لضمان سلامة البيانات
3. **تنظيف المراجع**: تنظيف أي مراجع للتصنيف في جدول المنتجات قبل الحذف
4. **رسائل خطأ محسنة**: رسائل خطأ أكثر وضوحاً مع إرشادات للحل
5. **رابط سكريبت الإصلاح**: عرض رابط لسكريبت الإصلاح عند حدوث خطأ قيود قاعدة البيانات

### في ملف `fix_category_deletion_issue.php`:
1. **فحص شامل**: فحص القيود المرجعية وحالة قاعدة البيانات
2. **إصلاح تلقائي**: إزالة القيود المشكلة وإضافة قيود محسنة
3. **اختبار الوظيفة**: إنشاء تصنيف تجريبي واختبار حذفه
4. **تقرير مفصل**: عرض تقرير مفصل عن الإصلاحات المطبقة

## كيفية عمل الحل

1. **القيد المرجعي الجديد**: يسمح بـ `SET NULL` عند حذف التصنيف
2. **تنظيف البيانات**: تنظيف أي مراجع للتصنيف المحذوف في جدول المنتجات
3. **المعاملات الآمنة**: استخدام معاملات قاعدة البيانات لضمان سلامة العمليات
4. **التحقق المسبق**: التحقق من وجود منتجات مرتبطة قبل السماح بالحذف

## اختبار الحل

1. اذهب إلى صفحة الكافتيريا: `client/cafeteria.php`
2. أنشئ تصنيف جديد
3. تأكد من عدم وجود منتجات مرتبطة بالتصنيف
4. جرب حذف التصنيف
5. يجب أن يتم الحذف بنجاح

## ملاحظات مهمة

- **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق الإصلاحات
- **البيئة التجريبية**: جرب الحل في بيئة تجريبية أولاً
- **المنتجات المرتبطة**: لا يمكن حذف تصنيف يحتوي على منتجات، يجب حذف المنتجات أو تغيير تصنيفها أولاً
- **الصلاحيات**: تأكد من أن المستخدم لديه صلاحيات كافية لتعديل قاعدة البيانات

## استكشاف الأخطاء

### إذا استمرت المشكلة:
1. تحقق من سجلات أخطاء PHP
2. تأكد من تطبيق جميع الإصلاحات
3. تحقق من صلاحيات قاعدة البيانات
4. تأكد من عدم وجود قيود مرجعية أخرى

### رسائل الخطأ الشائعة:
- `Cannot delete or update a parent row`: قيد مرجعي يمنع الحذف
- `Foreign key constraint fails`: مشكلة في القيود المرجعية
- `Access denied`: مشكلة في صلاحيات قاعدة البيانات

## الدعم
إذا واجهت أي مشاكل، تحقق من:
1. إعدادات قاعدة البيانات
2. صلاحيات المستخدم
3. سجلات الأخطاء
4. بنية الجداول

---
**تاريخ الإنشاء**: 2025-06-20  
**الإصدار**: 1.0  
**المطور**: PlayGood System
