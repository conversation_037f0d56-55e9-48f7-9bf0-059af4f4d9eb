<?php
/**
 * Content Security Policy and XSS Protection
 * يطبق سياسات أمان المحتوى وحماية من XSS
 */

class ContentSecurityPolicy {
    
    private $nonce;
    private $csp_directives = [];
    
    public function __construct() {
        $this->nonce = base64_encode(random_bytes(16));
        $this->initializeCSP();
    }
    
    /**
     * تهيئة سياسات الأمان الافتراضية
     */
    private function initializeCSP() {
        $this->csp_directives = [
            'default-src' => ["'self'"],
            'script-src' => [
                "'self'",
                "'nonce-{$this->nonce}'",
                'https://cdn.jsdelivr.net',
                'https://cdnjs.cloudflare.com',
                'https://code.jquery.com'
            ],
            'style-src' => [
                "'self'",
                "'unsafe-inline'", // مطلوب للـ Bootstrap وCSS المخصص
                'https://cdn.jsdelivr.net',
                'https://cdnjs.cloudflare.com',
                'https://fonts.googleapis.com'
            ],
            'font-src' => [
                "'self'",
                'https://fonts.gstatic.com',
                'https://cdn.jsdelivr.net',
                'https://cdnjs.cloudflare.com'
            ],
            'img-src' => [
                "'self'",
                'data:',
                'https:',
                'blob:'
            ],
            'connect-src' => [
                "'self'",
                'https:'
            ],
            'media-src' => [
                "'self'"
            ],
            'object-src' => [
                "'none'"
            ],
            'frame-src' => [
                "'none'"
            ],
            'frame-ancestors' => [
                "'none'"
            ],
            'base-uri' => [
                "'self'"
            ],
            'form-action' => [
                "'self'"
            ],
            'upgrade-insecure-requests' => []
        ];
    }
    
    /**
     * إضافة مصدر لتوجيه معين
     */
    public function addSource($directive, $source) {
        if (!isset($this->csp_directives[$directive])) {
            $this->csp_directives[$directive] = [];
        }
        
        if (!in_array($source, $this->csp_directives[$directive])) {
            $this->csp_directives[$directive][] = $source;
        }
    }
    
    /**
     * إنشاء CSP header
     */
    public function generateCSPHeader() {
        $csp_parts = [];
        
        foreach ($this->csp_directives as $directive => $sources) {
            if (empty($sources)) {
                $csp_parts[] = $directive;
            } else {
                $csp_parts[] = $directive . ' ' . implode(' ', $sources);
            }
        }
        
        return implode('; ', $csp_parts);
    }
    
    /**
     * تطبيق headers الأمان
     */
    public function applySecurityHeaders() {
        // Content Security Policy
        header('Content-Security-Policy: ' . $this->generateCSPHeader());
        
        // Additional security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
        
        // HTTPS enforcement (uncomment when using SSL)
        // header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
    }
    
    /**
     * الحصول على nonce للـ scripts
     */
    public function getNonce() {
        return $this->nonce;
    }
    
    /**
     * إنشاء script tag مع nonce
     */
    public function scriptTag($content = '', $src = '') {
        $nonce_attr = 'nonce="' . $this->nonce . '"';
        
        if ($src) {
            return "<script src=\"{$src}\" {$nonce_attr}></script>";
        } else {
            return "<script {$nonce_attr}>{$content}</script>";
        }
    }
    
    /**
     * تنظيف المحتوى من XSS
     */
    public function sanitizeOutput($content) {
        // إزالة الـ scripts الضارة
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
        
        // إزالة الـ event handlers
        $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // إزالة javascript: URLs
        $content = preg_replace('/javascript\s*:/i', '', $content);
        
        // إزالة vbscript: URLs
        $content = preg_replace('/vbscript\s*:/i', '', $content);
        
        // إزالة data: URLs للـ scripts
        $content = preg_replace('/data\s*:\s*text\/javascript/i', '', $content);
        
        return $content;
    }
    
    /**
     * فحص المحتوى للـ XSS
     */
    public function detectXSS($input) {
        $xss_patterns = [
            // Script tags
            '/<script[^>]*>.*?<\/script>/is',
            '/<script[^>]*>/i',
            
            // Event handlers
            '/\s*on\w+\s*=\s*["\'][^"\']*["\']/i',
            
            // JavaScript URLs
            '/javascript\s*:/i',
            '/vbscript\s*:/i',
            
            // Data URLs
            '/data\s*:\s*text\/javascript/i',
            '/data\s*:\s*application\/javascript/i',
            
            // HTML entities
            '/&#x?[0-9a-f]+;?/i',
            
            // Expression() CSS
            '/expression\s*\(/i',
            
            // Import statements
            '/@import/i',
            
            // Meta refresh
            '/<meta[^>]*http-equiv[^>]*refresh/i',
            
            // Object/embed tags
            '/<(object|embed|applet|iframe)[^>]*>/i',
            
            // Form tags with suspicious action
            '/<form[^>]*action\s*=\s*["\']javascript:/i'
        ];
        
        $detected_patterns = [];
        
        foreach ($xss_patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $detected_patterns[] = $pattern;
            }
        }
        
        return $detected_patterns;
    }
    
    /**
     * تنظيف البيانات المدخلة
     */
    public function sanitizeInput($input, $allow_html = false) {
        if (is_array($input)) {
            return array_map(function($item) use ($allow_html) {
                return $this->sanitizeInput($item, $allow_html);
            }, $input);
        }
        
        if (!is_string($input)) {
            return $input;
        }
        
        // فحص XSS
        $xss_detected = $this->detectXSS($input);
        if (!empty($xss_detected)) {
            // تسجيل محاولة XSS
            error_log("XSS attempt detected: " . json_encode([
                'input' => $input,
                'patterns' => $xss_detected,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]));
            
            // إرجاع نص آمن
            return '[محتوى محظور لأسباب أمنية]';
        }
        
        if ($allow_html) {
            // السماح بـ HTML محدود وآمن
            $allowed_tags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
            return strip_tags($input, $allowed_tags);
        } else {
            // تحويل جميع الأحرف الخاصة
            return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }
    }
    
    /**
     * إنشاء output buffer آمن
     */
    public function startSecureOutput() {
        ob_start([$this, 'sanitizeOutput']);
    }
    
    /**
     * إنهاء output buffer الآمن
     */
    public function endSecureOutput() {
        ob_end_flush();
    }
}

/**
 * دالة مساعدة لتنظيف النصوص
 */
function safe_echo($text, $allow_html = false) {
    global $csp;
    if (isset($csp)) {
        echo $csp->sanitizeInput($text, $allow_html);
    } else {
        echo htmlspecialchars($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}

/**
 * دالة مساعدة لإنشاء script tag آمن
 */
function safe_script($content = '', $src = '') {
    global $csp;
    if (isset($csp)) {
        return $csp->scriptTag($content, $src);
    } else {
        if ($src) {
            return "<script src=\"{$src}\"></script>";
        } else {
            return "<script>{$content}</script>";
        }
    }
}

// تهيئة CSP
$csp = new ContentSecurityPolicy();

// تطبيق headers الأمان
$csp->applySecurityHeaders();

// بدء output buffer آمن
$csp->startSecureOutput();

// تنظيف جميع المدخلات تلقائياً
if (!empty($_POST)) {
    $_POST = $csp->sanitizeInput($_POST);
}

if (!empty($_GET)) {
    $_GET = $csp->sanitizeInput($_GET);
}
?>
