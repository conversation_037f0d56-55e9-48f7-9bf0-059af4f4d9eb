<?php
// إعدادات النظام التجريبي
define('TRIAL_DURATION_HOURS', 3); // مدة التجربة بالساعات
define('TRIAL_CLEANUP_INTERVAL', 300); // فترة تنظيف البيانات بالثواني (5 دقائق)

// إعدادات قاعدة البيانات التجريبية
require_once __DIR__ . '/database.php';

// دالة إنشاء جداول النظام التجريبي
function createTrialTables() {
    global $pdo;
    
    try {
        // جدول العملاء التجريبيين
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS trial_clients (
                trial_id INT AUTO_INCREMENT PRIMARY KEY,
                business_name VARCHAR(200) NOT NULL,
                owner_name VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                phone VARCHAR(20) NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                trial_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                trial_end DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        // جدول الأجهزة التجريبية
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS trial_devices (
                device_id INT AUTO_INCREMENT PRIMARY KEY,
                trial_id INT NOT NULL,
                device_name VARCHAR(100) NOT NULL,
                device_type ENUM('PS4','PS5','Xbox','PC') NOT NULL,
                hourly_rate DECIMAL(8,2) NOT NULL DEFAULT 15.00,
                status ENUM('available','occupied','maintenance') DEFAULT 'available',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        // جدول العملاء التجريبيين
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS trial_customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                trial_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(100) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        // جدول الجلسات التجريبية
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS trial_sessions (
                session_id INT AUTO_INCREMENT PRIMARY KEY,
                trial_id INT NOT NULL,
                device_id INT NOT NULL,
                customer_id INT NOT NULL,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time DATETIME NULL,
                duration_minutes INT DEFAULT 0,
                total_cost DECIMAL(10,2) DEFAULT 0.00,
                status ENUM('active','completed','cancelled') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE,
                FOREIGN KEY (device_id) REFERENCES trial_devices(device_id) ON DELETE CASCADE,
                FOREIGN KEY (customer_id) REFERENCES trial_customers(customer_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        // جدول منتجات الكافتيريا التجريبية
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS trial_cafeteria_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                trial_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        return true;
    } catch (PDOException $e) {
        error_log('خطأ في إنشاء جداول النظام التجريبي: ' . $e->getMessage());
        return false;
    }
}

// دالة إضافة بيانات تجريبية أساسية
function addTrialSampleData($trial_id) {
    global $pdo;
    
    try {
        // إضافة أجهزة تجريبية
        $devices = [
            ['جهاز PS5 - تجريبي', 'PS5', 20.00],
            ['جهاز PS4 - تجريبي', 'PS4', 15.00],
            ['جهاز Xbox - تجريبي', 'Xbox', 18.00],
            ['جهاز PC - تجريبي', 'PC', 25.00]
        ];

        foreach ($devices as $device) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_devices (trial_id, device_name, device_type, hourly_rate) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $device[0], $device[1], $device[2]]);
        }

        // إضافة عملاء تجريبيين
        $customers = [
            ['أحمد محمد', '01234567890', '<EMAIL>'],
            ['فاطمة علي', '01234567891', '<EMAIL>'],
            ['محمد حسن', '01234567892', '<EMAIL>'],
            ['سارة أحمد', '01234567893', '<EMAIL>']
        ];

        foreach ($customers as $customer) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_customers (trial_id, name, phone, email) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $customer[0], $customer[1], $customer[2]]);
        }

        // إضافة منتجات كافتيريا تجريبية
        $cafeteria_items = [
            ['شاي', 5.00, 'مشروبات ساخنة', 'شاي سادة'],
            ['قهوة', 10.00, 'مشروبات ساخنة', 'قهوة تركي'],
            ['بيبسي', 12.00, 'مشروبات باردة', 'مشروب غازي'],
            ['ساندويتش', 25.00, 'مأكولات', 'ساندويتش جبن']
        ];

        foreach ($cafeteria_items as $item) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_cafeteria_items (trial_id, name, price, category, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $item[0], $item[1], $item[2], $item[3]]);
        }

        return true;
    } catch (PDOException $e) {
        error_log('خطأ في إضافة البيانات التجريبية: ' . $e->getMessage());
        return false;
    }
}

// دالة حساب الوقت المتبقي للتجربة
function getTrialTimeRemaining($trial_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT trial_end FROM trial_clients WHERE trial_id = ? AND is_active = 1");
        $stmt->execute([$trial_id]);
        $result = $stmt->fetch();
        
        if ($result) {
            $trial_end = new DateTime($result['trial_end']);
            $now = new DateTime();
            
            if ($now < $trial_end) {
                $diff = $now->diff($trial_end);
                return [
                    'expired' => false,
                    'hours' => $diff->h + ($diff->days * 24),
                    'minutes' => $diff->i,
                    'seconds' => $diff->s,
                    'total_seconds' => ($trial_end->getTimestamp() - $now->getTimestamp())
                ];
            }
        }
        
        return ['expired' => true, 'hours' => 0, 'minutes' => 0, 'seconds' => 0, 'total_seconds' => 0];
    } catch (Exception $e) {
        error_log('خطأ في حساب الوقت المتبقي: ' . $e->getMessage());
        return ['expired' => true, 'hours' => 0, 'minutes' => 0, 'seconds' => 0, 'total_seconds' => 0];
    }
}

// دالة تنظيف البيانات المنتهية الصلاحية
function cleanupExpiredTrials() {
    global $pdo;
    
    try {
        // حذف الحسابات التجريبية المنتهية الصلاحية
        $stmt = $pdo->prepare("DELETE FROM trial_clients WHERE trial_end < NOW()");
        $deleted = $stmt->execute();
        
        if ($deleted) {
            $count = $stmt->rowCount();
            error_log("تم حذف {$count} حساب تجريبي منتهي الصلاحية");
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('خطأ في تنظيف البيانات التجريبية: ' . $e->getMessage());
        return false;
    }
}

// إنشاء الجداول عند تحميل الملف
createTrialTables();
?>
