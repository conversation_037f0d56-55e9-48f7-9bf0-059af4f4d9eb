-- إنشاء نظام إدارة المخزن - PlayGood
-- تشغيل هذا الملف لإضافة نظام إدارة المخزن المتقدم

-- 1. تحديث جدول cafeteria_items لإضافة حقول المخزن
ALTER TABLE cafeteria_items 
ADD COLUMN IF NOT EXISTS stock_quantity INT DEFAULT 0 COMMENT 'الكمية المتوفرة',
ADD COLUMN IF NOT EXISTS cost_price DECIMAL(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
ADD COLUMN IF NOT EXISTS min_stock_level INT DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
ADD COLUMN IF NOT EXISTS max_stock_level INT DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
ADD COLUMN IF NOT EXISTS status ENUM('available', 'low_stock', 'out_of_stock', 'discontinued') DEFAULT 'available' COMMENT 'حالة المنتج',
ADD COLUMN IF NOT EXISTS barcode VARCHAR(100) NULL COMMENT 'الباركود',
ADD COLUMN IF NOT EXISTS supplier VARCHAR(200) NULL COMMENT 'المورد',
ADD COLUMN IF NOT EXISTS last_restock_date TIMESTAMP NULL COMMENT 'تاريخ آخر تجديد للمخزن',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث';

-- 2. إنشاء جدول ربط المنتجات بالأجهزة والذراعات
CREATE TABLE IF NOT EXISTS product_device_associations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    device_id INT NOT NULL,
    device_part ENUM('controller', 'headset', 'cable', 'accessory', 'other') DEFAULT 'other' COMMENT 'نوع الجزء',
    quantity_needed INT DEFAULT 1 COMMENT 'الكمية المطلوبة لكل جهاز',
    notes TEXT NULL COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE,
    FOREIGN KEY (device_id) REFERENCES devices(device_id) ON DELETE CASCADE,
    UNIQUE KEY unique_product_device (product_id, device_id, device_part)
);

-- 3. إنشاء جدول تاريخ حركة المخزن
CREATE TABLE IF NOT EXISTS inventory_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'adjustment', 'expired', 'damaged') NOT NULL COMMENT 'نوع الحركة',
    quantity INT NOT NULL COMMENT 'الكمية',
    previous_quantity INT NOT NULL COMMENT 'الكمية السابقة',
    new_quantity INT NOT NULL COMMENT 'الكمية الجديدة',
    unit_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT 'تكلفة الوحدة',
    total_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT 'التكلفة الإجمالية',
    reference_type ENUM('purchase', 'sale', 'session', 'order', 'manual', 'system') DEFAULT 'manual' COMMENT 'نوع المرجع',
    reference_id INT NULL COMMENT 'معرف المرجع',
    notes TEXT NULL COMMENT 'ملاحظات',
    client_id INT NOT NULL,
    created_by INT NULL COMMENT 'المستخدم الذي أجرى العملية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE,
    INDEX idx_inventory_movements_product (product_id),
    INDEX idx_inventory_movements_client (client_id),
    INDEX idx_inventory_movements_date (created_at)
);

-- 4. إنشاء جدول تصنيفات المنتجات المحسن
CREATE TABLE IF NOT EXISTS product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    parent_id INT NULL COMMENT 'التصنيف الأب للتصنيفات الفرعية',
    icon VARCHAR(50) DEFAULT 'fas fa-box' COMMENT 'أيقونة التصنيف',
    color VARCHAR(7) DEFAULT '#007bff' COMMENT 'لون التصنيف',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    is_active BOOLEAN DEFAULT TRUE,
    client_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    UNIQUE KEY unique_category_per_client (name, client_id),
    INDEX idx_categories_client (client_id)
);

-- 5. إضافة فهارس لتحسين الأداء
ALTER TABLE cafeteria_items 
ADD INDEX IF NOT EXISTS idx_stock_status (status, stock_quantity),
ADD INDEX IF NOT EXISTS idx_low_stock (stock_quantity, min_stock_level),
ADD INDEX IF NOT EXISTS idx_barcode (barcode),
ADD INDEX IF NOT EXISTS idx_supplier (supplier);

-- 6. إدراج تصنيفات افتراضية
INSERT IGNORE INTO product_categories (name, description, icon, color, client_id) VALUES
('مشروبات ساخنة', 'الشاي والقهوة والمشروبات الساخنة', 'fas fa-coffee', '#8B4513', 1),
('مشروبات باردة', 'العصائر والمياه والمشروبات الباردة', 'fas fa-glass-whiskey', '#4169E1', 1),
('وجبات خفيفة', 'الساندويتشات والوجبات السريعة', 'fas fa-hamburger', '#FF6347', 1),
('حلويات', 'الكيك والحلويات والآيس كريم', 'fas fa-birthday-cake', '#FF69B4', 1),
('أكسسوارات الألعاب', 'يدات التحكم والسماعات والكابلات', 'fas fa-gamepad', '#32CD32', 1),
('قطع غيار', 'قطع غيار الأجهزة والصيانة', 'fas fa-tools', '#FF8C00', 1);

-- 7. إنشاء view لعرض بيانات المخزن المفصلة
CREATE OR REPLACE VIEW inventory_overview AS
SELECT 
    ci.id,
    ci.name,
    ci.price as selling_price,
    ci.cost_price,
    ci.stock_quantity,
    ci.min_stock_level,
    ci.max_stock_level,
    ci.status,
    ci.barcode,
    ci.supplier,
    ci.category,
    ci.description,
    ci.last_restock_date,
    ci.created_at,
    ci.updated_at,
    ci.client_id,
    pc.name as category_name,
    pc.icon as category_icon,
    pc.color as category_color,
    CASE 
        WHEN ci.stock_quantity <= 0 THEN 'نفذ'
        WHEN ci.stock_quantity <= ci.min_stock_level THEN 'ناقص'
        WHEN ci.stock_quantity >= ci.max_stock_level THEN 'مكتمل'
        ELSE 'متوفر'
    END as stock_status_text,
    CASE 
        WHEN ci.stock_quantity <= 0 THEN 'danger'
        WHEN ci.stock_quantity <= ci.min_stock_level THEN 'warning'
        WHEN ci.stock_quantity >= ci.max_stock_level THEN 'info'
        ELSE 'success'
    END as stock_status_class,
    (ci.price - ci.cost_price) as profit_margin,
    ROUND(((ci.price - ci.cost_price) / ci.price) * 100, 2) as profit_percentage
FROM cafeteria_items ci
LEFT JOIN product_categories pc ON ci.category = pc.name AND ci.client_id = pc.client_id;

-- 8. إنشاء trigger لتحديث حالة المنتج تلقائياً
DELIMITER //
CREATE TRIGGER IF NOT EXISTS update_product_status 
BEFORE UPDATE ON cafeteria_items
FOR EACH ROW
BEGIN
    IF NEW.stock_quantity <= 0 THEN
        SET NEW.status = 'out_of_stock';
    ELSEIF NEW.stock_quantity <= NEW.min_stock_level THEN
        SET NEW.status = 'low_stock';
    ELSE
        SET NEW.status = 'available';
    END IF;
END//
DELIMITER ;

-- 9. إنشاء trigger لتسجيل حركة المخزن
DELIMITER //
CREATE TRIGGER IF NOT EXISTS log_inventory_movement 
AFTER UPDATE ON cafeteria_items
FOR EACH ROW
BEGIN
    IF OLD.stock_quantity != NEW.stock_quantity THEN
        INSERT INTO inventory_movements (
            product_id, 
            movement_type, 
            quantity, 
            previous_quantity, 
            new_quantity, 
            unit_cost, 
            total_cost, 
            reference_type, 
            notes, 
            client_id
        ) VALUES (
            NEW.id,
            CASE 
                WHEN NEW.stock_quantity > OLD.stock_quantity THEN 'in'
                ELSE 'out'
            END,
            ABS(NEW.stock_quantity - OLD.stock_quantity),
            OLD.stock_quantity,
            NEW.stock_quantity,
            NEW.cost_price,
            ABS(NEW.stock_quantity - OLD.stock_quantity) * NEW.cost_price,
            'manual',
            'تحديث تلقائي للمخزن',
            NEW.client_id
        );
    END IF;
END//
DELIMITER ;

-- 10. تحديث البيانات الموجودة
UPDATE cafeteria_items 
SET stock_quantity = 50, 
    cost_price = price * 0.6, 
    min_stock_level = 5, 
    max_stock_level = 100,
    status = 'available'
WHERE stock_quantity IS NULL OR stock_quantity = 0;

-- عرض النتائج
SELECT 'تم إنشاء نظام إدارة المخزن بنجاح' as الحالة;
SELECT COUNT(*) as 'عدد المنتجات المحدثة' FROM cafeteria_items WHERE stock_quantity > 0;
