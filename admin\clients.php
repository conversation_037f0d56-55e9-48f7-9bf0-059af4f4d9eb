<?php
require_once '../config/database.php';
require_once 'includes/auth.php';
require_once '../includes/backup_permissions.php';

// التحقق من تسجيل الدخول وصلاحية الوصول لهذه الصفحة
checkAdminSession();
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * إنهاء جلسات جميع موظفي العميل المعطل
 */
function invalidateClientEmployeeSessions($pdo, $client_id) {
    try {
        // إنشاء ملف لتتبع الجلسات المنتهية
        $invalidated_sessions_file = '../temp/invalidated_sessions.json';

        // إنشاء مجلد temp إذا لم يكن موجوداً
        if (!file_exists('../temp')) {
            mkdir('../temp', 0755, true);
        }

        // جلب معرفات جميع موظفي العميل
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE client_id = ?");
        $stmt->execute([$client_id]);
        $employees = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!empty($employees)) {
            // قراءة الجلسات المنتهية الحالية
            $invalidated_sessions = [];
            if (file_exists($invalidated_sessions_file)) {
                $content = file_get_contents($invalidated_sessions_file);
                $invalidated_sessions = json_decode($content, true) ?: [];
            }

            // إضافة معرفات الموظفين إلى قائمة الجلسات المنتهية
            foreach ($employees as $employee_id) {
                $invalidated_sessions[$employee_id] = [
                    'invalidated_at' => time(),
                    'client_id' => $client_id,
                    'reason' => 'client_deactivated'
                ];
            }

            // حفظ الجلسات المنتهية
            file_put_contents($invalidated_sessions_file, json_encode($invalidated_sessions, JSON_PRETTY_PRINT));
        }

        return true;
    } catch (Exception $e) {
        error_log('خطأ في إنهاء جلسات الموظفين: ' . $e->getMessage());
        return false;
    }
}

/**
 * تنظيف الجلسات المنتهية القديمة (أكثر من 24 ساعة)
 */
function cleanupOldInvalidatedSessions() {
    $invalidated_sessions_file = '../temp/invalidated_sessions.json';

    if (!file_exists($invalidated_sessions_file)) {
        return;
    }

    try {
        $content = file_get_contents($invalidated_sessions_file);
        $invalidated_sessions = json_decode($content, true) ?: [];

        $current_time = time();
        $cleanup_threshold = 24 * 60 * 60; // 24 ساعة

        $cleaned_sessions = [];
        foreach ($invalidated_sessions as $employee_id => $session_data) {
            // الاحتفاظ بالجلسات التي تم إنهاؤها خلال آخر 24 ساعة
            if (($current_time - $session_data['invalidated_at']) < $cleanup_threshold) {
                $cleaned_sessions[$employee_id] = $session_data;
            }
        }

        // حفظ الجلسات المنظفة
        file_put_contents($invalidated_sessions_file, json_encode($cleaned_sessions, JSON_PRETTY_PRINT));

    } catch (Exception $e) {
        error_log('خطأ في تنظيف الجلسات المنتهية: ' . $e->getMessage());
    }
}

$success = '';
$error = '';

// تنظيف الجلسات المنتهية القديمة
cleanupOldInvalidatedSessions();

// معالجة العمليات المختلفة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_client':
            $business_name = sanitize($_POST['business_name']);
            $owner_name = sanitize($_POST['owner_name']);
            $email = sanitize($_POST['email']);
            $phone = sanitize($_POST['phone']);
            $password = $_POST['password'];
            $address = sanitize($_POST['address']);
            $city = sanitize($_POST['city']);
            $subscription_plan = $_POST['subscription_plan'];
            
            if (empty($business_name) || empty($owner_name) || empty($email) || empty($phone) || empty($password)) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } else {
                try {
                    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
                    $stmt = $pdo->prepare("SELECT client_id FROM clients WHERE email = ?");
                    $stmt->execute([$email]);
                    $check = $stmt->fetch();
                    if ($check) {
                        $error = 'البريد الإلكتروني مستخدم مسبقاً';
                    } else {
                        $password_hash = password_hash($password, PASSWORD_DEFAULT);
                        $sql = "INSERT INTO clients (business_name, owner_name, email, phone, password_hash, address, city, subscription_plan, subscription_start)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURDATE())";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([$business_name, $owner_name, $email, $phone, $password_hash, $address, $city, $subscription_plan]);
                        $success = 'تم إضافة العميل بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ أثناء إضافة العميل: ' . $e->getMessage();
                }
            }
            break;
            
        case 'update_client':
            $client_id = intval($_POST['client_id']);
            $business_name = sanitize($_POST['business_name']);
            $owner_name = sanitize($_POST['owner_name']);
            $email = sanitize($_POST['email']);
            $phone = sanitize($_POST['phone']);
            $address = sanitize($_POST['address']);
            $city = sanitize($_POST['city']);
            $subscription_plan = $_POST['subscription_plan'];
            
            if (empty($business_name) || empty($owner_name) || empty($email) || empty($phone)) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } else {
                try {
                    // التحقق من عدم وجود البريد الإلكتروني لعميل آخر
                    $stmt = $pdo->prepare("SELECT client_id FROM clients WHERE email = ? AND client_id != ?");
                    $stmt->execute([$email, $client_id]);
                    $check = $stmt->fetch();
                    if ($check) {
                        $error = 'البريد الإلكتروني مستخدم مسبقاً';
                    } else {
                        $sql = "UPDATE clients SET business_name = ?, owner_name = ?, email = ?, phone = ?, address = ?, city = ?, subscription_plan = ?, updated_at = NOW() WHERE client_id = ?";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([$business_name, $owner_name, $email, $phone, $address, $city, $subscription_plan, $client_id]);
                        $success = 'تم تحديث بيانات العميل بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ أثناء تحديث العميل: ' . $e->getMessage();
                }
            }
            break;
            
        case 'toggle_status':
            $client_id = intval($_POST['client_id']);
            $new_status = intval($_POST['new_status']);

            try {
                // بدء المعاملة
                $pdo->beginTransaction();

                // تحديث حالة العميل
                $stmt = $pdo->prepare("UPDATE clients SET is_active = ?, updated_at = NOW() WHERE client_id = ?");
                $stmt->execute([$new_status, $client_id]);

                // تحديث حالة جميع موظفي العميل
                if ($new_status == 0) {
                    // إلغاء تفعيل جميع الموظفين عند إلغاء تفعيل العميل
                    $stmt = $pdo->prepare("UPDATE employees SET is_active = 0 WHERE client_id = ?");
                    $stmt->execute([$client_id]);

                    // إنهاء جلسات جميع موظفي العميل المعطل
                    invalidateClientEmployeeSessions($pdo, $client_id);
                } else {
                    // إعادة تفعيل جميع الموظفين عند إعادة تفعيل العميل
                    $stmt = $pdo->prepare("UPDATE employees SET is_active = 1 WHERE client_id = ?");
                    $stmt->execute([$client_id]);
                }

                // تأكيد المعاملة
                $pdo->commit();

                $status_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
                $employee_action = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
                $logout_message = $new_status ? '' : ' وتم إنهاء جلسات جميع الموظفين';
                $success = "تم {$status_text} العميل و{$employee_action} جميع موظفيه بنجاح{$logout_message}";
            } catch (Exception $e) {
                // التراجع عن المعاملة في حالة الخطأ
                $pdo->rollBack();
                $error = 'حدث خطأ أثناء تغيير حالة العميل: ' . $e->getMessage();
            }
            break;

        case 'toggle_backup':
            $client_id = intval($_POST['client_id']);
            $new_backup_status = intval($_POST['new_backup_status']);

            try {
                // التأكد من وجود حقل backup_enabled
                ensureBackupColumnExists();

                if (setClientBackupPermission($client_id, $new_backup_status)) {
                    $status_text = $new_backup_status ? 'تفعيل' : 'تعطيل';
                    $success = "تم {$status_text} صلاحية النسخ الاحتياطي للعميل بنجاح";
                } else {
                    $error = 'فشل في تحديث صلاحية النسخ الاحتياطي';
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء تحديث صلاحية النسخ الاحتياطي: ' . $e->getMessage();
            }
            break;
    }
}

// البحث والفلترة
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$plan_filter = $_GET['plan'] ?? '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(business_name LIKE ? OR owner_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = intval($status_filter);
}

if (!empty($plan_filter)) {
    $where_conditions[] = "subscription_plan = ?";
    $params[] = $plan_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// التأكد من وجود حقل backup_enabled
ensureBackupColumnExists();

// جلب العملاء مع حالة النسخ الاحتياطي
$sql = "SELECT *,
        COALESCE(backup_enabled, 1) as backup_enabled
        FROM clients {$where_clause} ORDER BY created_at DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$clients = $stmt->fetchAll();

// إحصائيات سريعة
// المجموع
$stmt = $pdo->query("SELECT COUNT(*) as count FROM clients");
$total = $stmt->fetch()['count'];

// النشطين
$stmt = $pdo->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 1");
$active = $stmt->fetch()['count'];

// غير النشطين
$stmt = $pdo->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 0");
$inactive = $stmt->fetch()['count'];

$stats = [
    'total' => $total,
    'active' => $active,
    'inactive' => $inactive
];

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        /* تحسينات للتصميم مع القائمة العائمة */
        .container-fluid {
            padding-right: 1rem;
            padding-left: 1rem;
        }

        /* تأثيرات إضافية للكروت */
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        /* تحسين عرض الجدول */
        .table-responsive {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* تحسين الأزرار */
        .btn-group .btn {
            margin: 0 2px;
        }

        /* تحسين النماذج */
        .modal-body .row {
            margin: 0;
        }

        .modal-body .col-md-6,
        .modal-body .col-md-12 {
            padding: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- المحتوى الرئيسي -->
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        إدارة العملاء
                    </h1>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3><?php echo $stats['total']; ?></h3>
                                <p>إجمالي العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <h3><?php echo $stats['active']; ?></h3>
                                <p>العملاء النشطين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-times fa-2x mb-2"></i>
                                <h3><?php echo $stats['inactive']; ?></h3>
                                <p>العملاء غير النشطين</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- مربع البحث والفلاتر -->
                <div class="search-box">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" placeholder="اسم المحل، المالك، الإيميل أو الهاتف" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">خطة الاشتراك</label>
                            <select class="form-select" name="plan">
                                <option value="">جميع الخطط</option>
                                <option value="basic" <?php echo $plan_filter === 'basic' ? 'selected' : ''; ?>>أساسية</option>
                                <option value="premium" <?php echo $plan_filter === 'premium' ? 'selected' : ''; ?>>مميزة</option>
                                <option value="enterprise" <?php echo $plan_filter === 'enterprise' ? 'selected' : ''; ?>>مؤسسية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- إدارة العملاء -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            إدارة العملاء
                        </h5>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addClientModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة عميل جديد
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم العميل</th>
                                        <th>اسم المحل</th>
                                        <th>اسم المالك</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>خطة الاشتراك</th>
                                        <th>الحالة</th>
                                        <th>النسخ الاحتياطي</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($clients)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-5">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">لا توجد عملاء مطابقة للبحث</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($clients as $client): ?>
                                            <tr>
                                                <td><?php echo $client['client_id']; ?></td>
                                                <td><?php echo htmlspecialchars($client['business_name']); ?></td>
                                                <td><?php echo htmlspecialchars($client['owner_name']); ?></td>
                                                <td><?php echo htmlspecialchars($client['email']); ?></td>
                                                <td><?php echo htmlspecialchars($client['phone']); ?></td>
                                                <td>
                                                    <?php
                                                    $plan_badges = [
                                                        'basic' => 'bg-secondary',
                                                        'premium' => 'bg-warning',
                                                        'enterprise' => 'bg-success'
                                                    ];
                                                    $plan_names = [
                                                        'basic' => 'أساسية',
                                                        'premium' => 'مميزة',
                                                        'enterprise' => 'مؤسسية'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $plan_badges[$client['subscription_plan']]; ?>">
                                                        <?php echo $plan_names[$client['subscription_plan']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $client['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $client['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge <?php echo $client['backup_enabled'] ? 'bg-primary' : 'bg-secondary'; ?> me-2">
                                                            <i class="fas <?php echo $client['backup_enabled'] ? 'fa-check' : 'fa-times'; ?> me-1"></i>
                                                            <?php echo $client['backup_enabled'] ? 'مفعل' : 'معطل'; ?>
                                                        </span>
                                                        <form method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تغيير صلاحية النسخ الاحتياطي؟')">
                                                            <input type="hidden" name="action" value="toggle_backup">
                                                            <input type="hidden" name="client_id" value="<?php echo $client['client_id']; ?>">
                                                            <input type="hidden" name="new_backup_status" value="<?php echo $client['backup_enabled'] ? 0 : 1; ?>">
                                                            <button type="submit" class="btn btn-sm <?php echo $client['backup_enabled'] ? 'btn-outline-warning' : 'btn-outline-primary'; ?>"
                                                                    title="<?php echo $client['backup_enabled'] ? 'تعطيل النسخ الاحتياطي' : 'تفعيل النسخ الاحتياطي'; ?>">
                                                                <i class="fas <?php echo $client['backup_enabled'] ? 'fa-ban' : 'fa-database'; ?>"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($client['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <!-- إدارة الصلاحيات -->
                                                        <a href="client_permissions.php?client_id=<?php echo $client['client_id']; ?>"
                                                           class="btn btn-sm btn-outline-warning"
                                                           title="إدارة صلاحيات العميل">
                                                            <i class="fas fa-user-shield"></i>
                                                        </a>

                                                        <!-- إدارة الأجهزة -->
                                                        <a href="client_devices.php?client_id=<?php echo $client['client_id']; ?>"
                                                           class="btn btn-sm btn-outline-info"
                                                           title="إدارة أجهزة العميل">
                                                            <i class="fas fa-desktop"></i>
                                                        </a>

                                                        <!-- تعديل العميل -->
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="editClient(<?php echo htmlspecialchars(json_encode($client)); ?>)"
                                                                data-bs-toggle="modal" data-bs-target="#editClientModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>

                                                        <!-- تغيير الحالة -->
                                                        <form method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تغيير حالة العميل؟')">
                                                            <input type="hidden" name="action" value="toggle_status">
                                                            <input type="hidden" name="client_id" value="<?php echo $client['client_id']; ?>">
                                                            <input type="hidden" name="new_status" value="<?php echo $client['is_active'] ? 0 : 1; ?>">
                                                            <button type="submit" class="btn btn-sm <?php echo $client['is_active'] ? 'btn-outline-warning' : 'btn-outline-success'; ?>">
                                                                <i class="fas <?php echo $client['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة عميل جديد -->
    <div class="modal fade" id="addClientModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عميل جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_client">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المحل *</label>
                                <input type="text" class="form-control" name="business_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">اسم المالك *</label>
                                <input type="text" class="form-control" name="owner_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">خطة الاشتراك</label>
                                <select class="form-select" name="subscription_plan">
                                    <option value="basic">أساسية</option>
                                    <option value="premium">مميزة</option>
                                    <option value="enterprise">مؤسسية</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" name="address" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إضافة العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تعديل العميل -->
    <div class="modal fade" id="editClientModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات العميل
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_client">
                        <input type="hidden" name="client_id" id="edit_client_id">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المحل *</label>
                                <input type="text" class="form-control" name="business_name" id="edit_business_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">اسم المالك *</label>
                                <input type="text" class="form-control" name="owner_name" id="edit_owner_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" name="email" id="edit_email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" id="edit_phone" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">خطة الاشتراك</label>
                                <select class="form-select" name="subscription_plan" id="edit_subscription_plan">
                                    <option value="basic">أساسية</option>
                                    <option value="premium">مميزة</option>
                                    <option value="enterprise">مؤسسية</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city" id="edit_city">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" name="address" id="edit_address" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editClient(client) {
            document.getElementById('edit_client_id').value = client.client_id;
            document.getElementById('edit_business_name').value = client.business_name;
            document.getElementById('edit_owner_name').value = client.owner_name;
            document.getElementById('edit_email').value = client.email;
            document.getElementById('edit_phone').value = client.phone;
            document.getElementById('edit_subscription_plan').value = client.subscription_plan;
            document.getElementById('edit_city').value = client.city || '';
            document.getElementById('edit_address').value = client.address || '';
        }

        // إخفاء الرسائل تلقائياً بعد 5 ثوانٍ
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>