# نظام صلاحيات الإدمن - PlayGood

## نظرة عامة

تم إنشاء نظام صلاحيات شامل ومتقدم لإدارة الوصول لصفحات لوحة تحكم الإدمن. يوفر هذا النظام تحكماً دقيقاً في من يمكنه الوصول لأي صفحة في لوحة التحكم.

## ✨ المميزات الرئيسية

### 🔐 نظام صلاحيات متدرج
- **Super Admin**: وصول كامل لجميع الصفحات والميزات
- **Admin**: وصول للصفحات الأساسية مع إمكانية التخصيص
- **صلاحيات مخصصة**: تحديد صلاحيات فردية لكل مدير

### 📄 إدارة الصفحات
- تصنيف الصفحات حسب الفئات (العملاء، التقارير، الإعدادات، إلخ)
- تحديد متطلبات الدور لكل صفحة
- تفعيل/تعطيل الصفحات ديناميكياً

### 🛡️ الحماية التلقائية
- فحص تلقائي للصلاحيات عند الوصول لأي صفحة
- إخفاء الروابط غير المسموحة من القائمة الجانبية
- إعادة توجيه آمنة للصفحات غير المصرح بها

### 📊 واجهة إدارية متقدمة
- صفحة مخصصة لإدارة صلاحيات المديرين
- عرض مرئي للصلاحيات مع إمكانية التبديل السريع
- تجميع الصلاحيات حسب الفئات

## 🗂️ هيكل النظام

### الجداول الأساسية

#### `admin_pages`
```sql
- page_id: معرف الصفحة
- page_name: اسم الصفحة (فريد)
- page_label: التسمية المعروضة
- page_url: رابط الصفحة
- page_icon: أيقونة الصفحة
- category: فئة الصفحة
- is_active: حالة التفعيل
- is_default: صفحة افتراضية للمديرين الجدد
- required_role: الدور المطلوب (super_admin/admin/any)
```

#### `admin_page_permissions`
```sql
- admin_id: معرف المدير
- page_id: معرف الصفحة
- is_enabled: حالة الصلاحية
- granted_by: من منح الصلاحية
- granted_at: تاريخ منح الصلاحية
```

### الملفات الرئيسية

#### `admin/includes/admin-permissions.php`
- دوال التحقق من الصلاحيات
- إدارة جلسات المديرين
- دوال مساعدة للصلاحيات

#### `admin/includes/auth.php`
- نظام المصادقة المحدث
- فحص الصلاحيات التلقائي
- دوال التحقق من الأدوار

#### `admin/admin_permissions.php`
- واجهة إدارة صلاحيات المديرين
- تبديل الصلاحيات بـ AJAX
- إعادة تعيين الصلاحيات

#### `admin/includes/sidebar.php`
- القائمة الجانبية المحدثة
- إخفاء الروابط غير المسموحة
- عرض ديناميكي للصفحات

## 🚀 التشغيل والإعداد

### 1. تشغيل النظام
```bash
# انتقل إلى مجلد المشروع وشغل:
php setup_admin_permissions.php
```

أو افتح في المتصفح:
```
http://localhost/playgood/setup_admin_permissions.php
```

### 2. التحقق من التشغيل
- تأكد من إنشاء الجداول بنجاح
- تحقق من وجود البيانات الأساسية
- اختبر الوصول للصفحات

### 3. الإعداد الأولي
1. سجل دخول كـ Super Admin
2. انتقل إلى "صلاحيات المديرين"
3. اختر مديراً لتخصيص صلاحياته
4. فعّل/عطّل الصفحات حسب الحاجة

## 📋 الصفحات المتاحة

### الصفحات الأساسية
- **dashboard**: لوحة التحكم الرئيسية
- **profile**: الملف الشخصي

### إدارة العملاء
- **clients**: إدارة العملاء
- **client_permissions**: صلاحيات العملاء
- **client_devices**: أجهزة العملاء

### التقارير
- **reports**: التقارير والإحصائيات

### الإعدادات والإدارة
- **settings**: إعدادات النظام
- **admin_permissions**: صلاحيات المديرين (Super Admin فقط)
- **admins**: إدارة المديرين (Super Admin فقط)
- **system_logs**: سجلات النظام (Super Admin فقط)

### النظام
- **backup**: النسخ الاحتياطية

## 🔧 الاستخدام

### التحقق من الصلاحيات في الكود

```php
// التحقق من صلاحية صفحة معينة
if (hasAdminPagePermission('clients')) {
    // المدير لديه صلاحية الوصول لصفحة العملاء
}

// التحقق من دور المدير
if (hasAdminRole('super_admin')) {
    // المدير هو Super Admin
}

// الحصول على الصفحات المسموحة
$allowed_pages = getAllowedAdminPages();
```

### حماية الصفحات

```php
// في بداية أي صفحة إدمن
require_once 'includes/auth.php';
checkAdminSession(); // يتضمن فحص الصلاحيات تلقائياً

// أو للتحقق من دور معين
requireAdminRole('super_admin');

// أو للتحقق من صفحة معينة
requirePagePermission('clients');
```

### إضافة صفحة جديدة

```sql
INSERT INTO admin_pages (page_name, page_label, page_url, page_icon, category, is_default, required_role)
VALUES ('new_page', 'صفحة جديدة', 'new_page.php', 'fas fa-star', 'custom', TRUE, 'any');
```

## 🛠️ التخصيص

### إضافة فئة جديدة
1. أضف الصفحات الجديدة لجدول `admin_pages`
2. حدد `category` جديدة
3. أضف الفئة لمصفوفة `$category_names` في `admin_permissions.php`

### تخصيص الصلاحيات الافتراضية
عدّل في `create_admin_permissions_system.sql`:
```sql
-- غيّر is_default إلى TRUE للصفحات المطلوبة افتراضياً
UPDATE admin_pages SET is_default = TRUE WHERE page_name IN ('dashboard', 'clients');
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### 1. عدم ظهور الصفحات في القائمة
- تحقق من `is_active = TRUE` في جدول `admin_pages`
- تأكد من وجود صلاحية في `admin_page_permissions`

#### 2. رسالة "ليس لديك صلاحية"
- تحقق من `required_role` للصفحة
- تأكد من دور المدير في جدول `admins`

#### 3. خطأ في تحميل الصفحة
- تأكد من تضمين `admin-permissions.php`
- تحقق من وجود الجداول في قاعدة البيانات

### فحص الصلاحيات يدوياً

```sql
-- عرض صلاحيات مدير معين
SELECT * FROM admin_page_permissions_detailed 
WHERE admin_id = 1;

-- عرض الصفحات المتاحة لدور معين
SELECT * FROM admin_pages 
WHERE required_role IN ('any', 'admin') 
AND is_active = TRUE;
```

## 📈 التطوير المستقبلي

### ميزات مقترحة
- [ ] سجل أنشطة المديرين
- [ ] صلاحيات مؤقتة بتاريخ انتهاء
- [ ] مجموعات صلاحيات جاهزة
- [ ] تصدير/استيراد إعدادات الصلاحيات
- [ ] إشعارات تغيير الصلاحيات

### تحسينات الأداء
- [ ] تخزين مؤقت للصلاحيات
- [ ] فهرسة محسنة للجداول
- [ ] تحسين استعلامات قاعدة البيانات

## 📞 الدعم

في حالة وجود مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء
3. اختبر النظام خطوة بخطوة
4. تأكد من صحة إعدادات قاعدة البيانات

---

**تم إنشاء هذا النظام بواسطة Augment Agent لمشروع PlayGood**
