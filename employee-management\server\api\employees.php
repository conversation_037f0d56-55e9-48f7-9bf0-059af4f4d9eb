<?php
// filepath: employee-management/server/api/employees.php

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Code to retrieve employee records
        $employees = getAllEmployees(); // Assume this function is defined in functions.php
        echo json_encode($employees);
        break;

    case 'POST':
        // Code to add a new employee
        $data = json_decode(file_get_contents('php://input'), true);
        $result = addEmployee($data); // Assume this function is defined in functions.php
        echo json_encode($result);
        break;

    case 'PUT':
        // Code to update an existing employee
        $data = json_decode(file_get_contents('php://input'), true);
        $result = updateEmployee($data); // Assume this function is defined in functions.php
        echo json_encode($result);
        break;

    case 'DELETE':
        // Code to delete an employee
        $data = json_decode(file_get_contents('php://input'), true);
        $result = deleteEmployee($data['id']); // Assume this function is defined in functions.php
        echo json_encode($result);
        break;

    default:
        http_response_code(405);
        echo json_encode(['message' => 'Method Not Allowed']);
        break;
}