/* ملف CSS لضبط المسافات والتمرير في صفحات المصادقة - PlayGood */
/* تحسينات المسافات والأحجام والتمرير */

/* تحسينات عامة للمسافات */
.auth-page {
    padding: 0;
    margin: 0;
}

.auth-container {
    padding: 1rem;
    max-width: 100%;
}

/* تحسين مسافات البطاقة */
.auth-card-enhanced {
    margin: 0 auto;
    max-width: 100%;
    width: 100%;
}

.auth-card-header {
    padding: 2rem 1.5rem;
}

.auth-card-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
}

/* تحسين التمرير */
.auth-card-body {
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.2) transparent;
}

.auth-card-body::-webkit-scrollbar {
    width: 4px;
}

.auth-card-body::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
}

.auth-card-body::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.2);
    border-radius: 2px;
    transition: background 0.3s ease;
}

.auth-card-body::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.4);
}

/* تحسين مسافات النماذج */
.auth-form-floating {
    margin-bottom: 1.2rem;
}

.auth-form-floating:last-of-type {
    margin-bottom: 1.5rem;
}

/* تحسين مسافات الصفوف */
.row .auth-form-floating {
    margin-bottom: 1rem;
}

.row:last-child .auth-form-floating {
    margin-bottom: 1.2rem;
}

/* تحسين مسافات الأزرار */
.auth-btn,
.auth-btn-modern {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

/* تحسين مسافات التنبيهات */
.auth-alert {
    margin-bottom: 1rem;
    padding: 0.8rem 1rem;
}

/* تحسين مسافات الروابط */
.auth-link-modern {
    margin: 0.5rem 0;
}

.auth-link-alt {
    margin: 0.3rem;
    padding: 0.8rem 1rem;
}

/* تحسين مسافات العناوين */
.auth-title {
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    margin-bottom: 0;
}

/* تحسين مسافات الأيقونة */
.auth-icon {
    margin-bottom: 1rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .auth-container {
        padding: 0.5rem;
    }
    
    .auth-card-enhanced {
        margin: 0.5rem;
        border-radius: 16px;
    }
    
    .auth-card-header {
        padding: 1.5rem 1rem;
    }
    
    .auth-card-body {
        padding: 1rem;
        max-height: 65vh;
    }
    
    .auth-form-floating {
        margin-bottom: 1rem;
    }
    
    .row .auth-form-floating {
        margin-bottom: 0.8rem;
    }
    
    .auth-btn,
    .auth-btn-modern {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
    }
    
    .auth-alert {
        margin-bottom: 0.8rem;
        padding: 0.7rem 0.8rem;
    }
    
    .auth-icon {
        margin-bottom: 0.8rem;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .auth-container {
        padding: 0.3rem;
    }
    
    .auth-card-enhanced {
        margin: 0.3rem;
        border-radius: 14px;
    }
    
    .auth-card-header {
        padding: 1.2rem 0.8rem;
    }
    
    .auth-card-body {
        padding: 0.8rem;
        max-height: 60vh;
    }
    
    .auth-form-floating {
        margin-bottom: 0.8rem;
    }
    
    .row .auth-form-floating {
        margin-bottom: 0.6rem;
    }
    
    .auth-btn,
    .auth-btn-modern {
        margin-top: 0.6rem;
        margin-bottom: 0.6rem;
        padding: 0.9rem 1.2rem;
    }
    
    .auth-alert {
        margin-bottom: 0.6rem;
        padding: 0.6rem;
        font-size: 0.9rem;
    }
    
    .auth-title {
        font-size: 1.3rem;
        margin-bottom: 0.3rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
    
    .auth-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 0.6rem;
    }
}

/* تحسين المسافات في الصفحات الطويلة */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين التمرير للصفحة كاملة */
body.auth-page {
    overflow-x: hidden;
    overflow-y: auto;
}

/* تحسين مسافات النصوص */
.auth-form-label {
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* تحسين مسافات الحقول النصية الطويلة */
.auth-form-control[type="email"],
.auth-form-control[type="tel"] {
    letter-spacing: 0.3px;
}

.auth-form-control[type="password"] {
    letter-spacing: 1px;
}

/* تحسين مسافات textarea */
.auth-form-control[rows] {
    min-height: 60px;
    max-height: 120px;
    line-height: 1.4;
    padding-top: 0.8rem;
    padding-bottom: 0.8rem;
}

/* تحسين مسافات الروابط الإضافية */
.text-center.mt-4 {
    margin-top: 1rem !important;
}

.text-center.mt-5 {
    margin-top: 1.5rem !important;
}

/* تحسين مسافات المعلومات الإضافية */
.text-white-50.small {
    margin-top: 0.5rem;
    padding: 0.5rem;
}

/* تحسين مسافات الفواصل */
.pt-4 {
    padding-top: 1rem !important;
}

/* تحسين مسافات الأعمدة */
.col-md-6 .auth-form-floating,
.col-lg-6 .auth-form-floating {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .col-md-6 .auth-form-floating,
    .col-lg-6 .auth-form-floating {
        margin-bottom: 0.8rem;
    }
}

/* تحسين مسافات الحاوي الرئيسي */
.container.auth-container {
    width: 100%;
    max-width: 1200px;
}

/* تحسين مسافات الصفوف */
.row.justify-content-center {
    margin: 0;
    width: 100%;
}

.row.justify-content-center > [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

@media (max-width: 768px) {
    .row.justify-content-center > [class*="col-"] {
        padding-left: 0.3rem;
        padding-right: 0.3rem;
    }
}

/* تحسين التباعد العمودي */
.py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
}

.py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

@media (max-width: 768px) {
    .py-5 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
    
    .py-3 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
}
