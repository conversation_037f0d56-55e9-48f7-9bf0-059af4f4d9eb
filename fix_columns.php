<?php
/**
 * إصلاح الأعمدة المفقودة - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// زيادة حد الذاكرة
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);

echo "<h1>🔧 إصلاح الأعمدة المفقودة - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص وإصلاح جدول clients
    echo "<h2>1. إصلاح جدول clients</h2>";
    
    // فحص الأعمدة الموجودة
    $columns_check = $pdo->query("DESCRIBE clients");
    $existing_columns = [];
    while ($row = $columns_check->fetch()) {
        $existing_columns[] = $row['Field'];
    }
    
    echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $existing_columns) . "</p>";
    
    // الأعمدة المطلوبة
    $required_columns = [
        'client_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(255) NOT NULL DEFAULT "مركز الألعاب"',
        'email' => 'VARCHAR(255) DEFAULT NULL',
        'phone' => 'VARCHAR(20) DEFAULT NULL',
        'address' => 'TEXT DEFAULT NULL',
        'business_type' => 'VARCHAR(50) DEFAULT "gaming_center"',
        'description' => 'TEXT DEFAULT NULL',
        'working_hours' => 'VARCHAR(255) DEFAULT "من 9 صباحاً إلى 12 منتصف الليل"',
        'password' => 'VARCHAR(255) DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    $fixes_applied = 0;
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $sql = "ALTER TABLE clients ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ تم إضافة عمود: $column_name</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود مسبقاً</p>";
        }
    }
    
    // إضافة بيانات افتراضية إذا لم تكن موجودة
    echo "<h2>2. إضافة بيانات افتراضية</h2>";
    
    $client_check = $pdo->query("SELECT COUNT(*) as count FROM clients");
    $client_count = $client_check->fetch()['count'];
    
    if ($client_count == 0) {
        $default_client = $pdo->prepare("
            INSERT INTO clients (client_id, name, email, phone, address, business_type, password) 
            VALUES (1, 'مركز الألعاب', '<EMAIL>', '***********', 'شارع فريد - الأحساء', 'gaming_center', ?)
        ");
        $default_password = password_hash('123456', PASSWORD_DEFAULT);
        $default_client->execute([$default_password]);
        echo "<p style='color: green;'>✅ تم إضافة عميل افتراضي</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: blue;'>ℹ️ يوجد $client_count عميل في النظام</p>";
        
        // تحديث البيانات الفارغة
        $update_empty = $pdo->exec("
            UPDATE clients 
            SET name = 'مركز الألعاب' 
            WHERE name IS NULL OR name = ''
        ");
        if ($update_empty > 0) {
            echo "<p style='color: green;'>✅ تم تحديث $update_empty عميل بأسماء فارغة</p>";
            $fixes_applied++;
        }
    }
    
    // فحص وإصلاح جدول employees
    echo "<h2>3. إصلاح جدول employees</h2>";
    
    $employees_check = $pdo->query("SHOW TABLES LIKE 'employees'");
    if ($employees_check->rowCount() == 0) {
        $create_employees = "
            CREATE TABLE employees (
                employee_id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL DEFAULT 1,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE,
                phone VARCHAR(20),
                address TEXT,
                role VARCHAR(50) DEFAULT 'employee',
                password VARCHAR(255) NOT NULL,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
            )
        ";
        $pdo->exec($create_employees);
        echo "<p style='color: green;'>✅ تم إنشاء جدول employees</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول employees موجود</p>";
        
        // فحص الأعمدة المطلوبة
        $emp_columns_check = $pdo->query("DESCRIBE employees");
        $emp_existing_columns = [];
        while ($row = $emp_columns_check->fetch()) {
            $emp_existing_columns[] = $row['Field'];
        }
        
        $emp_required_columns = [
            'name' => 'VARCHAR(255) NOT NULL DEFAULT "موظف"',
            'email' => 'VARCHAR(255) DEFAULT NULL',
            'phone' => 'VARCHAR(20) DEFAULT NULL',
            'address' => 'TEXT DEFAULT NULL',
            'role' => 'VARCHAR(50) DEFAULT "employee"',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];
        
        foreach ($emp_required_columns as $column_name => $column_definition) {
            if (!in_array($column_name, $emp_existing_columns)) {
                try {
                    $sql = "ALTER TABLE employees ADD COLUMN $column_name $column_definition";
                    $pdo->exec($sql);
                    echo "<p style='color: green;'>✅ تم إضافة عمود employees.$column_name</p>";
                    $fixes_applied++;
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة عمود employees.$column_name: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    // فحص وإصلاح جدول devices
    echo "<h2>4. إصلاح جدول devices</h2>";
    
    $devices_check = $pdo->query("DESCRIBE devices");
    $devices_existing_columns = [];
    while ($row = $devices_check->fetch()) {
        $devices_existing_columns[] = $row['Field'];
    }
    
    if (!in_array('client_id', $devices_existing_columns)) {
        try {
            $pdo->exec("ALTER TABLE devices ADD COLUMN client_id INT NOT NULL DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود client_id لجدول devices</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في إضافة client_id لجدول devices: " . $e->getMessage() . "</p>";
        }
    }
    
    // اختبار الاستعلامات
    echo "<h2>5. اختبار الاستعلامات</h2>";
    
    try {
        $test_client = $pdo->query("SELECT client_id, name, email FROM clients LIMIT 1");
        $client_data = $test_client->fetch();
        if ($client_data) {
            echo "<p style='color: green;'>✅ استعلام العملاء يعمل - العميل: " . htmlspecialchars($client_data['name']) . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام العملاء: " . $e->getMessage() . "</p>";
    }
    
    try {
        $test_devices = $pdo->query("SELECT COUNT(*) as count FROM devices");
        $devices_count = $test_devices->fetch()['count'];
        echo "<p style='color: green;'>✅ استعلام الأجهزة يعمل - عدد الأجهزة: $devices_count</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<ul>";
    echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
    echo "<li>جدول clients محدث ويحتوي على جميع الأعمدة المطلوبة</li>";
    echo "<li>جدول employees جاهز للاستخدام</li>";
    echo "<li>جدول devices محدث</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الإصلاح</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر النظام الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/profile.php' style='background: #667eea; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>الملف الشخصي</a>";
echo "<a href='client/settings.php' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إعدادات المحل</a>";
echo "<a href='client/dashboard.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>
