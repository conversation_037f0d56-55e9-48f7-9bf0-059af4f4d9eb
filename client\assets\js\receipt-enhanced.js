/**
 * وظائف الفاتورة المحسنة
 * Enhanced Receipt Functions
 */

class ReceiptEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.setupPrintOptimization();
        this.setupInteractiveElements();
        this.setupAnimations();
        this.setupKeyboardShortcuts();
        this.setupAccessibility();
    }

    // تحسين الطباعة
    setupPrintOptimization() {
        window.addEventListener('beforeprint', () => {
            this.preparePrint();
        });

        window.addEventListener('afterprint', () => {
            this.restoreAfterPrint();
        });
    }

    preparePrint() {
        // إضافة كلاسات الطباعة
        document.body.classList.add('print-optimized');
        
        // تحسين الألوان
        const elements = document.querySelectorAll('.receipt-header, .total-section, .company-logo');
        elements.forEach(el => el.classList.add('print-colors'));
        
        // إخفاء العناصر غير المرغوبة
        const noPrintElements = document.querySelectorAll('.no-print, .receipt-actions');
        noPrintElements.forEach(el => {
            el.style.display = 'none';
            el.setAttribute('data-hidden-for-print', 'true');
        });
        
        // تحسين الخطوط للطباعة
        const arabicTexts = document.querySelectorAll('.arabic-text');
        arabicTexts.forEach(el => {
            el.style.fontFamily = 'Arial, Tahoma, sans-serif';
        });
    }

    restoreAfterPrint() {
        // إزالة كلاسات الطباعة
        document.body.classList.remove('print-optimized');
        
        // استعادة الألوان
        const elements = document.querySelectorAll('.print-colors');
        elements.forEach(el => el.classList.remove('print-colors'));
        
        // إظهار العناصر المخفية
        const hiddenElements = document.querySelectorAll('[data-hidden-for-print="true"]');
        hiddenElements.forEach(el => {
            el.style.display = '';
            el.removeAttribute('data-hidden-for-print');
        });
        
        // استعادة الخطوط
        const arabicTexts = document.querySelectorAll('.arabic-text');
        arabicTexts.forEach(el => {
            el.style.fontFamily = '';
        });
    }

    // إعداد العناصر التفاعلية
    setupInteractiveElements() {
        // تأثيرات النقر
        const interactiveElements = document.querySelectorAll('.receipt-interactive');
        interactiveElements.forEach(element => {
            element.addEventListener('click', (e) => {
                this.addClickEffect(e.target);
            });
            
            element.addEventListener('mouseenter', (e) => {
                this.addHoverEffect(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.removeHoverEffect(e.target);
            });
        });

        // تحسين الأزرار
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(button => {
            button.addEventListener('mouseenter', (e) => {
                e.target.style.transform = 'translateY(-3px) scale(1.05)';
                e.target.style.boxShadow = '0 8px 25px rgba(0,0,0,0.2)';
            });
            
            button.addEventListener('mouseleave', (e) => {
                e.target.style.transform = '';
                e.target.style.boxShadow = '';
            });
        });
    }

    addClickEffect(element) {
        element.style.transform = 'scale(0.95)';
        element.style.transition = 'transform 0.1s ease';
        
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }

    addHoverEffect(element) {
        element.style.backgroundColor = '#f8f9fa';
        element.style.transition = 'background-color 0.2s ease';
    }

    removeHoverEffect(element) {
        element.style.backgroundColor = '';
    }

    // إعداد الرسوم المتحركة
    setupAnimations() {
        // تأثير التحميل
        const receiptContainer = document.querySelector('.receipt-container');
        if (receiptContainer) {
            receiptContainer.style.opacity = '0';
            receiptContainer.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                receiptContainer.style.transition = 'all 0.6s ease';
                receiptContainer.style.opacity = '1';
                receiptContainer.style.transform = 'translateY(0)';
            }, 100);
        }

        // تأثير النبض للمجموع
        const totalSection = document.querySelector('.total-section');
        if (totalSection) {
            setTimeout(() => {
                totalSection.classList.add('receipt-pulse');
            }, 1000);
        }

        // تأثير التدرج للعناصر
        this.animateElements();
    }

    animateElements() {
        const elements = document.querySelectorAll('.detail-row');
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateX(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.4s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateX(0)';
            }, 200 + (index * 100));
        });
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+P للطباعة
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            
            // Ctrl+S للحفظ
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveReceipt();
            }
            
            // Ctrl+Shift+S للمشاركة
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                this.shareReceipt();
            }
        });
    }

    // إعداد إمكانية الوصول
    setupAccessibility() {
        // إضافة ARIA labels
        const receiptContainer = document.querySelector('.receipt-container');
        if (receiptContainer) {
            receiptContainer.setAttribute('role', 'document');
            receiptContainer.setAttribute('aria-label', 'فاتورة مبيعات');
        }

        // تحسين التنقل بلوحة المفاتيح
        const interactiveElements = document.querySelectorAll('.receipt-interactive');
        interactiveElements.forEach((element, index) => {
            element.setAttribute('tabindex', index + 1);
            element.setAttribute('role', 'button');
        });

        // إضافة تلميحات للأزرار
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(button => {
            const text = button.textContent.trim();
            button.setAttribute('aria-label', text);
            button.setAttribute('title', text);
        });
    }

    // حفظ الفاتورة
    saveReceipt() {
        // محاولة استخدام Web Share API
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            }).catch(console.error);
        } else {
            // نسخ الرابط
            this.copyToClipboard(window.location.href);
        }
    }

    // مشاركة الفاتورة
    shareReceipt() {
        const receiptNumber = document.querySelector('.receipt-number')?.textContent || '';
        const totalAmount = document.querySelector('.total-amount')?.textContent || '';
        
        const shareText = `${receiptNumber}\nالمبلغ الإجمالي: ${totalAmount}`;
        
        if (navigator.share) {
            navigator.share({
                title: 'فاتورة مبيعات',
                text: shareText,
                url: window.location.href
            }).catch(console.error);
        } else {
            this.copyToClipboard(shareText);
        }
    }

    // نسخ النص إلى الحافظة
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification('تم نسخ المعلومات إلى الحافظة');
            }).catch(console.error);
        } else {
            // طريقة بديلة للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('تم نسخ المعلومات إلى الحافظة');
        }
    }

    // إظهار إشعار
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'receipt-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 1000;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // إخفاء الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // تحسين الأداء
    optimizePerformance() {
        // تأجيل تحميل الصور
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
}

// تهيئة المحسن عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new ReceiptEnhancer();
});

// تصدير الكلاس للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReceiptEnhancer;
}
