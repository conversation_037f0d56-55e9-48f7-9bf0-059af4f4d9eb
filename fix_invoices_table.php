<?php
require_once 'config/database.php';

echo "<h1>إصلاح جدول الفواتير</h1>";

try {
    // 1. التحقق من وجود عمود client_id في جدول invoices
    echo "<h2>1. فحص هيكل جدول invoices</h2>";
    $stmt = $pdo->query("DESCRIBE invoices");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $client_id_exists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'client_id') {
            $client_id_exists = true;
            break;
        }
    }
    
    if ($client_id_exists) {
        echo "<p style='color: green;'>✅ عمود client_id موجود بالفعل</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ عمود client_id غير موجود - سيتم إضافته</p>";
        
        // إضافة عمود client_id
        echo "<h3>إضافة عمود client_id...</h3>";
        $pdo->exec("ALTER TABLE invoices ADD COLUMN client_id INT(11) NOT NULL DEFAULT 1 AFTER total_cost");
        echo "<p style='color: green;'>✅ تم إضافة عمود client_id بنجاح</p>";
        
        // إضافة فهرس للعمود الجديد
        $pdo->exec("ALTER TABLE invoices ADD INDEX idx_invoices_client_id (client_id)");
        echo "<p style='color: green;'>✅ تم إضافة فهرس للعمود client_id</p>";
    }
    
    // 2. تحديث الفواتير الموجودة بـ client_id من الجلسات المرتبطة
    echo "<h2>2. تحديث client_id للفواتير الموجودة</h2>";
    $stmt = $pdo->query("
        UPDATE invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        SET i.client_id = d.client_id
        WHERE i.client_id = 1 OR i.client_id IS NULL
    ");
    $updated_rows = $stmt->rowCount();
    echo "<p style='color: green;'>✅ تم تحديث $updated_rows فاتورة بـ client_id الصحيح</p>";
    
    // 3. إنشاء الفواتير المفقودة للجلسات المكتملة
    echo "<h2>3. إنشاء الفواتير المفقودة</h2>";
    
    // جلب الجلسات المكتملة بدون فواتير
    $stmt = $pdo->query("
        SELECT s.session_id, s.end_time, s.total_cost, d.client_id, d.hourly_rate,
               TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN invoices i ON s.session_id = i.session_id
        WHERE s.status = 'completed' AND i.session_id IS NULL
        ORDER BY s.end_time DESC
    ");
    $sessions_without_invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الجلسات المكتملة بدون فواتير: <strong>" . count($sessions_without_invoices) . "</strong></p>";
    
    if (count($sessions_without_invoices) > 0) {
        echo "<h3>إنشاء الفواتير...</h3>";
        
        $created_count = 0;
        foreach ($sessions_without_invoices as $session) {
            // حساب التكلفة
            $duration_minutes = $session['duration_minutes'] ?? 0;
            $hourly_rate = $session['hourly_rate'] ?? 0;
            $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
            
            // جلب تكلفة المنتجات
            $products_stmt = $pdo->prepare("
                SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                FROM session_products sp
                WHERE sp.session_id = ?
            ");
            $products_stmt->execute([$session['session_id']]);
            $products_cost = $products_stmt->fetchColumn() ?: 0;
            
            // استخدام التكلفة المحفوظة في الجلسة إذا كانت متاحة
            $total_cost = $session['total_cost'] > 0 ? $session['total_cost'] : ($time_cost + $products_cost);
            
            // إنشاء رقم الفاتورة
            $invoice_number = date('Ymd', strtotime($session['end_time'])) . str_pad($session['session_id'], 4, '0', STR_PAD_LEFT);
            
            try {
                $insert_stmt = $pdo->prepare("
                    INSERT INTO invoices (
                        session_id,
                        invoice_number,
                        time_cost,
                        products_cost,
                        total_cost,
                        client_id,
                        created_by,
                        created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $result = $insert_stmt->execute([
                    $session['session_id'],
                    $invoice_number,
                    $time_cost,
                    $products_cost,
                    $total_cost,
                    $session['client_id'],
                    $session['client_id'],
                    $session['end_time']
                ]);
                
                if ($result) {
                    $created_count++;
                    echo "<p style='color: green;'>✅ تم إنشاء فاتورة للجلسة {$session['session_id']} - رقم الفاتورة: $invoice_number</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء فاتورة للجلسة {$session['session_id']}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p style='color: green; font-weight: bold;'>✅ تم إنشاء $created_count فاتورة جديدة</p>";
    }
    
    // 4. عرض الإحصائيات النهائية
    echo "<h2>4. الإحصائيات النهائية</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sessions WHERE status = 'completed'");
    $completed_sessions = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM invoices");
    $total_invoices = $stmt->fetchColumn();
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM sessions s
        LEFT JOIN invoices i ON s.session_id = i.session_id
        WHERE s.status = 'completed' AND i.session_id IS NULL
    ");
    $missing_invoices = $stmt->fetchColumn();
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 الإحصائيات:</h3>";
    echo "<ul>";
    echo "<li><strong>الجلسات المكتملة:</strong> $completed_sessions</li>";
    echo "<li><strong>إجمالي الفواتير:</strong> $total_invoices</li>";
    echo "<li><strong>الفواتير المفقودة:</strong> $missing_invoices</li>";
    echo "</ul>";
    
    if ($missing_invoices == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 جميع الجلسات المكتملة لديها فواتير الآن!</p>";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ لا تزال هناك $missing_invoices جلسة بدون فواتير</p>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a>";
echo "<a href='debug_invoices_creation.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص المشكلة</a>";
echo "</div>";
?>
