<?php
/**
 * نظام النسخ الاحتياطي الآمن والمشفر
 * يتضمن تشفير البيانات وحماية الملفات
 */

class SecureBackupSystem {
    
    private $pdo;
    private $backup_dir;
    private $encryption_key;
    private $cipher_method = 'AES-256-CBC';
    private $max_backups = 30; // الاحتفاظ بـ 30 نسخة احتياطية
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->backup_dir = __DIR__ . '/../backups/secure/';
        $this->encryption_key = $this->getEncryptionKey();
        $this->ensureBackupDirectory();
    }
    
    /**
     * التأكد من وجود مجلد النسخ الاحتياطي
     */
    private function ensureBackupDirectory() {
        if (!is_dir($this->backup_dir)) {
            mkdir($this->backup_dir, 0700, true); // صلاحيات محدودة
            
            // إنشاء ملف .htaccess للحماية
            file_put_contents($this->backup_dir . '.htaccess', 
                "# Complete access denial\n" .
                "Require all denied\n" .
                "<Files \"*\">\n" .
                "    Require all denied\n" .
                "</Files>"
            );
            
            // إنشاء ملف index.php فارغ
            file_put_contents($this->backup_dir . 'index.php', '<?php // Access denied ?>');
        }
    }
    
    /**
     * الحصول على مفتاح التشفير
     */
    private function getEncryptionKey() {
        $key_file = __DIR__ . '/../config/.backup_key';
        
        if (!file_exists($key_file)) {
            $key = random_bytes(32);
            file_put_contents($key_file, base64_encode($key));
            chmod($key_file, 0600);
        } else {
            $key = base64_decode(file_get_contents($key_file));
        }
        
        return $key;
    }
    
    /**
     * إنشاء نسخة احتياطية شاملة
     */
    public function createFullBackup($include_files = true) {
        try {
            $backup_id = date('Y-m-d_H-i-s') . '_' . uniqid();
            $backup_data = [
                'id' => $backup_id,
                'timestamp' => date('Y-m-d H:i:s'),
                'type' => 'full',
                'database' => $this->backupDatabase(),
                'files' => $include_files ? $this->backupFiles() : null,
                'system_info' => $this->getSystemInfo()
            ];
            
            // تشفير البيانات
            $encrypted_backup = $this->encryptData(json_encode($backup_data));
            
            // حفظ النسخة الاحتياطية
            $backup_file = $this->backup_dir . $backup_id . '.backup';
            file_put_contents($backup_file, $encrypted_backup);
            chmod($backup_file, 0600);
            
            // تسجيل النسخة الاحتياطية في قاعدة البيانات
            $this->logBackup($backup_id, 'full', filesize($backup_file));
            
            // تنظيف النسخ القديمة
            $this->cleanupOldBackups();
            
            return [
                'success' => true,
                'backup_id' => $backup_id,
                'file_size' => filesize($backup_file),
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("Backup creation failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * نسخ احتياطي لقاعدة البيانات
     */
    private function backupDatabase() {
        $tables = [];
        
        // الحصول على قائمة الجداول
        $stmt = $this->pdo->query("SHOW TABLES");
        $table_names = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($table_names as $table_name) {
            $table_data = [
                'name' => $table_name,
                'structure' => $this->getTableStructure($table_name),
                'data' => $this->getTableData($table_name)
            ];
            
            $tables[] = $table_data;
        }
        
        return $tables;
    }
    
    /**
     * الحصول على هيكل الجدول
     */
    private function getTableStructure($table_name) {
        $stmt = $this->pdo->query("SHOW CREATE TABLE `$table_name`");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['Create Table'];
    }
    
    /**
     * الحصول على بيانات الجدول
     */
    private function getTableData($table_name) {
        $stmt = $this->pdo->query("SELECT * FROM `$table_name`");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * نسخ احتياطي للملفات
     */
    private function backupFiles() {
        $files_to_backup = [
            'config/',
            'includes/',
            'assets/',
            'uploads/',
            '.htaccess'
        ];
        
        $files_data = [];
        
        foreach ($files_to_backup as $path) {
            if (is_file($path)) {
                $files_data[] = [
                    'path' => $path,
                    'type' => 'file',
                    'content' => base64_encode(file_get_contents($path)),
                    'permissions' => fileperms($path)
                ];
            } elseif (is_dir($path)) {
                $files_data = array_merge($files_data, $this->backupDirectory($path));
            }
        }
        
        return $files_data;
    }
    
    /**
     * نسخ احتياطي لمجلد
     */
    private function backupDirectory($dir_path) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir_path, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relative_path = str_replace(getcwd() . DIRECTORY_SEPARATOR, '', $file->getPathname());
                $relative_path = str_replace('\\', '/', $relative_path);
                
                // تجاهل الملفات الكبيرة جداً (أكبر من 10MB)
                if ($file->getSize() > 10485760) {
                    continue;
                }
                
                $files[] = [
                    'path' => $relative_path,
                    'type' => 'file',
                    'content' => base64_encode(file_get_contents($file->getPathname())),
                    'permissions' => $file->getPerms()
                ];
            }
        }
        
        return $files;
    }
    
    /**
     * الحصول على معلومات النظام
     */
    private function getSystemInfo() {
        return [
            'php_version' => PHP_VERSION,
            'mysql_version' => $this->pdo->query("SELECT VERSION()")->fetchColumn(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'backup_version' => '1.0',
            'site_url' => $_SERVER['HTTP_HOST'] ?? 'localhost'
        ];
    }
    
    /**
     * تشفير البيانات
     */
    private function encryptData($data) {
        $iv = random_bytes(openssl_cipher_iv_length($this->cipher_method));
        $encrypted = openssl_encrypt($data, $this->cipher_method, $this->encryption_key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    private function decryptData($encrypted_data) {
        $data = base64_decode($encrypted_data);
        $iv_length = openssl_cipher_iv_length($this->cipher_method);
        $iv = substr($data, 0, $iv_length);
        $encrypted = substr($data, $iv_length);
        return openssl_decrypt($encrypted, $this->cipher_method, $this->encryption_key, 0, $iv);
    }
    
    /**
     * تسجيل النسخة الاحتياطية
     */
    private function logBackup($backup_id, $type, $file_size) {
        try {
            // إنشاء جدول سجل النسخ الاحتياطية إذا لم يكن موجوداً
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS backup_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    backup_id VARCHAR(100) NOT NULL UNIQUE,
                    backup_type ENUM('full', 'database', 'files') NOT NULL,
                    file_size BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INT NULL,
                    status ENUM('completed', 'failed', 'corrupted') DEFAULT 'completed',
                    INDEX idx_backup_id (backup_id),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_log (backup_id, backup_type, file_size, created_by) 
                VALUES (?, ?, ?, ?)
            ");
            
            $user_id = $_SESSION['admin_id'] ?? $_SESSION['client_id'] ?? null;
            $stmt->execute([$backup_id, $type, $file_size, $user_id]);
            
        } catch (PDOException $e) {
            error_log("Backup logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * استعادة النسخة الاحتياطية
     */
    public function restoreBackup($backup_id, $restore_database = true, $restore_files = true) {
        try {
            $backup_file = $this->backup_dir . $backup_id . '.backup';
            
            if (!file_exists($backup_file)) {
                throw new Exception('ملف النسخة الاحتياطية غير موجود');
            }
            
            // فك تشفير البيانات
            $encrypted_data = file_get_contents($backup_file);
            $backup_data = json_decode($this->decryptData($encrypted_data), true);
            
            if (!$backup_data) {
                throw new Exception('فشل في فك تشفير النسخة الاحتياطية');
            }
            
            $results = [];
            
            // استعادة قاعدة البيانات
            if ($restore_database && isset($backup_data['database'])) {
                $results['database'] = $this->restoreDatabase($backup_data['database']);
            }
            
            // استعادة الملفات
            if ($restore_files && isset($backup_data['files'])) {
                $results['files'] = $this->restoreFiles($backup_data['files']);
            }
            
            return [
                'success' => true,
                'results' => $results,
                'message' => 'تم استعادة النسخة الاحتياطية بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("Backup restoration failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'فشل في استعادة النسخة الاحتياطية: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * استعادة قاعدة البيانات
     */
    private function restoreDatabase($tables_data) {
        $restored_tables = 0;
        
        // تعطيل فحص المفاتيح الخارجية مؤقتاً
        $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        foreach ($tables_data as $table_data) {
            try {
                // حذف الجدول إذا كان موجوداً
                $this->pdo->exec("DROP TABLE IF EXISTS `{$table_data['name']}`");
                
                // إنشاء الجدول
                $this->pdo->exec($table_data['structure']);
                
                // إدراج البيانات
                if (!empty($table_data['data'])) {
                    foreach ($table_data['data'] as $row) {
                        $columns = array_keys($row);
                        $placeholders = str_repeat('?,', count($columns) - 1) . '?';
                        
                        $stmt = $this->pdo->prepare(
                            "INSERT INTO `{$table_data['name']}` (`" . 
                            implode('`, `', $columns) . "`) VALUES ($placeholders)"
                        );
                        $stmt->execute(array_values($row));
                    }
                }
                
                $restored_tables++;
                
            } catch (PDOException $e) {
                error_log("Failed to restore table {$table_data['name']}: " . $e->getMessage());
            }
        }
        
        // إعادة تفعيل فحص المفاتيح الخارجية
        $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        return "تم استعادة $restored_tables جدول";
    }
    
    /**
     * استعادة الملفات
     */
    private function restoreFiles($files_data) {
        $restored_files = 0;
        
        foreach ($files_data as $file_data) {
            try {
                $file_path = $file_data['path'];
                $file_dir = dirname($file_path);
                
                // إنشاء المجلد إذا لم يكن موجوداً
                if (!is_dir($file_dir)) {
                    mkdir($file_dir, 0755, true);
                }
                
                // استعادة الملف
                $content = base64_decode($file_data['content']);
                file_put_contents($file_path, $content);
                
                // استعادة الصلاحيات
                if (isset($file_data['permissions'])) {
                    chmod($file_path, $file_data['permissions']);
                }
                
                $restored_files++;
                
            } catch (Exception $e) {
                error_log("Failed to restore file {$file_data['path']}: " . $e->getMessage());
            }
        }
        
        return "تم استعادة $restored_files ملف";
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    private function cleanupOldBackups() {
        try {
            // الحصول على قائمة النسخ الاحتياطية مرتبة بالتاريخ
            $stmt = $this->pdo->prepare("
                SELECT backup_id FROM backup_log 
                ORDER BY created_at DESC 
                LIMIT ?, 1000
            ");
            $stmt->execute([$this->max_backups]);
            $old_backups = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($old_backups as $backup_id) {
                $backup_file = $this->backup_dir . $backup_id . '.backup';
                if (file_exists($backup_file)) {
                    unlink($backup_file);
                }
                
                // حذف السجل من قاعدة البيانات
                $delete_stmt = $this->pdo->prepare("DELETE FROM backup_log WHERE backup_id = ?");
                $delete_stmt->execute([$backup_id]);
            }
            
        } catch (Exception $e) {
            error_log("Backup cleanup failed: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getBackupList() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM backup_log
                ORDER BY created_at DESC
                LIMIT 50
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Failed to get backup list: " . $e->getMessage());
            return [];
        }
    }

    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($backup_id) {
        try {
            $backup_file = $this->backup_dir . $backup_id . '.backup';

            if (file_exists($backup_file)) {
                unlink($backup_file);
            }

            $stmt = $this->pdo->prepare("DELETE FROM backup_log WHERE backup_id = ?");
            $stmt->execute([$backup_id]);

            return ['success' => true, 'message' => 'تم حذف النسخة الاحتياطية'];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * فحص سلامة النسخة الاحتياطية
     */
    public function verifyBackup($backup_id) {
        try {
            $backup_file = $this->backup_dir . $backup_id . '.backup';

            if (!file_exists($backup_file)) {
                return ['valid' => false, 'error' => 'الملف غير موجود'];
            }

            $encrypted_data = file_get_contents($backup_file);
            $backup_data = json_decode($this->decryptData($encrypted_data), true);

            if (!$backup_data) {
                return ['valid' => false, 'error' => 'فشل في فك التشفير'];
            }

            $checks = [
                'has_id' => isset($backup_data['id']),
                'has_timestamp' => isset($backup_data['timestamp']),
                'has_database' => isset($backup_data['database']) && is_array($backup_data['database']),
                'has_system_info' => isset($backup_data['system_info'])
            ];

            $valid = array_reduce($checks, function($carry, $check) {
                return $carry && $check;
            }, true);

            return [
                'valid' => $valid,
                'checks' => $checks,
                'size' => filesize($backup_file),
                'tables_count' => isset($backup_data['database']) ? count($backup_data['database']) : 0
            ];

        } catch (Exception $e) {
            return ['valid' => false, 'error' => $e->getMessage()];
        }
    }
}
?>
