<?php
/**
 * إضافة حقل صلاحية النسخ الاحتياطي إلى جدول العملاء
 * تشغيل هذا الملف مرة واحدة لإضافة الحقل الجديد
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إضافة حقل النسخ الاحتياطي للعملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h4 class='mb-0'><i class='fas fa-database me-2'></i>إضافة حقل النسخ الاحتياطي للعملاء</h4>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // التحقق من وجود الحقل
    echo "<h5 class='text-info'>1. التحقق من وجود الحقل:</h5>";
    $stmt = $pdo->prepare("SHOW COLUMNS FROM clients LIKE 'backup_enabled'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<div class='alert alert-info'><i class='fas fa-info-circle me-2'></i>الحقل موجود مسبقاً</div>";
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>الحقل غير موجود - سيتم إضافته</div>";
        
        // إضافة الحقل
        echo "<h5 class='text-success'>2. إضافة الحقل:</h5>";
        $pdo->exec("
            ALTER TABLE clients 
            ADD COLUMN backup_enabled TINYINT(1) DEFAULT 1 
            COMMENT 'صلاحية النسخ الاحتياطي للعميل (1=مفعل، 0=معطل)'
        ");
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إضافة الحقل بنجاح</div>";
    }
    
    // عرض بنية الجدول الحالية
    echo "<h5 class='text-info'>3. بنية جدول العملاء الحالية:</h5>";
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped table-sm'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>اسم الحقل</th><th>النوع</th><th>Null</th><th>المفتاح</th><th>القيمة الافتراضية</th><th>إضافي</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($columns as $column) {
        $highlight = ($column['Field'] == 'backup_enabled') ? 'table-success' : '';
        echo "<tr class='{$highlight}'>";
        echo "<td><strong>" . htmlspecialchars($column['Field']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // عرض العملاء الحاليين مع حالة النسخ الاحتياطي
    echo "<h5 class='text-info'>4. العملاء الحاليين مع حالة النسخ الاحتياطي:</h5>";
    $stmt = $pdo->query("
        SELECT 
            client_id, 
            business_name, 
            owner_name, 
            email, 
            is_active,
            backup_enabled
        FROM clients 
        ORDER BY client_id ASC
    ");
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($clients)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-hover'>";
        echo "<thead class='table-dark'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>اسم المحل</th>";
        echo "<th>اسم المالك</th>";
        echo "<th>البريد الإلكتروني</th>";
        echo "<th>حالة الحساب</th>";
        echo "<th>النسخ الاحتياطي</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($clients as $client) {
            echo "<tr>";
            echo "<td>" . $client['client_id'] . "</td>";
            echo "<td>" . htmlspecialchars($client['business_name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['owner_name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['email']) . "</td>";
            
            // حالة الحساب
            if ($client['is_active']) {
                echo "<td><span class='badge bg-success'>نشط</span></td>";
            } else {
                echo "<td><span class='badge bg-danger'>معطل</span></td>";
            }
            
            // حالة النسخ الاحتياطي
            if ($client['backup_enabled']) {
                echo "<td><span class='badge bg-primary'><i class='fas fa-check me-1'></i>مفعل</span></td>";
            } else {
                echo "<td><span class='badge bg-warning'><i class='fas fa-times me-1'></i>معطل</span></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>لا يوجد عملاء في النظام</div>";
    }
    
    // اختبار الدوال الجديدة
    echo "<h5 class='text-success'>5. اختبار الدوال الجديدة:</h5>";
    
    require_once 'includes/backup_permissions.php';
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card border-info'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>الإعداد العام</h6>";
    $globalEnabled = isBackupEnabledGlobally();
    if ($globalEnabled) {
        echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>مفعل عموماً</span>";
    } else {
        echo "<span class='badge bg-danger fs-6'><i class='fas fa-times me-1'></i>معطل عموماً</span>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>اختبار دالة العميل</h6>";
    if (!empty($clients)) {
        $first_client = $clients[0];
        $clientEnabled = isBackupEnabledForClient($first_client['client_id']);
        echo "<p class='small mb-1'>العميل: " . htmlspecialchars($first_client['business_name']) . "</p>";
        if ($clientEnabled) {
            echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>مفعل</span>";
        } else {
            echo "<span class='badge bg-danger fs-6'><i class='fas fa-times me-1'></i>معطل</span>";
        }
    } else {
        echo "<span class='badge bg-secondary fs-6'>لا يوجد عملاء للاختبار</span>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-circle me-2'></i>";
    echo "<strong>خطأ:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>"; // card-body
echo "</div>"; // card

echo "<div class='mt-3 text-center'>";
echo "<a href='admin/clients.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users me-2'></i>إدارة العملاء";
echo "</a>";
echo "<a href='admin/settings.php' class='btn btn-success me-2'>";
echo "<i class='fas fa-cog me-2'></i>إعدادات الأدمن";
echo "</a>";
echo "<a href='test_backup_permissions.php' class='btn btn-info'>";
echo "<i class='fas fa-test-tube me-2'></i>اختبار الصلاحيات";
echo "</a>";
echo "</div>";

echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
