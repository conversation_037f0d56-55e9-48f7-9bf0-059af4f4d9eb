<?php
/**
 * صفحة إعدادات المحل - تعديل بيانات المحل والإعدادات العامة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';
require_once '../includes/backup_permissions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الإعدادات
    if (!hasPagePermission('settings')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الإعدادات';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('settings')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_settings')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد نوع المستخدم
$is_employee = isset($_SESSION['employee_id']);
$client_id = $_SESSION['client_id'];

// التحقق من صلاحيات النسخ الاحتياطي
$backup_enabled = isBackupEnabledForClient($client_id);
$system_maintenance = isSystemInMaintenance();
$page_title = "إعدادات المحل";
$active_page = "settings";

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';

        if ($action === 'update_business') {
            // تحديث بيانات المحل
            $business_name = trim($_POST['business_name']);
            $owner_name = trim($_POST['owner_name']);
            $business_type = trim($_POST['business_type']);
            $business_phone = trim($_POST['business_phone']);
            $business_email = trim($_POST['business_email']);
            $business_address = trim($_POST['business_address']);
            $business_description = trim($_POST['business_description']);
            $working_hours = trim($_POST['working_hours']);

            if (empty($business_name)) {
                throw new Exception('اسم المحل مطلوب');
            }

            try {
                $update_stmt = $pdo->prepare("
                    UPDATE clients
                    SET business_name = ?, owner_name = ?, business_type = ?, phone = ?, email = ?, address = ?,
                        description = ?, working_hours = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE client_id = ?
                ");
                $update_stmt->execute([
                    $business_name, $owner_name, $business_type, $business_phone, $business_email,
                    $business_address, $business_description, $working_hours, $client_id
                ]);
            } catch (PDOException $e) {
                // محاولة بدون الأعمدة الإضافية إذا لم تكن موجودة
                $update_stmt = $pdo->prepare("
                    UPDATE clients
                    SET business_name = ?, owner_name = ?, phone = ?, email = ?, address = ?
                    WHERE client_id = ?
                ");
                $update_stmt->execute([
                    $business_name, $owner_name, $business_phone, $business_email,
                    $business_address, $client_id
                ]);
            }

            // تحديث متغيرات الجلسة لتحديث الـ header فوراً
            $_SESSION['business_name'] = $business_name;
            $_SESSION['owner_name'] = $owner_name;
            $_SESSION['client_name'] = $business_name; // للتوافق مع الكود القديم

            $_SESSION['success'] = 'تم تحديث بيانات المحل بنجاح';

        } elseif ($action === 'update_pricing') {
            // تحديث إعدادات الأسعار
            $ps4_rate = floatval($_POST['ps4_rate']);
            $ps5_rate = floatval($_POST['ps5_rate']);
            $pc_rate = floatval($_POST['pc_rate']);
            $xbox_rate = floatval($_POST['xbox_rate']);

            // تحديث أسعار الأجهزة
            $device_rates = [
                'PS4' => $ps4_rate,
                'PS5' => $ps5_rate,
                'PC' => $pc_rate,
                'Xbox' => $xbox_rate
            ];

            foreach ($device_rates as $device_type => $rate) {
                $device_stmt = $pdo->prepare("
                    UPDATE devices SET hourly_rate = ?
                    WHERE client_id = ? AND device_type = ?
                ");
                $device_stmt->execute([$rate, $client_id, $device_type]);
            }

            $_SESSION['success'] = 'تم تحديث إعدادات الأسعار بنجاح';

        } elseif ($action === 'update_theme') {
            // تحديث إعدادات المظهر والألوان
            $primary_color = trim($_POST['primary_color']);
            $secondary_color = trim($_POST['secondary_color']);
            $accent_color = trim($_POST['accent_color']);
            $header_style = trim($_POST['header_style']);
            $sidebar_position = trim($_POST['sidebar_position']);
            $theme_mode = trim($_POST['theme_mode']);

            // التحقق من صحة الألوان
            if (!preg_match('/^#[a-fA-F0-9]{6}$/', $primary_color)) {
                throw new Exception('لون أساسي غير صحيح');
            }
            if (!preg_match('/^#[a-fA-F0-9]{6}$/', $secondary_color)) {
                throw new Exception('لون ثانوي غير صحيح');
            }
            if (!preg_match('/^#[a-fA-F0-9]{6}$/', $accent_color)) {
                throw new Exception('لون مميز غير صحيح');
            }

            // إنشاء جدول إعدادات المظهر إذا لم يكن موجوداً
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS client_theme_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    client_id INT NOT NULL,
                    primary_color VARCHAR(7) DEFAULT '#0d6efd',
                    secondary_color VARCHAR(7) DEFAULT '#6c757d',
                    accent_color VARCHAR(7) DEFAULT '#20c997',
                    header_style ENUM('top', 'sidebar') DEFAULT 'top',
                    sidebar_position ENUM('right', 'left') DEFAULT 'right',
                    theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_client (client_id),
                    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
                )
            ");

            // حفظ أو تحديث إعدادات المظهر
            $theme_stmt = $pdo->prepare("
                INSERT INTO client_theme_settings
                (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                primary_color = VALUES(primary_color),
                secondary_color = VALUES(secondary_color),
                accent_color = VALUES(accent_color),
                header_style = VALUES(header_style),
                sidebar_position = VALUES(sidebar_position),
                theme_mode = VALUES(theme_mode),
                updated_at = CURRENT_TIMESTAMP
            ");
            $theme_stmt->execute([
                $client_id, $primary_color, $secondary_color, $accent_color,
                $header_style, $sidebar_position, $theme_mode
            ]);

            $_SESSION['success'] = 'تم تحديث إعدادات المظهر بنجاح';
        }

        header('Location: settings.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// جلب بيانات المحل الحالية
try {
    $business_stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
    $business_stmt->execute([$client_id]);
    $business_data = $business_stmt->fetch(PDO::FETCH_ASSOC);

    // إذا لم توجد بيانات، إنشاء بيانات افتراضية
    if (!$business_data) {
        $business_data = [
            'client_id' => $client_id,
            'business_name' => 'مركز الألعاب',
            'owner_name' => 'صاحب المحل',
            'email' => '<EMAIL>',
            'phone' => '***********',
            'address' => 'شارع فريد - الأحساء',
            'business_type' => 'gaming_center',
            'description' => 'مركز ألعاب متطور',
            'working_hours' => 'من 9 صباحاً إلى 12 منتصف الليل'
        ];
    }

    // جلب أسعار الأجهزة
    try {
        $device_rates_stmt = $pdo->prepare("
            SELECT device_type, AVG(hourly_rate) as avg_rate
            FROM devices
            WHERE client_id = ?
            GROUP BY device_type
        ");
        $device_rates_stmt->execute([$client_id]);
        $device_rates_raw = $device_rates_stmt->fetchAll(PDO::FETCH_ASSOC);

        $device_rates = [];
        foreach ($device_rates_raw as $rate) {
            $device_rates[$rate['device_type']] = $rate['avg_rate'];
        }
    } catch (PDOException $e) {
        // أسعار افتراضية إذا فشل الاستعلام
        $device_rates = [
            'PS4' => 20,
            'PS5' => 30,
            'PC' => 25,
            'Xbox' => 25
        ];
    }

    // جلب إعدادات المظهر الحالية
    try {
        $theme_stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
        $theme_stmt->execute([$client_id]);
        $theme_settings = $theme_stmt->fetch(PDO::FETCH_ASSOC);

        // إعدادات افتراضية إذا لم توجد
        if (!$theme_settings) {
            $theme_settings = [
                'primary_color' => '#0d6efd',
                'secondary_color' => '#6c757d',
                'accent_color' => '#20c997',
                'header_style' => 'top',
                'sidebar_position' => 'right',
                'theme_mode' => 'light'
            ];
        }
    } catch (PDOException $e) {
        // إعدادات افتراضية في حالة الخطأ
        $theme_settings = [
            'primary_color' => '#0d6efd',
            'secondary_color' => '#6c757d',
            'accent_color' => '#20c997',
            'header_style' => 'top',
            'sidebar_position' => 'right',
            'theme_mode' => 'light'
        ];
    }

} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
    $business_data = [
        'business_name' => 'مركز الألعاب',
        'owner_name' => 'صاحب المحل',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'address' => 'شارع فريد - الأحساء',
        'business_type' => 'gaming_center',
        'description' => 'مركز ألعاب متطور',
        'working_hours' => 'من 9 صباحاً إلى 12 منتصف الليل'
    ];
    $device_rates = [
        'PS4' => 20,
        'PS5' => 30,
        'PC' => 25,
        'Xbox' => 25
    ];
}

require_once 'includes/header.php';
?>

<style>
.settings-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.settings-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
}

.section-title {
    color: #28a745;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-success-gradient {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-success-gradient:hover {
    transform: translateY(-2px);
    color: white;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.nav-pills .nav-link {
    color: #28a745;
    border-radius: 25px;
    margin-bottom: 10px;
}

.nav-pills .nav-link:hover {
    background-color: rgba(40, 167, 69, 0.1);
}

.settings-icon {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.price-input {
    position: relative;
}

.price-input::before {
    content: 'ج.م';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 10;
}

.price-input input {
    padding-left: 40px;
}

/* تنسيقات تخصيص المظهر */
.theme-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.theme-preview:hover {
    border-color: #28a745;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.1);
}

.preset-color {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.preset-color:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.preset-color .fas.fa-check {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.form-control.form-control-color {
    border: 3px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control.form-control-color:hover {
    border-color: #28a745;
    transform: scale(1.05);
}

.color-input-group {
    position: relative;
}

.color-input-group .form-control[readonly] {
    background-color: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    text-align: center;
}

/* تحسينات للمعاينة */
.preview-header {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.preview-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.preview-content .btn {
    transition: all 0.3s ease;
    margin: 5px;
}

.preview-content .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تحسينات للنماذج */
.form-floating > label {
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus ~ label {
    color: #28a745;
}

/* تأثيرات الأزرار */
.btn-success-gradient {
    position: relative;
    overflow: hidden;
}

.btn-success-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-success-gradient:hover::before {
    left: 100%;
}

/* تحسينات للتبويبات */
.nav-pills .nav-link {
    position: relative;
    overflow: hidden;
}

.nav-pills .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(40, 167, 69, 0.1), transparent);
    transition: left 0.3s;
}

.nav-pills .nav-link:hover::before {
    left: 100%;
}
</style>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="settings-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <div class="settings-icon">
                    <i class="fas fa-cog fa-3x"></i>
                </div>
            </div>
            <div class="col-md-10">
                <h2 class="mb-2">إعدادات المحل</h2>
                <p class="mb-0 opacity-75">إدارة بيانات المحل والإعدادات العامة والأسعار</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قائمة التبويبات -->
        <div class="col-lg-3">
            <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                <button class="nav-link active" id="v-pills-business-tab" data-bs-toggle="pill"
                        data-bs-target="#v-pills-business" type="button" role="tab">
                    <i class="fas fa-store me-2"></i>بيانات المحل
                </button>
                <button class="nav-link" id="v-pills-pricing-tab" data-bs-toggle="pill"
                        data-bs-target="#v-pills-pricing" type="button" role="tab">
                    <i class="fas fa-tags me-2"></i>الأسعار والرسوم
                </button>
                <button class="nav-link" id="v-pills-theme-tab" data-bs-toggle="pill"
                        data-bs-target="#v-pills-theme" type="button" role="tab">
                    <i class="fas fa-palette me-2"></i>تخصيص المظهر
                </button>
                <?php if ($backup_enabled): ?>
                <button class="nav-link" id="v-pills-backup-tab" data-bs-toggle="pill"
                        data-bs-target="#v-pills-backup" type="button" role="tab">
                    <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    <?php if ($system_maintenance): ?>
                        <span class="badge bg-warning ms-1">صيانة</span>
                    <?php endif; ?>
                </button>
                <?php endif; ?>
            </div>

            <div class="settings-section mt-3">
                <div class="d-grid gap-2">
                    <a href="profile.php" class="btn btn-outline-success">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- محتوى التبويبات -->
        <div class="col-lg-9">
            <div class="tab-content" id="v-pills-tabContent">

                <!-- تبويب بيانات المحل -->
                <div class="tab-pane fade show active" id="v-pills-business" role="tabpanel">
                    <div class="settings-section">
                        <h4 class="section-title">
                            <i class="fas fa-store"></i>بيانات المحل الأساسية
                        </h4>

                        <form method="POST">
                            <input type="hidden" name="action" value="update_business">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="business_name" name="business_name"
                                               value="<?php echo htmlspecialchars($business_data['business_name'] ?? $business_data['name'] ?? ''); ?>" required>
                                        <label for="business_name">اسم المحل</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="owner_name" name="owner_name"
                                               value="<?php echo htmlspecialchars($business_data['owner_name'] ?? ''); ?>" required>
                                        <label for="owner_name">اسم صاحب المحل</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="business_type" name="business_type">
                                            <option value="gaming_center" <?php echo ($business_data['business_type'] ?? '') === 'gaming_center' ? 'selected' : ''; ?>>مركز ألعاب</option>
                                            <option value="internet_cafe" <?php echo ($business_data['business_type'] ?? '') === 'internet_cafe' ? 'selected' : ''; ?>>مقهى إنترنت</option>
                                            <option value="playstation_cafe" <?php echo ($business_data['business_type'] ?? '') === 'playstation_cafe' ? 'selected' : ''; ?>>مقهى بلايستيشن</option>
                                            <option value="mixed" <?php echo ($business_data['business_type'] ?? '') === 'mixed' ? 'selected' : ''; ?>>مختلط</option>
                                        </select>
                                        <label for="business_type">نوع المحل</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="tel" class="form-control" id="business_phone" name="business_phone"
                                               value="<?php echo htmlspecialchars($business_data['phone'] ?? ''); ?>">
                                        <label for="business_phone">رقم الهاتف</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="business_email" name="business_email"
                                       value="<?php echo htmlspecialchars($business_data['email'] ?? ''); ?>">
                                <label for="business_email">البريد الإلكتروني</label>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="business_address" name="business_address" style="height: 80px"><?php echo htmlspecialchars($business_data['address'] ?? ''); ?></textarea>
                                <label for="business_address">العنوان</label>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="business_description" name="business_description" style="height: 100px"><?php echo htmlspecialchars($business_data['description'] ?? ''); ?></textarea>
                                <label for="business_description">وصف المحل</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="working_hours" name="working_hours"
                                       value="<?php echo htmlspecialchars($business_data['working_hours'] ?? 'من 9 صباحاً إلى 12 منتصف الليل'); ?>"
                                       placeholder="مثال: من 9 صباحاً إلى 12 منتصف الليل">
                                <label for="working_hours">ساعات العمل</label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success-gradient">
                                    <i class="fas fa-save me-2"></i>حفظ بيانات المحل
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- تبويب الأسعار -->
                <div class="tab-pane fade" id="v-pills-pricing" role="tabpanel">
                    <div class="settings-section">
                        <h4 class="section-title">
                            <i class="fas fa-tags"></i>إعدادات الأسعار والرسوم
                        </h4>

                        <form method="POST">
                            <input type="hidden" name="action" value="update_pricing">

                            <h5 class="text-success mb-3">أسعار الأجهزة</h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">PlayStation 4</label>
                                    <div class="price-input">
                                        <input type="number" class="form-control" name="ps4_rate"
                                               value="<?php echo $device_rates['PS4'] ?? 20; ?>"
                                               step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">PlayStation 5</label>
                                    <div class="price-input">
                                        <input type="number" class="form-control" name="ps5_rate"
                                               value="<?php echo $device_rates['PS5'] ?? 30; ?>"
                                               step="0.01" min="0">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">PC Gaming</label>
                                    <div class="price-input">
                                        <input type="number" class="form-control" name="pc_rate"
                                               value="<?php echo $device_rates['PC'] ?? 25; ?>"
                                               step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Xbox</label>
                                    <div class="price-input">
                                        <input type="number" class="form-control" name="xbox_rate"
                                               value="<?php echo $device_rates['Xbox'] ?? 25; ?>"
                                               step="0.01" min="0">
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-success-gradient">
                                    <i class="fas fa-save me-2"></i>حفظ إعدادات الأسعار
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- تبويب تخصيص المظهر -->
                <div class="tab-pane fade" id="v-pills-theme" role="tabpanel">
                    <div class="settings-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="section-title mb-0">
                                <i class="fas fa-palette"></i>تخصيص المظهر والألوان
                            </h4>
                            <a href="theme-help.html" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-question-circle me-1"></i>دليل الاستخدام
                            </a>
                        </div>

                        <form method="POST" id="themeForm">
                            <input type="hidden" name="action" value="update_theme">

                            <!-- إعدادات الألوان -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-paint-brush me-2"></i>ألوان الموقع
                                    </h5>

                                    <!-- ألوان محددة مسبقاً -->
                                    <div class="mb-3">
                                        <label class="form-label">اختر من الألوان المحددة مسبقاً:</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#0d6efd" data-secondary="#6c757d" data-accent="#20c997"
                                                    style="background: linear-gradient(45deg, #0d6efd, #20c997); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="الأزرق الكلاسيكي">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#dc3545" data-secondary="#6c757d" data-accent="#fd7e14"
                                                    style="background: linear-gradient(45deg, #dc3545, #fd7e14); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="الأحمر النشط">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#198754" data-secondary="#6c757d" data-accent="#20c997"
                                                    style="background: linear-gradient(45deg, #198754, #20c997); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="الأخضر الطبيعي">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#6f42c1" data-secondary="#6c757d" data-accent="#e83e8c"
                                                    style="background: linear-gradient(45deg, #6f42c1, #e83e8c); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="البنفسجي الملكي">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#fd7e14" data-secondary="#6c757d" data-accent="#ffc107"
                                                    style="background: linear-gradient(45deg, #fd7e14, #ffc107); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="البرتقالي المشرق">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#0dcaf0" data-secondary="#6c757d" data-accent="#20c997"
                                                    style="background: linear-gradient(45deg, #0dcaf0, #20c997); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="الأزرق السماوي">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm preset-color"
                                                    data-primary="#212529" data-secondary="#495057" data-accent="#6c757d"
                                                    style="background: linear-gradient(45deg, #212529, #6c757d); color: white; width: 60px; height: 40px; border: none; border-radius: 8px;"
                                                    title="الرمادي الداكن">
                                                <i class="fas fa-check" style="display: none;"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">اللون الأساسي</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color me-2"
                                               name="primary_color" id="primary_color"
                                               value="<?php echo $theme_settings['primary_color']; ?>"
                                               style="width: 60px; height: 40px;">
                                        <input type="text" class="form-control"
                                               value="<?php echo $theme_settings['primary_color']; ?>"
                                               readonly>
                                    </div>
                                    <small class="text-muted">لون الهيدر والأزرار الرئيسية</small>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">اللون الثانوي</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color me-2"
                                               name="secondary_color" id="secondary_color"
                                               value="<?php echo $theme_settings['secondary_color']; ?>"
                                               style="width: 60px; height: 40px;">
                                        <input type="text" class="form-control"
                                               value="<?php echo $theme_settings['secondary_color']; ?>"
                                               readonly>
                                    </div>
                                    <small class="text-muted">لون النصوص الثانوية والحدود</small>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">اللون المميز</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color me-2"
                                               name="accent_color" id="accent_color"
                                               value="<?php echo $theme_settings['accent_color']; ?>"
                                               style="width: 60px; height: 40px;">
                                        <input type="text" class="form-control"
                                               value="<?php echo $theme_settings['accent_color']; ?>"
                                               readonly>
                                    </div>
                                    <small class="text-muted">لون التمييز والتأكيدات</small>
                                </div>
                            </div>

                            <!-- إعدادات الهيدر -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-window-maximize me-2"></i>تخصيص الهيدر
                                    </h5>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">نمط الهيدر</label>
                                    <select class="form-select" name="header_style" id="header_style">
                                        <option value="top" <?php echo $theme_settings['header_style'] === 'top' ? 'selected' : ''; ?>>
                                            هيدر علوي مثبت
                                        </option>
                                        <option value="sidebar" <?php echo $theme_settings['header_style'] === 'sidebar' ? 'selected' : ''; ?>>
                                            قائمة جانبية
                                        </option>
                                    </select>
                                    <small class="text-muted">اختر شكل عرض القائمة الرئيسية</small>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">موضع القائمة الجانبية</label>
                                    <select class="form-select" name="sidebar_position" id="sidebar_position">
                                        <option value="right" <?php echo $theme_settings['sidebar_position'] === 'right' ? 'selected' : ''; ?>>
                                            يمين الصفحة
                                        </option>
                                        <option value="left" <?php echo $theme_settings['sidebar_position'] === 'left' ? 'selected' : ''; ?>>
                                            يسار الصفحة
                                        </option>
                                    </select>
                                    <small class="text-muted">يظهر فقط عند اختيار القائمة الجانبية</small>
                                </div>
                            </div>

                            <!-- إعدادات النمط العام -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-adjust me-2"></i>النمط العام
                                    </h5>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">وضع العرض</label>
                                    <select class="form-select" name="theme_mode" id="theme_mode">
                                        <option value="light" <?php echo $theme_settings['theme_mode'] === 'light' ? 'selected' : ''; ?>>
                                            فاتح
                                        </option>
                                        <option value="dark" <?php echo $theme_settings['theme_mode'] === 'dark' ? 'selected' : ''; ?>>
                                            داكن
                                        </option>
                                        <option value="auto" <?php echo $theme_settings['theme_mode'] === 'auto' ? 'selected' : ''; ?>>
                                            تلقائي (حسب النظام)
                                        </option>
                                    </select>
                                    <small class="text-muted">اختر نمط العرض المفضل</small>
                                </div>
                            </div>

                            <!-- معاينة الألوان -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-eye me-2"></i>معاينة الألوان
                                    </h5>
                                    <div class="theme-preview p-3 border rounded" id="themePreview">
                                        <div class="preview-header p-2 text-white rounded mb-2" style="background-color: <?php echo $theme_settings['primary_color']; ?>">
                                            <i class="fas fa-gamepad me-2"></i>عينة الهيدر
                                        </div>
                                        <div class="preview-content">
                                            <button class="btn me-2 mb-2" style="background-color: <?php echo $theme_settings['primary_color']; ?>; color: white;">
                                                زر أساسي
                                            </button>
                                            <button class="btn me-2 mb-2" style="background-color: <?php echo $theme_settings['secondary_color']; ?>; color: white;">
                                                زر ثانوي
                                            </button>
                                            <button class="btn me-2 mb-2" style="background-color: <?php echo $theme_settings['accent_color']; ?>; color: white;">
                                                زر مميز
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-success-gradient w-100">
                                        <i class="fas fa-save me-2"></i>حفظ إعدادات المظهر
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-secondary w-100" id="resetThemeBtn">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين للافتراضي
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- تبويب النسخ الاحتياطي -->
                <div class="tab-pane fade" id="v-pills-backup" role="tabpanel">
                    <div class="settings-section">
                        <h4 class="section-title">
                            <i class="fas fa-database"></i>النسخ الاحتياطي والاستعادة
                        </h4>

                        <?php if ($system_maintenance): ?>
                            <?php echo getMaintenanceModeMessage(); ?>
                        <?php endif; ?>

                        <?php if (!$backup_enabled): ?>
                            <?php echo getClientBackupDisabledMessage($client_id); ?>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                يُنصح بعمل نسخة احتياطية من البيانات بشكل دوري لضمان عدم فقدانها
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <i class="fas fa-download fa-3x text-success mb-3"></i>
                                            <h5>تصدير البيانات</h5>
                                            <p class="text-muted">تحميل نسخة احتياطية من جميع البيانات</p>
                                            <button class="btn btn-success-gradient" onclick="exportDatabase()" <?php echo $system_maintenance ? 'disabled' : ''; ?>>
                                                <i class="fas fa-download me-2"></i>تصدير البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <i class="fas fa-upload fa-3x text-warning mb-3"></i>
                                            <h5>استيراد البيانات</h5>
                                            <p class="text-muted">استعادة البيانات من نسخة احتياطية</p>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-warning" onclick="showImportModal()" <?php echo $system_maintenance ? 'disabled' : ''; ?>>
                                                    <i class="fas fa-upload me-2"></i>استيراد عادي
                                                </button>
                                                <a href="../improved_import.php" class="btn btn-success btn-sm" <?php echo $system_maintenance ? 'style="pointer-events: none; opacity: 0.5;"' : ''; ?>>
                                                    <i class="fas fa-rocket me-1"></i>استيراد محسن
                                                </a>
                                                <a href="../diagnose_import_issues.php" class="btn btn-info btn-sm" <?php echo $system_maintenance ? 'style="pointer-events: none; opacity: 0.5;"' : ''; ?>>
                                                    <i class="fas fa-stethoscope me-1"></i>تشخيص المشاكل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal استيراد البيانات -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">استيراد البيانات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيؤدي استيراد البيانات إلى استبدال البيانات الحالية. تأكد من عمل نسخة احتياطية أولاً.
                </div>
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية (SQL)</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                        <div class="form-text">الحد الأقصى لحجم الملف: 50 ميجابايت</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="importDatabase()">
                    <i class="fas fa-upload me-2"></i>استيراد البيانات
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// دالة تصدير قاعدة البيانات
function exportDatabase() {
    if (confirm('هل تريد تصدير نسخة احتياطية من البيانات؟')) {
        showLoading('جاري إنشاء النسخة الاحتياطية...');

        // إنشاء نموذج مخفي للتحميل
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'backup_handler.php';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'export';

        form.appendChild(actionInput);
        document.body.appendChild(form);
        form.submit();

        // إخفاء مؤشر التحميل بعد ثانيتين
        setTimeout(() => {
            hideLoading();
            showNotification('تم بدء تحميل النسخة الاحتياطية', 'success');
        }, 2000);
    }
}

// دالة عرض نافذة الاستيراد
function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// دالة استيراد قاعدة البيانات
function importDatabase() {
    const fileInput = document.getElementById('backup_file');
    const file = fileInput.files[0];

    if (!file) {
        alert('يرجى اختيار ملف النسخة الاحتياطية');
        return;
    }

    if (file.size > 50 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
        return;
    }

    if (!confirm('هل أنت متأكد من استيراد هذه البيانات؟ سيتم استبدال البيانات الحالية.')) {
        return;
    }

    showLoading('جاري استيراد البيانات...');

    const formData = new FormData();
    formData.append('action', 'import');
    formData.append('backup_file', file);

    fetch('backup_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showNotification(data.message, 'success');
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            modal.hide();
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification('خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showNotification('حدث خطأ أثناء استيراد البيانات', 'error');
        console.error('Error:', error);
    });
}

// دوال مساعدة للتحميل والإشعارات
function showLoading(message) {
    // يمكن تحسين هذه الدالة لعرض مؤشر تحميل أفضل
    console.log(message);
}

function hideLoading() {
    // يمكن تحسين هذه الدالة لإخفاء مؤشر التحميل
    console.log('Loading hidden');
}

function showNotification(message, type) {
    // يمكن تحسين هذه الدالة لعرض إشعارات أفضل
    if (type === 'success') {
        alert('✅ ' + message);
    } else {
        alert('❌ ' + message);
    }
}
</script>

<script>
// تحديث الـ header فوراً عند حفظ البيانات
document.addEventListener('DOMContentLoaded', function() {
    const businessForm = document.querySelector('form[action=""][method="POST"]');

    if (businessForm) {
        businessForm.addEventListener('submit', function(e) {
            // السماح للنموذج بالإرسال أولاً
            setTimeout(function() {
                // تحديث الـ header بعد ثانية واحدة من الإرسال
                if (typeof updateHeaderInfo === 'function') {
                    updateHeaderInfo();
                }
            }, 1000);
        });
    }

    // تحديث فوري عند تحميل صفحة الإعدادات
    setTimeout(function() {
        if (typeof updateHeaderInfo === 'function') {
            updateHeaderInfo();
        }
    }, 500);
});

// تحديث الـ header عند تغيير اسم المحل في الوقت الفعلي
document.addEventListener('DOMContentLoaded', function() {
    const businessNameInput = document.getElementById('business_name');
    const ownerNameInput = document.getElementById('owner_name');

    if (businessNameInput) {
        businessNameInput.addEventListener('input', function() {
            // تحديث فوري في الـ navbar (معاينة)
            const navbarBrand = document.querySelector('.navbar-brand');
            if (navbarBrand && this.value.trim()) {
                navbarBrand.innerHTML = '<i class="fas fa-gamepad me-2"></i>' + this.value.trim();
            }
        });
    }

    if (ownerNameInput) {
        ownerNameInput.addEventListener('input', function() {
            // تحديث فوري لاسم المستخدم (معاينة)
            const userNameElements = document.querySelectorAll('.fw-bold');
            userNameElements.forEach(element => {
                if (element.textContent.trim() && this.value.trim()) {
                    element.textContent = this.value.trim();
                }
            });

            // تحديث الأحرف الأولى في الأفاتار
            if (this.value.trim()) {
                const avatarElements = document.querySelectorAll('.user-avatar');
                avatarElements.forEach(element => {
                    element.textContent = this.value.trim().charAt(0).toUpperCase();
                });
            }
        });
    }
});

// JavaScript لمعاينة الألوان في الوقت الفعلي
document.addEventListener('DOMContentLoaded', function() {
    const primaryColorInput = document.getElementById('primary_color');
    const secondaryColorInput = document.getElementById('secondary_color');
    const accentColorInput = document.getElementById('accent_color');
    const headerStyleSelect = document.getElementById('header_style');
    const sidebarPositionSelect = document.getElementById('sidebar_position');

    // تحديث معاينة الألوان
    function updateColorPreview() {
        const primaryColor = primaryColorInput.value;
        const secondaryColor = secondaryColorInput.value;
        const accentColor = accentColorInput.value;

        // تحديث المعاينة
        const previewHeader = document.querySelector('.preview-header');
        const primaryBtn = document.querySelector('.preview-content .btn:nth-child(1)');
        const secondaryBtn = document.querySelector('.preview-content .btn:nth-child(2)');
        const accentBtn = document.querySelector('.preview-content .btn:nth-child(3)');

        if (previewHeader) previewHeader.style.backgroundColor = primaryColor;
        if (primaryBtn) primaryBtn.style.backgroundColor = primaryColor;
        if (secondaryBtn) secondaryBtn.style.backgroundColor = secondaryColor;
        if (accentBtn) accentBtn.style.backgroundColor = accentColor;

        // تحديث قيم النص
        const primaryTextInput = primaryColorInput.nextElementSibling;
        const secondaryTextInput = secondaryColorInput.nextElementSibling;
        const accentTextInput = accentColorInput.nextElementSibling;

        if (primaryTextInput) primaryTextInput.value = primaryColor;
        if (secondaryTextInput) secondaryTextInput.value = secondaryColor;
        if (accentTextInput) accentTextInput.value = accentColor;
    }

    // إضافة مستمعي الأحداث
    if (primaryColorInput) {
        primaryColorInput.addEventListener('input', updateColorPreview);
    }
    if (secondaryColorInput) {
        secondaryColorInput.addEventListener('input', updateColorPreview);
    }
    if (accentColorInput) {
        accentColorInput.addEventListener('input', updateColorPreview);
    }

    // تحديث حالة حقل موضع القائمة الجانبية
    function updateSidebarPositionState() {
        const headerStyle = headerStyleSelect.value;
        const sidebarPositionDiv = sidebarPositionSelect.closest('.col-md-6');

        if (headerStyle === 'sidebar') {
            sidebarPositionDiv.style.opacity = '1';
            sidebarPositionSelect.disabled = false;
        } else {
            sidebarPositionDiv.style.opacity = '0.5';
            sidebarPositionSelect.disabled = true;
        }
    }

    if (headerStyleSelect) {
        headerStyleSelect.addEventListener('change', updateSidebarPositionState);
        updateSidebarPositionState(); // تحديث الحالة الأولية
    }

    // معاينة فورية للتغييرات على الهيدر الحالي
    function previewHeaderChanges() {
        const primaryColor = primaryColorInput.value;
        const currentNavbar = document.querySelector('.navbar');

        if (currentNavbar && primaryColor) {
            // حفظ اللون الأصلي
            if (!currentNavbar.dataset.originalColor) {
                currentNavbar.dataset.originalColor = getComputedStyle(currentNavbar).backgroundColor;
            }

            // تطبيق اللون الجديد مؤقتاً
            currentNavbar.style.backgroundColor = primaryColor;
        }
    }

    // معالجة الألوان المحددة مسبقاً
    const presetColorButtons = document.querySelectorAll('.preset-color');
    presetColorButtons.forEach(button => {
        button.addEventListener('click', function() {
            const primaryColor = this.dataset.primary;
            const secondaryColor = this.dataset.secondary;
            const accentColor = this.dataset.accent;

            // تحديث قيم الألوان
            if (primaryColorInput) primaryColorInput.value = primaryColor;
            if (secondaryColorInput) secondaryColorInput.value = secondaryColor;
            if (accentColorInput) accentColorInput.value = accentColor;

            // تحديث المعاينة
            updateColorPreview();

            // إظهار علامة الاختيار على الزر المحدد
            presetColorButtons.forEach(btn => {
                const checkIcon = btn.querySelector('.fas.fa-check');
                if (checkIcon) checkIcon.style.display = 'none';
            });

            const selectedCheckIcon = this.querySelector('.fas.fa-check');
            if (selectedCheckIcon) selectedCheckIcon.style.display = 'inline';

            // إضافة تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // زر إعادة تعيين الإعدادات للافتراضي
    const resetThemeBtn = document.getElementById('resetThemeBtn');
    if (resetThemeBtn) {
        resetThemeBtn.addEventListener('click', function() {
            if (confirm('هل تريد إعادة تعيين جميع إعدادات المظهر إلى الافتراضية؟')) {
                // الألوان الافتراضية
                if (primaryColorInput) primaryColorInput.value = '#0d6efd';
                if (secondaryColorInput) secondaryColorInput.value = '#6c757d';
                if (accentColorInput) accentColorInput.value = '#20c997';
                if (headerStyleSelect) headerStyleSelect.value = 'top';
                if (sidebarPositionSelect) sidebarPositionSelect.value = 'right';

                const themeModeSelect = document.getElementById('theme_mode');
                if (themeModeSelect) themeModeSelect.value = 'light';

                // تحديث المعاينة
                updateColorPreview();
                updateSidebarPositionState();

                // إزالة علامات الاختيار من الألوان المحددة مسبقاً
                presetColorButtons.forEach(btn => {
                    const checkIcon = btn.querySelector('.fas.fa-check');
                    if (checkIcon) checkIcon.style.display = 'none';
                });

                // إظهار علامة الاختيار على اللون الافتراضي (الأول)
                if (presetColorButtons.length > 0) {
                    const firstCheckIcon = presetColorButtons[0].querySelector('.fas.fa-check');
                    if (firstCheckIcon) firstCheckIcon.style.display = 'inline';
                }
            }
        });
    }

    // إضافة معاينة فورية (اختيارية)
    if (primaryColorInput) {
        primaryColorInput.addEventListener('input', function() {
            updateColorPreview();
            // يمكن تفعيل هذا للمعاينة الفورية على الهيدر الحقيقي
            // previewHeaderChanges();
        });
    }

    // استعادة اللون الأصلي عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        const currentNavbar = document.querySelector('.navbar');
        if (currentNavbar && currentNavbar.dataset.originalColor) {
            currentNavbar.style.backgroundColor = currentNavbar.dataset.originalColor;
        }
    });
});
</script>

<!-- تضمين ملف JavaScript لتخصيص المظهر -->
<script src="assets/js/theme-customizer.js"></script>

<?php require_once 'includes/footer.php'; ?>