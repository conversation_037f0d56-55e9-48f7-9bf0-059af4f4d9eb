<?php
/**
 * سكريپت إعداد النظام التجريبي - PlayGood
 * يقوم بإنشاء الجداول وإعداد النظام للاستخدام
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد النظام التجريبي - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap' rel='stylesheet'>
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .setup-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            max-width: 800px; 
            width: 100%;
            margin: 20px;
        }
        .setup-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 2rem; 
            border-radius: 20px 20px 0 0; 
            text-align: center;
        }
        .setup-body { 
            padding: 2rem; 
        }
        .step { 
            padding: 1rem; 
            margin: 1rem 0; 
            border-radius: 10px; 
            border-left: 4px solid #667eea;
        }
        .step.success { 
            background: #d4edda; 
            border-color: #28a745; 
        }
        .step.error { 
            background: #f8d7da; 
            border-color: #dc3545; 
        }
        .step.info { 
            background: #d1ecf1; 
            border-color: #17a2b8; 
        }
    </style>
</head>
<body>
    <div class='setup-container'>
        <div class='setup-header'>
            <h1><i class='fas fa-cog me-2'></i>إعداد النظام التجريبي</h1>
            <p>PlayGood Trial System Setup</p>
        </div>
        <div class='setup-body'>";

$steps = [];
$errors = [];

try {
    // الخطوة 1: التحقق من الاتصال بقاعدة البيانات
    echo "<div class='step info'>
            <h5><i class='fas fa-database me-2'></i>الخطوة 1: التحقق من الاتصال بقاعدة البيانات</h5>";
    
    if ($pdo) {
        echo "<p class='text-success mb-0'><i class='fas fa-check me-2'></i>تم الاتصال بقاعدة البيانات بنجاح</p>";
        $steps[] = "✅ الاتصال بقاعدة البيانات";
    } else {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    echo "</div>";

    // الخطوة 2: إنشاء مجلد السجلات
    echo "<div class='step info'>
            <h5><i class='fas fa-folder me-2'></i>الخطوة 2: إنشاء مجلد السجلات</h5>";
    
    if (!is_dir('logs')) {
        if (mkdir('logs', 0755, true)) {
            echo "<p class='text-success mb-0'><i class='fas fa-check me-2'></i>تم إنشاء مجلد logs بنجاح</p>";
        } else {
            echo "<p class='text-warning mb-0'><i class='fas fa-exclamation-triangle me-2'></i>تعذر إنشاء مجلد logs - يرجى إنشاؤه يدوياً</p>";
        }
    } else {
        echo "<p class='text-success mb-0'><i class='fas fa-check me-2'></i>مجلد logs موجود بالفعل</p>";
    }
    $steps[] = "✅ مجلد السجلات";
    echo "</div>";

    // الخطوة 3: إنشاء جداول النظام التجريبي
    echo "<div class='step info'>
            <h5><i class='fas fa-table me-2'></i>الخطوة 3: إنشاء جداول قاعدة البيانات</h5>";

    // جدول العملاء التجريبيين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS trial_clients (
            trial_id INT AUTO_INCREMENT PRIMARY KEY,
            business_name VARCHAR(200) NOT NULL,
            owner_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(20) NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            trial_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            trial_end DATETIME NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول trial_clients</p>";

    // جدول الأجهزة التجريبية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS trial_devices (
            device_id INT AUTO_INCREMENT PRIMARY KEY,
            trial_id INT NOT NULL,
            device_name VARCHAR(100) NOT NULL,
            device_type ENUM('PS4','PS5','Xbox','PC') NOT NULL,
            hourly_rate DECIMAL(8,2) NOT NULL DEFAULT 15.00,
            status ENUM('available','occupied','maintenance') DEFAULT 'available',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول trial_devices</p>";

    // جدول العملاء التجريبيين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS trial_customers (
            customer_id INT AUTO_INCREMENT PRIMARY KEY,
            trial_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(100) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول trial_customers</p>";

    // جدول الجلسات التجريبية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS trial_sessions (
            session_id INT AUTO_INCREMENT PRIMARY KEY,
            trial_id INT NOT NULL,
            device_id INT NOT NULL,
            customer_id INT NOT NULL,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time DATETIME NULL,
            duration_minutes INT DEFAULT 0,
            total_cost DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('active','completed','cancelled') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE,
            FOREIGN KEY (device_id) REFERENCES trial_devices(device_id) ON DELETE CASCADE,
            FOREIGN KEY (customer_id) REFERENCES trial_customers(customer_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول trial_sessions</p>";

    // جدول منتجات الكافتيريا التجريبية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS trial_cafeteria_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            trial_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            category VARCHAR(50) NOT NULL,
            description TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trial_id) REFERENCES trial_clients(trial_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول trial_cafeteria_items</p>";

    $steps[] = "✅ جداول قاعدة البيانات";
    echo "</div>";

    // الخطوة 4: التحقق من الملفات المطلوبة
    echo "<div class='step info'>
            <h5><i class='fas fa-file-code me-2'></i>الخطوة 4: التحقق من الملفات المطلوبة</h5>";

    $required_files = [
        'config/trial-config.php',
        'includes/trial-auth.php',
        'trial-register.php',
        'trial-login.php',
        'trial-dashboard.php',
        'trial-devices.php',
        'trial-customers.php',
        'trial-sessions.php',
        'trial-cafeteria.php',
        'trial-logout.php',
        'api/trial-cleanup.php'
    ];

    $missing_files = [];
    foreach ($required_files as $file) {
        if (file_exists($file)) {
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>{$file}</p>";
        } else {
            echo "<p class='text-danger'><i class='fas fa-times me-2'></i>{$file} - مفقود</p>";
            $missing_files[] = $file;
        }
    }

    if (empty($missing_files)) {
        $steps[] = "✅ جميع الملفات المطلوبة";
    } else {
        $errors[] = "ملفات مفقودة: " . implode(', ', $missing_files);
    }
    echo "</div>";

    // الخطوة 5: اختبار النظام
    echo "<div class='step info'>
            <h5><i class='fas fa-vial me-2'></i>الخطوة 5: اختبار النظام</h5>";

    // اختبار إنشاء حساب تجريبي
    $test_email = 'test_' . time() . '@example.com';
    $test_password = password_hash('test123', PASSWORD_DEFAULT);
    $trial_end = date('Y-m-d H:i:s', strtotime('+3 hours'));

    $stmt = $pdo->prepare("
        INSERT INTO trial_clients (business_name, owner_name, email, phone, password_hash, trial_end) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    if ($stmt->execute(['محل تجريبي', 'مستخدم تجريبي', $test_email, '0123456789', $test_password, $trial_end])) {
        $test_trial_id = $pdo->lastInsertId();
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء حساب تجريبي للاختبار</p>";
        
        // حذف الحساب التجريبي
        $pdo->prepare("DELETE FROM trial_clients WHERE trial_id = ?")->execute([$test_trial_id]);
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم حذف الحساب التجريبي بنجاح</p>";
        
        $steps[] = "✅ اختبار النظام";
    } else {
        $errors[] = "فشل في اختبار إنشاء حساب تجريبي";
    }
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ</h5>
            <p class='text-danger mb-0'>{$e->getMessage()}</p>
          </div>";
    $errors[] = $e->getMessage();
}

// النتيجة النهائية
echo "<div class='step " . (empty($errors) ? 'success' : 'error') . "'>
        <h4><i class='fas fa-" . (empty($errors) ? 'check-circle' : 'exclamation-triangle') . " me-2'></i>النتيجة النهائية</h4>";

if (empty($errors)) {
    echo "<p class='text-success'><strong>تم إعداد النظام التجريبي بنجاح! 🎉</strong></p>
          <h6>الخطوات المكتملة:</h6>
          <ul>";
    foreach ($steps as $step) {
        echo "<li>{$step}</li>";
    }
    echo "</ul>
          <div class='mt-3'>
            <a href='index.php' class='btn btn-primary me-2'>
                <i class='fas fa-home me-1'></i>الصفحة الرئيسية
            </a>
            <a href='trial-register.php' class='btn btn-success'>
                <i class='fas fa-rocket me-1'></i>تجربة النظام
            </a>
          </div>";
} else {
    echo "<p class='text-danger'><strong>حدثت أخطاء أثناء الإعداد:</strong></p>
          <ul>";
    foreach ($errors as $error) {
        echo "<li class='text-danger'>{$error}</li>";
    }
    echo "</ul>
          <p class='text-info'>يرجى إصلاح الأخطاء وإعادة تشغيل الإعداد.</p>
          <div class='mt-3'>
            <a href='setup-trial-system.php' class='btn btn-warning'>
                <i class='fas fa-redo me-1'></i>إعادة المحاولة
            </a>
          </div>";
}

echo "</div>
        </div>
    </div>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>
</body>
</html>";
?>
