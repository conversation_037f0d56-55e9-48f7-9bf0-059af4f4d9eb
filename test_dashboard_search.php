<?php
/**
 * اختبار البحث في لوحة التحكم
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار البحث في لوحة التحكم</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h3><i class='fas fa-search me-2'></i>اختبار البحث في لوحة التحكم</h3>
        </div>
        <div class='card-body'>
            
            <!-- محاكاة نموذج البحث من dashboard.php -->
            <div class='mb-3'>
                <label for='customer_search' class='form-label'>
                    <i class='fas fa-user me-1'></i>البحث عن عميل
                </label>
                <div class='input-group'>
                    <input type='text' class='form-control' id='customer_search' 
                           placeholder='ابحث عن عميل أو اتركه فارغاً' autocomplete='off'>
                    <button type='button' class='btn btn-outline-secondary' onclick='clearCustomer()'>
                        <i class='fas fa-times'></i>
                    </button>
                </div>
                <input type='hidden' name='customer_id' id='selected_customer_id'>
                <div id='customer_results' class='list-group mt-2' style='display: none;'></div>
            </div>
            
            <!-- معلومات التشخيص -->
            <div class='alert alert-info'>
                <h6><i class='fas fa-info-circle me-2'></i>معلومات التشخيص:</h6>
                <div id='debug_info'></div>
            </div>
            
            <!-- اختبار مباشر لـ API -->
            <div class='card bg-light'>
                <div class='card-body'>
                    <h6>اختبار API مباشر:</h6>
                    <div class='row'>
                        <div class='col-md-8'>
                            <input type='text' id='direct_search' class='form-control' placeholder='اختبار مباشر...'>
                        </div>
                        <div class='col-md-4'>
                            <button onclick='testDirectAPI()' class='btn btn-success w-100'>اختبار API</button>
                        </div>
                    </div>
                    <div id='api_results' class='mt-3'></div>
                </div>
            </div>
            
            <div class='text-center mt-4'>
                <a href='client/dashboard.php' class='btn btn-primary'>الذهاب للوحة التحكم الفعلية</a>
            </div>
        </div>
    </div>
</div>

<script>
// نسخ نفس الكود من dashboard.php
let customerSearchTimeout;

document.getElementById('customer_search').addEventListener('input', function() {
    const query = this.value.trim();
    
    // تحديث معلومات التشخيص
    updateDebugInfo('تم كتابة: \"' + query + '\"');
    
    clearTimeout(customerSearchTimeout);
    
    if (query.length < 2) {
        document.getElementById('customer_results').style.display = 'none';
        updateDebugInfo('النص قصير جداً (أقل من حرفين)');
        return;
    }
    
    updateDebugInfo('انتظار 300ms قبل البحث...');
    
    customerSearchTimeout = setTimeout(() => {
        searchCustomers(query);
    }, 300);
});

function searchCustomers(query) {
    const resultsDiv = document.getElementById('customer_results');
    
    updateDebugInfo('بدء البحث عن: \"' + query + '\"');
    
    // إظهار مؤشر التحميل
    resultsDiv.innerHTML = '<div class=\"list-group-item text-muted\"><i class=\"fas fa-spinner fa-spin me-2\"></i>جاري البحث...</div>';
    resultsDiv.style.display = 'block';
    
    const apiUrl = 'client/api/search-customers.php?q=' + encodeURIComponent(query);
    updateDebugInfo('استدعاء API: ' + apiUrl);

    fetch(apiUrl)
        .then(response => {
            updateDebugInfo('استجابة API - Status: ' + response.status);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: \${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            updateDebugInfo('البيانات المستلمة: ' + text.substring(0, 200) + (text.length > 200 ? '...' : ''));
            
            try {
                const customers = JSON.parse(text);
                
                if (Array.isArray(customers)) {
                    updateDebugInfo('تم تحليل JSON بنجاح - عدد النتائج: ' + customers.length);
                    
                    if (customers.length === 0) {
                        resultsDiv.innerHTML = '<div class=\"list-group-item text-muted\">لا توجد نتائج</div>';
                    } else {
                        resultsDiv.innerHTML = customers.map(customer =>
                            '<button type="button" class="list-group-item list-group-item-action"' +
                                    ' onclick="selectCustomer(' + customer.id + ', \'' + customer.name + '\', \'' + (customer.phone || '') + '\')">' +
                                '<div class="d-flex justify-content-between align-items-center">' +
                                    '<div>' +
                                        '<strong>' + customer.name + '</strong>' +
                                        (customer.phone ? '<br><small class="text-muted">' + customer.phone + '</small>' : '') +
                                    '</div>' +
                                    '<i class="fas fa-user-plus text-primary"></i>' +
                                '</div>' +
                            '</button>'
                        ).join('');
                    }
                } else {
                    updateDebugInfo('خطأ: البيانات ليست مصفوفة');
                    resultsDiv.innerHTML = '<div class=\"list-group-item text-danger\">خطأ في الاستجابة</div>';
                }
                
                resultsDiv.style.display = 'block';
            } catch (error) {
                updateDebugInfo('خطأ في تحليل JSON: ' + error.message);
                console.error('خطأ في تحليل JSON:', error);
                resultsDiv.innerHTML = '<div class=\"list-group-item text-danger\">خطأ في تحليل البيانات</div>';
                resultsDiv.style.display = 'block';
            }
        })
        .catch(error => {
            updateDebugInfo('خطأ في الشبكة: ' + error.message);
            console.error('خطأ في البحث عن العملاء:', error);
            const resultsDiv = document.getElementById('customer_results');
            resultsDiv.innerHTML = '<div class=\"list-group-item text-danger\">خطأ في الاتصال<br><small>' + error.message + '</small></div>';
            resultsDiv.style.display = 'block';
        });
}

function selectCustomer(id, name, phone) {
    document.getElementById('selected_customer_id').value = id;
    document.getElementById('customer_search').value = name + (phone ? ' - ' + phone : '');
    document.getElementById('customer_results').style.display = 'none';
    updateDebugInfo('تم اختيار العميل: ' + name + ' (ID: ' + id + ')');
}

function clearCustomer() {
    document.getElementById('selected_customer_id').value = '';
    document.getElementById('customer_search').value = '';
    document.getElementById('customer_results').style.display = 'none';
    updateDebugInfo('تم مسح اختيار العميل');
}

function updateDebugInfo(message) {
    const debugDiv = document.getElementById('debug_info');
    const timestamp = new Date().toLocaleTimeString();
    debugDiv.innerHTML = '<small>[' + timestamp + '] ' + message + '</small><br>' + debugDiv.innerHTML;
}

function testDirectAPI() {
    const query = document.getElementById('direct_search').value.trim();
    const resultsDiv = document.getElementById('api_results');
    
    if (!query) {
        resultsDiv.innerHTML = '<div class=\"alert alert-warning\">يرجى إدخال نص للبحث</div>';
        return;
    }
    
    resultsDiv.innerHTML = '<div class=\"alert alert-info\">جاري الاختبار...</div>';
    
    fetch('client/api/search-customers.php?q=' + encodeURIComponent(query))
        .then(response => response.text())
        .then(text => {
            resultsDiv.innerHTML = `
                <div class=\"alert alert-success\">
                    <h6>نتيجة API:</h6>
                    <pre style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;\">\${text}</pre>
                </div>
            `;
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class=\"alert alert-danger\">خطأ: \${error.message}</div>`;
        });
}

// إخفاء نتائج البحث عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('#customer_search') && !e.target.closest('#customer_results')) {
        document.getElementById('customer_results').style.display = 'none';
    }
});

// رسالة ترحيب
updateDebugInfo('تم تحميل الصفحة - جاهز للاختبار');
</script>

</body>
</html>";
?>
