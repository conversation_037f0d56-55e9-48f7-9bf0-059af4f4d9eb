-- إنشاء نظام صلاحيات الإدمن - إصدار مبسط
-- تشغيل هذا الملف لإضافة نظام التحكم في صلاحيات الوصول لصفحات لوحة تحكم الإدمن

-- 1. إن<PERSON><PERSON>ء جدول الصفحات المتاحة للإدمن
CREATE TABLE IF NOT EXISTS admin_pages (
    page_id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) NOT NULL UNIQUE,
    page_label VARCHAR(200) NOT NULL,
    page_url VARCHAR(255) NOT NULL,
    page_icon VARCHAR(50) DEFAULT 'fas fa-file',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE COMMENT 'هل هذه الصفحة متاحة افتراضياً للمديرين الجدد',
    required_role ENUM('super_admin', 'admin', 'any') DEFAULT 'any' COMMENT 'الدور المطلوب للوصول للصفحة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول صلاحيات الصفحات للإدمن
CREATE TABLE IF NOT EXISTS admin_page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    page_id INT NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    granted_by INT DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES admin_pages(page_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES admins(admin_id) ON DELETE SET NULL,
    UNIQUE KEY unique_admin_page (admin_id, page_id)
);

-- 3. إدراج الصفحات الأساسية للإدمن
INSERT IGNORE INTO admin_pages (page_name, page_label, page_url, page_icon, category, description, is_default, required_role) VALUES
-- الصفحات الأساسية
('dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-dashboard', 'main', 'لوحة التحكم الرئيسية مع الإحصائيات', TRUE, 'any'),
('profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة الملف الشخصي للإدمن', TRUE, 'any'),

-- إدارة العملاء
('clients', 'إدارة العملاء', 'clients.php', 'fas fa-users', 'clients', 'عرض وإدارة قائمة العملاء', TRUE, 'any'),
('client_permissions', 'صلاحيات العملاء', 'client_permissions.php', 'fas fa-user-shield', 'clients', 'إدارة صلاحيات الوصول للعملاء', TRUE, 'admin'),
('client_devices', 'أجهزة العملاء', 'client_devices.php', 'fas fa-desktop', 'clients', 'إدارة أجهزة العملاء', TRUE, 'any'),

-- التقارير والإحصائيات
('reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'عرض التقارير والإحصائيات المفصلة', TRUE, 'any'),

-- الإعدادات والإدارة
('settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام العامة', TRUE, 'admin'),
('admin_permissions', 'صلاحيات الإدمن', 'admin_permissions.php', 'fas fa-user-cog', 'admin', 'إدارة صلاحيات المديرين', FALSE, 'super_admin'),
('system_logs', 'سجلات النظام', 'system_logs.php', 'fas fa-file-alt', 'admin', 'عرض سجلات النظام والأنشطة', FALSE, 'super_admin'),

-- النسخ الاحتياطية
('backup', 'النسخ الاحتياطية', 'backup.php', 'fas fa-download', 'system', 'إنشاء وإدارة النسخ الاحتياطية', FALSE, 'admin'),

-- إدارة المديرين
('admins', 'إدارة المديرين', 'admins.php', 'fas fa-user-tie', 'admin', 'إضافة وإدارة المديرين', FALSE, 'super_admin');

-- 4. إنشاء view لعرض صلاحيات الإدمن بشكل مفصل
CREATE OR REPLACE VIEW admin_page_permissions_detailed AS
SELECT 
    a.admin_id,
    a.username,
    a.full_name,
    a.role,
    a.is_active as admin_active,
    ap.page_id,
    ap.page_name,
    ap.page_label,
    ap.page_url,
    ap.page_icon,
    ap.category,
    ap.description,
    ap.is_default,
    ap.required_role,
    CASE 
        WHEN a.role = 'super_admin' THEN TRUE
        WHEN ap.required_role = 'super_admin' AND a.role != 'super_admin' THEN FALSE
        WHEN ap.required_role = 'admin' AND a.role NOT IN ('super_admin', 'admin') THEN FALSE
        ELSE COALESCE(app.is_enabled, ap.is_default)
    END as has_permission,
    app.granted_at,
    app.updated_at
FROM admins a
CROSS JOIN admin_pages ap
LEFT JOIN admin_page_permissions app ON a.admin_id = app.admin_id AND ap.page_id = app.page_id
WHERE ap.is_active = 1 AND a.is_active = 1
ORDER BY a.admin_id, ap.category, ap.page_label;

-- 5. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_admin_pages_name ON admin_pages(page_name);
CREATE INDEX IF NOT EXISTS idx_admin_pages_category ON admin_pages(category);
CREATE INDEX IF NOT EXISTS idx_admin_pages_active ON admin_pages(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_page_permissions_admin ON admin_page_permissions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_page_permissions_page ON admin_page_permissions(page_id);

-- 6. منح الصلاحيات للمديرين الحاليين
INSERT IGNORE INTO admin_page_permissions (admin_id, page_id, is_enabled)
SELECT a.admin_id, ap.page_id, TRUE
FROM admins a
CROSS JOIN admin_pages ap
WHERE a.is_active = 1 
AND ap.is_active = 1
AND (
    a.role = 'super_admin' OR 
    (ap.is_default = TRUE AND (ap.required_role = 'any' OR ap.required_role = 'admin'))
);

-- 7. إضافة تعليقات للجداول
ALTER TABLE admin_pages COMMENT = 'جدول الصفحات المتاحة في لوحة تحكم الإدمن';
ALTER TABLE admin_page_permissions COMMENT = 'جدول صلاحيات الوصول لصفحات الإدمن';

-- عرض النتائج
SELECT 'تم إنشاء نظام صلاحيات الإدمن بنجاح!' as message;
SELECT COUNT(*) as total_pages FROM admin_pages WHERE is_active = 1;
SELECT COUNT(*) as total_permissions FROM admin_page_permissions;
SELECT username, full_name, role, COUNT(app.id) as granted_permissions
FROM admins a
LEFT JOIN admin_page_permissions app ON a.admin_id = app.admin_id
WHERE a.is_active = 1
GROUP BY a.admin_id;
