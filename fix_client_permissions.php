<?php
/**
 * ملف إصلاح سريع لمشكلة صلاحيات العملاء
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح صلاحيات العملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 800px; margin: 2rem auto; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h1 class='h3 mb-0'><i class='fas fa-tools me-2'></i>إصلاح صلاحيات العملاء</h1>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<h2>1. إنشاء الجداول المطلوبة</h2>";
    
    // إنشاء جدول client_pages
    $create_pages_table = "
        CREATE TABLE IF NOT EXISTS client_pages (
            page_id INT AUTO_INCREMENT PRIMARY KEY,
            page_name VARCHAR(100) NOT NULL UNIQUE,
            page_label VARCHAR(200) NOT NULL,
            page_url VARCHAR(255) NOT NULL,
            page_icon VARCHAR(50) DEFAULT 'fas fa-file',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            is_default BOOLEAN DEFAULT FALSE COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";
    
    $pdo->exec($create_pages_table);
    echo "<p class='success'>✅ تم إنشاء جدول client_pages</p>";
    
    // إنشاء جدول client_page_permissions
    $create_permissions_table = "
        CREATE TABLE IF NOT EXISTS client_page_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            page_id INT NOT NULL,
            is_enabled BOOLEAN DEFAULT TRUE,
            granted_by INT DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
            granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
            FOREIGN KEY (page_id) REFERENCES client_pages(page_id) ON DELETE CASCADE,
            UNIQUE KEY unique_client_page (client_id, page_id)
        )
    ";
    
    $pdo->exec($create_permissions_table);
    echo "<p class='success'>✅ تم إنشاء جدول client_page_permissions</p>";
    
    echo "<h2>2. إدراج الصفحات الافتراضية</h2>";
    
    $pages_data = [
        // الصفحات الأساسية (متاحة افتراضياً)
        ['dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', 1],
        ['profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', 1],
        ['devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', 1],
        ['sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', 1],
        ['customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', 1],
        ['reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير مالية وإحصائية', 1],
        ['invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إدارة وطباعة الفواتير', 1],
        ['settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والمحل', 1],
        
        // الصفحات الاختيارية (غير متاحة افتراضياً)
        ['rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف', 0],
        ['cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا', 0],
        ['orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات المستقلة', 0],
        ['employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة موظفي المحل', 0],
        ['attendance', 'نظام الحضور', 'attendance.php', 'fas fa-clock', 'employees', 'تسجيل حضور وانصراف الموظفين', 0],
        ['shifts', 'إدارة الورديات', 'shifts.php', 'fas fa-calendar-alt', 'employees', 'تنظيم ورديات العمل', 0],
        ['finances', 'المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة المصروفات والإيرادات', 0],
        ['inventory', 'إدارة المخزون', 'inventory.php', 'fas fa-boxes', 'inventory', 'إدارة مخزون المنتجات', 0],
        ['reservations', 'الحجوزات', 'reservations.php', 'fas fa-calendar-check', 'reservations', 'إدارة حجوزات العملاء', 0],
        ['invoice_settings', 'إعدادات الفواتير', 'invoice_settings.php', 'fas fa-file-alt', 'settings', 'تخصيص شكل ومحتوى الفواتير', 0]
    ];
    
    $insert_page_stmt = $pdo->prepare("
        INSERT IGNORE INTO client_pages (page_name, page_label, page_url, page_icon, category, description, is_default) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $inserted_count = 0;
    foreach ($pages_data as $page) {
        $result = $insert_page_stmt->execute($page);
        if ($result) {
            $inserted_count++;
        }
    }
    
    echo "<p class='success'>✅ تم إدراج $inserted_count صفحة</p>";
    
    echo "<h2>3. منح الصلاحيات الافتراضية للعملاء</h2>";
    
    // جلب جميع العملاء
    $clients_stmt = $pdo->query("SELECT client_id FROM clients WHERE is_active = 1");
    $clients = $clients_stmt->fetchAll();
    
    if (!empty($clients)) {
        $grant_permissions_stmt = $pdo->prepare("
            INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
            SELECT ?, page_id, TRUE
            FROM client_pages
            WHERE is_default = TRUE AND is_active = TRUE
        ");
        
        $granted_count = 0;
        foreach ($clients as $client) {
            $result = $grant_permissions_stmt->execute([$client['client_id']]);
            if ($result) {
                $granted_count++;
            }
        }
        
        echo "<p class='success'>✅ تم منح الصلاحيات الافتراضية لـ $granted_count عميل</p>";
    } else {
        echo "<p class='warning'>⚠️ لا يوجد عملاء نشطين في النظام</p>";
    }
    
    echo "<h2>4. التحقق من النتائج</h2>";
    
    // عدد الصفحات
    $pages_count = $pdo->query("SELECT COUNT(*) FROM client_pages")->fetchColumn();
    echo "<p class='success'>📄 إجمالي الصفحات: $pages_count</p>";
    
    // عدد الصفحات الافتراضية
    $default_pages_count = $pdo->query("SELECT COUNT(*) FROM client_pages WHERE is_default = 1")->fetchColumn();
    echo "<p class='success'>🏠 الصفحات الافتراضية: $default_pages_count</p>";
    
    // عدد الصلاحيات
    $permissions_count = $pdo->query("SELECT COUNT(*) FROM client_page_permissions")->fetchColumn();
    echo "<p class='success'>🔐 إجمالي الصلاحيات: $permissions_count</p>";
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h4>🎉 تم إصلاح النظام بنجاح!</h4>";
    echo "<p>الآن يمكن للعملاء الوصول إلى الصفحات المسموحة لهم.</p>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='diagnose_permissions.php' class='btn btn-info me-2'>اختبار النظام</a>";
    echo "<a href='admin/client_permissions.php' class='btn btn-primary me-2'>إدارة الصلاحيات</a>";
    echo "<a href='client/dashboard.php' class='btn btn-success'>لوحة تحكم العميل</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء الإصلاح</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
