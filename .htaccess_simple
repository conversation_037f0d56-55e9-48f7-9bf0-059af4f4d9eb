# Simple .htaccess for testing
RewriteEngine On

# Allow direct access to existing files and directories
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Custom Error Pages
ErrorDocument 404 /playgood/404.php
ErrorDocument 403 /playgood/403.php
ErrorDocument 500 /playgood/500.php

# Specific redirects for common pages
RewriteRule ^home$ index.php [NC,L]
RewriteRule ^dashboard$ client/dashboard.php [NC,L]
RewriteRule ^login$ client/login.php [NC,L]
RewriteRule ^register$ register.php [NC,L]
RewriteRule ^admin$ admin/login.php [NC,L]
