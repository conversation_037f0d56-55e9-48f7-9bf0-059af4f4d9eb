
// دالة محسنة لتحميل المنتجات في modal التعديل
function loadEditSessionProducts(sessionId) {
    console.log('Loading products for session:', sessionId);
    
    if (!sessionId) {
        console.error('Session ID is required');
        return;
    }
    
    const productsContainer = document.getElementById('edit_session_products');
    if (!productsContainer) {
        console.error('Products container not found: edit_session_products');
        return;
    }
    
    // إظهار مؤشر التحميل
    productsContainer.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري تحميل المنتجات...</p></div>';
    
    fetch(`api/get_session_products.php?session_id=${sessionId}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    if (data.products && data.products.length > 0) {
                        // حساب إجمالي تكلفة المنتجات
                        const totalProductsCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);
                        
                        productsContainer.innerHTML = `
                            <div class="list-group">
                                ${data.products.map(product => `
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">${product.product_name}</h6>
                                            <small class="text-muted">
                                                ${product.quantity} × ${product.price} ج.م
                                            </small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="me-3 fw-bold">${product.total} ج.م</span>
                                            <button class="btn btn-danger btn-sm"
                                                    onclick="deleteEditSessionProduct(${sessionId}, ${product.product_id})"
                                                    title="حذف المنتج">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="mt-2 p-2 bg-light rounded">
                                <div class="d-flex justify-content-between">
                                    <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                    <span class="fw-bold text-primary">${totalProductsCost.toFixed(2)} ج.م</span>
                                </div>
                            </div>
                        `;
                    } else {
                        productsContainer.innerHTML = `
                            <div class="text-center p-3">
                                <i class="fas fa-coffee fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد منتجات مضافة</p>
                                <small class="text-muted">اضغط على "إضافة منتج" لإضافة منتجات للجلسة</small>
                            </div>
                        `;
                    }
                } else {
                    throw new Error(data.error || 'فشل في جلب المنتجات');
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                productsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ في تحليل البيانات:</strong> ${jsonError.message}
                        <details class="mt-2">
                            <summary>تفاصيل الخطأ</summary>
                            <pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            productsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <strong>خطأ في تحميل المنتجات:</strong> ${error.message}
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadEditSessionProducts(${sessionId})">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// التأكد من استدعاء الدالة عند فتح modal التعديل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع لحدث فتح modal التعديل
    const editModal = document.getElementById('editSessionModal');
    if (editModal) {
        editModal.addEventListener('shown.bs.modal', function() {
            const sessionId = document.getElementById('edit_session_id').value;
            if (sessionId) {
                console.log('Modal opened, loading products for session:', sessionId);
                loadEditSessionProducts(sessionId);
            }
        });
    }
});
