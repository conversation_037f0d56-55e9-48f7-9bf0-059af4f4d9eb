<?php
/**
 * ملف اختبار لوظيفة تغيير اسم المستخدم
 * يمكن استخدامه للتأكد من أن جميع الوظائف تعمل بشكل صحيح
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

// اختبار الاتصال بقاعدة البيانات
echo "<h2>اختبار وظيفة تغيير اسم المستخدم</h2>";

try {
    // اختبار 1: التحقق من هيكل جدول الموظفين
    echo "<h3>1. اختبار هيكل جدول الموظفين:</h3>";
    $stmt = $pdo->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_columns = ['id', 'client_id', 'username', 'password_hash', 'name', 'role', 'last_login'];
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            echo "✅ العمود '$col' موجود<br>";
        } else {
            echo "❌ العمود '$col' غير موجود<br>";
        }
    }
    
    // اختبار 2: التحقق من القيود الفريدة
    echo "<h3>2. اختبار القيود الفريدة:</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM employees WHERE Key_name = 'uk_employee_username_client'");
    $unique_constraint = $stmt->fetch();
    
    if ($unique_constraint) {
        echo "✅ القيد الفريد لاسم المستخدم موجود<br>";
    } else {
        echo "❌ القيد الفريد لاسم المستخدم غير موجود<br>";
        echo "<strong>يجب إنشاء القيد الفريد:</strong><br>";
        echo "<code>ALTER TABLE employees ADD CONSTRAINT uk_employee_username_client UNIQUE (client_id, username);</code><br>";
    }
    
    // اختبار 3: التحقق من وجود الملفات المطلوبة
    echo "<h3>3. اختبار وجود الملفات:</h3>";
    $required_files = [
        'change_username.php' => 'صفحة تغيير اسم المستخدم',
        'edit_employee.php' => 'صفحة تعديل الموظف',
        'employee_permissions.php' => 'صفحة صلاحيات الموظف',
        'employees.php' => 'صفحة إدارة الموظفين',
        'assets/css/permissions.css' => 'ملف الأنماط'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "✅ $description ($file) موجود<br>";
        } else {
            echo "❌ $description ($file) غير موجود<br>";
        }
    }
    
    // اختبار 4: اختبار وظيفة التحقق من اسم المستخدم
    echo "<h3>4. اختبار وظيفة التحقق من اسم المستخدم:</h3>";
    
    function validateUsername($username) {
        if (empty($username)) {
            return "اسم المستخدم فارغ";
        }
        if (strlen($username) < 3) {
            return "اسم المستخدم قصير جداً";
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            return "اسم المستخدم يحتوي على أحرف غير مسموحة";
        }
        return "صحيح";
    }
    
    $test_usernames = [
        'test123' => 'صحيح',
        'ab' => 'قصير جداً',
        'test user' => 'يحتوي على مسافة',
        'test@123' => 'يحتوي على رموز',
        '' => 'فارغ',
        'valid_username_123' => 'صحيح'
    ];
    
    foreach ($test_usernames as $username => $expected) {
        $result = validateUsername($username);
        $status = ($result === $expected || ($expected === 'صحيح' && $result === 'صحيح')) ? '✅' : '❌';
        echo "$status اسم المستخدم '$username': $result<br>";
    }
    
    // اختبار 5: عرض إحصائيات الموظفين
    echo "<h3>5. إحصائيات الموظفين الحالية:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total_employees FROM employees");
    $total = $stmt->fetch()['total_employees'];
    echo "إجمالي الموظفين: $total<br>";
    
    if ($total > 0) {
        $stmt = $pdo->query("SELECT role, COUNT(*) as count FROM employees GROUP BY role");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "توزيع الأدوار:<br>";
        foreach ($roles as $role) {
            $role_name = match($role['role']) {
                'manager' => 'مدير',
                'cashier' => 'كاشير',
                'waiter' => 'ويتر',
                'cleaner' => 'عامل نظافة',
                default => $role['role']
            };
            echo "- $role_name: {$role['count']}<br>";
        }
    }
    
    echo "<h3>✅ اكتمل الاختبار بنجاح!</h3>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من أن جميع الملفات موجودة</li>";
    echo "<li>اختبر تسجيل الدخول كعميل أو موظف مدير</li>";
    echo "<li>جرب تغيير اسم المستخدم لأحد الموظفين</li>";
    echo "<li>تأكد من أن الموظف يحتاج لتسجيل الدخول مرة أخرى</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p>الرسالة: " . $e->getMessage() . "</p>";
    echo "<p>يرجى التأكد من إعدادات قاعدة البيانات في ملف config/database.php</p>";
}

echo "<hr>";
echo "<p><a href='employees.php'>العودة لصفحة الموظفين</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

code {
    background: #e9ecef;
    padding: 5px 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
