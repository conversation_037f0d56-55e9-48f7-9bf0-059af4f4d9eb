<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

try {
    // التحقق من وجود جدول customers
    $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($table_check->rowCount() == 0) {
        echo json_encode([
            'success' => false, 
            'error' => 'جدول العملاء غير موجود',
            'customers' => []
        ]);
        exit;
    }

    // التحقق من هيكل جدول customers لتحديد اسم عمود المعرف
    $customer_id_column = 'customer_id';
    try {
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
            $customer_id_column = 'id';
        }
    } catch (PDOException $e) {
        $customer_id_column = 'customer_id'; // افتراضي
    }

    // جلب جميع العملاء للعميل الحالي
    $stmt = $pdo->prepare("
        SELECT 
            $customer_id_column as customer_id,
            $customer_id_column as id,
            name, 
            phone, 
            email,
            notes,
            created_at
        FROM customers 
        WHERE client_id = ? 
        ORDER BY name ASC
        LIMIT 1000
    ");
    
    $stmt->execute([$client_id]);
    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيف البيانات وتنسيقها
    $formatted_customers = array_map(function($customer) {
        return [
            'customer_id' => (int)$customer['customer_id'],
            'id' => (int)$customer['id'],
            'name' => htmlspecialchars($customer['name'] ?? ''),
            'phone' => htmlspecialchars($customer['phone'] ?? ''),
            'email' => htmlspecialchars($customer['email'] ?? ''),
            'notes' => htmlspecialchars($customer['notes'] ?? ''),
            'created_at' => $customer['created_at'] ?? null
        ];
    }, $customers);

    echo json_encode([
        'success' => true,
        'customers' => $formatted_customers,
        'total_count' => count($formatted_customers),
        'message' => count($formatted_customers) > 0 ? 'تم جلب العملاء بنجاح' : 'لا يوجد عملاء مسجلين'
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    error_log('خطأ في جلب العملاء: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات',
        'customers' => []
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log('خطأ عام في جلب العملاء: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ غير متوقع',
        'customers' => []
    ], JSON_UNESCAPED_UNICODE);
}
?>
