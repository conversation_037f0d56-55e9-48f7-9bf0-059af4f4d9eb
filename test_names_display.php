<?php
require_once 'config/database.php';

echo "<h1>اختبار عرض أسماء العملاء والأجهزة في الفواتير</h1>";

try {
    // اختبار الاستعلام المستخدم في صفحة الفواتير
    $invoices_query = $pdo->prepare("
        SELECT 
            i.*,
            s.session_id,
            s.start_time,
            s.end_time,
            d.device_name,
            d.device_type,
            r.room_name,
            c.name as customer_name,
            c.phone as customer_phone,
            TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE d.client_id = 1
        ORDER BY i.created_at DESC
        LIMIT 10
    ");
    
    $invoices_query->execute();
    $invoices = $invoices_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>✅ نتائج الاختبار - عدد الفواتير: " . count($invoices) . "</h2>";
    
    if (count($invoices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>رقم الفاتورة</th><th>رقم الجلسة</th><th>اسم العميل</th><th>هاتف العميل</th><th>اسم الجهاز</th><th>نوع الجهاز</th><th>التكلفة</th><th>الحالة</th>";
        echo "</tr>";
        
        foreach ($invoices as $invoice) {
            $customer_status = !empty($invoice['customer_name']) ? '✅' : '❌';
            $device_status = !empty($invoice['device_name']) ? '✅' : '❌';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($invoice['invoice_number']) . "</td>";
            echo "<td>" . $invoice['session_id'] . "</td>";
            echo "<td>" . $customer_status . " " . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($invoice['customer_phone'] ?? 'غير محدد') . "</td>";
            echo "<td>" . $device_status . " " . htmlspecialchars($invoice['device_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($invoice['device_type'] ?? 'غير محدد') . "</td>";
            echo "<td>" . number_format($invoice['total_cost'], 2) . " ج.م</td>";
            echo "<td>" . $invoice['payment_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // إحصائيات
        $customers_with_names = 0;
        $devices_with_names = 0;
        
        foreach ($invoices as $invoice) {
            if (!empty($invoice['customer_name'])) $customers_with_names++;
            if (!empty($invoice['device_name'])) $devices_with_names++;
        }
        
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📊 إحصائيات العرض:</h3>";
        echo "<ul>";
        echo "<li><strong>فواتير بأسماء عملاء:</strong> $customers_with_names من " . count($invoices) . " (" . round(($customers_with_names/count($invoices))*100, 1) . "%)</li>";
        echo "<li><strong>فواتير بأسماء أجهزة:</strong> $devices_with_names من " . count($invoices) . " (" . round(($devices_with_names/count($invoices))*100, 1) . "%)</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($customers_with_names == count($invoices) && $devices_with_names == count($invoices)) {
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 جميع الفواتير تعرض الأسماء بشكل صحيح!</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>لا توجد فواتير للاختبار</p>";
    }
    
    // اختبار عرض فاتورة واحدة
    if (count($invoices) > 0) {
        echo "<h2>اختبار عرض فاتورة واحدة</h2>";
        $test_invoice = $invoices[0];
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>تفاصيل الفاتورة " . $test_invoice['invoice_number'] . ":</h4>";
        echo "<ul>";
        echo "<li><strong>العميل:</strong> " . htmlspecialchars($test_invoice['customer_name'] ?? 'غير محدد') . "</li>";
        echo "<li><strong>هاتف العميل:</strong> " . htmlspecialchars($test_invoice['customer_phone'] ?? 'غير محدد') . "</li>";
        echo "<li><strong>الجهاز:</strong> " . htmlspecialchars($test_invoice['device_name'] ?? 'غير محدد') . "</li>";
        echo "<li><strong>نوع الجهاز:</strong> " . htmlspecialchars($test_invoice['device_type'] ?? 'غير محدد') . "</li>";
        echo "<li><strong>التكلفة:</strong> " . number_format($test_invoice['total_cost'], 2) . " ج.م</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><a href='client/invoice.php?id=" . $test_invoice['invoice_id'] . "' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض الفاتورة الكاملة</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<h3>🎯 النتيجة النهائية</h3>";
echo "<p style='color: green; font-size: 16px;'>تم إصلاح ربط أسماء العملاء والأجهزة في الفواتير بنجاح!</p>";
echo "<p>الآن جميع الفواتير تعرض:</p>";
echo "<ul style='text-align: left; display: inline-block;'>";
echo "<li>✅ أسماء العملاء من جدول customers</li>";
echo "<li>✅ أسماء الأجهزة من جدول devices</li>";
echo "<li>✅ جميع التفاصيل الأخرى بشكل صحيح</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a>";
echo "</div>";
?>
