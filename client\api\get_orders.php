<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

try {
    // معاملات البحث والتصفية
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(50, max(10, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

    // بناء شروط البحث
    $where_conditions = ["o.client_id = ?"];
    $params = [$client_id];

    if (!empty($status_filter) && in_array($status_filter, ['pending', 'completed', 'cancelled'])) {
        $where_conditions[] = "o.status = ?";
        $params[] = $status_filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(o.order_number LIKE ? OR c.name LIKE ? OR o.notes LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    if (!empty($date_from)) {
        $where_conditions[] = "DATE(o.created_at) >= ?";
        $params[] = $date_from;
    }

    if (!empty($date_to)) {
        $where_conditions[] = "DATE(o.created_at) <= ?";
        $params[] = $date_to;
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions);

    // التحقق من وجود عمود customer_id في جدول customers
    $customer_id_column = null;
    try {
        $columns_stmt = $pdo->prepare("SHOW COLUMNS FROM customers LIKE 'customer_id'");
        $columns_stmt->execute();
        if ($columns_stmt->rowCount() > 0) {
            $customer_id_column = 'customer_id';
        }
    } catch (PDOException $e) {
        // في حالة عدم وجود الجدول أو العمود
    }

    // عدد الأوردرات الكلي
    if ($customer_id_column) {
        $count_query = "
            SELECT COUNT(*) as total 
            FROM orders o 
            LEFT JOIN customers c ON o.customer_id = c.$customer_id_column 
            $where_clause
        ";
    } else {
        $count_query = "
            SELECT COUNT(*) as total 
            FROM orders o 
            $where_clause
        ";
    }

    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_orders = $count_stmt->fetch()['total'];

    // جلب الأوردرات
    if ($customer_id_column) {
        $orders_query = "
            SELECT 
                o.*,
                c.name as customer_name,
                c.phone as customer_phone,
                COUNT(oi.id) as items_count
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.$customer_id_column
            LEFT JOIN order_items oi ON o.id = oi.order_id
            $where_clause
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT $limit OFFSET $offset
        ";
    } else {
        $orders_query = "
            SELECT 
                o.*,
                NULL as customer_name,
                NULL as customer_phone,
                COUNT(oi.id) as items_count
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            $where_clause
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT $limit OFFSET $offset
        ";
    }

    $orders_stmt = $pdo->prepare($orders_query);
    $orders_stmt->execute($params);
    $orders = $orders_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنسيق البيانات
    foreach ($orders as &$order) {
        $order['created_at_formatted'] = date('Y-m-d H:i', strtotime($order['created_at']));
        $order['total_amount_formatted'] = number_format($order['total_amount'], 2);
        
        // ترجمة الحالة
        $status_translations = [
            'pending' => 'قيد الانتظار',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];
        $order['status_text'] = $status_translations[$order['status']] ?? $order['status'];
        
        // ترجمة طريقة الدفع
        $payment_translations = [
            'cash' => 'نقدي',
            'card' => 'بطاقة',
            'other' => 'أخرى'
        ];
        $order['payment_method_text'] = $payment_translations[$order['payment_method']] ?? $order['payment_method'];
    }

    echo json_encode([
        'success' => true,
        'orders' => $orders,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($total_orders / $limit),
            'total_orders' => $total_orders,
            'per_page' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء جلب الأوردرات: ' . $e->getMessage()
    ]);
}
?>
