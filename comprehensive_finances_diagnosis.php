<?php
/**
 * تشخيص شامل لمشاكل صفحة الماليات - PlayGood
 * يفحص جميع الجوانب المحتملة للمشكلة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص شامل لمشاكل صفحة الماليات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$issues_found = 0;
$fixes_suggested = [];

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    
    // 1. فحص قاعدة البيانات
    echo "<h2>1. فحص قاعدة البيانات</h2>";
    
    // فحص الجداول المطلوبة
    $required_tables = ['expense_types', 'expenses', 'income_types', 'additional_income', 'sessions', 'devices', 'customers'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
            $missing_tables[] = $table;
            $issues_found++;
        }
    }
    
    if (!empty($missing_tables)) {
        $fixes_suggested[] = "إنشاء الجداول المفقودة: " . implode(', ', $missing_tables);
    }
    
    // 2. فحص البيانات المالية
    echo "<h2>2. فحص البيانات المالية</h2>";
    
    if (!in_array('expenses', $missing_tables)) {
        // فحص المصروفات
        $expenses_check = $pdo->prepare("
            SELECT 
                COUNT(*) as total_expenses,
                COUNT(CASE WHEN amount IS NULL OR amount = '' OR amount = '0' THEN 1 END) as invalid_amounts,
                COUNT(CASE WHEN NOT amount REGEXP '^[0-9]+\.?[0-9]*$' THEN 1 END) as non_numeric_amounts
            FROM expenses 
            WHERE client_id = ?
        ");
        $expenses_check->execute([$client_id]);
        $expenses_stats = $expenses_check->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 إحصائيات المصروفات:</h4>";
        echo "<p><strong>إجمالي المصروفات:</strong> " . $expenses_stats['total_expenses'] . "</p>";
        echo "<p><strong>مبالغ غير صحيحة:</strong> " . $expenses_stats['invalid_amounts'] . "</p>";
        echo "<p><strong>مبالغ غير رقمية:</strong> " . $expenses_stats['non_numeric_amounts'] . "</p>";
        echo "</div>";
        
        if ($expenses_stats['invalid_amounts'] > 0 || $expenses_stats['non_numeric_amounts'] > 0) {
            $issues_found++;
            $fixes_suggested[] = "تشغيل fix_finances_display.php لإصلاح المبالغ غير الصحيحة";
        }
    }
    
    if (!in_array('additional_income', $missing_tables)) {
        // فحص الإيرادات
        $income_check = $pdo->prepare("
            SELECT 
                COUNT(*) as total_income,
                COUNT(CASE WHEN amount IS NULL OR amount = '' OR amount = '0' THEN 1 END) as invalid_amounts,
                COUNT(CASE WHEN NOT amount REGEXP '^[0-9]+\.?[0-9]*$' THEN 1 END) as non_numeric_amounts
            FROM additional_income 
            WHERE client_id = ?
        ");
        $income_check->execute([$client_id]);
        $income_stats = $income_check->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 إحصائيات الإيرادات الإضافية:</h4>";
        echo "<p><strong>إجمالي الإيرادات:</strong> " . $income_stats['total_income'] . "</p>";
        echo "<p><strong>مبالغ غير صحيحة:</strong> " . $income_stats['invalid_amounts'] . "</p>";
        echo "<p><strong>مبالغ غير رقمية:</strong> " . $income_stats['non_numeric_amounts'] . "</p>";
        echo "</div>";
        
        if ($income_stats['invalid_amounts'] > 0 || $income_stats['non_numeric_amounts'] > 0) {
            $issues_found++;
            $fixes_suggested[] = "تشغيل fix_finances_display.php لإصلاح مبالغ الإيرادات";
        }
    }
    
    // 3. فحص إيرادات الجلسات
    echo "<h2>3. فحص إيرادات الجلسات</h2>";
    
    if (!in_array('sessions', $missing_tables) && !in_array('devices', $missing_tables)) {
        $sessions_check = $pdo->prepare("
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_sessions,
                COUNT(CASE WHEN s.status = 'completed' AND (s.total_cost IS NULL OR s.total_cost = '' OR s.total_cost = '0') THEN 1 END) as zero_cost_sessions,
                AVG(CASE WHEN s.status = 'completed' AND s.total_cost > 0 THEN s.total_cost END) as avg_session_cost
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ?
        ");
        $sessions_check->execute([$client_id]);
        $sessions_stats = $sessions_check->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 إحصائيات الجلسات:</h4>";
        echo "<p><strong>إجمالي الجلسات:</strong> " . $sessions_stats['total_sessions'] . "</p>";
        echo "<p><strong>الجلسات المكتملة:</strong> " . $sessions_stats['completed_sessions'] . "</p>";
        echo "<p><strong>جلسات بتكلفة صفر:</strong> " . $sessions_stats['zero_cost_sessions'] . "</p>";
        echo "<p><strong>متوسط تكلفة الجلسة:</strong> " . number_format($sessions_stats['avg_session_cost'] ?? 0, 2) . " ج.م</p>";
        echo "</div>";
        
        if ($sessions_stats['zero_cost_sessions'] > 0) {
            $issues_found++;
            $fixes_suggested[] = "إعادة حساب تكاليف الجلسات الفارغة";
        }
    }
    
    // 4. فحص خلط البيانات
    echo "<h2>4. فحص خلط البيانات</h2>";
    
    if (!in_array('sessions', $missing_tables) && !in_array('devices', $missing_tables) && !in_array('customers', $missing_tables)) {
        $data_mix_check = $pdo->prepare("
            SELECT 
                COUNT(*) as total_sessions_with_customers,
                COUNT(CASE WHEN d.device_name = c.name THEN 1 END) as mixed_data_sessions
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = ? AND c.name IS NOT NULL
        ");
        $data_mix_check->execute([$client_id]);
        $mix_stats = $data_mix_check->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 فحص خلط البيانات:</h4>";
        echo "<p><strong>جلسات مع عملاء:</strong> " . $mix_stats['total_sessions_with_customers'] . "</p>";
        echo "<p><strong>جلسات بخلط البيانات:</strong> " . $mix_stats['mixed_data_sessions'] . "</p>";
        echo "</div>";
        
        if ($mix_stats['mixed_data_sessions'] > 0) {
            $issues_found++;
            $fixes_suggested[] = "تشغيل fix_data_display_mix.php لإصلاح خلط البيانات";
        }
    }
    
    // 5. فحص الملفات
    echo "<h2>5. فحص الملفات</h2>";
    
    $files_to_check = [
        'client/finances.php' => 'صفحة الماليات',
        'assets/js/main.js' => 'ملف الجافاسكريبت الرئيسي',
        'config/database.php' => 'ملف قاعدة البيانات'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ $description موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ $description غير موجود</p>";
            $issues_found++;
            $fixes_suggested[] = "إنشاء أو استعادة $description";
        }
    }
    
    // 6. فحص وظائف الجافاسكريبت
    echo "<h2>6. فحص وظائف الجافاسكريبت</h2>";
    
    if (file_exists('client/finances.php')) {
        $finances_content = file_get_contents('client/finances.php');
        
        $js_functions = [
            'formatNumber' => 'وظيفة تنسيق الأرقام',
            'number_format' => 'استخدام PHP number_format'
        ];
        
        foreach ($js_functions as $function => $description) {
            if (strpos($finances_content, $function) !== false) {
                echo "<p style='color: green;'>✅ $description موجودة</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ $description غير موجودة</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    $issues_found++;
}

// 7. ملخص التشخيص والحلول
echo "<h2>📋 ملخص التشخيص</h2>";

if ($issues_found > 0) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ تم العثور على $issues_found مشكلة</h4>";
    echo "<h5>الحلول المقترحة:</h5>";
    echo "<ol>";
    foreach ($fixes_suggested as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ لم يتم العثور على مشاكل واضحة</h4>";
    echo "<p>إذا كانت المشكلة لا تزال موجودة، قد تكون المشكلة في:</p>";
    echo "<ul>";
    echo "<li>إعدادات المتصفح أو JavaScript</li>";
    echo "<li>تداخل في ملفات CSS أو JavaScript</li>";
    echo "<li>مشكلة في عرض البيانات على الواجهة</li>";
    echo "</ul>";
    echo "</div>";
}

// 8. أدوات الإصلاح المتاحة
echo "<h2>🛠️ أدوات الإصلاح المتاحة</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>الملفات المتاحة للإصلاح:</h4>";
echo "<ul>";
echo "<li><strong>debug_finances_display.php</strong> - تشخيص تفصيلي للبيانات المالية</li>";
echo "<li><strong>fix_finances_display.php</strong> - إصلاح البيانات المالية غير الصحيحة</li>";
echo "<li><strong>fix_finances_javascript.php</strong> - إصلاح مشاكل الجافاسكريبت</li>";
echo "<li><strong>test_finances_javascript.php</strong> - اختبار وظائف الجافاسكريبت</li>";
echo "<li><strong>fix_data_display_mix.php</strong> - إصلاح خلط البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔄 الخطوات الموصى بها:</h4>";
echo "<ol>";
echo "<li>شغل debug_finances_display.php للحصول على تشخيص مفصل</li>";
echo "<li>إذا وُجدت مشاكل في البيانات، شغل fix_finances_display.php</li>";
echo "<li>إذا كانت المشكلة في الجافاسكريبت، شغل fix_finances_javascript.php</li>";
echo "<li>اختبر النتائج باستخدام test_finances_javascript.php</li>";
echo "<li>زر صفحة الماليات للتحقق من الإصلاحات</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
