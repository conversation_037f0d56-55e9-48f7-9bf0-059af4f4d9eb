<?php
/**
 * اختبار نظام التسعير للعب الفردي والزوجي - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🎮 اختبار نظام التسعير للعب الفردي والزوجي</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. عرض الأجهزة وأسعارها
    echo "<h2>1. الأجهزة المتاحة وأسعارها</h2>";
    
    $devices = $pdo->query("
        SELECT device_id, device_name, device_type, single_rate, multi_rate, status
        FROM devices 
        WHERE client_id = 1 
        ORDER BY device_id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>اسم الجهاز</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>السعر الفردي</th>";
        echo "<th style='padding: 8px;'>السعر الزوجي</th>";
        echo "<th style='padding: 8px;'>الفرق</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        foreach ($devices as $device) {
            $single_rate = $device['single_rate'] ?? 0;
            $multi_rate = $device['multi_rate'] ?? 0;
            $difference = $multi_rate - $single_rate;
            $percentage = $single_rate > 0 ? (($difference / $single_rate) * 100) : 0;
            
            $status_color = $device['status'] === 'available' ? 'green' : 
                           ($device['status'] === 'occupied' ? 'orange' : 'red');
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$device['device_id']}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td style='padding: 8px;'>{$device['device_type']}</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: blue;'>" . number_format($single_rate, 2) . " ج.م</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: red;'>" . number_format($multi_rate, 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>+" . number_format($difference, 2) . " ج.م (" . number_format($percentage, 1) . "%)</td>";
            echo "<td style='padding: 8px; color: $status_color;'>{$device['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد أجهزة في قاعدة البيانات</p>";
        echo "<p><a href='fix_device_pricing.php'>انقر هنا لإضافة أجهزة تجريبية</a></p>";
    }
    
    // 2. اختبار إنشاء جلسات تجريبية
    if (isset($_POST['create_test_session'])) {
        echo "<h2>2. إنشاء جلسة تجريبية</h2>";
        
        $device_id = intval($_POST['test_device_id']);
        $game_type = $_POST['test_game_type'];
        
        try {
            $pdo->beginTransaction();
            
            // التحقق من توفر الجهاز
            $device_check = $pdo->prepare("SELECT device_name, single_rate, multi_rate FROM devices WHERE device_id = ? AND status = 'available'");
            $device_check->execute([$device_id]);
            $device = $device_check->fetch();
            
            if ($device) {
                // إنشاء الجلسة
                $insert_session = $pdo->prepare("
                    INSERT INTO sessions (device_id, client_id, start_time, status, game_type, created_by)
                    VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?, ?)
                ");
                $insert_session->execute([$device_id, 1, $game_type, 1]);
                
                $session_id = $pdo->lastInsertId();
                
                // تحديث حالة الجهاز
                $update_device = $pdo->prepare("UPDATE devices SET status = 'occupied' WHERE device_id = ?");
                $update_device->execute([$device_id]);
                
                $pdo->commit();
                
                $rate = $game_type === 'multiplayer' ? $device['multi_rate'] : $device['single_rate'];
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ تم إنشاء الجلسة بنجاح!</h4>";
                echo "<p><strong>رقم الجلسة:</strong> $session_id</p>";
                echo "<p><strong>الجهاز:</strong> " . htmlspecialchars($device['device_name']) . "</p>";
                echo "<p><strong>نوع اللعب:</strong> " . ($game_type === 'multiplayer' ? 'زوجي' : 'فردي') . "</p>";
                echo "<p><strong>السعر:</strong> " . number_format($rate, 2) . " ج.م/ساعة</p>";
                echo "</div>";
                
            } else {
                throw new Exception("الجهاز غير متاح");
            }
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ فشل في إنشاء الجلسة</h4>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
    // 3. عرض الجلسات النشطة
    echo "<h2>3. الجلسات النشطة</h2>";
    
    $active_sessions = $pdo->query("
        SELECT s.session_id, s.start_time, s.game_type,
               d.device_name, d.single_rate, d.multi_rate,
               TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE s.status = 'active' AND d.client_id = 1
        ORDER BY s.start_time DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($active_sessions) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>رقم الجلسة</th>";
        echo "<th style='padding: 8px;'>الجهاز</th>";
        echo "<th style='padding: 8px;'>نوع اللعب</th>";
        echo "<th style='padding: 8px;'>المدة</th>";
        echo "<th style='padding: 8px;'>السعر/ساعة</th>";
        echo "<th style='padding: 8px;'>التكلفة الحالية</th>";
        echo "<th style='padding: 8px;'>إجراءات</th>";
        echo "</tr>";
        
        foreach ($active_sessions as $session) {
            $game_type = $session['game_type'] ?? 'single';
            $rate = $game_type === 'multiplayer' ? $session['multi_rate'] : $session['single_rate'];
            $duration_hours = ceil($session['duration_minutes'] / 60);
            $current_cost = $duration_hours * $rate;
            
            $game_type_text = $game_type === 'multiplayer' ? 'زوجي' : 'فردي';
            $game_type_color = $game_type === 'multiplayer' ? 'red' : 'blue';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$session['session_id']}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($session['device_name']) . "</td>";
            echo "<td style='padding: 8px; color: $game_type_color; font-weight: bold;'>$game_type_text</td>";
            echo "<td style='padding: 8px;'>" . floor($session['duration_minutes'] / 60) . ":" . sprintf("%02d", $session['duration_minutes'] % 60) . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($rate, 2) . " ج.م</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . number_format($current_cost, 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='end_session_id' value='{$session['session_id']}'>";
            echo "<button type='submit' name='end_test_session' style='background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;'>إنهاء</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد جلسات نشطة حالياً</p>";
    }
    
    // 4. نموذج إنشاء جلسة تجريبية
    $available_devices = $pdo->query("
        SELECT device_id, device_name, device_type, single_rate, multi_rate
        FROM devices 
        WHERE client_id = 1 AND status = 'available'
        ORDER BY device_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($available_devices) > 0) {
        echo "<h2>4. إنشاء جلسة تجريبية</h2>";
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label><strong>اختر الجهاز:</strong></label><br>";
        echo "<select name='test_device_id' required style='padding: 5px; margin: 5px 0;'>";
        echo "<option value=''>-- اختر جهاز --</option>";
        foreach ($available_devices as $device) {
            echo "<option value='{$device['device_id']}'>";
            echo htmlspecialchars($device['device_name']) . " ({$device['device_type']}) - ";
            echo "فردي: " . number_format($device['single_rate'], 2) . " ج.م، ";
            echo "زوجي: " . number_format($device['multi_rate'], 2) . " ج.م";
            echo "</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label><strong>نوع اللعب:</strong></label><br>";
        echo "<input type='radio' name='test_game_type' value='single' id='single' checked>";
        echo "<label for='single' style='margin-left: 10px;'>فردي</label>";
        echo "<input type='radio' name='test_game_type' value='multiplayer' id='multiplayer'>";
        echo "<label for='multiplayer'>زوجي</label>";
        echo "</div>";
        
        echo "<button type='submit' name='create_test_session' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px;'>إنشاء جلسة تجريبية</button>";
        echo "</form>";
    }
    
    // معالجة إنهاء الجلسة
    if (isset($_POST['end_test_session'])) {
        $session_id = intval($_POST['end_session_id']);
        
        try {
            $pdo->beginTransaction();
            
            // جلب معلومات الجلسة
            $session_info = $pdo->prepare("
                SELECT s.device_id, s.start_time, s.game_type,
                       d.single_rate, d.multi_rate,
                       TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                WHERE s.session_id = ? AND s.status = 'active'
            ");
            $session_info->execute([$session_id]);
            $session = $session_info->fetch();
            
            if ($session) {
                $game_type = $session['game_type'] ?? 'single';
                $rate = $game_type === 'multiplayer' ? $session['multi_rate'] : $session['single_rate'];
                $duration_hours = ceil($session['duration_minutes'] / 60);
                $total_cost = $duration_hours * $rate;
                
                // إنهاء الجلسة
                $end_session = $pdo->prepare("
                    UPDATE sessions 
                    SET end_time = CURRENT_TIMESTAMP, status = 'completed', total_cost = ?
                    WHERE session_id = ?
                ");
                $end_session->execute([$total_cost, $session_id]);
                
                // تحرير الجهاز
                $free_device = $pdo->prepare("UPDATE devices SET status = 'available' WHERE device_id = ?");
                $free_device->execute([$session['device_id']]);
                
                $pdo->commit();
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ تم إنهاء الجلسة بنجاح!</h4>";
                echo "<p><strong>رقم الجلسة:</strong> $session_id</p>";
                echo "<p><strong>نوع اللعب:</strong> " . ($game_type === 'multiplayer' ? 'زوجي' : 'فردي') . "</p>";
                echo "<p><strong>المدة:</strong> " . floor($session['duration_minutes'] / 60) . ":" . sprintf("%02d", $session['duration_minutes'] % 60) . "</p>";
                echo "<p><strong>السعر:</strong> " . number_format($rate, 2) . " ج.م/ساعة</p>";
                echo "<p><strong>التكلفة الإجمالية:</strong> " . number_format($total_cost, 2) . " ج.م</p>";
                echo "</div>";
                
                // إعادة تحميل الصفحة لتحديث البيانات
                echo "<script>setTimeout(() => location.reload(), 2000);</script>";
            }
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ فشل في إنهاء الجلسة</h4>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ حدث خطأ</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<div style='margin: 20px 0; text-align: center;'>";
echo "<a href='fix_device_pricing.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح أسعار الأجهزة</a>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>انتقل لصفحة الجلسات</a>";
echo "</div>";
?>
