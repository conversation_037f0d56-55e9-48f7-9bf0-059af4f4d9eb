<?php
/**
 * إعداد الصفحات المفقودة لنظام صلاحيات العملاء
 * PlayGood - Gaming Center Management System
 */

// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'config/database.php';

// التحقق من الصلاحيات (يجب أن يكون المستخدم admin)
if (!isset($_SESSION['admin_id'])) {
    header('Location: admin/login.php');
    exit;
}

$page_title = "إعداد الصفحات المفقودة";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .result-box { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
        .success-box { border-left-color: #28a745; }
        .error-box { border-left-color: #dc3545; }
        .warning-box { border-left-color: #ffc107; }
    </style>
</head>
<body class="bg-light">

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة الصفحات المطلوبة لنظام صلاحيات العملاء
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])): ?>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            جاري إضافة الصفحات المطلوبة...
                        </div>

                        <?php
                        try {
                            // قراءة وتنفيذ سكريبت SQL
                            $sql_content = file_get_contents('add_missing_client_pages.sql');
                            
                            if ($sql_content === false) {
                                throw new Exception('لا يمكن قراءة ملف SQL');
                            }

                            // تقسيم الاستعلامات
                            $queries = array_filter(array_map('trim', explode(';', $sql_content)));
                            
                            $success_count = 0;
                            $error_count = 0;
                            $results = [];

                            foreach ($queries as $query) {
                                if (empty($query) || strpos($query, '--') === 0) {
                                    continue;
                                }

                                try {
                                    $stmt = $pdo->prepare($query);
                                    $stmt->execute();
                                    
                                    // إذا كان الاستعلام SELECT، عرض النتائج
                                    if (stripos($query, 'SELECT') === 0) {
                                        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                        if (!empty($result)) {
                                            $results[] = $result;
                                        }
                                    }
                                    
                                    $success_count++;
                                } catch (PDOException $e) {
                                    $error_count++;
                                    echo "<div class='result-box error-box'>";
                                    echo "<strong>خطأ في الاستعلام:</strong><br>";
                                    echo "<code>" . htmlspecialchars(substr($query, 0, 100)) . "...</code><br>";
                                    echo "<small class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</small>";
                                    echo "</div>";
                                }
                            }

                            // عرض النتائج
                            if ($success_count > 0) {
                                echo "<div class='result-box success-box'>";
                                echo "<h5><i class='fas fa-check-circle text-success me-2'></i>تم تنفيذ العمليات بنجاح!</h5>";
                                echo "<p>تم تنفيذ <strong>$success_count</strong> استعلام بنجاح";
                                if ($error_count > 0) {
                                    echo " مع <strong>$error_count</strong> خطأ";
                                }
                                echo "</p>";
                                echo "</div>";
                            }

                            // عرض نتائج الاستعلامات
                            foreach ($results as $result) {
                                if (!empty($result)) {
                                    echo "<div class='result-box'>";
                                    echo "<div class='table-responsive'>";
                                    echo "<table class='table table-sm table-striped'>";
                                    
                                    // عرض الرؤوس
                                    echo "<thead class='table-dark'><tr>";
                                    foreach (array_keys($result[0]) as $header) {
                                        echo "<th>" . htmlspecialchars($header) . "</th>";
                                    }
                                    echo "</tr></thead>";
                                    
                                    // عرض البيانات
                                    echo "<tbody>";
                                    foreach ($result as $row) {
                                        echo "<tr>";
                                        foreach ($row as $value) {
                                            echo "<td>" . htmlspecialchars($value) . "</td>";
                                        }
                                        echo "</tr>";
                                    }
                                    echo "</tbody></table>";
                                    echo "</div></div>";
                                }
                            }

                            // رسالة النجاح النهائية
                            if ($error_count === 0) {
                                echo "<div class='alert alert-success mt-4'>";
                                echo "<h5><i class='fas fa-check-circle me-2'></i>تم الإعداد بنجاح!</h5>";
                                echo "<p>تم إضافة جميع الصفحات المفقودة لنظام صلاحيات العملاء.</p>";
                                echo "<p class='mb-0'>يمكنك الآن الذهاب لصفحة إدارة صلاحيات العملاء لتفعيل أو تعطيل هذه الصفحات.</p>";
                                echo "</div>";
                                
                                echo "<div class='text-center mt-4'>";
                                echo "<a href='admin/client_permissions.php' class='btn btn-primary me-2'>";
                                echo "<i class='fas fa-users-cog me-1'></i>إدارة صلاحيات العملاء";
                                echo "</a>";
                                echo "<a href='admin/dashboard.php' class='btn btn-secondary'>";
                                echo "<i class='fas fa-home me-1'></i>لوحة تحكم الإدمن";
                                echo "</a>";
                                echo "</div>";
                            }

                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>";
                            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                            echo "</div>";
                        }
                        ?>

                    <?php else: ?>
                        
                        <!-- معلومات حول العملية -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>حول هذه العملية</h5>
                            <p>ستقوم هذه العملية بإضافة الصفحات المطلوبة التالية لنظام صلاحيات العملاء:</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-shopping-cart text-primary me-1"></i>صفحات الأوردر والطلبات:</h6>
                                    <ul class="small">
                                        <li>إدارة الأوردرات</li>
                                    </ul>

                                    <h6><i class="fas fa-money-bill-wave text-success me-1"></i>صفحات الماليات:</h6>
                                    <ul class="small">
                                        <li>إدارة الماليات</li>
                                        <li>الإيرادات</li>
                                        <li>المصروفات</li>
                                    </ul>
                                </div>

                                <div class="col-md-6">
                                    <h6><i class="fas fa-user-tie text-warning me-1"></i>صفحات الموظفين:</h6>
                                    <ul class="small">
                                        <li>إدارة الموظفين</li>
                                    </ul>

                                    <h6><i class="fas fa-door-open text-info me-1"></i>صفحات الغرف:</h6>
                                    <ul class="small">
                                        <li>إدارة الغرف</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>ملاحظة مهمة:</h6>
                            <p class="mb-0">جميع الصفحات المضافة ستكون <strong>غير مفعلة افتراضياً</strong> للعملاء. يمكنك تفعيلها لاحقاً من صفحة إدارة صلاحيات العملاء حسب احتياجات كل عميل.</p>
                        </div>

                        <!-- نموذج التأكيد -->
                        <form method="POST" class="text-center">
                            <button type="submit" name="setup" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus-circle me-2"></i>
                                إضافة الصفحات المطلوبة
                            </button>
                        </form>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
