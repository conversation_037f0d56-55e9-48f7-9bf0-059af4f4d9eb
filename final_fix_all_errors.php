<?php
/**
 * سكريپت الإصلاح النهائي لجميع الأخطاء
 * يحل جميع مشاكل قاعدة البيانات والأعمدة المفقودة
 */

require_once 'config/database.php';

echo "<h1>الإصلاح النهائي الشامل - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$fixes_applied = 0;
$errors = [];

$transaction_started = false;

try {
    $pdo->beginTransaction();
    $transaction_started = true;
    
    echo "<h2>1. إصلاح جدول customers</h2>";
    
    // التحقق من وجود جدول customers
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول customers غير موجود - جاري الإنشاء...</p>";
        
        $create_customers = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                notes TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customers_client_id (client_id),
                INDEX idx_customers_phone (phone)
            )
        ";
        
        $pdo->exec($create_customers);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
        $fixes_applied++;
        
        // إضافة بيانات تجريبية
        $sample_customers = [
            ['أحمد محمد علي', '01234567890', '<EMAIL>'],
            ['فاطمة حسن', '01234567891', '<EMAIL>'],
            ['محمد أحمد', '01234567892', '<EMAIL>'],
            ['سارة علي', '01234567893', '<EMAIL>'],
            ['عمر خالد', '01234567894', '<EMAIL>']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, 1)");
        foreach ($sample_customers as $customer) {
            $insert_stmt->execute($customer);
        }
        echo "<p style='color: green;'>✅ تم إضافة 5 عملاء تجريبيين</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: green;'>✅ جدول customers موجود</p>";
        
        // فحص الأعمدة
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('customer_id', $columns) && in_array('id', $columns)) {
            echo "<p style='color: blue;'>ℹ️ الجدول يستخدم عمود 'id' بدلاً من 'customer_id'</p>";
        }
        
        if (!in_array('client_id', $columns)) {
            echo "<p style='color: orange;'>⚠️ عمود client_id غير موجود - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE customers ADD COLUMN client_id INT NOT NULL DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
            $fixes_applied++;
        }
    }
    
    echo "<h2>2. إصلاح جدول sessions</h2>";
    
    // فحص أعمدة sessions
    $stmt = $pdo->query("DESCRIBE sessions");
    $session_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_session_columns = [
        'client_id' => 'INT NULL',
        'total_cost' => 'DECIMAL(10,2) DEFAULT 0.00',
        'customer_id' => 'INT NULL',
        'created_by' => 'INT NULL',
        'updated_by' => 'INT NULL'
    ];
    
    foreach ($required_session_columns as $column => $definition) {
        if (!in_array($column, $session_columns)) {
            echo "<p style='color: orange;'>⚠️ عمود sessions.$column غير موجود - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE sessions ADD COLUMN $column $definition");
            echo "<p style='color: green;'>✅ تم إضافة عمود sessions.$column</p>";
            $fixes_applied++;
        }
    }
    
    // تحديث client_id في sessions
    $pdo->exec("UPDATE sessions s 
               JOIN devices d ON s.device_id = d.device_id 
               SET s.client_id = d.client_id 
               WHERE s.client_id IS NULL OR s.client_id = 0");
    echo "<p style='color: green;'>✅ تم تحديث معرفات العملاء في الجلسات</p>";
    $fixes_applied++;
    
    echo "<h2>3. إصلاح جدول rooms</h2>";

    // التحقق من وجود جدول rooms
    $stmt = $pdo->query("SHOW TABLES LIKE 'rooms'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول rooms غير موجود - جاري الإنشاء...</p>";

        $create_rooms = "
            CREATE TABLE rooms (
                room_id INT AUTO_INCREMENT PRIMARY KEY,
                room_name VARCHAR(100) NOT NULL,
                description TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_rooms_client_id (client_id),
                INDEX idx_rooms_name (room_name)
            )
        ";

        $pdo->exec($create_rooms);
        echo "<p style='color: green;'>✅ تم إنشاء جدول rooms</p>";
        $fixes_applied++;

        // إضافة غرف تجريبية
        $sample_rooms = [
            ['الصالة الرئيسية', 'صالة الألعاب الرئيسية'],
            ['غرفة VIP', 'غرفة خاصة للعملاء المميزين'],
            ['قسم البلايستيشن', 'منطقة ألعاب البلايستيشن'],
            ['قسم الكمبيوتر', 'منطقة ألعاب الكمبيوتر']
        ];

        $insert_stmt = $pdo->prepare("INSERT INTO rooms (room_name, description, client_id) VALUES (?, ?, 1)");
        foreach ($sample_rooms as $room) {
            $insert_stmt->execute($room);
        }
        echo "<p style='color: green;'>✅ تم إضافة 4 غرف تجريبية</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: green;'>✅ جدول rooms موجود</p>";

        // فحص الأعمدة المطلوبة
        $stmt = $pdo->query("DESCRIBE rooms");
        $rooms_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $required_rooms_columns = [
            'client_id' => 'INT NOT NULL DEFAULT 1',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];

        foreach ($required_rooms_columns as $column => $definition) {
            if (!in_array($column, $rooms_columns)) {
                echo "<p style='color: orange;'>⚠️ عمود rooms.$column غير موجود - جاري الإضافة...</p>";
                $pdo->exec("ALTER TABLE rooms ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✅ تم إضافة عمود rooms.$column</p>";
                $fixes_applied++;
            }
        }
    }

    echo "<h2>4. إصلاح جدول cafeteria_items</h2>";

    try {
        $stmt = $pdo->query("DESCRIBE cafeteria_items");
        $cafeteria_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('client_id', $cafeteria_columns)) {
            echo "<p style='color: orange;'>⚠️ عمود client_id غير موجود في cafeteria_items - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE cafeteria_items ADD COLUMN client_id INT NOT NULL DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود client_id لجدول cafeteria_items</p>";
            $fixes_applied++;
        }
    } catch (PDOException $e) {
        echo "<p style='color: blue;'>ℹ️ جدول cafeteria_items غير موجود (اختياري)</p>";
    }
    
    echo "<h2>5. إنشاء الجداول المساعدة</h2>";
    
    // جدول session_products
    $session_products_sql = "
        CREATE TABLE IF NOT EXISTS session_products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_products_session_id (session_id),
            INDEX idx_session_products_product_id (product_id)
        )
    ";
    $pdo->exec($session_products_sql);
    echo "<p style='color: green;'>✅ جدول session_products جاهز</p>";
    $fixes_applied++;
    
    // جدول invoices
    $invoices_sql = "
        CREATE TABLE IF NOT EXISTS invoices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            invoice_number VARCHAR(50) NOT NULL UNIQUE,
            time_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            products_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            client_id INT NOT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_invoices_session_id (session_id),
            INDEX idx_invoices_client_id (client_id)
        )
    ";
    $pdo->exec($invoices_sql);
    echo "<p style='color: green;'>✅ جدول invoices جاهز</p>";
    $fixes_applied++;
    
    echo "<h2>6. إضافة الفهارس</h2>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_sessions_client_id ON sessions(client_id)" => "فهرس sessions.client_id",
        "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)" => "فهرس sessions.status",
        "CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id)" => "فهرس sessions.device_id",
        "CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status)" => "فهرس devices مركب",
        "CREATE INDEX IF NOT EXISTS idx_customers_client_id ON customers(client_id)" => "فهرس customers.client_id"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ $description: موجود مسبقاً</p>";
        }
    }
    
    echo "<h2>7. اختبار الاستعلامات</h2>";
    
    // اختبار استعلام الجلسات النشطة
    try {
        $test_query = "
            SELECT s.*, d.device_name, c.name as customer_name
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1 AND s.status = 'active'
            LIMIT 1
        ";
        $stmt = $pdo->query($test_query);
        echo "<p style='color: green;'>✅ استعلام الجلسات النشطة يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        // جرب مع عمود id
        try {
            $test_query = "
                SELECT s.*, d.device_name, c.name as customer_name
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                LEFT JOIN customers c ON s.customer_id = c.id
                WHERE d.client_id = 1 AND s.status = 'active'
                LIMIT 1
            ";
            $stmt = $pdo->query($test_query);
            echo "<p style='color: green;'>✅ استعلام الجلسات النشطة يعمل مع عمود id</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ استعلام الجلسات: " . $e->getMessage() . "</p>";
        }
    }
    
    // اختبار استعلام العملاء
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE client_id = 1");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✅ استعلام العملاء يعمل - يوجد " . $result['count'] . " عميل</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام العملاء: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }

    // اختبار استعلام الغرف
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM rooms WHERE client_id = 1");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✅ استعلام الغرف يعمل - يوجد " . $result['count'] . " غرفة</p>";

        // اختبار استعلام الغرف مع الأجهزة
        $rooms_test = $pdo->query("
            SELECT r.room_name, COUNT(d.device_id) as device_count
            FROM rooms r
            LEFT JOIN devices d ON r.room_id = d.room_id
            WHERE r.client_id = 1
            GROUP BY r.room_id, r.room_name
            LIMIT 3
        ");
        $rooms_result = $rooms_test->fetchAll();
        echo "<p style='color: green;'>✅ استعلام الغرف مع الأجهزة يعمل بشكل صحيح</p>";

    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام الغرف: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    $pdo->commit();
    
    echo "<h2 style='color: green;'>🎉 تم الانتهاء من جميع الإصلاحات!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ النتائج:</h3>";
    echo "<ul>";
    echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
    echo "<li>عدد الأخطاء: " . count($errors) . "</li>";
    echo "<li>النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>اختبر النظام الآن:</h3>";
    echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";
    echo "<p><a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الجلسات</a></p>";
    echo "<p><a href='client/customers.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة العملاء</a></p>";
    echo "<p><a href='client/rooms.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الغرف</a></p>";
    
} catch (Exception $e) {
    if ($transaction_started) {
        try {
            $pdo->rollBack();
        } catch (PDOException $rollback_error) {
            // تجاهل خطأ rollback إذا لم تكن هناك transaction نشطة
        }
    }
    echo "<h2 style='color: red;'>❌ حدث خطأ أثناء الإصلاح</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>يرجى التحقق من إعدادات قاعدة البيانات والمحاولة مرة أخرى.</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getFile() . " في السطر " . $e->getLine() . "</p>";
}

echo "</div>";
?>
