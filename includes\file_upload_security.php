<?php
/**
 * نظام حماية رفع الملفات
 * يتضمن فحص شامل للملفات المرفوعة وحماية من الملفات الضارة
 */

class FileUploadSecurity {
    
    private $allowed_extensions = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt'],
        'spreadsheet' => ['xls', 'xlsx', 'csv'],
        'archive' => ['zip', 'rar']
    ];
    
    private $allowed_mime_types = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv', 'application/zip', 'application/x-rar-compressed'
    ];
    
    private $dangerous_extensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'js', 'vbs',
        'exe', 'com', 'bat', 'cmd', 'scr', 'pif', 'msi', 'dll', 'sh', 'pl', 'py',
        'rb', 'jar', 'war', 'ear', 'htaccess', 'htpasswd'
    ];
    
    private $max_file_size = 10485760; // 10MB
    private $upload_dir = 'uploads/';
    private $quarantine_dir = 'quarantine/';
    
    public function __construct() {
        $this->ensureDirectories();
    }
    
    /**
     * التأكد من وجود المجلدات المطلوبة
     */
    private function ensureDirectories() {
        $dirs = [$this->upload_dir, $this->quarantine_dir];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                
                // إنشاء ملف .htaccess لحماية المجلد
                file_put_contents($dir . '.htaccess', 
                    "# Block direct access\n" .
                    "Require all denied\n" .
                    "<Files \"*.jpg\">\n" .
                    "    Require all granted\n" .
                    "</Files>\n" .
                    "<Files \"*.jpeg\">\n" .
                    "    Require all granted\n" .
                    "</Files>\n" .
                    "<Files \"*.png\">\n" .
                    "    Require all granted\n" .
                    "</Files>\n" .
                    "<Files \"*.gif\">\n" .
                    "    Require all granted\n" .
                    "</Files>\n" .
                    "<Files \"*.webp\">\n" .
                    "    Require all granted\n" .
                    "</Files>\n" .
                    "<Files \"*.pdf\">\n" .
                    "    Require all granted\n" .
                    "</Files>"
                );
            }
        }
    }
    
    /**
     * فحص شامل للملف المرفوع
     */
    public function validateUpload($file, $allowed_types = ['image']) {
        $validation_result = [
            'valid' => false,
            'errors' => [],
            'warnings' => [],
            'file_info' => []
        ];
        
        // فحص أساسي للملف
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $validation_result['errors'][] = 'ملف غير صالح أو لم يتم رفعه بشكل صحيح';
            return $validation_result;
        }
        
        // فحص حجم الملف
        if ($file['size'] > $this->max_file_size) {
            $validation_result['errors'][] = 'حجم الملف كبير جداً. الحد الأقصى: ' . 
                $this->formatBytes($this->max_file_size);
            return $validation_result;
        }
        
        if ($file['size'] == 0) {
            $validation_result['errors'][] = 'الملف فارغ';
            return $validation_result;
        }
        
        // فحص امتداد الملف
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $validation_result['file_info']['extension'] = $file_extension;
        
        if (in_array($file_extension, $this->dangerous_extensions)) {
            $validation_result['errors'][] = 'نوع الملف غير مسموح لأسباب أمنية';
            $this->quarantineFile($file, 'dangerous_extension');
            return $validation_result;
        }
        
        // فحص الامتداد ضد الأنواع المسموحة
        $extension_allowed = false;
        foreach ($allowed_types as $type) {
            if (isset($this->allowed_extensions[$type]) && 
                in_array($file_extension, $this->allowed_extensions[$type])) {
                $extension_allowed = true;
                break;
            }
        }
        
        if (!$extension_allowed) {
            $validation_result['errors'][] = 'امتداد الملف غير مسموح';
            return $validation_result;
        }
        
        // فحص MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $validation_result['file_info']['mime_type'] = $mime_type;
        
        if (!in_array($mime_type, $this->allowed_mime_types)) {
            $validation_result['errors'][] = 'نوع الملف غير مسموح (MIME type)';
            return $validation_result;
        }
        
        // فحص محتوى الملف للتأكد من عدم وجود كود ضار
        $file_content = file_get_contents($file['tmp_name']);
        $malicious_patterns = $this->scanForMaliciousContent($file_content);
        
        if (!empty($malicious_patterns)) {
            $validation_result['errors'][] = 'تم اكتشاف محتوى مشبوه في الملف';
            $validation_result['warnings'] = $malicious_patterns;
            $this->quarantineFile($file, 'malicious_content', $malicious_patterns);
            return $validation_result;
        }
        
        // فحص خاص للصور
        if (in_array($file_extension, $this->allowed_extensions['image'])) {
            $image_validation = $this->validateImage($file);
            if (!$image_validation['valid']) {
                $validation_result['errors'] = array_merge(
                    $validation_result['errors'], 
                    $image_validation['errors']
                );
                return $validation_result;
            }
        }
        
        // فحص اسم الملف
        $filename_validation = $this->validateFilename($file['name']);
        if (!$filename_validation['valid']) {
            $validation_result['errors'] = array_merge(
                $validation_result['errors'], 
                $filename_validation['errors']
            );
            return $validation_result;
        }
        
        $validation_result['valid'] = true;
        $validation_result['file_info']['size'] = $file['size'];
        $validation_result['file_info']['original_name'] = $file['name'];
        
        return $validation_result;
    }
    
    /**
     * فحص الصور
     */
    private function validateImage($file) {
        $result = ['valid' => true, 'errors' => []];
        
        // التحقق من أن الملف صورة حقيقية
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            $result['valid'] = false;
            $result['errors'][] = 'الملف ليس صورة صالحة';
            return $result;
        }
        
        // فحص أبعاد الصورة
        $width = $image_info[0];
        $height = $image_info[1];
        
        if ($width > 5000 || $height > 5000) {
            $result['valid'] = false;
            $result['errors'][] = 'أبعاد الصورة كبيرة جداً';
            return $result;
        }
        
        if ($width < 10 || $height < 10) {
            $result['valid'] = false;
            $result['errors'][] = 'أبعاد الصورة صغيرة جداً';
            return $result;
        }
        
        return $result;
    }
    
    /**
     * فحص اسم الملف
     */
    private function validateFilename($filename) {
        $result = ['valid' => true, 'errors' => []];
        
        // فحص الأحرف المسموحة
        if (!preg_match('/^[a-zA-Z0-9\-_\.\s\u0600-\u06FF]+$/u', $filename)) {
            $result['valid'] = false;
            $result['errors'][] = 'اسم الملف يحتوي على أحرف غير مسموحة';
        }
        
        // فحص طول اسم الملف
        if (strlen($filename) > 255) {
            $result['valid'] = false;
            $result['errors'][] = 'اسم الملف طويل جداً';
        }
        
        // فحص الأسماء المحظورة
        $forbidden_names = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'lpt1', 'lpt2'];
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);
        
        if (in_array(strtolower($name_without_ext), $forbidden_names)) {
            $result['valid'] = false;
            $result['errors'][] = 'اسم الملف محظور';
        }
        
        return $result;
    }
    
    /**
     * فحص المحتوى الضار
     */
    private function scanForMaliciousContent($content) {
        $malicious_patterns = [
            'php_code' => '/<\?php|<\?=|\<\?|\?\>/',
            'script_tags' => '/<script[^>]*>.*?<\/script>/i',
            'javascript' => '/javascript:|vbscript:|onload=|onerror=/i',
            'sql_injection' => '/(union|select|insert|update|delete|drop|create|alter)\s+/i',
            'file_inclusion' => '/(include|require|file_get_contents|fopen|readfile)\s*\(/i',
            'command_execution' => '/(exec|system|shell_exec|passthru|eval)\s*\(/i',
            'base64_suspicious' => '/eval\s*\(\s*base64_decode/i'
        ];
        
        $detected_patterns = [];
        
        foreach ($malicious_patterns as $pattern_name => $pattern) {
            if (preg_match($pattern, $content)) {
                $detected_patterns[] = $pattern_name;
            }
        }
        
        return $detected_patterns;
    }
    
    /**
     * عزل الملف المشبوه
     */
    private function quarantineFile($file, $reason, $details = []) {
        $quarantine_filename = date('Y-m-d_H-i-s') . '_' . 
            uniqid() . '_' . basename($file['name']);
        $quarantine_path = $this->quarantine_dir . $quarantine_filename;
        
        if (move_uploaded_file($file['tmp_name'], $quarantine_path)) {
            // تسجيل معلومات الملف المعزول
            $log_data = [
                'timestamp' => date('Y-m-d H:i:s'),
                'original_name' => $file['name'],
                'quarantine_name' => $quarantine_filename,
                'reason' => $reason,
                'details' => $details,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ];
            
            file_put_contents(
                $this->quarantine_dir . 'quarantine_log.json',
                json_encode($log_data) . "\n",
                FILE_APPEND | LOCK_EX
            );
        }
    }
    
    /**
     * رفع آمن للملف
     */
    public function secureUpload($file, $allowed_types = ['image'], $custom_name = null) {
        $validation = $this->validateUpload($file, $allowed_types);
        
        if (!$validation['valid']) {
            return $validation;
        }
        
        // إنشاء اسم ملف آمن
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $safe_filename = $custom_name ?: 
            date('Y-m-d_H-i-s') . '_' . uniqid() . '.' . $file_extension;
        
        // التأكد من عدم وجود الملف
        $upload_path = $this->upload_dir . $safe_filename;
        $counter = 1;
        while (file_exists($upload_path)) {
            $name_without_ext = pathinfo($safe_filename, PATHINFO_FILENAME);
            $upload_path = $this->upload_dir . $name_without_ext . '_' . $counter . '.' . $file_extension;
            $counter++;
        }
        
        // رفع الملف
        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            // تطبيق صلاحيات آمنة
            chmod($upload_path, 0644);
            
            $validation['file_info']['uploaded_path'] = $upload_path;
            $validation['file_info']['uploaded_name'] = basename($upload_path);
            
            return $validation;
        } else {
            $validation['valid'] = false;
            $validation['errors'][] = 'فشل في رفع الملف';
            return $validation;
        }
    }
    
    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    /**
     * حذف الملفات القديمة من الحجر الصحي
     */
    public function cleanupQuarantine($days = 30) {
        $files = glob($this->quarantine_dir . '*');
        $cutoff_time = time() - ($days * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }
}
?>
