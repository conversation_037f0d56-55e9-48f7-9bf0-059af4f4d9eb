# إصلاحات مشروع PlayGood

## المشكلة الحالية
```
Fatal error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.client_id' in 'where clause'
```

هذا الخطأ يحدث لأن العمود `client_id` غير موجود في جدول `sessions`.

## الحلول المتاحة

### الحل الأول: تشغيل سكريبت الإصلاح التلقائي (الأسهل)

1. افتح المتصفح واذهب إلى:
   ```
   http://localhost/playgood/fix_database.php
   ```

2. سيقوم السكريبت بإصلاح جميع المشاكل تلقائياً

3. بعد انتهاء الإصلاح، اذهب إلى لوحة التحكم:
   ```
   http://localhost/playgood/client/dashboard.php
   ```

### الحل الثاني: تشغيل SQL يدوياً

1. افتح phpMyAdmin أو أي أداة إدارة قاعدة بيانات

2. اختر قاعدة البيانات الخاصة بالمشروع

3. شغل الكود التالي:

```sql
-- إضافة الأعمدة المفقودة
ALTER TABLE sessions 
ADD COLUMN client_id INT NULL,
ADD COLUMN total_cost DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN customer_id INT NULL,
ADD COLUMN created_by INT NULL,
ADD COLUMN updated_by INT NULL;

-- تحديث client_id من جدول devices
UPDATE sessions s 
JOIN devices d ON s.device_id = d.device_id 
SET s.client_id = d.client_id 
WHERE s.client_id IS NULL;

-- إضافة client_id لجدول cafeteria_items
ALTER TABLE cafeteria_items 
ADD COLUMN client_id INT NOT NULL DEFAULT 1;

-- إنشاء جدول session_products
CREATE TABLE IF NOT EXISTS session_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول invoices
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    time_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    products_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    client_id INT NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## الإصلاحات المطبقة في الكود

### 1. dashboard.php
- ✅ تم تغيير `s.client_id` إلى `d.client_id` في جميع الاستعلامات
- ✅ تم إصلاح استعلامات الإحصائيات
- ✅ تم تحسين عرض البيانات

### 2. sessions.php
- ✅ تم تغيير `s.client_id` إلى `d.client_id` في جميع الاستعلامات
- ✅ تم إصلاح إنهاء الجلسات
- ✅ تم تحسين عرض الجلسات النشطة والمكتملة

### 3. ملفات أخرى
- ✅ تم إصلاح جميع الملفات التي تستخدم جدول sessions
- ✅ تم تحديث ملفات API
- ✅ تم تحسين نظام الصلاحيات

## التحقق من نجاح الإصلاح

بعد تطبيق الإصلاحات، تأكد من:

1. **لوحة التحكم تعمل بدون أخطاء**
   ```
   http://localhost/playgood/client/dashboard.php
   ```

2. **صفحة الجلسات تعمل بشكل صحيح**
   ```
   http://localhost/playgood/client/sessions.php
   ```

3. **يمكن بدء وإنهاء الجلسات**

4. **الإحصائيات تظهر بشكل صحيح**

## في حالة استمرار المشاكل

إذا استمرت المشاكل، تحقق من:

1. **إعدادات قاعدة البيانات** في `config/database.php`
2. **صلاحيات المستخدم** في قاعدة البيانات
3. **وجود جميع الجداول المطلوبة**

## هيكل قاعدة البيانات المطلوب

```
clients (العملاء الرئيسيين)
├── devices (الأجهزة)
├── rooms (الغرف)
├── employees (الموظفين)
├── customers (العملاء الفرعيين)
├── sessions (الجلسات) - مرتبط بـ devices
├── cafeteria_items (منتجات الكافتيريا)
├── session_products (منتجات الجلسات)
└── invoices (الفواتير)
```

## الدعم

في حالة وجود مشاكل إضافية، يرجى:

1. التحقق من سجلات الأخطاء في Apache/PHP
2. التأكد من تشغيل MySQL بشكل صحيح
3. التحقق من صلاحيات الملفات

---

**ملاحظة مهمة:** تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق أي إصلاحات.
