<?php
/**
 * ملف اختبار نظام صلاحيات الصفحات للعملاء
 * يختبر جميع مكونات النظام للتأكد من عمله بشكل صحيح
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نظام صلاحيات الصفحات للعملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 1000px; margin: 2rem auto; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".test-section { margin-bottom: 2rem; padding: 1rem; border: 1px solid #dee2e6; border-radius: 8px; background: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h1 class='h3 mb-0'><i class='fas fa-vial me-2'></i>اختبار نظام صلاحيات الصفحات للعملاء</h1>";
echo "</div>";
echo "<div class='card-body'>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

function runTest($test_name, $test_function) {
    global $total_tests, $passed_tests, $test_results;
    
    $total_tests++;
    echo "<div class='test-section'>";
    echo "<h4>🧪 $test_name</h4>";
    
    try {
        $result = $test_function();
        if ($result) {
            echo "<p class='success'>✅ نجح الاختبار</p>";
            $passed_tests++;
            $test_results[$test_name] = 'نجح';
        } else {
            echo "<p class='error'>❌ فشل الاختبار</p>";
            $test_results[$test_name] = 'فشل';
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</p>";
        $test_results[$test_name] = 'خطأ: ' . $e->getMessage();
    }
    
    echo "</div>";
}

// اختبار 1: التحقق من وجود الجداول
runTest("التحقق من وجود الجداول", function() use ($pdo) {
    $required_tables = ['client_pages', 'client_page_permissions'];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            echo "<p class='error'>الجدول $table غير موجود</p>";
            return false;
        }
        echo "<p class='success'>الجدول $table موجود</p>";
    }
    
    return true;
});

// اختبار 2: التحقق من البيانات الأساسية
runTest("التحقق من البيانات الأساسية", function() use ($pdo) {
    // فحص الصفحات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM client_pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    
    if ($pages_count == 0) {
        echo "<p class='error'>لا توجد صفحات في الجدول</p>";
        return false;
    }
    
    echo "<p class='success'>عدد الصفحات المتاحة: $pages_count</p>";
    
    // فحص الصفحات الافتراضية
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM client_pages WHERE is_default = 1 AND is_active = 1");
    $default_count = $stmt->fetch()['count'];
    
    echo "<p class='success'>عدد الصفحات الافتراضية: $default_count</p>";
    
    return $pages_count > 0;
});

// اختبار 3: اختبار الإجراء المخزن
runTest("اختبار الإجراء المخزن", function() use ($pdo) {
    // البحث عن عميل للاختبار
    $stmt = $pdo->query("SELECT client_id FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if (!$client) {
        echo "<p class='warning'>لا يوجد عملاء للاختبار</p>";
        return true; // نجح الاختبار لأنه لا يوجد عملاء
    }
    
    $client_id = $client['client_id'];
    
    // حذف الصلاحيات الموجودة للاختبار
    $stmt = $pdo->prepare("DELETE FROM client_page_permissions WHERE client_id = ?");
    $stmt->execute([$client_id]);
    
    // تشغيل الإجراء المخزن
    $stmt = $pdo->prepare("CALL GrantDefaultPermissionsToClient(?)");
    $stmt->execute([$client_id]);
    
    // التحقق من النتيجة
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM client_page_permissions WHERE client_id = ?");
    $stmt->execute([$client_id]);
    $permissions_count = $stmt->fetch()['count'];
    
    echo "<p class='success'>تم منح $permissions_count صلاحية افتراضية للعميل $client_id</p>";
    
    return $permissions_count > 0;
});

// اختبار 4: اختبار العرض (View)
runTest("اختبار العرض المفصل", function() use ($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM client_page_permissions_detailed LIMIT 5");
        $results = $stmt->fetchAll();
        
        echo "<p class='success'>تم جلب " . count($results) . " سجل من العرض المفصل</p>";
        
        if (!empty($results)) {
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>العميل</th><th>الصفحة</th><th>الصلاحية</th></tr></thead>";
            echo "<tbody>";
            foreach (array_slice($results, 0, 3) as $row) {
                $permission_status = $row['has_permission'] ? '✅ مسموح' : '❌ غير مسموح';
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['page_label']) . "</td>";
                echo "<td>$permission_status</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        }
        
        return true;
    } catch (Exception $e) {
        echo "<p class='error'>خطأ في العرض: " . $e->getMessage() . "</p>";
        return false;
    }
});

// اختبار 5: اختبار دوال PHP
runTest("اختبار دوال PHP", function() use ($pdo) {
    // محاكاة جلسة عميل
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // البحث عن عميل للاختبار
    $stmt = $pdo->query("SELECT client_id FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if (!$client) {
        echo "<p class='warning'>لا يوجد عملاء للاختبار</p>";
        return true;
    }
    
    $_SESSION['client_id'] = $client['client_id'];
    
    // تضمين ملف الدوال
    require_once 'client/includes/auth.php';
    
    // اختبار دالة hasPagePermission
    $dashboard_permission = hasPagePermission('dashboard');
    $devices_permission = hasPagePermission('devices');
    
    echo "<p class='success'>صلاحية لوحة التحكم: " . ($dashboard_permission ? '✅ مسموح' : '❌ غير مسموح') . "</p>";
    echo "<p class='success'>صلاحية الأجهزة: " . ($devices_permission ? '✅ مسموح' : '❌ غير مسموح') . "</p>";
    
    // اختبار دالة getAllowedPages
    $allowed_pages = getAllowedPages();
    echo "<p class='success'>عدد الصفحات المسموحة: " . count($allowed_pages) . "</p>";
    
    if (!empty($allowed_pages)) {
        echo "<p>أمثلة على الصفحات المسموحة:</p>";
        echo "<ul>";
        foreach (array_slice($allowed_pages, 0, 5) as $page) {
            echo "<li>" . htmlspecialchars($page['page_label']) . " (" . htmlspecialchars($page['page_name']) . ")</li>";
        }
        echo "</ul>";
    }
    
    return true;
});

// اختبار 6: اختبار واجهة الأدمن
runTest("اختبار ملفات واجهة الأدمن", function() {
    $admin_files = [
        'admin/client_permissions.php' => 'صفحة إدارة صلاحيات العملاء',
        'admin/includes/sidebar.php' => 'القائمة الجانبية للأدمن'
    ];
    
    foreach ($admin_files as $file => $description) {
        if (!file_exists($file)) {
            echo "<p class='error'>الملف غير موجود: $file ($description)</p>";
            return false;
        }
        echo "<p class='success'>الملف موجود: $description</p>";
    }
    
    return true;
});

// اختبار 7: اختبار ملفات العميل
runTest("اختبار ملفات العميل المحدثة", function() {
    $client_files = [
        'client/includes/auth.php' => 'ملف التحقق من الصلاحيات',
        'client/includes/header.php' => 'ملف الرأس والقائمة الجانبية'
    ];
    
    foreach ($client_files as $file => $description) {
        if (!file_exists($file)) {
            echo "<p class='error'>الملف غير موجود: $file ($description)</p>";
            return false;
        }
        
        $content = file_get_contents($file);
        
        // فحص وجود الدوال الجديدة في auth.php
        if ($file === 'client/includes/auth.php') {
            if (strpos($content, 'hasPagePermission') === false) {
                echo "<p class='error'>دالة hasPagePermission غير موجودة في $file</p>";
                return false;
            }
            if (strpos($content, 'getAllowedPages') === false) {
                echo "<p class='error'>دالة getAllowedPages غير موجودة في $file</p>";
                return false;
            }
        }
        
        // فحص استخدام الدوال الجديدة في header.php
        if ($file === 'client/includes/header.php') {
            if (strpos($content, 'hasPagePermission') === false) {
                echo "<p class='warning'>لم يتم تحديث header.php لاستخدام النظام الجديد</p>";
            }
        }
        
        echo "<p class='success'>الملف محدث: $description</p>";
    }
    
    return true;
});

// عرض النتائج النهائية
echo "<div class='alert alert-info mt-4'>";
echo "<h3>📊 ملخص نتائج الاختبار</h3>";
echo "<p><strong>إجمالي الاختبارات:</strong> $total_tests</p>";
echo "<p><strong>الاختبارات الناجحة:</strong> $passed_tests</p>";
echo "<p><strong>الاختبارات الفاشلة:</strong> " . ($total_tests - $passed_tests) . "</p>";

$success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 2) : 0;
echo "<p><strong>معدل النجاح:</strong> $success_rate%</p>";

if ($success_rate >= 80) {
    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 النظام يعمل بشكل ممتاز!</h4>";
    echo "<p>جميع الاختبارات الأساسية نجحت. يمكنك البدء في استخدام النظام.</p>";
} elseif ($success_rate >= 60) {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ النظام يعمل مع بعض المشاكل</h4>";
    echo "<p>معظم الاختبارات نجحت، لكن هناك بعض المشاكل التي تحتاج إصلاح.</p>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ النظام يحتاج إصلاح</h4>";
    echo "<p>عدة اختبارات فشلت. يرجى مراجعة الأخطاء وإصلاحها قبل الاستخدام.</p>";
}
echo "</div>";
echo "</div>";

// عرض تفاصيل النتائج
echo "<div class='mt-4'>";
echo "<h4>تفاصيل النتائج:</h4>";
echo "<table class='table table-striped'>";
echo "<thead><tr><th>الاختبار</th><th>النتيجة</th></tr></thead>";
echo "<tbody>";
foreach ($test_results as $test => $result) {
    $badge_class = (strpos($result, 'نجح') !== false) ? 'bg-success' : 'bg-danger';
    echo "<tr>";
    echo "<td>$test</td>";
    echo "<td><span class='badge $badge_class'>$result</span></td>";
    echo "</tr>";
}
echo "</tbody></table>";
echo "</div>";

// روابط مفيدة
echo "<div class='mt-4'>";
echo "<h4>روابط مفيدة:</h4>";
echo "<div class='btn-group' role='group'>";
echo "<a href='setup_client_page_permissions.php' class='btn btn-primary'>إعداد النظام</a>";
echo "<a href='admin/client_permissions.php' class='btn btn-success'>إدارة الصلاحيات</a>";
echo "<a href='admin/dashboard.php' class='btn btn-secondary'>لوحة تحكم الأدمن</a>";
echo "<a href='client/dashboard.php' class='btn btn-info'>لوحة تحكم العميل</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
