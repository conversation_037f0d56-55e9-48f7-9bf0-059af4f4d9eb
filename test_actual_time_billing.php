<?php
/**
 * اختبار حساب التكلفة بناءً على الوقت الفعلي
 * مقارنة بين النظام القديم (تقريب الساعات) والنظام الجديد (الوقت الفعلي)
 */

require_once 'config/database.php';

echo "<h1>اختبار حساب التكلفة بناءً على الوقت الفعلي</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // اختبار مقارنة بين النظامين
    echo "<h3>🔍 مقارنة بين النظام القديم والجديد</h3>";
    
    // أمثلة مختلفة للمدد
    $test_durations = [
        10,   // 10 دقائق
        25,   // 25 دقيقة
        45,   // 45 دقيقة
        60,   // ساعة واحدة
        75,   // ساعة و 15 دقيقة
        90,   // ساعة ونصف
        120,  // ساعتان
        135   // ساعتان و 15 دقيقة
    ];
    
    $hourly_rate = 20; // سعر افتراضي 20 جنيه/ساعة
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 مقارنة التكلفة (السعر: $hourly_rate ج.م/ساعة)</h4>";
    echo "<table style='width: 100%; border-collapse: collapse; margin-top: 15px;'>";
    echo "<thead>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>المدة</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>النظام القديم (تقريب)</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>النظام الجديد (فعلي)</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الفرق</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>نسبة التوفير</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($test_durations as $duration_minutes) {
        // النظام القديم: تقريب للساعة الكاملة
        $old_cost = ceil($duration_minutes / 60) * $hourly_rate;
        
        // النظام الجديد: الوقت الفعلي
        $new_cost = ($duration_minutes / 60) * $hourly_rate;
        
        // الفرق والنسبة
        $difference = $old_cost - $new_cost;
        $savings_percentage = $old_cost > 0 ? (($difference / $old_cost) * 100) : 0;
        
        // تحديد لون الصف
        $row_color = $difference > 0 ? '#d4edda' : ($difference < 0 ? '#f8d7da' : '#fff3cd');
        
        echo "<tr style='background: $row_color;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
        
        // عرض المدة بشكل مفهوم
        if ($duration_minutes < 60) {
            echo "$duration_minutes دقيقة";
        } else {
            $hours = floor($duration_minutes / 60);
            $minutes = $duration_minutes % 60;
            if ($minutes > 0) {
                echo "$hours ساعة و $minutes دقيقة";
            } else {
                echo "$hours ساعة";
            }
        }
        
        echo "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($old_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($new_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
        
        if ($difference > 0) {
            echo "<span style='color: green; font-weight: bold;'>-" . number_format($difference, 2) . " ج.م</span>";
        } elseif ($difference < 0) {
            echo "<span style='color: red; font-weight: bold;'>+" . number_format(abs($difference), 2) . " ج.م</span>";
        } else {
            echo "<span style='color: gray;'>لا يوجد فرق</span>";
        }
        
        echo "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
        
        if ($savings_percentage > 0) {
            echo "<span style='color: green; font-weight: bold;'>" . number_format($savings_percentage, 1) . "%</span>";
        } elseif ($savings_percentage < 0) {
            echo "<span style='color: red; font-weight: bold;'>" . number_format(abs($savings_percentage), 1) . "%</span>";
        } else {
            echo "<span style='color: gray;'>0%</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // اختبار على جلسات حقيقية
    echo "<h3>🎮 اختبار على الجلسات الحقيقية</h3>";
    
    // البحث عن جلسات نشطة
    $active_sessions_query = "
        SELECT s.session_id, s.start_time, s.customer_id,
               d.device_name, d.single_rate, d.multi_rate, d.hourly_rate,
               c.name as customer_name,
               TIMESTAMPDIFF(MINUTE, s.start_time, NOW()) as duration_minutes,
               COALESCE(s.game_type, 'single') as game_type
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'active'
        ORDER BY s.start_time DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->query($active_sessions_query);
    $active_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($active_sessions) > 0) {
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🔄 الجلسات النشطة الحالية</h4>";
        echo "<table style='width: 100%; border-collapse: collapse; margin-top: 15px;'>";
        echo "<thead>";
        echo "<tr style='background: #17a2b8; color: white;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>الجهاز</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>العميل</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>المدة الحالية</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>التكلفة القديمة</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>التكلفة الجديدة</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>الفرق</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($active_sessions as $session) {
            $duration_minutes = $session['duration_minutes'];
            $game_type = $session['game_type'];
            
            // تحديد السعر المناسب
            if ($game_type === 'multiplayer') {
                $rate = $session['multi_rate'] ?? $session['hourly_rate'] ?? 0;
            } else {
                $rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
            }
            
            // حساب التكلفة بالطريقتين
            $old_cost = ceil($duration_minutes / 60) * $rate;
            $new_cost = ($duration_minutes / 60) * $rate;
            $difference = $old_cost - $new_cost;
            
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($session['device_name']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
            
            if ($duration_minutes < 60) {
                echo "$duration_minutes دقيقة";
            } else {
                $hours = floor($duration_minutes / 60);
                $minutes = $duration_minutes % 60;
                if ($minutes > 0) {
                    echo "$hours ساعة و $minutes دقيقة";
                } else {
                    echo "$hours ساعة";
                }
            }
            
            echo "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($old_cost, 2) . " ج.م</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($new_cost, 2) . " ج.م</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
            
            if ($difference > 0) {
                echo "<span style='color: green; font-weight: bold;'>-" . number_format($difference, 2) . " ج.م</span>";
            } elseif ($difference < 0) {
                echo "<span style='color: red; font-weight: bold;'>+" . number_format(abs($difference), 2) . " ج.م</span>";
            } else {
                echo "<span style='color: gray;'>لا يوجد فرق</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة حالياً للاختبار</p>";
    }
    
    // ملخص التعديلات
    echo "<hr>";
    echo "<h3>📋 ملخص التعديلات المطبقة</h3>";
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #bee5eb;'>";
    echo "<h4>🔧 التعديلات التي تم تطبيقها:</h4>";
    echo "<ol>";
    echo "<li><strong>تعديل حساب التكلفة في إنهاء الجلسة:</strong>";
    echo "<ul>";
    echo "<li>تم تغيير <code>ceil(\$duration_minutes / 60) * \$hourly_rate</code></li>";
    echo "<li>إلى <code>(\$duration_minutes / 60) * \$hourly_rate</code></li>";
    echo "<li>في ملف <code>client/sessions.php</code></li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>تعديل عرض التكلفة في الجلسات النشطة:</strong>";
    echo "<ul>";
    echo "<li>تم تحديث حساب التكلفة الحالية للجلسات النشطة</li>";
    echo "<li>لتعكس الوقت الفعلي بدلاً من التقريب</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>تعديل حساب التكلفة في الفاتورة:</strong>";
    echo "<ul>";
    echo "<li>تم تعديل ملف <code>client/invoice.php</code></li>";
    echo "<li>لحساب التكلفة بناءً على الوقت الفعلي</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4>✅ فوائد النظام الجديد:</h4>";
    echo "<ul>";
    echo "<li><strong>عدالة أكثر:</strong> العميل يدفع فقط مقابل الوقت الذي قضاه فعلياً</li>";
    echo "<li><strong>شفافية:</strong> حساب دقيق للتكلفة بناءً على الدقائق</li>";
    echo "<li><strong>رضا العملاء:</strong> لن يدفع العميل ساعة كاملة مقابل 10 دقائق فقط</li>";
    echo "<li><strong>تنافسية:</strong> أسعار أكثر عدالة مقارنة بالمنافسين</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ تم تطبيق جميع التعديلات بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>النظام الآن يحسب التكلفة بناءً على الوقت الفعلي الذي قضاه العميل.</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في الاتصال بقاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

code {
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

table {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}
</style>
