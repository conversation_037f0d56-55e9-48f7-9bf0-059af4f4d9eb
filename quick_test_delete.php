<?php
/**
 * اختبار سريع لحذف المنتجات
 */

session_start();
$_SESSION['client_id'] = 1; // للاختبار

require_once 'config/database.php';

echo "<h1>اختبار سريع لحذف المنتجات</h1>";

try {
    // إنشاء بيانات تجريبية
    echo "<h2>1. إنشاء بيانات تجريبية</h2>";
    
    // جهاز
    $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, single_rate, multi_rate, status, client_id) 
               VALUES ('جهاز اختبار', 'PS5', 15.00, 10.00, 20.00, 'available', 1)");
    $device_id = $pdo->lastInsertId();
    echo "<p>✅ جهاز: $device_id</p>";
    
    // جلسة
    $pdo->exec("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) 
               VALUES ($device_id, 1, CURRENT_TIMESTAMP, 'active', 1)");
    $session_id = $pdo->lastInsertId();
    echo "<p>✅ جلسة: $session_id</p>";
    
    // منتج
    $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) 
               VALUES ('منتج اختبار', 5.00, 'اختبار', 1)");
    $product_id = $pdo->lastInsertId();
    echo "<p>✅ منتج: $product_id</p>";
    
    // إضافة منتج للجلسة
    $pdo->exec("INSERT INTO session_products (session_id, product_id, quantity, price) 
               VALUES ($session_id, $product_id, 2, 5.00)");
    echo "<p>✅ تم إضافة المنتج للجلسة</p>";
    
    echo "<h2>2. اختبار API الحذف</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/delete_session_product.php';
    
    $delete_data = [
        'session_id' => $session_id,
        'product_id' => $product_id
    ];
    
    echo "<p><strong>البيانات المرسلة:</strong></p>";
    echo "<pre>" . json_encode($delete_data, JSON_PRETTY_PRINT) . "</pre>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($delete_data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    echo "<p><strong>استجابة API:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $json_response = json_decode($response, true);
    if ($json_response && isset($json_response['success'])) {
        if ($json_response['success']) {
            echo "<p style='color: green; font-size: 18px;'><strong>✅ نجح الحذف!</strong></p>";
        } else {
            echo "<p style='color: red; font-size: 18px;'><strong>❌ فشل الحذف: " . $json_response['error'] . "</strong></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ استجابة غير صحيحة</p>";
    }
    
    echo "<h2>3. فحص قاعدة البيانات</h2>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ? AND product_id = ?");
    $stmt->execute([$session_id, $product_id]);
    $count = $stmt->fetch()['count'];
    
    echo "<p>عدد المنتجات المتبقية في الجلسة: <strong>$count</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ تم الحذف من قاعدة البيانات!</strong></p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ لم يتم الحذف من قاعدة البيانات!</strong></p>";
    }
    
    echo "<h2>4. اختبار JavaScript</h2>";
    ?>
    
    <button onclick="testJSDelete()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار JavaScript
    </button>
    
    <div id="js-result" style="margin-top: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;"></div>
    
    <script>
    function testJSDelete() {
        const resultDiv = document.getElementById('js-result');
        resultDiv.innerHTML = '<p style="color: blue;">جاري الاختبار...</p>';
        
        // إضافة منتج جديد أولاً
        fetch('client/api/add_session_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: <?php echo $session_id; ?>,
                product_id: <?php echo $product_id; ?>,
                quantity: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML += '<p style="color: green;">✅ تم إضافة منتج للاختبار</p>';
                
                // الآن اختبار الحذف
                return fetch('client/api/delete_session_product.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: <?php echo $session_id; ?>,
                        product_id: <?php echo $product_id; ?>
                    })
                });
            } else {
                throw new Error('فشل في إضافة المنتج: ' + data.error);
            }
        })
        .then(response => response.text())
        .then(responseText => {
            console.log('Delete response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    resultDiv.innerHTML += `
                        <p style="color: green;"><strong>✅ JavaScript: نجح الحذف!</strong></p>
                        <p>الرسالة: ${data.message}</p>
                        <p>التكلفة الجديدة: ${data.total_cost} ج.م</p>
                    `;
                } else {
                    resultDiv.innerHTML += `
                        <p style="color: red;"><strong>❌ JavaScript: فشل الحذف</strong></p>
                        <p>الخطأ: ${data.error}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <p style="color: red;"><strong>❌ خطأ في JSON:</strong> ${error.message}</p>
                    <details>
                        <summary>الاستجابة الخام</summary>
                        <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; font-size: 12px;">${responseText}</pre>
                    </details>
                `;
            }
        })
        .catch(error => {
            resultDiv.innerHTML += `<p style="color: red;"><strong>❌ خطأ:</strong> ${error.message}</p>`;
        });
    }
    </script>
    
    <?php
    
    // تنظيف البيانات
    echo "<h2>5. تنظيف البيانات</h2>";
    $pdo->exec("DELETE FROM session_products WHERE session_id = $session_id");
    $pdo->exec("DELETE FROM sessions WHERE session_id = $session_id");
    $pdo->exec("DELETE FROM cafeteria_items WHERE id = $product_id");
    $pdo->exec("DELETE FROM devices WHERE device_id = $device_id");
    echo "<p>✅ تم حذف البيانات التجريبية</p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}
h1, h2 {
    color: #333;
}
pre {
    font-size: 14px;
}
</style>
