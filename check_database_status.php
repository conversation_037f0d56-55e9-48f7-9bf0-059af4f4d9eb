<?php
/**
 * سكريپت فحص حالة قاعدة البيانات
 * يتحقق من وجود قاعدة البيانات والجداول
 */

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص حالة قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-info text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-search me-2'></i>
                        فحص حالة قاعدة البيانات
                    </h3>
                </div>
                <div class='card-body'>";

try {
    // قراءة إعدادات قاعدة البيانات من ملف الإعدادات
    echo "<h5>1. فحص إعدادات قاعدة البيانات</h5>";

    // تضمين ملف الإعدادات بدون تنفيذ الاتصال
    $config_content = file_get_contents('config/database.php');

    // استخراج القيم من ملف الإعدادات
    preg_match("/define\\('DB_HOST',\\s*'([^']+)'/", $config_content, $host_match);
    preg_match("/define\\('DB_NAME',\\s*'([^']+)'/", $config_content, $dbname_match);
    preg_match("/define\\('DB_USER',\\s*'([^']+)'/", $config_content, $username_match);
    preg_match("/define\\('DB_PASS',\\s*'([^']*)'/", $config_content, $password_match);

    $host = $host_match[1] ?? 'localhost';
    $dbname = $dbname_match[1] ?? 'station';
    $username = $username_match[1] ?? 'root';
    $password = $password_match[1] ?? '';

    echo "<div class='alert alert-info'>
            <h6>الإعدادات المقروءة من config/database.php:</h6>
            <ul class='mb-0'>
                <li>الخادم: <code>$host</code></li>
                <li>قاعدة البيانات: <code>$dbname</code></li>
                <li>المستخدم: <code>$username</code></li>
                <li>كلمة المرور: <code>" . (empty($password) ? 'فارغة' : 'محددة') . "</code></li>
            </ul>
          </div>";
    
    // محاولة الاتصال بالخادم بدون تحديد قاعدة بيانات
    echo "<h5>2. اختبار الاتصال بخادم قاعدة البيانات</h5>";
    
    try {
        $pdo_server = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم الاتصال بخادم قاعدة البيانات بنجاح</p>";
        
        // فحص إصدار MySQL
        $version = $pdo_server->query('SELECT VERSION()')->fetchColumn();
        echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>إصدار MySQL/MariaDB: $version</p>";
        
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>
                <h6><i class='fas fa-times me-2'></i>فشل الاتصال بخادم قاعدة البيانات!</h6>
                <p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
                <p><strong>الحلول:</strong></p>
                <ul>
                    <li>تأكد من تشغيل XAMPP/WAMP</li>
                    <li>تأكد من تشغيل خدمة MySQL</li>
                    <li>تحقق من إعدادات المستخدم وكلمة المرور</li>
                </ul>
              </div>";
        exit;
    }
    
    // فحص قواعد البيانات الموجودة
    echo "<h5>3. فحص قواعد البيانات الموجودة</h5>";
    
    $databases = $pdo_server->query('SHOW DATABASES')->fetchAll(PDO::FETCH_COLUMN);
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h6>قواعد البيانات الموجودة:</h6>";
    echo "<ul class='list-group'>";
    
    $station_exists = false;
    foreach ($databases as $db) {
        if ($db === $dbname) {
            $station_exists = true;
            echo "<li class='list-group-item list-group-item-success'><i class='fas fa-database me-2'></i><strong>$db</strong> (المطلوبة)</li>";
        } else {
            echo "<li class='list-group-item'><i class='fas fa-database me-2'></i>$db</li>";
        }
    }
    echo "</ul>";
    echo "</div>";
    
    if (!$station_exists) {
        echo "<div class='col-md-6'>";
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>قاعدة البيانات '$dbname' غير موجودة!</h6>";
        echo "<p>يجب إنشاء قاعدة البيانات أولاً.</p>";
        echo "<button onclick='createDatabase()' class='btn btn-warning'>
                <i class='fas fa-plus me-1'></i>إنشاء قاعدة البيانات
              </button>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='col-md-6'>";
        echo "<div class='alert alert-success'>";
        echo "<h6><i class='fas fa-check me-2'></i>قاعدة البيانات '$dbname' موجودة</h6>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // إذا كانت قاعدة البيانات موجودة، فحص الجداول
    if ($station_exists) {
        echo "<h5>4. فحص الجداول في قاعدة البيانات</h5>";
        
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $tables = $pdo->query('SHOW TABLES')->fetchAll(PDO::FETCH_COLUMN);
            
            $required_tables = [
                'clients', 'devices', 'customers', 'sessions', 'invoices', 
                'employees', 'cafeteria_items', 'session_products', 'rooms',
                'expenses', 'expense_types', 'income_types', 'additional_income'
            ];
            
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<h6>الجداول الموجودة (" . count($tables) . "):</h6>";
            
            if (empty($tables)) {
                echo "<div class='alert alert-warning'>لا توجد جداول في قاعدة البيانات</div>";
            } else {
                echo "<ul class='list-group' style='max-height: 300px; overflow-y: auto;'>";
                foreach ($tables as $table) {
                    $is_required = in_array($table, $required_tables);
                    $class = $is_required ? 'list-group-item-success' : 'list-group-item-light';
                    $icon = $is_required ? 'fas fa-check text-success' : 'fas fa-table text-muted';
                    echo "<li class='list-group-item $class'><i class='$icon me-2'></i>$table</li>";
                }
                echo "</ul>";
            }
            
            echo "</div>";
            echo "<div class='col-md-6'>";
            echo "<h6>الجداول المطلوبة:</h6>";
            echo "<ul class='list-group'>";
            
            $missing_tables = [];
            foreach ($required_tables as $required_table) {
                if (in_array($required_table, $tables)) {
                    echo "<li class='list-group-item list-group-item-success'><i class='fas fa-check me-2'></i>$required_table</li>";
                } else {
                    $missing_tables[] = $required_table;
                    echo "<li class='list-group-item list-group-item-danger'><i class='fas fa-times me-2'></i>$required_table (مفقود)</li>";
                }
            }
            echo "</ul>";
            
            if (!empty($missing_tables)) {
                echo "<div class='alert alert-danger mt-3'>";
                echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>جداول مفقودة!</h6>";
                echo "<p>عدد الجداول المفقودة: " . count($missing_tables) . "</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-success mt-3'>";
                echo "<h6><i class='fas fa-check-circle me-2'></i>جميع الجداول المطلوبة موجودة!</h6>";
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h6><i class='fas fa-times me-2'></i>فشل الاتصال بقاعدة البيانات '$dbname'!</h6>";
            echo "<p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
    // فحص ملف SQL
    echo "<h5>5. فحص ملف station.sql</h5>";
    
    $sql_file = 'station.sql';
    if (file_exists($sql_file)) {
        $file_size = filesize($sql_file);
        $file_size_mb = round($file_size / 1024 / 1024, 2);
        echo "<div class='alert alert-success'>";
        echo "<h6><i class='fas fa-file-alt me-2'></i>ملف station.sql موجود</h6>";
        echo "<p>حجم الملف: " . number_format($file_size) . " بايت ($file_size_mb MB)</p>";
        echo "</div>";
        
        // قراءة عينة من الملف
        $sample = file_get_contents($sql_file, false, null, 0, 1000);
        echo "<h6>عينة من محتوى الملف:</h6>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars($sample);
        if ($file_size > 1000) {
            echo "\n... (باقي الملف)";
        }
        echo "</pre>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>ملف station.sql غير موجود!</h6>";
        echo "<p>يجب وضع ملف station.sql في مجلد playgood</p>";
        echo "</div>";
    }
    
    // الخلاصة والحلول
    echo "<h5>6. الخلاصة والحلول</h5>";
    
    if (!$station_exists) {
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>المشكلة: قاعدة البيانات غير موجودة</h6>";
        echo "<p><strong>الحل:</strong> إنشاء قاعدة البيانات من ملف SQL</p>";
        echo "</div>";
        
        if (file_exists($sql_file)) {
            echo "<div class='text-center'>";
            echo "<a href='create_database_from_sql.php' class='btn btn-primary btn-lg me-2'>";
            echo "<i class='fas fa-database me-1'></i>إنشاء قاعدة البيانات من ملف SQL";
            echo "</a>";
            echo "</div>";
        }
        
    } elseif (!empty($missing_tables)) {
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>المشكلة: جداول مفقودة</h6>";
        echo "<p><strong>الحل:</strong> إعادة إنشاء الجداول من ملف SQL</p>";
        echo "</div>";
        
        echo "<div class='text-center'>";
        echo "<a href='create_database_from_sql.php' class='btn btn-warning btn-lg me-2'>";
        echo "<i class='fas fa-redo me-1'></i>إعادة إنشاء الجداول";
        echo "</a>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h6><i class='fas fa-check-circle me-2'></i>قاعدة البيانات سليمة!</h6>";
        echo "<p>جميع الجداول المطلوبة موجودة ويمكن استخدام النظام</p>";
        echo "</div>";
        
        echo "<div class='text-center'>";
        echo "<a href='client/dashboard.php' class='btn btn-success btn-lg me-2'>";
        echo "<i class='fas fa-home me-1'></i>الذهاب للوحة التحكم";
        echo "</a>";
        echo "<a href='reset_and_fix_database.php' class='btn btn-info btn-lg'>";
        echo "<i class='fas fa-tools me-1'></i>تشغيل إصلاحات إضافية";
        echo "</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ غير متوقع!</h5>";
    echo "<p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>

<script>
function createDatabase() {
    if (confirm('هل تريد إنشاء قاعدة البيانات station؟')) {
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=create_database'
        })
        .then(response => response.text())
        .then(data => {
            location.reload();
        })
        .catch(error => {
            alert('حدث خطأ: ' + error);
        });
    }
}
</script>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";

// معالجة طلب إنشاء قاعدة البيانات
if (isset($_POST['action']) && $_POST['action'] === 'create_database') {
    try {
        // قراءة الإعدادات مرة أخرى
        $config_content = file_get_contents('config/database.php');
        preg_match("/define\\('DB_HOST',\\s*'([^']+)'/", $config_content, $host_match);
        preg_match("/define\\('DB_USER',\\s*'([^']+)'/", $config_content, $username_match);
        preg_match("/define\\('DB_PASS',\\s*'([^']*)'/", $config_content, $password_match);
        preg_match("/define\\('DB_NAME',\\s*'([^']+)'/", $config_content, $dbname_match);

        $host = $host_match[1] ?? 'localhost';
        $username = $username_match[1] ?? 'root';
        $password = $password_match[1] ?? '';
        $dbname = $dbname_match[1] ?? 'station';

        $pdo_server = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo_server->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "تم إنشاء قاعدة البيانات بنجاح";
    } catch (PDOException $e) {
        echo "فشل في إنشاء قاعدة البيانات: " . $e->getMessage();
    }
    exit;
}
?>
