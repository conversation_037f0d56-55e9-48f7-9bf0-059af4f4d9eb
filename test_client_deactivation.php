<?php
/**
 * اختبار وظيفة إلغاء تفعيل العميل وإنهاء جلسات الموظفين
 */

require_once 'config/database.php';

echo "<h2>اختبار وظيفة إلغاء تفعيل العميل وإنهاء جلسات الموظفين</h2>";

// دالة لإنهاء جلسات جميع موظفي العميل المعطل (نسخة من admin/clients.php)
function invalidateClientEmployeeSessions($pdo, $client_id) {
    try {
        // إنشاء ملف لتتبع الجلسات المنتهية
        $invalidated_sessions_file = 'temp/invalidated_sessions.json';
        
        // إنشاء مجلد temp إذا لم يكن موجوداً
        if (!file_exists('temp')) {
            mkdir('temp', 0755, true);
        }
        
        // جلب معرفات جميع موظفي العميل
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE client_id = ?");
        $stmt->execute([$client_id]);
        $employees = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($employees)) {
            // قراءة الجلسات المنتهية الحالية
            $invalidated_sessions = [];
            if (file_exists($invalidated_sessions_file)) {
                $content = file_get_contents($invalidated_sessions_file);
                $invalidated_sessions = json_decode($content, true) ?: [];
            }
            
            // إضافة معرفات الموظفين إلى قائمة الجلسات المنتهية
            foreach ($employees as $employee_id) {
                $invalidated_sessions[$employee_id] = [
                    'invalidated_at' => time(),
                    'client_id' => $client_id,
                    'reason' => 'client_deactivated'
                ];
            }
            
            // حفظ الجلسات المنتهية
            file_put_contents($invalidated_sessions_file, json_encode($invalidated_sessions, JSON_PRETTY_PRINT));
            
            echo "<p>✅ تم إنهاء جلسات " . count($employees) . " موظف للعميل رقم {$client_id}</p>";
            echo "<p>معرفات الموظفين: " . implode(', ', $employees) . "</p>";
        } else {
            echo "<p>⚠️ لا يوجد موظفين للعميل رقم {$client_id}</p>";
        }
        
        return true;
    } catch (Exception $e) {
        echo "<p>❌ خطأ في إنهاء جلسات الموظفين: " . $e->getMessage() . "</p>";
        return false;
    }
}

// دالة للتحقق من الجلسات المنتهية
function checkInvalidatedSessions() {
    $invalidated_sessions_file = 'temp/invalidated_sessions.json';
    
    if (!file_exists($invalidated_sessions_file)) {
        echo "<p>📄 لا يوجد ملف جلسات منتهية</p>";
        return;
    }
    
    $content = file_get_contents($invalidated_sessions_file);
    $invalidated_sessions = json_decode($content, true) ?: [];
    
    if (empty($invalidated_sessions)) {
        echo "<p>📄 لا توجد جلسات منتهية</p>";
        return;
    }
    
    echo "<h3>الجلسات المنتهية:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>معرف الموظف</th><th>معرف العميل</th><th>السبب</th><th>وقت الإنهاء</th></tr>";
    
    foreach ($invalidated_sessions as $employee_id => $session_data) {
        $invalidated_time = date('Y-m-d H:i:s', $session_data['invalidated_at']);
        echo "<tr>";
        echo "<td>{$employee_id}</td>";
        echo "<td>{$session_data['client_id']}</td>";
        echo "<td>{$session_data['reason']}</td>";
        echo "<td>{$invalidated_time}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// اختبار الوظيفة
try {
    // جلب عميل للاختبار
    $stmt = $pdo->query("SELECT client_id, business_name FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if ($client) {
        echo "<h3>اختبار مع العميل: {$client['business_name']} (ID: {$client['client_id']})</h3>";
        
        // عرض الجلسات المنتهية الحالية
        echo "<h4>الجلسات المنتهية قبل الاختبار:</h4>";
        checkInvalidatedSessions();
        
        // تشغيل دالة إنهاء الجلسات
        echo "<h4>تشغيل دالة إنهاء الجلسات:</h4>";
        invalidateClientEmployeeSessions($pdo, $client['client_id']);
        
        // عرض الجلسات المنتهية بعد الاختبار
        echo "<h4>الجلسات المنتهية بعد الاختبار:</h4>";
        checkInvalidatedSessions();
        
    } else {
        echo "<p>❌ لا يوجد عملاء في قاعدة البيانات للاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='admin/clients.php'>العودة إلى إدارة العملاء</a></p>";
?>
