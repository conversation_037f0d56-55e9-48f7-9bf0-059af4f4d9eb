<?php
/**
 * إصلاح أسعار الأجهزة - PlayGood
 * التأكد من وجود أسعار فردي وزوجي لجميع الأجهزة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>💰 إصلاح أسعار الأجهزة للعب الفردي والزوجي</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص الأجهزة الحالية وأسعارها
    echo "<h2>1. فحص الأجهزة الحالية وأسعارها</h2>";
    
    $devices = $pdo->query("
        SELECT device_id, device_name, device_type, status, hourly_rate, single_rate, multi_rate
        FROM devices 
        WHERE client_id = 1 
        ORDER BY device_id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>اسم الجهاز</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>السعر العام</th>";
        echo "<th style='padding: 8px;'>السعر الفردي</th>";
        echo "<th style='padding: 8px;'>السعر الزوجي</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        $needs_fix = [];
        
        foreach ($devices as $device) {
            $row_class = '';
            $status_text = '';
            
            if (empty($device['single_rate']) || $device['single_rate'] <= 0 || 
                empty($device['multi_rate']) || $device['multi_rate'] <= 0) {
                $needs_fix[] = $device;
                $row_class = 'style="background: #fff3cd;"';
                $status_text = '⚠️ يحتاج إصلاح';
            } else {
                $row_class = 'style="background: #d4edda;"';
                $status_text = '✅ جيد';
            }
            
            echo "<tr $row_class>";
            echo "<td style='padding: 8px;'>{$device['device_id']}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td style='padding: 8px;'>{$device['device_type']}</td>";
            echo "<td style='padding: 8px;'>" . number_format($device['hourly_rate'], 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>" . number_format($device['single_rate'] ?? 0, 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>" . number_format($device['multi_rate'] ?? 0, 2) . " ج.م</td>";
            echo "<td style='padding: 8px;'>$status_text</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>إجمالي الأجهزة: " . count($devices) . "</p>";
        echo "<p>الأجهزة التي تحتاج إصلاح: " . count($needs_fix) . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ لا توجد أجهزة في قاعدة البيانات</p>";
    }
    
    // 2. إصلاح الأسعار المفقودة
    if (!empty($needs_fix)) {
        echo "<h2>2. إصلاح الأسعار المفقودة</h2>";
        
        // أسعار افتراضية حسب نوع الجهاز
        $default_rates = [
            'PS5' => ['single' => 25.00, 'multi' => 40.00],
            'PS4' => ['single' => 20.00, 'multi' => 35.00],
            'PC' => ['single' => 30.00, 'multi' => 50.00],
            'Xbox' => ['single' => 25.00, 'multi' => 40.00],
            'XBOX' => ['single' => 25.00, 'multi' => 40.00],
            'SWITCH' => ['single' => 20.00, 'multi' => 30.00],
            'VR' => ['single' => 35.00, 'multi' => 55.00]
        ];
        
        $fixed_count = 0;
        
        foreach ($needs_fix as $device) {
            $device_type = strtoupper($device['device_type']);
            $rates = $default_rates[$device_type] ?? ['single' => 25.00, 'multi' => 40.00];
            
            // استخدام السعر العام كأساس إذا كان متوفراً
            if ($device['hourly_rate'] > 0) {
                $single_rate = $device['single_rate'] > 0 ? $device['single_rate'] : $device['hourly_rate'];
                $multi_rate = $device['multi_rate'] > 0 ? $device['multi_rate'] : ($device['hourly_rate'] * 1.6);
            } else {
                $single_rate = $rates['single'];
                $multi_rate = $rates['multi'];
            }
            
            // تحديث قاعدة البيانات
            $update_stmt = $pdo->prepare("
                UPDATE devices 
                SET single_rate = ?, multi_rate = ?, hourly_rate = GREATEST(hourly_rate, ?)
                WHERE device_id = ?
            ");
            
            if ($update_stmt->execute([$single_rate, $multi_rate, $single_rate, $device['device_id']])) {
                echo "<p style='color: green;'>✅ تم إصلاح أسعار: " . htmlspecialchars($device['device_name']) . 
                     " → فردي: {$single_rate} ج.م، زوجي: {$multi_rate} ج.م</p>";
                $fixed_count++;
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح: " . htmlspecialchars($device['device_name']) . "</p>";
            }
        }
        
        echo "<p><strong>تم إصلاح $fixed_count جهاز</strong></p>";
    } else {
        echo "<h2>2. جميع الأسعار صحيحة ✅</h2>";
    }
    
    // 3. إضافة أجهزة تجريبية إذا كان العدد قليل
    $device_count = count($devices);
    if ($device_count < 3) {
        echo "<h2>3. إضافة أجهزة تجريبية</h2>";
        echo "<p style='color: orange;'>⚠️ عدد الأجهزة قليل ($device_count)، سيتم إضافة أجهزة تجريبية</p>";
        
        $sample_devices = [
            ['PlayStation 5 - الرئيسي', 'PS5', 'available', 25.00, 40.00],
            ['PlayStation 4 - الثانوي', 'PS4', 'available', 20.00, 35.00],
            ['Gaming PC - المتقدم', 'PC', 'available', 30.00, 50.00],
            ['Xbox Series X', 'Xbox', 'available', 25.00, 40.00]
        ];
        
        $insert_stmt = $pdo->prepare("
            INSERT INTO devices (device_name, device_type, status, hourly_rate, single_rate, multi_rate, client_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $added_count = 0;
        foreach ($sample_devices as $device) {
            if ($insert_stmt->execute([
                $device[0], $device[1], $device[2], 
                $device[3], $device[3], $device[4], 1
            ])) {
                echo "<p style='color: green;'>✅ تم إضافة جهاز: {$device[0]} (فردي: {$device[3]} ج.م، زوجي: {$device[4]} ج.م)</p>";
                $added_count++;
            }
        }
        
        echo "<p><strong>تم إضافة $added_count جهاز جديد</strong></p>";
    }
    
    // 4. عرض النتيجة النهائية
    echo "<h2>4. النتيجة النهائية</h2>";
    
    $final_devices = $pdo->query("
        SELECT device_id, device_name, device_type, single_rate, multi_rate, status
        FROM devices 
        WHERE client_id = 1 
        ORDER BY device_id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #d4edda;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>اسم الجهاز</th>";
    echo "<th style='padding: 8px;'>النوع</th>";
    echo "<th style='padding: 8px;'>السعر الفردي</th>";
    echo "<th style='padding: 8px;'>السعر الزوجي</th>";
    echo "<th style='padding: 8px;'>الحالة</th>";
    echo "</tr>";
    
    $all_good = true;
    foreach ($final_devices as $device) {
        $is_good = $device['single_rate'] > 0 && $device['multi_rate'] > 0;
        if (!$is_good) $all_good = false;
        
        $row_style = $is_good ? 'background: #d4edda;' : 'background: #f8d7da;';
        
        echo "<tr style='$row_style'>";
        echo "<td style='padding: 8px;'>{$device['device_id']}</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
        echo "<td style='padding: 8px;'>{$device['device_type']}</td>";
        echo "<td style='padding: 8px;'>" . number_format($device['single_rate'], 2) . " ج.م</td>";
        echo "<td style='padding: 8px;'>" . number_format($device['multi_rate'], 2) . " ج.م</td>";
        echo "<td style='padding: 8px;'>" . ($is_good ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($all_good) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin: 0;'>🎉 تم الإصلاح بنجاح!</h3>";
        echo "<p style='margin: 10px 0 0 0;'>جميع الأجهزة لديها أسعار صحيحة للعب الفردي والزوجي</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin: 0;'>⚠️ لا تزال هناك مشاكل</h3>";
        echo "<p style='margin: 10px 0 0 0;'>بعض الأجهزة لا تزال تحتاج إصلاح</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ حدث خطأ</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<div style='margin: 20px 0; text-align: center;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل لصفحة الجلسات</a>";
echo "</div>";
?>
