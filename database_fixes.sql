-- إصلاح<PERSON><PERSON> قاعدة البيانات للمشروع PlayGood
-- تشغيل هذا الملف لإضافة الأعمدة والجداول المفقودة

-- إضافة عمود client_id إلى جدول sessions إذا لم يكن موجوداً
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS client_id INT NOT NULL DEFAULT 1,
ADD COLUMN IF NOT EXISTS total_cost DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS customer_id INT NULL,
ADD COLUMN IF NOT EXISTS created_by INT NULL,
ADD COLUMN IF NOT EXISTS updated_by INT NULL;

-- إضافة فهارس لتحسين الأداء
ALTER TABLE sessions 
ADD INDEX IF NOT EXISTS idx_sessions_client_id (client_id),
ADD INDEX IF NOT EXISTS idx_sessions_status (status),
ADD INDEX IF NOT EXISTS idx_sessions_device_id (device_id),
ADD INDEX IF NOT EXISTS idx_sessions_start_time (start_time);

-- إضا<PERSON>ة عمود client_id إلى جدول cafeteria_items إذا لم يكن موجوداً
ALTER TABLE cafeteria_items 
ADD COLUMN IF NOT EXISTS client_id INT NOT NULL DEFAULT 1;

-- إضافة فهرس لجدول cafeteria_items
ALTER TABLE cafeteria_items 
ADD INDEX IF NOT EXISTS idx_cafeteria_client_id (client_id);

-- إضافة عمود client_id إلى جدول categories إذا لم يكن موجوداً
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS client_id INT NOT NULL DEFAULT 1;

-- إضافة فهرس لجدول categories
ALTER TABLE categories 
ADD INDEX IF NOT EXISTS idx_categories_client_id (client_id);

-- إنشاء جدول session_products إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS session_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_products_session_id (session_id),
    INDEX idx_session_products_product_id (product_id),
    FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE
);

-- إنشاء جدول order_items لربط المنتجات بالأوردرات المستقلة
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_items_order_id (order_id),
    INDEX idx_order_items_product_id (product_id),
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE
);

-- تحديث جدول orders لإضافة حقول مفيدة
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS created_by INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(10,2) DEFAULT 0.00;

-- إنشاء جدول invoices إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    time_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    products_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    client_id INT NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_invoices_session_id (session_id),
    INDEX idx_invoices_client_id (client_id),
    INDEX idx_invoices_invoice_number (invoice_number),
    FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- تحديث البيانات الموجودة لربطها بالعميل الافتراضي
-- (يجب تعديل هذا حسب بنية قاعدة البيانات الفعلية)

-- تحديث sessions لربطها بالعميل من خلال الجهاز
UPDATE sessions s 
JOIN devices d ON s.device_id = d.device_id 
SET s.client_id = d.client_id 
WHERE s.client_id IS NULL OR s.client_id = 0;

-- تحديث cafeteria_items لربطها بالعميل الافتراضي
UPDATE cafeteria_items 
SET client_id = 1 
WHERE client_id IS NULL OR client_id = 0;

-- تحديث categories لربطها بالعميل الافتراضي
UPDATE categories 
SET client_id = 1 
WHERE client_id IS NULL OR client_id = 0;

-- إضافة أعمدة إضافية لجدول employees إذا لم تكن موجودة
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS last_logout TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_locked BOOLEAN DEFAULT FALSE;

-- إضافة فهارس لجدول employees
ALTER TABLE employees 
ADD INDEX IF NOT EXISTS idx_employees_client_id (client_id),
ADD INDEX IF NOT EXISTS idx_employees_username (username),
ADD INDEX IF NOT EXISTS idx_employees_is_active (is_active);

-- إضافة أعمدة إضافية لجدول devices إذا لم تكن موجودة
ALTER TABLE devices 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- إضافة أعمدة إضافية لجدول rooms إذا لم تكن موجودة
ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- إضافة أعمدة إضافية لجدول customers إذا لم تكن موجودة
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- تحسين أداء الاستعلامات بإضافة فهارس مركبة
ALTER TABLE sessions 
ADD INDEX IF NOT EXISTS idx_sessions_client_status (client_id, status),
ADD INDEX IF NOT EXISTS idx_sessions_device_status (device_id, status);

ALTER TABLE devices 
ADD INDEX IF NOT EXISTS idx_devices_client_status (client_id, status);

-- إنشاء view للجلسات مع تفاصيل كاملة
CREATE OR REPLACE VIEW sessions_detailed AS
SELECT 
    s.*,
    d.device_name,
    d.device_type,
    d.hourly_rate,
    r.room_name,
    c.name as customer_name,
    c.phone as customer_phone,
    cl.business_name,
    TIMESTAMPDIFF(MINUTE, s.start_time, COALESCE(s.end_time, CURRENT_TIMESTAMP)) as duration_minutes,
    CASE 
        WHEN s.status = 'active' THEN 
            (TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) * d.hourly_rate / 60)
        ELSE s.total_cost 
    END as calculated_cost
FROM sessions s
JOIN devices d ON s.device_id = d.device_id
LEFT JOIN rooms r ON d.room_id = r.room_id
LEFT JOIN customers c ON s.customer_id = c.id
LEFT JOIN clients cl ON d.client_id = cl.client_id;

-- إنشاء view للإحصائيات اليومية
CREATE OR REPLACE VIEW daily_stats AS
SELECT 
    d.client_id,
    DATE(s.start_time) as date,
    COUNT(s.session_id) as total_sessions,
    SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_sessions,
    SUM(CASE WHEN s.status = 'completed' THEN 1 ELSE 0 END) as completed_sessions,
    COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as total_income,
    AVG(CASE WHEN s.status = 'completed' THEN TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) ELSE NULL END) as avg_session_duration
FROM sessions s
JOIN devices d ON s.device_id = d.device_id
GROUP BY d.client_id, DATE(s.start_time);

-- تحديث البيانات النهائي
-- تأكد من أن جميع الجلسات لها client_id صحيح
UPDATE sessions s 
JOIN devices d ON s.device_id = d.device_id 
SET s.client_id = d.client_id 
WHERE s.client_id != d.client_id OR s.client_id IS NULL;

-- رسالة نجاح
SELECT 'تم تطبيق جميع الإصلاحات بنجاح!' as message;
