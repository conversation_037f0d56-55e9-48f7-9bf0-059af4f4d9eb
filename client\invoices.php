<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

$page_title = "إدارة الفواتير";
$active_page = "invoices";

// معالجة الفلاتر
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// بناء استعلام الفواتير
$where_conditions = ["d.client_id = ?"];
$params = [$client_id];

if ($status_filter && in_array($status_filter, ['pending', 'paid', 'cancelled'])) {
    $where_conditions[] = "i.payment_status = ?";
    $params[] = $status_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(i.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(i.created_at) <= ?";
    $params[] = $date_to;
}

if ($search) {
    $where_conditions[] = "(i.invoice_number LIKE ? OR c.name LIKE ? OR d.device_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // جلب الفواتير مع التفاصيل
    $invoices_query = $pdo->prepare("
        SELECT 
            i.*,
            s.session_id,
            s.start_time,
            s.end_time,
            d.device_name,
            d.device_type,
            r.room_name,
            c.name as customer_name,
            c.phone as customer_phone,
            TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE $where_clause
        ORDER BY i.created_at DESC
        LIMIT 50
    ");
    
    $invoices_query->execute($params);
    $invoices = $invoices_query->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الفواتير
    $stats_query = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            SUM(CASE WHEN i.payment_status = 'paid' THEN i.total_cost ELSE 0 END) as paid_amount,
            SUM(CASE WHEN i.payment_status = 'pending' THEN i.total_cost ELSE 0 END) as pending_amount,
            COUNT(CASE WHEN i.payment_status = 'paid' THEN 1 END) as paid_count,
            COUNT(CASE WHEN i.payment_status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN i.payment_status = 'cancelled' THEN 1 END) as cancelled_count
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
    ");
    $stats_query->execute([$client_id]);
    $stats = $stats_query->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    // تسجيل الخطأ للمطور
    error_log("Invoices page error: " . $e->getMessage());

    // رسالة مبسطة للمستخدم
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $_SESSION['error'] = "جدول الفواتير غير موجود. يرجى تشغيل سكريپت الإصلاح أولاً.";
    } else {
        $_SESSION['error'] = "حدث خطأ في جلب الفواتير. يرجى المحاولة مرة أخرى.";
    }

    $invoices = [];
    $stats = [
        'total_invoices' => 0,
        'paid_amount' => 0,
        'pending_amount' => 0,
        'paid_count' => 0,
        'pending_count' => 0,
        'cancelled_count' => 0
    ];
}

require_once 'includes/header.php';
?>

<style>
.invoice-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.invoice-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85em;
}

.status-pending {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #2d3436;
}

.status-paid {
    background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.filter-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان والإحصائيات -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
        <h1 class="h2 mb-0">
            <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>إدارة الفواتير
        </h1>
        <div class="btn-toolbar">
            <a href="sessions.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-play-circle me-1"></i>الجلسات
            </a>
            <a href="dashboard.php" class="btn btn-primary">
                <i class="fas fa-home me-1"></i>لوحة التحكم
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <i class="fas fa-file-invoice fa-3x mb-3 opacity-75"></i>
                <h3 class="mb-1"><?php echo number_format($stats['total_invoices']); ?></h3>
                <p class="mb-0">إجمالي الفواتير</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <i class="fas fa-check-circle fa-3x mb-3 opacity-75"></i>
                <h3 class="mb-1"><?php echo number_format($stats['paid_count']); ?></h3>
                <p class="mb-0">مدفوعة</p>
                <small class="opacity-75"><?php echo number_format($stats['paid_amount'], 2); ?> ج.م</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <i class="fas fa-clock fa-3x mb-3 opacity-75"></i>
                <h3 class="mb-1"><?php echo number_format($stats['pending_count']); ?></h3>
                <p class="mb-0">معلقة</p>
                <small class="opacity-75"><?php echo number_format($stats['pending_amount'], 2); ?> ج.م</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <i class="fas fa-times-circle fa-3x mb-3 opacity-75"></i>
                <h3 class="mb-1"><?php echo number_format($stats['cancelled_count']); ?></h3>
                <p class="mb-0">ملغية</p>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-card">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">حالة الدفع</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                    <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo $date_from; ?>">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo $date_to; ?>">
            </div>
            <div class="col-md-3">
                <label for="search" class="form-label">بحث</label>
                <div class="input-group">
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="رقم الفاتورة، العميل، الجهاز..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة الفواتير -->
    <div class="row">
        <?php if (count($invoices) > 0): ?>
            <?php foreach ($invoices as $invoice): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card invoice-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-hashtag me-1"></i><?php echo htmlspecialchars($invoice['invoice_number']); ?>
                            </h6>
                            <span class="status-badge status-<?php echo $invoice['payment_status']; ?>">
                                <?php 
                                $status_text = [
                                    'pending' => 'معلق',
                                    'paid' => 'مدفوع',
                                    'cancelled' => 'ملغي'
                                ];
                                echo $status_text[$invoice['payment_status']];
                                ?>
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">العميل:</small>
                                    <div class="fw-bold">
                                        <?php echo $invoice['customer_name'] ? htmlspecialchars($invoice['customer_name']) : 'غير مسجل'; ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الجهاز:</small>
                                    <div class="fw-bold">
                                        <?php
                                        // التأكد من عرض اسم الجهاز وليس اسم العميل
                                        $device_display = $invoice['device_name'] ?? 'غير محدد';
                                        if (empty(trim($device_display)) || $device_display === $invoice['customer_name']) {
                                            $device_display = 'جهاز غير محدد';
                                        }
                                        echo htmlspecialchars($device_display);
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">التاريخ:</small>
                                    <div><?php echo date('Y-m-d', strtotime($invoice['created_at'])); ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">المدة:</small>
                                    <div><?php echo ceil($invoice['duration_minutes'] / 60); ?> ساعة</div>
                                </div>
                            </div>
                            
                            <div class="text-center mb-3">
                                <h4 class="text-primary mb-0"><?php echo number_format($invoice['total_cost'], 2); ?> ج.م</h4>
                                <small class="text-muted">المبلغ الإجمالي</small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex gap-2">
                                <?php
                                // استخدام invoice_id إذا كان موجود، وإلا استخدام session_id
                                $invoice_link = isset($invoice['invoice_id']) && $invoice['invoice_id']
                                    ? "invoice.php?id=" . $invoice['invoice_id']
                                    : "invoice.php?session_id=" . $invoice['session_id'];
                                $invoice_id_for_update = isset($invoice['invoice_id']) && $invoice['invoice_id']
                                    ? $invoice['invoice_id']
                                    : $invoice['session_id'];
                                ?>
                                <a href="<?php echo $invoice_link; ?>" class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <?php if (isset($invoice['invoice_id']) && $invoice['invoice_id']): ?>
                                    <button onclick="updateInvoiceStatus(<?php echo $invoice['invoice_id']; ?>, '<?php echo $invoice['payment_status'] ?? 'pending'; ?>')"
                                            class="btn btn-success btn-sm flex-fill">
                                        <i class="fas fa-edit me-1"></i>تحديث
                                    </button>
                                <?php else: ?>
                                    <span class="btn btn-secondary btn-sm flex-fill disabled">
                                        <i class="fas fa-info-circle me-1"></i>غير متاح
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد فواتير</h4>
                    <?php if (isset($_SESSION['error']) && strpos($_SESSION['error'], 'جدول الفواتير غير موجود') !== false): ?>
                        <p class="text-danger">جدول الفواتير غير موجود في قاعدة البيانات</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يبدو أن جدول الفواتير غير موجود. يرجى تشغيل سكريپت الإصلاح لإنشاء الجداول المطلوبة.
                        </div>
                        <a href="../simple_fix_all.php" class="btn btn-warning me-2">
                            <i class="fas fa-tools me-1"></i>تشغيل سكريپت الإصلاح
                        </a>
                    <?php else: ?>
                        <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
                    <?php endif; ?>
                    <a href="sessions.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>بدء جلسة جديدة
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function updateInvoiceStatus(invoiceId, currentStatus) {
    // نفس الكود من invoice.php
    updatePaymentStatus(invoiceId);
}

function updatePaymentStatus(invoiceId) {
    const options = `
        <div class="text-center">
            <h5 class="mb-3">تحديث حالة الدفع</h5>
            <div class="d-grid gap-2">
                <button class="btn btn-warning" onclick="setPaymentStatus(${invoiceId}, 'pending')">
                    <i class="fas fa-clock me-2"></i>معلق
                </button>
                <button class="btn btn-success" onclick="setPaymentStatus(${invoiceId}, 'paid')">
                    <i class="fas fa-check-circle me-2"></i>تم الدفع
                </button>
                <button class="btn btn-danger" onclick="setPaymentStatus(${invoiceId}, 'cancelled')">
                    <i class="fas fa-times-circle me-2"></i>ملغي
                </button>
            </div>
        </div>
    `;
    
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث حالة الدفع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${options}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function setPaymentStatus(invoiceId, status) {
    fetch('api/update_payment_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            invoice_id: invoiceId,
            payment_status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + (data.error || 'فشل في تحديث حالة الدفع'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>
