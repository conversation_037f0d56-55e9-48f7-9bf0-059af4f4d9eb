<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الأوردر
if (!isset($_GET['order_id']) || !is_numeric($_GET['order_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'معرف الأوردر مطلوب']);
    exit;
}

$order_id = intval($_GET['order_id']);

try {
    // التحقق من وجود عمود customer_id في جدول customers
    $customer_id_column = null;
    try {
        $columns_stmt = $pdo->prepare("SHOW COLUMNS FROM customers LIKE 'customer_id'");
        $columns_stmt->execute();
        if ($columns_stmt->rowCount() > 0) {
            $customer_id_column = 'customer_id';
        }
    } catch (PDOException $e) {
        // في حالة عدم وجود الجدول أو العمود
    }

    // جلب تفاصيل الأوردر
    if ($customer_id_column) {
        $order_query = "
            SELECT 
                o.*,
                c.name as customer_name,
                c.phone as customer_phone,
                c.email as customer_email
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.$customer_id_column
            WHERE o.id = ? AND o.client_id = ?
        ";
    } else {
        $order_query = "
            SELECT 
                o.*,
                NULL as customer_name,
                NULL as customer_phone,
                NULL as customer_email
            FROM orders o
            WHERE o.id = ? AND o.client_id = ?
        ";
    }

    $order_stmt = $pdo->prepare($order_query);
    $order_stmt->execute([$order_id, $client_id]);
    $order = $order_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'الأوردر غير موجود']);
        exit;
    }

    // جلب منتجات الأوردر
    $items_stmt = $pdo->prepare("
        SELECT 
            oi.*,
            ci.name as product_name,
            ci.category as product_category
        FROM order_items oi
        JOIN cafeteria_items ci ON oi.product_id = ci.id
        WHERE oi.order_id = ?
        ORDER BY oi.created_at ASC
    ");
    $items_stmt->execute([$order_id]);
    $items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنسيق البيانات
    $order['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($order['created_at']));
    $order['updated_at_formatted'] = date('Y-m-d H:i:s', strtotime($order['updated_at']));
    $order['total_amount_formatted'] = number_format($order['total_amount'], 2);

    // ترجمة الحالة
    $status_translations = [
        'pending' => 'قيد الانتظار',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    $order['status_text'] = $status_translations[$order['status']] ?? $order['status'];

    // ترجمة طريقة الدفع
    $payment_translations = [
        'cash' => 'نقدي',
        'card' => 'بطاقة',
        'other' => 'أخرى'
    ];
    $order['payment_method_text'] = $payment_translations[$order['payment_method']] ?? $order['payment_method'];

    // تنسيق المنتجات
    foreach ($items as &$item) {
        $item['price_formatted'] = number_format($item['price'], 2);
        $item['total_price_formatted'] = number_format($item['total_price'], 2);
    }

    // حساب الإحصائيات
    $total_items = count($items);
    $total_quantity = array_sum(array_column($items, 'quantity'));

    echo json_encode([
        'success' => true,
        'order' => $order,
        'items' => $items,
        'statistics' => [
            'total_items' => $total_items,
            'total_quantity' => $total_quantity,
            'total_amount' => $order['total_amount']
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء جلب تفاصيل الأوردر: ' . $e->getMessage()
    ]);
}
?>
