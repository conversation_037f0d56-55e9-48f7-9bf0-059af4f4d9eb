<?php
/**
 * إصلاح مشكلة خلط البيانات في العرض - PlayGood
 * حل مشكلة ظهور اسم العميل في مكان التكلفة واسم الجهاز
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشكلة خلط البيانات في العرض</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص وإصلاح بيانات الأجهزة
    echo "<h2>1. فحص وإصلاح بيانات الأجهزة</h2>";
    
    try {
        // البحث عن أجهزة بأسماء مشبوهة (أسماء عربية قد تكون أسماء عملاء)
        $suspicious_devices = $pdo->query("
            SELECT device_id, device_name, device_type, single_rate, multi_rate
            FROM devices 
            WHERE client_id = 1 
            AND (
                device_name REGEXP '^[أ-ي\\s]+$' 
                OR LENGTH(device_name) < 3 
                OR device_name IN (SELECT name FROM customers WHERE client_id = 1)
            )
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>الأجهزة المشبوهة: <strong>" . count($suspicious_devices) . "</strong></p>";
        
        if (count($suspicious_devices) > 0) {
            echo "<h4>الأجهزة التي تحتاج إصلاح:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم الحالي</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>الاسم الجديد</th>";
            echo "<th style='padding: 8px;'>الإجراء</th>";
            echo "</tr>";
            
            $device_names = [
                'PS5' => 'PlayStation 5',
                'PS4' => 'PlayStation 4', 
                'PC' => 'Gaming PC',
                'XBOX' => 'Xbox Series',
                'SWITCH' => 'Nintendo Switch',
                'VR' => 'VR Headset'
            ];
            
            $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
            $counter = 1;
            
            foreach ($suspicious_devices as $device) {
                $device_type = strtoupper($device['device_type']);
                $base_name = $device_names[$device_type] ?? $device_type;
                $new_name = $base_name . ' - ' . $counter;
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($device['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
                echo "<td style='padding: 8px; color: green;'>" . htmlspecialchars($new_name) . "</td>";
                
                try {
                    $update_stmt->execute([$new_name, $device['device_id']]);
                    echo "<td style='padding: 8px; color: green;'>✅ تم التحديث</td>";
                } catch (PDOException $e) {
                    echo "<td style='padding: 8px; color: red;'>❌ خطأ</td>";
                }
                
                echo "</tr>";
                $counter++;
            }
            echo "</table>";
        } else {
            echo "<p style='color: green;'>✅ جميع أسماء الأجهزة تبدو صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص وإصلاح بيانات العملاء
    echo "<h2>2. فحص وإصلاح بيانات العملاء</h2>";
    
    try {
        // البحث عن عملاء بأسماء مشبوهة
        $suspicious_customers = $pdo->query("
            SELECT customer_id, name, phone
            FROM customers 
            WHERE client_id = 1 
            AND (
                name REGEXP '^[A-Za-z0-9\\s]+$' 
                OR name IN (SELECT device_name FROM devices WHERE client_id = 1)
                OR LENGTH(name) < 2
            )
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>العملاء المشبوهين: <strong>" . count($suspicious_customers) . "</strong></p>";
        
        if (count($suspicious_customers) > 0) {
            echo "<h4>العملاء الذين يحتاجون مراجعة:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم</th>";
            echo "<th style='padding: 8px;'>الهاتف</th>";
            echo "<th style='padding: 8px;'>المشكلة</th>";
            echo "</tr>";
            
            foreach ($suspicious_customers as $customer) {
                $issue = '';
                if (preg_match('/^[A-Za-z0-9\s]+$/', $customer['name'])) {
                    $issue = 'اسم بالإنجليزية';
                } elseif (strlen($customer['name']) < 2) {
                    $issue = 'اسم قصير جداً';
                } else {
                    $issue = 'قد يكون اسم جهاز';
                }
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "<td style='padding: 8px; color: orange;'>" . $issue . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: green;'>✅ جميع أسماء العملاء تبدو صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص العملاء: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص الجلسات والعلاقات
    echo "<h2>3. فحص الجلسات والعلاقات</h2>";
    
    try {
        // فحص الجلسات مع JOIN
        $sessions_check = $pdo->query("
            SELECT 
                s.session_id,
                s.device_id,
                s.customer_id,
                d.device_name,
                c.name as customer_name,
                CASE 
                    WHEN d.device_name = c.name THEN 'مشكلة: نفس الاسم'
                    WHEN d.device_name IS NULL THEN 'مشكلة: اسم الجهاز فارغ'
                    WHEN c.name IS NULL THEN 'طبيعي: لا يوجد عميل'
                    ELSE 'طبيعي: أسماء مختلفة'
                END as status
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY s.session_id DESC
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات المفحوصة: <strong>" . count($sessions_check) . "</strong></p>";
        
        if (count($sessions_check) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID الجلسة</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>اسم العميل</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "</tr>";
            
            $problems_count = 0;
            
            foreach ($sessions_check as $session) {
                $is_problem = strpos($session['status'], 'مشكلة') !== false;
                if ($is_problem) $problems_count++;
                
                echo "<tr" . ($is_problem ? " style='background: #ffe6e6;'" : "") . ">";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . htmlspecialchars($session['device_name'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_problem ? "red" : "green") . ";'>" . $session['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>عدد المشاكل المكتشفة:</strong> $problems_count</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 4. إنشاء بيانات تجريبية صحيحة
    echo "<h2>4. إنشاء بيانات تجريبية صحيحة</h2>";
    
    try {
        // التأكد من وجود أجهزة بأسماء صحيحة
        $correct_devices_count = $pdo->query("
            SELECT COUNT(*) 
            FROM devices 
            WHERE client_id = 1 
            AND device_name NOT REGEXP '^[أ-ي\\s]+$'
            AND LENGTH(device_name) >= 3
        ")->fetchColumn();
        
        echo "<p>عدد الأجهزة بأسماء صحيحة: <strong>$correct_devices_count</strong></p>";
        
        if ($correct_devices_count < 3) {
            echo "<p style='color: orange;'>⚠️ سيتم إضافة أجهزة بأسماء صحيحة</p>";
            
            $correct_devices = [
                ['PlayStation 5 Pro', 'PS5', 'available', 30.00, 50.00],
                ['PlayStation 4 Slim', 'PS4', 'available', 25.00, 40.00],
                ['Gaming PC RTX', 'PC', 'available', 35.00, 60.00],
                ['Xbox Series X', 'XBOX', 'available', 30.00, 50.00]
            ];
            
            $insert_device = $pdo->prepare("
                INSERT INTO devices (device_name, device_type, status, single_rate, multi_rate, client_id) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($correct_devices as $device) {
                try {
                    $insert_device->execute([
                        $device[0], $device[1], $device[2], 
                        $device[3], $device[4], 1
                    ]);
                    echo "<p style='color: green;'>✅ تم إضافة جهاز: {$device[0]}</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة {$device[0]}: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        // التأكد من وجود عملاء بأسماء صحيحة
        $correct_customers_count = $pdo->query("
            SELECT COUNT(*) 
            FROM customers 
            WHERE client_id = 1 
            AND name REGEXP '^[أ-ي\\s]+$'
            AND LENGTH(name) >= 3
        ")->fetchColumn();
        
        echo "<p>عدد العملاء بأسماء صحيحة: <strong>$correct_customers_count</strong></p>";
        
        if ($correct_customers_count < 3) {
            echo "<p style='color: orange;'>⚠️ سيتم إضافة عملاء بأسماء صحيحة</p>";
            
            $correct_customers = [
                ['أحمد محمد علي', '01234567890', '<EMAIL>'],
                ['فاطمة حسن محمود', '01987654321', '<EMAIL>'],
                ['محمد عبدالله أحمد', '01122334455', '<EMAIL>'],
                ['سارة علي حسن', '01555666777', '<EMAIL>']
            ];
            
            $insert_customer = $pdo->prepare("
                INSERT INTO customers (name, phone, email, client_id) 
                VALUES (?, ?, ?, ?)
            ");
            
            foreach ($correct_customers as $customer) {
                try {
                    $insert_customer->execute([
                        $customer[0], $customer[1], $customer[2], 1
                    ]);
                    echo "<p style='color: green;'>✅ تم إضافة عميل: {$customer[0]}</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة {$customer[0]}: " . $e->getMessage() . "</p>";
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "</p>";
    }
    
    // 5. اختبار نهائي
    echo "<h2>5. اختبار نهائي للبيانات</h2>";
    
    try {
        // اختبار الجلسات بعد الإصلاح
        $final_test = $pdo->query("
            SELECT 
                s.session_id,
                d.device_name,
                c.name as customer_name,
                d.single_rate,
                TIMESTAMPDIFF(MINUTE, s.start_time, COALESCE(s.end_time, NOW())) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY s.session_id DESC
            LIMIT 3
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($final_test) > 0) {
            echo "<h4>🧪 اختبار العرض النهائي:</h4>";
            
            foreach ($final_test as $test) {
                echo "<div style='border: 1px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f8fff8;'>";
                echo "<h5>جلسة رقم: " . $test['session_id'] . "</h5>";
                
                $device_name = $test['device_name'] ?? 'غير محدد';
                $customer_name = $test['customer_name'] ?? 'غير محدد';
                $duration = $test['duration_minutes'] ?? 0;
                $rate = $test['single_rate'] ?? 0;
                $cost = $duration > 0 && $rate > 0 ? ceil($duration / 60) * $rate : 0;
                
                echo "<p><strong>اسم الجهاز:</strong> <span style='color: blue; font-weight: bold;'>$device_name</span></p>";
                echo "<p><strong>اسم العميل:</strong> <span style='color: red; font-weight: bold;'>$customer_name</span></p>";
                echo "<p><strong>التكلفة المحسوبة:</strong> <span style='color: green; font-weight: bold;'>$cost ج.م</span></p>";
                
                // فحص المشاكل
                $issues = [];
                if ($device_name === $customer_name) $issues[] = "اسم الجهاز = اسم العميل";
                if (empty($device_name) || $device_name === 'غير محدد') $issues[] = "اسم الجهاز فارغ";
                if (!is_numeric($cost)) $issues[] = "التكلفة ليست رقم";
                
                if (count($issues) > 0) {
                    echo "<p style='color: red;'><strong>مشاكل:</strong> " . implode(', ', $issues) . "</p>";
                } else {
                    echo "<p style='color: green;'><strong>✅ لا توجد مشاكل</strong></p>";
                }
                
                echo "</div>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار النهائي: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بعد تشغيل هذا الإصلاح:</h4>";
echo "<ol>";
echo "<li><strong>اختبر صفحة الجلسات:</strong> يجب أن تظهر التكلفة بالأرقام</li>";
echo "<li><strong>اختبر صفحة الفواتير:</strong> يجب أن تظهر أسماء الأجهزة والعملاء صحيحة</li>";
echo "<li><strong>شغل ملف التشخيص:</strong> للتأكد من حل المشاكل</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='debug_display_mix.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ملف التشخيص</a>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/invoices.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الفواتير</a>";
echo "</div>";

echo "</div>";
?>
