<?php
/**
 * ملف التهيئة الشامل لنظام الحماية المحدث
 * يجب تضمين هذا الملف في بداية كل صفحة PHP
 */

// منع الوصول المباشر
if (!defined('SECURITY_BOOTSTRAP')) {
    define('SECURITY_BOOTSTRAP', true);
}

// تضمين إعدادات الأمان المتقدمة
require_once __DIR__ . '/config/security_config.php';
require_once __DIR__ . '/includes/secure_session_middleware.php';

// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تحميل مكونات الحماية
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/database_security.php';
require_once __DIR__ . '/includes/content_security.php';
require_once __DIR__ . '/includes/intrusion_detection.php';
require_once __DIR__ . '/includes/csrf_middleware.php';

// تطبيق headers الأمان
if (isset($csp)) {
    $csp->applySecurityHeaders();
}

// فحص الطلب للتهديدات (إلا للـ APIs العامة)
$current_path = $_SERVER['REQUEST_URI'] ?? '';
$excluded_paths = ['/api/public/', '/webhook/', '/cron/', '/security_test_suite.php'];

$should_scan = true;
foreach ($excluded_paths as $excluded_path) {
    if (strpos($current_path, $excluded_path) !== false) {
        $should_scan = false;
        break;
    }
}

if ($should_scan && isset($ids)) {
    $threats = $ids->scanCurrentRequest();
    
    // تسجيل التهديدات المكتشفة
    if (!empty($threats)) {
        error_log("Security threats detected: " . json_encode([
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'threats' => $threats,
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }
}

/**
 * دالة مساعدة للتحقق من الصلاحيات
 */
function checkPermission($required_permission, $user_type = 'client') {
    if ($user_type === 'admin') {
        return isset($_SESSION['admin_id']);
    } elseif ($user_type === 'client') {
        return isset($_SESSION['client_id']);
    } elseif ($user_type === 'employee') {
        return isset($_SESSION['employee_id']);
    }
    
    return false;
}

/**
 * دالة مساعدة لإعادة التوجيه الآمن
 */
function secureRedirect($url, $permanent = false) {
    // التحقق من أن الـ URL آمن
    $parsed_url = parse_url($url);
    
    // السماح بالـ URLs النسبية فقط أو نفس النطاق
    if (isset($parsed_url['host'])) {
        $current_host = $_SERVER['HTTP_HOST'] ?? '';
        if ($parsed_url['host'] !== $current_host) {
            $url = '/'; // إعادة توجيه للصفحة الرئيسية في حالة نطاق مختلف
        }
    }
    
    $status_code = $permanent ? 301 : 302;
    header("Location: $url", true, $status_code);
    exit;
}

/**
 * دالة مساعدة لتنظيف المخرجات
 */
function secureOutput($content, $allow_html = false) {
    global $csp;
    
    if (isset($csp)) {
        return $csp->sanitizeInput($content, $allow_html);
    }
    
    return htmlspecialchars($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

/**
 * دالة مساعدة لتسجيل الأحداث الأمنية
 */
function logSecurityEvent($event_type, $description, $severity = 'medium') {
    global $security;
    
    if (isset($security)) {
        $ip = $security->getClientIp();
        $security->logSuspiciousActivity($ip, $event_type, $description, $severity);
    }
    
    // تسجيل إضافي في ملف السجل
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'event_type' => $event_type,
        'description' => $description,
        'severity' => $severity
    ];
    
    error_log("SECURITY_EVENT: " . json_encode($log_entry));
}

/**
 * دالة مساعدة للتحقق من قوة كلمة المرور
 */
function validatePasswordStrength($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
    }
    
    // فحص كلمات المرور الشائعة
    $common_passwords = [
        '123456', 'password', '123456789', '12345678', '12345',
        '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
    ];
    
    if (in_array(strtolower($password), $common_passwords)) {
        $errors[] = 'كلمة المرور شائعة جداً، يرجى اختيار كلمة مرور أقوى';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'strength' => calculatePasswordStrength($password)
    ];
}

/**
 * حساب قوة كلمة المرور
 */
function calculatePasswordStrength($password) {
    $score = 0;
    
    // الطول
    $score += min(strlen($password) * 2, 20);
    
    // التنوع في الأحرف
    if (preg_match('/[a-z]/', $password)) $score += 5;
    if (preg_match('/[A-Z]/', $password)) $score += 5;
    if (preg_match('/[0-9]/', $password)) $score += 5;
    if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
    
    // التعقيد
    if (preg_match('/[A-Za-z]/', $password) && preg_match('/[0-9]/', $password)) $score += 10;
    if (preg_match('/[^A-Za-z0-9]/', $password) && strlen($password) >= 8) $score += 15;
    
    // تحديد مستوى القوة
    if ($score < 30) return 'ضعيف';
    if ($score < 60) return 'متوسط';
    if ($score < 80) return 'قوي';
    return 'قوي جداً';
}

/**
 * دالة مساعدة لإنشاء رمز عشوائي آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * دالة مساعدة للتحقق من صحة البريد الإلكتروني
 */
function validateSecureEmail($email) {
    // فحص أساسي
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['valid' => false, 'error' => 'البريد الإلكتروني غير صالح'];
    }
    
    // فحص النطاقات المشبوهة
    $suspicious_domains = [
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
        'mailinator.com', 'throwaway.email'
    ];
    
    $domain = substr(strrchr($email, "@"), 1);
    if (in_array(strtolower($domain), $suspicious_domains)) {
        return ['valid' => false, 'error' => 'البريد الإلكتروني من نطاق مؤقت غير مسموح'];
    }
    
    return ['valid' => true];
}

/**
 * دالة مساعدة لتنظيف رقم الهاتف
 */
function sanitizePhoneNumber($phone) {
    // إزالة جميع الأحرف غير الرقمية
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // التحقق من صحة الرقم
    if (strlen($phone) < 10 || strlen($phone) > 15) {
        return null;
    }
    
    return $phone;
}

// تسجيل بداية الجلسة الآمنة
if (isset($_SESSION['admin_id']) || isset($_SESSION['client_id']) || isset($_SESSION['employee_id'])) {
    // تحديث آخر نشاط
    $_SESSION['last_activity'] = time();
    
    // فحص انتهاء صلاحية الجلسة
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity'] > 3600)) {
        
        // انتهت صلاحية الجلسة
        session_destroy();
        logSecurityEvent('session_expired', 'انتهت صلاحية الجلسة', 'low');
        secureRedirect('/client/login.php');
    }
}

// تنظيف البيانات المدخلة تلقائياً
if (!empty($_POST) && isset($csp)) {
    $_POST = $csp->sanitizeInput($_POST);
}

if (!empty($_GET) && isset($csp)) {
    $_GET = $csp->sanitizeInput($_GET);
}

// إعداد معالج الأخطاء الآمن
set_error_handler(function($severity, $message, $file, $line) {
    // عدم كشف مسارات الملفات في رسائل الخطأ
    $safe_message = "حدث خطأ في النظام";
    
    // تسجيل الخطأ الفعلي في السجل
    error_log("PHP Error: $message in $file on line $line");
    
    // عرض رسالة آمنة للمستخدم
    if (!(error_reporting() & $severity)) {
        return;
    }
    
    // في بيئة التطوير فقط، يمكن عرض تفاصيل أكثر
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        echo "<div class='alert alert-danger'>خطأ: $message</div>";
    }
    
    return true;
});

// إعداد معالج الاستثناءات الآمن
set_exception_handler(function($exception) {
    // تسجيل الاستثناء
    error_log("Uncaught Exception: " . $exception->getMessage() . 
              " in " . $exception->getFile() . 
              " on line " . $exception->getLine());
    
    // عرض صفحة خطأ آمنة
    http_response_code(500);
    
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        echo "<h1>خطأ في النظام</h1>";
        echo "<p>" . htmlspecialchars($exception->getMessage()) . "</p>";
    } else {
        echo "<h1>خطأ في الخادم</h1>";
        echo "<p>نعتذر، حدث خطأ مؤقت. يرجى المحاولة لاحقاً.</p>";
    }
});

// تسجيل تحميل نظام الحماية
logSecurityEvent('security_bootstrap_loaded', 'تم تحميل نظام الحماية بنجاح', 'low');
?>
