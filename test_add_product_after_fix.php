<?php
require_once 'config/database.php';
session_start();

// تعيين client_id للاختبار
$_SESSION['client_id'] = 1;

echo "<h1>اختبار إضافة المنتجات بعد الإصلاح</h1>";

try {
    echo "<h2>1. التحقق من بنية جدول session_products</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_fields = ['unit_price', 'total_price'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        $found = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $field) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $missing_fields[] = $field;
        }
    }
    
    if (empty($missing_fields)) {
        echo "<p style='color: green;'>✅ جميع الحقول المطلوبة موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ الحقول المفقودة: " . implode(', ', $missing_fields) . "</p>";
        echo "<p>يرجى تشغيل ملف الإصلاح أولاً</p>";
        exit;
    }
    
    echo "<h2>2. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة - سأنشئ واحدة</p>";
        
        // البحث عن جهاز
        $stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
        $device = $stmt->fetch();
        
        if ($device) {
            $pdo->exec("INSERT INTO sessions (device_id, status, start_time) VALUES ({$device['device_id']}, 'active', NOW())");
            $session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة: $session_id</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة</p>";
            exit;
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام الجلسة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاء واحد
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات - سأنشئ واحد</p>";
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج اختبار', 7.50, 'مشروبات', 1)");
        $product_id = $pdo->lastInsertId();
        $product_name = 'منتج اختبار';
        $product_price = 7.50;
        echo "<p style='color: green;'>✅ تم إنشاء منتج: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: blue;'>ℹ️ استخدام المنتج: $product_name ($product_id)</p>";
    }
    
    echo "<h2>3. اختبار إضافة منتج عبر API</h2>";
    
    // محاكاة طلب API
    $api_data = [
        'session_id' => $session_id,
        'product_id' => $product_id,
        'quantity' => 3
    ];
    
    echo "<p>بيانات الطلب:</p>";
    echo "<pre>" . json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // استدعاء API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($api_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: ' . $_SERVER['HTTP_COOKIE'] // تمرير الجلسة
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p>كود الاستجابة: $http_code</p>";
    echo "<p>الاستجابة:</p>";
    echo "<pre>" . $response . "</pre>";
    
    $response_data = json_decode($response, true);
    
    if ($response_data && $response_data['success']) {
        echo "<p style='color: green;'>✅ تم إضافة المنتج بنجاح!</p>";
        
        // التحقق من البيانات في قاعدة البيانات
        echo "<h2>4. التحقق من البيانات المحفوظة</h2>";
        
        $stmt = $pdo->prepare("SELECT * FROM session_products WHERE session_id = ? ORDER BY id DESC LIMIT 1");
        $stmt->execute([$session_id]);
        $saved_product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($saved_product) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>الحقل</th><th>القيمة</th></tr>";
            foreach ($saved_product as $key => $value) {
                echo "<tr><td>$key</td><td>$value</td></tr>";
            }
            echo "</table>";
            
            // التحقق من صحة الحسابات
            $expected_total = $product_price * $api_data['quantity'];
            if (abs($saved_product['total_price'] - $expected_total) < 0.01) {
                echo "<p style='color: green;'>✅ الحسابات صحيحة: {$saved_product['total_price']} = $product_price × {$api_data['quantity']}</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في الحسابات: {$saved_product['total_price']} ≠ $expected_total</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لم يتم العثور على البيانات المحفوظة</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة المنتج</p>";
        if ($response_data && isset($response_data['error'])) {
            echo "<p>الخطأ: {$response_data['error']}</p>";
        }
    }
    
    echo "<h2>5. اختبار إضافة منتج مباشرة</h2>";
    
    // اختبار إضافة منتج مباشرة في قاعدة البيانات
    try {
        $quantity = 2;
        $unit_price = $product_price;
        $total_price = $unit_price * $quantity;
        
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);
        
        $direct_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إضافة منتج مباشرة برقم: $direct_id</p>";
        
        // حذف السجل التجريبي
        $pdo->exec("DELETE FROM session_products WHERE id = $direct_id");
        echo "<p style='color: blue;'>ℹ️ تم حذف السجل التجريبي</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل في الإضافة المباشرة: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. الخلاصة</h2>";
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>نتائج الاختبار:</h3>";
    echo "<ul>";
    echo "<li>بنية الجدول: ✅ صحيحة</li>";
    echo "<li>API إضافة المنتج: " . ($response_data && $response_data['success'] ? "✅ يعمل" : "❌ لا يعمل") . "</li>";
    echo "<li>الإضافة المباشرة: ✅ تعمل</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ حدث خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
