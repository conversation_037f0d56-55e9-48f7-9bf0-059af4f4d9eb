<?php
/**
 * ملف تشغيل نظام الورديات - PlayGood
 * يقوم بإنشاء الجداول والبيانات الأساسية لنظام الورديات
 */

require_once 'config/database.php';

// بدء العملية
echo "<h1>🚀 تشغيل نظام الورديات - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";

try {
    // قراءة ملف SQL
    $sql_file = 'create_shifts_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<h2>📋 خطوات التشغيل:</h2>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    $success_count = 0;
    $error_count = 0;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    foreach ($queries as $index => $query) {
        $query = trim($query);
        
        // تجاهل الاستعلامات الفارغة والتعليقات
        if (empty($query) || strpos($query, '--') === 0 || strpos($query, '/*') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($query);
            $success_count++;
            
            // عرض نوع العملية
            if (stripos($query, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $query, $matches);
                $table_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>$table_name</strong></p>";
            } elseif (stripos($query, 'INSERT') !== false) {
                echo "<p style='color: blue;'>📝 تم إدراج بيانات</p>";
            } elseif (stripos($query, 'CREATE OR REPLACE VIEW') !== false) {
                preg_match('/CREATE OR REPLACE VIEW\s+(\w+)/i', $query, $matches);
                $view_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: purple;'>👁️ تم إنشاء عرض: <strong>$view_name</strong></p>";
            } elseif (stripos($query, 'CREATE PROCEDURE') !== false) {
                preg_match('/CREATE PROCEDURE.*?(\w+)/i', $query, $matches);
                $proc_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: orange;'>⚙️ تم إنشاء إجراء: <strong>$proc_name</strong></p>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>❌ خطأ في الاستعلام " . ($index + 1) . ": " . $e->getMessage() . "</p>";
        }
    }
    
    echo "</div>";
    
    echo "<h2>📊 نتائج التشغيل:</h2>";
    echo "<div style='background: " . ($error_count > 0 ? '#fff3cd' : '#d1edff') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ العمليات الناجحة:</strong> $success_count</p>";
    echo "<p><strong>❌ العمليات الفاشلة:</strong> $error_count</p>";
    echo "</div>";
    
    if ($error_count == 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم تشغيل نظام الورديات بنجاح!</h3>";
        echo "<p>يمكنك الآن استخدام النظام من خلال:</p>";
        echo "<ul>";
        echo "<li><a href='client/shifts.php' target='_blank'>صفحة إدارة الورديات</a></li>";
        echo "<li><a href='client/attendance.php' target='_blank'>صفحة الحضور والانصراف</a></li>";
        echo "<li><a href='client/shift_reports.php' target='_blank'>صفحة تقارير الورديات</a></li>";
        echo "</ul>";
        echo "</div>";
        
        // التحقق من البيانات
        echo "<h2>🔍 التحقق من البيانات:</h2>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        
        // عدد الجداول المنشأة
        $tables = ['shift_templates', 'shifts', 'employee_shifts', 'shift_attendance', 'shift_settings'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<p>📋 جدول <strong>$table</strong>: $count سجل</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ جدول <strong>$table</strong>: غير موجود</p>";
            }
        }
        
        // عدد الصلاحيات المضافة
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM permissions WHERE category = 'shifts'");
            $permissions_count = $stmt->fetchColumn();
            echo "<p>🔐 صلاحيات الورديات: $permissions_count صلاحية</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على صلاحيات الورديات</p>";
        }
        
        // عدد الصفحات المضافة
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM pages WHERE category = 'shifts'");
            $pages_count = $stmt->fetchColumn();
            echo "<p>📄 صفحات الورديات: $pages_count صفحة</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على صفحات الورديات</p>";
        }
        
        echo "</div>";
        
        // نصائح للاستخدام
        echo "<h2>💡 نصائح للبدء:</h2>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<ol>";
        echo "<li><strong>إعداد الصلاحيات:</strong> تأكد من منح الموظفين الصلاحيات المناسبة</li>";
        echo "<li><strong>إنشاء قوالب الورديات:</strong> أنشئ قوالب للورديات المتكررة</li>";
        echo "<li><strong>جدولة الورديات:</strong> ابدأ بإنشاء ورديات لهذا الأسبوع</li>";
        echo "<li><strong>تخصيص الموظفين:</strong> خصص الموظفين للورديات المناسبة</li>";
        echo "<li><strong>بدء التسجيل:</strong> ابدأ تسجيل الحضور والانصراف</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ تحذير: حدثت بعض الأخطاء</h3>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام.</p>";
        echo "<p>يمكنك إعادة تشغيل هذا الملف بعد إصلاح المشاكل.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في التشغيل</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>وجود ملف create_shifts_system.sql</li>";
    echo "<li>صحة اتصال قاعدة البيانات</li>";
    echo "<li>صلاحيات قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr style='margin: 30px 0;'>";
echo "<p style='text-align: center; color: #666;'>";
echo "نظام الورديات المتقدم - PlayGood<br>";
echo "تم التطوير بواسطة Augment Agent<br>";
echo date('Y-m-d H:i:s');
echo "</p>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

div {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

p, li {
    line-height: 1.6;
}

code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>
