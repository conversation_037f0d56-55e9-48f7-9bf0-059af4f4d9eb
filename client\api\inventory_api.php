<?php
require_once '../../config/database.php';
require_once '../includes/auth.php';

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    require_once '../includes/employee-auth.php';
    if (!employeeHasPermission('manage_cafeteria')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'غير مسموح'], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

header('Content-Type: application/json');

try {
    $client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($pdo, $client_id);
            break;
        case 'POST':
            handlePostRequest($pdo, $client_id);
            break;
        case 'PUT':
            handlePutRequest($pdo, $client_id);
            break;
        case 'DELETE':
            handleDeleteRequest($pdo, $client_id);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'طريقة غير مدعومة'], JSON_UNESCAPED_UNICODE);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

function handleGetRequest($pdo, $client_id) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getProductsList($pdo, $client_id);
            break;
        case 'product':
            getProduct($pdo, $client_id, $_GET['id'] ?? 0);
            break;
        case 'movements':
            getInventoryMovements($pdo, $client_id, $_GET['product_id'] ?? 0);
            break;
        case 'low_stock':
            getLowStockProducts($pdo, $client_id);
            break;
        case 'categories':
            getCategories($pdo, $client_id);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
    }
}

function handlePostRequest($pdo, $client_id) {
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $data['action'] ?? '';
    
    switch ($action) {
        case 'add_product':
            addProduct($pdo, $client_id, $data);
            break;
        case 'update_stock':
            updateStock($pdo, $client_id, $data);
            break;
        case 'bulk_update':
            bulkUpdateStock($pdo, $client_id, $data);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'إجراء غير صحيح'], JSON_UNESCAPED_UNICODE);
    }
}

function handlePutRequest($pdo, $client_id) {
    $data = json_decode(file_get_contents('php://input'), true);
    updateProduct($pdo, $client_id, $data);
}

function handleDeleteRequest($pdo, $client_id) {
    $product_id = $_GET['id'] ?? 0;
    deleteProduct($pdo, $client_id, $product_id);
}

function getProductsList($pdo, $client_id) {
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $status = $_GET['status'] ?? '';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $where_conditions = ['client_id = ?'];
    $params = [$client_id];
    
    if (!empty($search)) {
        $where_conditions[] = '(name LIKE ? OR barcode LIKE ? OR supplier LIKE ?)';
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($category)) {
        $where_conditions[] = 'category = ?';
        $params[] = $category;
    }
    
    if (!empty($status)) {
        switch ($status) {
            case 'low_stock':
                $where_conditions[] = 'stock_quantity <= min_stock_level AND stock_quantity > 0';
                break;
            case 'out_of_stock':
                $where_conditions[] = 'stock_quantity <= 0';
                break;
            case 'available':
                $where_conditions[] = 'stock_quantity > min_stock_level';
                break;
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // عدد المنتجات الإجمالي
    $count_sql = "SELECT COUNT(*) FROM cafeteria_items WHERE $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total = $count_stmt->fetchColumn();
    
    // جلب المنتجات
    $sql = "SELECT *, 
            CASE 
                WHEN stock_quantity <= 0 THEN 'out_of_stock'
                WHEN stock_quantity <= min_stock_level THEN 'low_stock'
                WHEN stock_quantity >= max_stock_level THEN 'full_stock'
                ELSE 'available'
            END as stock_status,
            (price - cost_price) as profit_margin,
            ROUND(((price - cost_price) / NULLIF(price, 0)) * 100, 2) as profit_percentage
            FROM cafeteria_items 
            WHERE $where_clause 
            ORDER BY name ASC 
            LIMIT $limit OFFSET $offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'products' => $products,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_products' => $total,
                'per_page' => $limit
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function getProduct($pdo, $client_id, $product_id) {
    $stmt = $pdo->prepare("SELECT * FROM cafeteria_items WHERE id = ? AND client_id = ?");
    $stmt->execute([$product_id, $client_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'المنتج غير موجود'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    echo json_encode(['success' => true, 'data' => $product], JSON_UNESCAPED_UNICODE);
}

function getInventoryMovements($pdo, $client_id, $product_id) {
    $sql = "SELECT im.*, ci.name as product_name, e.name as employee_name
            FROM inventory_movements im
            JOIN cafeteria_items ci ON im.product_id = ci.id
            LEFT JOIN employees e ON im.created_by = e.id
            WHERE im.client_id = ?";
    
    $params = [$client_id];
    
    if ($product_id > 0) {
        $sql .= " AND im.product_id = ?";
        $params[] = $product_id;
    }
    
    $sql .= " ORDER BY im.created_at DESC LIMIT 50";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $movements = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'data' => $movements], JSON_UNESCAPED_UNICODE);
}

function getLowStockProducts($pdo, $client_id) {
    $sql = "SELECT * FROM cafeteria_items 
            WHERE client_id = ? AND (stock_quantity <= 0 OR stock_quantity <= min_stock_level)
            ORDER BY stock_quantity ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$client_id]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'data' => $products], JSON_UNESCAPED_UNICODE);
}

function getCategories($pdo, $client_id) {
    $stmt = $pdo->prepare("SELECT DISTINCT category FROM cafeteria_items WHERE client_id = ? ORDER BY category");
    $stmt->execute([$client_id]);
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo json_encode(['success' => true, 'data' => $categories], JSON_UNESCAPED_UNICODE);
}

function addProduct($pdo, $client_id, $data) {
    // التحقق من البيانات المطلوبة
    $required_fields = ['name', 'price', 'cost_price', 'category'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => "الحقل $field مطلوب"], JSON_UNESCAPED_UNICODE);
            return;
        }
    }
    
    // التحقق من عدم وجود منتج بنفس الاسم
    $check_stmt = $pdo->prepare("SELECT id FROM cafeteria_items WHERE name = ? AND client_id = ?");
    $check_stmt->execute([$data['name'], $client_id]);
    
    if ($check_stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'يوجد منتج بنفس الاسم مسبقاً'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $stmt = $pdo->prepare("INSERT INTO cafeteria_items 
        (name, price, cost_price, category, description, stock_quantity, min_stock_level, max_stock_level, barcode, supplier, client_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        $data['name'],
        floatval($data['price']),
        floatval($data['cost_price']),
        $data['category'],
        $data['description'] ?? '',
        intval($data['stock_quantity'] ?? 0),
        intval($data['min_stock_level'] ?? 5),
        intval($data['max_stock_level'] ?? 100),
        $data['barcode'] ?? '',
        $data['supplier'] ?? '',
        $client_id
    ]);
    
    if ($result) {
        $product_id = $pdo->lastInsertId();
        echo json_encode(['success' => true, 'message' => 'تم إضافة المنتج بنجاح', 'product_id' => $product_id], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'فشل في إضافة المنتج'], JSON_UNESCAPED_UNICODE);
    }
}

function updateProduct($pdo, $client_id, $data) {
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'معرف المنتج مطلوب'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $stmt = $pdo->prepare("UPDATE cafeteria_items SET 
        name = ?, price = ?, cost_price = ?, category = ?, description = ?, 
        min_stock_level = ?, max_stock_level = ?, barcode = ?, supplier = ?
        WHERE id = ? AND client_id = ?");
    
    $result = $stmt->execute([
        $data['name'],
        floatval($data['price']),
        floatval($data['cost_price']),
        $data['category'],
        $data['description'] ?? '',
        intval($data['min_stock_level'] ?? 5),
        intval($data['max_stock_level'] ?? 100),
        $data['barcode'] ?? '',
        $data['supplier'] ?? '',
        $data['id'],
        $client_id
    ]);
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث المنتج بنجاح'], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'فشل في تحديث المنتج'], JSON_UNESCAPED_UNICODE);
    }
}

function updateStock($pdo, $client_id, $data) {
    if (empty($data['product_id']) || !isset($data['new_quantity'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'بيانات غير مكتملة'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // جلب الكمية الحالية
        $stmt = $pdo->prepare("SELECT stock_quantity FROM cafeteria_items WHERE id = ? AND client_id = ?");
        $stmt->execute([$data['product_id'], $client_id]);
        $current_quantity = $stmt->fetchColumn();
        
        if ($current_quantity === false) {
            throw new Exception('المنتج غير موجود');
        }
        
        // تحديث الكمية
        $stmt = $pdo->prepare("UPDATE cafeteria_items SET stock_quantity = ? WHERE id = ? AND client_id = ?");
        $stmt->execute([$data['new_quantity'], $data['product_id'], $client_id]);
        
        $pdo->commit();
        echo json_encode(['success' => true, 'message' => 'تم تحديث المخزن بنجاح'], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

function deleteProduct($pdo, $client_id, $product_id) {
    if (empty($product_id)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'معرف المنتج مطلوب'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM cafeteria_items WHERE id = ? AND client_id = ?");
    $result = $stmt->execute([$product_id, $client_id]);
    
    if ($result && $stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'تم حذف المنتج بنجاح'], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'المنتج غير موجود'], JSON_UNESCAPED_UNICODE);
    }
}
?>
