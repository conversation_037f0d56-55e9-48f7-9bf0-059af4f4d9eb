# نظام التجربة المجانية - PlayGood

## نظرة عامة

تم إنشاء نظام تجربة مجانية متكامل لمنصة PlayGood يسمح للعملاء المحتملين بتجربة النظام لمدة 3 ساعات كاملة مع جميع المميزات الأساسية.

## المميزات الرئيسية

### 🎮 **تجربة كاملة لمدة 3 ساعات**
- تسجيل سريع بدون تأكيد بريد إلكتروني
- وصول فوري لجميع المميزات الأساسية
- عداد زمني مرئي للوقت المتبقي
- حذف تلقائي للبيانات بعد انتهاء المدة

### 📊 **المميزات المتاحة في التجربة**
- **إدارة الأجهزة**: إضافة وإدارة أجهزة الألعاب
- **إدارة العملاء**: إضافة وتتبع العملاء
- **إدارة الجلسات**: بدء وإنهاء جلسات الألعاب
- **إدارة الكافتيريا**: إدارة منتجات ومشروبات الكافتيريا
- **لوحة تحكم تفاعلية**: إحصائيات وتقارير أساسية

### 🔒 **الأمان والخصوصية**
- بيانات منفصلة تماماً عن النظام الرئيسي
- حذف تلقائي للبيانات المنتهية الصلاحية
- تشفير كلمات المرور
- جلسات آمنة ومحمية

## الملفات المُنشأة

### ملفات التكوين
- `config/trial-config.php` - إعدادات النظام التجريبي
- `includes/trial-auth.php` - نظام المصادقة التجريبية

### صفحات المستخدم
- `trial-register.php` - صفحة التسجيل التجريبي
- `trial-login.php` - صفحة تسجيل الدخول التجريبي
- `trial-dashboard.php` - لوحة التحكم التجريبية
- `trial-logout.php` - تسجيل الخروج

### صفحات الإدارة
- `trial-devices.php` - إدارة الأجهزة
- `trial-customers.php` - إدارة العملاء
- `trial-sessions.php` - إدارة الجلسات
- `trial-cafeteria.php` - إدارة الكافتيريا

### أدوات النظام
- `api/trial-cleanup.php` - سكريبت تنظيف البيانات المنتهية الصلاحية

## قاعدة البيانات

### الجداول المُنشأة تلقائياً
1. **trial_clients** - بيانات العملاء التجريبيين
2. **trial_devices** - أجهزة الألعاب التجريبية
3. **trial_customers** - عملاء المحلات التجريبية
4. **trial_sessions** - جلسات الألعاب التجريبية
5. **trial_cafeteria_items** - منتجات الكافتيريا التجريبية

### العلاقات
- جميع الجداول مرتبطة بـ `trial_id` مع `CASCADE DELETE`
- حذف الحساب التجريبي يحذف جميع البيانات المرتبطة تلقائياً

## كيفية الاستخدام

### للمطورين

1. **تشغيل النظام**:
   ```bash
   php -S localhost:8000
   ```

2. **الوصول للتجربة المجانية**:
   - الصفحة الرئيسية: `http://localhost:8000`
   - التسجيل التجريبي: `http://localhost:8000/trial-register.php`
   - تسجيل الدخول التجريبي: `http://localhost:8000/trial-login.php`

3. **تنظيف البيانات يدوياً**:
   ```
   http://localhost:8000/api/trial-cleanup.php
   ```

### للمستخدمين

1. **التسجيل**:
   - اضغط على "تجربة مجانية" من الصفحة الرئيسية
   - أدخل بيانات المحل الأساسية
   - ابدأ التجربة فوراً

2. **استكشاف المميزات**:
   - لوحة التحكم تعرض إحصائيات شاملة
   - إضافة أجهزة وعملاء تجريبيين
   - تجربة بدء وإنهاء الجلسات
   - إدارة منتجات الكافتيريا

## الإعدادات

### في `config/trial-config.php`:
```php
define('TRIAL_DURATION_HOURS', 3); // مدة التجربة بالساعات
define('TRIAL_CLEANUP_INTERVAL', 300); // فترة تنظيف البيانات بالثواني
```

## التنظيف التلقائي

### آلية التنظيف
- يتم تشغيل تنظيف البيانات تلقائياً كل 5 دقائق
- حذف الحسابات المنتهية الصلاحية مع جميع بياناتها
- تنظيف الجلسات المعلقة (أكثر من 24 ساعة)

### إعداد Cron Job (اختياري)
```bash
# تشغيل كل 5 دقائق
*/5 * * * * curl -s http://localhost:8000/api/trial-cleanup.php > /dev/null
```

## السجلات

### ملف السجل
- `logs/trial-cleanup.log` - سجل عمليات التنظيف
- يتم تسجيل جميع عمليات الحذف والأخطاء

### مثال على السجل
```
[2025-06-15 10:30:00] بدء عملية تنظيف البيانات التجريبية
[2025-06-15 10:30:01] حذف الحساب التجريبي: محل الألعاب (<EMAIL>) - انتهت في: 2025-06-15 07:30:00
[2025-06-15 10:30:01] انتهاء عملية التنظيف - تم حذف 1 حساب تجريبي و 0 جلسة معلقة
```

## الأمان

### الحماية المُطبقة
- **عزل البيانات**: جداول منفصلة تماماً عن النظام الرئيسي
- **تشفير كلمات المرور**: باستخدام `password_hash()`
- **التحقق من الجلسات**: فحص صحة الجلسة في كل طلب
- **حذف تلقائي**: منع تراكم البيانات التجريبية

### نصائح الأمان
- تأكد من تشغيل سكريبت التنظيف دورياً
- راقب حجم قاعدة البيانات
- احتفظ بنسخ احتياطية من النظام الرئيسي

## التطوير المستقبلي

### مميزات مقترحة
- **تقارير متقدمة**: إضافة تقارير مالية تفصيلية
- **إشعارات**: تنبيهات قبل انتهاء التجربة
- **تصدير البيانات**: السماح بتصدير البيانات قبل الحذف
- **تمديد التجربة**: إمكانية تمديد فترة التجربة

### تحسينات تقنية
- **API متقدم**: واجهة برمجية للتكامل مع أنظمة أخرى
- **تحليلات**: تتبع استخدام المميزات في التجربة
- **تحسين الأداء**: فهرسة قاعدة البيانات وتحسين الاستعلامات

## الدعم الفني

### المشاكل الشائعة
1. **انتهاء التجربة مبكراً**: تحقق من إعدادات المنطقة الزمنية
2. **عدم حذف البيانات**: تأكد من تشغيل سكريبت التنظيف
3. **مشاكل في قاعدة البيانات**: تحقق من صلاحيات المستخدم

### الحصول على المساعدة
- راجع ملفات السجل في مجلد `logs/`
- تحقق من إعدادات قاعدة البيانات
- تأكد من صحة مسارات الملفات

---

**تم إنشاء النظام بواسطة**: فريق تطوير PlayGood  
**التاريخ**: يونيو 2025  
**الإصدار**: 1.0.0
