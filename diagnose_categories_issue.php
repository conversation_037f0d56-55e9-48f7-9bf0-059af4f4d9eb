<?php
/**
 * تشخيص سريع لمشكلة حذف التصنيفات
 * يفحص المشكلة ويقدم تقريراً مفصلاً
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة حذف التصنيفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h1><i class="fas fa-stethoscope me-2"></i>تشخيص مشكلة حذف التصنيفات</h1>
                </div>
                <div class="card-body">

<?php
try {
    echo "<h2><i class='fas fa-database me-2'></i>1. فحص قاعدة البيانات</h2>";
    
    // فحص الجداول
    $tables = ['categories', 'cafeteria_items'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>جدول $table موجود</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>جدول $table غير موجود!</p>";
        }
    }
    
    echo "<h2><i class='fas fa-link me-2'></i>2. فحص القيود المرجعية</h2>";
    
    $constraints = $pdo->query("
        SELECT
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'cafeteria_items'
        AND REFERENCED_TABLE_NAME = 'categories'
    ")->fetchAll();
    
    if (count($constraints) > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تم العثور على قيود مرجعية:</h5>";
        foreach ($constraints as $constraint) {
            echo "<p><strong>القيد:</strong> {$constraint['CONSTRAINT_NAME']}<br>";
            echo "<strong>العمود:</strong> {$constraint['COLUMN_NAME']} → {$constraint['REFERENCED_COLUMN_NAME']}<br>";
            echo "<strong>قاعدة الحذف:</strong> RESTRICT (افتراضي)</p>";

            echo "<p class='error'><i class='fas fa-exclamation-circle me-2'></i><strong>هذا قد يكون سبب المشكلة!</strong> القيد قد يمنع حذف التصنيفات</p>";
        }
        echo "</div>";
    } else {
        echo "<p class='success'><i class='fas fa-check me-2'></i>لا توجد قيود مرجعية مشكلة</p>";
    }
    
    echo "<h2><i class='fas fa-table me-2'></i>3. فحص بنية البيانات</h2>";
    
    // فحص عينة من المنتجات
    $items = $pdo->query("SELECT id, name, category, category_id FROM cafeteria_items LIMIT 5")->fetchAll();
    
    if (count($items) > 0) {
        echo "<h5>عينة من المنتجات:</h5>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead class='table-dark'><tr><th>المعرف</th><th>الاسم</th><th>التصنيف (نص)</th><th>معرف التصنيف</th></tr></thead>";
        echo "<tbody>";
        
        $uses_name = false;
        $uses_id = false;
        
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . htmlspecialchars($item['category'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($item['category_id'] ?? 'NULL') . "</td>";
            echo "</tr>";
            
            if (!empty($item['category'])) $uses_name = true;
            if (!empty($item['category_id'])) $uses_id = true;
        }
        echo "</tbody></table>";
        
        // تحليل طريقة الربط
        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-info-circle me-2'></i>تحليل طريقة الربط:</h6>";
        if ($uses_name && !$uses_id) {
            echo "<p class='success'>✅ النظام يستخدم أسماء التصنيفات فقط - هذا جيد!</p>";
        } elseif ($uses_id && !$uses_name) {
            echo "<p class='warning'>⚠️ النظام يستخدم معرفات التصنيفات فقط</p>";
        } elseif ($uses_name && $uses_id) {
            echo "<p class='info'>ℹ️ النظام يستخدم كلا الطريقتين</p>";
        } else {
            echo "<p class='error'>❌ لا يوجد ربط واضح بالتصنيفات</p>";
        }
        echo "</div>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد منتجات في الكافتيريا</p>";
    }
    
    echo "<h2><i class='fas fa-list me-2'></i>4. فحص التصنيفات</h2>";
    
    $categories = $pdo->query("SELECT category_id, name, client_id FROM categories ORDER BY name")->fetchAll();
    
    if (count($categories) > 0) {
        echo "<h5>التصنيفات الموجودة:</h5>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead class='table-dark'><tr><th>المعرف</th><th>الاسم</th><th>معرف العميل</th><th>عدد المنتجات</th><th>يمكن حذفه؟</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($categories as $category) {
            // عد المنتجات المرتبطة بهذا التصنيف
            $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM cafeteria_items WHERE category = ? AND client_id = ?");
            $count_stmt->execute([$category['name'], $category['client_id']]);
            $product_count = $count_stmt->fetchColumn();
            
            echo "<tr>";
            echo "<td>{$category['category_id']}</td>";
            echo "<td>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td>{$category['client_id']}</td>";
            echo "<td>$product_count</td>";
            
            if ($product_count > 0) {
                echo "<td class='error'><i class='fas fa-times me-1'></i>لا (يحتوي على $product_count منتج)</td>";
            } else {
                echo "<td class='success'><i class='fas fa-check me-1'></i>نعم</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد تصنيفات</p>";
    }
    
    echo "<h2><i class='fas fa-test-tube me-2'></i>5. اختبار الحذف</h2>";
    
    // إنشاء تصنيف تجريبي
    $test_name = 'تصنيف تجريبي ' . date('H:i:s');
    $stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
    $stmt->execute([$test_name]);
    $test_id = $pdo->lastInsertId();
    
    echo "<p class='info'><i class='fas fa-plus me-2'></i>تم إنشاء تصنيف تجريبي: '$test_name' (معرف: $test_id)</p>";
    
    // محاولة حذف التصنيف
    try {
        $delete_stmt = $pdo->prepare("DELETE FROM categories WHERE category_id = ? AND client_id = 1");
        $delete_stmt->execute([$test_id]);
        
        if ($delete_stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i><strong>نجح الحذف!</strong> المشكلة محلولة</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل الحذف - لم يتم حذف أي صف</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i><strong>فشل الحذف:</strong> " . $e->getMessage() . "</p>";
        
        if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
            echo "<div class='alert alert-danger'>";
            echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>السبب: قيد مرجعي</h6>";
            echo "<p>يوجد قيد مرجعي يمنع حذف التصنيف. يجب إصلاح هذا القيد.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2><i class='fas fa-tools me-2'></i>6. الحلول المقترحة</h2>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5><i class='fas fa-wrench me-2'></i>الحل الأول: إصلاح القيود</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p>تشغيل ملف الإصلاح الشامل:</p>";
    echo "<a href='fix_categories_deletion.php' class='btn btn-primary'>";
    echo "<i class='fas fa-play me-2'></i>تشغيل الإصلاح";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5><i class='fas fa-database me-2'></i>الحل الثاني: SQL مباشر</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p>تشغيل ملف SQL للإصلاح:</p>";
    echo "<a href='fix_categories_constraints.sql' class='btn btn-success' download>";
    echo "<i class='fas fa-download me-2'></i>تحميل ملف SQL";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<h3><i class='fas fa-arrow-right me-2'></i>الخطوات التالية</h3>";
    echo "<ol>";
    echo "<li>قم بتشغيل أحد الحلول أعلاه</li>";
    echo "<li>اختبر حذف التصنيفات في <a href='client/cafeteria.php'>صفحة الكافتيريا</a></li>";
    echo "<li>إذا استمرت المشكلة، راجع سجلات الأخطاء</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ في التشخيص</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
