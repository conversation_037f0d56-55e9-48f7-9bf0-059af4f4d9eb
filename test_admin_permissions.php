<?php
/**
 * ملف اختبار نظام صلاحيات الإدمن - PlayGood
 * يقوم بفحص جميع مكونات النظام للتأكد من عملها بشكل صحيح
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نظام صلاحيات الإدمن</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 1000px; margin-top: 2rem; }
        .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .test-item { margin-bottom: 1rem; padding: 1rem; border-radius: 10px; }
        .test-item.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .test-item.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .test-item.warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .test-item.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        .table { font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header text-center'>
                <h3><i class='fas fa-test-tube me-2'></i>اختبار نظام صلاحيات الإدمن</h3>
            </div>
            <div class='card-body'>";

$tests_passed = 0;
$tests_failed = 0;
$tests_warnings = 0;

try {
    // اختبار 1: فحص الجداول الأساسية
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-database me-2'></i>اختبار الجداول الأساسية</h5></div>
            <div class='card-body'>";
    
    $required_tables = [
        'admin_pages' => 'جدول صفحات الإدمن',
        'admin_page_permissions' => 'جدول صلاحيات الصفحات',
        'admins' => 'جدول المديرين'
    ];
    
    foreach ($required_tables as $table => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='test-item success'>
                        <i class='fas fa-check me-2'></i><strong>$description:</strong> موجود
                      </div>";
                $tests_passed++;
            } else {
                echo "<div class='test-item error'>
                        <i class='fas fa-times me-2'></i><strong>$description:</strong> غير موجود
                      </div>";
                $tests_failed++;
            }
        } catch (Exception $e) {
            echo "<div class='test-item error'>
                    <i class='fas fa-times me-2'></i><strong>$description:</strong> خطأ - " . htmlspecialchars($e->getMessage()) . "
                  </div>";
            $tests_failed++;
        }
    }
    echo "</div></div>";

    // اختبار 2: فحص البيانات الأساسية
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-data me-2'></i>اختبار البيانات الأساسية</h5></div>
            <div class='card-body'>";
    
    // عدد الصفحات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    if ($pages_count > 0) {
        echo "<div class='test-item success'>
                <i class='fas fa-check me-2'></i><strong>صفحات الإدمن:</strong> $pages_count صفحة متاحة
              </div>";
        $tests_passed++;
    } else {
        echo "<div class='test-item error'>
                <i class='fas fa-times me-2'></i><strong>صفحات الإدمن:</strong> لا توجد صفحات متاحة
              </div>";
        $tests_failed++;
    }
    
    // عدد المديرين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins WHERE is_active = 1");
    $admins_count = $stmt->fetch()['count'];
    if ($admins_count > 0) {
        echo "<div class='test-item success'>
                <i class='fas fa-check me-2'></i><strong>المديرين النشطين:</strong> $admins_count مدير
              </div>";
        $tests_passed++;
    } else {
        echo "<div class='test-item warning'>
                <i class='fas fa-exclamation-triangle me-2'></i><strong>المديرين النشطين:</strong> لا يوجد مديرين نشطين
              </div>";
        $tests_warnings++;
    }
    
    echo "</div></div>";

    // اختبار 3: فحص الملفات المطلوبة
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-file-code me-2'></i>اختبار الملفات المطلوبة</h5></div>
            <div class='card-body'>";
    
    $required_files = [
        'admin/includes/admin-permissions.php' => 'ملف دوال الصلاحيات',
        'admin/includes/auth.php' => 'ملف المصادقة المحدث',
        'admin/admin_permissions.php' => 'صفحة إدارة الصلاحيات',
        'create_admin_permissions_system.sql' => 'ملف SQL للنظام'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='test-item success'>
                    <i class='fas fa-check me-2'></i><strong>$description:</strong> موجود
                  </div>";
            $tests_passed++;
        } else {
            echo "<div class='test-item error'>
                    <i class='fas fa-times me-2'></i><strong>$description:</strong> غير موجود
                  </div>";
            $tests_failed++;
        }
    }
    echo "</div></div>";

    // اختبار 4: فحص الدوال
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-cogs me-2'></i>اختبار الدوال</h5></div>
            <div class='card-body'>";
    
    // تحميل ملف الصلاحيات
    if (file_exists('admin/includes/admin-permissions.php')) {
        require_once 'admin/includes/admin-permissions.php';
        
        $functions_to_test = [
            'hasAdminPagePermission' => 'دالة فحص صلاحية الصفحة',
            'getAllowedAdminPages' => 'دالة جلب الصفحات المسموحة',
            'hasAdminRole' => 'دالة فحص دور المدير',
            'getCurrentAdminInfo' => 'دالة جلب معلومات المدير'
        ];
        
        foreach ($functions_to_test as $function => $description) {
            if (function_exists($function)) {
                echo "<div class='test-item success'>
                        <i class='fas fa-check me-2'></i><strong>$description:</strong> متاحة
                      </div>";
                $tests_passed++;
            } else {
                echo "<div class='test-item error'>
                        <i class='fas fa-times me-2'></i><strong>$description:</strong> غير متاحة
                      </div>";
                $tests_failed++;
            }
        }
    } else {
        echo "<div class='test-item error'>
                <i class='fas fa-times me-2'></i><strong>ملف الصلاحيات:</strong> غير موجود - لا يمكن اختبار الدوال
              </div>";
        $tests_failed++;
    }
    echo "</div></div>";

    // اختبار 5: فحص الـ Views والـ Procedures
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-database me-2'></i>اختبار Views والـ Procedures</h5></div>
            <div class='card-body'>";
    
    // فحص View
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM admin_page_permissions_detailed LIMIT 1");
        echo "<div class='test-item success'>
                <i class='fas fa-check me-2'></i><strong>View admin_page_permissions_detailed:</strong> يعمل بشكل صحيح
              </div>";
        $tests_passed++;
    } catch (Exception $e) {
        echo "<div class='test-item error'>
                <i class='fas fa-times me-2'></i><strong>View admin_page_permissions_detailed:</strong> خطأ - " . htmlspecialchars($e->getMessage()) . "
              </div>";
        $tests_failed++;
    }
    
    // فحص Stored Procedures
    try {
        $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name IN ('CheckAdminPagePermission', 'GrantDefaultPermissionsToAdmin')");
        $procedures = $stmt->fetchAll();
        if (count($procedures) >= 2) {
            echo "<div class='test-item success'>
                    <i class='fas fa-check me-2'></i><strong>Stored Procedures:</strong> " . count($procedures) . " إجراء موجود
                  </div>";
            $tests_passed++;
        } else {
            echo "<div class='test-item warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i><strong>Stored Procedures:</strong> " . count($procedures) . " إجراء موجود (متوقع 2)
                  </div>";
            $tests_warnings++;
        }
    } catch (Exception $e) {
        echo "<div class='test-item warning'>
                <i class='fas fa-exclamation-triangle me-2'></i><strong>Stored Procedures:</strong> لا يمكن التحقق - " . htmlspecialchars($e->getMessage()) . "
              </div>";
        $tests_warnings++;
    }
    echo "</div></div>";

    // اختبار 6: عرض الصفحات المتاحة
    echo "<div class='card mb-3'>
            <div class='card-header'><h5><i class='fas fa-list me-2'></i>الصفحات المتاحة في النظام</h5></div>
            <div class='card-body'>";
    
    $stmt = $pdo->query("
        SELECT page_name, page_label, category, required_role, is_default, is_active
        FROM admin_pages 
        ORDER BY category, page_label
    ");
    $pages = $stmt->fetchAll();
    
    if (!empty($pages)) {
        echo "<div class='table-responsive'>
                <table class='table table-striped'>
                    <thead>
                        <tr>
                            <th>اسم الصفحة</th>
                            <th>التسمية</th>
                            <th>الفئة</th>
                            <th>الدور المطلوب</th>
                            <th>افتراضية</th>
                            <th>نشطة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($pages as $page) {
            $role_text = [
                'super_admin' => 'مدير عام',
                'admin' => 'مدير',
                'any' => 'أي دور'
            ];
            
            echo "<tr>
                    <td><code>" . htmlspecialchars($page['page_name']) . "</code></td>
                    <td>" . htmlspecialchars($page['page_label']) . "</td>
                    <td><span class='badge bg-secondary'>" . htmlspecialchars($page['category']) . "</span></td>
                    <td><span class='badge bg-info'>" . ($role_text[$page['required_role']] ?? $page['required_role']) . "</span></td>
                    <td>" . ($page['is_default'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-muted"></i>') . "</td>
                    <td>" . ($page['is_active'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>') . "</td>
                  </tr>";
        }
        
        echo "    </tbody>
                </table>
              </div>";
    } else {
        echo "<div class='test-item warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>لا توجد صفحات في النظام
              </div>";
    }
    echo "</div></div>";

    // النتيجة النهائية
    $total_tests = $tests_passed + $tests_failed + $tests_warnings;
    $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 1) : 0;
    
    echo "<div class='card'>
            <div class='card-header'><h5><i class='fas fa-chart-pie me-2'></i>نتائج الاختبار</h5></div>
            <div class='card-body'>";
    
    echo "<div class='row text-center'>
            <div class='col-md-3'>
                <div class='test-item success'>
                    <h3>$tests_passed</h3>
                    <p>اختبار نجح</p>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='test-item error'>
                    <h3>$tests_failed</h3>
                    <p>اختبار فشل</p>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='test-item warning'>
                    <h3>$tests_warnings</h3>
                    <p>تحذير</p>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='test-item info'>
                    <h3>$success_rate%</h3>
                    <p>معدل النجاح</p>
                </div>
            </div>
          </div>";
    
    if ($tests_failed == 0) {
        echo "<div class='alert alert-success mt-3'>
                <h5><i class='fas fa-check-circle me-2'></i>النظام يعمل بشكل ممتاز!</h5>
                <p>جميع الاختبارات الأساسية نجحت. يمكنك الآن استخدام نظام صلاحيات الإدمن.</p>
              </div>";
    } elseif ($tests_failed <= 2) {
        echo "<div class='alert alert-warning mt-3'>
                <h5><i class='fas fa-exclamation-triangle me-2'></i>النظام يعمل مع بعض المشاكل البسيطة</h5>
                <p>معظم المكونات تعمل بشكل صحيح، لكن هناك بعض المشاكل التي يجب حلها.</p>
              </div>";
    } else {
        echo "<div class='alert alert-danger mt-3'>
                <h5><i class='fas fa-times-circle me-2'></i>النظام يحتاج إلى إصلاح</h5>
                <p>هناك مشاكل كبيرة تحتاج إلى حل قبل استخدام النظام.</p>
              </div>";
    }
    
    echo "</div></div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-circle me-2'></i>خطأ في الاختبار!</h5>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "        </div>
        </div>
        
        <div class='text-center mt-4'>
            <a href='setup_admin_permissions.php' class='btn btn-warning me-2'>
                <i class='fas fa-redo me-2'></i>إعادة تشغيل النظام
            </a>
            <a href='admin/dashboard.php' class='btn btn-primary'>
                <i class='fas fa-arrow-left me-2'></i>الانتقال إلى لوحة التحكم
            </a>
        </div>
    </div>
    
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
