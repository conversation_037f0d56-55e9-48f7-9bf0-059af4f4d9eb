<?php
/**
 * إصلاح بسيط لجدول العملاء - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔧 إصلاح بسيط لجدول العملاء</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // حذف جدول customers إذا كان موجود
    echo "<h2>1. إعادة إنشاء جدول customers</h2>";
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS customers");
        echo "<p style='color: orange;'>⚠️ تم حذف جدول customers القديم</p>";
    } catch (PDOException $e) {
        echo "<p style='color: blue;'>ℹ️ جدول customers غير موجود</p>";
    }
    
    // إنشاء جدول customers جديد بدون مفاتيح خارجية
    echo "<h2>2. إنشاء جدول customers جديد</h2>";
    
    $create_customers_sql = "
        CREATE TABLE customers (
            customer_id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(255) NULL,
            notes TEXT NULL,
            client_id INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_customers_client (client_id),
            INDEX idx_customers_name (name),
            INDEX idx_customers_phone (phone)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($create_customers_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers جديد بنجاح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إنشاء جدول customers: " . $e->getMessage() . "</p>";
        throw $e;
    }
    
    // فحص هيكل الجدول الجديد
    echo "<h2>3. فحص هيكل الجدول الجديد</h2>";
    
    $columns_check = $pdo->query("DESCRIBE customers");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم العمود</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    while ($column = $columns_check->fetch()) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 10px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // إضافة بعض العملاء التجريبيين
    echo "<h2>4. إضافة عملاء تجريبيين</h2>";
    
    $sample_customers = [
        ['أحمد محمد', '01234567890', '<EMAIL>', 'عميل مميز'],
        ['فاطمة علي', '01987654321', '<EMAIL>', 'عميل جديد'],
        ['محمد حسن', '01122334455', null, 'عميل بدون بريد إلكتروني'],
        ['سارة أحمد', '01555666777', '<EMAIL>', null]
    ];
    
    $insert_stmt = $pdo->prepare("
        INSERT INTO customers (name, phone, email, notes, client_id) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $added_count = 0;
    foreach ($sample_customers as $customer) {
        try {
            $insert_stmt->execute([
                $customer[0], // name
                $customer[1], // phone
                $customer[2], // email
                $customer[3], // notes
                1             // client_id
            ]);
            $added_count++;
            echo "<p style='color: green;'>✅ تم إضافة العميل: " . $customer[0] . "</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في إضافة العميل " . $customer[0] . ": " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p style='color: blue;'>ℹ️ تم إضافة $added_count عميل تجريبي</p>";
    
    // اختبار إضافة عميل جديد
    echo "<h2>5. اختبار إضافة عميل جديد</h2>";
    
    try {
        $test_stmt = $pdo->prepare("
            INSERT INTO customers (name, phone, email, notes, client_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $test_data = [
            'عميل اختبار - ' . date('Y-m-d H:i:s'),
            '01999888777',
            '<EMAIL>',
            'عميل تجريبي للاختبار النهائي',
            1
        ];
        
        $test_stmt->execute($test_data);
        $customer_id = $pdo->lastInsertId();
        
        echo "<p style='color: green;'>✅ تم إضافة عميل اختبار بنجاح - ID: $customer_id</p>";
        
        // حذف العميل التجريبي
        $delete_stmt = $pdo->prepare("DELETE FROM customers WHERE customer_id = ?");
        $delete_stmt->execute([$customer_id]);
        echo "<p style='color: blue;'>ℹ️ تم حذف العميل التجريبي</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار إضافة العميل: " . $e->getMessage() . "</p>";
    }
    
    // عرض العملاء الموجودين
    echo "<h2>6. العملاء الموجودين</h2>";
    
    try {
        $customers_stmt = $pdo->query("SELECT * FROM customers ORDER BY created_at DESC");
        $customers = $customers_stmt->fetchAll();
        
        if (count($customers) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>الاسم</th>";
            echo "<th style='padding: 10px;'>الهاتف</th>";
            echo "<th style='padding: 10px;'>البريد</th>";
            echo "<th style='padding: 10px;'>تاريخ الإضافة</th>";
            echo "</tr>";
            
            foreach ($customers as $customer) {
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['email'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 10px;'>" . $customer['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p style='color: green;'>✅ يوجد " . count($customers) . " عميل في النظام</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا يوجد عملاء في النظام</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في جلب العملاء: " . $e->getMessage() . "</p>";
    }
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<ul>";
    echo "<li>✅ تم إنشاء جدول customers جديد بدون مفاتيح خارجية</li>";
    echo "<li>✅ تم إضافة $added_count عميل تجريبي</li>";
    echo "<li>✅ اختبار الإضافة: " . (isset($customer_id) ? "نجح" : "فشل") . "</li>";
    echo "<li>✅ الجدول جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الإصلاح</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر إضافة العملاء الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/customers.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة العملاء</a>";
echo "<a href='fix_customers_foreign_key.php' style='background: #dc3545; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إصلاح المفاتيح الخارجية</a>";
echo "</div>";

echo "</div>";
?>
