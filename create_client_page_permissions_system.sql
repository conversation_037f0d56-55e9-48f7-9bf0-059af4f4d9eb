-- إنشاء نظام صلاحيات الصفحات للعملاء - PlayGood
-- تشغيل هذا الملف لإضافة نظام التحكم في صلاحيات الصفحات للعملاء

-- 1. إنشاء جدول الصفحات المتاحة للعملاء
CREATE TABLE IF NOT EXISTS client_pages (
    page_id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) NOT NULL UNIQUE,
    page_label VARCHAR(200) NOT NULL,
    page_url VARCHAR(255) NOT NULL,
    page_icon VARCHAR(50) DEFAULT 'fas fa-file',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول صلاحيات الصفحات للعملاء (Many-to-Many)
CREATE TABLE IF NOT EXISTS client_page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    page_id INT NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    granted_by INT DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES client_pages(page_id) ON DELETE CASCADE,
    UNIQUE KEY unique_client_page (client_id, page_id)
);

-- 3. إدراج الصفحات المتاحة للعملاء
INSERT IGNORE INTO client_pages (page_name, page_label, page_url, page_icon, category, description, is_default) VALUES
-- الصفحات الأساسية (متاحة افتراضياً)
('dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', TRUE),
('profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', TRUE),

-- إدارة الأجهزة والغرف
('devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', TRUE),
('rooms', 'إدارة الغرف', 'rooms.php', 'fas fa-door-open', 'devices', 'تنظيم الأجهزة في غرف', FALSE),

-- إدارة الجلسات والعملاء
('sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', TRUE),
('customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', TRUE),

-- الكافتيريا والأوردرات
('cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا', FALSE),
('orders', 'إدارة الأوردرات', 'orders.php', 'fas fa-shopping-cart', 'orders', 'إدارة الطلبات المستقلة', FALSE),

-- الموظفين والحضور
('employees', 'إدارة الموظفين', 'employees.php', 'fas fa-user-tie', 'employees', 'إدارة موظفي المحل', FALSE),
('attendance', 'نظام الحضور', 'attendance.php', 'fas fa-clock', 'employees', 'تسجيل حضور وانصراف الموظفين', FALSE),
('shifts', 'إدارة الورديات', 'shifts.php', 'fas fa-calendar-alt', 'employees', 'تنظيم ورديات العمل', FALSE),

-- التقارير والمالية
('reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير مالية وإحصائية', TRUE),
('finances', 'المالية', 'finances.php', 'fas fa-money-bill-wave', 'finances', 'إدارة المصروفات والإيرادات', FALSE),
('revenues', 'الإيرادات', 'revenues.php', 'fas fa-arrow-up', 'finances', 'إدارة وتتبع الإيرادات', FALSE),
('expenses', 'المصروفات', 'expenses.php', 'fas fa-arrow-down', 'finances', 'إدارة وتتبع المصروفات', FALSE),
('invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إدارة وطباعة الفواتير', TRUE),

-- المخزون والحجوزات
('inventory', 'إدارة المخزون', 'inventory.php', 'fas fa-boxes', 'inventory', 'إدارة مخزون المنتجات', FALSE),
('reservations', 'الحجوزات', 'reservations.php', 'fas fa-calendar-check', 'reservations', 'إدارة حجوزات العملاء', FALSE),

-- الإعدادات
('settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والمحل', TRUE),
('invoice_settings', 'إعدادات الفواتير', 'invoice_settings.php', 'fas fa-file-alt', 'settings', 'تخصيص شكل ومحتوى الفواتير', FALSE);

-- 4. إنشاء view لعرض صلاحيات العملاء بشكل مفصل
CREATE OR REPLACE VIEW client_page_permissions_detailed AS
SELECT 
    c.client_id,
    c.business_name,
    c.owner_name,
    c.is_active as client_active,
    cp.page_id,
    cp.page_name,
    cp.page_label,
    cp.page_url,
    cp.page_icon,
    cp.category,
    cp.description,
    cp.is_default,
    COALESCE(cpp.is_enabled, cp.is_default) as has_permission,
    cpp.granted_at,
    cpp.updated_at
FROM clients c
CROSS JOIN client_pages cp
LEFT JOIN client_page_permissions cpp ON c.client_id = cpp.client_id AND cp.page_id = cpp.page_id
WHERE cp.is_active = 1
ORDER BY c.client_id, cp.category, cp.page_label;

-- 5. إنشاء stored procedure لمنح الصلاحيات الافتراضية للعملاء الجدد
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GrantDefaultPermissionsToClient(IN client_id INT)
BEGIN
    INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
    SELECT client_id, page_id, TRUE
    FROM client_pages
    WHERE is_default = TRUE AND is_active = TRUE;
END //
DELIMITER ;

-- 6. إنشاء trigger لمنح الصلاحيات الافتراضية عند إضافة عميل جديد
DELIMITER //
CREATE TRIGGER IF NOT EXISTS after_client_insert
AFTER INSERT ON clients
FOR EACH ROW
BEGIN
    CALL GrantDefaultPermissionsToClient(NEW.client_id);
END //
DELIMITER ;

-- 7. منح الصلاحيات الافتراضية للعملاء الموجودين
INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
SELECT c.client_id, cp.page_id, TRUE
FROM clients c
CROSS JOIN client_pages cp
WHERE cp.is_default = TRUE AND cp.is_active = TRUE;

-- تم الانتهاء من إنشاء نظام صلاحيات الصفحات للعملاء
SELECT 'تم إنشاء نظام صلاحيات الصفحات للعملاء بنجاح!' as message;
