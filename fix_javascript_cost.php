<?php
/**
 * إصلاح JavaScript التكلفة - PlayGood
 * حل مشكلة JavaScript الذي يحدث التكلفة خطأ
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح JavaScript التكلفة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص المشكلة الحالية
    echo "<h2>1. فحص المشكلة الحالية</h2>";
    
    $sessions = $pdo->query("
        SELECT s.session_id, s.start_time, d.device_name, c.name as customer_name, d.single_rate,
               TIMESTAMPDIFF(MINUTE, s.start_time, NOW()) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'active' AND d.client_id = 1
        ORDER BY s.session_id DESC
        LIMIT 3
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الجلسات النشطة: <strong>" . count($sessions) . "</strong></p>";
    
    if (count($sessions) > 0) {
        foreach ($sessions as $session) {
            $duration_minutes = $session['duration_minutes'] ?? 0;
            $hourly_rate = $session['single_rate'] ?? 0;
            $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
            
            echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 15px 0; border-radius: 8px;'>";
            echo "<h4>جلسة رقم: " . $session['session_id'] . "</h4>";
            echo "<p><strong>اسم الجهاز:</strong> <span style='color: blue;'>" . htmlspecialchars($session['device_name']) . "</span></p>";
            echo "<p><strong>اسم العميل:</strong> <span style='color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</span></p>";
            echo "<p><strong>التكلفة المحسوبة في PHP:</strong> <span style='background: yellow; padding: 5px; font-weight: bold;'>$time_cost ج.م</span></p>";
            
            // عنصر التكلفة الفعلي
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h5>عنصر التكلفة الفعلي:</h5>";
            echo "<div class='session-cost fs-5 fw-bold text-primary' data-session-id='" . $session['session_id'] . "' id='cost_" . $session['session_id'] . "'>";
            echo number_format($time_cost, 2) . " ج.م";
            echo "</div>";
            echo "</div>";
            
            // اختبار JavaScript
            echo "<button onclick='testCostUpdate(" . $session['session_id'] . ", \"" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "\", " . $time_cost . ")' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>اختبار تحديث التكلفة</button>";
            echo "<button onclick='resetCost(" . $session['session_id'] . ", " . $time_cost . ")' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>إعادة تعيين التكلفة</button>";
            
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
    }
    
    // 2. إنشاء JavaScript محسن
    echo "<h2>2. JavaScript محسن لمنع المشكلة</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>🔧 الحلول المطبقة:</h4>";
    echo "<ul>";
    echo "<li>إضافة حماية من تحديث التكلفة خطأ</li>";
    echo "<li>التأكد من أن التكلفة رقم وليس نص</li>";
    echo "<li>إضافة سجلات للتشخيص</li>";
    echo "<li>منع تداخل العمليات</li>";
    echo "</ul>";
    echo "</div>";
    
    // 3. اختبار مباشر
    echo "<h2>3. اختبار مباشر للحل</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>🎯 خطوات الاختبار:</h4>";
    echo "<ol>";
    echo "<li>استخدم الأزرار أعلاه لاختبار تحديث التكلفة</li>";
    echo "<li>تحقق من أن التكلفة تبقى رقم وليس اسم العميل</li>";
    echo "<li>افتح Developer Tools (F12) وتحقق من Console</li>";
    echo "<li>اختبر صفحة الجلسات الأصلية</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='test_api_cost.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>اختبار API</a>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/sessions.php?debug=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات + تشخيص</a>";
echo "</div>";

echo "</div>";
?>

<script>
// JavaScript محسن لمنع مشكلة التكلفة

// حماية من تحديث التكلفة خطأ
function safeCostUpdate(sessionId, newCost) {
    console.log('safeCostUpdate called:', { sessionId, newCost, type: typeof newCost });
    
    const costElement = document.querySelector(`[data-session-id="${sessionId}"] .session-cost`) || 
                       document.getElementById(`cost_${sessionId}`);
    
    if (!costElement) {
        console.error('Cost element not found for session:', sessionId);
        return false;
    }
    
    // التأكد من أن التكلفة رقم
    if (typeof newCost === 'string' && !isNaN(parseFloat(newCost))) {
        newCost = parseFloat(newCost);
    }
    
    if (typeof newCost !== 'number' || isNaN(newCost)) {
        console.error('Invalid cost value:', newCost, 'Type:', typeof newCost);
        return false;
    }
    
    // تحديث التكلفة
    const formattedCost = newCost.toFixed(2) + ' ج.م';
    costElement.textContent = formattedCost;
    
    console.log('Cost updated successfully:', formattedCost);
    return true;
}

// اختبار تحديث التكلفة
function testCostUpdate(sessionId, customerName, correctCost) {
    console.log('Testing cost update for session:', sessionId);
    
    const costElement = document.querySelector(`[data-session-id="${sessionId}"] .session-cost`) || 
                       document.getElementById(`cost_${sessionId}`);
    
    if (!costElement) {
        alert('عنصر التكلفة غير موجود!');
        return;
    }
    
    // عرض القيمة الحالية
    const currentValue = costElement.textContent;
    console.log('Current cost value:', currentValue);
    
    // محاولة تحديث خاطئ (محاكاة المشكلة)
    console.log('Simulating wrong update with customer name...');
    costElement.textContent = customerName;
    
    setTimeout(() => {
        alert(`تم تحديث التكلفة خطأ إلى: "${customerName}"\nسيتم إصلاحها الآن...`);
        
        // إصلاح التكلفة
        if (safeCostUpdate(sessionId, correctCost)) {
            alert('تم إصلاح التكلفة بنجاح!');
        } else {
            alert('فشل في إصلاح التكلفة!');
        }
    }, 2000);
}

// إعادة تعيين التكلفة
function resetCost(sessionId, correctCost) {
    if (safeCostUpdate(sessionId, correctCost)) {
        alert('تم إعادة تعيين التكلفة بنجاح!');
    } else {
        alert('فشل في إعادة تعيين التكلفة!');
    }
}

// حماية عامة لجميع عناصر التكلفة
function protectAllCostElements() {
    const costElements = document.querySelectorAll('.session-cost');
    
    costElements.forEach(element => {
        const sessionId = element.getAttribute('data-session-id');
        
        // مراقبة التغييرات
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const newValue = element.textContent;
                    
                    // فحص إذا كانت القيمة تحتوي على نص بدلاً من رقم
                    if (newValue && !newValue.match(/^\d+(\.\d{2})?\s*ج\.م$/)) {
                        console.warn('Suspicious cost value detected:', newValue, 'for session:', sessionId);
                        
                        // محاولة استخراج رقم من النص
                        const numberMatch = newValue.match(/(\d+(?:\.\d{2})?)/);
                        if (numberMatch) {
                            const extractedNumber = parseFloat(numberMatch[1]);
                            console.log('Extracted number:', extractedNumber);
                            safeCostUpdate(sessionId, extractedNumber);
                        } else {
                            console.error('No valid number found in:', newValue);
                            // يمكن إضافة استدعاء API لجلب التكلفة الصحيحة هنا
                        }
                    }
                }
            });
        });
        
        observer.observe(element, {
            childList: true,
            characterData: true,
            subtree: true
        });
        
        console.log('Protection enabled for session:', sessionId);
    });
}

// تفعيل الحماية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, enabling cost protection...');
    protectAllCostElements();
});

// إعادة تعريف دالة تحديث التكلفة الأصلية لتكون آمنة
if (window.updateSessionProducts) {
    const originalUpdateSessionProducts = window.updateSessionProducts;
    window.updateSessionProducts = function(sessionId) {
        console.log('Safe updateSessionProducts called for session:', sessionId);
        return originalUpdateSessionProducts(sessionId);
    };
}

console.log('Enhanced cost protection JavaScript loaded successfully!');
</script>
