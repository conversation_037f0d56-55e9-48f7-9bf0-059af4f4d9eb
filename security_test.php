<?php
/**
 * صفحة اختبار الأمان - Security Test Page
 * لاختبار فعالية نظام الحماية ضد سرقة الجلسات
 * PlayGood Gaming Center Management System
 */

require_once 'config/database.php';
require_once 'includes/auth_guard.php';

// إضافة JavaScript للحماية
addSessionProtectionJS();

$test_results = [];
$security_stats = [];

// الحصول على إحصائيات الأمان
try {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    $security_stats = $middleware->getSecurityStats();
} catch (Exception $e) {
    $security_stats = ['error' => $e->getMessage()];
}

// اختبار محاولة الوصول بدون جلسة
$test_results['no_session'] = [
    'name' => 'اختبار الوصول بدون جلسة',
    'status' => !isset($_SESSION['client_id']) && !isset($_SESSION['admin_id']) && !isset($_SESSION['employee_id']),
    'description' => 'التحقق من منع الوصول للصفحات المحمية بدون جلسة صحيحة'
];

// اختبار وجود رموز الأمان
$test_results['security_tokens'] = [
    'name' => 'اختبار رموز الأمان',
    'status' => isset($_SESSION['security_token']) && isset($_SESSION['session_fingerprint']),
    'description' => 'التحقق من وجود رموز الأمان في الجلسة'
];

// اختبار headers الأمان
$security_headers = [
    'X-Frame-Options',
    'X-Content-Type-Options',
    'X-XSS-Protection',
    'Content-Security-Policy',
    'Referrer-Policy'
];

$headers_test = true;
foreach ($security_headers as $header) {
    if (!isset(getallheaders()[$header])) {
        $headers_test = false;
        break;
    }
}

$test_results['security_headers'] = [
    'name' => 'اختبار Headers الأمان',
    'status' => $headers_test,
    'description' => 'التحقق من وجود headers الأمان المطلوبة'
];

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأمان - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .security-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .test-pass {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-fail {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .security-warning {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .security-info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="security-card p-4">
                    <div class="text-center mb-4">
                        <h1 class="display-4 text-primary">
                            <i class="fas fa-shield-alt me-3"></i>
                            اختبار الأمان
                        </h1>
                        <p class="lead text-muted">فحص شامل لنظام الحماية ضد سرقة الجلسات</p>
                    </div>

                    <!-- تحذير أمني -->
                    <div class="security-warning">
                        <h4><i class="fas fa-exclamation-triangle me-2"></i>تحذير أمني مهم</h4>
                        <p class="mb-0">
                            هذه الصفحة مخصصة لاختبار الأمان فقط. إذا كنت تستطيع رؤية هذه الصفحة بدون تسجيل دخول صحيح، 
                            فهذا يعني وجود ثغرة أمنية يجب إصلاحها فوراً.
                        </p>
                    </div>

                    <!-- إحصائيات الأمان -->
                    <?php if (!empty($security_stats) && !isset($security_stats['error'])): ?>
                    <div class="security-info">
                        <h4><i class="fas fa-chart-bar me-2"></i>إحصائيات الأمان</h4>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $security_stats['active_sessions'] ?? 0; ?></div>
                                <div>الجلسات النشطة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $security_stats['attacks_today'] ?? 0; ?></div>
                                <div>محاولات اختراق اليوم</div>
                            </div>
                        </div>
                        
                        <?php if (!empty($security_stats['attack_types'])): ?>
                        <h5 class="mt-3">أنواع الهجمات الأكثر شيوعاً:</h5>
                        <ul class="list-unstyled">
                            <?php foreach ($security_stats['attack_types'] as $attack): ?>
                            <li class="mb-2">
                                <strong><?php echo htmlspecialchars($attack['attack_type']); ?>:</strong>
                                <?php echo $attack['count']; ?> محاولة
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <!-- نتائج الاختبارات -->
                    <h3 class="mb-4"><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبارات</h3>
                    
                    <?php foreach ($test_results as $test): ?>
                    <div class="test-item <?php echo $test['status'] ? 'test-pass' : 'test-fail'; ?>">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas <?php echo $test['status'] ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'; ?> fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1"><?php echo $test['name']; ?></h5>
                                <p class="mb-0 text-muted"><?php echo $test['description']; ?></p>
                            </div>
                            <div class="badge <?php echo $test['status'] ? 'bg-success' : 'bg-danger'; ?> fs-6">
                                <?php echo $test['status'] ? 'نجح' : 'فشل'; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <!-- معلومات الجلسة الحالية -->
                    <div class="mt-5">
                        <h3><i class="fas fa-info-circle me-2"></i>معلومات الجلسة الحالية</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">بيانات الجلسة</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Session ID:</strong> <?php echo session_id(); ?></li>
                                            <li><strong>IP Address:</strong> <?php echo $_SERVER['REMOTE_ADDR']; ?></li>
                                            <li><strong>User Agent:</strong> <?php echo substr($_SERVER['HTTP_USER_AGENT'], 0, 50) . '...'; ?></li>
                                            <li><strong>Security Token:</strong> 
                                                <?php echo isset($_SESSION['security_token']) ? 'موجود' : 'غير موجود'; ?>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">حالة المستخدم</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Admin:</strong> 
                                                <?php echo isset($_SESSION['admin_id']) ? 'مسجل دخول' : 'غير مسجل'; ?>
                                            </li>
                                            <li><strong>Client:</strong> 
                                                <?php echo isset($_SESSION['client_id']) ? 'مسجل دخول' : 'غير مسجل'; ?>
                                            </li>
                                            <li><strong>Employee:</strong> 
                                                <?php echo isset($_SESSION['employee_id']) ? 'مسجل دخول' : 'غير مسجل'; ?>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختبارات إضافية -->
                    <div class="mt-5">
                        <h3><i class="fas fa-tools me-2"></i>اختبارات إضافية</h3>
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-warning w-100 mb-2" onclick="testSessionHijack()">
                                    <i class="fas fa-bug me-2"></i>
                                    اختبار سرقة الجلسة
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info w-100 mb-2" onclick="testCSRF()">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    اختبار CSRF
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-secondary w-100 mb-2" onclick="testXSS()">
                                    <i class="fas fa-code me-2"></i>
                                    اختبار XSS
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- روابط مفيدة -->
                    <div class="text-center mt-5">
                        <a href="admin/login.php" class="btn btn-primary me-2">
                            <i class="fas fa-user-shield me-2"></i>دخول المدير
                        </a>
                        <a href="client/login.php" class="btn btn-success me-2">
                            <i class="fas fa-store me-2"></i>دخول العميل
                        </a>
                        <a href="client/employee-login.php" class="btn btn-info">
                            <i class="fas fa-user-tie me-2"></i>دخول الموظف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testSessionHijack() {
            alert('اختبار سرقة الجلسة: محاولة استخدام session ID من متصفح آخر سيتم رفضها بسبب التحقق من IP و User Agent');
        }

        function testCSRF() {
            alert('اختبار CSRF: جميع النماذج محمية برموز CSRF للمنع من الهجمات العابرة للمواقع');
        }

        function testXSS() {
            alert('اختبار XSS: جميع المدخلات يتم تنظيفها وتشفيرها لمنع حقن الأكواد الضارة');
        }

        // عرض تحذير عند محاولة فتح أدوات المطور
        let devToolsWarning = 0;
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                devToolsWarning++;
                if (devToolsWarning <= 3) {
                    alert('تحذير: محاولة فتح أدوات المطور قد تؤدي إلى إنهاء الجلسة لأسباب أمنية');
                }
            }
        });
    </script>
</body>
</html>
