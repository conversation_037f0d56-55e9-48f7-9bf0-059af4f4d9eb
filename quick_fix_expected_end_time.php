<?php
/**
 * إصلاح سريع لمشكلة عمود expected_end_time
 * PlayGood Gaming Center Management System
 */

echo "<h1>إصلاح سريع - عمود expected_end_time</h1>";

try {
    // إعدادات قاعدة البيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    // محاولة الاتصال بقواعد البيانات المختلفة
    $databases = ['station', 'playgood'];
    $pdo = null;
    $connected_db = '';
    
    foreach ($databases as $dbname) {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            $connected_db = $dbname;
            echo "<p style='color: green;'>✅ متصل بقاعدة البيانات: $dbname</p>";
            break;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    if (!$pdo) {
        throw new Exception("فشل في الاتصال بأي من قواعد البيانات");
    }

    // فحص وجود جدول sessions
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول sessions غير موجود");
    }

    // فحص الأعمدة الموجودة
    $stmt = $pdo->query("DESCRIBE sessions");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>إضافة الأعمدة المفقودة:</h2>";
    
    // الأعمدة المطلوبة
    $required_columns = [
        'expected_end_time' => 'TIMESTAMP NULL DEFAULT NULL',
        'notes' => 'TEXT NULL DEFAULT NULL',
        'game_type' => 'ENUM("single","multiplayer") DEFAULT "single"',
        'client_id' => 'INT NULL DEFAULT NULL',
        'customer_id' => 'INT NULL DEFAULT NULL',
        'total_cost' => 'DECIMAL(10,2) DEFAULT 0.00',
        'created_by' => 'INT NULL DEFAULT NULL',
        'updated_by' => 'INT NULL DEFAULT NULL'
    ];
    
    $fixes_count = 0;
    
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $pdo->exec("ALTER TABLE sessions ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✅ تم إضافة عمود: $column</p>";
                $fixes_count++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود $column: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود $column موجود مسبقاً</p>";
        }
    }
    
    // اختبار سريع
    echo "<h2>اختبار سريع:</h2>";
    try {
        $test_query = "SELECT expected_end_time FROM sessions LIMIT 1";
        $pdo->query($test_query);
        echo "<p style='color: green;'>✅ عمود expected_end_time يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ مازال هناك مشكلة في عمود expected_end_time</p>";
    }
    
    echo "<hr>";
    echo "<h2>النتيجة:</h2>";
    if ($fixes_count > 0) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ تم إصلاح $fixes_count عمود</strong></p>";
        echo "<p>يمكنك الآن استخدام النظام بدون مشاكل</p>";
    } else {
        echo "<p style='color: blue; font-size: 18px;'><strong>ℹ️ جميع الأعمدة موجودة مسبقاً</strong></p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2 { color: #333; }
p { margin: 8px 0; padding: 8px; border-radius: 4px; }
hr { border: none; height: 2px; background: #007bff; margin: 20px 0; }
</style>
