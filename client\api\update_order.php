<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($data['order_id']) || !is_numeric($data['order_id'])) {
        throw new Exception('معرف الأوردر مطلوب');
    }

    $order_id = intval($data['order_id']);
    $action = isset($data['action']) ? $data['action'] : '';

    // التحقق من وجود الأوردر
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND client_id = ?");
    $stmt->execute([$order_id, $client_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        throw new Exception('الأوردر غير موجود');
    }

    $pdo->beginTransaction();

    switch ($action) {
        case 'update_status':
            if (!isset($data['status']) || !in_array($data['status'], ['pending', 'completed', 'cancelled'])) {
                throw new Exception('حالة الأوردر غير صحيحة');
            }

            $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$data['status'], $order_id]);

            $message = 'تم تحديث حالة الأوردر بنجاح';
            break;

        case 'update_payment_method':
            if (!isset($data['payment_method']) || !in_array($data['payment_method'], ['cash', 'card', 'other'])) {
                throw new Exception('طريقة الدفع غير صحيحة');
            }

            $stmt = $pdo->prepare("UPDATE orders SET payment_method = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$data['payment_method'], $order_id]);

            $message = 'تم تحديث طريقة الدفع بنجاح';
            break;

        case 'update_notes':
            $notes = isset($data['notes']) ? trim($data['notes']) : '';

            $stmt = $pdo->prepare("UPDATE orders SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$notes, $order_id]);

            $message = 'تم تحديث الملاحظات بنجاح';
            break;

        case 'update_customer':
            $customer_id = isset($data['customer_id']) && !empty($data['customer_id']) ? intval($data['customer_id']) : null;

            // التحقق من وجود العميل إذا تم تحديده
            if ($customer_id) {
                $customer_stmt = $pdo->prepare("SELECT customer_id FROM customers WHERE customer_id = ? AND client_id = ?");
                $customer_stmt->execute([$customer_id, $client_id]);
                if (!$customer_stmt->fetch()) {
                    throw new Exception('العميل غير موجود');
                }
            }

            $stmt = $pdo->prepare("UPDATE orders SET customer_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$customer_id, $order_id]);

            $message = 'تم تحديث العميل بنجاح';
            break;

        case 'add_item':
            if (!isset($data['product_id']) || !isset($data['quantity']) || $data['quantity'] <= 0) {
                throw new Exception('بيانات المنتج غير صحيحة');
            }

            // جلب معلومات المنتج
            $product_stmt = $pdo->prepare("SELECT id, name, price FROM cafeteria_items WHERE id = ? AND client_id = ?");
            $product_stmt->execute([$data['product_id'], $client_id]);
            $product = $product_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$product) {
                throw new Exception('المنتج غير موجود');
            }

            $quantity = intval($data['quantity']);
            $price = floatval($product['price']);
            $total_price = $price * $quantity;

            // إضافة المنتج للأوردر
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price, total_price) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$order_id, $product['id'], $quantity, $price, $total_price]);

            // تحديث المجموع الكلي للأوردر
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET total_amount = (
                    SELECT SUM(total_price) FROM order_items WHERE order_id = ?
                ), updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$order_id, $order_id]);

            $message = 'تم إضافة المنتج للأوردر بنجاح';
            break;

        case 'remove_item':
            if (!isset($data['item_id']) || !is_numeric($data['item_id'])) {
                throw new Exception('معرف المنتج مطلوب');
            }

            $item_id = intval($data['item_id']);

            // التحقق من وجود المنتج في الأوردر
            $item_stmt = $pdo->prepare("SELECT id FROM order_items WHERE id = ? AND order_id = ?");
            $item_stmt->execute([$item_id, $order_id]);
            if (!$item_stmt->fetch()) {
                throw new Exception('المنتج غير موجود في الأوردر');
            }

            // حذف المنتج
            $stmt = $pdo->prepare("DELETE FROM order_items WHERE id = ? AND order_id = ?");
            $stmt->execute([$item_id, $order_id]);

            // تحديث المجموع الكلي للأوردر
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET total_amount = COALESCE((
                    SELECT SUM(total_price) FROM order_items WHERE order_id = ?
                ), 0), updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$order_id, $order_id]);

            $message = 'تم حذف المنتج من الأوردر بنجاح';
            break;

        default:
            throw new Exception('العملية غير مدعومة');
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => $message
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
