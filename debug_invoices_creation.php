<?php
require_once 'config/database.php';

echo "<h1>تشخيص مشكلة إنشاء الفواتير</h1>";

try {
    // 1. فحص وجود جدول invoices
    echo "<h2>1. فحص وجود جدول invoices</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'invoices'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول invoices موجود</p>";
        
        // فحص هيكل الجدول
        echo "<h3>هيكل جدول invoices:</h3>";
        $stmt = $pdo->query("DESCRIBE invoices");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول invoices غير موجود</p>";
    }
    
    // 2. فحص الجلسات المكتملة
    echo "<h2>2. فحص الجلسات المكتملة</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sessions WHERE status = 'completed'");
    $completed_count = $stmt->fetchColumn();
    echo "<p>عدد الجلسات المكتملة: <strong>$completed_count</strong></p>";
    
    if ($completed_count > 0) {
        echo "<h3>آخر 5 جلسات مكتملة:</h3>";
        $stmt = $pdo->query("
            SELECT session_id, device_id, start_time, end_time, total_cost, client_id 
            FROM sessions 
            WHERE status = 'completed' 
            ORDER BY end_time DESC 
            LIMIT 5
        ");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Session ID</th><th>Device ID</th><th>Start Time</th><th>End Time</th><th>Total Cost</th><th>Client ID</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>" . $session['session_id'] . "</td>";
            echo "<td>" . $session['device_id'] . "</td>";
            echo "<td>" . $session['start_time'] . "</td>";
            echo "<td>" . $session['end_time'] . "</td>";
            echo "<td>" . $session['total_cost'] . "</td>";
            echo "<td>" . $session['client_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. فحص الفواتير الموجودة
    if ($table_exists) {
        echo "<h2>3. فحص الفواتير الموجودة</h2>";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM invoices");
        $invoices_count = $stmt->fetchColumn();
        echo "<p>عدد الفواتير الموجودة: <strong>$invoices_count</strong></p>";
        
        if ($invoices_count > 0) {
            echo "<h3>جميع الفواتير:</h3>";
            $stmt = $pdo->query("
                SELECT * FROM invoices 
                ORDER BY created_at DESC
            ");
            $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr>";
            foreach (array_keys($invoices[0]) as $column) {
                echo "<th>$column</th>";
            }
            echo "</tr>";
            foreach ($invoices as $invoice) {
                echo "<tr>";
                foreach ($invoice as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 4. فحص الجلسات التي لا تملك فواتير
        echo "<h2>4. الجلسات المكتملة بدون فواتير</h2>";
        $stmt = $pdo->query("
            SELECT s.session_id, s.device_id, s.end_time, s.total_cost, s.client_id
            FROM sessions s
            LEFT JOIN invoices i ON s.session_id = i.session_id
            WHERE s.status = 'completed' AND i.session_id IS NULL
            ORDER BY s.end_time DESC
        ");
        $sessions_without_invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات المكتملة بدون فواتير: <strong>" . count($sessions_without_invoices) . "</strong></p>";
        
        if (count($sessions_without_invoices) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Session ID</th><th>Device ID</th><th>End Time</th><th>Total Cost</th><th>Client ID</th></tr>";
            foreach ($sessions_without_invoices as $session) {
                echo "<tr>";
                echo "<td>" . $session['session_id'] . "</td>";
                echo "<td>" . $session['device_id'] . "</td>";
                echo "<td>" . $session['end_time'] . "</td>";
                echo "<td>" . $session['total_cost'] . "</td>";
                echo "<td>" . $session['client_id'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // 5. اختبار إنشاء فاتورة تجريبية
    if ($table_exists && $completed_count > 0) {
        echo "<h2>5. اختبار إنشاء فاتورة تجريبية</h2>";
        
        // جلب جلسة مكتملة بدون فاتورة
        $stmt = $pdo->query("
            SELECT s.*
            FROM sessions s
            LEFT JOIN invoices i ON s.session_id = i.session_id
            WHERE s.status = 'completed' AND i.session_id IS NULL
            LIMIT 1
        ");
        $test_session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_session) {
            echo "<p>محاولة إنشاء فاتورة للجلسة رقم: " . $test_session['session_id'] . "</p>";
            
            $invoice_number = date('Ymd') . str_pad($test_session['session_id'], 4, '0', STR_PAD_LEFT);
            $time_cost = 25.00; // قيمة تجريبية
            $products_cost = 0.00;
            $total_cost = $time_cost + $products_cost;
            $client_id = $test_session['client_id'];
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO invoices (
                        session_id,
                        invoice_number,
                        time_cost,
                        products_cost,
                        total_cost,
                        client_id,
                        created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $result = $stmt->execute([
                    $test_session['session_id'],
                    $invoice_number,
                    $time_cost,
                    $products_cost,
                    $total_cost,
                    $client_id,
                    $client_id
                ]);
                
                if ($result) {
                    $invoice_id = $pdo->lastInsertId();
                    echo "<p style='color: green;'>✅ تم إنشاء فاتورة تجريبية بنجاح! ID: $invoice_id</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في إنشاء الفاتورة</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء الفاتورة: " . $e->getMessage() . "</p>";
                echo "<p>كود الخطأ: " . $e->getCode() . "</p>";
            }
        } else {
            echo "<p>لا توجد جلسات مكتملة بدون فواتير للاختبار</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='client/invoices.php'>الذهاب إلى صفحة الفواتير</a></p>";
echo "<p><a href='client/sessions.php'>الذهاب إلى صفحة الجلسات</a></p>";
?>
