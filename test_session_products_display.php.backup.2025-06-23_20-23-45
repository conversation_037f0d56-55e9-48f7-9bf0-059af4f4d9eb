<?php
/**
 * اختبار عرض المنتجات في modal تعديل الجلسة
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار عرض المنتجات في modal تعديل الجلسة</h1>";
echo "<hr>";

try {
    echo "<h2>1. إنشاء بيانات تجريبية</h2>";
    
    // إنشاء جهاز تجريبي إذا لم يكن موجود
    $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
    $device = $stmt->fetch();
    
    if (!$device) {
        $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, single_rate, multi_rate, status, client_id) 
                   VALUES ('جهاز تجريبي', 'PS5', 15.00, 10.00, 20.00, 'available', 1)");
        $device_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جهاز تجريبي: $device_id</p>";
    } else {
        $device_id = $device['device_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام جهاز موجود: $device_id</p>";
    }
    
    // إنشاء جلسة تجريبية
    $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
    $stmt->execute([$device_id, 1, 1]);
    $session_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    
    // إنشاء منتجات تجريبية
    $products = [
        ['كوكاكولا', 3.50, 'مشروبات'],
        ['شيبس', 2.00, 'وجبات خفيفة'],
        ['قهوة', 5.00, 'مشروبات ساخنة']
    ];
    
    $product_ids = [];
    foreach ($products as $product) {
        $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([$product[0], $product[1], $product[2], 1]);
        $product_ids[] = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء منتج: {$product[0]} - {$product[1]} ج.م</p>";
    }
    
    // إضافة بعض المنتجات للجلسة
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_ids[0], 2, 3.50]); // 2 كوكاكولا
    $stmt->execute([$session_id, $product_ids[1], 1, 2.00]); // 1 شيبس
    echo "<p style='color: green;'>✅ تم إضافة منتجات للجلسة</p>";
    
    echo "<h2>2. اختبار API جلب منتجات الجلسة</h2>";
    
    // اختبار API
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/get_session_products.php?session_id=' . $session_id;
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";
    echo "<h3>استجابة API:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $json_response = json_decode($response, true);
    if ($json_response && isset($json_response['success']) && $json_response['success']) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ API يعمل بشكل صحيح!</strong></p>";
        
        echo "<h3>المنتجات المسترجعة:</h3>";
        echo "<div class='products-display' style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: white;'>";
        
        if (count($json_response['products']) > 0) {
            $total_cost = 0;
            echo "<div class='list-group'>";
            foreach ($json_response['products'] as $product) {
                $total_cost += $product['total'];
                echo "<div class='list-group-item d-flex justify-content-between align-items-center' style='margin-bottom: 5px; padding: 10px; border: 1px solid #eee; border-radius: 3px;'>";
                echo "<div>";
                echo "<h6 style='margin: 0; font-weight: bold;'>{$product['product_name']}</h6>";
                echo "<small style='color: #666;'>{$product['quantity']} × {$product['price']} ج.م</small>";
                echo "</div>";
                echo "<div>";
                echo "<span style='font-weight: bold; color: #007bff;'>{$product['total']} ج.م</span>";
                echo "</div>";
                echo "</div>";
            }
            echo "</div>";
            echo "<div style='margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;'>";
            echo "<div style='display: flex; justify-content: space-between;'>";
            echo "<span><strong>إجمالي تكلفة المنتجات:</strong></span>";
            echo "<span style='font-weight: bold; color: #007bff;'>" . number_format($total_cost, 2) . " ج.م</span>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<p style='color: #666; text-align: center; margin: 20px 0;'>لا توجد منتجات مضافة</p>";
        }
        
        echo "</div>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ API أرجع خطأ</strong></p>";
    }
    
    echo "<h2>3. اختبار modal تعديل الجلسة</h2>";
    ?>
    
    <div style="margin: 20px 0;">
        <button onclick="testEditModal()" style="background: #ffc107; color: #000; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">
            <i class="fas fa-edit"></i> اختبار modal تعديل الجلسة
        </button>
    </div>
    
    <div id="test-results" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;"></div>
    
    <!-- محاكاة modal تعديل الجلسة -->
    <div id="mockEditModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border: 2px solid #007bff; border-radius: 10px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 1000; max-width: 600px; width: 90%;">
        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
            <h5 style="margin: 0; color: #007bff;"><i class="fas fa-edit"></i> تعديل الجلسة</h5>
            <button onclick="closeMockModal()" style="background: #dc3545; color: white; border: none; border-radius: 3px; padding: 5px 10px; cursor: pointer;">×</button>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label style="font-weight: bold; display: block; margin-bottom: 5px;">الجهاز:</label>
            <input type="text" value="جهاز تجريبي - غرف عامة" readonly style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; background: #f8f9fa;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label style="font-weight: bold; display: block; margin-bottom: 5px;"><i class="fas fa-coffee"></i> إدارة المنتجات:</label>
            <div style="border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
                <div style="margin-bottom: 10px;">
                    <h6 style="color: #666; margin: 0 0 10px 0;">المنتجات المضافة:</h6>
                    <div id="mock_session_products">
                        <!-- سيتم تحميل المنتجات هنا -->
                    </div>
                </div>
                <button onclick="loadMockProducts()" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
            </div>
        </div>
        
        <div style="text-align: right;">
            <button onclick="closeMockModal()" style="background: #6c757d; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer; margin-left: 10px;">إلغاء</button>
            <button style="background: #ffc107; color: #000; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer; font-weight: bold;">حفظ التعديلات</button>
        </div>
    </div>
    
    <div id="overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;" onclick="closeMockModal()"></div>
    
    <script>
    const sessionId = <?php echo $session_id; ?>;
    
    function testEditModal() {
        document.getElementById('test-results').innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...</p>';
        
        // محاكاة تحميل المنتجات
        loadMockSessionProducts();
        
        // إظهار modal
        document.getElementById('mockEditModal').style.display = 'block';
        document.getElementById('overlay').style.display = 'block';
    }
    
    function loadMockSessionProducts() {
        fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('mock_session_products');
                
                if (data.success && data.products.length > 0) {
                    const totalCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);
                    
                    container.innerHTML = `
                        <div style="border: 1px solid #eee; border-radius: 3px;">
                            ${data.products.map(product => `
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #f0f0f0;">
                                    <div>
                                        <h6 style="margin: 0; font-weight: bold;">${product.product_name}</h6>
                                        <small style="color: #666;">${product.quantity} × ${product.price} ج.م</small>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span style="margin-left: 10px; font-weight: bold; color: #007bff;">${product.total} ج.م</span>
                                        <button onclick="deleteMockProduct(${product.product_id})" style="background: #dc3545; color: white; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer;" title="حذف المنتج">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                            <div style="display: flex; justify-content: space-between;">
                                <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                <span style="font-weight: bold; color: #007bff;">${totalCost.toFixed(2)} ج.م</span>
                            </div>
                        </div>
                    `;
                    
                    document.getElementById('test-results').innerHTML = '<p style="color: green;"><strong>✅ تم تحميل المنتجات بنجاح في modal التعديل!</strong></p>';
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <i class="fas fa-coffee" style="font-size: 2em; color: #ccc; margin-bottom: 10px;"></i>
                            <p style="color: #666; margin: 0;">لا توجد منتجات مضافة</p>
                            <small style="color: #999;">اضغط على "إضافة منتج" لإضافة منتجات للجلسة</small>
                        </div>
                    `;
                    
                    document.getElementById('test-results').innerHTML = '<p style="color: orange;"><strong>⚠️ لا توجد منتجات مضافة للجلسة</strong></p>';
                }
            })
            .catch(error => {
                document.getElementById('test-results').innerHTML = `<p style="color: red;"><strong>❌ خطأ في تحميل المنتجات:</strong> ${error.message}</p>`;
            });
    }
    
    function loadMockProducts() {
        alert('سيتم فتح modal إضافة المنتجات هنا');
    }
    
    function deleteMockProduct(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            // محاكاة حذف المنتج
            loadMockSessionProducts();
        }
    }
    
    function closeMockModal() {
        document.getElementById('mockEditModal').style.display = 'none';
        document.getElementById('overlay').style.display = 'none';
    }
    </script>
    
    <?php
    
    echo "<h2>4. تنظيف البيانات التجريبية</h2>";
    
    // حذف البيانات التجريبية
    $pdo->exec("DELETE FROM session_products WHERE session_id = $session_id");
    $pdo->exec("DELETE FROM sessions WHERE session_id = $session_id");
    foreach ($product_ids as $pid) {
        $pdo->exec("DELETE FROM cafeteria_items WHERE id = $pid");
    }
    
    echo "<p style='color: blue;'>ℹ️ تم حذف البيانات التجريبية</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
p {
    margin: 8px 0;
    padding: 8px;
    border-radius: 4px;
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
.products-display {
    font-family: Arial, sans-serif;
}
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
