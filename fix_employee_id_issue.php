<?php
/**
 * إصلاح مشكلة employee_id مقابل id في جدول employees
 * هذا الملف يحل مشكلة التضارب في أسماء الأعمدة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة employee_id</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .fix-section { margin-bottom: 30px; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; }
        .fix-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .fix-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fix-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .fix-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .fix-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container py-4'>
    <div class='card'>
        <div class='card-header bg-danger text-white'>
            <h3 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح مشكلة employee_id في جدول employees</h3>
        </div>
        <div class='card-body'>";

$fixes_applied = 0;
$errors = [];

try {
    // 1. فحص بنية جدول employees
    echo "<div class='fix-section'>
            <h5><i class='fas fa-database me-2'></i>فحص بنية جدول employees</h5>";
    
    try {
        $columns_stmt = $pdo->query("DESCRIBE employees");
        $columns = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $has_id = false;
        $has_employee_id = false;
        $primary_key = null;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'id') {
                $has_id = true;
                if ($column['Key'] === 'PRI') {
                    $primary_key = 'id';
                }
            }
            if ($column['Field'] === 'employee_id') {
                $has_employee_id = true;
                if ($column['Key'] === 'PRI') {
                    $primary_key = 'employee_id';
                }
            }
        }
        
        echo "<div class='fix-info'>
                <i class='fas fa-info-circle me-2'></i>
                بنية الجدول الحالية:
                <br>• يحتوي على عمود 'id': " . ($has_id ? 'نعم' : 'لا') . "
                <br>• يحتوي على عمود 'employee_id': " . ($has_employee_id ? 'نعم' : 'لا') . "
                <br>• المفتاح الأساسي: " . ($primary_key ?: 'غير محدد') . "
              </div>";
        
        // إذا كان الجدول يستخدم id كمفتاح أساسي، فهذا صحيح
        if ($primary_key === 'id') {
            echo "<div class='fix-success'>
                    <i class='fas fa-check me-2'></i>
                    الجدول يستخدم 'id' كمفتاح أساسي - هذا صحيح
                  </div>";
        } elseif ($primary_key === 'employee_id') {
            echo "<div class='fix-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    الجدول يستخدم 'employee_id' كمفتاح أساسي - يحتاج تحديث
                  </div>";
        }
        
    } catch (PDOException $e) {
        echo "<div class='fix-error'>
                <i class='fas fa-times me-2'></i>خطأ في فحص بنية الجدول: {$e->getMessage()}
              </div>";
        $errors[] = $e->getMessage();
    }
    echo "</div>";
    
    // 2. فحص الجلسات الحالية
    echo "<div class='fix-section'>
            <h5><i class='fas fa-user-check me-2'></i>فحص الجلسات النشطة</h5>";
    
    if (isset($_SESSION['employee_id'])) {
        echo "<div class='fix-info'>
                <i class='fas fa-info-circle me-2'></i>
                يوجد موظف مسجل دخول حالياً - معرف الجلسة: {$_SESSION['employee_id']}
              </div>";
        
        // التحقق من وجود الموظف في قاعدة البيانات
        try {
            // محاولة البحث بـ id أولاً
            $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ?");
            $stmt->execute([$_SESSION['employee_id']]);
            $employee = $stmt->fetch();
            
            if ($employee) {
                echo "<div class='fix-success'>
                        <i class='fas fa-check me-2'></i>
                        تم العثور على الموظف باستخدام عمود 'id' - الجلسة صحيحة
                      </div>";
            } else {
                // محاولة البحث بـ employee_id
                try {
                    $stmt = $pdo->prepare("SELECT * FROM employees WHERE employee_id = ?");
                    $stmt->execute([$_SESSION['employee_id']]);
                    $employee = $stmt->fetch();
                    
                    if ($employee) {
                        echo "<div class='fix-warning'>
                                <i class='fas fa-exclamation-triangle me-2'></i>
                                تم العثور على الموظف باستخدام عمود 'employee_id' - يحتاج تحديث
                              </div>";
                    } else {
                        echo "<div class='fix-error'>
                                <i class='fas fa-times me-2'></i>
                                لم يتم العثور على الموظف في قاعدة البيانات
                              </div>";
                    }
                } catch (PDOException $e) {
                    echo "<div class='fix-info'>
                            <i class='fas fa-info-circle me-2'></i>
                            عمود 'employee_id' غير موجود - الجدول يستخدم 'id' فقط
                          </div>";
                }
            }
        } catch (PDOException $e) {
            echo "<div class='fix-error'>
                    <i class='fas fa-times me-2'></i>خطأ في البحث عن الموظف: {$e->getMessage()}
                  </div>";
        }
    } else {
        echo "<div class='fix-info'>
                <i class='fas fa-info-circle me-2'></i>
                لا يوجد موظف مسجل دخول حالياً
              </div>";
    }
    echo "</div>";
    
    // 3. فحص الملفات التي تحتاج إصلاح
    echo "<div class='fix-section'>
            <h5><i class='fas fa-file-code me-2'></i>فحص الملفات التي تحتاج إصلاح</h5>";
    
    $files_to_check = [
        'client/profile.php' => 'صفحة الملف الشخصي',
        'client/includes/employee-auth.php' => 'ملف دوال المصادقة',
        'client/login.php' => 'صفحة تسجيل الدخول'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $has_employee_id_usage = strpos($content, 'employee_id') !== false;
            
            if ($has_employee_id_usage) {
                echo "<div class='fix-warning'>
                        <i class='fas fa-exclamation-triangle me-2'></i>
                        $description ($file) يستخدم 'employee_id' - يحتاج تحديث
                      </div>";
            } else {
                echo "<div class='fix-success'>
                        <i class='fas fa-check me-2'></i>
                        $description ($file) لا يستخدم 'employee_id' - صحيح
                      </div>";
            }
        } else {
            echo "<div class='fix-info'>
                    <i class='fas fa-info-circle me-2'></i>
                    $description ($file) غير موجود
                  </div>";
        }
    }
    echo "</div>";
    
    // 4. اختبار الاستعلامات
    echo "<div class='fix-section'>
            <h5><i class='fas fa-vial me-2'></i>اختبار الاستعلامات</h5>";
    
    // اختبار استعلام جلب الموظفين
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
        $result = $stmt->fetch();
        echo "<div class='fix-success'>
                <i class='fas fa-check me-2'></i>
                استعلام عدد الموظفين يعمل - يوجد {$result['count']} موظف
              </div>";
    } catch (PDOException $e) {
        echo "<div class='fix-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام الموظفين: {$e->getMessage()}
              </div>";
        $errors[] = $e->getMessage();
    }
    
    // اختبار استعلام تسجيل الدخول
    try {
        $stmt = $pdo->prepare("SELECT id, name, role FROM employees WHERE username = ? LIMIT 1");
        $stmt->execute(['test']);
        echo "<div class='fix-success'>
                <i class='fas fa-check me-2'></i>
                استعلام تسجيل الدخول يعمل مع عمود 'id'
              </div>";
    } catch (PDOException $e) {
        echo "<div class='fix-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام تسجيل الدخول: {$e->getMessage()}
              </div>";
        $errors[] = $e->getMessage();
    }
    
    // اختبار استعلام تحديث البيانات
    try {
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE id = ? LIMIT 1");
        $stmt->execute([1]);
        echo "<div class='fix-success'>
                <i class='fas fa-check me-2'></i>
                استعلام تحديث البيانات يعمل مع عمود 'id'
              </div>";
    } catch (PDOException $e) {
        echo "<div class='fix-error'>
                <i class='fas fa-times me-2'></i>خطأ في استعلام تحديث البيانات: {$e->getMessage()}
              </div>";
        $errors[] = $e->getMessage();
    }
    echo "</div>";
    
    // 5. التوصيات
    echo "<div class='fix-section'>
            <h5><i class='fas fa-lightbulb me-2'></i>التوصيات والحلول</h5>";
    
    if (empty($errors)) {
        echo "<div class='fix-success'>
                <i class='fas fa-thumbs-up me-2'></i>
                <strong>جميع الاختبارات نجحت!</strong> النظام يعمل بشكل صحيح.
              </div>";
    } else {
        echo "<div class='fix-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                <strong>تم العثور على مشاكل تحتاج إصلاح:</strong>
              </div>";
        
        echo "<div class='fix-info'>
                <i class='fas fa-tools me-2'></i>
                <strong>الحلول المقترحة:</strong>
                <br>1. تأكد من أن جدول employees يستخدم 'id' كمفتاح أساسي
                <br>2. تحديث جميع الملفات لتستخدم 'id' بدلاً من 'employee_id'
                <br>3. تحديث جلسات تسجيل الدخول لتستخدم المعرف الصحيح
                <br>4. اختبار جميع وظائف النظام بعد التحديث
              </div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='fix-error'>
            <i class='fas fa-exclamation-triangle me-2'></i>خطأ عام في الفحص: {$e->getMessage()}
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='client/profile.php' class='btn btn-primary me-2' target='_blank'>
                <i class='fas fa-user me-2'></i>اختبار صفحة الملف الشخصي
            </a>
            <a href='client/employees.php' class='btn btn-secondary me-2' target='_blank'>
                <i class='fas fa-users me-2'></i>اختبار صفحة الموظفين
            </a>
            <a href='test_new_permissions_page.php' class='btn btn-info'>
                <i class='fas fa-vial me-2'></i>اختبار صفحة الصلاحيات
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
