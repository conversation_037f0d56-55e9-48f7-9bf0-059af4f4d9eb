<?php
require_once 'config/database.php';
require_once 'includes/advanced_security.php';

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    try {
        $result = $test_function();
        if ($result) {
            $passed_tests++;
            $test_results[] = ['name' => $test_name, 'status' => 'PASS', 'message' => 'اختبار ناجح'];
        } else {
            $test_results[] = ['name' => $test_name, 'status' => 'FAIL', 'message' => 'اختبار فاشل'];
        }
    } catch (Exception $e) {
        $test_results[] = ['name' => $test_name, 'status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

runTest('اتصال قاعدة البيانات', function() use ($pdo) {
    return $pdo instanceof PDO;
});

runTest('تحميل نظام الحماية', function() {
    return class_exists('SecurityManager');
});

runTest('تحميل الحماية المتقدمة', function() {
    return class_exists('AdvancedSecurity');
});

runTest('فحص ملفات .htaccess', function() {
    $required_files = [
        '.htaccess',
        'config/.htaccess',
        'includes/.htaccess',
        'logs/.htaccess',
        'temp/.htaccess'
    ];
    
    foreach ($required_files as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }
    return true;
});

runTest('فحص معالج الأخطاء', function() {
    return file_exists('includes/error_handler.php');
});

runTest('فحص مجلدات الحماية', function() {
    $required_dirs = ['logs', 'temp', 'quarantine'];
    
    foreach ($required_dirs as $dir) {
        if (!is_dir($dir)) {
            return false;
        }
    }
    return true;
});

runTest('اختبار تنظيف البيانات', function() {
    global $security;
    
    $test_input = '<script>alert("xss")</script>';
    $cleaned = $security->sanitizeInput($test_input);
    
    return !strpos($cleaned, '<script>');
});

runTest('اختبار CSRF Token', function() {
    global $security;
    
    $token = $security->generateCsrfToken('test');
    return !empty($token) && strlen($token) > 10;
});

runTest('اختبار كشف SQL Injection', function() {
    global $db_security;
    
    if (!isset($db_security)) {
        return false;
    }
    
    $malicious_query = "SELECT * FROM users WHERE id = 1 OR 1=1";
    $patterns = $db_security->detectSqlInjection($malicious_query);
    
    return !empty($patterns);
});

runTest('اختبار تشفير البيانات', function() {
    global $db_security;
    
    if (!isset($db_security)) {
        return false;
    }
    
    $test_data = 'test sensitive data';
    $encrypted = $db_security->encrypt($test_data);
    $decrypted = $db_security->decrypt($encrypted);
    
    return $decrypted === $test_data;
});

runTest('فحص إعدادات PHP الآمنة', function() {
    $safe_settings = [
        'display_errors' => '0',
        'log_errors' => '1',
        'expose_php' => '0'
    ];
    
    foreach ($safe_settings as $setting => $expected) {
        if (ini_get($setting) != $expected) {
            return false;
        }
    }
    return true;
});

runTest('اختبار حظر IP', function() {
    global $advanced_security;
    
    return method_exists($advanced_security, 'isIpBlocked');
});

runTest('اختبار فحص التهديدات', function() {
    global $advanced_security;
    
    return method_exists($advanced_security, 'scanRequest');
});

runTest('فحص ملفات التكوين المحمية', function() {
    $config_files = ['config/database.php', 'includes/security.php'];
    
    foreach ($config_files as $file) {
        if (!file_exists($file)) {
            return false;
        }
        
        $content = file_get_contents($file);
        if (strpos($content, '//') !== false || strpos($content, '/*') !== false) {
            return false;
        }
    }
    return true;
});

runTest('اختبار Headers الأمان', function() {
    $headers = headers_list();
    $security_headers = ['X-Content-Type-Options', 'X-Frame-Options', 'X-XSS-Protection'];
    
    foreach ($security_headers as $header) {
        $found = false;
        foreach ($headers as $sent_header) {
            if (strpos($sent_header, $header) !== false) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            return false;
        }
    }
    return true;
});

$success_rate = round(($passed_tests / $total_tests) * 100, 2);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأمان النهائي - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .test-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 20px 0; }
        .test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; }
        .test-item { padding: 15px; border-bottom: 1px solid #eee; transition: all 0.3s ease; }
        .test-item:hover { background: #f8f9fa; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-error { color: #ffc107; }
        .progress-custom { height: 10px; border-radius: 10px; }
        .summary-card { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 10px; padding: 20px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="test-container">
            <div class="test-header text-center">
                <h1><i class="fas fa-shield-alt me-3"></i>اختبار الأمان النهائي</h1>
                <p class="mb-0">فحص شامل لجميع أنظمة الحماية والأمان</p>
            </div>
            
            <div class="p-4">
                <div class="summary-card text-center">
                    <h3><i class="fas fa-chart-pie me-2"></i>نتائج الاختبار</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <h4><?php echo $total_tests; ?></h4>
                            <p>إجمالي الاختبارات</p>
                        </div>
                        <div class="col-md-3">
                            <h4><?php echo $passed_tests; ?></h4>
                            <p>اختبارات ناجحة</p>
                        </div>
                        <div class="col-md-3">
                            <h4><?php echo $total_tests - $passed_tests; ?></h4>
                            <p>اختبارات فاشلة</p>
                        </div>
                        <div class="col-md-3">
                            <h4><?php echo $success_rate; ?>%</h4>
                            <p>معدل النجاح</p>
                        </div>
                    </div>
                    
                    <div class="progress progress-custom mt-3">
                        <div class="progress-bar bg-light" style="width: <?php echo $success_rate; ?>%"></div>
                    </div>
                </div>
                
                <h4><i class="fas fa-list-check me-2"></i>تفاصيل الاختبارات</h4>
                
                <?php foreach ($test_results as $test): ?>
                <div class="test-item">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <strong><?php echo htmlspecialchars($test['name']); ?></strong>
                        </div>
                        <div class="col-md-3">
                            <span class="status-<?php echo strtolower($test['status']); ?>">
                                <i class="fas fa-<?php echo $test['status'] === 'PASS' ? 'check-circle' : ($test['status'] === 'FAIL' ? 'times-circle' : 'exclamation-triangle'); ?> me-1"></i>
                                <?php echo $test['status']; ?>
                            </span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted"><?php echo htmlspecialchars($test['message']); ?></small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <div class="text-center mt-4">
                    <?php if ($success_rate >= 90): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-shield-check me-2"></i>
                            <strong>ممتاز!</strong> النظام محمي بشكل كامل ومؤمن ضد التهديدات.
                        </div>
                    <?php elseif ($success_rate >= 70): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>جيد!</strong> النظام محمي بشكل جيد مع بعض التحسينات المطلوبة.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-shield-virus me-2"></i>
                            <strong>تحذير!</strong> النظام يحتاج إلى تحسينات أمنية إضافية.
                        </div>
                    <?php endif; ?>
                    
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
