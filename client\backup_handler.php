<?php
session_start();
require_once '../config/database.php';
require_once '../includes/backup_permissions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح بالوصول']);
    exit();
}

// دالة تقسيم استعلامات SQL بطريقة محسنة
function splitSQLQueries($sql) {
    // إزالة التعليقات
    $sql = preg_replace('/--.*$/m', '', $sql);
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);

    // تقسيم الاستعلامات مع مراعاة النصوص المحاطة بعلامات اقتباس
    $queries = [];
    $current_query = '';
    $in_string = false;
    $string_char = '';
    $escaped = false;

    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];

        if ($escaped) {
            $current_query .= $char;
            $escaped = false;
            continue;
        }

        if ($char === '\\') {
            $current_query .= $char;
            $escaped = true;
            continue;
        }

        if (!$in_string && ($char === '"' || $char === "'")) {
            $in_string = true;
            $string_char = $char;
            $current_query .= $char;
        } elseif ($in_string && $char === $string_char) {
            $in_string = false;
            $current_query .= $char;
        } elseif (!$in_string && $char === ';') {
            $query = trim($current_query);
            if (!empty($query)) {
                $queries[] = $query;
            }
            $current_query = '';
        } else {
            $current_query .= $char;
        }
    }

    // إضافة الاستعلام الأخير إذا لم ينته بفاصلة منقوطة
    $query = trim($current_query);
    if (!empty($query)) {
        $queries[] = $query;
    }

    return array_filter($queries, function($query) {
        $query = trim($query);
        return !empty($query) && !preg_match('/^(--|\/\*)/', $query);
    });
}

// دالة للتحقق من الأخطاء التي يمكن تجاهلها
function isIgnorableError($errorMsg) {
    $ignorableErrors = [
        'Duplicate entry',
        'already exists',
        'Unknown database',
        'Table \'.*\' already exists',
        'Duplicate key name',
        'Multiple primary key defined'
    ];

    foreach ($ignorableErrors as $pattern) {
        if (preg_match('/' . $pattern . '/i', $errorMsg)) {
            return true;
        }
    }

    return false;
}

// التحقق من صلاحيات النسخ الاحتياطي
$client_id = $_SESSION['client_id'];
if (!isBackupEnabledForClient($client_id)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'تم تعطيل ميزة النسخ الاحتياطي لحسابكم من قبل إدارة النظام']);
    exit();
}

// التحقق من وضع الصيانة
if (isSystemInMaintenance()) {
    http_response_code(503);
    echo json_encode(['success' => false, 'message' => 'النظام في وضع الصيانة حالياً. يرجى المحاولة لاحقاً']);
    exit();
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'export':
        try {
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            $backupDir = __DIR__ . '/../backups';
            if (!file_exists($backupDir)) {
                mkdir($backupDir, 0777, true);
            }

            $clientId = $_SESSION['client_id'];

            // التحقق من عدد ملفات النسخ الاحتياطي الحالية
            $maxBackupFiles = getMaxBackupFiles();
            $existingBackups = glob("{$backupDir}/client_{$clientId}_backup_*.sql");

            if (count($existingBackups) >= $maxBackupFiles) {
                // حذف أقدم ملف نسخ احتياطي
                usort($existingBackups, function($a, $b) {
                    return filemtime($a) - filemtime($b);
                });
                unlink($existingBackups[0]);
            }

            // إنشاء اسم الملف
            $date = date('Y-m-d_H-i-s');
            $backupFile = "{$backupDir}/client_{$clientId}_backup_{$date}.sql";
            
            // تحديد مسار mysqldump
            $mysqldumpPath = 'C:\\xampp\\mysql\\bin\\mysqldump.exe';
            if (!file_exists($mysqldumpPath)) {
                // محاولة العثور على mysqldump في مسارات أخرى
                $possiblePaths = [
                    'C:\\xampp\\mysql\\bin\\mysqldump.exe',
                    'C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe',
                    'C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysqldump.exe',
                    'mysqldump' // في حالة وجوده في PATH
                ];
                
                $mysqldumpPath = null;
                foreach ($possiblePaths as $path) {
                    if ($path === 'mysqldump' || file_exists($path)) {
                        $mysqldumpPath = $path;
                        break;
                    }
                }
                
                if (!$mysqldumpPath) {
                    throw new Exception('لم يتم العثور على mysqldump. يرجى التأكد من تثبيت MySQL بشكل صحيح.');
                }
            }
            
            // بناء الأمر
            $command = "\"{$mysqldumpPath}\" --user=" . DB_USER . " --host=" . DB_HOST;
            if (DB_PASS !== '') {
                $command .= " --password=" . DB_PASS;
            }
            $command .= " --single-transaction --routines --triggers " . DB_NAME . " > \"{$backupFile}\"";
            
            // تنفيذ الأمر
            exec($command, $output, $return_var);
            
            if ($return_var === 0 && file_exists($backupFile) && filesize($backupFile) > 0) {
                // إرسال الملف للتحميل
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename="' . basename($backupFile) . '"');
                header('Content-Length: ' . filesize($backupFile));
                header('Cache-Control: must-revalidate');
                header('Pragma: public');
                
                readfile($backupFile);
                
                // حذف الملف المؤقت بعد التحميل
                unlink($backupFile);
                exit();
            } else {
                throw new Exception('فشل في إنشاء النسخة الاحتياطية. كود الخطأ: ' . $return_var);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;
        
    case 'import':
        try {
            if (!isset($_FILES['backup_file']) || $_FILES['backup_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('لم يتم رفع الملف بشكل صحيح');
            }
            
            $uploadedFile = $_FILES['backup_file'];
            
            // التحقق من نوع الملف
            $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
            if ($fileExtension !== 'sql') {
                throw new Exception('نوع الملف غير مدعوم. يجب أن يكون ملف SQL');
            }
            
            // التحقق من حجم الملف (أقصى 50 ميجابايت)
            if ($uploadedFile['size'] > 50 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
            }
            
            // قراءة محتوى الملف
            $sqlContent = file_get_contents($uploadedFile['tmp_name']);
            if ($sqlContent === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }
            
            // تحسين تقسيم الاستعلامات للتعامل مع الاستعلامات متعددة الأسطر
            $queries = splitSQLQueries($sqlContent);

            if (empty($queries)) {
                throw new Exception('الملف لا يحتوي على استعلامات SQL صالحة');
            }

            // إعدادات قاعدة البيانات المحسنة
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            $pdo->exec("SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO'");
            $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("SET CHARACTER SET utf8mb4");
            $pdo->exec("SET SESSION sql_mode = ''");

            $executed = 0;
            $errors = 0;
            $errorDetails = [];

            foreach ($queries as $index => $query) {
                $query = trim($query);
                if (empty($query)) continue;

                try {
                    $pdo->exec($query);
                    $executed++;
                } catch (PDOException $e) {
                    $errors++;
                    $errorMsg = $e->getMessage();

                    // تجاهل بعض الأخطاء غير المهمة
                    if (isIgnorableError($errorMsg)) {
                        continue;
                    }

                    $errorDetails[] = [
                        'query_index' => $index + 1,
                        'error' => $errorMsg,
                        'query_preview' => substr($query, 0, 100) . '...'
                    ];

                    // زيادة حد الأخطاء المسموح به إلى 50
                    if ($errors > 50) {
                        throw new Exception('تم إيقاف العملية بسبب كثرة الأخطاء. يرجى استخدام أداة الاستيراد المحسن: improved_import.php أو أداة التشخيص: diagnose_import_issues.php');
                    }
                }
            }
            
            // إعادة تفعيل فحص المفاتيح الخارجية
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo json_encode([
                'success' => true, 
                'message' => "تم استيراد البيانات بنجاح. تم تنفيذ {$executed} استعلام مع {$errors} أخطاء"
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'إجراء غير صالح']);
        break;
}
?>
