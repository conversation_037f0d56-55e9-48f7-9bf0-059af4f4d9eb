<?php
/**
 * تشخيص مباشر لمشكلة التكلفة - PlayGood
 * فحص مباشر لما يحدث في كود حساب التكلفة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص مباشر لمشكلة التكلفة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. محاكاة نفس الكود المستخدم في sessions.php
    echo "<h2>1. محاكاة نفس الكود المستخدم في sessions.php</h2>";
    
    try {
        // نفس الاستعلام تماماً
        $client_id = 1;
        
        // فحص وجود عمود customer_id
        $customer_id_column = null;
        try {
            $columns = $pdo->query("DESCRIBE customers")->fetchAll(PDO::FETCH_COLUMN);
            if (in_array('customer_id', $columns)) {
                $customer_id_column = 'customer_id';
            }
        } catch (PDOException $e) {
            // جدول العملاء غير موجود
        }
        
        echo "<p><strong>عمود العميل المستخدم:</strong> " . ($customer_id_column ?? 'NULL') . "</p>";
        
        if ($customer_id_column) {
            $active_sessions = $pdo->prepare("
                SELECT s.*,
                       d.device_name,
                       d.device_type,
                       d.hourly_rate,
                       d.single_rate,
                       d.multi_rate,
                       r.room_name,
                       c.name as customer_name,
                       c.phone as customer_phone,
                       TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                LEFT JOIN rooms r ON d.room_id = r.room_id
                LEFT JOIN customers c ON s.customer_id = c.$customer_id_column
                WHERE d.client_id = ? AND s.status = 'active'
                ORDER BY s.start_time DESC
            ");
        } else {
            $active_sessions = $pdo->prepare("
                SELECT s.*,
                       d.device_name,
                       d.device_type,
                       d.hourly_rate,
                       d.single_rate,
                       d.multi_rate,
                       r.room_name,
                       NULL as customer_name,
                       NULL as customer_phone,
                       TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                LEFT JOIN rooms r ON d.room_id = r.room_id
                WHERE d.client_id = ? AND s.status = 'active'
                ORDER BY s.start_time DESC
            ");
        }
        
        $active_sessions->execute([$client_id]);
        $active_sessions = $active_sessions->fetchAll();
        
        echo "<p>عدد الجلسات النشطة: <strong>" . count($active_sessions) . "</strong></p>";
        
        if (count($active_sessions) > 0) {
            foreach ($active_sessions as $index => $session) {
                echo "<div style='border: 3px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 10px; background: #fff5f5;'>";
                echo "<h3 style='color: #dc3545;'>🚨 جلسة رقم: " . $session['session_id'] . " (الفهرس: $index)</h3>";
                
                // عرض جميع المتغيرات خطوة بخطوة
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4>📊 المتغيرات الأساسية:</h4>";
                
                // طباعة جميع البيانات
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th style='padding: 8px;'>المتغير</th>";
                echo "<th style='padding: 8px;'>القيمة</th>";
                echo "<th style='padding: 8px;'>النوع</th>";
                echo "</tr>";
                
                $vars_to_check = [
                    'session_id' => $session['session_id'] ?? null,
                    'device_name' => $session['device_name'] ?? null,
                    'customer_name' => $session['customer_name'] ?? null,
                    'duration_minutes' => $session['duration_minutes'] ?? null,
                    'hourly_rate' => $session['hourly_rate'] ?? null,
                    'single_rate' => $session['single_rate'] ?? null,
                    'multi_rate' => $session['multi_rate'] ?? null
                ];
                
                foreach ($vars_to_check as $var_name => $var_value) {
                    echo "<tr>";
                    echo "<td style='padding: 8px; font-weight: bold;'>$var_name</td>";
                    echo "<td style='padding: 8px; color: blue;'>" . ($var_value ?? 'NULL') . "</td>";
                    echo "<td style='padding: 8px; color: green;'>" . gettype($var_value) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</div>";
                
                // محاكاة نفس الكود تماماً
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4>🧮 محاكاة كود حساب التكلفة:</h4>";
                
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; direction: ltr; text-align: left;'>";
                echo "// حساب تكلفة الوقت\n";
                
                $duration_minutes = $session['duration_minutes'] ?? 0;
                echo "\$duration_minutes = " . var_export($duration_minutes, true) . ";\n";
                
                // استخدام single_rate بدلاً من hourly_rate
                $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                echo "\$hourly_rate = \$session['single_rate'] ?? \$session['hourly_rate'] ?? 0;\n";
                echo "\$hourly_rate = " . var_export($hourly_rate, true) . ";\n";
                
                $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                echo "\$time_cost = \$duration_minutes > 0 && \$hourly_rate > 0 ? ceil(\$duration_minutes / 60) * \$hourly_rate : 0;\n";
                echo "\$time_cost = " . var_export($time_cost, true) . ";\n";
                
                // جلب تكلفة المنتجات
                echo "\n// جلب تكلفة المنتجات\n";
                try {
                    $products_stmt = $pdo->prepare("
                        SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                        FROM session_products sp
                        WHERE sp.session_id = ?
                    ");
                    $products_stmt->execute([$session['session_id']]);
                    $products_cost = $products_stmt->fetchColumn() ?: 0;
                    echo "\$products_cost = " . var_export($products_cost, true) . ";\n";
                } catch (PDOException $e) {
                    $products_cost = 0;
                    echo "\$products_cost = 0; // خطأ: " . $e->getMessage() . "\n";
                }
                
                // إجمالي التكلفة
                echo "\n// إجمالي التكلفة\n";
                $total_cost = $time_cost + $products_cost;
                echo "\$total_cost = \$time_cost + \$products_cost;\n";
                echo "\$total_cost = " . var_export($total_cost, true) . ";\n";
                
                // التأكد من عرض الرقم وليس النص
                echo "\n// التحقق من النوع\n";
                echo "is_numeric(\$total_cost) = " . (is_numeric($total_cost) ? 'true' : 'false') . ";\n";
                
                if (is_numeric($total_cost)) {
                    $formatted_cost = number_format($total_cost, 2) . ' ج.م';
                    echo "number_format(\$total_cost, 2) . ' ج.م' = '" . $formatted_cost . "';\n";
                } else {
                    $formatted_cost = '0.00 ج.م';
                    echo "'0.00 ج.م' (fallback)\n";
                }
                
                echo "</pre>";
                echo "</div>";
                
                // النتيجة النهائية
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4>🎯 النتيجة النهائية:</h4>";
                echo "<p style='font-size: 24px; font-weight: bold; color: #155724;'>$formatted_cost</p>";
                
                // فحص إضافي
                echo "<h5>🔍 فحص إضافي:</h5>";
                echo "<p><strong>هل النتيجة تحتوي على اسم العميل؟</strong> ";
                $customer_name = $session['customer_name'] ?? '';
                if (!empty($customer_name) && strpos($formatted_cost, $customer_name) !== false) {
                    echo "<span style='color: red; font-weight: bold;'>❌ نعم! المشكلة موجودة</span>";
                } else {
                    echo "<span style='color: green; font-weight: bold;'>✅ لا، النتيجة صحيحة</span>";
                }
                echo "</p>";
                
                echo "<p><strong>اسم العميل:</strong> " . htmlspecialchars($customer_name) . "</p>";
                echo "<p><strong>اسم الجهاز:</strong> " . htmlspecialchars($session['device_name'] ?? '') . "</p>";
                echo "</div>";
                
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
            
            // إنشاء جلسة تجريبية
            echo "<h3>إنشاء جلسة تجريبية:</h3>";
            
            try {
                // البحث عن جهاز متاح
                $device = $pdo->query("
                    SELECT device_id, device_name, single_rate 
                    FROM devices 
                    WHERE client_id = 1 AND status = 'available' 
                    LIMIT 1
                ")->fetch(PDO::FETCH_ASSOC);
                
                if ($device) {
                    // البحث عن عميل
                    $customer = $pdo->query("
                        SELECT customer_id, name 
                        FROM customers 
                        WHERE client_id = 1 
                        LIMIT 1
                    ")->fetch(PDO::FETCH_ASSOC);
                    
                    // إنشاء جلسة تجريبية
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO sessions (device_id, customer_id, start_time, status, client_id) 
                        VALUES (?, ?, NOW(), 'active', ?)
                    ");
                    $insert_stmt->execute([
                        $device['device_id'], 
                        $customer['customer_id'] ?? null, 
                        1
                    ]);
                    
                    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية</p>";
                    echo "<p><strong>الجهاز:</strong> " . htmlspecialchars($device['device_name']) . "</p>";
                    echo "<p><strong>العميل:</strong> " . htmlspecialchars($customer['name'] ?? 'غير محدد') . "</p>";
                    echo "<p><a href='debug_cost_direct.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تحميل الصفحة</a></p>";
                } else {
                    echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء جلسة تجريبية: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في محاكاة الكود: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص JavaScript
    echo "<h2>2. فحص JavaScript</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>🔧 اختبار JavaScript:</h4>";
    echo "<p>افتح Developer Tools (F12) وتحقق من:</p>";
    echo "<ul>";
    echo "<li>Console للأخطاء</li>";
    echo "<li>Network للطلبات</li>";
    echo "<li>Elements لفحص DOM</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بناءً على النتائج أعلاه:</h4>";
echo "<ol>";
echo "<li><strong>إذا كانت النتيجة النهائية صحيحة هنا:</strong> المشكلة في JavaScript أو CSS</li>";
echo "<li><strong>إذا كانت النتيجة تحتوي على اسم العميل:</strong> المشكلة في قاعدة البيانات</li>";
echo "<li><strong>إذا كانت النتيجة 0.00:</strong> المشكلة في الأسعار أو المدة</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='fix_device_rates.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إصلاح الأسعار</a>";
echo "<a href='fix_data_display_mix.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إصلاح البيانات</a>";
echo "</div>";

echo "</div>";
?>
