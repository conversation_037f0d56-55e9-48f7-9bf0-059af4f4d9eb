<?php
/**
 * صفحة الملف الشخصي - تعديل بيانات الموظف
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: ../login.php');
    exit;
}

// تحديد نوع المستخدم
$is_employee = isset($_SESSION['employee_id']);
$user_id = $is_employee ? $_SESSION['employee_id'] : $_SESSION['client_id'];
$client_id = $is_employee ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "الملف الشخصي";
$active_page = "profile";

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $owner_name = trim($_POST['owner_name']);
        $business_name = trim($_POST['business_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // التحقق من صحة البيانات
        if (empty($owner_name) || empty($email)) {
            throw new Exception('اسم المالك والبريد الإلكتروني مطلوبان');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        // تحديث البيانات الأساسية
        if ($is_employee) {
            // تحديث بيانات الموظف
            try {
                $update_stmt = $pdo->prepare("
                    UPDATE employees
                    SET name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $update_stmt->execute([$owner_name, $email, $phone, $address, $user_id]);
            } catch (PDOException $e) {
                // محاولة بدون updated_at إذا لم يكن موجود
                $update_stmt = $pdo->prepare("
                    UPDATE employees
                    SET name = ?, email = ?, phone = ?, address = ?
                    WHERE id = ?
                ");
                $update_stmt->execute([$owner_name, $email, $phone, $address, $user_id]);
            }
        } else {
            // تحديث بيانات صاحب المحل
            try {
                $update_stmt = $pdo->prepare("
                    UPDATE clients
                    SET owner_name = ?, business_name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE client_id = ?
                ");
                $update_stmt->execute([$owner_name, $business_name, $email, $phone, $address, $user_id]);
            } catch (PDOException $e) {
                // محاولة بدون updated_at إذا لم يكن موجود
                $update_stmt = $pdo->prepare("
                    UPDATE clients
                    SET owner_name = ?, business_name = ?, email = ?, phone = ?, address = ?
                    WHERE client_id = ?
                ");
                $update_stmt->execute([$owner_name, $business_name, $email, $phone, $address, $user_id]);
            }
        }
        
        // تحديث كلمة المرور إذا تم إدخالها
        if (!empty($new_password)) {
            if (strlen($new_password) < 6) {
                throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }
            
            if ($new_password !== $confirm_password) {
                throw new Exception('تأكيد كلمة المرور غير متطابق');
            }
            
            // التحقق من كلمة المرور الحالية
            $table = $is_employee ? 'employees' : 'clients';
            $id_field = $is_employee ? 'id' : 'client_id';
            $password_field = $is_employee ? 'password_hash' : 'password';

            $check_stmt = $pdo->prepare("SELECT $password_field FROM $table WHERE $id_field = ?");
            $check_stmt->execute([$user_id]);
            $current_hash = $check_stmt->fetchColumn();

            if (!password_verify($current_password, $current_hash)) {
                throw new Exception('كلمة المرور الحالية غير صحيحة');
            }

            // تحديث كلمة المرور
            $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $password_stmt = $pdo->prepare("UPDATE $table SET $password_field = ? WHERE $id_field = ?");
            $password_stmt->execute([$new_hash, $user_id]);
        }
        
        // تحديث متغيرات الجلسة لتحديث الـ header فوراً
        if ($is_employee) {
            $_SESSION['employee_name'] = $owner_name;
        } else {
            $_SESSION['business_name'] = $business_name;
            $_SESSION['owner_name'] = $owner_name;
            $_SESSION['client_name'] = $business_name; // للتوافق مع الكود القديم
        }

        $_SESSION['success'] = 'تم تحديث البيانات بنجاح';
        header('Location: profile.php');
        exit;
        
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// جلب البيانات الحالية
try {
    if ($is_employee) {
        // محاولة جلب بيانات الموظف مع معلومات العميل
        try {
            $stmt = $pdo->prepare("
                SELECT e.*, c.business_name as client_business_name, e.role
                FROM employees e
                JOIN clients c ON e.client_id = c.client_id
                WHERE e.id = ?
            ");
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            // محاولة بدون JOIN إذا فشل
            $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ?");
            $stmt->execute([$user_id]);
        }
    } else {
        // جلب بيانات العميل
        try {
            $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            // إنشاء بيانات افتراضية إذا لم توجد
            $default_data = [
                'client_id' => $user_id,
                'name' => 'مركز الألعاب',
                'email' => '<EMAIL>',
                'phone' => '***********',
                'address' => 'شارع فريد - الأحساء',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $user_data = $default_data;
        }
    }

    if (!isset($user_data)) {
        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    if (!$user_data) {
        // إنشاء بيانات افتراضية
        $user_data = [
            'name' => $is_employee ? 'موظف' : 'صاحب المحل',
            'owner_name' => $is_employee ? 'موظف' : 'صاحب المحل',
            'business_name' => 'مركز الألعاب',
            'email' => $is_employee ? '<EMAIL>' : '<EMAIL>',
            'phone' => '***********',
            'address' => 'شارع فريد - الأحساء',
            'created_at' => date('Y-m-d H:i:s')
        ];

        if ($is_employee) {
            $user_data['role'] = 'employee';
            $user_data['client_business_name'] = 'مركز الألعاب';
        }
    }

} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
    $user_data = [
        'name' => $is_employee ? 'موظف' : 'صاحب المحل',
        'owner_name' => $is_employee ? 'موظف' : 'صاحب المحل',
        'business_name' => 'مركز الألعاب',
        'email' => $is_employee ? '<EMAIL>' : '<EMAIL>',
        'phone' => '***********',
        'address' => 'شارع فريد - الأحساء',
        'created_at' => date('Y-m-d H:i:s')
    ];
}

require_once 'includes/header.php';
?>

<style>
.profile-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255,255,255,0.3);
}

.form-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.section-title {
    color: #667eea;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    color: white;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.strength-weak { background: #dc3545; width: 25%; }
.strength-medium { background: #ffc107; width: 50%; }
.strength-good { background: #28a745; width: 75%; }
.strength-strong { background: #20c997; width: 100%; }
</style>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- بطاقة الملف الشخصي -->
    <div class="profile-card">
        <div class="row align-items-center">
            <div class="col-md-3 text-center">
                <div class="profile-avatar">
                    <i class="fas fa-user fa-4x"></i>
                </div>
            </div>
            <div class="col-md-9">
                <h2 class="mb-2"><?php echo htmlspecialchars($user_data['owner_name'] ?? $user_data['name'] ?? 'المستخدم'); ?></h2>
                <p class="mb-1 opacity-75">
                    <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($user_data['email'] ?? ''); ?>
                </p>
                <?php if ($is_employee): ?>
                    <p class="mb-1 opacity-75">
                        <i class="fas fa-briefcase me-2"></i><?php echo htmlspecialchars($user_data['role'] ?? 'موظف'); ?>
                    </p>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-building me-2"></i><?php echo htmlspecialchars($user_data['client_business_name'] ?? $user_data['business_name'] ?? ''); ?>
                    </p>
                <?php else: ?>
                    <p class="mb-1 opacity-75">
                        <i class="fas fa-crown me-2"></i>صاحب المحل
                    </p>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-store me-2"></i><?php echo htmlspecialchars($user_data['business_name'] ?? 'مركز الألعاب'); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <form method="POST" id="profileForm">
        <div class="row">
            <!-- البيانات الأساسية -->
            <div class="col-lg-8">
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-user-edit"></i>البيانات الأساسية
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="owner_name" name="owner_name"
                                       value="<?php echo htmlspecialchars($user_data['owner_name'] ?? $user_data['name'] ?? ''); ?>" required>
                                <label for="owner_name"><?php echo $is_employee ? 'اسم الموظف' : 'اسم صاحب المحل'; ?></label>
                            </div>
                        </div>
                        <?php if (!$is_employee): ?>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="business_name" name="business_name"
                                       value="<?php echo htmlspecialchars($user_data['business_name'] ?? ''); ?>" required>
                                <label for="business_name">اسم المحل</label>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($user_data['email'] ?? ''); ?>" required>
                                <label for="email">البريد الإلكتروني</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($user_data['phone'] ?? ''); ?>">
                                <label for="phone">رقم الهاتف</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" id="address" name="address"
                               value="<?php echo htmlspecialchars($user_data['address'] ?? ''); ?>">
                        <label for="address">العنوان</label>
                    </div>
                </div>

                <!-- تغيير كلمة المرور -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-lock"></i>تغيير كلمة المرور
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="current_password" name="current_password">
                                <label for="current_password">كلمة المرور الحالية</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <label for="new_password">كلمة المرور الجديدة</label>
                                <div class="password-strength" id="passwordStrength"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                <label for="confirm_password">تأكيد كلمة المرور</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="col-lg-4">
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-info-circle"></i>معلومات الحساب
                    </h4>
                    
                    <div class="mb-3">
                        <label class="form-label">تاريخ الإنشاء</label>
                        <div class="form-control-plaintext">
                            <?php echo date('Y-m-d H:i', strtotime($user_data['created_at'] ?? 'now')); ?>
                        </div>
                    </div>
                    
                    <?php if (isset($user_data['updated_at'])): ?>
                    <div class="mb-3">
                        <label class="form-label">آخر تحديث</label>
                        <div class="form-control-plaintext">
                            <?php echo date('Y-m-d H:i', strtotime($user_data['updated_at'])); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($is_employee): ?>
                    <div class="mb-3">
                        <label class="form-label">الدور الوظيفي</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-primary"><?php echo htmlspecialchars($user_data['role'] ?? 'موظف'); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- أزرار الحفظ -->
                <div class="form-section">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-gradient">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                        </a>
                        <a href="settings.php" class="btn btn-outline-primary">
                            <i class="fas fa-cog me-2"></i>إعدادات المحل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// تحقق من قوة كلمة المرور
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('passwordStrength');
    
    if (password.length === 0) {
        strengthBar.className = 'password-strength';
        return;
    }
    
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;
    if (/\d/.test(password)) strength++;
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    const classes = ['', 'strength-weak', 'strength-medium', 'strength-good', 'strength-strong'];
    strengthBar.className = 'password-strength ' + (classes[Math.min(strength, 4)] || '');
});

// تحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// تحقق من النموذج قبل الإرسال
document.getElementById('profileForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const currentPassword = document.getElementById('current_password').value;
    
    if (newPassword && !currentPassword) {
        e.preventDefault();
        alert('يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور');
        document.getElementById('current_password').focus();
    }
});

// تحديث الـ header فوراً عند حفظ البيانات
document.getElementById('profileForm').addEventListener('submit', function(e) {
    // السماح للنموذج بالإرسال أولاً
    setTimeout(function() {
        // تحديث الـ header بعد ثانية واحدة من الإرسال
        if (typeof updateHeaderInfo === 'function') {
            updateHeaderInfo();
        }
    }, 1000);
});

// تحديث فوري عند تحميل صفحة الملف الشخصي
setTimeout(function() {
    if (typeof updateHeaderInfo === 'function') {
        updateHeaderInfo();
    }
}, 500);

// تحديث الـ header عند تغيير الأسماء في الوقت الفعلي (معاينة)
const ownerNameInput = document.getElementById('owner_name');
const businessNameInput = document.getElementById('business_name');

if (ownerNameInput) {
    ownerNameInput.addEventListener('input', function() {
        // تحديث فوري لاسم المستخدم (معاينة)
        const userNameElements = document.querySelectorAll('.fw-bold');
        userNameElements.forEach(element => {
            if (element.textContent.trim() && this.value.trim()) {
                element.textContent = this.value.trim();
            }
        });

        // تحديث الأحرف الأولى في الأفاتار
        if (this.value.trim()) {
            const avatarElements = document.querySelectorAll('.user-avatar');
            avatarElements.forEach(element => {
                element.textContent = this.value.trim().charAt(0).toUpperCase();
            });
        }
    });
}

if (businessNameInput) {
    businessNameInput.addEventListener('input', function() {
        // تحديث فوري في الـ navbar (معاينة)
        const navbarBrand = document.querySelector('.navbar-brand');
        if (navbarBrand && this.value.trim()) {
            navbarBrand.innerHTML = '<i class="fas fa-gamepad me-2"></i>' + this.value.trim();
        }
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>
