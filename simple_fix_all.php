<?php
/**
 * سكريپت الإصلاح المبسط - بدون transactions
 * يحل جميع مشاكل قاعدة البيانات بطريقة آمنة
 */

require_once 'config/database.php';

echo "<h1>الإصلاح المبسط الشامل - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$fixes_applied = 0;
$errors = [];

echo "<h2>1. إصلاح جدول customers</h2>";

try {
    // التحقق من وجود جدول customers
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول customers غير موجود - جاري الإنشاء...</p>";
        
        $create_customers = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                notes TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        $pdo->exec($create_customers);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
        $fixes_applied++;
        
        // إضافة بيانات تجريبية
        $sample_customers = [
            ['أحمد محمد علي', '01234567890', '<EMAIL>'],
            ['فاطمة حسن', '01234567891', '<EMAIL>'],
            ['محمد أحمد', '01234567892', '<EMAIL>']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, 1)");
        foreach ($sample_customers as $customer) {
            $insert_stmt->execute($customer);
        }
        echo "<p style='color: green;'>✅ تم إضافة 3 عملاء تجريبيين</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: green;'>✅ جدول customers موجود</p>";
        
        // فحص عمود client_id
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('client_id', $columns)) {
            echo "<p style='color: orange;'>⚠️ عمود client_id غير موجود - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE customers ADD COLUMN client_id INT NOT NULL DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
            $fixes_applied++;
        }
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في إصلاح جدول customers: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "<h2>2. إصلاح جدول sessions</h2>";

try {
    // فحص أعمدة sessions
    $stmt = $pdo->query("DESCRIBE sessions");
    $session_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_session_columns = [
        'client_id' => 'INT NULL',
        'total_cost' => 'DECIMAL(10,2) DEFAULT 0.00',
        'customer_id' => 'INT NULL',
        'expected_end_time' => 'TIMESTAMP NULL',
        'notes' => 'TEXT NULL',
        'created_by' => 'INT NULL',
        'updated_by' => 'INT NULL'
    ];
    
    foreach ($required_session_columns as $column => $definition) {
        if (!in_array($column, $session_columns)) {
            echo "<p style='color: orange;'>⚠️ عمود sessions.$column غير موجود - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE sessions ADD COLUMN $column $definition");
            echo "<p style='color: green;'>✅ تم إضافة عمود sessions.$column</p>";
            $fixes_applied++;
        }
    }
    
    // تحديث client_id في sessions
    $pdo->exec("UPDATE sessions s 
               JOIN devices d ON s.device_id = d.device_id 
               SET s.client_id = d.client_id 
               WHERE s.client_id IS NULL OR s.client_id = 0");
    echo "<p style='color: green;'>✅ تم تحديث معرفات العملاء في الجلسات</p>";
    $fixes_applied++;
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في إصلاح جدول sessions: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "<h2>3. إصلاح جدول rooms</h2>";

try {
    // التحقق من وجود جدول rooms
    $stmt = $pdo->query("SHOW TABLES LIKE 'rooms'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول rooms غير موجود - جاري الإنشاء...</p>";
        
        $create_rooms = "
            CREATE TABLE rooms (
                room_id INT AUTO_INCREMENT PRIMARY KEY,
                room_name VARCHAR(100) NOT NULL,
                description TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        $pdo->exec($create_rooms);
        echo "<p style='color: green;'>✅ تم إنشاء جدول rooms</p>";
        $fixes_applied++;
        
        // إضافة غرف تجريبية
        $sample_rooms = [
            ['الصالة الرئيسية', 'صالة الألعاب الرئيسية'],
            ['غرفة VIP', 'غرفة خاصة للعملاء المميزين'],
            ['قسم البلايستيشن', 'منطقة ألعاب البلايستيشن']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO rooms (room_name, description, client_id) VALUES (?, ?, 1)");
        foreach ($sample_rooms as $room) {
            $insert_stmt->execute($room);
        }
        echo "<p style='color: green;'>✅ تم إضافة 3 غرف تجريبية</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: green;'>✅ جدول rooms موجود</p>";
        
        // فحص الأعمدة المطلوبة
        $stmt = $pdo->query("DESCRIBE rooms");
        $rooms_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_rooms_columns = [
            'client_id' => 'INT NOT NULL DEFAULT 1',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ];
        
        foreach ($required_rooms_columns as $column => $definition) {
            if (!in_array($column, $rooms_columns)) {
                echo "<p style='color: orange;'>⚠️ عمود rooms.$column غير موجود - جاري الإضافة...</p>";
                $pdo->exec("ALTER TABLE rooms ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✅ تم إضافة عمود rooms.$column</p>";
                $fixes_applied++;
            }
        }
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في إصلاح جدول rooms: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "<h2>4. إصلاح جدول categories</h2>";

try {
    // التحقق من وجود جدول categories
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول categories غير موجود - جاري الإنشاء...</p>";

        $create_categories = "
            CREATE TABLE categories (
                category_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_category_client (name, client_id)
            )
        ";

        $pdo->exec($create_categories);
        echo "<p style='color: green;'>✅ تم إنشاء جدول categories</p>";
        $fixes_applied++;

        // إضافة تصنيفات تجريبية
        $sample_categories = [
            ['مشروبات باردة', 'العصائر والمشروبات الغازية'],
            ['مشروبات ساخنة', 'الشاي والقهوة'],
            ['وجبات خفيفة', 'البسكويت والشيبس'],
            ['حلويات', 'الكيك والحلويات'],
            ['ساندويتشات', 'الساندويتشات والبرجر']
        ];

        $insert_stmt = $pdo->prepare("INSERT INTO categories (name, description, client_id) VALUES (?, ?, 1)");
        foreach ($sample_categories as $category) {
            $insert_stmt->execute($category);
        }
        echo "<p style='color: green;'>✅ تم إضافة 5 تصنيفات تجريبية</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: green;'>✅ جدول categories موجود</p>";

        // فحص عمود client_id
        $stmt = $pdo->query("DESCRIBE categories");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('client_id', $columns)) {
            echo "<p style='color: orange;'>⚠️ عمود client_id غير موجود - جاري الإضافة...</p>";
            $pdo->exec("ALTER TABLE categories ADD COLUMN client_id INT NOT NULL DEFAULT 1");
            echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
            $fixes_applied++;
        }
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في إصلاح جدول categories: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "<h2>5. إنشاء الجداول المساعدة</h2>";

try {
    // جدول session_products
    $session_products_sql = "
        CREATE TABLE IF NOT EXISTS session_products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";
    $pdo->exec($session_products_sql);
    echo "<p style='color: green;'>✅ جدول session_products جاهز</p>";
    $fixes_applied++;

    // جدول invoices مع حالة الدفع
    $invoices_sql = "
        CREATE TABLE IF NOT EXISTS invoices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            invoice_number VARCHAR(50) NOT NULL UNIQUE,
            time_cost DECIMAL(10,2) DEFAULT 0.00,
            products_cost DECIMAL(10,2) DEFAULT 0.00,
            total_cost DECIMAL(10,2) NOT NULL,
            payment_status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
            client_id INT NOT NULL,
            created_by INT NULL,
            updated_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_invoices_session (session_id),
            INDEX idx_invoices_client (client_id),
            INDEX idx_invoices_status (payment_status),
            INDEX idx_invoices_number (invoice_number)
        )
    ";
    $pdo->exec($invoices_sql);
    echo "<p style='color: green;'>✅ جدول invoices جاهز</p>";
    $fixes_applied++;

    // التحقق من وجود عمود payment_status في جدول invoices الموجود
    $check_payment_status = $pdo->query("SHOW COLUMNS FROM invoices LIKE 'payment_status'");
    if ($check_payment_status->rowCount() == 0) {
        $pdo->exec("ALTER TABLE invoices ADD COLUMN payment_status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending' AFTER total_cost");
        echo "<p style='color: green;'>✅ تم إضافة عمود payment_status لجدول invoices</p>";
        $fixes_applied++;
    }

    // جدول سجل تغييرات حالة الدفع
    $payment_log_sql = "
        CREATE TABLE IF NOT EXISTS payment_status_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_id INT NOT NULL,
            old_status ENUM('pending', 'paid', 'cancelled'),
            new_status ENUM('pending', 'paid', 'cancelled') NOT NULL,
            changed_by INT NOT NULL,
            changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_payment_log_invoice (invoice_id),
            INDEX idx_payment_log_date (changed_at)
        )
    ";
    $pdo->exec($payment_log_sql);
    echo "<p style='color: green;'>✅ جدول payment_status_log جاهز</p>";
    $fixes_applied++;

    // جدول إعدادات الفاتورة
    $invoice_settings_sql = "
        CREATE TABLE IF NOT EXISTS invoice_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            header_color VARCHAR(7) DEFAULT '#dc3545',
            footer_text TEXT DEFAULT 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
            footer_color VARCHAR(7) DEFAULT '#000000',
            company_address TEXT,
            company_phone VARCHAR(20),
            show_qr_code BOOLEAN DEFAULT TRUE,
            print_logo_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client_settings (client_id)
        )
    ";
    $pdo->exec($invoice_settings_sql);
    echo "<p style='color: green;'>✅ جدول invoice_settings جاهز</p>";
    $fixes_applied++;

    // إضافة إعدادات افتراضية للعميل
    $default_settings_sql = "
        INSERT IGNORE INTO invoice_settings (client_id, footer_text, company_address, company_phone)
        VALUES (1, 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', 'شارع فريد - الأحساء', '***********')
    ";
    $pdo->exec($default_settings_sql);
    echo "<p style='color: green;'>✅ تم إضافة إعدادات افتراضية</p>";
    $fixes_applied++;

    // إنشاء جدول إعدادات المحل
    $business_settings_sql = "
        CREATE TABLE IF NOT EXISTS business_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client_setting (client_id, setting_key),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
        )
    ";
    $pdo->exec($business_settings_sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول إعدادات المحل</p>";
    $fixes_applied++;

    // إضافة أعمدة إضافية لجدول العملاء
    try {
        // فحص وإضافة الأعمدة المفقودة
        $columns_to_add = [
            "name VARCHAR(255) NOT NULL DEFAULT 'مركز الألعاب'",
            "email VARCHAR(255) DEFAULT NULL",
            "phone VARCHAR(20) DEFAULT NULL",
            "address TEXT DEFAULT NULL",
            "business_type VARCHAR(50) DEFAULT 'gaming_center'",
            "description TEXT DEFAULT NULL",
            "working_hours VARCHAR(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل'",
            "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];

        foreach ($columns_to_add as $column) {
            try {
                $column_name = explode(' ', $column)[0];
                $pdo->exec("ALTER TABLE clients ADD COLUMN $column");
                echo "<p style='color: green;'>✅ تم إضافة عمود $column_name لجدول العملاء</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                // العمود موجود مسبقاً أو خطأ آخر
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    echo "<p style='color: orange;'>⚠️ عمود $column_name: " . $e->getMessage() . "</p>";
                }
            }
        }

    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في تحديث جدول العملاء: " . $e->getMessage() . "</p>";
    }

    // إضافة فهارس لتحسين الأداء مع فحص وجود الأعمدة
    echo "<h3>تحسين أداء قاعدة البيانات</h3>";

    // فحص وجود الأعمدة قبل إضافة الفهارس
    function columnExists($pdo, $table, $column) {
        try {
            $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
            $stmt->execute([$column]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    function tableExists($pdo, $table) {
        try {
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    // فهارس أساسية للجلسات
    if (tableExists($pdo, 'sessions')) {
        $session_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)"
        ];

        if (columnExists($pdo, 'sessions', 'device_id')) {
            $session_indexes[] = "CREATE INDEX IF NOT EXISTS idx_sessions_device_status ON sessions(device_id, status)";
        }

        foreach ($session_indexes as $index_sql) {
            try {
                $pdo->exec($index_sql);
                echo "<p style='color: green;'>✅ تم إضافة فهرس للجلسات</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ فهرس الجلسات: " . $e->getMessage() . "</p>";
                }
            }
        }
    }

    // فهارس الأجهزة
    if (tableExists($pdo, 'devices')) {
        $device_indexes = [];

        if (columnExists($pdo, 'devices', 'client_id') && columnExists($pdo, 'devices', 'status')) {
            $device_indexes[] = "CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status)";
        } elseif (columnExists($pdo, 'devices', 'status')) {
            $device_indexes[] = "CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status)";
        }

        foreach ($device_indexes as $index_sql) {
            try {
                $pdo->exec($index_sql);
                echo "<p style='color: green;'>✅ تم إضافة فهرس للأجهزة</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ فهرس الأجهزة: " . $e->getMessage() . "</p>";
                }
            }
        }
    }

    // فهارس العملاء
    if (tableExists($pdo, 'customers') && columnExists($pdo, 'customers', 'client_id')) {
        try {
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_customers_client ON customers(client_id)");
            echo "<p style='color: green;'>✅ تم إضافة فهرس للعملاء</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                echo "<p style='color: orange;'>⚠️ فهرس العملاء: " . $e->getMessage() . "</p>";
            }
        }
    }

    // فهارس الفواتير
    if (tableExists($pdo, 'invoices')) {
        $invoice_indexes = [];

        if (columnExists($pdo, 'invoices', 'client_id')) {
            $invoice_indexes[] = "CREATE INDEX IF NOT EXISTS idx_invoices_client ON invoices(client_id)";
        }

        if (columnExists($pdo, 'invoices', 'payment_status')) {
            $invoice_indexes[] = "CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON invoices(payment_status)";
        }

        foreach ($invoice_indexes as $index_sql) {
            try {
                $pdo->exec($index_sql);
                echo "<p style='color: green;'>✅ تم إضافة فهرس للفواتير</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ فهرس الفواتير: " . $e->getMessage() . "</p>";
                }
            }
        }
    }

} catch (PDOException $e) {
    echo "<p style='color: orange;'>⚠️ الجداول المساعدة: " . $e->getMessage() . "</p>";
}

echo "<h2>6. اختبار الاستعلامات</h2>";

// اختبار استعلام الجلسات النشطة
try {
    $test_query = "
        SELECT s.*, d.device_name
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = 1 AND s.status = 'active'
        LIMIT 1
    ";
    $stmt = $pdo->query($test_query);
    echo "<p style='color: green;'>✅ استعلام الجلسات النشطة يعمل بشكل صحيح</p>";
} catch (PDOException $e) {
    echo "<p style='color: orange;'>⚠️ استعلام الجلسات: " . $e->getMessage() . "</p>";
}

// اختبار استعلام العملاء
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE client_id = 1");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ استعلام العملاء يعمل - يوجد " . $result['count'] . " عميل</p>";
} catch (PDOException $e) {
    echo "<p style='color: orange;'>⚠️ استعلام العملاء: " . $e->getMessage() . "</p>";
}

// اختبار استعلام الغرف
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM rooms WHERE client_id = 1");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ استعلام الغرف يعمل - يوجد " . $result['count'] . " غرفة</p>";
} catch (PDOException $e) {
    echo "<p style='color: orange;'>⚠️ استعلام الغرف: " . $e->getMessage() . "</p>";
}

// اختبار استعلام التصنيفات
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE client_id = 1");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ استعلام التصنيفات يعمل - يوجد " . $result['count'] . " تصنيف</p>";

    // اختبار إضافة تصنيف
    $test_category = 'تصنيف تجريبي ' . date('H:i:s');
    $test_stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
    $test_stmt->execute([$test_category]);
    echo "<p style='color: green;'>✅ اختبار إضافة تصنيف نجح</p>";

    // حذف التصنيف التجريبي
    $test_id = $pdo->lastInsertId();
    $pdo->prepare("DELETE FROM categories WHERE category_id = ?")->execute([$test_id]);
    echo "<p style='color: green;'>✅ تم حذف التصنيف التجريبي</p>";

} catch (PDOException $e) {
    echo "<p style='color: orange;'>⚠️ استعلام التصنيفات: " . $e->getMessage() . "</p>";
}

echo "<h2>النتائج النهائية</h2>";

if (count($errors) == 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<ul>";
    echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
    echo "<li>لا توجد أخطاء</li>";
    echo "<li>النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ تم الإصلاح مع بعض التحذيرات</h3>";
    echo "<ul>";
    echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
    echo "<li>عدد التحذيرات: " . count($errors) . "</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>اختبر النظام الآن:</h3>";
echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";
echo "<p><a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الجلسات</a></p>";
echo "<p><a href='client/customers.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة العملاء</a></p>";
echo "<p><a href='client/rooms.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الغرف</a></p>";
echo "<p><a href='client/cafeteria.php' style='background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الكافتيريا</a></p>";

echo "</div>";
?>
