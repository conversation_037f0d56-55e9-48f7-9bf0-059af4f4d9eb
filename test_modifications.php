<?php
/**
 * ملف اختبار التعديلات الجديدة
 * 1. اختبار منع تكرار العميل في أكثر من جلسة
 * 2. اختبار عرض مدة اللعب بالدقائق في الفاتورة
 */

require_once 'config/database.php';

echo "<h1>اختبار التعديلات الجديدة</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // اختبار 1: التحقق من وجود جلسات نشطة للعميل
    echo "<h3>🔍 اختبار 1: التحقق من منع تكرار العميل في الجلسات النشطة</h3>";
    
    // البحث عن عميل لديه جلسة نشطة
    $active_customer_query = "
        SELECT s.customer_id, c.name as customer_name, COUNT(*) as active_sessions
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'active' AND s.customer_id IS NOT NULL
        GROUP BY s.customer_id
        HAVING active_sessions > 0
        LIMIT 1
    ";
    
    $stmt = $pdo->query($active_customer_query);
    $active_customer = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($active_customer) {
        echo "<p><strong>✅ تم العثور على عميل لديه جلسة نشطة:</strong></p>";
        echo "<ul>";
        echo "<li>معرف العميل: " . $active_customer['customer_id'] . "</li>";
        echo "<li>اسم العميل: " . htmlspecialchars($active_customer['customer_name'] ?? 'غير محدد') . "</li>";
        echo "<li>عدد الجلسات النشطة: " . $active_customer['active_sessions'] . "</li>";
        echo "</ul>";
        
        // محاولة إنشاء جلسة جديدة لنفس العميل (يجب أن تفشل)
        echo "<p><strong>🧪 اختبار منع إنشاء جلسة جديدة لنفس العميل:</strong></p>";
        
        // البحث عن جهاز متاح
        $available_device_query = "
            SELECT device_id, device_name 
            FROM devices 
            WHERE status = 'available' 
            LIMIT 1
        ";
        $device_stmt = $pdo->query($available_device_query);
        $available_device = $device_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($available_device) {
            // محاكاة التحقق من الجلسات النشطة (نفس الكود المضاف)
            $check_stmt = $pdo->prepare("
                SELECT COUNT(*) as active_count 
                FROM sessions s 
                JOIN devices d ON s.device_id = d.device_id 
                WHERE s.customer_id = ? AND s.status = 'active'
            ");
            $check_stmt->execute([$active_customer['customer_id']]);
            $active_count = $check_stmt->fetchColumn();
            
            if ($active_count > 0) {
                echo "<p style='color: green;'>✅ <strong>النتيجة: التحقق يعمل بشكل صحيح!</strong></p>";
                echo "<p>تم منع إنشاء جلسة جديدة للعميل لأن لديه " . $active_count . " جلسة نشطة بالفعل.</p>";
            } else {
                echo "<p style='color: red;'>❌ <strong>خطأ: التحقق لا يعمل بشكل صحيح!</strong></p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أجهزة متاحة لاختبار إنشاء جلسة جديدة</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة حالياً لاختبار منع التكرار</p>";
        
        // إنشاء جلسة تجريبية للاختبار
        echo "<p><strong>🔧 إنشاء جلسة تجريبية للاختبار:</strong></p>";
        
        // البحث عن عميل وجهاز متاح
        $customer_query = "SELECT customer_id, name FROM customers LIMIT 1";
        $customer_stmt = $pdo->query($customer_query);
        $test_customer = $customer_stmt->fetch(PDO::FETCH_ASSOC);
        
        $device_query = "SELECT device_id, device_name FROM devices WHERE status = 'available' LIMIT 1";
        $device_stmt = $pdo->query($device_query);
        $test_device = $device_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_customer && $test_device) {
            echo "<p>سيتم إنشاء جلسة تجريبية للعميل: " . htmlspecialchars($test_customer['name']) . " على الجهاز: " . htmlspecialchars($test_device['device_name']) . "</p>";
            echo "<p style='color: blue;'>ℹ️ يمكنك تشغيل هذا الاختبار مرة أخرى بعد إنشاء جلسة لرؤية التحقق يعمل.</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد عملاء أو أجهزة متاحة للاختبار</p>";
        }
    }
    
    echo "<hr>";
    
    // اختبار 2: عرض مدة اللعب بالدقائق في الفاتورة
    echo "<h3>🕐 اختبار 2: عرض مدة اللعب بالدقائق في الفاتورة</h3>";
    
    // البحث عن جلسة مكتملة لاختبار الفاتورة
    $completed_session_query = "
        SELECT s.session_id, s.start_time, s.end_time,
               TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
               d.device_name, c.name as customer_name
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'completed' AND s.end_time IS NOT NULL
        ORDER BY s.end_time DESC
        LIMIT 1
    ";
    
    $session_stmt = $pdo->query($completed_session_query);
    $completed_session = $session_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($completed_session) {
        echo "<p><strong>✅ تم العثور على جلسة مكتملة للاختبار:</strong></p>";
        echo "<ul>";
        echo "<li>معرف الجلسة: " . $completed_session['session_id'] . "</li>";
        echo "<li>الجهاز: " . htmlspecialchars($completed_session['device_name']) . "</li>";
        echo "<li>العميل: " . htmlspecialchars($completed_session['customer_name'] ?? 'غير محدد') . "</li>";
        echo "<li>وقت البدء: " . $completed_session['start_time'] . "</li>";
        echo "<li>وقت الانتهاء: " . $completed_session['end_time'] . "</li>";
        echo "<li><strong>مدة اللعب: " . $completed_session['duration_minutes'] . " دقيقة</strong></li>";
        echo "</ul>";
        
        echo "<p><strong>🔗 رابط الفاتورة للاختبار:</strong></p>";
        echo "<p><a href='client/invoice.php?session_id=" . $completed_session['session_id'] . "' target='_blank' style='color: blue; text-decoration: underline;'>عرض الفاتورة (يجب أن تظهر المدة بالدقائق)</a></p>";
        
        echo "<p style='color: green;'>✅ <strong>النتيجة المتوقعة:</strong> يجب أن تظهر مدة اللعب في الفاتورة كـ \"" . $completed_session['duration_minutes'] . " دقيقة\" بدلاً من الساعات.</p>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات مكتملة لاختبار الفاتورة</p>";
        echo "<p>يمكنك إنشاء جلسة جديدة وإنهاؤها لاختبار عرض الفاتورة.</p>";
    }
    
    echo "<hr>";
    
    // ملخص التعديلات
    echo "<h3>📋 ملخص التعديلات المطبقة:</h3>";
    echo "<ol>";
    echo "<li><strong>منع تكرار العميل في الجلسات النشطة:</strong>";
    echo "<ul>";
    echo "<li>تم إضافة تحقق في ملف <code>client/sessions.php</code></li>";
    echo "<li>يتم التحقق من وجود جلسة نشطة للعميل قبل إنشاء جلسة جديدة</li>";
    echo "<li>إذا كان للعميل جلسة نشطة، يتم منع إنشاء جلسة جديدة مع رسالة خطأ واضحة</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>عرض مدة اللعب بالدقائق في الفاتورة:</strong>";
    echo "<ul>";
    echo "<li>تم تعديل ملف <code>client/invoice.php</code></li>";
    echo "<li>تم تغيير عرض مدة اللعب من الساعات إلى الدقائق</li>";
    echo "<li>الآن تظهر المدة كـ \"X دقيقة\" بدلاً من \"X ساعة\"</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    
    echo "<hr>";
    echo "<h3>✅ تم تطبيق جميع التعديلات بنجاح!</h3>";
    echo "<p style='color: green;'><strong>يمكنك الآن اختبار النظام للتأكد من عمل التعديلات بشكل صحيح.</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في الاتصال بقاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

code {
    background-color: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

ul, ol {
    line-height: 1.6;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 20px 0;
}
</style>
