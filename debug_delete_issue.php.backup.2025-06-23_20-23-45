<?php
/**
 * تشخيص مشكلة عدم عمل حذف المنتجات
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>تشخيص مشكلة عدم عمل حذف المنتجات</h1>";
echo "<hr>";

try {
    echo "<h2>1. فحص API الحذف مباشرة</h2>";
    
    // إنشاء بيانات تجريبية سريعة
    $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
    $device = $stmt->fetch();
    
    if (!$device) {
        $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, single_rate, multi_rate, status, client_id) 
                   VALUES ('جهاز اختبار الحذف', 'PS5', 15.00, 10.00, 20.00, 'available', 1)");
        $device_id = $pdo->lastInsertId();
    } else {
        $device_id = $device['device_id'];
    }
    
    // إنشاء جلسة
    $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
    $stmt->execute([$device_id, 1, 1]);
    $session_id = $pdo->lastInsertId();
    
    // إنشاء منتج
    $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
    $stmt->execute(['منتج اختبار الحذف', 5.00, 'اختبار', 1]);
    $product_id = $pdo->lastInsertId();
    
    // إضافة المنتج للجلسة
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, 2, 5.00]);
    $session_product_id = $pdo->lastInsertId();
    
    echo "<p style='color: green;'>✅ تم إنشاء بيانات الاختبار:</p>";
    echo "<ul>";
    echo "<li>معرف الجلسة: $session_id</li>";
    echo "<li>معرف المنتج: $product_id</li>";
    echo "<li>معرف منتج الجلسة: $session_product_id</li>";
    echo "</ul>";
    
    // فحص البيانات قبل الحذف
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ? AND product_id = ?");
    $stmt->execute([$session_id, $product_id]);
    $count_before = $stmt->fetch()['count'];
    echo "<p>عدد المنتجات قبل الحذف: <strong>$count_before</strong></p>";
    
    echo "<h2>2. اختبار API الحذف عبر cURL</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/delete_session_product.php';
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    $delete_data = [
        'session_id' => $session_id,
        'product_id' => $product_id
    ];
    
    echo "<h3>بيانات الحذف:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo json_encode($delete_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($delete_data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($curl);
    curl_close($curl);
    
    echo "<h3>نتيجة API:</h3>";
    echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ خطأ cURL: $curl_error</p>";
    } else {
        echo "<h4>استجابة API:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        $json_response = json_decode($response, true);
        if ($json_response) {
            if (isset($json_response['success']) && $json_response['success']) {
                echo "<p style='color: green; font-size: 18px;'><strong>✅ API نجح في الحذف!</strong></p>";
            } else {
                echo "<p style='color: red; font-size: 18px;'><strong>❌ API أرجع خطأ:</strong> " . ($json_response['error'] ?? 'خطأ غير معروف') . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ الاستجابة ليست JSON صحيح</p>";
        }
    }
    
    // فحص البيانات بعد الحذف
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ? AND product_id = ?");
    $stmt->execute([$session_id, $product_id]);
    $count_after = $stmt->fetch()['count'];
    echo "<p>عدد المنتجات بعد الحذف: <strong>$count_after</strong></p>";
    
    if ($count_before > $count_after) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ تم الحذف بنجاح من قاعدة البيانات!</strong></p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ لم يتم الحذف من قاعدة البيانات!</strong></p>";
    }
    
    echo "<h2>3. اختبار JavaScript</h2>";
    ?>
    
    <div id="js-test-area" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white;">
        <h3>اختبار دالة JavaScript للحذف:</h3>
        <button onclick="testJSDelete()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            <i class="fas fa-trash"></i> اختبار حذف JavaScript
        </button>
        <div id="js-results" style="margin-top: 15px; padding: 10px; border: 1px solid #eee; border-radius: 3px; background: #f8f9fa;"></div>
    </div>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    const testProductId = <?php echo $product_id; ?>;
    
    function testJSDelete() {
        const resultsDiv = document.getElementById('js-results');
        resultsDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري اختبار الحذف...</p>';
        
        console.log('Testing delete with:', { sessionId: testSessionId, productId: testProductId });
        
        fetch('client/api/delete_session_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: testSessionId,
                product_id: testProductId
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <p style="color: green;"><strong>✅ JavaScript: نجح الحذف!</strong></p>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>التكلفة الجديدة:</strong> ${data.total_cost} ج.م</p>
                        <p><strong>معرف الجلسة:</strong> ${data.session_id}</p>
                        <p><strong>معرف المنتج:</strong> ${data.product_id}</p>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ JavaScript: فشل الحذف</strong></p>
                        <p><strong>الخطأ:</strong> ${data.error}</p>
                        <details>
                            <summary>تفاصيل إضافية</summary>
                            <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                }
            } catch (jsonError) {
                resultsDiv.innerHTML = `
                    <p style="color: red;"><strong>❌ خطأ في تحليل JSON:</strong> ${jsonError.message}</p>
                    <details>
                        <summary>الاستجابة الخام</summary>
                        <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto; font-size: 12px;">${responseText}</pre>
                    </details>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultsDiv.innerHTML = `
                <p style="color: red;"><strong>❌ خطأ في الشبكة:</strong> ${error.message}</p>
            `;
        });
    }
    </script>
    
    <?php
    
    echo "<h2>4. فحص ملف API مباشرة</h2>";
    
    $api_file_path = 'client/api/delete_session_product.php';
    if (file_exists($api_file_path)) {
        echo "<p style='color: green;'>✅ ملف API موجود: $api_file_path</p>";
        
        // فحص صلاحيات الملف
        if (is_readable($api_file_path)) {
            echo "<p style='color: green;'>✅ الملف قابل للقراءة</p>";
        } else {
            echo "<p style='color: red;'>❌ الملف غير قابل للقراءة</p>";
        }
        
        // فحص حجم الملف
        $file_size = filesize($api_file_path);
        echo "<p>حجم الملف: $file_size بايت</p>";
        
        if ($file_size > 0) {
            echo "<p style='color: green;'>✅ الملف ليس فارغ</p>";
        } else {
            echo "<p style='color: red;'>❌ الملف فارغ</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ ملف API غير موجود: $api_file_path</p>";
    }
    
    echo "<h2>5. فحص أخطاء PHP</h2>";
    
    // تفعيل عرض الأخطاء مؤقتاً
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    
    echo "<p>تم تفعيل عرض أخطاء PHP</p>";
    echo "<p>تحقق من console المتصفح وسجلات الخادم للأخطاء</p>";
    
    echo "<h2>6. اختبار مباشر للحذف من قاعدة البيانات</h2>";
    
    // إضافة منتج آخر للاختبار
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, 1, 5.00]);
    echo "<p style='color: blue;'>ℹ️ تم إضافة منتج آخر للاختبار</p>";
    
    // فحص العدد قبل الحذف
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ? AND product_id = ?");
    $stmt->execute([$session_id, $product_id]);
    $count_before_direct = $stmt->fetch()['count'];
    echo "<p>عدد المنتجات قبل الحذف المباشر: <strong>$count_before_direct</strong></p>";
    
    // حذف مباشر
    $stmt = $pdo->prepare("DELETE FROM session_products WHERE session_id = ? AND product_id = ? LIMIT 1");
    $deleted = $stmt->execute([$session_id, $product_id]);
    $rows_affected = $stmt->rowCount();
    
    echo "<p>نتيجة الحذف المباشر: " . ($deleted ? 'نجح' : 'فشل') . "</p>";
    echo "<p>عدد الصفوف المتأثرة: <strong>$rows_affected</strong></p>";
    
    // فحص العدد بعد الحذف
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ? AND product_id = ?");
    $stmt->execute([$session_id, $product_id]);
    $count_after_direct = $stmt->fetch()['count'];
    echo "<p>عدد المنتجات بعد الحذف المباشر: <strong>$count_after_direct</strong></p>";
    
    if ($count_before_direct > $count_after_direct) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ الحذف المباشر من قاعدة البيانات يعمل!</strong></p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ مشكلة في الحذف المباشر من قاعدة البيانات!</strong></p>";
    }
    
    echo "<h2>7. الحلول المقترحة</h2>";
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h3>إذا كان API لا يعمل:</h3>";
    echo "<ol>";
    echo "<li>تحقق من مسار API في JavaScript</li>";
    echo "<li>تأكد من وجود ملف delete_session_product.php</li>";
    echo "<li>تحقق من أخطاء PHP في سجلات الخادم</li>";
    echo "<li>تأكد من صحة بيانات JSON المرسلة</li>";
    echo "</ol>";
    
    echo "<h3>إذا كان JavaScript لا يعمل:</h3>";
    echo "<ol>";
    echo "<li>تحقق من console المتصفح للأخطاء</li>";
    echo "<li>تأكد من استدعاء الدالة بالمعاملات الصحيحة</li>";
    echo "<li>تحقق من وجود مكتبة SweetAlert</li>";
    echo "</ol>";
    echo "</div>";
    
    // تنظيف البيانات التجريبية
    $pdo->exec("DELETE FROM session_products WHERE session_id = $session_id");
    $pdo->exec("DELETE FROM sessions WHERE session_id = $session_id");
    $pdo->exec("DELETE FROM cafeteria_items WHERE id = $product_id");
    echo "<p style='color: blue;'>ℹ️ تم حذف البيانات التجريبية</p>";

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
