<?php
/**
 * اختبار API إعدادات الفاتورة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>اختبار API إعدادات الفاتورة - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// محاكاة تسجيل الدخول إذا لم يكن مسجل
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    echo "<p style='color: orange;'>⚠️ تم تعيين جلسة تجريبية (client_id = 1)</p>";
}

echo "<h2>1. فحص الجلسة الحالية</h2>";
echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
echo "<tr><th style='padding: 10px;'>المفتاح</th><th style='padding: 10px;'>القيمة</th></tr>";
foreach ($_SESSION as $key => $value) {
    echo "<tr><td style='padding: 10px;'>$key</td><td style='padding: 10px;'>$value</td></tr>";
}
echo "</table>";

echo "<h2>2. فحص وجود جدول invoice_settings</h2>";

try {
    require_once 'config/database.php';
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'invoice_settings'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول invoice_settings غير موجود</p>";
        
        echo "<h3>إنشاء جدول invoice_settings:</h3>";
        $create_sql = "
            CREATE TABLE invoice_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                header_color VARCHAR(7) DEFAULT '#dc3545',
                footer_text TEXT DEFAULT 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
                footer_color VARCHAR(7) DEFAULT '#000000',
                company_address TEXT,
                company_phone VARCHAR(20),
                show_qr_code BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_client_settings (client_id)
            )
        ";
        
        try {
            $pdo->exec($create_sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول invoice_settings بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ جدول invoice_settings موجود</p>";
    }
    
    echo "<h2>3. فحص الإعدادات الحالية</h2>";
    
    $settings_stmt = $pdo->prepare("SELECT * FROM invoice_settings WHERE client_id = ?");
    $settings_stmt->execute([$_SESSION['client_id']]);
    $settings = $settings_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<p style='color: green;'>✅ توجد إعدادات للعميل</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
        echo "<tr><th style='padding: 10px;'>الحقل</th><th style='padding: 10px;'>القيمة</th></tr>";
        foreach ($settings as $key => $value) {
            echo "<tr><td style='padding: 10px;'>$key</td><td style='padding: 10px;'>$value</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد إعدادات للعميل - سيتم إنشاؤها عند الحفظ</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار API مباشرة</h2>";

if (isset($_POST['test_api'])) {
    echo "<h3>نتيجة اختبار API:</h3>";
    
    $test_data = [
        'header_color' => '#007bff',
        'footer_color' => '#28a745',
        'footer_text' => 'نص تجريبي للاختبار',
        'company_address' => 'عنوان تجريبي',
        'company_phone' => '0123456789',
        'show_qr_code' => true
    ];
    
    // محاكاة طلب API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/playgood/client/api/update_invoice_settings.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: ' . session_name() . '=' . session_id()
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    echo "<p><strong>Response:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    if ($response) {
        $json_response = json_decode($response, true);
        if ($json_response) {
            if ($json_response['success']) {
                echo "<p style='color: green;'>✅ API يعمل بنجاح!</p>";
            } else {
                echo "<p style='color: red;'>❌ API فشل: " . ($json_response['error'] ?? 'خطأ غير معروف') . "</p>";
                if (isset($json_response['debug'])) {
                    echo "<p><strong>تفاصيل الخطأ:</strong></p>";
                    echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px;'>";
                    print_r($json_response['debug']);
                    echo "</pre>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ استجابة غير صحيحة من API</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ لا توجد استجابة من API</p>";
    }
}

echo "<h2>5. اختبار يدوي</h2>";
echo "<form method='POST'>";
echo "<button type='submit' name='test_api' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>اختبار API</button>";
echo "</form>";

echo "<h2>6. اختبار JavaScript</h2>";
echo "<div id='jsTestResult'></div>";
echo "<button onclick='testJavaScriptAPI()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0;'>اختبار JavaScript API</button>";

echo "<script>
function testJavaScriptAPI() {
    const testData = {
        header_color: '#ff6b6b',
        footer_color: '#4ecdc4',
        footer_text: 'اختبار JavaScript API',
        company_address: 'عنوان من JavaScript',
        company_phone: '0987654321',
        show_qr_code: false
    };
    
    document.getElementById('jsTestResult').innerHTML = '<p>جاري الاختبار...</p>';
    
    fetch('client/api/update_invoice_settings.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Response text:', text);
        document.getElementById('jsTestResult').innerHTML = 
            '<h3>نتيجة اختبار JavaScript:</h3>' +
            '<pre style=\"background: #f5f5f5; padding: 15px; border-radius: 5px;\">' + 
            text + '</pre>';
        
        try {
            const data = JSON.parse(text);
            if (data.success) {
                document.getElementById('jsTestResult').innerHTML += 
                    '<p style=\"color: green;\">✅ JavaScript API يعمل بنجاح!</p>';
            } else {
                document.getElementById('jsTestResult').innerHTML += 
                    '<p style=\"color: red;\">❌ JavaScript API فشل: ' + (data.error || 'خطأ غير معروف') + '</p>';
            }
        } catch (e) {
            document.getElementById('jsTestResult').innerHTML += 
                '<p style=\"color: red;\">❌ استجابة غير صحيحة: ' + e.message + '</p>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('jsTestResult').innerHTML = 
            '<p style=\"color: red;\">❌ حدث خطأ في الاتصال: ' + error.message + '</p>';
    });
}
</script>";

echo "<h2>7. روابط مفيدة</h2>";
echo "<p><a href='client/invoice_settings.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة إعدادات الفاتورة</a></p>";
echo "<p><a href='client/invoices.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a></p>";
echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>سكريپت الإصلاح الشامل</a></p>";

echo "</div>";
?>
