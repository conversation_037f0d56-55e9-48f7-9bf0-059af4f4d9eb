<?php
require_once '../config/database.php';
require_once 'includes/auth.php';
require_once 'ensure_device_columns.php';
checkAdminSession();

// التأكد من وجود الأعمدة المطلوبة في جدول الأجهزة
ensureDeviceColumns($pdo);

function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

$success = '';
$error = '';

// التحقق من وجود معرف العميل
if (!isset($_GET['client_id']) || !is_numeric($_GET['client_id'])) {
    header('Location: clients.php');
    exit;
}

$client_id = intval($_GET['client_id']);

// جلب بيانات العميل
try {
    $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
    $stmt->execute([$client_id]);
    $client = $stmt->fetch();
    
    if (!$client) {
        header('Location: clients.php?error=client_not_found');
        exit;
    }
} catch (Exception $e) {
    header('Location: clients.php?error=database_error');
    exit;
}

// معالجة العمليات المختلفة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_device':
            $device_name = sanitize($_POST['device_name']);
            $device_type = $_POST['device_type'];
            $hourly_rate = floatval($_POST['hourly_rate']);
            $single_rate = floatval($_POST['single_rate']);
            $multi_rate = floatval($_POST['multi_rate']);
            $room_id = !empty($_POST['room_id']) ? intval($_POST['room_id']) : null;
            $status = $_POST['status'] ?? 'available';
            
            if (empty($device_name) || empty($device_type) || $hourly_rate <= 0 || $single_rate <= 0 || $multi_rate <= 0) {
                $error = 'يرجى ملء جميع الحقول المطلوبة بقيم صحيحة';
            } else {
                try {
                    $sql = "INSERT INTO devices (client_id, device_name, device_type, hourly_rate, single_rate, multi_rate, room_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([$client_id, $device_name, $device_type, $hourly_rate, $single_rate, $multi_rate, $room_id, $status]);
                    $success = 'تم إضافة الجهاز بنجاح';
                } catch (Exception $e) {
                    $error = 'حدث خطأ أثناء إضافة الجهاز: ' . $e->getMessage();
                }
            }
            break;
            
        case 'update_device':
            $device_id = intval($_POST['device_id']);
            $device_name = sanitize($_POST['device_name']);
            $device_type = $_POST['device_type'];
            $hourly_rate = floatval($_POST['hourly_rate']);
            $single_rate = floatval($_POST['single_rate']);
            $multi_rate = floatval($_POST['multi_rate']);
            $room_id = !empty($_POST['room_id']) ? intval($_POST['room_id']) : null;
            $status = $_POST['status'] ?? 'available';
            
            if (empty($device_name) || empty($device_type) || $hourly_rate <= 0 || $single_rate <= 0 || $multi_rate <= 0) {
                $error = 'يرجى ملء جميع الحقول المطلوبة بقيم صحيحة';
            } else {
                try {
                    $sql = "UPDATE devices SET device_name = ?, device_type = ?, hourly_rate = ?, single_rate = ?, multi_rate = ?, room_id = ?, status = ?, updated_at = NOW() 
                            WHERE device_id = ? AND client_id = ?";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([$device_name, $device_type, $hourly_rate, $single_rate, $multi_rate, $room_id, $status, $device_id, $client_id]);
                    $success = 'تم تحديث بيانات الجهاز بنجاح';
                } catch (Exception $e) {
                    $error = 'حدث خطأ أثناء تحديث الجهاز: ' . $e->getMessage();
                }
            }
            break;
            
        case 'delete_device':
            $device_id = intval($_POST['device_id']);
            
            try {
                // التحقق من وجود جلسات نشطة للجهاز
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM sessions WHERE device_id = ? AND status = 'active'");
                $stmt->execute([$device_id]);
                $active_sessions = $stmt->fetch()['count'];
                
                if ($active_sessions > 0) {
                    $error = 'لا يمكن حذف الجهاز لوجود جلسات نشطة مرتبطة به';
                } else {
                    $pdo->beginTransaction();
                    
                    // تحديث الجلسات المرتبطة بالجهاز لتصبح device_id = NULL
                    $stmt = $pdo->prepare("UPDATE sessions SET device_id = NULL WHERE device_id = ?");
                    $stmt->execute([$device_id]);
                    
                    // حذف الجهاز
                    $stmt = $pdo->prepare("DELETE FROM devices WHERE device_id = ? AND client_id = ?");
                    $stmt->execute([$device_id, $client_id]);
                    
                    $pdo->commit();
                    $success = 'تم حذف الجهاز بنجاح';
                }
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                $error = 'حدث خطأ أثناء حذف الجهاز: ' . $e->getMessage();
            }
            break;
    }
}

// جلب الغرف الخاصة بالعميل
try {
    $stmt = $pdo->prepare("SELECT * FROM rooms WHERE client_id = ? ORDER BY room_name");
    $stmt->execute([$client_id]);
    $rooms = $stmt->fetchAll();
} catch (Exception $e) {
    $rooms = [];
}

// جلب أجهزة العميل مع معلومات الغرف والجلسات النشطة
try {
    $sql = "SELECT d.*, r.room_name,
            (SELECT COUNT(*) FROM sessions s WHERE s.device_id = d.device_id AND s.status = 'active') as active_sessions_count
            FROM devices d
            LEFT JOIN rooms r ON d.room_id = r.room_id
            WHERE d.client_id = ?
            ORDER BY d.device_name";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$client_id]);
    $devices = $stmt->fetchAll();
} catch (Exception $e) {
    $devices = [];
    $error = 'حدث خطأ في جلب بيانات الأجهزة: ' . $e->getMessage();
}

// إحصائيات سريعة
$stats = [
    'total' => count($devices),
    'available' => 0,
    'occupied' => 0,
    'maintenance' => 0
];

foreach ($devices as $device) {
    $stats[$device['status']]++;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أجهزة العميل - <?php echo htmlspecialchars($client['business_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .stats-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .stats-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stats-card-danger {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .client-info {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- المحتوى الرئيسي -->
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h2">
                            <i class="fas fa-desktop me-2"></i>
                            إدارة أجهزة العميل
                        </h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="clients.php">إدارة العملاء</a></li>
                                <li class="breadcrumb-item active">إدارة الأجهزة</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>

                <!-- الإحصائيات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-desktop fa-2x mb-2"></i>
                                <h3><?php echo $stats['total']; ?></h3>
                                <p>إجمالي الأجهزة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3><?php echo $stats['available']; ?></h3>
                                <p>متاح</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <h3><?php echo $stats['occupied']; ?></h3>
                                <p>مشغول</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-tools fa-2x mb-2"></i>
                                <h3><?php echo $stats['maintenance']; ?></h3>
                                <p>صيانة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إدارة الأجهزة -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-desktop me-2"></i>
                            أجهزة العميل
                        </h5>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة جهاز جديد
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الجهاز</th>
                                        <th>اسم الجهاز</th>
                                        <th>النوع</th>
                                        <th>الغرفة</th>
                                        <th>سعر الساعة العادي</th>
                                        <th>سعر الساعة (فردي)</th>
                                        <th>سعر الساعة (مالتي)</th>
                                        <th>الحالة</th>
                                        <th>الجلسات النشطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($devices)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-5">
                                                <i class="fas fa-desktop fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">لا توجد أجهزة مضافة لهذا العميل</p>
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                                                    <i class="fas fa-plus me-2"></i>
                                                    إضافة أول جهاز
                                                </button>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($devices as $device): ?>
                                            <tr>
                                                <td><?php echo $device['device_id']; ?></td>
                                                <td><?php echo htmlspecialchars($device['device_name']); ?></td>
                                                <td>
                                                    <?php
                                                    $type_icons = [
                                                        'PS4' => 'fab fa-playstation',
                                                        'PS5' => 'fab fa-playstation',
                                                        'Xbox' => 'fab fa-xbox',
                                                        'PC' => 'fas fa-desktop'
                                                    ];
                                                    ?>
                                                    <i class="<?php echo $type_icons[$device['device_type']] ?? 'fas fa-gamepad'; ?> me-1"></i>
                                                    <?php echo $device['device_type']; ?>
                                                </td>
                                                <td><?php echo $device['room_name'] ? htmlspecialchars($device['room_name']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                                <td><?php echo number_format($device['hourly_rate'], 2); ?> ر.س</td>
                                                <td><?php echo number_format($device['single_rate'] ?? 0, 2); ?> ر.س</td>
                                                <td><?php echo number_format($device['multi_rate'] ?? 0, 2); ?> ر.س</td>
                                                <td>
                                                    <?php
                                                    $status_badges = [
                                                        'available' => 'bg-success',
                                                        'occupied' => 'bg-warning',
                                                        'maintenance' => 'bg-danger'
                                                    ];
                                                    $status_names = [
                                                        'available' => 'متاح',
                                                        'occupied' => 'مشغول',
                                                        'maintenance' => 'صيانة'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $status_badges[$device['status']]; ?>">
                                                        <?php echo $status_names[$device['status']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($device['active_sessions_count'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $device['active_sessions_count']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لا توجد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <!-- تعديل الجهاز -->
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="editDevice(<?php echo htmlspecialchars(json_encode($device)); ?>)"
                                                                data-bs-toggle="modal" data-bs-target="#editDeviceModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>

                                                        <!-- حذف الجهاز -->
                                                        <?php if ($device['active_sessions_count'] == 0): ?>
                                                            <form method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الجهاز؟')">
                                                                <input type="hidden" name="action" value="delete_device">
                                                                <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن حذف الجهاز لوجود جلسات نشطة">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة جهاز جديد -->
    <div class="modal fade" id="addDeviceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جهاز جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_device">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الجهاز *</label>
                                <input type="text" class="form-control" name="device_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نوع الجهاز *</label>
                                <select class="form-select" name="device_type" required>
                                    <option value="">اختر نوع الجهاز</option>
                                    <option value="PS4">PlayStation 4</option>
                                    <option value="PS5">PlayStation 5</option>
                                    <option value="Xbox">Xbox</option>
                                    <option value="PC">PC Gaming</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة العادي *</label>
                                <input type="number" class="form-control" name="hourly_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة (فردي) *</label>
                                <input type="number" class="form-control" name="single_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة (مالتي) *</label>
                                <input type="number" class="form-control" name="multi_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الغرفة</label>
                                <select class="form-select" name="room_id">
                                    <option value="">بدون غرفة محددة</option>
                                    <?php foreach ($rooms as $room): ?>
                                        <option value="<?php echo $room['room_id']; ?>">
                                            <?php echo htmlspecialchars($room['room_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">حالة الجهاز</label>
                                <select class="form-select" name="status">
                                    <option value="available">متاح</option>
                                    <option value="maintenance">صيانة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إضافة الجهاز
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تعديل الجهاز -->
    <div class="modal fade" id="editDeviceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات الجهاز
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_device">
                        <input type="hidden" name="device_id" id="edit_device_id">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الجهاز *</label>
                                <input type="text" class="form-control" name="device_name" id="edit_device_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نوع الجهاز *</label>
                                <select class="form-select" name="device_type" id="edit_device_type" required>
                                    <option value="">اختر نوع الجهاز</option>
                                    <option value="PS4">PlayStation 4</option>
                                    <option value="PS5">PlayStation 5</option>
                                    <option value="Xbox">Xbox</option>
                                    <option value="PC">PC Gaming</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة العادي *</label>
                                <input type="number" class="form-control" name="hourly_rate" id="edit_hourly_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة (فردي) *</label>
                                <input type="number" class="form-control" name="single_rate" id="edit_single_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سعر الساعة (مالتي) *</label>
                                <input type="number" class="form-control" name="multi_rate" id="edit_multi_rate" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الغرفة</label>
                                <select class="form-select" name="room_id" id="edit_room_id">
                                    <option value="">بدون غرفة محددة</option>
                                    <?php foreach ($rooms as $room): ?>
                                        <option value="<?php echo $room['room_id']; ?>">
                                            <?php echo htmlspecialchars($room['room_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">حالة الجهاز</label>
                                <select class="form-select" name="status" id="edit_status">
                                    <option value="available">متاح</option>
                                    <option value="occupied">مشغول</option>
                                    <option value="maintenance">صيانة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editDevice(device) {
            document.getElementById('edit_device_id').value = device.device_id;
            document.getElementById('edit_device_name').value = device.device_name;
            document.getElementById('edit_device_type').value = device.device_type;
            document.getElementById('edit_hourly_rate').value = device.hourly_rate;
            document.getElementById('edit_single_rate').value = device.single_rate || '';
            document.getElementById('edit_multi_rate').value = device.multi_rate || '';
            document.getElementById('edit_room_id').value = device.room_id || '';
            document.getElementById('edit_status').value = device.status;
        }

        // إخفاء الرسائل تلقائياً بعد 5 ثوانٍ
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // تأثيرات بصرية للكروت
        document.querySelectorAll('.card').forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // إضافة تأثير loading للنماذج
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    submitBtn.disabled = true;

                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 2000);
                }
            });
        });
    </script>
</body>
</html>

                <!-- معلومات العميل -->
                <div class="client-info">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">
                                <i class="fas fa-store me-2"></i>
                                <?php echo htmlspecialchars($client['business_name']); ?>
                            </h4>
                            <p class="mb-1">
                                <strong>المالك:</strong> <?php echo htmlspecialchars($client['owner_name']); ?>
                            </p>
                            <p class="mb-1">
                                <strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($client['email']); ?>
                            </p>
                            <p class="mb-0">
                                <strong>الهاتف:</strong> <?php echo htmlspecialchars($client['phone']); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge <?php echo $client['is_active'] ? 'bg-success' : 'bg-danger'; ?> fs-6">
                                <?php echo $client['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                    </div>
                </div>
