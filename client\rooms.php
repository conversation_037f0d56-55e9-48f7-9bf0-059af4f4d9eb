<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الغرف
    if (!hasPagePermission('rooms')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الغرف';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - التحقق من الوصول للصفحة والصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('rooms')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_rooms')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة الغرف";
$active_page = "rooms";

// Initialize messages array
$messages = [];

// Handle all POST/GET operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['delete'])) {
    // إضافة غرفة جديدة
    if (isset($_POST['add_room'])) {
        // Validate required fields
        if (empty($_POST['room_name'])) {
            $messages['error'] = "اسم الغرفة مطلوب";
        } else {
            try {
                $stmt = $pdo->prepare('INSERT INTO rooms (client_id, room_name, description) VALUES (?, ?, ?)');
                $stmt->execute([
                    $client_id,
                    trim($_POST['room_name']),
                    trim($_POST['description']) ?: null
                ]);
                $_SESSION['success'] = "تم إضافة الغرفة بنجاح";
                header('Location: rooms.php');
                exit;
            } catch (PDOException $e) {
                $messages['error'] = "حدث خطأ أثناء إضافة الغرفة";
            }
        }
    }

    // تعديل غرفة
    if (isset($_POST['edit_room'])) {
        try {
            $stmt = $pdo->prepare('UPDATE rooms SET room_name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE room_id = ? AND client_id = ?');
            $stmt->execute([
                trim($_POST['room_name']),
                trim($_POST['description']) ?: null,
                $_POST['room_id'],
                $client_id
            ]);
            $_SESSION['success'] = "تم تعديل الغرفة بنجاح";
            header('Location: rooms.php');
            exit;
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء تعديل الغرفة";
        }
    }

    // حذف غرفة
    if (isset($_GET['delete'])) {
        try {
            // التحقق من عدم وجود أجهزة في الغرفة
            $check_stmt = $pdo->prepare('SELECT COUNT(*) FROM devices WHERE room_id = ? AND client_id = ?');
            $check_stmt->execute([$_GET['delete'], $client_id]);
            $device_count = $check_stmt->fetchColumn();

            if ($device_count > 0) {
                $_SESSION['error'] = "لا يمكن حذف الغرفة لأنها تحتوي على أجهزة";
            } else {
                $stmt = $pdo->prepare('DELETE FROM rooms WHERE room_id = ? AND client_id = ?');
                $stmt->execute([$_GET['delete'], $client_id]);
                $_SESSION['success'] = "تم حذف الغرفة بنجاح";
            }
            header('Location: rooms.php');
            exit;
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء حذف الغرفة";
        }
    }
}

// التحقق من أعمدة جدول rooms أولاً
$rooms_columns = [];
try {
    $stmt = $pdo->query("DESCRIBE rooms");
    $rooms_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // إذا فشل الوصف، سنستخدم الأعمدة الأساسية فقط
    $rooms_columns = ['room_id', 'room_name', 'description'];
}

// بناء استعلام GROUP BY بناءً على الأعمدة الموجودة
$group_by_columns = ['r.room_id', 'r.room_name'];
if (in_array('description', $rooms_columns)) {
    $group_by_columns[] = 'r.description';
}
if (in_array('created_at', $rooms_columns)) {
    $group_by_columns[] = 'r.created_at';
}

$group_by_clause = implode(', ', $group_by_columns);

// Fetch rooms with device count
$rooms_stmt = $pdo->prepare("
    SELECT r.*,
           COUNT(d.device_id) as device_count,
           COUNT(CASE WHEN d.status = 'available' THEN 1 END) as available_devices,
           COUNT(CASE WHEN d.status = 'occupied' THEN 1 END) as occupied_devices
    FROM rooms r
    LEFT JOIN devices d ON r.room_id = d.room_id
    WHERE r.client_id = ?
    GROUP BY $group_by_clause
    ORDER BY r.room_name
");
$rooms_stmt->execute([$client_id]);
$rooms = $rooms_stmt->fetchAll();

require_once 'includes/header.php';

// Display any messages stored in session
if (isset($_SESSION['success'])) {
    $messages['success'] = $_SESSION['success'];
    unset($_SESSION['success']);
}
if (isset($_SESSION['error'])) {
    $messages['error'] = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($messages['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $messages['success']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if (isset($messages['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $messages['error']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إضافة غرفة جديدة -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-door-open me-2"></i>إضافة غرفة جديدة</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="room_name" class="form-label">اسم الغرفة</label>
                            <input type="text" class="form-control" id="room_name" name="room_name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الغرفة</div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف (اختياري)</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <button type="submit" name="add_room" class="btn btn-primary w-100">
                            <i class="fas fa-plus-circle me-2"></i>إضافة الغرفة
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- عرض الغرف -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الغرف</h5>
                </div>
                <div class="card-body">
                    <?php if (count($rooms) > 0): ?>
                        <div class="row g-3">
                            <?php foreach ($rooms as $room): ?>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-door-open me-2"></i>
                                                <?php echo htmlspecialchars($room['room_name']); ?>
                                            </h6>
                                            <?php if ($room['description']): ?>
                                                <p class="card-text text-muted">
                                                    <?php echo htmlspecialchars($room['description']); ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-door-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد غرف مسجلة بعد</h5>
                            <p class="text-muted">ابدأ بإضافة غرفة جديدة لتنظيم أجهزتك</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>