<?php
// استعادة كلمة المرور للموظفين
require_once '../config/database.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من وجود الموظف
        $stmt = $pdo->prepare("
            SELECT e.*, c.owner_name, c.business_name 
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.username = ? AND e.phone = ?
        ");
        $stmt->execute([$_POST['username'], $_POST['phone']]);
        $employee = $stmt->fetch();

        if ($employee) {
            // إنشاء كلمة مرور جديدة
            $new_password = bin2hex(random_bytes(4));
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

            // تحديث كلمة المرور
            $updateStmt = $pdo->prepare("
                UPDATE employees 
                SET password_hash = ? 
                WHERE id = ?
            ");
            $updateStmt->execute([$password_hash, $employee['id']]);

            $success = "تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة هي: " . $new_password;
        } else {
            $error = "لم نتمكن من العثور على حسابك. يرجى التحقق من البيانات المدخلة";
        }
    } catch (PDOException $e) {
        $error = "حدث خطأ في النظام";
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <h3 class="text-center mb-4">استعادة كلمة المرور</h3>
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <?php if (isset($success)): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                                <hr>
                                <a href="employee-login.php" class="btn btn-success w-100">
                                    العودة لتسجيل الدخول
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" name="username" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف المسجل</label>
                                    <input type="tel" name="phone" class="form-control" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    استعادة كلمة المرور
                                </button>
                                <a href="employee-login.php" class="btn btn-light w-100">
                                    العودة لتسجيل الدخول
                                </a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>