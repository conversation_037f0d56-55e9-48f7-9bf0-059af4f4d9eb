<?php
/**
 * حارس المصادقة - Auth Guard
 * يحمي الصفحات من الوصول غير المصرح به ويمنع سرقة الجلسات
 * PlayGood Gaming Center Management System
 */

// منع الوصول المباشر
if (!defined('AUTH_GUARD')) {
    define('AUTH_GUARD', true);
}

// تضمين نظام الحماية المتقدم
require_once __DIR__ . '/secure_session_middleware.php';

/**
 * حماية صفحات المدير
 */
function protectAdminPage($pdo) {
    // التحقق البسيط أولاً
    if (!isset($_SESSION['admin_id'])) {
        header('Location: login.php');
        exit;
    }

    // التحقق من وجود رسالة أمنية
    if (isset($_SESSION['security_error'])) {
        $error_message = $_SESSION['security_error'];
        unset($_SESSION['security_error']);

        // عرض رسالة التحذير الأمني
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'تحذير أمني',
                        text: '" . addslashes($error_message) . "',
                        confirmButtonText: 'موافق'
                    });
                } else {
                    alert('" . addslashes($error_message) . "');
                }
            });
        </script>";
    }

    // محاولة التحقق المتقدم (اختياري)
    try {
        $middleware = SecureSessionMiddleware::getInstance($pdo);
        return $middleware->validateAdminSession();
    } catch (Exception $e) {
        // في حالة فشل النظام المتقدم، استخدم التحقق البسيط
        error_log("خطأ في النظام الأمني المتقدم: " . $e->getMessage());
        return true;
    }
}

/**
 * حماية صفحات العميل
 */
function protectClientPage($pdo) {
    // التحقق البسيط أولاً
    if (!isset($_SESSION['client_id'])) {
        header('Location: login.php');
        exit;
    }

    // التحقق من وجود رسالة أمنية
    if (isset($_SESSION['security_error'])) {
        $error_message = $_SESSION['security_error'];
        unset($_SESSION['security_error']);

        // عرض رسالة التحذير الأمني
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'تحذير أمني',
                        text: '" . addslashes($error_message) . "',
                        confirmButtonText: 'موافق'
                    });
                } else {
                    alert('" . addslashes($error_message) . "');
                }
            });
        </script>";
    }

    // محاولة التحقق المتقدم (اختياري)
    try {
        $middleware = SecureSessionMiddleware::getInstance($pdo);
        return $middleware->validateClientSession();
    } catch (Exception $e) {
        // في حالة فشل النظام المتقدم، استخدم التحقق البسيط
        error_log("خطأ في النظام الأمني المتقدم: " . $e->getMessage());
        return true;
    }
}

/**
 * حماية صفحات الموظف
 */
function protectEmployeePage($pdo) {
    // التحقق البسيط أولاً
    if (!isset($_SESSION['employee_id'])) {
        header('Location: employee-login.php');
        exit;
    }

    // التحقق من وجود رسالة أمنية
    if (isset($_SESSION['security_error'])) {
        $error_message = $_SESSION['security_error'];
        unset($_SESSION['security_error']);

        // عرض رسالة التحذير الأمني
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'تحذير أمني',
                        text: '" . addslashes($error_message) . "',
                        confirmButtonText: 'موافق'
                    });
                } else {
                    alert('" . addslashes($error_message) . "');
                }
            });
        </script>";
    }

    // محاولة التحقق المتقدم (اختياري)
    try {
        $middleware = SecureSessionMiddleware::getInstance($pdo);
        return $middleware->validateEmployeeSession();
    } catch (Exception $e) {
        // في حالة فشل النظام المتقدم، استخدم التحقق البسيط
        error_log("خطأ في النظام الأمني المتقدم: " . $e->getMessage());
        return true;
    }
}

/**
 * حماية API endpoints
 */
function protectApiEndpoint($pdo, $user_type = 'client') {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    
    $is_valid = false;
    
    switch ($user_type) {
        case 'admin':
            $is_valid = isset($_SESSION['admin_id']) && $middleware->validateSession();
            break;
        case 'client':
            $is_valid = isset($_SESSION['client_id']) && $middleware->validateSession();
            break;
        case 'employee':
            $is_valid = isset($_SESSION['employee_id']) && $middleware->validateSession();
            break;
    }
    
    if (!$is_valid) {
        http_response_code(401);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح لك بالوصول - جلسة غير صحيحة',
            'code' => 'INVALID_SESSION'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    return true;
}

/**
 * تسجيل خروج آمن
 */
function secureLogout($pdo, $redirect_url = null) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    $middleware->destroySecureSession();
    
    if ($redirect_url) {
        header('Location: ' . $redirect_url);
        exit;
    }
}

/**
 * التحقق من النشاط المشبوه
 */
function checkSuspiciousActivity($pdo) {
    $middleware = SecureSessionMiddleware::getInstance($pdo);
    
    if ($middleware->checkSuspiciousActivity()) {
        // تسجيل النشاط المشبوه
        error_log("نشاط مشبوه من IP: " . $_SERVER['REMOTE_ADDR']);
        
        // إنهاء الجلسة
        $middleware->destroySecureSession();
        
        // إعادة توجيه مع رسالة تحذير
        session_start();
        $_SESSION['security_error'] = 'تم اكتشاف نشاط مشبوه. تم حظر وصولك مؤقتاً.';
        
        http_response_code(403);
        die('تم حظر وصولك مؤقتاً بسبب نشاط مشبوه');
    }
}

/**
 * إضافة headers أمنية
 */
function addSecurityHeaders() {
    // منع clickjacking
    header('X-Frame-Options: DENY');
    
    // منع MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // تفعيل XSS protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self';");
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // HTTPS enforcement (إذا كان متاحاً)
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    
    return $input;
}

/**
 * التحقق من CSRF token
 */
function validateCsrfToken($token, $form_name = 'default') {
    if (!isset($_SESSION['csrf_tokens'][$form_name])) {
        return false;
    }
    
    $stored_token = $_SESSION['csrf_tokens'][$form_name];
    
    // التحقق من انتهاء الصلاحية
    if (time() - $stored_token['timestamp'] > 3600) {
        unset($_SESSION['csrf_tokens'][$form_name]);
        return false;
    }
    
    $valid = hash_equals($stored_token['token'], $token);
    
    if ($valid) {
        unset($_SESSION['csrf_tokens'][$form_name]);
    }
    
    return $valid;
}

/**
 * توليد CSRF token
 */
function generateCsrfToken($form_name = 'default') {
    if (!isset($_SESSION['csrf_tokens'])) {
        $_SESSION['csrf_tokens'] = [];
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_tokens'][$form_name] = [
        'token' => $token,
        'timestamp' => time()
    ];
    
    return $token;
}

/**
 * إضافة JavaScript للحماية من session hijacking
 */
function addSessionProtectionJS() {
    echo "<script>
    // حماية من session hijacking في JavaScript
    (function() {
        'use strict';
        
        // مراقبة تغيير الـ URL
        let currentUrl = window.location.href;
        setInterval(function() {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                // التحقق من صحة الجلسة عند تغيير الصفحة
                checkSessionValidity();
            }
        }, 1000);
        
        // التحقق من صحة الجلسة
        function checkSessionValidity() {
            fetch(window.location.pathname + '?session_check=1', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (response.status === 401) {
                    window.location.href = '/client/login.php';
                }
            })
            .catch(error => {
                console.error('خطأ في التحقق من الجلسة:', error);
            });
        }
        
        // مراقبة النشاط المشبوه
        let suspiciousActivity = 0;
        
        // مراقبة محاولات فتح أدوات المطور
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'U')) {
                suspiciousActivity++;
                if (suspiciousActivity > 3) {
                    alert('تم اكتشاف نشاط مشبوه. سيتم تسجيل خروجك.');
                    window.location.href = '/client/logout.php';
                }
            }
        });
        
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            suspiciousActivity++;
            if (suspiciousActivity > 5) {
                alert('تم اكتشاف نشاط مشبوه. سيتم تسجيل خروجك.');
                window.location.href = '/client/logout.php';
            }
        });
        
    })();
    </script>";
}

// إضافة headers الأمان تلقائياً
addSecurityHeaders();

// التحقق من النشاط المشبوه إذا كان PDO متاحاً
if (isset($pdo)) {
    checkSuspiciousActivity($pdo);
}
