/* القائمة الجانبية العائمة - نظام إدارة محلات البلايستيشن */

/* أنماط القائمة الجانبية العائمة */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    color: white;
    font-size: 1.1rem;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.sidebar-toggle:focus {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

.floating-sidebar {
    position: fixed;
    top: 0;
    right: -350px;
    width: 320px;
    height: 100vh;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1050;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    overflow-x: hidden;
}

.floating-sidebar.show {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.sidebar-close {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: rotate(90deg);
}

.sidebar-content {
    padding: 1rem 0;
}

.floating-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    margin: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.floating-sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #fff;
    transform: translateX(-5px);
    text-decoration: none;
}

.floating-sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: #fff;
    font-weight: 600;
}

.floating-sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
    font-size: 1rem;
}

.floating-sidebar .nav-link span {
    flex: 1;
}

.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 1rem 1.5rem;
    border-width: 1px;
}

.sidebar-user-info {
    padding: 0 1.5rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.sidebar-user-info .fw-bold {
    font-size: 0.9rem;
    line-height: 1.2;
}

.sidebar-user-info .text-muted {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6) !important;
}

.logout-link {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 1rem;
    color: rgba(255, 255, 255, 0.7) !important;
}

.logout-link:hover {
    background: rgba(220, 53, 69, 0.2) !important;
    border-left-color: #dc3545 !important;
    color: white !important;
}

/* تأثيرات متحركة */
@keyframes slideInRight {
    from {
        right: -350px;
        opacity: 0;
    }
    to {
        right: 0;
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        right: 0;
        opacity: 1;
    }
    to {
        right: -350px;
        opacity: 0;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.8);
    }
    100% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floating-sidebar .nav-link {
    animation: fadeIn 0.3s ease forwards;
}

.floating-sidebar .nav-link:nth-child(1) { animation-delay: 0.1s; }
.floating-sidebar .nav-link:nth-child(2) { animation-delay: 0.2s; }
.floating-sidebar .nav-link:nth-child(3) { animation-delay: 0.3s; }
.floating-sidebar .nav-link:nth-child(4) { animation-delay: 0.4s; }
.floating-sidebar .nav-link:nth-child(5) { animation-delay: 0.5s; }

/* تصميم متجاوب */
@media (max-width: 768px) {
    .floating-sidebar {
        width: 280px;
        right: -280px;
    }
    
    .sidebar-toggle {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
    
    .sidebar-header {
        padding: 1rem;
    }
    
    .sidebar-header h5 {
        font-size: 1rem;
    }
    
    .floating-sidebar .nav-link {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .sidebar-user-info {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .floating-sidebar {
        width: 250px;
        right: -250px;
    }
    
    .sidebar-toggle {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
}

/* إخفاء القائمة عند الطباعة */
@media print {
    .sidebar-toggle,
    .floating-sidebar,
    .sidebar-overlay {
        display: none !important;
    }
}

/* تحسينات إضافية */
.floating-sidebar::-webkit-scrollbar {
    width: 6px;
}

.floating-sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.floating-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.floating-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* تأثيرات التحميل */
.nav-link.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* تحسينات الأداء */
.floating-sidebar {
    will-change: transform;
    transform: translateZ(0);
}

.sidebar-toggle {
    will-change: transform;
    transform: translateZ(0);
}

/* حالات خاصة */
.floating-sidebar.show .nav-link {
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

.floating-sidebar.closing .nav-link {
    animation: fadeOut 0.2s ease forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}
