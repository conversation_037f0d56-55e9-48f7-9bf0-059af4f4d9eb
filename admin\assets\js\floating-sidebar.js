/**
 * القائمة الجانبية العائمة - نظام إدارة محلات البلايستيشن
 * Floating Sidebar JavaScript Module
 */

class FloatingSidebar {
    constructor() {
        this.sidebarToggle = null;
        this.sidebarClose = null;
        this.floatingSidebar = null;
        this.sidebarOverlay = null;
        this.isOpen = false;
        this.isAnimating = false;
        
        this.init();
    }
    
    init() {
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupElements());
        } else {
            this.setupElements();
        }
    }
    
    setupElements() {
        // الحصول على العناصر
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.sidebarClose = document.getElementById('sidebarClose');
        this.floatingSidebar = document.getElementById('floatingSidebar');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (!this.sidebarToggle || !this.floatingSidebar) {
            console.warn('Floating sidebar elements not found');
            return;
        }
        
        this.bindEvents();
        this.setupAnimations();
        this.addPulseEffect();
    }
    
    bindEvents() {
        // أحداث النقر
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.openSidebar();
            });
        }
        
        if (this.sidebarClose) {
            this.sidebarClose.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeSidebar();
            });
        }
        
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // إغلاق بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeSidebar();
            }
        });
        
        // منع إغلاق القائمة عند النقر داخلها
        if (this.floatingSidebar) {
            this.floatingSidebar.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
        
        // تأثير تحميل الصفحات
        this.setupLinkEffects();
        
        // أحداث تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    openSidebar() {
        if (this.isAnimating || this.isOpen) return;
        
        this.isAnimating = true;
        this.isOpen = true;
        
        // إضافة الفئات
        if (this.floatingSidebar) {
            this.floatingSidebar.classList.add('show');
        }
        if (this.sidebarOverlay) {
            this.sidebarOverlay.classList.add('show');
        }
        
        // منع التمرير في الخلفية
        document.body.style.overflow = 'hidden';
        
        // إزالة تأثير النبضة
        if (this.sidebarToggle) {
            this.sidebarToggle.style.animation = '';
        }
        
        // انتهاء الرسوم المتحركة
        setTimeout(() => {
            this.isAnimating = false;
        }, 300);
        
        // تشغيل حدث مخصص
        this.dispatchEvent('sidebarOpened');
    }
    
    closeSidebar() {
        if (this.isAnimating || !this.isOpen) return;
        
        this.isAnimating = true;
        this.isOpen = false;
        
        // إضافة فئة الإغلاق
        if (this.floatingSidebar) {
            this.floatingSidebar.classList.add('closing');
        }
        
        // إزالة الفئات
        setTimeout(() => {
            if (this.floatingSidebar) {
                this.floatingSidebar.classList.remove('show', 'closing');
            }
            if (this.sidebarOverlay) {
                this.sidebarOverlay.classList.remove('show');
            }
            
            // استعادة التمرير
            document.body.style.overflow = '';
            
            this.isAnimating = false;
        }, 300);
        
        // تشغيل حدث مخصص
        this.dispatchEvent('sidebarClosed');
    }
    
    setupLinkEffects() {
        const links = document.querySelectorAll('.floating-sidebar .nav-link');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.href && !link.href.includes('#') && !link.classList.contains('logout-link')) {
                    const icon = link.querySelector('i');
                    if (icon && !icon.classList.contains('fa-sign-out-alt')) {
                        // إضافة تأثير التحميل
                        icon.className = 'fas fa-spinner fa-spin me-2';
                        link.classList.add('loading');
                        
                        // إغلاق القائمة بعد النقر
                        setTimeout(() => {
                            this.closeSidebar();
                        }, 200);
                    }
                }
            });
        });
    }
    
    setupAnimations() {
        if (!this.floatingSidebar) return;
        
        // إضافة تأخير للرسوم المتحركة للروابط
        const links = this.floatingSidebar.querySelectorAll('.nav-link');
        links.forEach((link, index) => {
            link.style.animationDelay = `${(index + 1) * 0.1}s`;
        });
    }
    
    addPulseEffect() {
        // إضافة تأثير النبضة للزر بعد التحميل
        setTimeout(() => {
            if (this.sidebarToggle && !this.isOpen) {
                this.sidebarToggle.style.animation = 'pulse 2s infinite';
                
                // إيقاف النبضة بعد 10 ثوان
                setTimeout(() => {
                    if (this.sidebarToggle) {
                        this.sidebarToggle.style.animation = '';
                    }
                }, 10000);
            }
        }, 1000);
    }
    
    handleResize() {
        // إغلاق القائمة عند تغيير حجم الشاشة للشاشات الكبيرة
        if (window.innerWidth > 1200 && this.isOpen) {
            this.closeSidebar();
        }
    }
    
    dispatchEvent(eventName) {
        const event = new CustomEvent(eventName, {
            detail: {
                sidebar: this,
                isOpen: this.isOpen
            }
        });
        document.dispatchEvent(event);
    }
    
    // طرق عامة للتحكم
    toggle() {
        if (this.isOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }
    
    isOpened() {
        return this.isOpen;
    }
    
    destroy() {
        // إزالة جميع المستمعين والعناصر
        if (this.sidebarToggle) {
            this.sidebarToggle.removeEventListener('click', this.openSidebar);
        }
        if (this.sidebarClose) {
            this.sidebarClose.removeEventListener('click', this.closeSidebar);
        }
        if (this.sidebarOverlay) {
            this.sidebarOverlay.removeEventListener('click', this.closeSidebar);
        }
        
        document.removeEventListener('keydown', this.handleKeydown);
        window.removeEventListener('resize', this.handleResize);
    }
}

// إنشاء مثيل عام للقائمة الجانبية
let floatingSidebarInstance = null;

// تهيئة القائمة الجانبية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    floatingSidebarInstance = new FloatingSidebar();
    
    // إضافة الكائن للنطاق العام للوصول إليه من أي مكان
    window.FloatingSidebar = floatingSidebarInstance;
});

// دوال مساعدة عامة
window.openSidebar = function() {
    if (floatingSidebarInstance) {
        floatingSidebarInstance.openSidebar();
    }
};

window.closeSidebar = function() {
    if (floatingSidebarInstance) {
        floatingSidebarInstance.closeSidebar();
    }
};

window.toggleSidebar = function() {
    if (floatingSidebarInstance) {
        floatingSidebarInstance.toggle();
    }
};

// إضافة مستمعين للأحداث المخصصة
document.addEventListener('sidebarOpened', function(e) {
    console.log('Sidebar opened', e.detail);
});

document.addEventListener('sidebarClosed', function(e) {
    console.log('Sidebar closed', e.detail);
});

// تصدير الفئة للاستخدام في وحدات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloatingSidebar;
}

// دعم AMD
if (typeof define === 'function' && define.amd) {
    define([], function() {
        return FloatingSidebar;
    });
}

// إضافة أنماط CSS ديناميكياً إذا لم تكن محملة
function ensureStylesLoaded() {
    const existingLink = document.querySelector('link[href*="floating-sidebar.css"]');
    if (!existingLink) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'assets/css/floating-sidebar.css';
        document.head.appendChild(link);
    }
}

// تشغيل فحص الأنماط
ensureStylesLoaded();

// إضافة تحسينات الأداء
if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
        // تحسينات إضافية يمكن تشغيلها عند عدم انشغال المتصفح
        console.log('Floating sidebar optimizations loaded');
    });
}
