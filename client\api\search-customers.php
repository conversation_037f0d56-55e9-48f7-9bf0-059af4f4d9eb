<?php
// تنظيف أي إخراج سابق
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من الصلاحيات (السماح للجميع بالبحث عن العملاء لبدء الجلسات)
// if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_customers')) {
//     http_response_code(403);
//     echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لعرض العملاء']);
//     exit;
// }

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$query = $_GET['q'] ?? '';

if (strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

try {
    // إضافة تسجيل للتشخيص
    error_log("البحث عن العملاء - العميل: $client_id، الاستعلام: $query");

    // التحقق من وجود جدول customers
    $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($table_check->rowCount() == 0) {
        error_log('جدول العملاء غير موجود');
        echo json_encode(['success' => false, 'message' => 'جدول العملاء غير موجود'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // التحقق من هيكل جدول customers
    $customer_id_column = 'customer_id';
    try {
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
            $customer_id_column = 'id';
        }
        error_log('أعمدة جدول العملاء: ' . implode(', ', $columns));
    } catch (PDOException $e) {
        $customer_id_column = 'customer_id'; // افتراضي
        error_log('خطأ في فحص هيكل الجدول: ' . $e->getMessage());
    }

    // تحضير مصطلح البحث
    $search_term = "%{$query}%";

    // التحقق من وجود العمود client_id
    $where_clause = "client_id = ?";
    $params = [$client_id, $search_term, $search_term, $search_term];

    // بناء الاستعلام
    $sql = "
        SELECT $customer_id_column as id, name, phone, email
        FROM customers
        WHERE $where_clause
        AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
        ORDER BY name ASC
        LIMIT 10
    ";

    error_log("استعلام البحث: $sql");
    error_log("معاملات البحث: " . json_encode($params));

    $search_stmt = $pdo->prepare($sql);
    $search_stmt->execute($params);

    $customers = $search_stmt->fetchAll(PDO::FETCH_ASSOC);

    error_log("عدد النتائج الموجودة: " . count($customers));

    // إذا لم توجد نتائج، جرب بحث أوسع
    if (empty($customers)) {
        error_log("لا توجد نتائج، جرب بحث أوسع...");

        // بحث في جميع العملاء للعميل الحالي
        $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE client_id = ?");
        $count_stmt->execute([$client_id]);
        $total_customers = $count_stmt->fetchColumn();

        error_log("إجمالي العملاء للعميل $client_id: $total_customers");

        if ($total_customers == 0) {
            echo json_encode(['success' => false, 'message' => 'لا يوجد عملاء مسجلين'], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    // تنظيف البيانات
    foreach ($customers as &$customer) {
        $customer['name'] = htmlspecialchars($customer['name'] ?? '');
        $customer['phone'] = htmlspecialchars($customer['phone'] ?? '');
        $customer['email'] = htmlspecialchars($customer['email'] ?? '');
    }

    echo json_encode([
        'success' => true,
        'customers' => $customers
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    error_log('خطأ في البحث عن العملاء: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
