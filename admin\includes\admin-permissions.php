<?php
/**
 * نظام صلاحيات الإدمن - PlayGood
 * ملف يحتوي على دوال التحقق من صلاحيات الوصول لصفحات لوحة تحكم الإدمن
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تضمين قاعدة البيانات
if (!isset($pdo)) {
    require_once __DIR__ . '/../../config/database.php';
}

/**
 * التحقق من صلاحية الإدمن للوصول لصفحة معينة
 * @param string $page_name اسم الصفحة
 * @param int|null $admin_id معرف الإدمن (اختياري، سيتم استخدام الجلسة الحالية إذا لم يتم تمريره)
 * @return bool هل الإدمن لديه صلاحية الوصول
 */
function hasAdminPagePermission($page_name, $admin_id = null) {
    global $pdo;

    // استخدام معرف الإدمن من الجلسة إذا لم يتم تمريره
    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        $admin_id = $_SESSION['admin_id'];
    }

    try {
        // التحقق من وجود جدول صلاحيات الإدمن
        $table_check = $pdo->query("SHOW TABLES LIKE 'admin_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، السماح بالوصول لجميع الصفحات
            return true;
        }

        // جلب دور الإدمن
        $stmt = $pdo->prepare("SELECT role FROM admins WHERE admin_id = ? AND is_active = 1");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();
        
        if (!$admin) {
            return false;
        }

        // Super Admin يمكنه الوصول لجميع الصفحات
        if ($admin['role'] === 'super_admin') {
            return true;
        }

        // التحقق من متطلبات الصفحة والصلاحيات
        $stmt = $pdo->prepare("
            SELECT 
                ap.required_role,
                ap.is_default,
                COALESCE(app.is_enabled, ap.is_default) as has_permission
            FROM admin_pages ap
            LEFT JOIN admin_page_permissions app ON ap.page_id = app.page_id AND app.admin_id = ?
            WHERE ap.page_name = ? AND ap.is_active = TRUE
        ");
        $stmt->execute([$admin_id, $page_name]);
        $result = $stmt->fetch();

        if (!$result) {
            return false;
        }

        // التحقق من متطلبات الدور
        if ($result['required_role'] === 'super_admin' && $admin['role'] !== 'super_admin') {
            return false;
        }

        if ($result['required_role'] === 'admin' && !in_array($admin['role'], ['super_admin', 'admin'])) {
            return false;
        }

        return (bool)$result['has_permission'];

    } catch (Exception $e) {
        // في حالة حدوث خطأ، السماح بالوصول (للأمان)
        error_log("Admin permission check error: " . $e->getMessage());
        return true;
    }
}

/**
 * التحقق من صلاحية الصفحة الحالية وإعادة التوجيه إذا لم تكن مسموحة
 */
function checkCurrentAdminPagePermission() {
    $current_page = basename($_SERVER['PHP_SELF'], '.php');

    // الصفحات المستثناة من فحص الصلاحيات
    $excluded_pages = ['login', 'logout'];

    if (!in_array($current_page, $excluded_pages)) {
        if (!hasAdminPagePermission($current_page)) {
            // إعادة التوجيه إلى لوحة التحكم مع رسالة خطأ
            $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
            header('Location: dashboard.php');
            exit();
        }
    }
}

/**
 * جلب قائمة الصفحات المسموحة للإدمن الحالي
 * @param int|null $admin_id معرف الإدمن (اختياري)
 * @return array قائمة الصفحات المسموحة
 */
function getAllowedAdminPages($admin_id = null) {
    global $pdo;

    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return [];
        }
        $admin_id = $_SESSION['admin_id'];
    }

    try {
        // التحقق من وجود جدول صلاحيات الإدمن
        $table_check = $pdo->query("SHOW TABLES LIKE 'admin_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، إرجاع جميع الصفحات الأساسية
            return [
                ['page_name' => 'dashboard', 'page_label' => 'لوحة التحكم', 'page_url' => 'dashboard.php', 'page_icon' => 'fas fa-dashboard', 'category' => 'main'],
                ['page_name' => 'clients', 'page_label' => 'إدارة العملاء', 'page_url' => 'clients.php', 'page_icon' => 'fas fa-users', 'category' => 'clients'],
                ['page_name' => 'reports', 'page_label' => 'التقارير', 'page_url' => 'reports.php', 'page_icon' => 'fas fa-chart-bar', 'category' => 'reports'],
                ['page_name' => 'settings', 'page_label' => 'الإعدادات', 'page_url' => 'settings.php', 'page_icon' => 'fas fa-cog', 'category' => 'settings']
            ];
        }

        // جلب دور الإدمن
        $stmt = $pdo->prepare("SELECT role FROM admins WHERE admin_id = ? AND is_active = 1");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();
        
        if (!$admin) {
            return [];
        }

        // بناء الاستعلام حسب دور الإدمن
        if ($admin['role'] === 'super_admin') {
            // Super Admin يرى جميع الصفحات
            $stmt = $pdo->prepare("
                SELECT ap.page_name, ap.page_label, ap.page_url, ap.page_icon, ap.category
                FROM admin_pages ap
                WHERE ap.is_active = TRUE
                ORDER BY ap.category, ap.page_label
            ");
            $stmt->execute();
        } else {
            // المديرين العاديين يرون الصفحات المسموحة لهم فقط
            $stmt = $pdo->prepare("
                SELECT ap.page_name, ap.page_label, ap.page_url, ap.page_icon, ap.category
                FROM admin_pages ap
                LEFT JOIN admin_page_permissions app ON ap.page_id = app.page_id AND app.admin_id = ?
                WHERE ap.is_active = TRUE
                AND ap.required_role != 'super_admin'
                AND (ap.required_role = 'any' OR ap.required_role = 'admin')
                AND COALESCE(app.is_enabled, ap.is_default) = TRUE
                ORDER BY ap.category, ap.page_label
            ");
            $stmt->execute([$admin_id]);
        }

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Get allowed admin pages error: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من دور الإدمن
 * @param string $required_role الدور المطلوب
 * @param int|null $admin_id معرف الإدمن (اختياري)
 * @return bool هل الإدمن لديه الدور المطلوب
 */
function hasAdminRole($required_role, $admin_id = null) {
    global $pdo;

    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        $admin_id = $_SESSION['admin_id'];
    }

    try {
        $stmt = $pdo->prepare("SELECT role FROM admins WHERE admin_id = ? AND is_active = 1");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();
        
        if (!$admin) {
            return false;
        }

        // Super Admin يمكنه الوصول لجميع الأدوار
        if ($admin['role'] === 'super_admin') {
            return true;
        }

        return $admin['role'] === $required_role;

    } catch (Exception $e) {
        error_log("Admin role check error: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب معلومات الإدمن الحالي
 * @param int|null $admin_id معرف الإدمن (اختياري)
 * @return array|false معلومات الإدمن أو false في حالة عدم وجوده
 */
function getCurrentAdminInfo($admin_id = null) {
    global $pdo;

    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        $admin_id = $_SESSION['admin_id'];
    }

    try {
        $stmt = $pdo->prepare("
            SELECT admin_id, username, email, full_name, role, created_at, last_login
            FROM admins 
            WHERE admin_id = ? AND is_active = 1
        ");
        $stmt->execute([$admin_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Get admin info error: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل نشاط الإدمن (للمراجعة المستقبلية)
 * @param string $action النشاط المنفذ
 * @param string $details تفاصيل النشاط
 * @param int|null $admin_id معرف الإدمن (اختياري)
 */
function logAdminActivity($action, $details = '', $admin_id = null) {
    global $pdo;

    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return;
        }
        $admin_id = $_SESSION['admin_id'];
    }

    try {
        // يمكن إضافة جدول لسجلات الأنشطة لاحقاً
        error_log("Admin Activity - ID: $admin_id, Action: $action, Details: $details");
    } catch (Exception $e) {
        error_log("Log admin activity error: " . $e->getMessage());
    }
}

/**
 * التحقق من صلاحية الإدمن لإدارة إدمن آخر
 * @param int $target_admin_id معرف الإدمن المراد إدارته
 * @param int|null $admin_id معرف الإدمن الحالي (اختياري)
 * @return bool هل يمكن إدارة الإدمن المحدد
 */
function canManageAdmin($target_admin_id, $admin_id = null) {
    global $pdo;

    if ($admin_id === null) {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        $admin_id = $_SESSION['admin_id'];
    }

    // لا يمكن إدارة النفس
    if ($admin_id == $target_admin_id) {
        return false;
    }

    try {
        // جلب أدوار كلا الإدمنين
        $stmt = $pdo->prepare("
            SELECT admin_id, role 
            FROM admins 
            WHERE admin_id IN (?, ?) AND is_active = 1
        ");
        $stmt->execute([$admin_id, $target_admin_id]);
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($admins) != 2) {
            return false;
        }

        $current_admin_role = null;
        $target_admin_role = null;

        foreach ($admins as $admin) {
            if ($admin['admin_id'] == $admin_id) {
                $current_admin_role = $admin['role'];
            } else {
                $target_admin_role = $admin['role'];
            }
        }

        // فقط Super Admin يمكنه إدارة المديرين الآخرين
        return $current_admin_role === 'super_admin';

    } catch (Exception $e) {
        error_log("Can manage admin check error: " . $e->getMessage());
        return false;
    }
}
