<?php
/**
 * ملف للتحقق من وجود جلسات نشطة للجهاز عبر AJAX
 */

session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مسموح بالوصول'], JSON_UNESCAPED_UNICODE);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة الطلب غير مسموحة'], JSON_UNESCAPED_UNICODE);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['device_id']) || !is_numeric($input['device_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف الجهاز مطلوب'], JSON_UNESCAPED_UNICODE);
    exit;
}

$client_id = $_SESSION['client_id'];
$device_id = intval($input['device_id']);

try {
    // التحقق من أن الجهاز ينتمي للعميل
    $device_check = $pdo->prepare("SELECT device_id FROM devices WHERE device_id = ? AND client_id = ?");
    $device_check->execute([$device_id, $client_id]);
    
    if (!$device_check->fetch()) {
        http_response_code(403);
        echo json_encode(['error' => 'غير مسموح بالوصول لهذا الجهاز'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // التحقق من وجود جلسات نشطة
    $active_sessions_stmt = $pdo->prepare("
        SELECT COUNT(*) as count, 
               GROUP_CONCAT(session_id) as session_ids
        FROM sessions 
        WHERE device_id = ? AND status = 'active'
    ");
    $active_sessions_stmt->execute([$device_id]);
    $result = $active_sessions_stmt->fetch(PDO::FETCH_ASSOC);

    $has_active_sessions = $result['count'] > 0;
    $session_ids = $has_active_sessions ? explode(',', $result['session_ids']) : [];

    // إرسال النتيجة
    echo json_encode([
        'has_active_sessions' => $has_active_sessions,
        'active_sessions_count' => intval($result['count']),
        'session_ids' => $session_ids
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    error_log('خطأ في التحقق من الجلسات النشطة: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ في الخادم'], JSON_UNESCAPED_UNICODE);
}
?>
