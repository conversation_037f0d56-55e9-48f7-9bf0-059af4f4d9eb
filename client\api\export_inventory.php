<?php
require_once '../../config/database.php';
require_once '../includes/auth.php';

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    require_once '../includes/employee-auth.php';
    if (!employeeHasPermission('manage_cafeteria')) {
        http_response_code(403);
        exit('غير مسموح');
    }
}

try {
    // تحديد معرف العميل
    $client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
    
    // معاملات البحث والفلترة
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $category_filter = isset($_GET['category']) ? trim($_GET['category']) : '';
    $status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    // بناء استعلام البحث
    $where_conditions = ['client_id = ?'];
    $params = [$client_id];
    
    if (!empty($search)) {
        $where_conditions[] = '(name LIKE ? OR barcode LIKE ? OR supplier LIKE ?)';
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = 'category = ?';
        $params[] = $category_filter;
    }
    
    if (!empty($status_filter)) {
        switch ($status_filter) {
            case 'low_stock':
                $where_conditions[] = 'stock_quantity <= min_stock_level AND stock_quantity > 0';
                break;
            case 'out_of_stock':
                $where_conditions[] = 'stock_quantity <= 0';
                break;
            case 'available':
                $where_conditions[] = 'stock_quantity > min_stock_level';
                break;
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // جلب البيانات
    $sql = "SELECT 
        name as 'اسم المنتج',
        category as 'التصنيف',
        stock_quantity as 'الكمية',
        CASE 
            WHEN stock_quantity <= 0 THEN 'نفذ'
            WHEN stock_quantity <= min_stock_level THEN 'ناقص'
            WHEN stock_quantity >= max_stock_level THEN 'مكتمل'
            ELSE 'متوفر'
        END as 'الحالة',
        cost_price as 'سعر التكلفة',
        price as 'سعر البيع',
        (price - cost_price) as 'الربح',
        ROUND(((price - cost_price) / price) * 100, 2) as 'نسبة الربح %',
        min_stock_level as 'الحد الأدنى',
        max_stock_level as 'الحد الأقصى',
        barcode as 'الباركود',
        supplier as 'المورد',
        (stock_quantity * cost_price) as 'قيمة المخزن',
        DATE(created_at) as 'تاريخ الإضافة',
        DATE(last_restock_date) as 'آخر تجديد'
        FROM cafeteria_items 
        WHERE $where_clause 
        ORDER BY name ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحديد نوع التصدير
    $export_type = $_GET['export'] ?? 'excel';
    
    if ($export_type === 'excel') {
        // تصدير Excel
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="inventory_' . date('Y-m-d_H-i-s') . '.xls"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        // عنوان التقرير
        echo "تقرير المخزن - " . date('Y-m-d H:i:s') . "\n\n";
        
        if (!empty($products)) {
            // رؤوس الأعمدة
            $headers = array_keys($products[0]);
            echo implode("\t", $headers) . "\n";
            
            // البيانات
            foreach ($products as $product) {
                $row = [];
                foreach ($product as $value) {
                    $row[] = $value ?? '';
                }
                echo implode("\t", $row) . "\n";
            }
            
            // إحصائيات إجمالية
            echo "\n\nالإحصائيات الإجمالية:\n";
            echo "إجمالي المنتجات:\t" . count($products) . "\n";
            
            $total_value = array_sum(array_column($products, 'قيمة المخزن'));
            echo "إجمالي قيمة المخزن:\t" . number_format($total_value, 2) . " ج.م\n";
            
            $out_of_stock = count(array_filter($products, function($p) { return $p['الحالة'] === 'نفذ'; }));
            echo "المنتجات النافذة:\t" . $out_of_stock . "\n";
            
            $low_stock = count(array_filter($products, function($p) { return $p['الحالة'] === 'ناقص'; }));
            echo "المنتجات الناقصة:\t" . $low_stock . "\n";
        } else {
            echo "لا توجد بيانات للتصدير";
        }
        
    } elseif ($export_type === 'csv') {
        // تصدير CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="inventory_' . date('Y-m-d_H-i-s') . '.csv"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        $output = fopen('php://output', 'w');
        
        if (!empty($products)) {
            // رؤوس الأعمدة
            fputcsv($output, array_keys($products[0]));
            
            // البيانات
            foreach ($products as $product) {
                fputcsv($output, array_values($product));
            }
        }
        
        fclose($output);
        
    } else {
        // تصدير JSON
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="inventory_' . date('Y-m-d_H-i-s') . '.json"');
        
        $export_data = [
            'export_date' => date('Y-m-d H:i:s'),
            'total_products' => count($products),
            'filters' => [
                'search' => $search,
                'category' => $category_filter,
                'status' => $status_filter
            ],
            'products' => $products
        ];
        
        echo json_encode($export_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    http_response_code(500);
    echo "خطأ غير متوقع: " . $e->getMessage();
}
?>
