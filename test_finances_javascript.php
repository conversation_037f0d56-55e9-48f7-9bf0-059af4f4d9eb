<?php
/**
 * اختبار وظائف الجافاسكريبت في صفحة الماليات - PlayGood
 * للتحقق من عرض المبالغ بشكل صحيح
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

require_once 'config/database.php';

$client_id = $_SESSION['client_id'];

// جلب بعض البيانات للاختبار
try {
    // جلب ملخص مالي بسيط
    $current_month = date('Y-m');
    
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(amount), 0) as total_expenses
        FROM expenses
        WHERE client_id = ? AND DATE_FORMAT(expense_date, '%Y-%m') = ?
    ");
    $stmt->execute([$client_id, $current_month]);
    $total_expenses = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(amount), 0) as total_additional_income
        FROM additional_income
        WHERE client_id = ? AND DATE_FORMAT(income_date, '%Y-%m') = ?
    ");
    $stmt->execute([$client_id, $current_month]);
    $total_additional_income = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(s.total_cost), 0) as total_sessions_income
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
        AND s.status = 'completed'
        AND DATE_FORMAT(s.start_time, '%Y-%m') = ?
    ");
    $stmt->execute([$client_id, $current_month]);
    $total_sessions_income = $stmt->fetchColumn() ?: 0;
    
    $net_profit = ($total_sessions_income + $total_additional_income) - $total_expenses;
    
} catch (PDOException $e) {
    $total_expenses = 0;
    $total_additional_income = 0;
    $total_sessions_income = 0;
    $net_profit = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف الجافاسكريبت - الماليات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .amount-display { font-size: 1.5rem; font-weight: bold; padding: 10px; margin: 5px; border-radius: 5px; }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <h1 class="mb-4">🧪 اختبار وظائف الجافاسكريبت - صفحة الماليات</h1>
    
    <!-- اختبار عرض البيانات الأساسية -->
    <div class="test-section">
        <h2>1. اختبار عرض البيانات الأساسية (PHP)</h2>
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title text-danger">إجمالي المصروفات</h6>
                        <div class="amount-display text-danger" id="php-expenses">
                            <?php echo number_format($total_expenses, 2); ?> ج.م
                        </div>
                        <small>القيمة الخام: <?php echo $total_expenses; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title text-success">إيرادات الجلسات</h6>
                        <div class="amount-display text-success" id="php-sessions">
                            <?php echo number_format($total_sessions_income, 2); ?> ج.م
                        </div>
                        <small>القيمة الخام: <?php echo $total_sessions_income; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title text-success">إيرادات إضافية</h6>
                        <div class="amount-display text-success" id="php-additional">
                            <?php echo number_format($total_additional_income, 2); ?> ج.م
                        </div>
                        <small>القيمة الخام: <?php echo $total_additional_income; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title text-info">صافي الربح</h6>
                        <div class="amount-display <?php echo $net_profit >= 0 ? 'text-success' : 'text-danger'; ?>" id="php-profit">
                            <?php echo number_format($net_profit, 2); ?> ج.م
                        </div>
                        <small>القيمة الخام: <?php echo $net_profit; ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- اختبار وظائف الجافاسكريبت -->
    <div class="test-section">
        <h2>2. اختبار وظائف الجافاسكريبت</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>أ. اختبار formatNumber</h4>
                <div id="js-format-test"></div>
                <button class="btn btn-primary" onclick="testFormatNumber()">اختبار formatNumber</button>
            </div>
            <div class="col-md-6">
                <h4>ب. اختبار formatCurrency</h4>
                <div id="js-currency-test"></div>
                <button class="btn btn-success" onclick="testFormatCurrency()">اختبار formatCurrency</button>
            </div>
        </div>
    </div>
    
    <!-- اختبار تحديث البيانات -->
    <div class="test-section">
        <h2>3. اختبار تحديث البيانات ديناميكياً</h2>
        <div class="row">
            <div class="col-md-4">
                <label>أدخل مبلغ للاختبار:</label>
                <input type="number" class="form-control" id="test-amount" value="1234.56" step="0.01">
            </div>
            <div class="col-md-4">
                <label>النتيجة المنسقة:</label>
                <div class="amount-display bg-light" id="formatted-result">0.00 ج.م</div>
            </div>
            <div class="col-md-4">
                <label>العمليات:</label><br>
                <button class="btn btn-info btn-sm" onclick="updateFormattedAmount()">تحديث التنسيق</button>
                <button class="btn btn-warning btn-sm" onclick="testRandomAmounts()">اختبار مبالغ عشوائية</button>
            </div>
        </div>
    </div>
    
    <!-- نتائج الاختبارات -->
    <div class="test-section">
        <h2>4. نتائج الاختبارات</h2>
        <div id="test-results"></div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// نسخ وظائف التنسيق من ملف main.js
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

// نسخ وظيفة formatNumber من صفحة الماليات
function formatNumber(num) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(num);
}

// اختبار formatNumber
function testFormatNumber() {
    const testDiv = document.getElementById('js-format-test');
    const testValues = [0, 1234.56, 999.99, 10000, 0.5, -500];
    let html = '<h5>نتائج اختبار formatNumber:</h5>';
    
    testValues.forEach(value => {
        try {
            const formatted = formatNumber(value);
            html += `<div class="test-result test-success">
                        <strong>القيمة:</strong> ${value} → <strong>النتيجة:</strong> ${formatted}
                     </div>`;
        } catch (error) {
            html += `<div class="test-result test-error">
                        <strong>القيمة:</strong> ${value} → <strong>خطأ:</strong> ${error.message}
                     </div>`;
        }
    });
    
    testDiv.innerHTML = html;
}

// اختبار formatCurrency
function testFormatCurrency() {
    const testDiv = document.getElementById('js-currency-test');
    const testValues = [0, 1234.56, 999.99, 10000, 0.5, -500];
    let html = '<h5>نتائج اختبار formatCurrency:</h5>';
    
    testValues.forEach(value => {
        try {
            const formatted = formatCurrency(value);
            html += `<div class="test-result test-success">
                        <strong>القيمة:</strong> ${value} → <strong>النتيجة:</strong> ${formatted}
                     </div>`;
        } catch (error) {
            html += `<div class="test-result test-error">
                        <strong>القيمة:</strong> ${value} → <strong>خطأ:</strong> ${error.message}
                     </div>`;
        }
    });
    
    testDiv.innerHTML = html;
}

// تحديث المبلغ المنسق
function updateFormattedAmount() {
    const amount = parseFloat(document.getElementById('test-amount').value) || 0;
    const resultDiv = document.getElementById('formatted-result');
    
    try {
        const formatted = formatNumber(amount);
        resultDiv.innerHTML = formatted;
        resultDiv.className = 'amount-display bg-success text-white';
    } catch (error) {
        resultDiv.innerHTML = 'خطأ: ' + error.message;
        resultDiv.className = 'amount-display bg-danger text-white';
    }
}

// اختبار مبالغ عشوائية
function testRandomAmounts() {
    const resultDiv = document.getElementById('test-results');
    let html = '<h5>اختبار مبالغ عشوائية:</h5>';
    
    for (let i = 0; i < 5; i++) {
        const randomAmount = Math.random() * 10000;
        try {
            const formatted = formatNumber(randomAmount);
            html += `<div class="test-result test-success">
                        <strong>مبلغ عشوائي:</strong> ${randomAmount.toFixed(2)} → <strong>منسق:</strong> ${formatted}
                     </div>`;
        } catch (error) {
            html += `<div class="test-result test-error">
                        <strong>مبلغ عشوائي:</strong> ${randomAmount.toFixed(2)} → <strong>خطأ:</strong> ${error.message}
                     </div>`;
        }
    }
    
    resultDiv.innerHTML = html;
}

// تحديث تلقائي عند تغيير المبلغ
document.getElementById('test-amount').addEventListener('input', updateFormattedAmount);

// تشغيل الاختبارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateFormattedAmount();
    
    // إضافة معلومات النظام
    const systemInfo = `
        <div class="test-result test-warning">
            <strong>معلومات النظام:</strong><br>
            - المتصفح: ${navigator.userAgent}<br>
            - اللغة: ${navigator.language}<br>
            - المنطقة الزمنية: ${Intl.DateTimeFormat().resolvedOptions().timeZone}
        </div>
    `;
    document.getElementById('test-results').innerHTML = systemInfo;
});
</script>

</body>
</html>
