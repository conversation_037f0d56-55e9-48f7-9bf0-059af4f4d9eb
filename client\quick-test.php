<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    $_SESSION['client_name'] = 'مركز الألعاب';
    $_SESSION['owner_name'] = 'صاحب المحل';
}

$page_title = "اختبار سريع للمظهر";
$active_page = "test";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle me-2"></i>اختبار المظهر المخصص</h4>
                <p>إذا كنت ترى هذه الرسالة بألوان حمراء وبرتقالية، فإن النظام يعمل بشكل صحيح!</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-palette me-2"></i>اختبار الألوان المخصصة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100 mb-2">زر أساسي (أحمر)</button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100 mb-2">زر نجاح (برتقالي)</button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary w-100 mb-2">زر ثانوي</button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success-gradient w-100 mb-2">زر متدرج</button>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary">150</h4>
                                    <p class="text-muted">العملاء</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-gamepad fa-2x text-success mb-2"></i>
                                    <h4 class="text-success">25</h4>
                                    <p class="text-muted">الجلسات النشطة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning">1,250 ج.م</h4>
                                    <p class="text-muted">الإيرادات اليوم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="progress mb-4">
                        <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                    </div>
                    
                    <div class="text-center">
                        <a href="settings.php" class="btn btn-primary me-2">
                            <i class="fas fa-cog me-1"></i>انتقل للإعدادات
                        </a>
                        <a href="dashboard.php" class="btn btn-success">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 اختبار المظهر المخصص');
    
    // فحص المتغيرات المخصصة
    const rootStyles = getComputedStyle(document.documentElement);
    const primaryColor = rootStyles.getPropertyValue('--custom-primary');
    const accentColor = rootStyles.getPropertyValue('--custom-accent');
    
    if (primaryColor && primaryColor.trim()) {
        console.log('✅ اللون الأساسي:', primaryColor.trim());
    } else {
        console.log('❌ اللون الأساسي غير متاح');
    }
    
    if (accentColor && accentColor.trim()) {
        console.log('✅ اللون المميز:', accentColor.trim());
    } else {
        console.log('❌ اللون المميز غير متاح');
    }
    
    // فحص تحميل ملفات CSS
    const customCSS = document.querySelector('link[href*="custom-theme.css"]');
    const dynamicCSS = document.querySelector('link[href*="theme-css.php"]');
    
    if (customCSS) {
        console.log('✅ ملف CSS الثابت محمل:', customCSS.href);
    }
    
    if (dynamicCSS) {
        console.log('✅ ملف CSS الديناميكي محمل:', dynamicCSS.href);
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
