/*
 * تصميم الفاتورة الاحترافي - شكل ريسيت
 * Professional Receipt Design
 */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --border-color: #bdc3c7;
    --light-bg: #ecf0f1;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 15px 35px rgba(0,0,0,0.2);
}

/* الخطوط والتخطيط العام */
.receipt-container {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تأثيرات الحركة */
.receipt-animate {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للطباعة الحرارية */
@media print {
    @page {
        size: 80mm auto;
        margin: 0;
    }
    
    body {
        font-size: 11px !important;
        line-height: 1.3 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
    }
    
    .receipt-container {
        width: 80mm !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }
    
    .receipt-header {
        padding: 15px 10px !important;
    }
    
    .receipt-title {
        font-size: 18px !important;
    }
    
    .company-section {
        padding: 15px 10px !important;
    }
    
    .company-logo {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }
    
    .receipt-details {
        padding: 10px !important;
    }
    
    .detail-row {
        padding: 6px 0 !important;
        font-size: 11px !important;
    }
    
    .total-section {
        padding: 15px 10px !important;
    }
    
    .total-amount {
        font-size: 20px !important;
    }
    
    .receipt-footer {
        padding: 15px 10px !important;
    }
    
    .qr-code {
        width: 60px !important;
        height: 60px !important;
    }
    
    .contact-info {
        font-size: 9px !important;
    }
    
    .receipt-actions {
        display: none !important;
    }
    
    .no-print {
        display: none !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .receipt-container {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .receipt-title {
        font-size: 20px;
    }
    
    .company-name {
        font-size: 16px;
    }
    
    .detail-row {
        font-size: 13px;
    }
    
    .total-amount {
        font-size: 24px;
    }
    
    .action-btn {
        padding: 10px 16px;
        font-size: 13px;
        margin: 3px;
    }
}

/* تحسينات إضافية للطباعة */
.print-optimized {
    font-size: 12px;
    line-height: 1.4;
}

.print-optimized .receipt-header {
    background: #000 !important;
    color: #fff !important;
}

.print-optimized .total-section {
    background: #000 !important;
    color: #fff !important;
}

.print-optimized .company-logo {
    background: #000 !important;
    color: #fff !important;
}

/* تأثيرات بصرية إضافية */
.receipt-glow {
    box-shadow: 0 0 30px rgba(52, 152, 219, 0.3);
}

.receipt-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* تحسينات للنصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    font-feature-settings: "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
}

/* تحسينات للأرقام */
.number-display {
    font-family: 'Cairo', 'Roboto Mono', monospace;
    font-variant-numeric: tabular-nums;
    direction: ltr;
    text-align: left;
}

/* تحسينات للألوان في الطباعة */
.print-colors {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
}

/* تحسينات للخطوط في الطباعة */
@media print {
    * {
        font-family: 'Arial', 'Helvetica', sans-serif !important;
    }
    
    .arabic-text {
        font-family: 'Arial', 'Tahoma', sans-serif !important;
    }
}

/* تحسينات للتباعد */
.receipt-spacing {
    letter-spacing: 0.3px;
    word-spacing: 1px;
}

/* تحسينات للحدود */
.receipt-borders {
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

/* تحسينات للظلال */
.receipt-shadow-light {
    box-shadow: var(--shadow-light);
}

.receipt-shadow-medium {
    box-shadow: var(--shadow-medium);
}

.receipt-shadow-heavy {
    box-shadow: var(--shadow-heavy);
}

/* تحسينات للانتقالات */
.receipt-transition {
    transition: all 0.3s ease;
}

.receipt-transition:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* تحسينات للتدرجات */
.receipt-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.receipt-gradient-accent {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--success-color) 100%);
}

/* تحسينات للأيقونات */
.receipt-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--accent-color);
    color: white;
    font-size: 12px;
    margin-left: 8px;
}

/* تحسينات للحالات */
.receipt-status-paid {
    color: var(--success-color);
    font-weight: bold;
}

.receipt-status-pending {
    color: #f39c12;
    font-weight: bold;
}

.receipt-status-cancelled {
    color: #e74c3c;
    font-weight: bold;
}

/* تحسينات للتفاعل */
.receipt-interactive:hover {
    cursor: pointer;
    background: var(--light-bg);
}

.receipt-interactive:active {
    transform: scale(0.98);
}

/* تحسينات للتحميل */
.receipt-loading {
    opacity: 0.7;
    pointer-events: none;
}

.receipt-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
