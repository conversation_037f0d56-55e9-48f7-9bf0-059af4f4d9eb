/* ملف CSS خاص بصفحات المصادقة - PlayGood */
/* تحسينات احترافية متقدمة */

/* تحسينات إضافية للنماذج */
.auth-form-control:invalid {
    border-color: rgba(220, 53, 69, 0.4);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.auth-form-control:valid {
    border-color: rgba(25, 135, 84, 0.4);
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.1);
}

/* تأثيرات الجسيمات المتحركة */
.auth-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 60px 60px;
    animation: particleFloat 15s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes particleFloat {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(-5px) translateX(-5px); }
    75% { transform: translateY(-15px) translateX(3px); }
    100% { transform: translateY(0) translateX(0); }
}

/* تأثيرات الأيقونات */
.auth-form-icon {
    transition: all 0.3s ease;
}

.auth-form-group:hover .auth-form-icon {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* تحسين الروابط */
.auth-link {
    position: relative;
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.auth-link:hover {
    background: rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
}

/* تأثيرات الخلفية المتحركة */
.auth-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 576px) {
    .auth-card {
        margin: 1rem;
        border-radius: 15px;
    }
    
    .auth-card-header {
        padding: 1.25rem;
    }
    
    .auth-card-body {
        padding: 1.25rem;
    }
    
    .auth-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .auth-title {
        font-size: 1.1rem;
    }
    
    .auth-form-control {
        padding: 0.75rem 2.25rem 0.75rem 0.75rem;
        font-size: 0.9rem;
        direction: rtl;
        text-align: right;
    }

    .auth-form-icon {
        right: 0.75rem;
        font-size: 1rem;
    }

    .auth-form-label {
        right: 2.25rem;
        direction: rtl;
        text-align: right;
    }

    .auth-form-control:focus ~ .auth-form-label,
    .auth-form-control:not(:placeholder-shown) ~ .auth-form-label {
        right: 2rem;
    }
    
    .auth-btn {
        padding: 0.875rem;
        font-size: 1rem;
    }
}

/* تحسين الطباعة */
@media print {
    .auth-page {
        background: white !important;
    }
    
    .auth-page::before,
    .auth-page::after {
        display: none !important;
    }
    
    .auth-card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .auth-card-header {
        background: #f8f9fa !important;
        color: #212529 !important;
    }
}

/* تأثيرات التركيز المحسنة */
.auth-form-control:focus {
    box-shadow: 
        0 0 0 0.25rem rgba(13, 110, 253, 0.1),
        0 4px 12px rgba(13, 110, 253, 0.15);
}

/* تحسين الألوان للوضع المظلم (إذا تم تفعيله لاحقاً) */
@media (prefers-color-scheme: dark) {
    .auth-card {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }
    
    .auth-form-control {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #f8f9fa;
    }
    
    .auth-form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .auth-form-label {
        color: rgba(255, 255, 255, 0.8);
    }
    
    .auth-form-icon {
        color: rgba(255, 255, 255, 0.6);
    }
}

/* تأثيرات الحركة المتقدمة */
.auth-card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الأزرار */
.auth-btn:active {
    transform: translateY(1px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

/* تأثيرات إضافية للتفاعل مع دعم RTL */
.auth-form-group.focused .auth-form-control {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
    direction: rtl;
    text-align: right;
}

.auth-form-group.focused .auth-form-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.auth-form-group.focused .auth-form-label {
    color: var(--primary-color);
    direction: rtl;
    text-align: right;
}

/* تحسينات إضافية لدعم RTL */
.auth-page {
    direction: rtl;
}

.auth-card-header {
    direction: rtl;
    text-align: center;
}

.auth-card-body {
    direction: rtl;
}

.auth-title {
    direction: rtl;
    text-align: center;
}

.auth-subtitle {
    direction: rtl;
    text-align: center;
}

.auth-alert {
    direction: rtl;
    text-align: right;
}

.auth-btn {
    direction: rtl;
}

.auth-link {
    direction: rtl;
}

.auth-link-alt {
    direction: rtl;
    text-align: center;
}

/* تحسين النصوص العربية */
.auth-form-control,
.auth-form-label,
.auth-alert,
.auth-title,
.auth-subtitle {
    font-family: 'Segoe UI', 'Cairo', 'Tajawal', 'Amiri', sans-serif;
}

/* تحسين المسافات للنصوص العربية */
.auth-form-control {
    line-height: 1.6;
    letter-spacing: 0.5px;
}

.auth-title {
    line-height: 1.4;
}

.auth-subtitle {
    line-height: 1.5;
}
