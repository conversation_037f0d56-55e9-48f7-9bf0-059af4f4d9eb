<?php
/**
 * اختبار سريع لطرق الحساب الثلاثة بعد الإصلاح
 */

require_once 'config/database.php';
require_once 'includes/billing_helper.php';

echo "<h1>اختبار سريع لطرق الحساب الثلاثة</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات - استخدام الاتصال من ملف الإعدادات
    // $pdo متوفر بالفعل من config/database.php
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // اختبار الدوال المساعدة
    echo "<h3>🧪 اختبار الدوال المساعدة</h3>";
    
    $test_client_id = 1;
    $test_duration = 45; // 45 دقيقة
    $hourly_rate = 20; // 20 ج.م/ساعة
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 اختبار مع مدة 45 دقيقة وسعر 20 ج.م/ساعة</h4>";
    
    // اختبار كل طريقة
    $methods = [
        'actual_time' => ['name' => 'الوقت الفعلي', 'color' => '#17a2b8', 'icon' => '⏱️'],
        'hourly_rounding' => ['name' => 'تقريب الساعة', 'color' => '#ffc107', 'icon' => '🕐'],
        'first_minute_full_hour' => ['name' => 'ساعة من أول دقيقة', 'color' => '#dc3545', 'icon' => '⚡']
    ];
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    foreach ($methods as $method => $info) {
        try {
            // محاكاة تغيير الإعداد
            updateBillingMethod($pdo, $test_client_id, $method);
            
            $cost = calculateTimeCost($pdo, $test_client_id, $test_duration, $hourly_rate);
            $details = calculateTimeCostWithDetails($pdo, $test_client_id, $test_duration, $hourly_rate);
            
            echo "<div style='background: white; padding: 20px; border-radius: 10px; border-left: 5px solid {$info['color']}; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
            echo "<h4 style='color: {$info['color']}; margin-top: 0;'>{$info['icon']} {$info['name']}</h4>";
            echo "<p><strong>التكلفة:</strong> <span style='font-size: 24px; font-weight: bold; color: {$info['color']};'>" . number_format($cost, 2) . " ج.م</span></p>";
            echo "<p><strong>التفاصيل:</strong> " . $details['calculation_note'] . "</p>";
            echo "<p style='font-size: 12px; color: #666;'>الطريقة: $method</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; border-left: 5px solid #dc3545;'>";
            echo "<h4 style='color: #721c24; margin-top: 0;'>❌ خطأ في {$info['name']}</h4>";
            echo "<p>الخطأ: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
    // مقارنة سريعة
    echo "<h3>📊 مقارنة سريعة</h3>";
    
    $comparison = compareBillingMethods($test_duration, $hourly_rate);
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
    echo "<thead>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>الطريقة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>التكلفة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>الساعات المحسوبة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>الفرق عن الوقت الفعلي</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    // الوقت الفعلي
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>⏱️ الوقت الفعلي</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd; color: #17a2b8; font-weight: bold;'>" . number_format($comparison['actual_time']['cost'], 2) . " ج.م</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . number_format($comparison['actual_time']['hours'], 2) . " ساعة</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd; color: #28a745;'>المرجع</td>";
    echo "</tr>";
    
    // تقريب الساعة
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>🕐 تقريب الساعة</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd; color: #ffc107; font-weight: bold;'>" . number_format($comparison['hourly_rounding']['cost'], 2) . " ج.م</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $comparison['hourly_rounding']['hours'] . " ساعة</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>";
    if ($comparison['hourly_rounding']['difference_from_actual'] > 0) {
        echo "<span style='color: #dc3545;'>+" . number_format($comparison['hourly_rounding']['difference_from_actual'], 2) . " ج.م</span>";
    } else {
        echo "<span style='color: #28a745;'>لا يوجد فرق</span>";
    }
    echo "</td>";
    echo "</tr>";
    
    // ساعة من أول دقيقة
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>⚡ ساعة من أول دقيقة</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd; color: #dc3545; font-weight: bold;'>" . number_format($comparison['first_minute_full_hour']['cost'], 2) . " ج.م</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $comparison['first_minute_full_hour']['hours'] . " ساعة</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>";
    if ($comparison['first_minute_full_hour']['difference_from_actual'] > 0) {
        echo "<span style='color: #dc3545;'>+" . number_format($comparison['first_minute_full_hour']['difference_from_actual'], 2) . " ج.م</span>";
    } else {
        echo "<span style='color: #28a745;'>لا يوجد فرق</span>";
    }
    echo "</td>";
    echo "</tr>";
    
    echo "</tbody>";
    echo "</table>";
    
    // روابط للاختبار
    echo "<h3>🔗 روابط للاختبار</h3>";
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin: 20px 0;'>";
    
    echo "<a href='client/invoice_settings.php' target='_blank' style='display: block; padding: 15px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; text-align: center;'>";
    echo "<i class='fas fa-cog'></i><br>إعدادات الفاتورة";
    echo "</a>";
    
    echo "<a href='client/sessions.php' target='_blank' style='display: block; padding: 15px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; text-align: center;'>";
    echo "<i class='fas fa-play'></i><br>بدء جلسة جديدة";
    echo "</a>";
    
    echo "<a href='test_three_billing_methods.php' target='_blank' style='display: block; padding: 15px; background: #17a2b8; color: white; text-decoration: none; border-radius: 8px; text-align: center;'>";
    echo "<i class='fas fa-chart-bar'></i><br>اختبار شامل";
    echo "</a>";
    
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ النظام جاهز للاستخدام!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>جميع طرق الحساب الثلاثة تعمل بشكل صحيح.</strong></p>";
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #bee5eb; margin-top: 20px;'>";
    echo "<h4>🎯 ملخص الطرق الثلاثة:</h4>";
    echo "<ul>";
    echo "<li><strong>⏱️ الوقت الفعلي:</strong> حساب دقيق ومنصف للعملاء</li>";
    echo "<li><strong>🕐 تقريب الساعة:</strong> النظام التقليدي المتوازن</li>";
    echo "<li><strong>⚡ ساعة من أول دقيقة:</strong> ضمان حد أدنى من الربح</li>";
    echo "</ul>";
    echo "<p><strong>يمكن للعميل الآن اختيار الطريقة المناسبة لعمله من إعدادات الفاتورة.</strong></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    background: white;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}

a {
    transition: transform 0.2s;
}

a:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}
</style>
