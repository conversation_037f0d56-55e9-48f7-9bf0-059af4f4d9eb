# 🛡️ نظام الحماية المتقدم - PlayGood Security System

## نظرة عامة

تم تطوير نظام حماية شامل ومتقدم لحماية موقع PlayGood من جميع أنواع الهجمات الإلكترونية، خاصة **سرقة الجلسات (Session Hijacking)**. النظام يوفر حماية متعددة الطبقات ومراقبة مستمرة للأنشطة المشبوهة.

## 🚨 المشكلة التي تم حلها

**المشكلة الأصلية:** كان بإمكان أي شخص أخذ session ID من أدوات المطور (Developer Tools) واستخدامه في متصفح آخر للدخول إلى حساب المستخدم.

**الحل:** نظام حماية متقدم يربط الجلسة بعدة عوامل أمنية ويمنع استخدامها من أجهزة أخرى.

## 🔧 المكونات الأساسية

### 1. Session Security Manager (`includes/session_security.php`)
- إدارة الجلسات الآمنة
- ربط الجلسة بـ IP Address و User Agent
- توليد رموز أمان إضافية
- مراقبة ومنع محاولات الاختراق

### 2. Secure Session Middleware (`includes/secure_session_middleware.php`)
- طبقة وسطية للتحقق من صحة الجلسات
- إعدادات جلسة آمنة متقدمة
- إدارة انتهاء صلاحية الجلسات

### 3. Auth Guard (`includes/auth_guard.php`)
- حماية الصفحات من الوصول غير المصرح
- دوال مساعدة للحماية
- JavaScript للحماية من الأنشطة المشبوهة

### 4. Security Config (`config/security_config.php`)
- إعدادات الأمان المتقدمة
- تشفير كلمات المرور بـ Argon2ID
- Headers الأمان
- تنظيف المدخلات

## 🔒 ميزات الحماية

### حماية الجلسات
- ✅ **Session Binding**: ربط الجلسة بـ IP Address
- ✅ **User Agent Validation**: التحقق من User Agent
- ✅ **Security Tokens**: رموز أمان إضافية
- ✅ **Session Fingerprinting**: بصمة فريدة لكل جلسة
- ✅ **Automatic Expiration**: انتهاء صلاحية تلقائي

### حماية من الهجمات
- ✅ **Session Hijacking Protection**: منع سرقة الجلسات
- ✅ **CSRF Protection**: حماية من الهجمات العابرة
- ✅ **XSS Protection**: منع حقن الأكواد الضارة
- ✅ **Brute Force Protection**: حماية من الهجمات المتكررة
- ✅ **IP Blocking**: حظر IP المشبوهة

### مراقبة الأمان
- ✅ **Real-time Monitoring**: مراقبة مستمرة
- ✅ **Attack Logging**: تسجيل محاولات الاختراق
- ✅ **Security Dashboard**: لوحة مراقبة للمدير
- ✅ **Automated Alerts**: تنبيهات تلقائية

## 📊 جداول قاعدة البيانات

### secure_sessions
```sql
CREATE TABLE secure_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) NOT NULL,
    user_id INT NOT NULL,
    user_type ENUM('admin', 'client', 'employee') NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    security_token VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE
);
```

### session_security_logs
```sql
CREATE TABLE session_security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    attack_type ENUM('hijack_attempt', 'invalid_token', 'ip_mismatch', 'agent_mismatch', 'expired_session') NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 كيفية الاستخدام

### 1. حماية صفحة المدير
```php
<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// حماية الصفحة
protectAdminPage($pdo);
?>
```

### 2. حماية صفحة العميل
```php
<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// حماية الصفحة
protectClientPage($pdo);
?>
```

### 3. حماية API Endpoint
```php
<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// حماية API
protectApiEndpoint($pdo, 'client');
?>
```

### 4. تسجيل دخول آمن
```php
<?php
// في صفحة تسجيل الدخول
if ($user && password_verify($password, $user['password_hash'])) {
    // إنشاء جلسة آمنة
    $security_token = createSecureUserSession($pdo, $user['id'], 'client');
    
    if ($security_token) {
        $_SESSION['client_id'] = $user['id'];
        // باقي بيانات الجلسة...
        header('Location: dashboard.php');
        exit;
    }
}
?>
```

### 5. تسجيل خروج آمن
```php
<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// تسجيل خروج آمن
secureLogout($pdo, 'login.php');
?>
```

## 🧪 الاختبار

### اختبار شامل للنظام
```bash
# زيارة صفحة الاختبار الشامل
http://yoursite.com/test_security_system.php
```

### اختبار تفاعلي
```bash
# زيارة صفحة الاختبار التفاعلي
http://yoursite.com/security_test.php
```

### مراقبة الأمان (للمدير فقط)
```bash
# لوحة مراقبة الأمان
http://yoursite.com/admin/security_monitor.php
```

## ⚙️ الإعدادات

### إعدادات الجلسة
```php
// في config/security_config.php
const SESSION_LIFETIME = 3600; // ساعة واحدة
const SESSION_REGENERATE_INTERVAL = 300; // 5 دقائق
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_DURATION = 900; // 15 دقيقة
```

### إعدادات كلمات المرور
```php
const MIN_PASSWORD_LENGTH = 8;
const PASSWORD_REQUIRE_UPPERCASE = true;
const PASSWORD_REQUIRE_LOWERCASE = true;
const PASSWORD_REQUIRE_NUMBERS = true;
```

## 🔍 مراقبة الأمان

### أنواع الهجمات المرصودة
- `hijack_attempt`: محاولة سرقة جلسة
- `invalid_token`: رمز أمان غير صحيح
- `ip_mismatch`: عدم تطابق IP Address
- `agent_mismatch`: تغيير User Agent
- `expired_session`: جلسة منتهية الصلاحية

### إحصائيات الأمان
- عدد الجلسات النشطة
- محاولات الاختراق اليومية
- أنواع الهجمات الأكثر شيوعاً
- الجلسات المحمية

## 🚨 التنبيهات والإنذارات

### تنبيهات تلقائية
- محاولة استخدام جلسة من IP مختلف
- تغيير User Agent أثناء الجلسة
- محاولات تسجيل دخول متكررة فاشلة
- استخدام رموز أمان غير صحيحة

### إجراءات الحماية التلقائية
- إنهاء الجلسة المشبوهة فوراً
- حظر IP مؤقت للأنشطة المشبوهة
- تسجيل جميع محاولات الاختراق
- إرسال تنبيهات للمدير

## 📝 السجلات

### سجلات الأمان
- جميع محاولات الاختراق مسجلة
- تفاصيل كاملة عن كل محاولة
- IP Address و User Agent
- الوقت والتاريخ الدقيق

### مسار السجلات
```
logs/security_YYYY-MM-DD.log
```

## 🔧 الصيانة

### تنظيف دوري
- الجلسات المنتهية الصلاحية تُحذف تلقائياً
- سجلات الأمان القديمة تُحذف بعد شهر
- تحسين قاعدة البيانات دورياً

### النسخ الاحتياطي
- نسخ احتياطية دورية لجداول الأمان
- حفظ سجلات الأمان المهمة
- استعادة سريعة في حالة الطوارئ

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "تم اكتشاف نشاط مشبوه"
- **السبب**: تغيير IP أو User Agent
- **الحل**: تسجيل دخول جديد من نفس الجهاز

#### 2. "انتهت صلاحية الجلسة"
- **السبب**: عدم النشاط لأكثر من ساعة
- **الحل**: تسجيل دخول جديد

#### 3. "خطأ في إنشاء جلسة آمنة"
- **السبب**: مشكلة في قاعدة البيانات
- **الحل**: فحص اتصال قاعدة البيانات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل أمنية:
- راجع ملف `SECURITY_GUIDE.md`
- استخدم صفحة اختبار النظام
- تحقق من سجلات الأمان

---

**تم تطوير هذا النظام خصيصاً لحماية موقع PlayGood من جميع أنواع الهجمات الإلكترونية وضمان أمان بيانات المستخدمين.**
