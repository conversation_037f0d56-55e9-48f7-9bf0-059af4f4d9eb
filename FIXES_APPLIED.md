# إصلاحات مشكلة s.status في جدول sessions - PlayGood

## المشكلة الأصلية
كانت هناك أخطاء SQL في الملفات التالية:
- `client/customers.php` (السطر 109)
- `client/devices.php` (السطر 271) 
- `client/sessions.php` (السطر 318)

الخطأ: `SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.status' in 'on clause'`

## سبب المشكلة
- عمود `status` مفقود من جدول `sessions` في قاعدة البيانات
- الاستعلامات تحاول الوصول لعمود `s.status` حيث `s` هو alias لجدول `sessions`

## الإصلاحات المطبقة

### 1. إصلاح الاستعلامات في الملفات
تم تعديل الاستعلامات لتتعامل مع حالة عدم وجود عمود status:

#### في `client/customers.php`:
```sql
-- قبل الإصلاح:
AND s.status = 'completed'

-- بعد الإصلاح:
AND (s.status = 'completed' OR s.status IS NULL)
```

#### في `client/devices.php`:
```sql
-- قبل الإصلاح:
WHERE s.device_id = d.device_id AND s.status = "active"

-- بعد الإصلاح:
WHERE s.device_id = d.device_id AND (s.status = "active" OR s.status IS NULL)
```

#### في `client/sessions.php`:
```sql
-- قبل الإصلاح:
WHERE d.client_id = ? AND s.status = 'active'

-- بعد الإصلاح:
WHERE d.client_id = ? AND (s.status = 'active' OR s.status IS NULL)
```

### 2. ملفات الإصلاح المنشأة

#### `fix_sessions_status_column.php`
- فحص هيكل جدول sessions
- إضافة عمود status إذا كان مفقوداً
- إضافة الأعمدة المفقودة الأخرى
- اختبار الاستعلامات المصححة
- إضافة فهارس لتحسين الأداء

#### `fix_sessions_status.sql`
- ملف SQL يمكن تشغيله مباشرة في phpMyAdmin
- إضافة عمود status والأعمدة المفقودة
- إنشاء فهارس للأداء
- اختبار الاستعلامات

#### `check_sessions_table.php`
- فحص تشخيصي لهيكل جدول sessions
- اختبار الاستعلامات البسيطة
- عرض معلومات مفصلة عن الجدول

### 3. الأعمدة المضافة لجدول sessions
```sql
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS status ENUM('active','completed','cancelled') NOT NULL DEFAULT 'active',
ADD COLUMN IF NOT EXISTS client_id INT NULL,
ADD COLUMN IF NOT EXISTS customer_id INT NULL,
ADD COLUMN IF NOT EXISTS total_cost DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS game_type ENUM('single','multiplayer') DEFAULT 'single',
ADD COLUMN IF NOT EXISTS created_by INT NULL,
ADD COLUMN IF NOT EXISTS updated_by INT NULL;
```

### 4. الفهارس المضافة
```sql
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_client_id ON sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_sessions_customer_id ON sessions(customer_id);
CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time);
```

## كيفية تطبيق الإصلاحات

### الطريقة الأولى: تشغيل ملف PHP
1. افتح المتصفح واذهب إلى: `http://localhost/playgood/fix_sessions_status_column.php`
2. سيتم تطبيق جميع الإصلاحات تلقائياً

### الطريقة الثانية: تشغيل ملف SQL
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `station`
3. اذهب إلى تبويب SQL
4. انسخ محتوى ملف `fix_sessions_status.sql` والصقه
5. اضغط تنفيذ

### الطريقة الثالثة: فحص تشخيصي
1. افتح المتصفح واذهب إلى: `http://localhost/playgood/check_sessions_table.php`
2. سيعرض حالة جدول sessions الحالية

## النتيجة المتوقعة
بعد تطبيق الإصلاحات، يجب أن تعمل الصفحات التالية بدون أخطاء:
- `client/customers.php` - صفحة العملاء
- `client/devices.php` - صفحة الأجهزة  
- `client/sessions.php` - صفحة الجلسات

## ملاحظات مهمة
- الإصلاحات متوافقة مع البيانات الموجودة
- لن تؤثر على الجلسات النشطة
- تم إضافة فهارس لتحسين الأداء
- جميع الاستعلامات محسنة للتعامل مع القيم الفارغة

## اختبار الإصلاحات
للتأكد من نجاح الإصلاحات:
1. افتح `http://localhost/playgood/client/customers.php`
2. افتح `http://localhost/playgood/client/devices.php`
3. افتح `http://localhost/playgood/client/sessions.php`

يجب ألا تظهر أي أخطاء SQL متعلقة بعمود `s.status`.
