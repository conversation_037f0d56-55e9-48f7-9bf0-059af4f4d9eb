<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة الطلب غير مدعومة']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'بيانات غير صحيحة']);
    exit;
}

// التحقق من وجود معرف الجلسة
if (!isset($input['session_id']) || !is_numeric($input['session_id'])) {
    echo json_encode(['success' => false, 'error' => 'معرف الجلسة مطلوب']);
    exit;
}

$session_id = $input['session_id'];
$customer_id = isset($input['customer_id']) && !empty($input['customer_id']) ? $input['customer_id'] : null;
$notes = isset($input['notes']) ? trim($input['notes']) : null;
$game_type = isset($input['game_type']) && in_array($input['game_type'], ['single', 'multiplayer']) ? $input['game_type'] : 'single';

try {
    $pdo->beginTransaction();
    
    // التحقق من أن الجلسة تنتمي للعميل وأنها نشطة
    $stmt = $pdo->prepare("
        SELECT s.session_id 
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE s.session_id = ? AND d.client_id = ? AND s.status = 'active'
    ");
    $stmt->execute([$session_id, $client_id]);
    
    if (!$stmt->fetch()) {
        throw new Exception('الجلسة غير موجودة أو غير نشطة');
    }
    
    // التحقق من صحة معرف العميل إذا تم تمريره
    if ($customer_id) {
        // التحقق من وجود عمود customer_id في جدول customers
        $customer_id_column = null;
        try {
            $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'customer_id'");
            if ($columns_check->rowCount() > 0) {
                $customer_id_column = 'customer_id';
            } else {
                $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'id'");
                if ($columns_check->rowCount() > 0) {
                    $customer_id_column = 'id';
                }
            }
        } catch (PDOException $e) {
            // في حالة عدم وجود جدول customers
            $customer_id_column = null;
        }
        
        if ($customer_id_column) {
            $customer_stmt = $pdo->prepare("SELECT $customer_id_column FROM customers WHERE $customer_id_column = ? AND client_id = ?");
            $customer_stmt->execute([$customer_id, $client_id]);
            
            if (!$customer_stmt->fetch()) {
                throw new Exception('العميل المحدد غير موجود');
            }
        }
    }
    
    // تحديث بيانات الجلسة
    $update_stmt = $pdo->prepare("
        UPDATE sessions
        SET customer_id = ?,
            notes = ?,
            game_type = ?,
            updated_by = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE session_id = ?
    ");

    $update_stmt->execute([
        $customer_id,
        $notes,
        $game_type,
        $client_id,
        $session_id
    ]);
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث الجلسة بنجاح'
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Update session error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage()
    ]);
}
?>
