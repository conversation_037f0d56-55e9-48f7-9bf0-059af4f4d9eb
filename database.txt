-- جدول المدراء (Super Admin)
CREATE TABLE admins (
    admin_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- جدول العملاء (أصحاب المحلات)
CREATE TABLE clients (
    client_id INT PRIMARY KEY AUTO_INCREMENT,
    business_name VARCHAR(200) NOT NULL,
    owner_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    subscription_start DATE,
    subscription_end DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الغرف/المناطق
CREATE TABLE rooms (
    room_id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    room_name VARCHAR(100) NOT NULL,
    room_type ENUM('VIP', 'regular', 'private') DEFAULT 'regular',
    capacity INT DEFAULT 1,
    special_rate DECIMAL(8,2) DEFAULT 0.00,
    description TEXT,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- جدول الأجهزة
CREATE TABLE devices (
    device_id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    device_type ENUM('PS4', 'PS5', 'Xbox', 'PC') NOT NULL,
    hourly_rate DECIMAL(8,2) NOT NULL,
    status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available',
    room_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(room_id)
);

-- إدراج بيانات تجريبية
INSERT INTO admins (username, email, password_hash, full_name, role) 
VALUES ('admin', '<EMAIL>', '$2y$10$hash_here', 'مدير النظام', 'super_admin');