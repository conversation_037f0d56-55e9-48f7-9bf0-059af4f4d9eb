# إدارة أجهزة العملاء من لوحة تحكم الأدمن

## نظرة عامة
تم إضافة إمكانية إدارة الأجهزة لكل عميل من لوحة تحكم الأدمن، مما يتيح للمدير العام إدارة شاملة لجميع أجهزة العملاء من مكان واحد.

## الملفات المضافة

### 1. `admin/client_devices.php`
الملف الرئيسي لإدارة أجهزة عميل محدد، يتضمن:
- عرض معلومات العميل
- إحصائيات سريعة عن الأجهزة (إجمالي، متاح، مشغول، صيانة)
- جدول شامل بجميع أجهزة العميل
- إمكانية إضافة أجهزة جديدة
- تعديل بيانات الأجهزة الموجودة
- حذف الأجهزة (مع التحقق من عدم وجود جلسات نشطة)

### 2. `admin/ensure_device_columns.php`
ملف للتأكد من وجود الأعمدة المطلوبة في جدول الأجهزة:
- `single_rate`: سعر الساعة للعب الفردي
- `multi_rate`: سعر الساعة للعب الجماعي
- `updated_at`: تاريخ آخر تحديث

### 3. `admin/test_client_devices.php`
ملف اختبار للتأكد من عمل النظام بشكل صحيح.

## التحديثات على الملفات الموجودة

### 1. `admin/clients.php`
- إضافة زر "إدارة الأجهزة" في جدول العملاء
- الزر يوجه إلى صفحة إدارة أجهزة العميل المحدد

### 2. `admin/includes/sidebar.php`
- إضافة رابط "أجهزة العملاء" في القائمة الجانبية

## الميزات الرئيسية

### 1. عرض شامل للأجهزة
- معلومات مفصلة عن كل جهاز (الاسم، النوع، الغرفة، الأسعار، الحالة)
- عرض عدد الجلسات النشطة لكل جهاز
- أيقونات مميزة لكل نوع جهاز (PlayStation, Xbox, PC)

### 2. إدارة متقدمة
- إضافة أجهزة جديدة مع جميع التفاصيل المطلوبة
- تعديل بيانات الأجهزة الموجودة
- حذف الأجهزة مع حماية من حذف الأجهزة التي لها جلسات نشطة
- ربط الأجهزة بالغرف

### 3. إحصائيات مفيدة
- إجمالي عدد الأجهزة
- عدد الأجهزة المتاحة
- عدد الأجهزة المشغولة
- عدد الأجهزة في الصيانة

### 4. واجهة مستخدم محسنة
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان وأيقونات واضحة
- رسائل تأكيد للعمليات الحساسة
- تأثيرات بصرية جذابة

## كيفية الاستخدام

### 1. الوصول للميزة
- تسجيل الدخول كمدير عام
- الذهاب إلى "إدارة العملاء"
- النقر على أيقونة الجهاز بجانب العميل المطلوب

### 2. إضافة جهاز جديد
- النقر على زر "إضافة جهاز جديد"
- ملء البيانات المطلوبة:
  - اسم الجهاز
  - نوع الجهاز (PS4, PS5, Xbox, PC)
  - سعر الساعة العادي
  - سعر الساعة للعب الفردي
  - سعر الساعة للعب الجماعي
  - الغرفة (اختياري)
  - حالة الجهاز
- النقر على "إضافة الجهاز"

### 3. تعديل جهاز موجود
- النقر على أيقونة التعديل بجانب الجهاز
- تعديل البيانات المطلوبة
- النقر على "حفظ التغييرات"

### 4. حذف جهاز
- التأكد من عدم وجود جلسات نشطة للجهاز
- النقر على أيقونة الحذف
- تأكيد الحذف

## الأمان والحماية

### 1. التحقق من الصلاحيات
- التأكد من تسجيل الدخول كمدير
- التحقق من وجود العميل قبل عرض أجهزته

### 2. حماية البيانات
- استخدام المعاملات (Transactions) للعمليات الحساسة
- تنظيف البيانات المدخلة لمنع SQL Injection
- التحقق من صحة البيانات قبل الحفظ

### 3. منع الحذف الخاطئ
- منع حذف الأجهزة التي لها جلسات نشطة
- رسائل تأكيد للعمليات الحساسة
- تحديث الجلسات المرتبطة عند حذف جهاز

## المتطلبات التقنية

### 1. قاعدة البيانات
- جدول `devices` مع الأعمدة المطلوبة
- جدول `clients` للعملاء
- جدول `rooms` للغرف (اختياري)
- جدول `sessions` للجلسات

### 2. الملفات المطلوبة
- Bootstrap 5.3.0 للتصميم
- Font Awesome 6.0.0 للأيقونات
- jQuery أو JavaScript عادي للتفاعل

## الاختبار والتشغيل

### 1. اختبار النظام
```
http://localhost/playgood/admin/test_client_devices.php
```

### 2. فحص الأعمدة
```
http://localhost/playgood/admin/ensure_device_columns.php
```

### 3. الوصول للميزة
```
http://localhost/playgood/admin/clients.php
```

## التطوير المستقبلي

### 1. ميزات مقترحة
- تصدير قائمة أجهزة العميل إلى Excel/PDF
- إضافة صور للأجهزة
- تتبع تاريخ الصيانة
- إشعارات عند انتهاء فترة الضمان

### 2. تحسينات محتملة
- البحث والفلترة في قائمة الأجهزة
- إضافة المزيد من أنواع الأجهزة
- ربط الأجهزة بالموظفين المسؤولين
- تقارير مفصلة عن استخدام الأجهزة

## الدعم والمساعدة

في حالة وجود مشاكل أو أسئلة:
1. تحقق من ملف الاختبار أولاً
2. تأكد من وجود جميع الأعمدة المطلوبة
3. تحقق من صلاحيات قاعدة البيانات
4. راجع ملفات السجل للأخطاء
