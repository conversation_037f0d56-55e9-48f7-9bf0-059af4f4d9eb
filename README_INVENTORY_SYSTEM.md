# نظام إدارة المخزن - PlayGood

## نظرة عامة

تم إنشاء نظام إدارة المخزن المتقدم لمحلات البلايستيشن مع جميع الميزات المطلوبة:

### الميزات الرئيسية

1. **إدارة المنتجات الشاملة**
   - عرض المنتجات مع الكمية وسعر التكلفة والبيع
   - حساب الربح تلقائياً (المبلغ والنسبة المئوية)
   - ربط المنتجات بالأجهزة والذراعات
   - بيان حالة المنتج (متوفر/ناقص/نفذ/مكتمل)

2. **البحث والفلترة المتقدمة**
   - البحث بالاسم أو الباركود أو المورد
   - فلترة حسب التصنيف
   - فلترة حسب حالة المخزن
   - ترقيم الصفحات

3. **تحذيرات المخزن**
   - تحذيرات للمنتجات الناقصة
   - تحذيرات للمنتجات التي توشك على النفاذ
   - إحصائيات فورية للمخزن

4. **تتبع حركة المخزن**
   - تسجيل جميع حركات الدخول والخروج
   - تاريخ الإضافة وآخر تحديث
   - ملاحظات لكل حركة

5. **التصدير والتقارير**
   - تصدير Excel/CSV/JSON
   - تقارير مفصلة بالإحصائيات
   - فلترة البيانات المصدرة

## الملفات المنشأة

### 1. ملفات قاعدة البيانات
- `inventory_system.sql` - إنشاء وتحديث جداول المخزن

### 2. ملفات الواجهة
- `client/inventory.php` - الصفحة الرئيسية لإدارة المخزن
- تحديث `client/includes/sidebar.php` - إضافة رابط المخزن

### 3. ملفات API
- `client/api/inventory_api.php` - API شامل لإدارة المخزن
- `client/api/get_inventory_stats.php` - جلب إحصائيات المخزن
- `client/api/export_inventory.php` - تصدير بيانات المخزن

## التثبيت والإعداد

### 1. تحديث قاعدة البيانات
```sql
-- تشغيل ملف SQL لإنشاء الجداول والحقول الجديدة
SOURCE inventory_system.sql;
```

### 2. التحقق من الصلاحيات
تأكد من أن المستخدمين لديهم صلاحية `manage_cafeteria` للوصول لصفحة المخزن.

### 3. الوصول للنظام
- الرابط: `client/inventory.php`
- يظهر في الشريط الجانبي تحت "إدارة المخزن"

## هيكل قاعدة البيانات

### الجداول الجديدة/المحدثة

#### 1. تحديث جدول `cafeteria_items`
```sql
-- حقول المخزن الجديدة
stock_quantity INT DEFAULT 0           -- الكمية المتوفرة
cost_price DECIMAL(10,2) DEFAULT 0.00  -- سعر التكلفة
min_stock_level INT DEFAULT 5          -- الحد الأدنى للمخزن
max_stock_level INT DEFAULT 100        -- الحد الأقصى للمخزن
status ENUM('available', 'low_stock', 'out_of_stock', 'discontinued')
barcode VARCHAR(100)                   -- الباركود
supplier VARCHAR(200)                  -- المورد
last_restock_date TIMESTAMP           -- تاريخ آخر تجديد
```

#### 2. جدول `inventory_movements`
```sql
-- تتبع حركة المخزن
id, product_id, movement_type, quantity, previous_quantity, 
new_quantity, unit_cost, total_cost, reference_type, 
reference_id, notes, client_id, created_by, created_at
```

#### 3. جدول `product_device_associations`
```sql
-- ربط المنتجات بالأجهزة
id, product_id, device_id, device_part, quantity_needed, notes
```

#### 4. جدول `product_categories`
```sql
-- تصنيفات المنتجات المحسنة
id, name, description, parent_id, icon, color, sort_order, is_active
```

## الاستخدام

### 1. إضافة منتج جديد
- انقر على "إضافة منتج"
- املأ البيانات المطلوبة (الاسم، السعر، التكلفة، إلخ)
- حدد الكمية الأولية والحدود الدنيا والعليا

### 2. تحديث المخزن
- انقر على زر التعديل بجانب الكمية
- أدخل الكمية الجديدة ونوع الحركة
- أضف ملاحظات إذا لزم الأمر

### 3. البحث والفلترة
- استخدم حقل البحث للبحث بالاسم/الباركود/المورد
- اختر التصنيف من القائمة المنسدلة
- فلتر حسب حالة المخزن (متوفر/ناقص/نفذ)

### 4. التصدير
- انقر على "تصدير" لتحميل البيانات
- يدعم تصدير Excel, CSV, JSON
- يطبق الفلاتر الحالية على البيانات المصدرة

## الإحصائيات المعروضة

### البطاقات العلوية
1. **إجمالي المنتجات** - العدد الكلي للمنتجات
2. **منتجات نفذت** - المنتجات بكمية صفر
3. **منتجات ناقصة** - المنتجات تحت الحد الأدنى
4. **قيمة المخزن** - إجمالي قيمة المخزن بسعر التكلفة

### معلومات المنتج
- اسم المنتج والباركود
- التصنيف
- الكمية الحالية والحد الأدنى
- حالة المخزن (ملونة)
- سعر التكلفة والبيع
- الربح (المبلغ والنسبة)
- المورد
- تاريخ الإضافة

## التحذيرات والتنبيهات

### تحذيرات تلقائية
- **أحمر**: منتجات نفذت (كمية = 0)
- **أصفر**: منتجات ناقصة (كمية ≤ الحد الأدنى)
- **أخضر**: منتجات متوفرة
- **أزرق**: منتجات مكتملة (كمية ≥ الحد الأقصى)

### Triggers تلقائية
- تحديث حالة المنتج عند تغيير الكمية
- تسجيل حركة المخزن تلقائياً
- حساب قيمة المخزن

## API المتاحة

### GET Endpoints
- `GET /api/inventory_api.php?action=list` - قائمة المنتجات
- `GET /api/inventory_api.php?action=product&id=X` - منتج محدد
- `GET /api/inventory_api.php?action=movements` - حركات المخزن
- `GET /api/inventory_api.php?action=low_stock` - منتجات ناقصة
- `GET /api/get_inventory_stats.php` - إحصائيات المخزن

### POST Endpoints
- `POST /api/inventory_api.php` - إضافة/تحديث منتج
- `POST /api/inventory_api.php` - تحديث المخزن

### Export
- `GET /api/export_inventory.php?export=excel` - تصدير Excel
- `GET /api/export_inventory.php?export=csv` - تصدير CSV
- `GET /api/export_inventory.php?export=json` - تصدير JSON

## الصلاحيات المطلوبة

- `manage_cafeteria` - للوصول لصفحة المخزن
- نفس صلاحيات الكافتيريا حالياً

## الميزات المستقبلية المقترحة

1. **تكامل مع نظام المبيعات**
   - خصم تلقائي من المخزن عند البيع
   - تتبع المبيعات لكل منتج

2. **تقارير متقدمة**
   - تقرير حركة المخزن الشهري
   - تحليل الربحية لكل منتج
   - توقعات نفاذ المخزن

3. **إشعارات**
   - إشعارات عند نفاذ المنتجات
   - تذكيرات إعادة الطلب

4. **إدارة الموردين**
   - قاعدة بيانات الموردين
   - تتبع أسعار الموردين
   - طلبات الشراء

## الدعم والصيانة

في حالة وجود مشاكل:

1. تحقق من تشغيل ملف `inventory_system.sql`
2. تأكد من وجود الصلاحيات المطلوبة
3. تحقق من سجلات الأخطاء في PHP/Apache
4. تأكد من تحديث الشريط الجانبي

---

تم إنشاء النظام بواسطة Augment Agent - PlayGood System
