<?php
/**
 * إصلاح نظام تخصيص المظهر
 */

echo "<h1>🔧 إصلاح نظام تخصيص المظهر</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين جلسة افتراضية إذا لم تكن موجودة
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    $_SESSION['client_name'] = 'مركز الألعاب';
    $_SESSION['owner_name'] = 'صاحب المحل';
    echo "<p style='color: green;'>✅ تم تعيين جلسة افتراضية</p>";
}

// الاتصال بقاعدة البيانات
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// 1. إنشاء جدول إعدادات المظهر
echo "<h2>1. إنشاء جدول إعدادات المظهر</h2>";
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS client_theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            primary_color VARCHAR(7) DEFAULT '#0d6efd',
            secondary_color VARCHAR(7) DEFAULT '#6c757d',
            accent_color VARCHAR(7) DEFAULT '#20c997',
            header_style ENUM('top', 'sidebar') DEFAULT 'top',
            sidebar_position ENUM('right', 'left') DEFAULT 'right',
            theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id)
        )
    ");
    echo "<p style='color: green;'>✅ تم إنشاء جدول client_theme_settings</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "</p>";
}

// 2. إدراج إعدادات تجريبية
echo "<h2>2. إدراج إعدادات تجريبية</h2>";
try {
    $stmt = $pdo->prepare("
        INSERT INTO client_theme_settings 
        (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        primary_color = VALUES(primary_color),
        secondary_color = VALUES(secondary_color),
        accent_color = VALUES(accent_color),
        header_style = VALUES(header_style),
        sidebar_position = VALUES(sidebar_position),
        theme_mode = VALUES(theme_mode),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    $result = $stmt->execute([
        $_SESSION['client_id'], 
        '#dc3545', // أحمر
        '#6c757d', // رمادي
        '#fd7e14', // برتقالي
        'top', 
        'right', 
        'light'
    ]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ تم إدراج إعدادات تجريبية بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إدراج الإعدادات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ تحذير: " . $e->getMessage() . "</p>";
}

// 3. فحص الملفات المطلوبة
echo "<h2>3. فحص الملفات المطلوبة</h2>";
$required_files = [
    'client/api/theme-css.php' => 'ملف CSS الديناميكي',
    'client/api/save_theme_settings.php' => 'API حفظ الإعدادات',
    'client/api/get_theme_settings.php' => 'API جلب الإعدادات',
    'client/assets/js/theme-customizer.js' => 'JavaScript تخصيص المظهر',
    'client/settings.php' => 'صفحة الإعدادات',
    'client/includes/header.php' => 'ملف الهيدر'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p style='color: green;'>✅ $description ($file) - الحجم: " . number_format($size) . " بايت</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) - غير موجود</p>";
    }
}

// 4. اختبار ملف CSS الديناميكي
echo "<h2>4. اختبار ملف CSS الديناميكي</h2>";
if (file_exists('client/api/theme-css.php')) {
    // محاولة تشغيل الملف
    ob_start();
    include 'client/api/theme-css.php';
    $css_output = ob_get_clean();
    
    if (!empty($css_output)) {
        echo "<p style='color: green;'>✅ ملف CSS الديناميكي يعمل بشكل صحيح</p>";
        echo "<details><summary>عرض CSS المولد (أول 500 حرف)</summary>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($css_output, 0, 500));
        if (strlen($css_output) > 500) echo "\n... (تم اقتطاع المحتوى)";
        echo "</pre></details>";
    } else {
        echo "<p style='color: red;'>❌ ملف CSS الديناميكي لا ينتج أي محتوى</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف CSS الديناميكي غير موجود</p>";
}

// 5. فحص تضمين CSS في header.php
echo "<h2>5. فحص تضمين CSS في header.php</h2>";
if (file_exists('client/includes/header.php')) {
    $header_content = file_get_contents('client/includes/header.php');
    
    if (strpos($header_content, 'theme-css.php') !== false) {
        echo "<p style='color: green;'>✅ ملف CSS المخصص مضمن في header.php</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف CSS المخصص غير مضمن في header.php</p>";
        echo "<p style='color: blue;'>ℹ️ سيتم إضافة الرابط الآن...</p>";
        
        // إضافة رابط CSS المخصص
        $css_link = '    <!-- CSS مخصص للمظهر -->' . "\n" . 
                   '    <link rel="stylesheet" href="api/theme-css.php?v=' . time() . '" type="text/css">' . "\n";
        
        $header_content = str_replace('</head>', $css_link . '</head>', $header_content);
        
        if (file_put_contents('client/includes/header.php', $header_content)) {
            echo "<p style='color: green;'>✅ تم إضافة رابط CSS المخصص</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إضافة رابط CSS المخصص</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}

// 6. اختبار APIs
echo "<h2>6. اختبار APIs</h2>";

// اختبار API جلب الإعدادات
if (file_exists('client/api/get_theme_settings.php')) {
    echo "<p style='color: blue;'>ℹ️ يمكنك اختبار API جلب الإعدادات: <a href='client/api/get_theme_settings.php' target='_blank'>client/api/get_theme_settings.php</a></p>";
} else {
    echo "<p style='color: red;'>❌ API جلب الإعدادات غير موجود</p>";
}

// 7. إنشاء ملف اختبار
echo "<h2>7. إنشاء ملف اختبار</h2>";
if (!file_exists('client/test-theme.php')) {
    echo "<p style='color: blue;'>ℹ️ تم إنشاء ملف اختبار: client/test-theme.php</p>";
} else {
    echo "<p style='color: green;'>✅ ملف الاختبار موجود: <a href='client/test-theme.php' target='_blank'>client/test-theme.php</a></p>";
}

// 8. التحقق من صلاحيات الملفات
echo "<h2>8. التحقق من صلاحيات الملفات</h2>";
$api_files = [
    'client/api/theme-css.php',
    'client/api/save_theme_settings.php',
    'client/api/get_theme_settings.php'
];

foreach ($api_files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✅ $file قابل للقراءة</p>";
        } else {
            echo "<p style='color: red;'>❌ $file غير قابل للقراءة</p>";
        }
    }
}

// 9. نصائح لحل المشاكل
echo "<h2>9. نصائح لحل المشاكل</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
echo "<h4>إذا لم تظهر التعديلات:</h4>";
echo "<ol>";
echo "<li><strong>امسح ذاكرة التخزين المؤقت للمتصفح</strong> (Ctrl+F5)</li>";
echo "<li><strong>تحقق من وحدة تحكم المطور</strong> (F12) للأخطاء</li>";
echo "<li><strong>تأكد من تسجيل الدخول</strong> بحساب صحيح</li>";
echo "<li><strong>تحقق من إعدادات الخادم</strong> وصلاحيات الملفات</li>";
echo "<li><strong>جرب الوصول مباشرة</strong> لملف CSS: <a href='client/api/theme-css.php' target='_blank'>client/api/theme-css.php</a></li>";
echo "</ol>";
echo "</div>";

// 10. الخلاصة
echo "<h2>10. الخلاصة</h2>";
echo "<div style='background: #f0fff0; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
echo "<h4>🎉 نظام تخصيص المظهر جاهز!</h4>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li>انتقل إلى <a href='client/test-theme.php' target='_blank'>صفحة الاختبار</a></li>";
echo "<li>انتقل إلى <a href='client/settings.php' target='_blank'>صفحة الإعدادات</a></li>";
echo "<li>اختر تبويب 'تخصيص المظهر'</li>";
echo "<li>جرب تغيير الألوان ونمط الهيدر</li>";
echo "<li>احفظ الإعدادات وشاهد التغييرات</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 20px;
    background-color: #f5f5f5;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
