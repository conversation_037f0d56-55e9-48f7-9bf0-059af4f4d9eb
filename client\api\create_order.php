<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$created_by = isset($_SESSION['employee_id']) ? $_SESSION['employee_id'] : $_SESSION['client_id'];

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($data['items']) || empty($data['items'])) {
        throw new Exception('يجب إضافة منتج واحد على الأقل');
    }

    $pdo->beginTransaction();

    // إنشاء رقم الأوردر
    $order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

    // حساب المجموع الكلي
    $total_amount = 0;
    $items_data = [];

    foreach ($data['items'] as $item) {
        if (!isset($item['product_id']) || !isset($item['quantity']) || $item['quantity'] <= 0) {
            throw new Exception('بيانات المنتج غير صحيحة');
        }

        // جلب معلومات المنتج
        $stmt = $pdo->prepare("SELECT id, name, price FROM cafeteria_items WHERE id = ? AND client_id = ?");
        $stmt->execute([$item['product_id'], $client_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$product) {
            throw new Exception('المنتج غير موجود');
        }

        $quantity = intval($item['quantity']);
        $price = floatval($product['price']);
        $item_total = $price * $quantity;
        $total_amount += $item_total;

        $items_data[] = [
            'product_id' => $product['id'],
            'product_name' => $product['name'],
            'quantity' => $quantity,
            'price' => $price,
            'total_price' => $item_total
        ];
    }

    // إنشاء الأوردر
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            client_id, 
            customer_id, 
            order_number, 
            total_amount, 
            status, 
            payment_method, 
            notes, 
            created_by,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ");

    $stmt->execute([
        $client_id,
        isset($data['customer_id']) && !empty($data['customer_id']) ? $data['customer_id'] : null,
        $order_number,
        $total_amount,
        isset($data['status']) ? $data['status'] : 'pending',
        isset($data['payment_method']) ? $data['payment_method'] : 'cash',
        isset($data['notes']) ? $data['notes'] : null,
        $created_by
    ]);

    $order_id = $pdo->lastInsertId();

    // إضافة المنتجات للأوردر
    $stmt = $pdo->prepare("
        INSERT INTO order_items (order_id, product_id, quantity, price, total_price) 
        VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($items_data as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['quantity'],
            $item['price'],
            $item['total_price']
        ]);
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء الأوردر بنجاح',
        'order_id' => $order_id,
        'order_number' => $order_number,
        'total_amount' => number_format($total_amount, 2)
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
