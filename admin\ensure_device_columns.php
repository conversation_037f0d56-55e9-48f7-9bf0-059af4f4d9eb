<?php
/**
 * التأكد من وجود الأعمدة المطلوبة في جدول الأجهزة
 * PlayGood - نظام إدارة محلات البلايستيشن
 */

require_once '../config/database.php';

function ensureDeviceColumns($pdo) {
    try {
        // فحص وجود الأعمدة المطلوبة
        $stmt = $pdo->query("DESCRIBE devices");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = [
            'single_rate' => 'DECIMAL(10,2) DEFAULT NULL',
            'multi_rate' => 'DECIMAL(10,2) DEFAULT NULL',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];
        
        $added_columns = [];
        
        foreach ($required_columns as $column_name => $column_definition) {
            if (!in_array($column_name, $columns)) {
                try {
                    $sql = "ALTER TABLE devices ADD COLUMN {$column_name} {$column_definition}";
                    $pdo->exec($sql);
                    $added_columns[] = $column_name;
                } catch (PDOException $e) {
                    // تجاهل الخطأ إذا كان العمود موجود مسبقاً
                    if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        return [
            'success' => true,
            'added_columns' => $added_columns,
            'message' => count($added_columns) > 0 ? 
                'تم إضافة الأعمدة: ' . implode(', ', $added_columns) : 
                'جميع الأعمدة موجودة مسبقاً'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'خطأ في فحص/إضافة الأعمدة: ' . $e->getMessage()
        ];
    }
}

// تشغيل الدالة إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'ensure_device_columns.php') {
    $result = ensureDeviceColumns($pdo);
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>فحص أعمدة جدول الأجهزة</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
    echo "</head>";
    echo "<body class='bg-light'>";
    echo "<div class='container mt-5'>";
    echo "<div class='row justify-content-center'>";
    echo "<div class='col-md-8'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h5 class='mb-0'>فحص أعمدة جدول الأجهزة</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    if ($result['success']) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo $result['message'];
        echo "</div>";
        
        if (!empty($result['added_columns'])) {
            echo "<div class='alert alert-info'>";
            echo "<strong>الأعمدة المضافة:</strong>";
            echo "<ul class='mb-0 mt-2'>";
            foreach ($result['added_columns'] as $column) {
                echo "<li>{$column}</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-exclamation-circle me-2'></i>";
        echo $result['message'];
        echo "</div>";
    }
    
    echo "<div class='mt-3'>";
    echo "<a href='clients.php' class='btn btn-primary'>العودة لإدارة العملاء</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
