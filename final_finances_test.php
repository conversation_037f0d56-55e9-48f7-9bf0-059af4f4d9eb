<?php
/**
 * اختبار نهائي لصفحة الماليات - PlayGood
 * للتحقق من أن جميع الإصلاحات تعمل بشكل صحيح
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>✅ اختبار نهائي لصفحة الماليات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    $all_tests_passed = true;
    
    // 1. اختبار البيانات المالية
    echo "<h2>1. اختبار البيانات المالية</h2>";
    
    try {
        // اختبار المصروفات
        $expenses_test = $pdo->prepare("
            SELECT e.*, et.name as expense_type_name
            FROM expenses e
            JOIN expense_types et ON e.expense_type_id = et.id
            WHERE e.client_id = ?
            ORDER BY e.expense_date DESC
            LIMIT 3
        ");
        $expenses_test->execute([$client_id]);
        $test_expenses = $expenses_test->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>أ. اختبار المصروفات:</h3>";
        if (!empty($test_expenses)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>المبلغ</th>";
            echo "<th style='padding: 8px;'>التنسيق</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "</tr>";
            
            foreach ($test_expenses as $expense) {
                $is_valid = is_numeric($expense['amount']) && $expense['amount'] > 0;
                $formatted = $is_valid ? number_format($expense['amount'], 2) . " ج.م" : "خطأ في التنسيق";
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($expense['expense_type_name']) . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_valid ? "green" : "red") . ";'>" . htmlspecialchars($expense['amount']) . "</td>";
                echo "<td style='padding: 8px;'>" . $formatted . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_valid ? "green" : "red") . ";'>" . ($is_valid ? "✅ صحيح" : "❌ خطأ") . "</td>";
                echo "</tr>";
                
                if (!$is_valid) $all_tests_passed = false;
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد مصروفات للاختبار</p>";
        }
        
        // اختبار الإيرادات
        $income_test = $pdo->prepare("
            SELECT ai.*, it.name as income_type_name
            FROM additional_income ai
            JOIN income_types it ON ai.income_type_id = it.id
            WHERE ai.client_id = ?
            ORDER BY ai.income_date DESC
            LIMIT 3
        ");
        $income_test->execute([$client_id]);
        $test_income = $income_test->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>ب. اختبار الإيرادات:</h3>";
        if (!empty($test_income)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>المبلغ</th>";
            echo "<th style='padding: 8px;'>التنسيق</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "</tr>";
            
            foreach ($test_income as $income) {
                $is_valid = is_numeric($income['amount']) && $income['amount'] > 0;
                $formatted = $is_valid ? number_format($income['amount'], 2) . " ج.م" : "خطأ في التنسيق";
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($income['income_type_name']) . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_valid ? "green" : "red") . ";'>" . htmlspecialchars($income['amount']) . "</td>";
                echo "<td style='padding: 8px;'>" . $formatted . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_valid ? "green" : "red") . ";'>" . ($is_valid ? "✅ صحيح" : "❌ خطأ") . "</td>";
                echo "</tr>";
                
                if (!$is_valid) $all_tests_passed = false;
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد إيرادات للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار البيانات: " . $e->getMessage() . "</p>";
        $all_tests_passed = false;
    }
    
    // 2. اختبار الملخص المالي
    echo "<h2>2. اختبار الملخص المالي</h2>";
    
    try {
        $current_month = date('Y-m');
        
        // إجمالي المصروفات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM expenses
            WHERE client_id = ? AND DATE_FORMAT(expense_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_expenses = $stmt->fetchColumn();
        
        // إجمالي الإيرادات الإضافية
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_additional_income
            FROM additional_income
            WHERE client_id = ? AND DATE_FORMAT(income_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_additional_income = $stmt->fetchColumn();
        
        // إجمالي إيرادات الجلسات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(s.total_cost), 0) as total_sessions_income
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ?
            AND s.status = 'completed'
            AND DATE_FORMAT(s.start_time, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_sessions_income = $stmt->fetchColumn();
        
        $net_profit = ($total_sessions_income + $total_additional_income) - $total_expenses;
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 الملخص المالي للشهر الحالي:</h4>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>البند</th>";
        echo "<th style='padding: 8px;'>القيمة الخام</th>";
        echo "<th style='padding: 8px;'>منسق</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        $financial_items = [
            ['إجمالي المصروفات', $total_expenses, 'danger'],
            ['إيرادات الجلسات', $total_sessions_income, 'success'],
            ['الإيرادات الإضافية', $total_additional_income, 'success'],
            ['صافي الربح', $net_profit, $net_profit >= 0 ? 'success' : 'danger']
        ];
        
        foreach ($financial_items as $item) {
            $is_valid = is_numeric($item[1]);
            $formatted = $is_valid ? number_format($item[1], 2) . " ج.م" : "خطأ";
            $color = $item[2] === 'danger' ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $item[0] . "</td>";
            echo "<td style='padding: 8px; color: $color;'>" . $item[1] . "</td>";
            echo "<td style='padding: 8px; color: $color;'>" . $formatted . "</td>";
            echo "<td style='padding: 8px; color: " . ($is_valid ? "green" : "red") . ";'>" . ($is_valid ? "✅ صحيح" : "❌ خطأ") . "</td>";
            echo "</tr>";
            
            if (!$is_valid) $all_tests_passed = false;
        }
        echo "</table>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار الملخص المالي: " . $e->getMessage() . "</p>";
        $all_tests_passed = false;
    }
    
    // 3. اختبار عدم وجود خلط في البيانات
    echo "<h2>3. اختبار عدم وجود خلط في البيانات</h2>";
    
    try {
        // فحص المصروفات للتأكد من عدم وجود أسماء في حقل المبلغ
        $mixed_expenses = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM expenses 
            WHERE client_id = ? 
            AND (
                amount REGEXP '^[أ-ي\\s]+$' 
                OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$'
            )
        ");
        $mixed_expenses->execute([$client_id]);
        $mixed_expenses_count = $mixed_expenses->fetchColumn();
        
        // فحص الإيرادات
        $mixed_income = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM additional_income 
            WHERE client_id = ? 
            AND (
                amount REGEXP '^[أ-ي\\s]+$' 
                OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$'
            )
        ");
        $mixed_income->execute([$client_id]);
        $mixed_income_count = $mixed_income->fetchColumn();
        
        echo "<div style='background: " . ($mixed_expenses_count == 0 && $mixed_income_count == 0 ? "#d4edda" : "#f8d7da") . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🔍 نتائج فحص خلط البيانات:</h4>";
        echo "<p><strong>مصروفات بمبالغ غير صحيحة:</strong> " . $mixed_expenses_count . "</p>";
        echo "<p><strong>إيرادات بمبالغ غير صحيحة:</strong> " . $mixed_income_count . "</p>";
        
        if ($mixed_expenses_count == 0 && $mixed_income_count == 0) {
            echo "<p style='color: green;'>✅ لا يوجد خلط في البيانات - جميع المبالغ صحيحة</p>";
        } else {
            echo "<p style='color: red;'>❌ يوجد خلط في البيانات - يحتاج إصلاح</p>";
            $all_tests_passed = false;
        }
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص خلط البيانات: " . $e->getMessage() . "</p>";
        $all_tests_passed = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
}

// النتيجة النهائية
echo "<h2>📋 النتيجة النهائية</h2>";

if ($all_tests_passed) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 تم حل المشكلة بنجاح!</h3>";
    echo "<p><strong>جميع الاختبارات نجحت</strong></p>";
    echo "<p>صفحة الماليات تعمل بشكل صحيح الآن</p>";
    echo "<a href='client/finances.php' class='btn btn-success' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>زيارة صفحة الماليات</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
    echo "<h3>⚠️ لا تزال هناك مشاكل</h3>";
    echo "<p>بعض الاختبارات فشلت - يحتاج مزيد من الإصلاح</p>";
    echo "<p>راجع النتائج أعلاه وشغل ملفات الإصلاح المناسبة</p>";
    echo "</div>";
}

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🛠️ ملفات الإصلاح المتاحة:</h4>";
echo "<ul>";
echo "<li><a href='fix_amount_customer_mix.php'>fix_amount_customer_mix.php</a> - إصلاح خلط أسماء العملاء مع المبالغ</li>";
echo "<li><a href='fix_finances_display.php'>fix_finances_display.php</a> - إصلاح البيانات المالية العامة</li>";
echo "<li><a href='debug_expenses_data.php'>debug_expenses_data.php</a> - تشخيص مفصل للمصروفات</li>";
echo "<li><a href='comprehensive_finances_diagnosis.php'>comprehensive_finances_diagnosis.php</a> - تشخيص شامل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
?>
