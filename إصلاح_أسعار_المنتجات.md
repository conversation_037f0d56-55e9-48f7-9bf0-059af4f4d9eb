# إصلاح مشكلة عرض أسعار المنتجات في صفحة الجلسات

## المشكلة
كانت أسعار المنتجات تظهر كـ "0.00" في صفحة الجلسات عند إضافة منتج إلى الجلسة، مما يؤثر على:
- عرض أسعار المنتجات المضافة للجلسة
- حساب التكلفة الإجمالية للجلسة
- دقة الفواتير والتقارير المالية

## أسباب المشكلة
تم اكتشاف مشكلتين رئيسيتين:

### 1. مشكلة في API جلب المنتجات
في ملف `client/api/get_products.php`، كان السعر يتم تنسيقه باستخدام `number_format()` مما يحوله إلى نص بدلاً من رقم:

```php
// الكود القديم (المشكلة)
'price' => number_format((float)$product['price'], 2),
```

### 2. مشكلة في API جلب منتجات الجلسة
في ملف `client/api/get_session_products.php`، كان هناك تضارب في أسماء الأعمدة بين `price`/`unit_price` و `total`/`total_price`.

## الحل المطبق

### 1. إصلاح ملف `client/api/get_products.php`
```php
// الكود الجديد (الحل)
'price' => (float)$product['price'], // إرسال السعر كرقم وليس نص منسق
'price_formatted' => number_format((float)$product['price'], 2), // السعر المنسق للعرض
```

### 2. إصلاح ملف `client/api/get_session_products.php`
```php
// الكود الجديد - يدعم كلا من البنيتين
SELECT
    sp.id,
    sp.product_id,
    sp.quantity,
    COALESCE(sp.unit_price, sp.price, 0) as price,
    COALESCE(ci.name, 'منتج محذوف') as product_name,
    COALESCE(sp.total_price, sp.total, sp.quantity * COALESCE(sp.unit_price, sp.price, 0)) as total,
    sp.created_at
FROM session_products sp
LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
```

### 3. تحديث ملف `client/sessions.php`
تم تحديث عرض الأسعار في ثلاثة أماكن:

```javascript
// الكود الجديد
<p class="card-text text-primary fw-bold">${product.price_formatted || product.price} ج.م</p>
```

**المواقع المحدثة:**
- السطر 1187: modal إضافة المنتجات للجلسة العادية
- السطر 1753: modal إضافة المنتجات في تعديل الجلسة
- السطر 1934: modal إضافة المنتجات عند إنهاء الجلسة

### 4. إصلاح بنية جدول `session_products`
تم إنشاء ملفات إصلاح لضمان وجود الأعمدة المطلوبة:
- `unit_price`: سعر الوحدة الواحدة
- `total_price`: إجمالي السعر (الكمية × سعر الوحدة)

## الفوائد من هذا الإصلاح

1. **عرض صحيح للأسعار**: الأسعار تظهر الآن بقيمتها الحقيقية بدلاً من 0
2. **مرونة في العرض**: يمكن استخدام السعر الرقمي للحسابات والسعر المنسق للعرض
3. **توافق مع JavaScript**: السعر الرقمي يعمل بشكل صحيح في العمليات الحسابية
4. **عدم كسر الكود الموجود**: الحل متوافق مع الكود الحالي

## ملفات الاختبار والإصلاح المنشأة

### ملفات الاختبار:
1. `test_product_price_fix.php` - اختبار شامل لعرض أسعار المنتجات
2. `quick_test_api.php` - اختبار سريع لـ API المنتجات
3. `test_session_products_fix.php` - اختبار إصلاح عرض منتجات الجلسة
4. `check_session_products_structure.php` - فحص بنية جدول session_products

### ملفات الإصلاح:
1. `fix_session_products_table.php` - إصلاح بنية جدول session_products
2. `fix_existing_session_products.php` - إصلاح البيانات الموجودة في الجدول

## كيفية التحقق من الإصلاح

1. افتح صفحة الجلسات: `client/sessions.php`
2. انقر على "إضافة منتجات" لأي جلسة نشطة
3. تحقق من أن أسعار المنتجات تظهر بشكل صحيح (وليس 0)
4. جرب إضافة منتج للجلسة للتأكد من أن العملية تتم بنجاح

## الملفات المعدلة

- `client/api/get_products.php` - تعديل طريقة إرسال أسعار المنتجات
- `client/api/get_session_products.php` - إصلاح جلب أسعار منتجات الجلسة
- `client/sessions.php` - تحديث عرض الأسعار في واجهة المستخدم

## خطوات التحقق من الإصلاح

### 1. فحص بنية الجدول:
```bash
# افتح في المتصفح
http://localhost/playgood/check_session_products_structure.php
```

### 2. إصلاح الجدول إذا لزم الأمر:
```bash
# افتح في المتصفح
http://localhost/playgood/fix_session_products_table.php
```

### 3. اختبار الوظائف:
```bash
# افتح في المتصفح
http://localhost/playgood/test_session_products_fix.php
```

### 4. اختبار في صفحة الجلسات:
1. افتح `client/sessions.php`
2. انقر على "إضافة منتجات" لأي جلسة نشطة
3. تحقق من ظهور الأسعار الصحيحة
4. أضف منتج وتحقق من ظهور السعر في قائمة المنتجات المضافة

## ملاحظات مهمة

- الإصلاح يحافظ على التوافق مع الكود الموجود
- لا يؤثر على أي وظائف أخرى في النظام
- يحسن من دقة عرض البيانات المالية
