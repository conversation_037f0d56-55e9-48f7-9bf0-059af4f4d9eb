<?php
/**
 * دوال مساعدة لنظام الورديات
 * تحتوي على الدوال المطلوبة لحساب ساعات العمل والإضافي
 */

/**
 * حساب ساعات العمل والإضافي لسجل حضور محدد
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $attendance_id معرف سجل الحضور
 * @return array نتائج الحساب
 */
function calculateAttendanceHours($pdo, $attendance_id) {
    try {
        // جلب بيانات الحضور والوردية
        $stmt = $pdo->prepare("
            SELECT 
                sa.*,
                s.start_time as scheduled_start,
                s.end_time as scheduled_end,
                s.break_duration
            FROM shift_attendance sa
            JOIN shifts s ON sa.shift_id = s.shift_id
            WHERE sa.attendance_id = ?
        ");
        $stmt->execute([$attendance_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$data) {
            return ['error' => 'سجل الحضور غير موجود'];
        }
        
        // التحقق من وجود أوقات الحضور والانصراف
        if (!$data['check_in_time'] || !$data['check_out_time']) {
            return ['error' => 'أوقات الحضور والانصراف غير مكتملة'];
        }
        
        $check_in = new DateTime($data['check_in_time']);
        $check_out = new DateTime($data['check_out_time']);
        $scheduled_start = new DateTime(date('Y-m-d') . ' ' . $data['scheduled_start']);
        $scheduled_end = new DateTime(date('Y-m-d') . ' ' . $data['scheduled_end']);
        
        // حساب إجمالي الساعات
        $total_minutes = $check_out->diff($check_in)->h * 60 + $check_out->diff($check_in)->i;
        $total_hours = $total_minutes / 60.0;
        
        // حساب وقت الاستراحة
        $break_hours = 0;
        if ($data['break_start_time'] && $data['break_end_time']) {
            $break_start = new DateTime($data['break_start_time']);
            $break_end = new DateTime($data['break_end_time']);
            $break_minutes = $break_end->diff($break_start)->h * 60 + $break_end->diff($break_start)->i;
            $break_hours = $break_minutes / 60.0;
        } else {
            // استخدام مدة الاستراحة المجدولة
            $break_hours = ($data['break_duration'] ?? 0) / 60.0;
        }
        
        // حساب ساعات العمل الفعلية
        $actual_hours = max(0, $total_hours - $break_hours);
        
        // حساب الساعات الإضافية (أكثر من 8 ساعات)
        $overtime_hours = max(0, $actual_hours - 8);
        
        // حساب التأخير
        $late_minutes = max(0, $check_in->diff($scheduled_start)->i + ($check_in->diff($scheduled_start)->h * 60));
        if ($check_in < $scheduled_start) {
            $late_minutes = 0; // وصل مبكراً
        }
        
        // حساب الخروج المبكر
        $early_leave_minutes = max(0, $scheduled_end->diff($check_out)->i + ($scheduled_end->diff($check_out)->h * 60));
        if ($check_out > $scheduled_end) {
            $early_leave_minutes = 0; // خرج متأخراً
        }
        
        // تحديد حالة الحضور
        $status = 'present';
        if ($late_minutes > 15) {
            $status = 'late';
        } elseif ($early_leave_minutes > 15) {
            $status = 'early_leave';
        } elseif ($overtime_hours > 0.5) {
            $status = 'overtime';
        }
        
        $result = [
            'actual_hours' => round($actual_hours, 2),
            'overtime_hours' => round($overtime_hours, 2),
            'break_hours' => round($break_hours, 2),
            'late_minutes' => $late_minutes,
            'early_leave_minutes' => $early_leave_minutes,
            'status' => $status
        ];
        
        // تحديث قاعدة البيانات
        $update_stmt = $pdo->prepare("
            UPDATE shift_attendance SET
                actual_hours = ?,
                overtime_hours = ?,
                break_hours = ?,
                late_minutes = ?,
                early_leave_minutes = ?,
                status = ?
            WHERE attendance_id = ?
        ");
        
        $update_stmt->execute([
            $result['actual_hours'],
            $result['overtime_hours'],
            $result['break_hours'],
            $result['late_minutes'],
            $result['early_leave_minutes'],
            $result['status'],
            $attendance_id
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        return ['error' => 'خطأ في حساب ساعات العمل: ' . $e->getMessage()];
    }
}

/**
 * إنشاء ورديات من قالب محدد
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $client_id معرف العميل
 * @param int $template_id معرف القالب
 * @param string $start_date تاريخ البداية
 * @param string $end_date تاريخ النهاية
 * @param int $created_by معرف منشئ الورديات
 * @return array نتيجة العملية
 */
function createShiftsFromTemplate($pdo, $client_id, $template_id, $start_date, $end_date, $created_by) {
    try {
        // جلب بيانات القالب
        $template_stmt = $pdo->prepare("
            SELECT * FROM shift_templates 
            WHERE template_id = ? AND client_id = ? AND is_active = 1
        ");
        $template_stmt->execute([$template_id, $client_id]);
        $template = $template_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            return ['error' => 'القالب غير موجود أو غير نشط'];
        }
        
        $current_date = new DateTime($start_date);
        $end_date_obj = new DateTime($end_date);
        $created_shifts = 0;
        
        // إنشاء الورديات للفترة المحددة
        while ($current_date <= $end_date_obj) {
            $shift_name = $template['template_name'] . ' - ' . $current_date->format('Y-m-d');
            
            // التحقق من عدم وجود وردية في نفس التاريخ والوقت
            $check_stmt = $pdo->prepare("
                SELECT COUNT(*) FROM shifts 
                WHERE client_id = ? AND shift_date = ? AND start_time = ? AND end_time = ?
            ");
            $check_stmt->execute([
                $client_id, 
                $current_date->format('Y-m-d'), 
                $template['start_time'], 
                $template['end_time']
            ]);
            
            if ($check_stmt->fetchColumn() == 0) {
                // إنشاء الوردية
                $insert_stmt = $pdo->prepare("
                    INSERT INTO shifts (
                        client_id, template_id, shift_name, shift_date, 
                        start_time, end_time, break_duration, is_overnight, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $insert_stmt->execute([
                    $client_id,
                    $template_id,
                    $shift_name,
                    $current_date->format('Y-m-d'),
                    $template['start_time'],
                    $template['end_time'],
                    $template['break_duration'],
                    $template['is_overnight'],
                    $created_by
                ]);
                
                $created_shifts++;
            }
            
            // الانتقال لليوم التالي
            $current_date->add(new DateInterval('P1D'));
        }
        
        return [
            'success' => true,
            'created_shifts' => $created_shifts,
            'message' => "تم إنشاء $created_shifts وردية بنجاح"
        ];
        
    } catch (Exception $e) {
        return ['error' => 'خطأ في إنشاء الورديات: ' . $e->getMessage()];
    }
}

/**
 * حساب إحصائيات الحضور لموظف محدد
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $employee_id معرف الموظف
 * @param string $start_date تاريخ البداية
 * @param string $end_date تاريخ النهاية
 * @return array الإحصائيات
 */
function getEmployeeAttendanceStats($pdo, $employee_id, $start_date, $end_date) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(sa.attendance_id) as total_shifts,
                COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_days,
                COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_days,
                COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late_days,
                SUM(sa.actual_hours) as total_hours,
                SUM(sa.overtime_hours) as total_overtime,
                AVG(sa.late_minutes) as avg_late_minutes,
                (COUNT(CASE WHEN sa.status = 'present' THEN 1 END) * 100.0 / NULLIF(COUNT(sa.attendance_id), 0)) as attendance_percentage
            FROM shift_attendance sa
            JOIN shifts s ON sa.shift_id = s.shift_id
            WHERE sa.employee_id = ? AND s.shift_date BETWEEN ? AND ?
        ");
        
        $stmt->execute([$employee_id, $start_date, $end_date]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // تنسيق النتائج
        return [
            'total_shifts' => (int)($stats['total_shifts'] ?? 0),
            'present_days' => (int)($stats['present_days'] ?? 0),
            'absent_days' => (int)($stats['absent_days'] ?? 0),
            'late_days' => (int)($stats['late_days'] ?? 0),
            'total_hours' => round($stats['total_hours'] ?? 0, 2),
            'total_overtime' => round($stats['total_overtime'] ?? 0, 2),
            'avg_late_minutes' => round($stats['avg_late_minutes'] ?? 0, 0),
            'attendance_percentage' => round($stats['attendance_percentage'] ?? 0, 1)
        ];
        
    } catch (Exception $e) {
        return ['error' => 'خطأ في حساب الإحصائيات: ' . $e->getMessage()];
    }
}

/**
 * التحقق من تضارب الورديات للموظف
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $employee_id معرف الموظف
 * @param string $shift_date تاريخ الوردية
 * @param string $start_time وقت البداية
 * @param string $end_time وقت النهاية
 * @param int $exclude_shift_id معرف الوردية المستثناة (للتعديل)
 * @return bool هل يوجد تضارب
 */
function checkShiftConflict($pdo, $employee_id, $shift_date, $start_time, $end_time, $exclude_shift_id = null) {
    try {
        $query = "
            SELECT COUNT(*) FROM employee_shifts es
            JOIN shifts s ON es.shift_id = s.shift_id
            WHERE es.employee_id = ?
            AND s.shift_date = ?
            AND (
                (s.start_time <= ? AND s.end_time > ?) OR
                (s.start_time < ? AND s.end_time >= ?) OR
                (s.start_time >= ? AND s.end_time <= ?)
            )
        ";

        $params = [$employee_id, $shift_date, $start_time, $start_time, $end_time, $end_time, $start_time, $end_time];

        if ($exclude_shift_id) {
            $query .= " AND s.shift_id != ?";
            $params[] = $exclude_shift_id;
        }

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;

    } catch (Exception $e) {
        return false; // في حالة الخطأ، نفترض عدم وجود تضارب
    }
}

/**
 * التحقق من حضور الموظف في وردية نشطة
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $employee_id معرف الموظف
 * @return array معلومات الحضور
 */
function isEmployeeCheckedIn($pdo, $employee_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                sa.attendance_id,
                sa.check_in_time,
                sa.check_out_time,
                sa.break_start_time,
                sa.break_end_time,
                s.shift_name,
                s.shift_date,
                s.start_time,
                s.end_time,
                CASE
                    WHEN sa.check_in_time IS NULL THEN 'not_checked_in'
                    WHEN sa.check_out_time IS NOT NULL THEN 'checked_out'
                    WHEN sa.break_start_time IS NOT NULL AND sa.break_end_time IS NULL THEN 'on_break'
                    ELSE 'checked_in'
                END as status
            FROM shift_attendance sa
            JOIN shifts s ON sa.shift_id = s.shift_id
            WHERE sa.employee_id = ?
            AND s.shift_date = CURDATE()
            AND sa.check_in_time IS NOT NULL
            ORDER BY sa.check_in_time DESC
            LIMIT 1
        ");

        $stmt->execute([$employee_id]);
        $attendance = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$attendance) {
            return [
                'is_checked_in' => false,
                'status' => 'no_shift_today',
                'message' => 'لا توجد وردية مجدولة اليوم أو لم يتم تسجيل الحضور'
            ];
        }

        $is_active = ($attendance['status'] == 'checked_in' || $attendance['status'] == 'on_break');

        return [
            'is_checked_in' => $is_active,
            'status' => $attendance['status'],
            'attendance_id' => $attendance['attendance_id'],
            'shift_name' => $attendance['shift_name'],
            'shift_date' => $attendance['shift_date'],
            'check_in_time' => $attendance['check_in_time'],
            'check_out_time' => $attendance['check_out_time'],
            'break_start_time' => $attendance['break_start_time'],
            'break_end_time' => $attendance['break_end_time'],
            'message' => $is_active ? 'الموظف مسجل حضور في وردية نشطة' : 'الموظف منصرف من الوردية'
        ];

    } catch (Exception $e) {
        return [
            'is_checked_in' => false,
            'status' => 'error',
            'message' => 'خطأ في التحقق من حضور الموظف: ' . $e->getMessage()
        ];
    }
}

/**
 * التحقق من إمكانية الموظف إنشاء جلسات جديدة
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $employee_id معرف الموظف
 * @return array نتيجة التحقق
 */
function canEmployeeCreateSessions($pdo, $employee_id) {
    try {
        // التحقق من حضور الموظف
        $attendance_check = isEmployeeCheckedIn($pdo, $employee_id);

        if (!$attendance_check['is_checked_in']) {
            return [
                'can_create' => false,
                'reason' => 'attendance_required',
                'message' => 'يجب تسجيل الحضور في وردية نشطة أولاً لإنشاء جلسات جديدة',
                'attendance_status' => $attendance_check['status']
            ];
        }

        // التحقق من حالة الاستراحة
        if ($attendance_check['status'] == 'on_break') {
            return [
                'can_create' => false,
                'reason' => 'on_break',
                'message' => 'لا يمكن إنشاء جلسات أثناء فترة الاستراحة',
                'attendance_status' => $attendance_check['status']
            ];
        }

        return [
            'can_create' => true,
            'reason' => 'authorized',
            'message' => 'يمكن إنشاء جلسات جديدة',
            'attendance_status' => $attendance_check['status'],
            'shift_name' => $attendance_check['shift_name']
        ];

    } catch (Exception $e) {
        return [
            'can_create' => false,
            'reason' => 'error',
            'message' => 'خطأ في التحقق من إمكانية إنشاء الجلسات: ' . $e->getMessage()
        ];
    }
}
?>
