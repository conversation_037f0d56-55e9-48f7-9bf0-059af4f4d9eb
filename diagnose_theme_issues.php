<?php
/**
 * تشخيص مشاكل نظام تخصيص المظهر
 */

echo "<h1>🔍 تشخيص مشاكل نظام تخصيص المظهر</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>1. فحص الجلسة</h2>";
if (isset($_SESSION['client_id'])) {
    echo "<p style='color: green;'>✅ معرف العميل: " . $_SESSION['client_id'] . "</p>";
} else {
    echo "<p style='color: red;'>❌ معرف العميل غير موجود في الجلسة</p>";
    $_SESSION['client_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ تم تعيين معرف عميل افتراضي: 1</p>";
}

// فحص قاعدة البيانات
echo "<h2>2. فحص قاعدة البيانات</h2>";
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص وجود الجدول
    $table_check = $pdo->query("SHOW TABLES LIKE 'client_theme_settings'");
    if ($table_check->rowCount() > 0) {
        echo "<p style='color: green;'>✅ جدول client_theme_settings موجود</p>";
        
        // فحص البيانات
        $stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
        $stmt->execute([$_SESSION['client_id']]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            echo "<p style='color: green;'>✅ إعدادات المظهر موجودة للعميل</p>";
            echo "<ul>";
            echo "<li>اللون الأساسي: <span style='color: " . $settings['primary_color'] . "; font-weight: bold;'>" . $settings['primary_color'] . "</span></li>";
            echo "<li>اللون الثانوي: <span style='color: " . $settings['secondary_color'] . "; font-weight: bold;'>" . $settings['secondary_color'] . "</span></li>";
            echo "<li>اللون المميز: <span style='color: " . $settings['accent_color'] . "; font-weight: bold;'>" . $settings['accent_color'] . "</span></li>";
            echo "<li>نمط الهيدر: " . $settings['header_style'] . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد إعدادات مظهر للعميل</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ جدول client_theme_settings غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// فحص ملف CSS الديناميكي
echo "<h2>3. فحص ملف CSS الديناميكي</h2>";
$css_file = 'client/api/theme-css.php';
if (file_exists($css_file)) {
    echo "<p style='color: green;'>✅ ملف CSS الديناميكي موجود</p>";
    
    // محاولة تشغيل الملف
    $css_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $css_file;
    echo "<p>🔗 رابط CSS: <a href='$css_url' target='_blank'>$css_url</a></p>";
    
    // فحص محتوى الملف
    ob_start();
    $_SESSION['client_id'] = $_SESSION['client_id'] ?? 1; // تأكد من وجود معرف العميل
    include $css_file;
    $css_content = ob_get_clean();
    
    if (!empty($css_content)) {
        echo "<p style='color: green;'>✅ ملف CSS ينتج محتوى (" . strlen($css_content) . " حرف)</p>";
        
        // فحص وجود المتغيرات المخصصة
        if (strpos($css_content, '--custom-primary') !== false) {
            echo "<p style='color: green;'>✅ المتغيرات المخصصة موجودة في CSS</p>";
        } else {
            echo "<p style='color: red;'>❌ المتغيرات المخصصة غير موجودة في CSS</p>";
        }
        
        // عرض جزء من CSS
        echo "<details><summary>عرض أول 1000 حرف من CSS</summary>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; direction: ltr;'>";
        echo htmlspecialchars(substr($css_content, 0, 1000));
        echo "</pre></details>";
    } else {
        echo "<p style='color: red;'>❌ ملف CSS لا ينتج أي محتوى</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف CSS الديناميكي غير موجود</p>";
}

// فحص تضمين CSS في header.php
echo "<h2>4. فحص تضمين CSS في header.php</h2>";
$header_file = 'client/includes/header.php';
if (file_exists($header_file)) {
    $header_content = file_get_contents($header_file);
    
    if (strpos($header_content, 'theme-css.php') !== false) {
        echo "<p style='color: green;'>✅ ملف CSS مضمن في header.php</p>";
        
        // استخراج السطر الذي يحتوي على الرابط
        $lines = explode("\n", $header_content);
        foreach ($lines as $line_num => $line) {
            if (strpos($line, 'theme-css.php') !== false) {
                echo "<p>📍 السطر " . ($line_num + 1) . ": <code>" . htmlspecialchars(trim($line)) . "</code></p>";
                break;
            }
        }
    } else {
        echo "<p style='color: red;'>❌ ملف CSS غير مضمن في header.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}

// فحص صفحة الإعدادات
echo "<h2>5. فحص صفحة الإعدادات</h2>";
$settings_file = 'client/settings.php';
if (file_exists($settings_file)) {
    $settings_content = file_get_contents($settings_file);
    
    if (strpos($settings_content, 'تخصيص المظهر') !== false) {
        echo "<p style='color: green;'>✅ تبويب تخصيص المظهر موجود في صفحة الإعدادات</p>";
    } else {
        echo "<p style='color: red;'>❌ تبويب تخصيص المظهر غير موجود في صفحة الإعدادات</p>";
    }
    
    if (strpos($settings_content, 'update_theme') !== false) {
        echo "<p style='color: green;'>✅ معالج حفظ إعدادات المظهر موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ معالج حفظ إعدادات المظهر غير موجود</p>";
    }
} else {
    echo "<p style='color: red;'>❌ صفحة الإعدادات غير موجودة</p>";
}

// فحص JavaScript
echo "<h2>6. فحص JavaScript</h2>";
$js_file = 'client/assets/js/theme-customizer.js';
if (file_exists($js_file)) {
    echo "<p style='color: green;'>✅ ملف JavaScript موجود</p>";
    
    $js_content = file_get_contents($js_file);
    $functions = ['ThemeCustomizer', 'updateColorPreview', 'saveThemeSettings'];
    
    foreach ($functions as $func) {
        if (strpos($js_content, $func) !== false) {
            echo "<p style='color: green;'>✅ وظيفة $func موجودة</p>";
        } else {
            echo "<p style='color: red;'>❌ وظيفة $func غير موجودة</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ملف JavaScript غير موجود</p>";
}

// اختبار APIs
echo "<h2>7. اختبار APIs</h2>";
$apis = [
    'client/api/get_theme_settings.php' => 'جلب الإعدادات',
    'client/api/save_theme_settings.php' => 'حفظ الإعدادات'
];

foreach ($apis as $api => $description) {
    if (file_exists($api)) {
        echo "<p style='color: green;'>✅ API $description موجود</p>";
        echo "<p>🔗 <a href='$api' target='_blank'>اختبار $api</a></p>";
    } else {
        echo "<p style='color: red;'>❌ API $description غير موجود</p>";
    }
}

// فحص صلاحيات الملفات
echo "<h2>8. فحص صلاحيات الملفات</h2>";
$files_to_check = [
    'client/api/theme-css.php',
    'client/api/get_theme_settings.php',
    'client/api/save_theme_settings.php',
    'client/includes/header.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? '✅' : '❌';
        $writable = is_writable($file) ? '✅' : '❌';
        echo "<p>$file - قراءة: $readable كتابة: $writable</p>";
    }
}

// اختبار متصفح
echo "<h2>9. اختبار المتصفح</h2>";
echo "<div id='browser-test'>";
echo "<p>🔍 فحص دعم المتصفح...</p>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const testDiv = document.getElementById('browser-test');";
echo "    let results = [];";
echo "    ";
echo "    // فحص دعم CSS Variables";
echo "    if (CSS.supports('color', 'var(--test)')) {";
echo "        results.push('✅ المتصفح يدعم CSS Variables');";
echo "    } else {";
echo "        results.push('❌ المتصفح لا يدعم CSS Variables');";
echo "    }";
echo "    ";
echo "    // فحص تحميل CSS المخصص";
echo "    const customCSS = document.querySelector('link[href*=\"theme-css.php\"]');";
echo "    if (customCSS) {";
echo "        results.push('✅ رابط CSS المخصص موجود في الصفحة');";
echo "    } else {";
echo "        results.push('❌ رابط CSS المخصص غير موجود في الصفحة');";
echo "    }";
echo "    ";
echo "    // فحص المتغيرات المخصصة";
echo "    const rootStyles = getComputedStyle(document.documentElement);";
echo "    const primaryColor = rootStyles.getPropertyValue('--custom-primary');";
echo "    if (primaryColor && primaryColor.trim()) {";
echo "        results.push('✅ المتغيرات المخصصة متاحة: ' + primaryColor.trim());";
echo "    } else {";
echo "        results.push('❌ المتغيرات المخصصة غير متاحة');";
echo "    }";
echo "    ";
echo "    testDiv.innerHTML = results.map(r => '<p>' + r + '</p>').join('');";
echo "});";
echo "</script>";

// الخلاصة والحلول
echo "<h2>10. الحلول المقترحة</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<h4>🛠️ خطوات حل المشاكل:</h4>";
echo "<ol>";
echo "<li><strong>امسح ذاكرة التخزين المؤقت:</strong> اضغط Ctrl+F5 أو Cmd+Shift+R</li>";
echo "<li><strong>تحقق من وحدة تحكم المطور:</strong> اضغط F12 وابحث عن أخطاء في تبويب Console</li>";
echo "<li><strong>اختبر CSS مباشرة:</strong> <a href='client/api/theme-css.php' target='_blank'>افتح ملف CSS</a></li>";
echo "<li><strong>جرب صفحة الاختبار:</strong> <a href='client/test-theme.php' target='_blank'>صفحة اختبار المظهر</a></li>";
echo "<li><strong>شغل ملف الإصلاح:</strong> <a href='fix_theme_system.php' target='_blank'>إصلاح النظام</a></li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

details {
    margin: 10px 0;
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
    padding: 5px;
}

code {
    background: #f1f1f1;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    margin: 10px 0;
    padding-right: 20px;
}
</style>
