<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// التحقق من وجود معامل الملف
if (!isset($_GET['file']) || empty($_GET['file'])) {
    die('ملف غير محدد');
}

$filename = basename($_GET['file']);
$backupDir = __DIR__ . '/../backups';
$filePath = $backupDir . '/' . $filename;

// التحقق من وجود الملف وأنه في المجلد الصحيح
if (!file_exists($filePath) || !is_file($filePath)) {
    die('الملف غير موجود');
}

// التحقق من أن الملف هو ملف نسخة احتياطية صالح
if (!preg_match('/^[a-zA-Z0-9_-]+_backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.sql$/', $filename)) {
    die('نوع ملف غير صالح');
}

// تحديد headers للتحميل
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: must-revalidate');
header('Pragma: public');

// قراءة وإرسال الملف
readfile($filePath);
exit();
?>
