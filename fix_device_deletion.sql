-- إصلاح مشكلة حذف الأجهزة - PlayGood
-- تشغيل هذا الملف لإصلاح قيود المرجعية التي تمنع حذف الأجهزة

-- 1. إزالة القيد المرجعي الحالي بين sessions و devices
ALTER TABLE sessions DROP FOREIGN KEY sessions_ibfk_1;

-- 2. تعديل عمود device_id في جدول sessions ليقبل NULL
ALTER TABLE sessions MODIFY COLUMN device_id INT NULL;

-- 3. إضافة قيد مرجعي جديد يسمح بـ SET NULL عند حذف الجهاز
ALTER TABLE sessions 
ADD CONSTRAINT sessions_device_fk 
FOREIGN KEY (device_id) REFERENCES devices(device_id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 4. إضافة فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id);

-- 5. تحديث الجلسات التي لها device_id غير موجود (تنظيف البيانات)
UPDATE sessions 
SET device_id = NULL 
WHERE device_id NOT IN (SELECT device_id FROM devices);

-- 6. إضافة عمود لتتبع اسم الجهاز المحذوف (اختياري)
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS deleted_device_name VARCHAR(100) NULL 
COMMENT 'اسم الجهاز في حالة حذفه';

-- 7. إنشاء trigger لحفظ اسم الجهاز قبل الحذف (اختياري)
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS before_device_delete
BEFORE DELETE ON devices
FOR EACH ROW
BEGIN
    UPDATE sessions 
    SET deleted_device_name = OLD.device_name 
    WHERE device_id = OLD.device_id AND deleted_device_name IS NULL;
END$$
DELIMITER ;

-- 8. إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_client_device ON sessions(client_id, device_id);
CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status);

-- تم الانتهاء من الإصلاحات
SELECT 'تم إصلاح مشكلة حذف الأجهزة بنجاح!' as message;
