<?php
/**
 * إصلاح مشكلة العمود billing_method المفقود
 */

require_once 'config/database.php';

echo "<h1>إصلاح مشكلة العمود billing_method</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات - استخدام الاتصال من ملف الإعدادات
    // $pdo متوفر بالفعل من config/database.php
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // التحقق من وجود الجدول
    echo "<h3>🔍 فحص جدول invoice_settings</h3>";
    
    $table_exists = $pdo->query("SHOW TABLES LIKE 'invoice_settings'")->rowCount();
    
    if ($table_exists == 0) {
        echo "<p style='color: red;'>❌ جدول invoice_settings غير موجود. سيتم إنشاؤه.</p>";
        
        // إنشاء الجدول مع العمود الجديد
        $create_table_sql = "
            CREATE TABLE invoice_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                header_color VARCHAR(7) DEFAULT '#dc3545',
                footer_text TEXT DEFAULT 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
                footer_color VARCHAR(7) DEFAULT '#000000',
                company_address TEXT,
                company_phone VARCHAR(20),
                show_qr_code BOOLEAN DEFAULT TRUE,
                billing_method ENUM('actual_time', 'hourly_rounding', 'first_minute_full_hour') DEFAULT 'actual_time' 
                    COMMENT 'طريقة حساب التكلفة',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_client_settings (client_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول invoice_settings مع العمود billing_method</p>";
        
    } else {
        echo "<p style='color: green;'>✅ جدول invoice_settings موجود</p>";
        
        // فحص الأعمدة الموجودة
        echo "<h3>📋 فحص الأعمدة الموجودة</h3>";
        
        $columns = $pdo->query("DESCRIBE invoice_settings")->fetchAll(PDO::FETCH_ASSOC);
        
        $billing_method_exists = false;
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>" . htmlspecialchars($column['Field']) . " - " . htmlspecialchars($column['Type']) . "</li>";
            if ($column['Field'] === 'billing_method') {
                $billing_method_exists = true;
            }
        }
        echo "</ul>";
        
        if (!$billing_method_exists) {
            echo "<p style='color: orange;'>⚠️ العمود billing_method مفقود. سيتم إضافته.</p>";
            
            // إضافة العمود
            $add_column_sql = "
                ALTER TABLE invoice_settings 
                ADD COLUMN billing_method ENUM('actual_time', 'hourly_rounding', 'first_minute_full_hour') 
                DEFAULT 'actual_time' 
                COMMENT 'طريقة حساب التكلفة'
            ";
            
            $pdo->exec($add_column_sql);
            echo "<p style='color: green;'>✅ تم إضافة العمود billing_method بنجاح</p>";
            
        } else {
            echo "<p style='color: green;'>✅ العمود billing_method موجود</p>";
            
            // التحقق من القيم المسموحة
            $column_info = $pdo->query("SHOW COLUMNS FROM invoice_settings WHERE Field = 'billing_method'")->fetch(PDO::FETCH_ASSOC);
            
            echo "<p><strong>نوع العمود الحالي:</strong> " . htmlspecialchars($column_info['Type']) . "</p>";
            
            // التحقق من وجود القيمة الجديدة
            if (strpos($column_info['Type'], 'first_minute_full_hour') === false) {
                echo "<p style='color: orange;'>⚠️ القيمة 'first_minute_full_hour' مفقودة. سيتم إضافتها.</p>";
                
                // تحديث العمود لإضافة القيمة الجديدة
                $modify_column_sql = "
                    ALTER TABLE invoice_settings 
                    MODIFY COLUMN billing_method ENUM('actual_time', 'hourly_rounding', 'first_minute_full_hour') 
                    DEFAULT 'actual_time' 
                    COMMENT 'طريقة حساب التكلفة'
                ";
                
                $pdo->exec($modify_column_sql);
                echo "<p style='color: green;'>✅ تم تحديث العمود لإضافة القيمة الجديدة</p>";
            } else {
                echo "<p style='color: green;'>✅ جميع القيم موجودة في العمود</p>";
            }
        }
    }
    
    // عرض الهيكل النهائي للجدول
    echo "<h3>📋 الهيكل النهائي للجدول</h3>";
    
    $final_columns = $pdo->query("DESCRIBE invoice_settings")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
    echo "<thead>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>اسم العمود</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>النوع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>القيمة الافتراضية</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>ملاحظات</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
        
        if ($column['Field'] === 'billing_method') {
            echo "<strong style='color: green;'>العمود المطلوب لطرق الحساب</strong>";
        } else {
            echo htmlspecialchars($column['Comment'] ?? '-');
        }
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // اختبار إدراج بيانات تجريبية
    echo "<h3>🧪 اختبار العمود الجديد</h3>";
    
    try {
        // محاولة إدراج إعدادات تجريبية
        $test_client_id = 999; // معرف تجريبي
        
        // حذف أي إعدادات تجريبية سابقة
        $pdo->prepare("DELETE FROM invoice_settings WHERE client_id = ?")->execute([$test_client_id]);
        
        // إدراج إعدادات تجريبية لكل طريقة حساب
        $test_methods = ['actual_time', 'hourly_rounding', 'first_minute_full_hour'];
        
        foreach ($test_methods as $index => $method) {
            $test_id = $test_client_id + $index;
            
            $insert_sql = "
                INSERT INTO invoice_settings (
                    client_id, 
                    header_color, 
                    footer_text, 
                    billing_method
                ) VALUES (?, ?, ?, ?)
            ";
            
            $pdo->prepare($insert_sql)->execute([
                $test_id,
                '#007bff',
                "اختبار طريقة $method",
                $method
            ]);
        }
        
        echo "<p style='color: green;'>✅ تم إدراج بيانات تجريبية بنجاح</p>";
        
        // عرض البيانات التجريبية
        $test_data = $pdo->query("
            SELECT client_id, billing_method, footer_text 
            FROM invoice_settings 
            WHERE client_id >= $test_client_id 
            ORDER BY client_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>البيانات التجريبية المدرجة:</h4>";
        echo "<ul>";
        foreach ($test_data as $row) {
            echo "<li>العميل {$row['client_id']}: {$row['billing_method']} - {$row['footer_text']}</li>";
        }
        echo "</ul>";
        
        // حذف البيانات التجريبية
        $pdo->prepare("DELETE FROM invoice_settings WHERE client_id >= ?")->execute([$test_client_id]);
        echo "<p style='color: blue;'>ℹ️ تم حذف البيانات التجريبية</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار العمود: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>✅ تم إصلاح المشكلة بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>العمود billing_method جاهز للاستخدام الآن.</strong></p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4>📝 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>انتقل إلى <a href='client/invoice_settings.php' target='_blank' style='color: #007bff;'>صفحة إعدادات الفاتورة</a></li>";
    echo "<li>اختر طريقة الحساب المفضلة</li>";
    echo "<li>احفظ الإعدادات</li>";
    echo "<li>اختبر النظام بإنشاء جلسة جديدة</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
    
    // محاولة إنشاء الجدول من الصفر
    echo "<h3>🔧 محاولة إنشاء الجدول من الصفر</h3>";
    
    try {
        $create_table_sql = "
            CREATE TABLE IF NOT EXISTS invoice_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                header_color VARCHAR(7) DEFAULT '#dc3545',
                footer_text TEXT DEFAULT 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
                footer_color VARCHAR(7) DEFAULT '#000000',
                company_address TEXT,
                company_phone VARCHAR(20),
                show_qr_code BOOLEAN DEFAULT TRUE,
                billing_method ENUM('actual_time', 'hourly_rounding', 'first_minute_full_hour') DEFAULT 'actual_time' 
                    COMMENT 'طريقة حساب التكلفة',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_client_settings (client_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء الجدول بنجاح</p>";
        
    } catch (PDOException $e2) {
        echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: " . $e2->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    border-collapse: collapse;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}
</style>
