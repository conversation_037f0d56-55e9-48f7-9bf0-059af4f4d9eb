<?php
/**
 * ملف الدوال المساعدة لتنسيق البيانات
 * يحتوي على دوال مشتركة لتنسيق المبالغ والتواريخ والنصوص
 */

/**
 * تنسيق المبالغ المالية بشكل موحد
 * @param mixed $amount المبلغ المراد تنسيقه
 * @param int $decimals عدد الخانات العشرية (افتراضي: 2)
 * @return string المبلغ منسق
 */
function formatAmount($amount, $decimals = 2) {
    // التحقق من أن القيمة رقمية
    if (!is_numeric($amount)) {
        // محاولة تحويل النص إلى رقم
        $amount = preg_replace('/[^0-9.-]/', '', $amount);
        if (!is_numeric($amount)) {
            return '0.00';
        }
    }
    
    // تحويل إلى رقم عشري
    $numericAmount = floatval($amount);
    
    // تنسيق الرقم
    return number_format($numericAmount, $decimals);
}

/**
 * تنسيق المبالغ مع العملة
 * @param mixed $amount المبلغ المراد تنسيقه
 * @param string $currency رمز العملة (افتراضي: ج.م)
 * @param int $decimals عدد الخانات العشرية (افتراضي: 2)
 * @return string المبلغ منسق مع العملة
 */
function formatAmountWithCurrency($amount, $currency = 'ج.م', $decimals = 2) {
    return formatAmount($amount, $decimals) . ' ' . $currency;
}

/**
 * تنسيق التاريخ بالتنسيق العربي
 * @param string $date التاريخ المراد تنسيقه
 * @param string $format تنسيق التاريخ (افتراضي: d/m/Y)
 * @return string التاريخ منسق
 */
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date) || $date === '0000-00-00') {
        return 'غير محدد';
    }
    
    try {
        return date($format, strtotime($date));
    } catch (Exception $e) {
        return 'تاريخ غير صحيح';
    }
}

/**
 * تنسيق التاريخ والوقت بالتنسيق العربي
 * @param string $datetime التاريخ والوقت المراد تنسيقه
 * @param string $format تنسيق التاريخ والوقت (افتراضي: d/m/Y H:i)
 * @return string التاريخ والوقت منسق
 */
function formatDateTime($datetime, $format = 'd/m/Y H:i') {
    if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
        return 'غير محدد';
    }
    
    try {
        return date($format, strtotime($datetime));
    } catch (Exception $e) {
        return 'تاريخ غير صحيح';
    }
}

/**
 * تنسيق النص الطويل مع قطع ذكي
 * @param string $text النص المراد تنسيقه
 * @param int $length الطول الأقصى (افتراضي: 50)
 * @param string $suffix النص المضاف في النهاية (افتراضي: ...)
 * @return string النص منسق
 */
function truncateText($text, $length = 50, $suffix = '...') {
    if (empty($text)) {
        return 'غير محدد';
    }
    
    $text = trim($text);
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * تنسيق رقم الهاتف
 * @param string $phone رقم الهاتف
 * @return string رقم الهاتف منسق
 */
function formatPhone($phone) {
    if (empty($phone)) {
        return 'غير محدد';
    }
    
    // إزالة جميع الرموز غير الرقمية
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // تنسيق الرقم حسب الطول
    if (strlen($phone) == 11 && substr($phone, 0, 2) == '01') {
        // رقم مصري
        return substr($phone, 0, 4) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
    }
    
    return $phone;
}

/**
 * تحويل حالة النشاط إلى نص عربي
 * @param mixed $status حالة النشاط
 * @return string النص العربي للحالة
 */
function formatStatus($status) {
    switch (strtolower($status)) {
        case 'active':
        case '1':
        case 1:
        case true:
            return 'نشط';
        case 'inactive':
        case '0':
        case 0:
        case false:
            return 'غير نشط';
        case 'pending':
            return 'في الانتظار';
        case 'completed':
            return 'مكتمل';
        case 'cancelled':
            return 'ملغي';
        case 'paid':
            return 'مدفوع';
        default:
            return 'غير محدد';
    }
}

/**
 * تنسيق النسبة المئوية
 * @param mixed $value القيمة
 * @param mixed $total المجموع الكلي
 * @param int $decimals عدد الخانات العشرية (افتراضي: 1)
 * @return string النسبة المئوية منسقة
 */
function formatPercentage($value, $total, $decimals = 1) {
    if ($total == 0) {
        return '0%';
    }
    
    $percentage = ($value / $total) * 100;
    return number_format($percentage, $decimals) . '%';
}

/**
 * تنسيق حجم الملف
 * @param int $bytes حجم الملف بالبايت
 * @param int $decimals عدد الخانات العشرية (افتراضي: 2)
 * @return string حجم الملف منسق
 */
function formatFileSize($bytes, $decimals = 2) {
    $size = array('B', 'KB', 'MB', 'GB', 'TB');
    $factor = floor((strlen($bytes) - 1) / 3);
    
    return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . @$size[$factor];
}

/**
 * تنسيق المدة الزمنية
 * @param int $minutes المدة بالدقائق
 * @return string المدة منسقة
 */
function formatDuration($minutes) {
    if ($minutes < 60) {
        return $minutes . ' دقيقة';
    }
    
    $hours = floor($minutes / 60);
    $remainingMinutes = $minutes % 60;
    
    if ($remainingMinutes == 0) {
        return $hours . ' ساعة';
    }
    
    return $hours . ' ساعة و ' . $remainingMinutes . ' دقيقة';
}

/**
 * تنظيف وتنسيق النص للعرض الآمن
 * @param string $text النص المراد تنظيفه
 * @param bool $allowHtml السماح بـ HTML (افتراضي: false)
 * @return string النص منظف
 */
function cleanText($text, $allowHtml = false) {
    if (empty($text)) {
        return '';
    }
    
    if (!$allowHtml) {
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
    
    return trim($text);
}
?>
