<?php
/**
 * اختبار حذف المنتجات من الجلسات
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار حذف المنتجات من الجلسات</h1>";
echo "<hr>";

try {
    echo "<h2>1. إنشاء بيانات تجريبية للاختبار</h2>";
    
    // إنشاء جلسة تجريبية
    $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
    $device = $stmt->fetch();
    
    if (!$device) {
        // إنشاء جهاز تجريبي
        $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, single_rate, multi_rate, status, client_id) 
                   VALUES ('جهاز تجريبي للحذف', 'PS5', 15.00, 10.00, 20.00, 'available', 1)");
        $device_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جهاز تجريبي: $device_id</p>";
    } else {
        $device_id = $device['device_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام جهاز موجود: $device_id</p>";
    }
    
    // إنشاء جلسة تجريبية
    $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
    $stmt->execute([$device_id, 1, 1]);
    $session_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    
    // إنشاء منتجات تجريبية
    $test_products = [
        ['منتج للحذف 1', 5.00, 'اختبار'],
        ['منتج للحذف 2', 3.50, 'اختبار'],
        ['منتج للحذف 3', 7.00, 'اختبار']
    ];
    
    $product_ids = [];
    foreach ($test_products as $product) {
        $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([$product[0], $product[1], $product[2], 1]);
        $product_ids[] = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء منتج: {$product[0]} - ID: " . end($product_ids) . "</p>";
    }
    
    // إضافة المنتجات للجلسة
    foreach ($product_ids as $index => $product_id) {
        $quantity = $index + 1; // كميات مختلفة
        $price = $test_products[$index][1];
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $quantity = quantity; $unit_price = price; $total_price = $unit_price * $quantity; $stmt->execute([session_id, product_id, $quantity, $unit_price, $total_price]);
        echo "<p style='color: green;'>✅ تم إضافة منتج للجلسة: {$test_products[$index][0]} × $quantity</p>";
    }
    
    echo "<h2>2. عرض المنتجات المضافة للجلسة</h2>";
    
    $stmt = $pdo->prepare("
        SELECT 
            sp.id as session_product_id,
            sp.product_id,
            sp.quantity,
            sp.price,
            ci.name as product_name,
            (sp.quantity * sp.price) as total
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        WHERE sp.session_id = ?
        ORDER BY sp.created_at DESC
    ");
    $stmt->execute([$session_id]);
    $session_products = $stmt->fetchAll();
    
    if (count($session_products) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th><th>إجراءات</th></tr>";
        
        foreach ($session_products as $product) {
            echo "<tr>";
            echo "<td>{$product['session_product_id']}</td>";
            echo "<td>{$product['product_name']}</td>";
            echo "<td>{$product['quantity']}</td>";
            echo "<td>{$product['price']} ج.م</td>";
            echo "<td>{$product['total']} ج.م</td>";
            echo "<td>";
            echo "<button onclick=\"testDeleteProduct({$session_id}, {$product['product_id']})\" style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>";
            echo "<i class='fas fa-trash'></i> حذف";
            echo "</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $total_cost = array_sum(array_column($session_products, 'total'));
        echo "<p><strong>إجمالي تكلفة المنتجات: " . number_format($total_cost, 2) . " ج.م</strong></p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات في الجلسة</p>";
    }
    
    echo "<h2>3. اختبار API الحذف</h2>";
    
    if (count($session_products) > 0) {
        $first_product = $session_products[0];
        echo "<p>سنختبر حذف المنتج: <strong>{$first_product['product_name']}</strong></p>";
        echo "<p>معرف الجلسة: <strong>$session_id</strong></p>";
        echo "<p>معرف المنتج: <strong>{$first_product['product_id']}</strong></p>";
        
        echo "<button onclick=\"testDeleteAPI({$session_id}, {$first_product['product_id']})\" style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px;'>";
        echo "<i class='fas fa-test'></i> اختبار API الحذف";
        echo "</button>";
        
        echo "<button onclick=\"refreshProducts()\" style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px;'>";
        echo "<i class='fas fa-refresh'></i> تحديث القائمة";
        echo "</button>";
    }
    
    echo "<div id='test-results' style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;'></div>";
    
    echo "<h2>4. اختبار JavaScript</h2>";
    ?>
    
    <div id="js-test-area" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white;">
        <h3>محاكاة modal تعديل الجلسة:</h3>
        <div id="mock_session_products" style="border: 1px solid #eee; border-radius: 5px; padding: 15px; min-height: 200px;">
            <!-- سيتم تحميل المنتجات هنا -->
        </div>
        <button onclick="loadMockProducts()" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px;">
            <i class="fas fa-refresh"></i> تحميل المنتجات
        </button>
    </div>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    
    function testDeleteProduct(sessionId, productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري الحذف...</p>';
            
            fetch('client/api/delete_session_product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    product_id: productId
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(responseText => {
                console.log('Raw response:', responseText);
                
                try {
                    const data = JSON.parse(responseText);
                    console.log('Parsed data:', data);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <p style="color: green;"><strong>✅ تم الحذف بنجاح!</strong></p>
                            <p><strong>الرسالة:</strong> ${data.message}</p>
                            <p><strong>التكلفة الجديدة:</strong> ${data.total_cost} ج.م</p>
                            <p><strong>عدد الصفوف المحذوفة:</strong> ${data.deleted_rows}</p>
                        `;
                        
                        // تحديث الصفحة بعد 2 ثانية
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        resultDiv.innerHTML = `
                            <p style="color: red;"><strong>❌ فشل الحذف:</strong> ${data.error}</p>
                        `;
                    }
                } catch (jsonError) {
                    resultDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ خطأ في JSON:</strong> ${jsonError.message}</p>
                        <details>
                            <summary>الاستجابة الخام</summary>
                            <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<p style="color: red;"><strong>❌ خطأ في الشبكة:</strong> ${error.message}</p>`;
            });
        }
    }
    
    function testDeleteAPI(sessionId, productId) {
        testDeleteProduct(sessionId, productId);
    }
    
    function refreshProducts() {
        location.reload();
    }
    
    function loadMockProducts() {
        const container = document.getElementById('mock_session_products');
        container.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري تحميل المنتجات...</p></div>';
        
        fetch(`client/api/get_session_products.php?session_id=${testSessionId}`)
            .then(response => response.text())
            .then(responseText => {
                try {
                    const data = JSON.parse(responseText);
                    
                    if (data.success && data.products.length > 0) {
                        const totalCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);
                        
                        container.innerHTML = `
                            <div style="border: 1px solid #eee; border-radius: 3px;">
                                ${data.products.map(product => `
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #f0f0f0;">
                                        <div>
                                            <h6 style="margin: 0; font-weight: bold;">${product.product_name}</h6>
                                            <small style="color: #666;">${product.quantity} × ${product.price} ج.م</small>
                                        </div>
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-left: 10px; font-weight: bold; color: #007bff;">${product.total} ج.م</span>
                                            <button onclick="testDeleteProduct(${testSessionId}, ${product.product_id})" style="background: #dc3545; color: white; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer;" title="حذف المنتج">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                    <span style="font-weight: bold; color: #007bff;">${totalCost.toFixed(2)} ج.م</span>
                                </div>
                            </div>
                        `;
                    } else {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                <i class="fas fa-coffee" style="font-size: 2em; color: #ccc; margin-bottom: 10px;"></i>
                                <p style="color: #666; margin: 0;">لا توجد منتجات مضافة</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    container.innerHTML = `
                        <div style="color: red; padding: 15px;">
                            <strong>خطأ في تحميل المنتجات:</strong> ${error.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                container.innerHTML = `
                    <div style="color: red; padding: 15px;">
                        <strong>خطأ في الشبكة:</strong> ${error.message}
                    </div>
                `;
            });
    }
    
    // تحميل المنتجات تلقائياً
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(loadMockProducts, 1000);
    });
    </script>
    
    <?php
    
    echo "<h2>5. تنظيف البيانات التجريبية</h2>";
    echo "<p>سيتم حذف البيانات التجريبية عند إغلاق الصفحة أو بعد الانتهاء من الاختبار.</p>";
    echo "<button onclick=\"cleanupTestData()\" style='background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
    echo "<i class='fas fa-trash'></i> حذف البيانات التجريبية الآن";
    echo "</button>";
    
    ?>
    <script>
    function cleanupTestData() {
        if (confirm('هل أنت متأكد من حذف جميع البيانات التجريبية؟')) {
            fetch('cleanup_test_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: <?php echo $session_id; ?>,
                    product_ids: <?php echo json_encode($product_ids); ?>
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حذف البيانات التجريبية بنجاح');
                    location.reload();
                } else {
                    alert('خطأ في حذف البيانات: ' + data.error);
                }
            })
            .catch(error => {
                alert('خطأ: ' + error.message);
            });
        }
    }
    
    // حذف البيانات التجريبية عند إغلاق الصفحة
    window.addEventListener('beforeunload', function() {
        navigator.sendBeacon('cleanup_test_data.php', JSON.stringify({
            session_id: <?php echo $session_id; ?>,
            product_ids: <?php echo json_encode($product_ids); ?>
        }));
    });
    </script>
    <?php

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
table {
    font-family: Arial, sans-serif;
    font-size: 14px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    font-weight: bold;
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
