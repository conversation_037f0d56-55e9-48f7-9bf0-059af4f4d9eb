/**
 * Arabic PDF Export Enhancement
 * تحسينات تصدير PDF مع دعم اللغة العربية
 * PlayGood System
 */

// إعدادات التصدير
const PDF_EXPORT_CONFIG = {
    // إعدادات html2canvas
    html2canvas: {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        letterRendering: true,
        foreignObjectRendering: true
    },
    
    // إعدادات jsPDF
    jsPDF: {
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true,
        floatPrecision: 16
    },
    
    // إعدادات الصفحة
    page: {
        width: 210, // A4 width in mm
        height: 297, // A4 height in mm
        margin: 10
    }
};

/**
 * تحسين جودة النص العربي في PDF
 */
function optimizeArabicText() {
    const arabicElements = document.querySelectorAll('[lang="ar"], .arabic-text, body');
    
    arabicElements.forEach(element => {
        // تحسين خصائص النص
        element.style.fontFamily = '"Segoe UI", "Tahoma", "Arial", sans-serif';
        element.style.fontWeight = 'normal';
        element.style.textRendering = 'optimizeLegibility';
        element.style.webkitFontSmoothing = 'antialiased';
        element.style.mozOsxFontSmoothing = 'grayscale';
    });
}

/**
 * تحضير الصفحة للتصدير
 */
function preparePageForExport() {
    return new Promise((resolve) => {
        // إخفاء العناصر غير المرغوب فيها
        const elementsToHide = document.querySelectorAll('.no-print, .btn-toolbar, .export-buttons, .dropdown-menu');
        elementsToHide.forEach(el => {
            el.style.display = 'none';
            el.setAttribute('data-hidden-for-export', 'true');
        });
        
        // تحسين النصوص العربية
        optimizeArabicText();
        
        // إضافة كلاس للتصدير
        document.body.classList.add('pdf-export-ready');
        
        // انتظار قصير للتأكد من تطبيق التغييرات
        setTimeout(resolve, 100);
    });
}

/**
 * استعادة الصفحة بعد التصدير
 */
function restorePageAfterExport() {
    // إظهار العناصر المخفية
    const hiddenElements = document.querySelectorAll('[data-hidden-for-export="true"]');
    hiddenElements.forEach(el => {
        el.style.display = '';
        el.removeAttribute('data-hidden-for-export');
    });
    
    // إزالة كلاس التصدير
    document.body.classList.remove('pdf-export-ready');
}

/**
 * تصدير PDF محسن مع دعم اللغة العربية
 */
async function exportEnhancedArabicPDF() {
    try {
        // عرض مؤشر التحميل
        Swal.fire({
            title: 'جاري إنشاء التقرير...',
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>يتم الآن تحضير التقرير بجودة عالية</p>
                    <small class="text-muted">قد يستغرق هذا بضع ثوانٍ...</small>
                </div>
            `,
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });
        
        // تحضير الصفحة
        await preparePageForExport();
        
        // الحصول على المحتوى المراد تصديره
        const reportContent = document.querySelector('.container-fluid');
        if (!reportContent) {
            throw new Error('لم يتم العثور على محتوى التقرير');
        }
        
        // تحويل إلى canvas
        const canvas = await html2canvas(reportContent, PDF_EXPORT_CONFIG.html2canvas);
        
        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF(PDF_EXPORT_CONFIG.jsPDF);
        
        const imgData = canvas.toDataURL('image/png', 1.0);
        const imgWidth = PDF_EXPORT_CONFIG.page.width - (PDF_EXPORT_CONFIG.page.margin * 2);
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        const pageHeight = PDF_EXPORT_CONFIG.page.height - (PDF_EXPORT_CONFIG.page.margin * 2);
        
        let heightLeft = imgHeight;
        let position = PDF_EXPORT_CONFIG.page.margin;
        
        // إضافة الصفحة الأولى
        doc.addImage(imgData, 'PNG', PDF_EXPORT_CONFIG.page.margin, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
        
        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight + PDF_EXPORT_CONFIG.page.margin;
            doc.addPage();
            doc.addImage(imgData, 'PNG', PDF_EXPORT_CONFIG.page.margin, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }
        
        // إضافة معلومات إضافية في الفوتر
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setTextColor(128);
            
            // تاريخ الإنشاء
            const currentDate = new Date().toLocaleDateString('ar-EG');
            doc.text(`تاريخ الإنشاء: ${currentDate}`, PDF_EXPORT_CONFIG.page.margin, PDF_EXPORT_CONFIG.page.height - 5);
            
            // رقم الصفحة
            doc.text(`صفحة ${i} من ${pageCount}`, PDF_EXPORT_CONFIG.page.width - PDF_EXPORT_CONFIG.page.margin - 20, PDF_EXPORT_CONFIG.page.height - 5);
        }
        
        // حفظ الملف
        const fileName = `تقرير_PlayGood_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
        
        // استعادة الصفحة
        restorePageAfterExport();
        
        // إغلاق مؤشر التحميل
        Swal.close();
        
        // رسالة نجاح
        Swal.fire({
            icon: 'success',
            title: 'تم إنشاء التقرير بنجاح!',
            html: `
                <div class="text-center">
                    <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                    <p>تم تحميل ملف PDF بجودة عالية</p>
                    <small class="text-muted">الملف: ${fileName}</small>
                </div>
            `,
            timer: 3000,
            showConfirmButton: false
        });
        
    } catch (error) {
        console.error('خطأ في إنشاء PDF:', error);
        
        // استعادة الصفحة في حالة الخطأ
        restorePageAfterExport();
        
        // إغلاق مؤشر التحميل
        Swal.close();
        
        // رسالة خطأ
        Swal.fire({
            icon: 'error',
            title: 'خطأ في إنشاء التقرير',
            html: `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <p>حدث خطأ أثناء إنشاء ملف PDF</p>
                    <small class="text-muted">${error.message}</small>
                </div>
            `,
            confirmButtonText: 'موافق',
            confirmButtonColor: '#dc3545'
        });
    }
}

/**
 * تصدير PDF بسيط (نص إنجليزي)
 */
function exportSimplePDF() {
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        
        // إعداد الخط
        doc.setFont('helvetica');
        
        // العنوان
        doc.setFontSize(20);
        doc.text('PlayGood - Statistics Report', 105, 20, { align: 'center' });
        
        // التاريخ
        doc.setFontSize(12);
        const currentDate = new Date().toLocaleDateString('en-US');
        doc.text(`Generated on: ${currentDate}`, 105, 35, { align: 'center' });
        
        // خط فاصل
        doc.line(20, 45, 190, 45);
        
        // المحتوى (سيتم إضافة المزيد حسب البيانات المتوفرة)
        let yPos = 60;
        doc.setFontSize(14);
        doc.text('Report Summary:', 20, yPos);
        
        yPos += 20;
        doc.setFontSize(11);
        doc.text('This report contains statistical data for the selected period.', 20, yPos);
        yPos += 10;
        doc.text('All amounts are in Egyptian Pounds (EGP).', 20, yPos);
        
        // حفظ الملف
        const fileName = `PlayGood_Report_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
        
        // رسالة نجاح
        Swal.fire({
            icon: 'success',
            title: 'PDF Created Successfully!',
            text: 'Simple PDF report has been downloaded',
            timer: 2000,
            showConfirmButton: false
        });
        
    } catch (error) {
        console.error('Error creating simple PDF:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to create PDF report',
            confirmButtonText: 'OK'
        });
    }
}

// تصدير الوظائف للاستخدام العام
window.exportEnhancedArabicPDF = exportEnhancedArabicPDF;
window.exportSimplePDF = exportSimplePDF;
