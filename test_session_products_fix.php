<?php
// اختبار إصلاح عرض منتجات الجلسة
session_start();
require_once 'config/database.php';

// محاكاة تسجيل الدخول
$_SESSION['client_id'] = 1;

echo "<h1>اختبار إصلاح عرض منتجات الجلسة</h1>";

try {
    // البحث عن جلسة نشطة أو إنشاؤها
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $stmt = $pdo->prepare("INSERT INTO sessions (device_id, customer_name, start_time, status) VALUES (1, 'عميل تجريبي', NOW(), 'active')");
        $stmt->execute();
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام الجلسة الموجودة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاؤه
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        // إنشاء منتج تجريبي
        $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('شاي تجريبي', 5.50, 'مشروبات', 1)");
        $stmt->execute();
        $product_id = $pdo->lastInsertId();
        $product_name = 'شاي تجريبي';
        $product_price = 5.50;
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_name ($product_price ج.م)</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: blue;'>ℹ️ استخدام المنتج الموجود: $product_name ($product_price ج.م)</p>";
    }
    
    // إضافة المنتج للجلسة
    $quantity = 2;
    $unit_price = $product_price;
    $total_price = $unit_price * $quantity;
    
    // حذف أي منتجات سابقة لهذا الاختبار
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ? AND product_id = ?")->execute([$session_id, $product_id]);
    
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);
    
    $test_product_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ تم إضافة المنتج للجلسة: $quantity × $unit_price = $total_price ج.م</p>";
    
    echo "<h2>اختبار API جلب منتجات الجلسة</h2>";
    
    // اختبار API
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $session_id;
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . session_name() . '=' . session_id()
        ]
    ]);
    
    $response = file_get_contents($api_url, false, $context);
    $data = json_decode($response, true);
    
    echo "<h3>استجابة API:</h3>";
    echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
    if ($data && $data['success'] && isset($data['products']) && count($data['products']) > 0) {
        echo "<h3>تحليل البيانات:</h3>";
        
        foreach ($data['products'] as $product) {
            $price_display = $product['price'];
            $total_display = $product['total'];
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>{$product['product_name']}</h4>";
            echo "<p><strong>الكمية:</strong> {$product['quantity']}</p>";
            echo "<p><strong>السعر:</strong> {$price_display} ج.م</p>";
            echo "<p><strong>الإجمالي:</strong> {$total_display} ج.م</p>";
            
            // التحقق من صحة البيانات
            if ($price_display > 0 && $total_display > 0) {
                echo "<p style='color: green;'>✅ البيانات صحيحة</p>";
            } else {
                echo "<p style='color: red;'>❌ البيانات غير صحيحة (السعر أو الإجمالي = 0)</p>";
            }
            echo "</div>";
        }
        
        // اختبار JavaScript
        echo "<h3>اختبار عرض JavaScript:</h3>";
        echo "<div id='js-display'></div>";
        
        echo "<script>
            const products = " . json_encode($data['products']) . ";
            let html = '';
            
            products.forEach(product => {
                html += `
                    <div style='border: 1px solid #eee; padding: 10px; margin: 5px 0; border-radius: 3px;'>
                        <div style='display: flex; justify-content: space-between; align-items: center;'>
                            <div>
                                <h6 style='margin: 0;'>\${product.product_name}</h6>
                                <small style='color: #666;'>\${product.quantity} × \${product.price} ج.م</small>
                            </div>
                            <div>
                                <span style='font-weight: bold; color: #007bff;'>\${product.total} ج.م</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('js-display').innerHTML = html;
        </script>";
        
    } else {
        echo "<p style='color: red;'>❌ فشل في جلب البيانات أو البيانات فارغة</p>";
    }
    
    // تنظيف البيانات التجريبية
    echo "<h3>تنظيف البيانات التجريبية:</h3>";
    $pdo->prepare("DELETE FROM session_products WHERE id = ?")->execute([$test_product_id]);
    echo "<p style='color: blue;'>ℹ️ تم حذف المنتج التجريبي من الجلسة</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
