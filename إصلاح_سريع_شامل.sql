-- =====================================================
-- سكريبت الإصلاح السريع الشامل - Station System
-- =====================================================
-- هذا السكريبت يصلح المشاكل الأساسية في قاعدة البيانات الحالية
-- دون الحاجة لإعادة إنشاء القاعدة من الصفر
-- =====================================================

USE `station`;

-- بدء المعاملة
START TRANSACTION;

SELECT '🚀 بدء عملية الإصلاح السريع الشامل...' AS status;

-- =====================================================
-- 1. إصلاح جدول cafeteria_items
-- =====================================================

-- إضافة العمود category إذا لم يكن موجوداً
SET @category_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'cafeteria_items' AND column_name = 'category');

SET @sql1 = IF(@category_col = 0, 
    'ALTER TABLE cafeteria_items ADD COLUMN category varchar(100) DEFAULT NULL AFTER price',
    'SELECT "✅ عمود category موجود" AS msg');
PREPARE stmt1 FROM @sql1; EXECUTE stmt1; DEALLOCATE PREPARE stmt1;

-- إضافة العمود client_id إذا لم يكن موجوداً
SET @client_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'cafeteria_items' AND column_name = 'client_id');

SET @sql2 = IF(@client_col = 0, 
    'ALTER TABLE cafeteria_items ADD COLUMN client_id int(11) NOT NULL DEFAULT 1 AFTER id',
    'SELECT "✅ عمود client_id موجود" AS msg');
PREPARE stmt2 FROM @sql2; EXECUTE stmt2; DEALLOCATE PREPARE stmt2;

-- إضافة أعمدة المخزون المحسنة
SET @stock_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'cafeteria_items' AND column_name = 'stock_quantity');

SET @sql3 = IF(@stock_col = 0, 
    'ALTER TABLE cafeteria_items 
     ADD COLUMN stock_quantity int(11) DEFAULT 0 AFTER price,
     ADD COLUMN min_stock_level int(11) DEFAULT 5 AFTER stock_quantity,
     ADD COLUMN max_stock_level int(11) DEFAULT 100 AFTER min_stock_level,
     ADD COLUMN cost_price decimal(10,2) DEFAULT 0.00 AFTER price,
     ADD COLUMN status enum("available","low_stock","out_of_stock","discontinued") DEFAULT "available" AFTER description',
    'SELECT "✅ أعمدة المخزون موجودة" AS msg');
PREPARE stmt3 FROM @sql3; EXECUTE stmt3; DEALLOCATE PREPARE stmt3;

-- =====================================================
-- 2. إنشاء/إصلاح جدول categories
-- =====================================================

CREATE TABLE IF NOT EXISTS `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-tag',
  `color` varchar(7) DEFAULT '#007bff',
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`category_id`),
  INDEX `idx_category_client` (`client_id`),
  INDEX `idx_category_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة تصنيفات افتراضية
INSERT IGNORE INTO categories (name, description, client_id) VALUES
('مشروبات ساخنة', 'القهوة والشاي والمشروبات الساخنة', 1),
('مشروبات باردة', 'العصائر والمشروبات الغازية', 1),
('وجبات خفيفة', 'السناكس والمقبلات', 1),
('حلويات', 'الكيك والحلويات', 1),
('وجبات رئيسية', 'الوجبات الأساسية', 1),
('آيس كريم', 'أنواع الآيس كريم المختلفة', 1);

-- =====================================================
-- 3. إصلاح جدول clients
-- =====================================================

-- إضافة أعمدة مهمة لجدول العملاء
SET @business_name_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'clients' AND column_name = 'business_name');

SET @sql4 = IF(@business_name_col = 0, 
    'ALTER TABLE clients 
     ADD COLUMN business_name varchar(200) DEFAULT NULL AFTER client_id,
     ADD COLUMN owner_name varchar(100) DEFAULT NULL AFTER business_name,
     ADD COLUMN subscription_plan enum("basic","premium","enterprise") DEFAULT "basic" AFTER password,
     ADD COLUMN is_active tinyint(1) DEFAULT 1 AFTER subscription_plan',
    'SELECT "✅ أعمدة clients محسنة" AS msg');
PREPARE stmt4 FROM @sql4; EXECUTE stmt4; DEALLOCATE PREPARE stmt4;

-- =====================================================
-- 4. إصلاح جدول devices
-- =====================================================

-- إضافة أعمدة محسنة للأجهزة
SET @device_type_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'devices' AND column_name = 'device_type');

SET @sql5 = IF(@device_type_col = 0, 
    'ALTER TABLE devices 
     ADD COLUMN device_type enum("PS4","PS5","Xbox","PC","Nintendo") DEFAULT "PS4" AFTER device_name,
     ADD COLUMN single_rate decimal(10,2) DEFAULT NULL AFTER hourly_rate,
     ADD COLUMN multi_rate decimal(10,2) DEFAULT NULL AFTER single_rate',
    'SELECT "✅ أعمدة devices محسنة" AS msg');
PREPARE stmt5 FROM @sql5; EXECUTE stmt5; DEALLOCATE PREPARE stmt5;

-- =====================================================
-- 5. إصلاح جدول sessions
-- =====================================================

-- إضافة أعمدة محسنة للجلسات
SET @session_type_col = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'sessions' AND column_name = 'session_type');

SET @sql6 = IF(@session_type_col = 0, 
    'ALTER TABLE sessions 
     ADD COLUMN session_type enum("hourly","single","multi") DEFAULT "hourly" AFTER customer_id,
     ADD COLUMN game_type varchar(100) DEFAULT NULL AFTER session_type,
     ADD COLUMN players_count int(11) DEFAULT 1 AFTER game_type,
     ADD COLUMN products_cost decimal(10,2) DEFAULT 0.00 AFTER time_cost',
    'SELECT "✅ أعمدة sessions محسنة" AS msg');
PREPARE stmt6 FROM @sql6; EXECUTE stmt6; DEALLOCATE PREPARE stmt6;

-- =====================================================
-- 6. إنشاء جداول مفقودة مهمة
-- =====================================================

-- جدول منتجات الجلسة
CREATE TABLE IF NOT EXISTS `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_session_product_session` (`session_id`),
  INDEX `idx_session_product_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الموظفين
CREATE TABLE IF NOT EXISTS `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner','technician') NOT NULL DEFAULT 'cashier',
  `salary` decimal(10,2) NOT NULL DEFAULT 0.00,
  `hire_date` date NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_employee_client` (`client_id`),
  INDEX `idx_employee_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. إضافة الفهارس المحسنة
-- =====================================================

-- فهارس cafeteria_items
CREATE INDEX IF NOT EXISTS idx_cafeteria_category ON cafeteria_items(category);
CREATE INDEX IF NOT EXISTS idx_cafeteria_client ON cafeteria_items(client_id);
CREATE INDEX IF NOT EXISTS idx_cafeteria_status ON cafeteria_items(status);

-- فهارس devices
CREATE INDEX IF NOT EXISTS idx_device_client ON devices(client_id);
CREATE INDEX IF NOT EXISTS idx_device_status ON devices(status);

-- فهارس sessions
CREATE INDEX IF NOT EXISTS idx_session_client ON sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_session_device ON sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_session_date ON sessions(start_time);

-- فهارس customers
CREATE INDEX IF NOT EXISTS idx_customer_client ON customers(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_phone ON customers(phone);

-- =====================================================
-- 8. تحديث البيانات الموجودة
-- =====================================================

-- تحديث منتجات الكافتيريا بتصنيفات افتراضية
UPDATE cafeteria_items ci
LEFT JOIN categories cat ON ci.category_id = cat.category_id
SET ci.category = cat.name
WHERE ci.category IS NULL AND cat.name IS NOT NULL;

-- تحديث المنتجات بدون تصنيف
UPDATE cafeteria_items 
SET category = 'عام' 
WHERE category IS NULL OR category = '';

-- تحديث حالة المنتجات
UPDATE cafeteria_items 
SET status = CASE 
    WHEN stock_quantity <= 0 THEN 'out_of_stock'
    WHEN stock_quantity <= min_stock_level THEN 'low_stock'
    ELSE 'available'
END
WHERE status IS NULL;

-- =====================================================
-- 9. التحقق من النتائج
-- =====================================================

SELECT '📊 نتائج الإصلاح:' AS section;

SELECT 
    'cafeteria_items' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN category IS NOT NULL THEN 1 END) AS with_category,
    COUNT(CASE WHEN client_id IS NOT NULL THEN 1 END) AS with_client_id
FROM cafeteria_items

UNION ALL

SELECT 
    'categories' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) AS active_categories,
    NULL as with_client_id
FROM categories

UNION ALL

SELECT 
    'devices' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN status = 'available' THEN 1 END) AS available_devices,
    NULL as with_client_id
FROM devices

UNION ALL

SELECT 
    'sessions' AS table_name,
    COUNT(*) AS total_records,
    COUNT(CASE WHEN end_time IS NULL THEN 1 END) AS active_sessions,
    NULL as with_client_id
FROM sessions;

-- تأكيد المعاملة
COMMIT;

SELECT '🎉 تم الانتهاء من الإصلاح السريع الشامل بنجاح!' AS final_status;
SELECT '⚠️ يُنصح بإعادة تشغيل الخادم وتجربة الوظائف' AS recommendation;
