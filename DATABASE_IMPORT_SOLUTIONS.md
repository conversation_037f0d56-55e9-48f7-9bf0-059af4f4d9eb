# حلول مشاكل استيراد قاعدة البيانات

## المشكلة الأساسية
عند محاولة استيراد البيانات من نسخة قاعدة البيانات، تظهر رسالة الخطأ:
```
❌ خطأ: تم إيقاف العملية بسبب كثرة الأخطاء
```

## الأسباب المحتملة

### 1. مشاكل في تشفير الملف
- الملف ليس بتشفير UTF-8
- وجود أحرف خاصة غير مدعومة
- مشاكل في تحويل التشفير

### 2. مشاكل في تقسيم الاستعلامات
- استعلامات متعددة الأسطر
- نصوص تحتوي على فاصلة منقوطة
- تعليقات SQL غير صحيحة

### 3. مشاكل في إعدادات قاعدة البيانات
- حد الذاكرة المسموح (max_allowed_packet)
- انتهاء وقت الاتصال (timeout)
- إعدادات SQL_MODE

### 4. مشاكل في المفاتيح الخارجية
- ترتيب إنشاء الجداول
- قيود المفاتيح الخارجية
- بيانات مرجعية مفقودة

## الحلول المطبقة

### 1. تحسين آلية تقسيم الاستعلامات
```php
function splitSQLQueries($sql) {
    // إزالة التعليقات
    $sql = preg_replace('/--.*$/m', '', $sql);
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
    
    // تقسيم ذكي مع مراعاة النصوص المحاطة بعلامات اقتباس
    // ...
}
```

### 2. تحسين معالجة الأخطاء
- زيادة حد الأخطاء المسموح من 10 إلى 50
- تجاهل الأخطاء غير المهمة (Duplicate entry, already exists)
- عرض تفاصيل أكثر للأخطاء

### 3. تحسين إعدادات قاعدة البيانات
```sql
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = '';
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET CHARACTER SET utf8mb4;
SET SESSION wait_timeout = 600;
SET SESSION interactive_timeout = 600;
```

### 4. تحويل تشفير الملف تلقائياً
```php
function convertFileEncoding($content) {
    $encoding = mb_detect_encoding($content, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
    if ($encoding !== 'UTF-8') {
        $content = mb_convert_encoding($content, 'UTF-8', $encoding);
    }
    return $content;
}
```

## الأدوات الجديدة

### 1. أداة التشخيص (diagnose_import_issues.php)
- فحص ملف SQL وتحليل محتواه
- فحص إعدادات قاعدة البيانات
- تحديد المشاكل المحتملة
- اقتراح الحلول

### 2. أداة الاستيراد المحسن (improved_import.php)
- معالجة متقدمة للأخطاء
- تحويل تشفير تلقائي
- معالجة الاستعلامات في مجموعات
- عرض تقدم العملية في الوقت الفعلي
- تجاهل ذكي للأخطاء غير المهمة

### 3. تحسين الأدوات الموجودة
- `client/backup_handler.php`: تحسين آلية الاستيراد
- `reimport_database.php`: إضافة معالجة أخطاء محسنة
- `create_database_from_sql.php`: زيادة حد الأخطاء المسموح

## كيفية الاستخدام

### للمشاكل البسيطة:
1. استخدم الاستيراد العادي من صفحة الإعدادات
2. إذا فشل، جرب الاستيراد المحسن

### للمشاكل المعقدة:
1. استخدم أداة التشخيص أولاً لتحديد المشكلة
2. اتبع التوصيات المقترحة
3. استخدم أداة الاستيراد المحسن

### للملفات الكبيرة:
1. تأكد من إعدادات الخادم (max_allowed_packet, timeout)
2. استخدم أداة الاستيراد المحسن التي تعالج الملفات في مجموعات
3. قسم الملف إذا كان أكبر من 50 ميجابايت

## الوصول للأدوات

### من صفحة الإعدادات:
- **استيراد عادي**: الطريقة التقليدية
- **استيراد محسن**: أداة متقدمة مع معالجة أخطاء
- **تشخيص المشاكل**: فحص وتحليل المشاكل

### الروابط المباشرة:
- `improved_import.php`: أداة الاستيراد المحسن
- `diagnose_import_issues.php`: أداة التشخيص
- `reimport_database.php`: إعادة استيراد من station.sql

## نصائح إضافية

### قبل الاستيراد:
1. عمل نسخة احتياطية من البيانات الحالية
2. التأكد من تشفير الملف (UTF-8)
3. فحص حجم الملف (أقل من 50 ميجابايت مفضل)

### أثناء الاستيراد:
1. عدم إغلاق المتصفح أثناء العملية
2. مراقبة رسائل الأخطاء
3. انتظار انتهاء العملية كاملة

### بعد الاستيراد:
1. فحص البيانات المستوردة
2. تشغيل إصلاحات إضافية إذا لزم الأمر
3. اختبار وظائف النظام

## استكشاف الأخطاء

### إذا استمرت المشاكل:
1. تحقق من سجلات أخطاء MySQL
2. تحقق من إعدادات PHP (memory_limit, max_execution_time)
3. تحقق من مساحة القرص الصلب
4. جرب تقسيم الملف لأجزاء أصغر

### الأخطاء الشائعة وحلولها:
- **"Duplicate entry"**: يتم تجاهلها تلقائياً
- **"Table already exists"**: يتم تجاهلها تلقائياً
- **"Foreign key constraint"**: يتم تعطيل فحص المفاتيح الخارجية
- **"Max allowed packet"**: زيادة قيمة max_allowed_packet في MySQL

## الدعم الفني
إذا استمرت المشاكل، يرجى:
1. استخدام أداة التشخيص وحفظ النتائج
2. فحص سجلات الأخطاء
3. التواصل مع الدعم الفني مع تفاصيل المشكلة
