<?php
/**
 * إصلاح سريع للبيانات - PlayGood
 * حل مشكلة عرض اسم العميل في جميع الحقول
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح سريع للبيانات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص وإصلاح أسماء الأجهزة
    echo "<h2>1. فحص وإصلاح أسماء الأجهزة</h2>";
    
    try {
        // فحص الأجهزة الحالية
        $devices = $pdo->query("
            SELECT device_id, device_name, device_type, status
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الأجهزة الموجودة: <strong>" . count($devices) . "</strong></p>";
        
        if (count($devices) > 0) {
            echo "<h4>الأجهزة الحالية:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم الحالي</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "<th style='padding: 8px;'>الإجراء</th>";
            echo "</tr>";
            
            $device_names = [
                'PS5' => 'PlayStation 5',
                'PS4' => 'PlayStation 4', 
                'PC' => 'Gaming PC',
                'XBOX' => 'Xbox Series',
                'SWITCH' => 'Nintendo Switch'
            ];
            
            $counter = 1;
            foreach ($devices as $device) {
                $current_name = $device['device_name'];
                $device_type = $device['device_type'];
                
                // إنشاء اسم جديد مناسب
                $base_name = $device_names[$device_type] ?? $device_type;
                $new_name = $base_name . ' - ' . $counter;
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($current_name) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device_type) . "</td>";
                echo "<td style='padding: 8px;'>" . $device['status'] . "</td>";
                
                // تحديث الاسم إذا كان مشكوك فيه
                if (empty($current_name) || strlen($current_name) < 3 || 
                    preg_match('/^[أ-ي\s]+$/', $current_name)) {
                    
                    try {
                        $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
                        $update_stmt->execute([$new_name, $device['device_id']]);
                        
                        echo "<td style='padding: 8px; color: green;'>✅ تم التحديث إلى: " . htmlspecialchars($new_name) . "</td>";
                    } catch (PDOException $e) {
                        echo "<td style='padding: 8px; color: red;'>❌ خطأ في التحديث</td>";
                    }
                } else {
                    echo "<td style='padding: 8px; color: blue;'>✓ الاسم صحيح</td>";
                }
                
                echo "</tr>";
                $counter++;
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص وإصلاح أسماء العملاء
    echo "<h2>2. فحص وإصلاح أسماء العملاء</h2>";
    
    try {
        $customers = $pdo->query("
            SELECT customer_id, name, phone, email
            FROM customers 
            WHERE client_id = 1 
            ORDER BY customer_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد العملاء: <strong>" . count($customers) . "</strong></p>";
        
        if (count($customers) > 0) {
            echo "<h4>العملاء الحاليين:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم</th>";
            echo "<th style='padding: 8px;'>الهاتف</th>";
            echo "<th style='padding: 8px;'>البريد</th>";
            echo "</tr>";
            
            foreach ($customers as $customer) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['email'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص العملاء: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص الجلسات بعد الإصلاح
    echo "<h2>3. فحص الجلسات بعد الإصلاح</h2>";
    
    try {
        $sessions = $pdo->query("
            SELECT 
                s.session_id,
                s.status,
                d.device_name,
                d.device_type,
                c.name as customer_name,
                TIMESTAMPDIFF(MINUTE, s.start_time, COALESCE(s.end_time, NOW())) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY s.session_id DESC
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات: <strong>" . count($sessions) . "</strong></p>";
        
        if (count($sessions) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID الجلسة</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>نوع الجهاز</th>";
            echo "<th style='padding: 8px;'>اسم العميل</th>";
            echo "<th style='padding: 8px;'>المدة</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "</tr>";
            
            foreach ($sessions as $session) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px; color: blue; font-weight: bold;'>" . htmlspecialchars($session['device_name']) . "</td>";
                echo "<td style='padding: 8px; color: green;'>" . htmlspecialchars($session['device_type']) . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px;'>" . $session['duration_minutes'] . " دقيقة</td>";
                echo "<td style='padding: 8px;'>" . $session['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p style='color: blue;'>ℹ️ الآن يجب أن ترى أسماء الأجهزة (أزرق) مختلفة عن أسماء العملاء (أحمر)</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 4. إضافة أجهزة تجريبية إذا لم تكن موجودة
    echo "<h2>4. إضافة أجهزة تجريبية</h2>";
    
    try {
        $device_count = $pdo->query("SELECT COUNT(*) FROM devices WHERE client_id = 1")->fetchColumn();
        
        if ($device_count < 3) {
            echo "<p style='color: orange;'>⚠️ عدد الأجهزة قليل، سيتم إضافة أجهزة تجريبية</p>";
            
            $sample_devices = [
                ['PlayStation 5 - الرئيسي', 'PS5', 'available', 25.00, 40.00],
                ['PlayStation 4 - الثانوي', 'PS4', 'available', 20.00, 35.00],
                ['Gaming PC - المتقدم', 'PC', 'available', 30.00, 50.00],
                ['Xbox Series X', 'XBOX', 'available', 25.00, 40.00]
            ];
            
            $insert_stmt = $pdo->prepare("
                INSERT INTO devices (device_name, device_type, status, single_rate, multi_rate, client_id) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($sample_devices as $device) {
                try {
                    $insert_stmt->execute([
                        $device[0], $device[1], $device[2], 
                        $device[3], $device[4], 1
                    ]);
                    echo "<p style='color: green;'>✅ تم إضافة: {$device[0]}</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة {$device[0]}: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✅ عدد الأجهزة كافي ($device_count أجهزة)</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 5. تنظيف البيانات المكررة
    echo "<h2>5. تنظيف البيانات المكررة</h2>";
    
    try {
        // البحث عن أجهزة بأسماء مشبوهة
        $suspicious_devices = $pdo->query("
            SELECT device_id, device_name, device_type
            FROM devices 
            WHERE client_id = 1 
            AND (device_name REGEXP '^[أ-ي\\s]+$' OR LENGTH(device_name) < 3)
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($suspicious_devices) > 0) {
            echo "<p style='color: orange;'>⚠️ تم العثور على " . count($suspicious_devices) . " جهاز بأسماء مشبوهة</p>";
            
            foreach ($suspicious_devices as $device) {
                $new_name = $device['device_type'] . ' - جهاز ' . $device['device_id'];
                
                try {
                    $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
                    $update_stmt->execute([$new_name, $device['device_id']]);
                    
                    echo "<p style='color: green;'>✅ تم تحديث الجهاز {$device['device_id']}: {$device['device_name']} → $new_name</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في تحديث الجهاز {$device['device_id']}</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✅ لا توجد أجهزة بأسماء مشبوهة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في تنظيف البيانات: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بعد تشغيل هذا الإصلاح:</h4>";
echo "<ol>";
echo "<li><strong>اختبر صفحة الجلسات:</strong> يجب أن تظهر التكلفة بالأرقام</li>";
echo "<li><strong>اختبر صفحة الفواتير:</strong> يجب أن تظهر أسماء الأجهزة الصحيحة</li>";
echo "<li><strong>إذا استمرت المشكلة:</strong> تحقق من JavaScript Console</li>";
echo "<li><strong>للتأكد:</strong> شغل ملف التشخيص مرة أخرى</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='fix_display_issue.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ملف التشخيص</a>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/invoices.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الفواتير</a>";
echo "<a href='client/dashboard.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>
