/* 
 * ملف CSS لإصلاح مشاكل عرض المبالغ المالية
 * يحسن عرض الأرقام والمبالغ في جميع أنحاء النظام
 */

/* تنسيق عام للمبالغ المالية */
.amount-display, 
.stats-number, 
.session-cost, 
.total-cost,
.financial-amount {
    font-family: "Arial", "Helvetica", sans-serif !important;
    font-weight: bold;
    direction: ltr;
    text-align: center;
    font-variant-numeric: tabular-nums;
}

/* تنسيق المبالغ في البطاقات الإحصائية */
.stats-number {
    font-size: 2rem;
    line-height: 1.2;
    letter-spacing: 0.5px;
}

/* تنسيق المبالغ في الجداول */
.table .amount-display,
.table .financial-amount {
    min-width: 100px;
    text-align: right;
    font-family: "Arial", sans-serif;
    white-space: nowrap;
}

/* تنسيق المبالغ في الفواتير */
.invoice-amount {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
}

/* تنسيق المبالغ الإيجابية والسلبية */
.amount-positive {
    color: #28a745 !important;
}

.amount-negative {
    color: #dc3545 !important;
}

.amount-neutral {
    color: #6c757d !important;
}

/* تحسين عرض العملة */
.currency-symbol {
    margin-right: 5px;
    font-size: 0.9em;
    opacity: 0.8;
}

/* إصلاح مشاكل الاتجاه في المبالغ */
.rtl-amount {
    direction: rtl;
    text-align: right;
}

.ltr-amount {
    direction: ltr;
    text-align: left;
}

/* تنسيق خاص للمبالغ الكبيرة */
.large-amount {
    font-size: 1.5rem;
    font-weight: 900;
}

.medium-amount {
    font-size: 1.2rem;
    font-weight: 700;
}

.small-amount {
    font-size: 1rem;
    font-weight: 600;
}

/* تنسيق المبالغ في النماذج */
.form-control.amount-input {
    text-align: right;
    font-family: "Arial", sans-serif;
    font-weight: 600;
}

/* تحسين عرض المبالغ في الكروت */
.card .amount-display {
    padding: 5px 10px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
}

/* تنسيق المبالغ في التقارير */
.report-amount {
    font-family: "Courier New", monospace;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* إصلاح مشاكل العرض على الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-number {
        font-size: 1.5rem;
    }
    
    .table .amount-display {
        min-width: 80px;
        font-size: 0.9rem;
    }
}

/* تنسيق خاص للمبالغ في الفواتير المطبوعة */
@media print {
    .amount-display,
    .stats-number,
    .financial-amount {
        color: #000 !important;
        font-weight: bold;
    }
}

/* تأثيرات بصرية للمبالغ */
.amount-highlight {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.amount-highlight:hover {
    background: linear-gradient(45deg, #e9ecef, #dee2e6);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* إصلاح مشاكل النصوص المختلطة */
.text-numeric {
    font-family: "Arial", sans-serif !important;
    font-variant-numeric: tabular-nums;
    letter-spacing: 0.5px;
}

/* تنسيق المبالغ في الإشعارات */
.alert .amount-display {
    font-weight: 900;
    text-decoration: underline;
}

/* تحسين قابلية القراءة */
.amount-readable {
    font-size: 1.1em;
    line-height: 1.4;
    letter-spacing: 0.3px;
}

/* إخفاء النصوص الخاطئة وإظهار المبالغ الصحيحة */
.amount-container {
    position: relative;
    display: inline-block;
}

.amount-container .amount-fallback {
    display: none;
}

.amount-container.has-error .amount-display {
    display: none;
}

.amount-container.has-error .amount-fallback {
    display: inline-block;
    color: #dc3545;
    font-style: italic;
}

/* تنسيق المبالغ في الملخصات المالية */
.financial-summary .amount-display {
    font-size: 1.3rem;
    font-weight: 800;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* تحسين التباين للمبالغ */
.high-contrast .amount-display {
    background-color: #000;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
}

/* تنسيق المبالغ في الرسوم البيانية */
.chart-amount {
    font-family: "Arial", sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
}

/* إصلاح مشاكل الفاصلة العشرية */
.decimal-fix {
    white-space: nowrap;
}

.decimal-fix::after {
    content: "";
    display: inline-block;
    width: 0;
}
