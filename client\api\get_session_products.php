<?php
// تنظيف أي إخراج سابق
if (ob_get_level()) {
    ob_clean();
}

// تعيين headers قبل أي إخراج
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاولة تضمين ملفات التكوين مع معالجة الأخطاء
try {
    require_once '../../config/database.php';
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الاتصال بقاعدة البيانات',
        'debug' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// التحقق من تسجيل الدخول بدون إعادة توجيه
if (!isset($_SESSION['client_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'يجب تسجيل الدخول أولاً',
        'redirect' => '../login.php'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    if (!isset($_GET['session_id'])) {
        throw new Exception('معرف الجلسة مطلوب');
    }

    $session_id = intval($_GET['session_id']);
    if ($session_id <= 0) {
        throw new Exception('معرف الجلسة غير صحيح');
    }

    // التحقق من وجود الجلسة أولاً
    $session_check = $pdo->prepare("SELECT session_id, status FROM sessions WHERE session_id = ?");
    $session_check->execute([$session_id]);
    $session_exists = $session_check->fetch();

    if (!$session_exists) {
        throw new Exception('الجلسة غير موجودة');
    }

    // التحقق من وجود جدول session_products
    try {
        $pdo->query("SELECT 1 FROM session_products LIMIT 1");
    } catch (PDOException $e) {
        // إنشاء الجدول إذا لم يكن موجود
        $create_table = "
            CREATE TABLE session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($create_table);
    }

    // فحص بنية الجدول أولاً لتحديد الأعمدة المتاحة
    $table_columns = [];
    $columns_stmt = $pdo->prepare("DESCRIBE session_products");
    $columns_stmt->execute();
    $columns_result = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($columns_result as $column) {
        $table_columns[] = $column['Field'];
    }

    // تحديد الأعمدة المناسبة بناءً على ما هو متاح
    $price_column = in_array('unit_price', $table_columns) ? 'sp.unit_price' : 'sp.price';
    $total_column = in_array('total_price', $table_columns) ? 'sp.total_price' :
                   (in_array('total', $table_columns) ? 'sp.total' :
                   "sp.quantity * COALESCE($price_column, 0)");

    $stmt = $pdo->prepare("
        SELECT
            sp.id,
            sp.product_id,
            sp.quantity,
            COALESCE($price_column, 0) as price,
            COALESCE(ci.name, 'منتج محذوف') as product_name,
            COALESCE($total_column, sp.quantity * COALESCE($price_column, 0)) as total,
            sp.created_at
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        WHERE sp.session_id = ?
        ORDER BY sp.created_at DESC
    ");
    $stmt->execute([$session_id]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيف البيانات وضمان أن الأسعار أرقام
    foreach ($products as &$product) {
        $product['price'] = (float)$product['price'];
        $product['total'] = (float)$product['total'];
        $product['quantity'] = (int)$product['quantity'];
    }

    // حساب التكلفة الإجمالية
    $stmt = $pdo->prepare("
        SELECT
            s.session_id,
            s.start_time,
            d.hourly_rate,
            TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE s.session_id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    $time_cost = 0;
    $products_cost = 0;
    $total_cost = 0;

    if ($session) {
        $time_cost = ceil($session['duration_minutes'] / 60) * $session['hourly_rate'];
        $products_cost = array_sum(array_column($products, 'total'));
        $total_cost = $time_cost + $products_cost;
    }

    echo json_encode([
        'success' => true,
        'products' => $products,
        'total_cost' => number_format($total_cost, 2),
        'time_cost' => number_format($time_cost, 2),
        'products_cost' => number_format($products_cost, 2),
        'session_id' => $session_id,
        'products_count' => count($products),
        'debug_info' => [
            'session_exists' => $session_exists ? true : false,
            'session_status' => $session_exists['status'] ?? null,
            'table_columns' => $table_columns,
            'price_column_used' => $price_column,
            'total_column_used' => $total_column
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // تسجيل الخطأ للمطورين
    error_log("Get Session Products Error: " . $e->getMessage() . " - Session ID: " . ($session_id ?? 'unknown'));

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'session_id' => $session_id ?? null,
        'debug_info' => [
            'file' => __FILE__,
            'line' => __LINE__
        ]
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    // تسجيل خطأ قاعدة البيانات
    error_log("Database Error in get_session_products: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في قاعدة البيانات',
        'debug_error' => $e->getMessage(),
        'sql_state' => $e->getCode()
    ], JSON_UNESCAPED_UNICODE);
} catch (Throwable $e) {
    // تسجيل أي خطأ آخر
    error_log("Unexpected Error in get_session_products: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ غير متوقع',
        'debug_error' => $e->getMessage(),
        'error_type' => get_class($e)
    ], JSON_UNESCAPED_UNICODE);
}