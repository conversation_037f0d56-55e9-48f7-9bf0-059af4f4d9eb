# نظام الورديات المتقدم - PlayGood

تم إنشاء نظام الورديات الشامل للمشروع بنجاح! هذا النظام يوفر إدارة كاملة للورديات والحضور والانصراف مع تقارير مفصلة.

## الميزات الرئيسية

### 1. إدارة الورديات
- إنشاء ورديات يومية مع تحديد الأوقات والموظفين
- استخدام قوالب الورديات المحفوظة مسبقاً
- تخصيص الموظفين للورديات مع تحديد الأدوار (مشرف، عادي، احتياطي)
- إعداد الحد الأدنى والأقصى للموظفين في كل وردية
- إدارة حالات الورديات (مجدولة، نشطة، مكتملة، ملغية)

### 2. نظام الحضور والانصراف
- تسجيل حضور وانصراف الموظفين بسهولة
- إدارة فترات الاستراحة (بداية ونهاية)
- حساب ساعات العمل والعمل الإضافي تلقائياً
- تتبع التأخير والخروج المبكر
- واجهة سريعة لتسجيل الحضور

### 3. التقارير المتقدمة
- **تقرير الملخص الشهري**: إحصائيات شاملة للشهر
- **التقرير اليومي**: تفاصيل الحضور يومياً
- **تقرير الموظفين**: أداء كل موظف على حدة
- **التقرير المفصل**: سجل مفصل لكل جلسة حضور
- إمكانية تصدير التقارير كملفات CSV

### 4. الإعدادات المرنة
- فترة السماح للتأخير
- الحد الأدنى للعمل الإضافي
- تسجيل خروج تلقائي
- مدة الاستراحة الافتراضية
- إشعارات قبل بداية الوردية

## الملفات المضافة

### قاعدة البيانات
- `create_shifts_system.sql` - سكريبت إنشاء جداول النظام

### الصفحات الرئيسية
- `client/shifts.php` - صفحة إدارة الورديات
- `client/attendance.php` - صفحة الحضور والانصراف
- `client/shift_reports.php` - صفحة تقارير الورديات

### ملفات API
- `client/api/get_shift_employees.php` - إدارة موظفي الوردية

## الجداول المضافة

### 1. shift_templates
قوالب الورديات المحفوظة مسبقاً
```sql
- template_id: معرف القالب
- client_id: معرف العميل
- template_name: اسم القالب
- start_time: وقت البداية
- end_time: وقت النهاية
- break_duration: مدة الاستراحة
- is_overnight: وردية ليلية
```

### 2. shifts
الورديات المجدولة
```sql
- shift_id: معرف الوردية
- client_id: معرف العميل
- shift_name: اسم الوردية
- shift_date: تاريخ الوردية
- start_time: وقت البداية
- end_time: وقت النهاية
- max_employees: الحد الأقصى للموظفين
- min_employees: الحد الأدنى للموظفين
- status: حالة الوردية
```

### 3. employee_shifts
تخصيص الموظفين للورديات
```sql
- assignment_id: معرف التخصيص
- shift_id: معرف الوردية
- employee_id: معرف الموظف
- role_in_shift: دور الموظف في الوردية
- is_mandatory: هل الحضور إجباري
- status: حالة التخصيص
```

### 4. shift_attendance
سجل الحضور والانصراف
```sql
- attendance_id: معرف سجل الحضور
- shift_id: معرف الوردية
- employee_id: معرف الموظف
- check_in_time: وقت الحضور
- check_out_time: وقت الانصراف
- break_start_time: بداية الاستراحة
- break_end_time: نهاية الاستراحة
- actual_hours: ساعات العمل الفعلية
- overtime_hours: ساعات العمل الإضافي
- late_minutes: دقائق التأخير
- status: حالة الحضور
```

### 5. shift_settings
إعدادات النظام
```sql
- setting_id: معرف الإعداد
- client_id: معرف العميل
- setting_name: اسم الإعداد
- setting_value: قيمة الإعداد
```

## الصلاحيات المضافة

تم إضافة الصلاحيات التالية لنظام الموظفين:
- `manage_shifts`: إدارة الورديات
- `view_shifts`: عرض الورديات
- `manage_attendance`: إدارة الحضور
- `view_attendance`: عرض الحضور
- `assign_shifts`: تخصيص الورديات
- `view_shift_reports`: تقارير الورديات

## الصفحات المضافة للنظام

تم إضافة الصفحات التالية لنظام الصفحات:
- `shifts`: الورديات
- `attendance`: الحضور والانصراف
- `shift_reports`: تقارير الورديات

## Views وإجراءات قاعدة البيانات

### Views المضافة
- `shifts_detailed`: عرض تفاصيل الورديات مع الموظفين
- `attendance_detailed`: عرض تفاصيل الحضور
- `monthly_attendance_stats`: إحصائيات الحضور الشهرية

### Stored Procedures
- `CreateShiftsFromTemplate`: إنشاء ورديات من القوالب
- `CalculateAttendanceHours`: حساب ساعات العمل والإضافي

## طريقة التشغيل

### 1. تشغيل سكريبت قاعدة البيانات
```sql
-- تشغيل الملف في phpMyAdmin أو MySQL
source create_shifts_system.sql;
```

### 2. التحقق من الصلاحيات
تأكد من أن الموظفين لديهم الصلاحيات المناسبة للوصول لصفحات الورديات.

### 3. إعداد القوالب
- انتقل إلى صفحة الورديات
- أنشئ قوالب الورديات الأساسية (صباحية، مسائية، ليلية)

### 4. بدء الاستخدام
- أنشئ الورديات اليومية
- خصص الموظفين للورديات
- ابدأ تسجيل الحضور والانصراف

## الاستخدام اليومي

### للمديرين
1. إنشاء الورديات اليومية أو الأسبوعية
2. تخصيص الموظفين للورديات
3. مراقبة الحضور والانصراف
4. مراجعة التقارير

### للموظفين
1. عرض الورديات المخصصة لهم
2. تسجيل الحضور والانصراف (إذا كانت لديهم الصلاحية)
3. عرض سجل الحضور الخاص بهم

## الميزات المتقدمة

### 1. الحساب التلقائي
- حساب ساعات العمل تلقائياً
- حساب العمل الإضافي
- تتبع فترات الاستراحة

### 2. التقارير الذكية
- نسب الحضور والغياب
- متوسط ساعات العمل
- إحصائيات التأخير

### 3. المرونة في الإعدادات
- إعدادات قابلة للتخصيص لكل عميل
- قوالب ورديات متعددة
- أدوار مختلفة للموظفين في الورديات

## التطوير المستقبلي

يمكن إضافة الميزات التالية مستقبلاً:
- إشعارات تلقائية للموظفين
- تطبيق موبايل للحضور والانصراف
- تكامل مع أنظمة الرواتب
- تقارير PDF متقدمة
- نظام الإجازات والعطل

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل:
1. تحقق من سجلات الأخطاء في PHP
2. تأكد من صحة إعدادات قاعدة البيانات
3. راجع صلاحيات الموظفين

---

تم إنشاء هذا النظام بعناية ليوفر حلاً شاملاً لإدارة الورديات والحضور في مراكز الألعاب والأعمال المشابهة.
