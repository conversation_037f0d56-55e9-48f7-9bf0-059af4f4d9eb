<?php
/**
 * إصلاح مشكلة اسم العمود في جدول العملاء
 * يتحقق من اسم العمود الصحيح ويصلح الاستعلامات
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح جدول العملاء - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-tools me-2'></i>
                        إصلاح جدول العملاء
                    </h3>
                </div>
                <div class='card-body'>";

try {
    echo "<h5>1. فحص جدول العملاء</h5>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                جدول customers غير موجود. جاري إنشاؤه...
              </div>";
        
        // إنشاء الجدول
        $create_table_sql = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(255),
                notes TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_by INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customers_client (client_id),
                INDEX idx_customers_name (name),
                INDEX idx_customers_phone (phone)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($create_table_sql);
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                تم إنشاء جدول customers بنجاح
              </div>";
    } else {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                جدول customers موجود
              </div>";
    }
    
    echo "<h5>2. فحص أعمدة الجدول</h5>";
    
    // فحص أعمدة الجدول
    $columns_stmt = $pdo->query("DESCRIBE customers");
    $columns = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>
            <table class='table table-sm'>
                <thead class='table-light'>
                    <tr>
                        <th>اسم العمود</th>
                        <th>النوع</th>
                        <th>Null</th>
                        <th>Key</th>
                        <th>Default</th>
                    </tr>
                </thead>
                <tbody>";
    
    $has_customer_id = false;
    $has_id = false;
    
    foreach ($columns as $column) {
        echo "<tr>
                <td><code>{$column['Field']}</code></td>
                <td>{$column['Type']}</td>
                <td>{$column['Null']}</td>
                <td>{$column['Key']}</td>
                <td>{$column['Default']}</td>
              </tr>";
        
        if ($column['Field'] === 'customer_id') {
            $has_customer_id = true;
        }
        if ($column['Field'] === 'id') {
            $has_id = true;
        }
    }
    
    echo "</tbody></table></div>";
    
    echo "<h5>3. تحديد العمود الصحيح</h5>";
    
    if ($has_customer_id) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                العمود الصحيح هو: <code>customer_id</code>
              </div>";
        $correct_column = 'customer_id';
    } elseif ($has_id) {
        echo "<div class='alert alert-info'>
                <i class='fas fa-info me-2'></i>
                العمود الموجود هو: <code>id</code> - سيتم إضافة عمود customer_id
              </div>";
        
        // إضافة عمود customer_id
        try {
            $pdo->exec("ALTER TABLE customers ADD COLUMN customer_id INT AUTO_INCREMENT PRIMARY KEY FIRST");
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة عمود customer_id بنجاح
                  </div>";
            $correct_column = 'customer_id';
        } catch (PDOException $e) {
            echo "<div class='alert alert-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    لا يمكن إضافة عمود customer_id. سيتم استخدام العمود id
                  </div>";
            $correct_column = 'id';
        }
    } else {
        echo "<div class='alert alert-danger'>
                <i class='fas fa-times me-2'></i>
                لا يوجد عمود معرف في جدول العملاء
              </div>";
        $correct_column = null;
    }
    
    if ($correct_column) {
        echo "<h5>4. اختبار الاستعلامات</h5>";
        
        // اختبار استعلام بسيط
        try {
            $test_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customers WHERE $correct_column > 0");
            $test_stmt->execute();
            $count = $test_stmt->fetchColumn();
            
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    الاستعلام يعمل بشكل صحيح. عدد العملاء: $count
                  </div>";
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>
                    <i class='fas fa-times me-2'></i>
                    خطأ في الاستعلام: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
        }
        
        // اختبار استعلام التقارير
        try {
            $test_reports_stmt = $pdo->prepare("
                SELECT c.name as customer_name, COUNT(s.session_id) as sessions_count
                FROM customers c
                LEFT JOIN sessions s ON c.$correct_column = s.customer_id
                GROUP BY c.$correct_column, c.name
                LIMIT 5
            ");
            $test_reports_stmt->execute();
            $test_results = $test_reports_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    استعلام التقارير يعمل بشكل صحيح
                  </div>";
            
            if (!empty($test_results)) {
                echo "<h6>عينة من البيانات:</h6>
                      <div class='table-responsive'>
                        <table class='table table-sm'>
                            <thead class='table-light'>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>عدد الجلسات</th>
                                </tr>
                            </thead>
                            <tbody>";
                
                foreach ($test_results as $result) {
                    echo "<tr>
                            <td>" . htmlspecialchars($result['customer_name'] ?: 'غير محدد') . "</td>
                            <td>{$result['sessions_count']}</td>
                          </tr>";
                }
                
                echo "</tbody></table></div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>
                    <i class='fas fa-times me-2'></i>
                    خطأ في استعلام التقارير: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
        }
        
        echo "<h5>5. النتيجة النهائية</h5>";
        echo "<div class='alert alert-success'>
                <h6><i class='fas fa-check-circle me-2'></i>تم الإصلاح بنجاح!</h6>
                <p class='mb-2'>العمود الصحيح للاستخدام: <code>$correct_column</code></p>
                <p class='mb-0'>يمكنك الآن استخدام صفحة التقارير بدون مشاكل.</p>
              </div>";
    }
    
    echo "<div class='text-center mt-4'>
            <a href='client/reports.php' class='btn btn-primary me-2'>
                <i class='fas fa-chart-bar me-1'></i>اختبار صفحة التقارير
            </a>
            <a href='client/sessions.php' class='btn btn-success me-2'>
                <i class='fas fa-play-circle me-1'></i>صفحة الجلسات
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>يرجى التحقق من:</p>
            <ul>
                <li>إعدادات قاعدة البيانات</li>
                <li>صلاحيات المستخدم</li>
                <li>وجود قاعدة البيانات</li>
            </ul>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
