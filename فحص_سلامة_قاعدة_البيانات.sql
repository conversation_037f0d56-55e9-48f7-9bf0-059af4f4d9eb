-- =====================================================
-- سكريبت فحص سلامة قاعدة البيانات - Station System
-- =====================================================
-- هذا السكريبت يتحقق من سلامة قاعدة البيانات بعد التطبيق
-- ويقدم تقرير شامل عن حالة النظام
-- =====================================================

USE `station`;

-- =====================================================
-- 1. فحص وجود الجداول الأساسية
-- =====================================================

SELECT 'فحص الجداول الأساسية' AS test_section;

SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            'admins', 'clients', 'subscription_plans', 'devices', 
            'customers', 'sessions', 'cafeteria_items', 'categories',
            'employees', 'permissions', 'invoices', 'expenses'
        ) THEN '✅ موجود'
        ELSE '❌ مفقود'
    END AS status
FROM information_schema.tables 
WHERE table_schema = 'station'
AND table_name IN (
    'admins', 'clients', 'subscription_plans', 'devices', 
    'customers', 'sessions', 'cafeteria_items', 'categories',
    'employees', 'permissions', 'invoices', 'expenses'
)
ORDER BY table_name;

-- =====================================================
-- 2. فحص الفهارس المهمة
-- =====================================================

SELECT 'فحص الفهارس' AS test_section;

SELECT 
    table_name,
    index_name,
    column_name,
    '✅ موجود' AS status
FROM information_schema.statistics 
WHERE table_schema = 'station'
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name;

-- =====================================================
-- 3. فحص القيود الخارجية
-- =====================================================

SELECT 'فحص القيود الخارجية' AS test_section;

SELECT 
    constraint_name,
    table_name,
    referenced_table_name,
    '✅ موجود' AS status
FROM information_schema.key_column_usage 
WHERE table_schema = 'station'
AND referenced_table_name IS NOT NULL
ORDER BY table_name, constraint_name;

-- =====================================================
-- 4. فحص Views
-- =====================================================

SELECT 'فحص Views' AS test_section;

SELECT 
    table_name AS view_name,
    '✅ موجود' AS status
FROM information_schema.views 
WHERE table_schema = 'station'
ORDER BY table_name;

-- =====================================================
-- 5. فحص الإجراءات المخزنة
-- =====================================================

SELECT 'فحص الإجراءات المخزنة' AS test_section;

SELECT 
    routine_name AS procedure_name,
    routine_type,
    '✅ موجود' AS status
FROM information_schema.routines 
WHERE routine_schema = 'station'
ORDER BY routine_name;

-- =====================================================
-- 6. فحص البيانات الأساسية
-- =====================================================

SELECT 'فحص البيانات الأساسية' AS test_section;

-- فحص المدير الرئيسي
SELECT 
    'المدير الرئيسي' AS item,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✅ موجود (', COUNT(*), ')')
        ELSE '❌ مفقود'
    END AS status
FROM admins 
WHERE role = 'super_admin';

-- فحص خطط الاشتراك
SELECT 
    'خطط الاشتراك' AS item,
    CASE 
        WHEN COUNT(*) >= 3 THEN CONCAT('✅ موجود (', COUNT(*), ')')
        ELSE CONCAT('⚠️ ناقص (', COUNT(*), '/3)')
    END AS status
FROM subscription_plans;

-- فحص الصلاحيات
SELECT 
    'الصلاحيات الأساسية' AS item,
    CASE 
        WHEN COUNT(*) >= 10 THEN CONCAT('✅ موجود (', COUNT(*), ')')
        ELSE CONCAT('⚠️ ناقص (', COUNT(*), '/10+)')
    END AS status
FROM permissions;

-- فحص الإعدادات
SELECT 
    'إعدادات النظام' AS item,
    CASE 
        WHEN COUNT(*) >= 5 THEN CONCAT('✅ موجود (', COUNT(*), ')')
        ELSE CONCAT('⚠️ ناقص (', COUNT(*), '/5+)')
    END AS status
FROM admin_settings;

-- =====================================================
-- 7. فحص سلامة البيانات
-- =====================================================

SELECT 'فحص سلامة البيانات' AS test_section;

-- فحص العملاء بدون خطة اشتراك صحيحة
SELECT 
    'عملاء بخطة اشتراك غير صحيحة' AS issue,
    COUNT(*) AS count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ لا توجد مشاكل'
        ELSE '⚠️ يحتاج مراجعة'
    END AS status
FROM clients c
LEFT JOIN subscription_plans sp ON c.subscription_plan = sp.plan_name
WHERE sp.plan_id IS NULL;

-- فحص الأجهزة بدون عميل
SELECT 
    'أجهزة بدون عميل' AS issue,
    COUNT(*) AS count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ لا توجد مشاكل'
        ELSE '⚠️ يحتاج مراجعة'
    END AS status
FROM devices d
LEFT JOIN clients c ON d.client_id = c.client_id
WHERE c.client_id IS NULL;

-- فحص الجلسات بدون جهاز أو عميل
SELECT 
    'جلسات بدون جهاز أو عميل' AS issue,
    COUNT(*) AS count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ لا توجد مشاكل'
        ELSE '⚠️ يحتاج مراجعة'
    END AS status
FROM sessions s
LEFT JOIN devices d ON s.device_id = d.device_id
LEFT JOIN clients c ON s.client_id = c.client_id
WHERE d.device_id IS NULL OR c.client_id IS NULL;

-- =====================================================
-- 8. فحص الأداء
-- =====================================================

SELECT 'فحص الأداء' AS test_section;

-- فحص حجم الجداول
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
    table_rows,
    CASE 
        WHEN table_rows > 10000 THEN '⚠️ جدول كبير'
        WHEN table_rows > 1000 THEN '✅ حجم متوسط'
        ELSE '✅ حجم صغير'
    END AS size_status
FROM information_schema.tables 
WHERE table_schema = 'station'
AND table_type = 'BASE TABLE'
ORDER BY (data_length + index_length) DESC;

-- =====================================================
-- 9. اختبار الإجراءات المخزنة
-- =====================================================

SELECT 'اختبار الإجراءات المخزنة' AS test_section;

-- اختبار إجراء فحص الحدود (إذا كان العميل رقم 1 موجود)
SET @client_exists = (SELECT COUNT(*) FROM clients WHERE client_id = 1);

SELECT 
    'إجراء CheckClientLimit' AS procedure_name,
    CASE 
        WHEN @client_exists > 0 THEN 'جاهز للاختبار'
        ELSE 'يحتاج بيانات تجريبية'
    END AS test_status;

-- اختبار إجراء الإحصائيات
SELECT 
    'إجراء GetClientStatistics' AS procedure_name,
    CASE 
        WHEN @client_exists > 0 THEN 'جاهز للاختبار'
        ELSE 'يحتاج بيانات تجريبية'
    END AS test_status;

-- =====================================================
-- 10. تقرير الحالة العامة
-- =====================================================

SELECT 'تقرير الحالة العامة' AS test_section;

SELECT 
    'إجمالي الجداول' AS metric,
    COUNT(*) AS value,
    'جدول' AS unit
FROM information_schema.tables 
WHERE table_schema = 'station'

UNION ALL

SELECT 
    'إجمالي الفهارس',
    COUNT(DISTINCT index_name),
    'فهرس'
FROM information_schema.statistics 
WHERE table_schema = 'station'

UNION ALL

SELECT 
    'إجمالي Views',
    COUNT(*),
    'view'
FROM information_schema.views 
WHERE table_schema = 'station'

UNION ALL

SELECT 
    'إجمالي الإجراءات',
    COUNT(*),
    'إجراء'
FROM information_schema.routines 
WHERE routine_schema = 'station'

UNION ALL

SELECT 
    'إجمالي العملاء',
    COUNT(*),
    'عميل'
FROM clients

UNION ALL

SELECT 
    'إجمالي الأجهزة',
    COUNT(*),
    'جهاز'
FROM devices

UNION ALL

SELECT 
    'إجمالي المنتجات',
    COUNT(*),
    'منتج'
FROM cafeteria_items;

-- =====================================================
-- 11. توصيات التحسين
-- =====================================================

SELECT 'توصيات التحسين' AS test_section;

-- فحص الجداول التي تحتاج فهارس إضافية
SELECT 
    'جداول تحتاج فهارس إضافية' AS recommendation,
    table_name,
    CONCAT('إضافة فهرس على العمود: ', column_name) AS suggestion
FROM information_schema.columns 
WHERE table_schema = 'station'
AND column_name IN ('created_at', 'updated_at', 'status', 'is_active')
AND table_name NOT IN (
    SELECT DISTINCT table_name 
    FROM information_schema.statistics 
    WHERE table_schema = 'station' 
    AND column_name = information_schema.columns.column_name
);

-- =====================================================
-- 12. خلاصة الفحص
-- =====================================================

SELECT 'خلاصة الفحص' AS final_summary;

SELECT 
    '🎯 حالة قاعدة البيانات' AS summary,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'station'
        ) >= 15 
        AND (
            SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'station'
        ) >= 5
        THEN '✅ ممتازة - جاهزة للإنتاج'
        ELSE '⚠️ تحتاج مراجعة'
    END AS status,
    NOW() AS check_time;

-- رسالة ختامية
SELECT 
    '📋 ملاحظات مهمة' AS notes,
    'تأكد من عمل نسخة احتياطية قبل أي تعديلات' AS note1,
    'راجع التوصيات أعلاه لتحسين الأداء' AS note2,
    'قم بتشغيل هذا الفحص دورياً' AS note3;

-- =====================================================
-- إنهاء السكريبت
-- =====================================================
