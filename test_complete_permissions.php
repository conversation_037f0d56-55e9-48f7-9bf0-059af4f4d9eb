<?php
/**
 * اختبار نظام صلاحيات العملاء الشامل
 * يتحقق من وجود جميع الصفحات وصحة النظام
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام صلاحيات العملاء الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .table { background: white; border-radius: 10px; overflow: hidden; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-vial me-2"></i>اختبار نظام صلاحيات العملاء الشامل</h3>
                        <p class="mb-0">فحص شامل لجميع مكونات النظام</p>
                    </div>
                    <div class="card-body">

<?php
try {
    echo "<h4><i class='fas fa-database me-2'></i>1. فحص قاعدة البيانات</h4>";
    
    // فحص الجداول المطلوبة
    $required_tables = ['client_pages', 'client_page_permissions', 'clients'];
    foreach ($required_tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "<div class='test-success'><i class='fas fa-check me-2'></i>جدول $table موجود</div>";
        } else {
            echo "<div class='test-error'><i class='fas fa-times me-2'></i>جدول $table غير موجود</div>";
        }
    }

    echo "<h4 class='mt-4'><i class='fas fa-list me-2'></i>2. إحصائيات الصفحات</h4>";
    
    // إحصائيات عامة
    $total_pages = $pdo->query("SELECT COUNT(*) FROM client_pages WHERE is_active = 1")->fetchColumn();
    $default_pages = $pdo->query("SELECT COUNT(*) FROM client_pages WHERE is_active = 1 AND is_default = 1")->fetchColumn();
    $optional_pages = $pdo->query("SELECT COUNT(*) FROM client_pages WHERE is_active = 1 AND is_default = 0")->fetchColumn();
    
    echo "<div class='test-info'><i class='fas fa-info-circle me-2'></i>إجمالي الصفحات النشطة: <strong>$total_pages</strong></div>";
    echo "<div class='test-info'><i class='fas fa-home me-2'></i>الصفحات الافتراضية: <strong>$default_pages</strong></div>";
    echo "<div class='test-info'><i class='fas fa-cog me-2'></i>الصفحات الاختيارية: <strong>$optional_pages</strong></div>";

    echo "<h4 class='mt-4'><i class='fas fa-layer-group me-2'></i>3. الصفحات حسب الفئة</h4>";
    
    $categories_query = "
        SELECT 
            category,
            COUNT(*) as total,
            SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_count,
            GROUP_CONCAT(page_label ORDER BY page_label SEPARATOR ', ') as pages
        FROM client_pages 
        WHERE is_active = 1 
        GROUP BY category 
        ORDER BY total DESC
    ";
    
    $categories = $pdo->query($categories_query)->fetchAll();
    
    echo "<div class='table-responsive'>
            <table class='table table-striped table-sm'>
                <thead class='table-dark'>
                    <tr>
                        <th>الفئة</th>
                        <th>العدد الإجمالي</th>
                        <th>الافتراضية</th>
                        <th>الاختيارية</th>
                        <th>الصفحات</th>
                    </tr>
                </thead>
                <tbody>";
    
    foreach ($categories as $cat) {
        $optional_count = $cat['total'] - $cat['default_count'];
        echo "<tr>
                <td><strong>{$cat['category']}</strong></td>
                <td><span class='badge bg-primary'>{$cat['total']}</span></td>
                <td><span class='badge bg-success'>{$cat['default_count']}</span></td>
                <td><span class='badge bg-warning text-dark'>$optional_count</span></td>
                <td><small>" . substr($cat['pages'], 0, 100) . (strlen($cat['pages']) > 100 ? '...' : '') . "</small></td>
              </tr>";
    }
    
    echo "</tbody></table></div>";

    echo "<h4 class='mt-4'><i class='fas fa-users me-2'></i>4. اختبار العملاء</h4>";
    
    // فحص العملاء
    $clients_count = $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn();
    echo "<div class='test-info'><i class='fas fa-info-circle me-2'></i>عدد العملاء في النظام: <strong>$clients_count</strong></div>";
    
    if ($clients_count > 0) {
        // اختبار عميل واحد
        $test_client = $pdo->query("SELECT * FROM clients LIMIT 1")->fetch();
        $client_id = $test_client['client_id'];
        $client_name = $test_client['business_name'];
        
        echo "<div class='test-success'><i class='fas fa-user me-2'></i>اختبار العميل: $client_name (ID: $client_id)</div>";
        
        // فحص صلاحيات العميل
        $permissions_query = "
            SELECT 
                cp.page_name,
                cp.page_label,
                cp.category,
                cp.is_default,
                COALESCE(cpp.is_enabled, cp.is_default) as has_permission
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.is_active = TRUE
            ORDER BY cp.category, cp.page_label
        ";
        
        $permissions = $pdo->prepare($permissions_query);
        $permissions->execute([$client_id]);
        $client_permissions = $permissions->fetchAll();
        
        $allowed_count = array_sum(array_column($client_permissions, 'has_permission'));
        $total_available = count($client_permissions);
        
        echo "<div class='test-info'><i class='fas fa-shield-alt me-2'></i>صلاحيات العميل: <strong>$allowed_count</strong> من <strong>$total_available</strong> صفحة مسموحة</div>";
        
        // عرض بعض الصلاحيات كمثال
        echo "<h5 class='mt-3'>عينة من صلاحيات العميل:</h5>";
        echo "<div class='row'>";
        
        $sample_permissions = array_slice($client_permissions, 0, 12);
        foreach ($sample_permissions as $perm) {
            $status_class = $perm['has_permission'] ? 'bg-success' : 'bg-danger';
            $status_text = $perm['has_permission'] ? 'مسموح' : 'محظور';
            $type_badge = $perm['is_default'] ? 'افتراضي' : 'اختياري';
            $type_class = $perm['is_default'] ? 'bg-primary' : 'bg-warning text-dark';
            
            echo "<div class='col-md-6 col-lg-4 mb-2'>
                    <div class='card card-body p-2'>
                        <small><strong>{$perm['page_label']}</strong></small>
                        <div>
                            <span class='badge $status_class'>$status_text</span>
                            <span class='badge $type_class'>$type_badge</span>
                        </div>
                        <small class='text-muted'>{$perm['category']}</small>
                    </div>
                  </div>";
        }
        
        echo "</div>";
    }

    echo "<h4 class='mt-4'><i class='fas fa-file-code me-2'></i>5. فحص الملفات الفعلية</h4>";
    
    // فحص وجود الملفات الفعلية
    $sample_files = [
        'client/dashboard.php' => 'لوحة التحكم',
        'client/devices.php' => 'إدارة الأجهزة',
        'client/sessions.php' => 'إدارة الجلسات',
        'client/customers.php' => 'إدارة العملاء',
        'client/employees.php' => 'إدارة الموظفين',
        'client/finances.php' => 'الإدارة المالية',
        'client/orders.php' => 'إدارة الأوردرات',
        'client/attendance.php' => 'الحضور والانصراف',
        'client/shifts.php' => 'إدارة الورديات',
        'client/inventory.php' => 'إدارة المخزون'
    ];
    
    $existing_files = 0;
    $total_files = count($sample_files);
    
    foreach ($sample_files as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='test-success'><i class='fas fa-check me-2'></i>$description ($file)</div>";
            $existing_files++;
        } else {
            echo "<div class='test-error'><i class='fas fa-times me-2'></i>$description ($file) - غير موجود</div>";
        }
    }
    
    $file_percentage = round(($existing_files / $total_files) * 100);
    echo "<div class='test-info'><i class='fas fa-chart-pie me-2'></i>الملفات الموجودة: <strong>$existing_files</strong> من <strong>$total_files</strong> ($file_percentage%)</div>";

    echo "<h4 class='mt-4'><i class='fas fa-check-circle me-2'></i>6. ملخص النتائج</h4>";
    
    $system_health = 'ممتاز';
    $health_class = 'success';
    
    if ($total_pages < 20) {
        $system_health = 'يحتاج تحسين';
        $health_class = 'warning';
    }
    
    if ($file_percentage < 70) {
        $system_health = 'يحتاج إصلاح';
        $health_class = 'danger';
    }
    
    echo "<div class='alert alert-$health_class'>
            <h5><i class='fas fa-heartbeat me-2'></i>حالة النظام: $system_health</h5>
            <ul class='mb-0'>
                <li>إجمالي الصفحات المتاحة: $total_pages</li>
                <li>الصفحات الافتراضية: $default_pages</li>
                <li>الصفحات الاختيارية: $optional_pages</li>
                <li>عدد الفئات: " . count($categories) . "</li>
                <li>عدد العملاء: $clients_count</li>
                <li>الملفات الموجودة: $file_percentage%</li>
            </ul>
          </div>";

    echo "<div class='text-center mt-4'>
            <a href='admin/client_permissions.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-shield-alt me-2'></i>إدارة صلاحيات العملاء
            </a>
            <a href='setup_complete_client_permissions.php' class='btn btn-success btn-lg'>
                <i class='fas fa-plus me-2'></i>إضافة الصفحات المفقودة
            </a>
          </div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ في الاختبار</h5>
            <p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}
?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
