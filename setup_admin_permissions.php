<?php
/**
 * ملف تشغيل نظام صلاحيات الإدمن - PlayGood
 * يقوم بإنشاء الجداول والبيانات الأساسية لنظام صلاحيات الإدمن
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشغيل نظام صلاحيات الإدمن</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 800px; margin-top: 2rem; }
        .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .step { margin-bottom: 1rem; padding: 1rem; border-radius: 10px; }
        .step.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header text-center'>
                <h3><i class='fas fa-user-cog me-2'></i>تشغيل نظام صلاحيات الإدمن</h3>
            </div>
            <div class='card-body'>";

try {
    echo "<div class='step info'>
            <h5><i class='fas fa-info-circle me-2'></i>بدء عملية التشغيل</h5>
            <p>جاري إنشاء نظام صلاحيات الإدمن...</p>
          </div>";

    // 1. قراءة وتنفيذ ملف SQL
    echo "<div class='step info'>
            <h6><i class='fas fa-database me-2'></i>الخطوة 1: إنشاء الجداول والإجراءات</h6>";
    
    $sql_file = 'create_admin_permissions_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    $executed_queries = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(--|\/\*|\s*$)/', $query)) {
            try {
                $pdo->exec($query);
                $executed_queries++;
            } catch (Exception $e) {
                // تجاهل أخطاء الجداول الموجودة مسبقاً
                if (!strpos($e->getMessage(), 'already exists')) {
                    echo "<div class='alert alert-warning'>تحذير في الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
        }
    }
    
    echo "<p class='text-success'><i class='fas fa-check me-1'></i>تم تنفيذ $executed_queries استعلام بنجاح</p>";
    echo "</div>";

    // 2. التحقق من وجود الجداول
    echo "<div class='step info'>
            <h6><i class='fas fa-table me-2'></i>الخطوة 2: التحقق من الجداول</h6>";
    
    $required_tables = ['admin_pages', 'admin_page_permissions'];
    $existing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>الجدول $table موجود</p>";
        } else {
            echo "<p class='text-danger'><i class='fas fa-times me-1'></i>الجدول $table غير موجود</p>";
        }
    }
    
    if (count($existing_tables) === count($required_tables)) {
        echo "<p class='text-success'><strong>جميع الجداول المطلوبة موجودة!</strong></p>";
    }
    echo "</div>";

    // 3. التحقق من البيانات
    echo "<div class='step info'>
            <h6><i class='fas fa-data me-2'></i>الخطوة 3: التحقق من البيانات الأساسية</h6>";
    
    // عدد الصفحات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-file me-1'></i>عدد الصفحات المتاحة: <strong>$pages_count</strong></p>";
    
    // عدد المديرين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins WHERE is_active = 1");
    $admins_count = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-users me-1'></i>عدد المديرين النشطين: <strong>$admins_count</strong></p>";
    
    // عدد الصلاحيات الممنوحة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_page_permissions");
    $permissions_count = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-shield-alt me-1'></i>عدد الصلاحيات الممنوحة: <strong>$permissions_count</strong></p>";
    
    echo "</div>";

    // 4. اختبار النظام
    echo "<div class='step info'>
            <h6><i class='fas fa-test-tube me-2'></i>الخطوة 4: اختبار النظام</h6>";
    
    // اختبار الدوال
    if (function_exists('hasAdminPagePermission')) {
        echo "<p class='text-success'><i class='fas fa-check me-1'></i>دالة hasAdminPagePermission متاحة</p>";
    } else {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>دالة hasAdminPagePermission غير متاحة (سيتم تحميلها عند الحاجة)</p>";
    }
    
    // اختبار الـ View
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM admin_page_permissions_detailed LIMIT 1");
        echo "<p class='text-success'><i class='fas fa-check me-1'></i>View admin_page_permissions_detailed يعمل بشكل صحيح</p>";
    } catch (Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-1'></i>خطأ في View: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // اختبار الـ Stored Procedures
    try {
        $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = 'CheckAdminPagePermission'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>Stored Procedure CheckAdminPagePermission موجود</p>";
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>Stored Procedure CheckAdminPagePermission غير موجود</p>";
        }
    } catch (Exception $e) {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>لا يمكن التحقق من Stored Procedures</p>";
    }
    
    echo "</div>";

    // 5. النتيجة النهائية
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle me-2'></i>تم تشغيل النظام بنجاح!</h5>
            <p>تم إنشاء نظام صلاحيات الإدمن بنجاح. يمكنك الآن:</p>
            <ul>
                <li>الوصول إلى <a href='admin/admin_permissions.php' class='btn btn-sm btn-primary'>صفحة إدارة صلاحيات المديرين</a></li>
                <li>تسجيل الدخول كـ Super Admin لإدارة صلاحيات المديرين الآخرين</li>
                <li>استخدام النظام الجديد للتحكم في الوصول للصفحات</li>
            </ul>
          </div>";

    // 6. معلومات إضافية
    echo "<div class='step info'>
            <h6><i class='fas fa-info me-2'></i>معلومات مهمة</h6>
            <ul>
                <li><strong>Super Admin:</strong> يمكنه الوصول لجميع الصفحات تلقائياً</li>
                <li><strong>Admin عادي:</strong> يحصل على الصلاحيات الافتراضية فقط</li>
                <li><strong>الصفحات المحمية:</strong> جميع صفحات لوحة تحكم الإدمن محمية الآن</li>
                <li><strong>القائمة الجانبية:</strong> تُظهر فقط الصفحات المسموحة للمدير</li>
            </ul>
          </div>";

    // 7. الخطوات التالية
    echo "<div class='step info'>
            <h6><i class='fas fa-arrow-right me-2'></i>الخطوات التالية</h6>
            <ol>
                <li>تسجيل الدخول كـ Super Admin</li>
                <li>الانتقال إلى صفحة إدارة صلاحيات المديرين</li>
                <li>تخصيص الصلاحيات للمديرين حسب الحاجة</li>
                <li>اختبار النظام مع مديرين مختلفين</li>
            </ol>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-circle me-2'></i>حدث خطأ!</h5>
            <p class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</p>
            <p>يرجى التحقق من:</p>
            <ul>
                <li>إعدادات قاعدة البيانات</li>
                <li>صلاحيات المستخدم في قاعدة البيانات</li>
                <li>وجود ملف create_admin_permissions_system.sql</li>
            </ul>
          </div>";
}

echo "        </div>
        </div>
        
        <div class='text-center mt-4'>
            <a href='admin/dashboard.php' class='btn btn-primary btn-lg'>
                <i class='fas fa-arrow-left me-2'></i>الانتقال إلى لوحة التحكم
            </a>
        </div>
    </div>
    
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
