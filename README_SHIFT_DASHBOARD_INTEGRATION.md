# نظام فتح/إنهاء الوردية من الداشبورد - PlayGood

تم تطبيق نظام متكامل لإدارة الورديات مباشرة من الداشبورد! الآن الموظفين يمكنهم فتح وإنهاء ورديتهم بضغطة زر واحدة من الصفحة الرئيسية.

## 🎯 الهدف من النظام

- **سهولة الاستخدام**: فتح/إنهاء الوردية بضغطة زر واحدة
- **التسجيل التلقائي**: تسجيل الحضور والانصراف تلقائياً
- **واجهة موحدة**: كل شيء من مكان واحد في الداشبورد
- **تتبع فوري**: عرض حالة الوردية الحالية مباشرة

## ✅ الميزات المطبقة

### 1. **أزرار الوردية في الداشبورد**
- **زر "فتح الوردية"**: يسجل الحضور ويبدأ الوردية
- **زر "إنهاء الوردية"**: يسجل الانصراف وينهي الوردية
- **عرض حالة الوردية**: معلومات مباشرة عن الوردية الحالية
- **روابط سريعة**: للوصول لصفحات الحضور والورديات

### 2. **واجهة مستخدم محسنة**
- **كارت مخصص للوردية**: عرض واضح لحالة الوردية
- **ألوان تمييزية**: أخضر للوردية النشطة، أصفر لعدم الحضور
- **معلومات تفصيلية**: اسم الوردية، وقت البدء، الحالة
- **تأكيدات ذكية**: رسائل تأكيد قبل العمليات المهمة

### 3. **تحديث تلقائي**
- **API للحالة**: تحديث حالة الوردية كل 30 ثانية
- **إشعارات ذكية**: تنبيهات للاستراحة ونهاية الوردية
- **مؤشر الوقت**: عرض الوقت الحالي مباشرة
- **إحصائيات فورية**: ساعات العمل والورديات

### 4. **تجربة مستخدم محسنة**
- **رسائل ترحيب**: عند تسجيل الدخول
- **تأثيرات بصرية**: للأزرار والتفاعلات
- **تحديث الصفحة**: بدون إعادة تحميل كاملة
- **حفظ الحالة**: في المتصفح للاستمرارية

## 📁 الملفات الجديدة والمحدثة

### الملفات الأساسية
- **`client/dashboard.php`** - إضافة أزرار ومعالجة الوردية
- **`client/employee-login.php`** - رسالة ترحيب للموظفين
- **`client/assets/js/shift-management.js`** - JavaScript لإدارة الورديات
- **`client/api/get_shift_status.php`** - API للحصول على حالة الوردية

### الوظائف الجديدة
```php
// معالجة فتح الوردية
case 'start_shift':
    // البحث عن الوردية المجدولة
    // إنشاء سجل حضور
    // تحديث حالة الوردية

// معالجة إنهاء الوردية  
case 'end_shift':
    // البحث عن سجل الحضور النشط
    // تسجيل الانصراف
    // حساب ساعات العمل
```

## 🔧 كيفية عمل النظام

### 1. **عند تسجيل دخول الموظف**
```php
// في employee-login.php
$_SESSION['success'] = "مرحباً " . $employee['name'] . "! يمكنك الآن فتح الوردية لبدء العمل.";
```

### 2. **في الداشبورد**
```php
// عرض حالة الوردية
$employee_attendance_status = isEmployeeCheckedIn($pdo, $_SESSION['employee_id']);

// أزرار حسب الحالة
if ($employee_attendance_status['is_checked_in']) {
    // عرض زر "إنهاء الوردية"
} else {
    // عرض زر "فتح الوردية"
}
```

### 3. **معالجة العمليات**
```php
// عند الضغط على فتح الوردية
if ($_POST['action'] == 'start_shift') {
    // البحث عن الوردية المجدولة اليوم
    // إنشاء سجل حضور جديد
    // تحديث حالة الوردية إلى نشطة
}
```

## 🎮 سيناريوهات الاستخدام

### السيناريو 1: موظف يبدأ يومه
1. **تسجيل الدخول**: يدخل الموظف للنظام
2. **رسالة ترحيب**: "مرحباً أحمد! يمكنك الآن فتح الوردية"
3. **الداشبورد**: يرى كارت الوردية مع زر "فتح الوردية"
4. **فتح الوردية**: يضغط الزر → تسجيل حضور تلقائي
5. **بدء العمل**: يمكنه الآن إنشاء الجلسات

### السيناريو 2: موظف في منتصف الوردية
1. **عرض الحالة**: كارت أخضر "في الوردية"
2. **معلومات الوردية**: اسم الوردية، وقت البدء
3. **أزرار إضافية**: "إدارة الحضور" للاستراحة
4. **تحديث تلقائي**: كل 30 ثانية

### السيناريو 3: موظف ينهي يومه
1. **زر إنهاء الوردية**: أحمر واضح
2. **تأكيد العملية**: "هل أنت متأكد من إنهاء الوردية؟"
3. **تسجيل الانصراف**: تلقائياً مع حساب الساعات
4. **رسالة شكر**: "شكراً لك على عملك اليوم"

## 🔒 الأمان والتحقق

### التحقق من الصلاحيات
- **موظفين فقط**: النظام يعمل للموظفين المسجلين فقط
- **ورديات مجدولة**: لا يمكن فتح وردية غير مجدولة
- **حضور واحد**: منع تسجيل حضور مضاعف

### التحقق من البيانات
```php
// التحقق من وجود وردية مجدولة
$stmt = $pdo->prepare("
    SELECT es.assignment_id, es.shift_id, s.shift_name
    FROM employee_shifts es
    JOIN shifts s ON es.shift_id = s.shift_id
    WHERE es.employee_id = ? AND s.shift_date = CURDATE()
");
```

## 📊 API ومعلومات الوردية

### نقطة النهاية: `/api/get_shift_status.php`
```json
{
    "success": true,
    "status": {
        "is_checked_in": true,
        "status": "working",
        "message": "مسجل حضور ويمكن إنشاء جلسات جديدة"
    },
    "current_shift": {
        "shift_name": "الوردية الصباحية",
        "check_in_time": "09:00",
        "minutes_worked": 240,
        "hours_worked": 4.0,
        "time_remaining": {
            "hours": 4,
            "minutes": 0,
            "total_minutes": 240
        }
    },
    "notifications": [
        {
            "type": "info",
            "message": "حان وقت الاستراحة",
            "icon": "fas fa-coffee"
        }
    ]
}
```

## 🎨 التصميم والواجهة

### كارت الوردية
```html
<div class="card border-primary">
    <div class="card-header bg-primary text-white">
        <h5><i class="fas fa-clock me-2"></i>إدارة الوردية</h5>
    </div>
    <div class="card-body">
        <!-- حالة الوردية -->
        <!-- أزرار العمليات -->
        <!-- روابط سريعة -->
    </div>
</div>
```

### الألوان والحالات
- **🟢 أخضر**: وردية نشطة
- **🟡 أصفر**: خارج الوردية
- **🔴 أحمر**: زر إنهاء الوردية
- **🔵 أزرق**: روابط إضافية

## 🚀 التشغيل والاستخدام

### 1. **إعداد الورديات**
```bash
# تأكد من تشغيل نظام الورديات
http://localhost/playgood/setup_shifts_system_simple.php
```

### 2. **إنشاء ورديات للموظفين**
- اذهب إلى صفحة الورديات
- أنشئ ورديات يومية
- خصص الموظفين للورديات

### 3. **تسجيل دخول الموظف**
- الموظف يسجل دخول عادي
- يرى رسالة ترحيب
- يضغط "فتح الوردية" في الداشبورد

### 4. **العمل اليومي**
- فتح الوردية → تسجيل حضور تلقائي
- إنشاء الجلسات بشكل طبيعي
- إنهاء الوردية → تسجيل انصراف تلقائي

## 🔧 التخصيص والإعدادات

### تخصيص رسائل الترحيب
```php
// في employee-login.php
$_SESSION['success'] = "مرحباً " . $employee['name'] . "! يمكنك الآن فتح الوردية لبدء العمل.";
```

### تخصيص فترات التحديث
```javascript
// في shift-management.js
setInterval(updateShiftStatus, 30000); // كل 30 ثانية
```

### إضافة إشعارات مخصصة
```php
// في get_shift_status.php
if ($shift_details['minutes_worked'] > 240) {
    $notifications[] = [
        'type' => 'info',
        'message' => 'حان وقت الاستراحة',
        'icon' => 'fas fa-coffee'
    ];
}
```

## 📈 الإحصائيات والتقارير

### إحصائيات الوردية
- **ساعات العمل اليومية**
- **عدد الورديات الأسبوعية**
- **إجمالي ساعات الشهر**
- **معدل الحضور**

### تقارير تلقائية
- **تقرير الحضور اليومي**
- **ملخص الورديات الأسبوعي**
- **إحصائيات الموظف الشهرية**

## 🛠️ الصيانة والتطوير

### إضافة ميزات جديدة
- **تسجيل الحضور بالبصمة**
- **إشعارات الهاتف المحمول**
- **تكامل مع أنظمة الرواتب**
- **تقارير متقدمة**

### تحسينات الأداء
- **تخزين مؤقت للحالات**
- **تحديث جزئي للواجهة**
- **ضغط البيانات المرسلة**

---

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. تحقق من سجلات الأخطاء في PHP
2. تأكد من إعداد الورديات بشكل صحيح
3. تحقق من صلاحيات الموظفين
4. راجع حالة قاعدة البيانات

النظام الآن يوفر تجربة سلسة ومتكاملة لإدارة الورديات مباشرة من الداشبورد! 🎯✨
