<?php
/**
 * اختبار صلاحيات النسخ الاحتياطي للعملاء الفردية
 * لاختبار الميزة الجديدة للتحكم في صلاحيات العملاء المحددين
 */

require_once 'config/database.php';
require_once 'includes/backup_permissions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار صلاحيات النسخ الاحتياطي للعملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h4 class='mb-0'><i class='fas fa-users-cog me-2'></i>اختبار صلاحيات النسخ الاحتياطي للعملاء</h4>";
echo "</div>";
echo "<div class='card-body'>";

// معالجة تحديث الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_client_backup'])) {
    $client_id = intval($_POST['client_id']);
    $enabled = isset($_POST['backup_enabled']) ? 1 : 0;
    
    if (setClientBackupPermission($client_id, $enabled)) {
        $status = $enabled ? 'تفعيل' : 'تعطيل';
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم {$status} النسخ الاحتياطي للعميل بنجاح</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>فشل في تحديث صلاحية النسخ الاحتياطي</div>";
    }
}

// التأكد من وجود حقل backup_enabled
echo "<h5 class='text-success'>1. التأكد من بنية قاعدة البيانات:</h5>";
try {
    ensureBackupColumnExists();
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم التأكد من وجود حقل backup_enabled</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage() . "</div>";
}

// اختبار الإعدادات العامة
echo "<h5 class='text-info'>2. الإعدادات العامة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card border-info'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title'>النسخ الاحتياطي عموماً</h6>";
$globalEnabled = isBackupEnabledGlobally();
if ($globalEnabled) {
    echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>مفعل عموماً</span>";
} else {
    echo "<span class='badge bg-danger fs-6'><i class='fas fa-times me-1'></i>معطل عموماً</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card border-warning'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title'>وضع الصيانة</h6>";
$maintenanceMode = isSystemInMaintenance();
if ($maintenanceMode) {
    echo "<span class='badge bg-warning fs-6'><i class='fas fa-tools me-1'></i>نشط</span>";
} else {
    echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>غير نشط</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// عرض العملاء مع صلاحياتهم
echo "<h5 class='text-success mt-4'>3. العملاء وصلاحياتهم:</h5>";
$clients = getClientsWithBackupStatus();

if (!empty($clients)) {
    echo "<div class='table-responsive'>";
    echo "<table class='table table-hover'>";
    echo "<thead class='table-dark'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>اسم المحل</th>";
    echo "<th>اسم المالك</th>";
    echo "<th>البريد الإلكتروني</th>";
    echo "<th>حالة الحساب</th>";
    echo "<th>النسخ الاحتياطي</th>";
    echo "<th>الاختبار</th>";
    echo "<th>الإجراءات</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($clients as $client) {
        echo "<tr>";
        echo "<td>" . $client['client_id'] . "</td>";
        echo "<td>" . htmlspecialchars($client['business_name']) . "</td>";
        echo "<td>" . htmlspecialchars($client['owner_name']) . "</td>";
        echo "<td>" . htmlspecialchars($client['email']) . "</td>";
        
        // حالة الحساب
        if ($client['is_active']) {
            echo "<td><span class='badge bg-success'>نشط</span></td>";
        } else {
            echo "<td><span class='badge bg-danger'>معطل</span></td>";
        }
        
        // حالة النسخ الاحتياطي
        if ($client['backup_enabled']) {
            echo "<td><span class='badge bg-primary'><i class='fas fa-check me-1'></i>مفعل</span></td>";
        } else {
            echo "<td><span class='badge bg-warning'><i class='fas fa-times me-1'></i>معطل</span></td>";
        }
        
        // اختبار الدالة
        $testResult = isBackupEnabledForClient($client['client_id']);
        if ($testResult) {
            echo "<td><span class='badge bg-success'><i class='fas fa-check me-1'></i>يعمل</span></td>";
        } else {
            echo "<td><span class='badge bg-danger'><i class='fas fa-times me-1'></i>لا يعمل</span></td>";
        }
        
        // أزرار الإجراءات
        echo "<td>";
        echo "<form method='POST' class='d-inline'>";
        echo "<input type='hidden' name='client_id' value='" . $client['client_id'] . "'>";
        if ($client['backup_enabled']) {
            echo "<button type='submit' name='update_client_backup' class='btn btn-sm btn-warning' title='تعطيل النسخ الاحتياطي'>";
            echo "<i class='fas fa-ban'></i>";
            echo "</button>";
        } else {
            echo "<input type='hidden' name='backup_enabled' value='1'>";
            echo "<button type='submit' name='update_client_backup' class='btn btn-sm btn-primary' title='تفعيل النسخ الاحتياطي'>";
            echo "<i class='fas fa-database'></i>";
            echo "</button>";
        }
        echo "</form>";
        echo "</td>";
        
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>لا يوجد عملاء في النظام</div>";
}

// اختبار الرسائل
echo "<h5 class='text-success mt-4'>4. اختبار الرسائل:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>رسالة تعطيل النسخ الاحتياطي العامة:</h6>";
echo getBackupDisabledMessage();
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>رسالة تعطيل النسخ الاحتياطي للعميل:</h6>";
echo getClientBackupDisabledMessage();
echo "</div>";
echo "</div>";

// اختبار سيناريوهات مختلفة
echo "<h5 class='text-success mt-4'>5. اختبار السيناريوهات:</h5>";
echo "<div class='row'>";

// سيناريو 1: عميل مفعل مع إعداد عام مفعل
echo "<div class='col-md-4'>";
echo "<div class='card border-success'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title text-success'>سيناريو 1</h6>";
echo "<p class='small'>عميل مفعل + إعداد عام مفعل</p>";
if (!empty($clients)) {
    $activeClient = null;
    foreach ($clients as $client) {
        if ($client['is_active'] && $client['backup_enabled']) {
            $activeClient = $client;
            break;
        }
    }
    if ($activeClient) {
        $result = isBackupEnabledForClient($activeClient['client_id']);
        echo "<span class='badge " . ($result ? 'bg-success' : 'bg-danger') . "'>";
        echo $result ? 'نجح الاختبار' : 'فشل الاختبار';
        echo "</span>";
    } else {
        echo "<span class='badge bg-secondary'>لا يوجد عميل مناسب</span>";
    }
} else {
    echo "<span class='badge bg-secondary'>لا يوجد عملاء</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";

// سيناريو 2: عميل معطل مع إعداد عام مفعل
echo "<div class='col-md-4'>";
echo "<div class='card border-warning'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title text-warning'>سيناريو 2</h6>";
echo "<p class='small'>عميل معطل + إعداد عام مفعل</p>";
if (!empty($clients)) {
    $disabledClient = null;
    foreach ($clients as $client) {
        if ($client['is_active'] && !$client['backup_enabled']) {
            $disabledClient = $client;
            break;
        }
    }
    if ($disabledClient) {
        $result = isBackupEnabledForClient($disabledClient['client_id']);
        echo "<span class='badge " . (!$result ? 'bg-success' : 'bg-danger') . "'>";
        echo !$result ? 'نجح الاختبار' : 'فشل الاختبار';
        echo "</span>";
    } else {
        echo "<span class='badge bg-secondary'>لا يوجد عميل مناسب</span>";
    }
} else {
    echo "<span class='badge bg-secondary'>لا يوجد عملاء</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";

// سيناريو 3: عميل غير موجود
echo "<div class='col-md-4'>";
echo "<div class='card border-danger'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title text-danger'>سيناريو 3</h6>";
echo "<p class='small'>عميل غير موجود</p>";
$result = isBackupEnabledForClient(99999);
echo "<span class='badge " . (!$result ? 'bg-success' : 'bg-danger') . "'>";
echo !$result ? 'نجح الاختبار' : 'فشل الاختبار';
echo "</span>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>"; // card-body
echo "</div>"; // card

echo "<div class='mt-3 text-center'>";
echo "<a href='add_client_backup_column.php' class='btn btn-info me-2'>";
echo "<i class='fas fa-database me-2'></i>إضافة حقل النسخ الاحتياطي";
echo "</a>";
echo "<a href='admin/clients.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users me-2'></i>إدارة العملاء";
echo "</a>";
echo "<a href='admin/settings.php' class='btn btn-success'>";
echo "<i class='fas fa-cog me-2'></i>إعدادات الأدمن";
echo "</a>";
echo "</div>";

echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
