<?php
session_start();

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['business_name'] = 'محل تجريبي';
}

echo "<h1>اختبار APIs نظام الأوردرات</h1>";
echo "<p><strong>معرف العميل:</strong> {$_SESSION['client_id']}</p>";

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار APIs الأوردرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .api-test {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .response-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container mt-4">
        
        <!-- اختبار API المنتجات -->
        <div class="api-test">
            <h3>1. اختبار API المنتجات</h3>
            <button class="btn btn-primary" onclick="testProductsAPI()">اختبار جلب المنتجات</button>
            <div id="products-response" class="response-box mt-3" style="display: none;"></div>
        </div>

        <!-- اختبار API العملاء -->
        <div class="api-test">
            <h3>2. اختبار API العملاء</h3>
            <button class="btn btn-primary me-2" onclick="testCustomersAPI()">اختبار جلب جميع العملاء</button>
            <button class="btn btn-secondary" onclick="testSearchCustomersAPI()">اختبار البحث عن العملاء</button>
            <div id="customers-response" class="response-box mt-3" style="display: none;"></div>
        </div>

        <!-- اختبار API الأوردرات -->
        <div class="api-test">
            <h3>3. اختبار API الأوردرات</h3>
            <button class="btn btn-primary me-2" onclick="testOrdersAPI()">اختبار جلب الأوردرات</button>
            <button class="btn btn-success me-2" onclick="testCreateOrderAPI()">اختبار إنشاء أوردر</button>
            <button class="btn btn-info" onclick="testOrderDetailsAPI()">اختبار تفاصيل الأوردر</button>
            <div id="orders-response" class="response-box mt-3" style="display: none;"></div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="api-test">
            <h3>4. ملخص النتائج</h3>
            <div id="test-summary"></div>
        </div>

    </div>

    <script>
        let testResults = [];

        function logResult(test, success, message, data = null) {
            testResults.push({ test, success, message, data });
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('test-summary');
            let html = '<ul>';
            testResults.forEach(result => {
                const className = result.success ? 'success' : 'error';
                const icon = result.success ? '✓' : '✗';
                html += `<li class="${className}">${icon} ${result.test}: ${result.message}</li>`;
            });
            html += '</ul>';
            summary.innerHTML = html;
        }

        function showResponse(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = JSON.stringify(data, null, 2);
        }

        // اختبار API المنتجات
        function testProductsAPI() {
            fetch('client/api/get_products.php')
                .then(response => response.json())
                .then(data => {
                    showResponse('products-response', data);
                    
                    if (data.success) {
                        let products = [];
                        if (data.data && data.data.products) {
                            products = data.data.products;
                        } else if (data.products) {
                            products = data.products;
                        }
                        
                        logResult('API المنتجات', true, `تم جلب ${products.length} منتج بنجاح`);
                    } else {
                        logResult('API المنتجات', false, data.error || 'فشل في جلب المنتجات');
                    }
                })
                .catch(error => {
                    showResponse('products-response', { error: error.message });
                    logResult('API المنتجات', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // اختبار API العملاء
        function testCustomersAPI() {
            fetch('client/api/get_all_customers.php')
                .then(response => response.json())
                .then(data => {
                    showResponse('customers-response', data);
                    
                    if (data.success) {
                        logResult('API جميع العملاء', true, `تم جلب ${data.customers.length} عميل بنجاح`);
                    } else {
                        logResult('API جميع العملاء', false, data.error || 'فشل في جلب العملاء');
                    }
                })
                .catch(error => {
                    showResponse('customers-response', { error: error.message });
                    logResult('API جميع العملاء', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // اختبار API البحث عن العملاء
        function testSearchCustomersAPI() {
            fetch('client/api/search-customers.php?q=test')
                .then(response => response.json())
                .then(data => {
                    showResponse('customers-response', data);
                    
                    if (data.success) {
                        logResult('API البحث عن العملاء', true, `تم البحث بنجاح`);
                    } else {
                        logResult('API البحث عن العملاء', false, data.error || 'فشل في البحث');
                    }
                })
                .catch(error => {
                    showResponse('customers-response', { error: error.message });
                    logResult('API البحث عن العملاء', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // اختبار API الأوردرات
        function testOrdersAPI() {
            fetch('client/api/get_orders.php?limit=5')
                .then(response => response.json())
                .then(data => {
                    showResponse('orders-response', data);
                    
                    if (data.success) {
                        logResult('API جلب الأوردرات', true, `تم جلب ${data.orders.length} أوردر بنجاح`);
                    } else {
                        logResult('API جلب الأوردرات', false, data.error || 'فشل في جلب الأوردرات');
                    }
                })
                .catch(error => {
                    showResponse('orders-response', { error: error.message });
                    logResult('API جلب الأوردرات', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // اختبار إنشاء أوردر
        function testCreateOrderAPI() {
            // أولاً جلب منتج للاختبار
            fetch('client/api/get_products.php')
                .then(response => response.json())
                .then(data => {
                    let products = [];
                    if (data.success) {
                        if (data.data && data.data.products) {
                            products = data.data.products;
                        } else if (data.products) {
                            products = data.products;
                        }
                    }
                    
                    if (products.length === 0) {
                        logResult('API إنشاء أوردر', false, 'لا توجد منتجات للاختبار');
                        return;
                    }
                    
                    // إنشاء أوردر تجريبي
                    const testOrder = {
                        customer_id: null,
                        payment_method: 'cash',
                        notes: 'أوردر اختبار API',
                        items: [
                            {
                                product_id: products[0].id,
                                quantity: 2
                            }
                        ]
                    };
                    
                    return fetch('client/api/create_order.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(testOrder)
                    });
                })
                .then(response => response.json())
                .then(data => {
                    showResponse('orders-response', data);
                    
                    if (data.success) {
                        logResult('API إنشاء أوردر', true, `تم إنشاء أوردر بنجاح: ${data.order_number}`);
                        
                        // اختبار تفاصيل الأوردر المُنشأ
                        if (data.order_id) {
                            setTimeout(() => testOrderDetailsAPI(data.order_id), 1000);
                        }
                    } else {
                        logResult('API إنشاء أوردر', false, data.error || 'فشل في إنشاء الأوردر');
                    }
                })
                .catch(error => {
                    showResponse('orders-response', { error: error.message });
                    logResult('API إنشاء أوردر', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // اختبار تفاصيل الأوردر
        function testOrderDetailsAPI(orderId = null) {
            if (!orderId) {
                // جلب أول أوردر متاح
                fetch('client/api/get_orders.php?limit=1')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.orders.length > 0) {
                            testOrderDetailsAPI(data.orders[0].id);
                        } else {
                            logResult('API تفاصيل الأوردر', false, 'لا توجد أوردرات للاختبار');
                        }
                    });
                return;
            }
            
            fetch(`client/api/get_order_details.php?order_id=${orderId}`)
                .then(response => response.json())
                .then(data => {
                    showResponse('orders-response', data);
                    
                    if (data.success) {
                        logResult('API تفاصيل الأوردر', true, `تم جلب تفاصيل الأوردر ${data.order.order_number} بنجاح`);
                    } else {
                        logResult('API تفاصيل الأوردر', false, data.error || 'فشل في جلب تفاصيل الأوردر');
                    }
                })
                .catch(error => {
                    showResponse('orders-response', { error: error.message });
                    logResult('API تفاصيل الأوردر', false, 'خطأ في الاتصال: ' + error.message);
                });
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            testResults = [];
            testProductsAPI();
            setTimeout(testCustomersAPI, 500);
            setTimeout(testOrdersAPI, 1000);
            setTimeout(testCreateOrderAPI, 1500);
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء اختبار APIs...');
        });
    </script>

    <div class="text-center mt-4">
        <button class="btn btn-success btn-lg" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <a href="client/orders.php" class="btn btn-primary btn-lg ms-3">انتقل إلى صفحة الأوردرات</a>
    </div>

</body>
</html>
