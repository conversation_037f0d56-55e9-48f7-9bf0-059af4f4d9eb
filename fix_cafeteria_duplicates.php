<?php
/**
 * إصلاح تكرار المنتجات في صفحة الكافتيريا
 * هذا الملف يحذف المنتجات المكررة ويضيف قيود لمنع التكرار
 */

require_once 'config/database.php';

// تعيين ترميز UTF-8
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح تكرار المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }
        .success-step .step-number { background: #28a745; }
        .warning-step .step-number { background: #ffc107; color: #000; }
        .error-step .step-number { background: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="text-center mb-4">
                    <h1 class="display-6"><i class="fas fa-tools me-3"></i>إصلاح تكرار المنتجات</h1>
                    <p class="text-muted">حذف المنتجات المكررة وإضافة قيود لمنع التكرار</p>
                </div>

<?php
try {
    echo "<div class='step-card'>
            <div class='card-header bg-info text-white'>
                <h5><span class='step-number'>1</span>فحص المنتجات المكررة</h5>
            </div>
            <div class='card-body'>";

    // فحص المنتجات المكررة
    $duplicates_query = "
        SELECT name, category, client_id, COUNT(*) as count
        FROM cafeteria_items 
        GROUP BY name, category, client_id 
        HAVING COUNT(*) > 1
        ORDER BY count DESC, name
    ";
    
    $duplicates_stmt = $pdo->query($duplicates_query);
    $duplicates = $duplicates_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($duplicates)) {
        echo "<p class='text-success'><i class='fas fa-check-circle me-2'></i>لا توجد منتجات مكررة</p>";
    } else {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>تم العثور على " . count($duplicates) . " منتج مكرر:</p>";
        echo "<ul class='list-group'>";
        foreach ($duplicates as $duplicate) {
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
            echo "<span><strong>{$duplicate['name']}</strong> - {$duplicate['category']} (عميل: {$duplicate['client_id']})</span>";
            echo "<span class='badge bg-warning rounded-pill'>{$duplicate['count']} نسخة</span>";
            echo "</li>";
        }
        echo "</ul>";
    }
    
    echo "</div></div>";

    // حذف المنتجات المكررة
    if (!empty($duplicates)) {
        echo "<div class='step-card warning-step'>
                <div class='card-header bg-warning text-dark'>
                    <h5><span class='step-number'>2</span>حذف المنتجات المكررة</h5>
                </div>
                <div class='card-body'>";

        $delete_query = "
            DELETE c1 FROM cafeteria_items c1
            INNER JOIN cafeteria_items c2 
            WHERE c1.id > c2.id 
              AND c1.name = c2.name 
              AND c1.category = c2.category 
              AND c1.client_id = c2.client_id
        ";
        
        $deleted_count = $pdo->exec($delete_query);
        
        if ($deleted_count > 0) {
            echo "<p class='text-success'><i class='fas fa-trash me-2'></i>تم حذف $deleted_count منتج مكرر</p>";
        } else {
            echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>لم يتم حذف أي منتجات</p>";
        }
        
        echo "</div></div>";
    }

    // إضافة القيد الفريد
    echo "<div class='step-card'>
            <div class='card-header bg-primary text-white'>
                <h5><span class='step-number'>3</span>إضافة قيد فريد لمنع التكرار</h5>
            </div>
            <div class='card-body'>";

    try {
        // حذف الفهرس إذا كان موجوداً
        $pdo->exec("DROP INDEX IF EXISTS unique_product_per_client ON cafeteria_items");
        
        // إضافة الفهرس الفريد
        $pdo->exec("ALTER TABLE cafeteria_items ADD UNIQUE INDEX unique_product_per_client (name, category, client_id)");
        
        echo "<p class='text-success'><i class='fas fa-shield-alt me-2'></i>تم إضافة قيد فريد لمنع تكرار المنتجات</p>";
        
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يمكن إضافة القيد الفريد - لا تزال هناك منتجات مكررة</p>";
        } elseif (strpos($e->getMessage(), 'already exists') !== false) {
            echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>القيد الفريد موجود مسبقاً</p>";
        } else {
            echo "<p class='text-danger'><i class='fas fa-times-circle me-2'></i>خطأ في إضافة القيد الفريد: " . $e->getMessage() . "</p>";
        }
    }

    echo "</div></div>";

    // إضافة فهرس للبحث
    echo "<div class='step-card'>
            <div class='card-header bg-secondary text-white'>
                <h5><span class='step-number'>4</span>تحسين الأداء</h5>
            </div>
            <div class='card-body'>";

    try {
        $pdo->exec("ALTER TABLE cafeteria_items ADD INDEX IF NOT EXISTS idx_cafeteria_search (name, category, client_id)");
        echo "<p class='text-success'><i class='fas fa-search me-2'></i>تم إضافة فهرس للبحث السريع</p>";
    } catch (PDOException $e) {
        echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>فهرس البحث موجود مسبقاً</p>";
    }

    echo "</div></div>";

    // التحقق النهائي
    echo "<div class='step-card success-step'>
            <div class='card-header bg-success text-white'>
                <h5><span class='step-number'>5</span>التحقق النهائي</h5>
            </div>
            <div class='card-body'>";

    // عد المنتجات الإجمالية
    $total_products = $pdo->query("SELECT COUNT(*) FROM cafeteria_items")->fetchColumn();
    
    // فحص المنتجات المكررة المتبقية
    $remaining_duplicates = $pdo->query($duplicates_query)->fetchAll();
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-light'>";
    echo "<div class='card-body text-center'>";
    echo "<h3 class='text-primary'>$total_products</h3>";
    echo "<p class='mb-0'>إجمالي المنتجات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card bg-light'>";
    echo "<div class='card-body text-center'>";
    echo "<h3 class='" . (empty($remaining_duplicates) ? 'text-success' : 'text-danger') . "'>" . count($remaining_duplicates) . "</h3>";
    echo "<p class='mb-0'>منتجات مكررة متبقية</p>";
    echo "</div></div></div>";
    echo "</div>";

    if (empty($remaining_duplicates)) {
        echo "<div class='alert alert-success mt-3'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "<strong>تم الإصلاح بنجاح!</strong> لا توجد منتجات مكررة ولن يمكن إضافة منتجات مكررة في المستقبل.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning mt-3'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "<strong>تحذير:</strong> لا تزال هناك منتجات مكررة. قد تحتاج لتشغيل الإصلاح مرة أخرى.";
        echo "</div>";
    }

    echo "</div></div>";

} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "<strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage();
    echo "</div>";
}
?>

                <div class="text-center mt-4">
                    <a href="client/cafeteria.php" class="btn btn-primary">
                        <i class="fas fa-coffee me-2"></i>الذهاب لصفحة الكافتيريا
                    </a>
                    <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">
                        <i class="fas fa-redo me-2"></i>تشغيل الإصلاح مرة أخرى
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
