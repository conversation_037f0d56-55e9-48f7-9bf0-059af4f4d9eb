<?php
/**
 * Check Sessions Table Structure - PlayGood
 * Simple diagnostic script to check the sessions table structure
 */

require_once 'config/database.php';

echo "<h1>فحص هيكل جدول sessions</h1>";

try {
    // فحص هيكل جدول sessions
    echo "<h2>هيكل جدول sessions الحالي:</h2>";
    
    $stmt = $pdo->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th></tr>";
    
    $status_exists = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'status') {
            $status_exists = true;
        }
    }
    echo "</table>";
    
    if ($status_exists) {
        echo "<p style='color: green; font-weight: bold;'>✅ عمود status موجود</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ عمود status غير موجود</p>";
    }
    
    // اختبار استعلام بسيط
    echo "<h2>اختبار استعلام بسيط:</h2>";
    try {
        $test_stmt = $pdo->query("SELECT COUNT(*) as total FROM sessions");
        $result = $test_stmt->fetch();
        echo "<p style='color: green;'>✅ إجمالي الجلسات: " . $result['total'] . "</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
    }
    
    // اختبار استعلام مع status
    echo "<h2>اختبار استعلام مع عمود status:</h2>";
    try {
        $test_stmt = $pdo->query("SELECT status, COUNT(*) as count FROM sessions GROUP BY status");
        $results = $test_stmt->fetchAll();
        
        if (count($results) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background-color: #f0f0f0;'><th>الحالة</th><th>العدد</th></tr>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                echo "<td>" . htmlspecialchars($row['count']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p style='color: green;'>✅ عمود status يعمل بشكل صحيح</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد بيانات في جدول sessions</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام status: " . $e->getMessage() . "</p>";
        
        // محاولة إضافة عمود status
        echo "<h3>محاولة إضافة عمود status:</h3>";
        try {
            $pdo->exec("ALTER TABLE sessions ADD COLUMN status ENUM('active','completed','cancelled') NOT NULL DEFAULT 'active'");
            echo "<p style='color: green;'>✅ تم إضافة عمود status بنجاح</p>";
        } catch (PDOException $e2) {
            echo "<p style='color: red;'>❌ فشل في إضافة عمود status: " . $e2->getMessage() . "</p>";
        }
    }
    
    // فحص الجداول المرتبطة
    echo "<h2>فحص الجداول المرتبطة:</h2>";
    
    $tables = ['devices', 'customers', 'rooms'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "<p style='color: green;'>✅ جدول $table: " . $result['count'] . " سجل</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>وجود قاعدة البيانات 'station'</li>";
    echo "<li>صحة بيانات الاتصال في config/database.php</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
table {
    width: 100%;
    border-collapse: collapse;
}
th, td {
    padding: 8px;
    text-align: right;
    border: 1px solid #ddd;
}
th {
    background-color: #f2f2f2;
}
</style>
