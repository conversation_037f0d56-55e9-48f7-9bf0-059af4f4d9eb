<?php
/**
 * إعدادات النظام التجريبي - صفحة إدارية
 * للتحكم في إعدادات التجربة المجانية
 */

session_start();

// التحقق من كلمة مرور الإدارة البسيطة
$admin_password = 'admin123'; // يمكن تغييرها
$is_admin = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_login'])) {
    if ($_POST['password'] === $admin_password) {
        $_SESSION['trial_admin'] = true;
        $is_admin = true;
    } else {
        $error = 'كلمة مرور خاطئة';
    }
}

if (isset($_SESSION['trial_admin'])) {
    $is_admin = true;
}

if (isset($_GET['logout'])) {
    unset($_SESSION['trial_admin']);
    $is_admin = false;
}

// معالجة تحديث الإعدادات
if ($is_admin && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $new_duration = intval($_POST['trial_duration'] ?? 3);
    $new_cleanup_interval = intval($_POST['cleanup_interval'] ?? 300);
    
    // قراءة الملف الحالي
    $config_file = 'config/trial-config.php';
    $content = file_get_contents($config_file);
    
    // تحديث القيم
    $content = preg_replace("/define\('TRIAL_DURATION_HOURS', \d+\);/", "define('TRIAL_DURATION_HOURS', $new_duration);", $content);
    $content = preg_replace("/define\('TRIAL_CLEANUP_INTERVAL', \d+\);/", "define('TRIAL_CLEANUP_INTERVAL', $new_cleanup_interval);", $content);
    
    // حفظ الملف
    if (file_put_contents($config_file, $content)) {
        $success = 'تم تحديث الإعدادات بنجاح!';
    } else {
        $error = 'فشل في تحديث الإعدادات';
    }
}

// الحصول على الإحصائيات
if ($is_admin) {
    require_once 'config/database.php';
    
    try {
        // عدد الحسابات التجريبية النشطة
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM trial_clients WHERE trial_end > NOW()");
        $stmt->execute();
        $active_trials = $stmt->fetchColumn();
        
        // عدد الحسابات المنتهية الصلاحية
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM trial_clients WHERE trial_end <= NOW()");
        $stmt->execute();
        $expired_trials = $stmt->fetchColumn();
        
        // إجمالي الحسابات
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM trial_clients");
        $stmt->execute();
        $total_trials = $stmt->fetchColumn();
        
        // آخر 10 حسابات تجريبية
        $stmt = $pdo->prepare("
            SELECT business_name, owner_name, email, trial_start, trial_end, 
                   CASE WHEN trial_end > NOW() THEN 'نشط' ELSE 'منتهي' END as status
            FROM trial_clients 
            ORDER BY trial_start DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $recent_trials = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $db_error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام التجريبي - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .admin-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .admin-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .admin-body {
            padding: 2rem;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 900;
            color: #2d3748;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .settings-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .badge {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #48bb78, #38a169) !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e) !important;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-card">
            <div class="admin-header">
                <h1>
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات النظام التجريبي
                </h1>
                <p class="mb-0">لوحة تحكم إدارية لنظام التجربة المجانية</p>
            </div>

            <div class="admin-body">
                <?php if (!$is_admin): ?>
                    <!-- نموذج تسجيل الدخول -->
                    <div class="login-form">
                        <h4 class="mb-4">تسجيل دخول الإدارة</h4>
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" name="admin_login" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>

                        <div class="mt-4">
                            <small class="text-muted">
                                كلمة المرور الافتراضية: <code>admin123</code>
                            </small>
                        </div>
                    </div>

                <?php else: ?>
                    <!-- لوحة التحكم الإدارية -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>لوحة التحكم الإدارية</h3>
                        <a href="?logout=1" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            خروج
                        </a>
                    </div>

                    <?php if (isset($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($db_error)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-database me-2"></i>
                            <?php echo htmlspecialchars($db_error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- الإحصائيات -->
                    <?php if (!isset($db_error)): ?>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number text-success"><?php echo $active_trials; ?></div>
                                <div class="stat-label">حسابات نشطة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number text-danger"><?php echo $expired_trials; ?></div>
                                <div class="stat-label">حسابات منتهية</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number text-primary"><?php echo $total_trials; ?></div>
                                <div class="stat-label">إجمالي الحسابات</div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- إعدادات النظام -->
                    <div class="settings-section">
                        <h5 class="mb-3">
                            <i class="fas fa-sliders-h me-2"></i>
                            إعدادات النظام
                        </h5>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="trial_duration" class="form-label">مدة التجربة (بالساعات)</label>
                                    <input type="number" class="form-control" id="trial_duration" name="trial_duration" 
                                           value="<?php echo defined('TRIAL_DURATION_HOURS') ? TRIAL_DURATION_HOURS : 3; ?>" min="1" max="24">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="cleanup_interval" class="form-label">فترة التنظيف (بالثواني)</label>
                                    <input type="number" class="form-control" id="cleanup_interval" name="cleanup_interval" 
                                           value="<?php echo defined('TRIAL_CLEANUP_INTERVAL') ? TRIAL_CLEANUP_INTERVAL : 300; ?>" min="60">
                                </div>
                            </div>
                            <button type="submit" name="update_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </form>
                    </div>

                    <!-- أدوات الإدارة -->
                    <div class="settings-section">
                        <h5 class="mb-3">
                            <i class="fas fa-tools me-2"></i>
                            أدوات الإدارة
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <a href="api/trial-cleanup.php" class="btn btn-warning w-100" target="_blank">
                                    <i class="fas fa-broom me-2"></i>
                                    تنظيف البيانات
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="fix-trial-database.php" class="btn btn-info w-100" target="_blank">
                                    <i class="fas fa-database me-2"></i>
                                    إصلاح قاعدة البيانات
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="setup-trial-system.php" class="btn btn-secondary w-100" target="_blank">
                                    <i class="fas fa-cog me-2"></i>
                                    إعداد النظام
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- آخر الحسابات التجريبية -->
                    <?php if (!isset($db_error) && !empty($recent_trials)): ?>
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-history me-2"></i>
                                آخر الحسابات التجريبية
                            </h5>
                            
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>اسم المحل</th>
                                            <th>المالك</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>تاريخ البدء</th>
                                            <th>تاريخ الانتهاء</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_trials as $trial): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($trial['business_name']); ?></td>
                                                <td><?php echo htmlspecialchars($trial['owner_name']); ?></td>
                                                <td><?php echo htmlspecialchars($trial['email']); ?></td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($trial['trial_start'])); ?></td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($trial['trial_end'])); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $trial['status'] === 'نشط' ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $trial['status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- روابط سريعة -->
                    <div class="text-center mt-4">
                        <a href="index.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                        <a href="trial-register.php" class="btn btn-outline-success">
                            <i class="fas fa-rocket me-2"></i>
                            تجربة النظام
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
