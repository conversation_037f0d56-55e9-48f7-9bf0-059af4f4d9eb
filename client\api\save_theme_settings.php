<?php
/**
 * API لحفظ إعدادات المظهر والألوان
 */

header('Content-Type: application/json; charset=utf-8');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مسموح بالوصول'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

require_once __DIR__ . '/../../config/database.php';

$client_id = $_SESSION['client_id'];

try {
    // جلب البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        // إذا لم تكن البيانات JSON، جرب POST العادي
        $input = $_POST;
    }
    
    $primary_color = trim($input['primary_color'] ?? '');
    $secondary_color = trim($input['secondary_color'] ?? '');
    $accent_color = trim($input['accent_color'] ?? '');
    $header_style = trim($input['header_style'] ?? 'top');
    $sidebar_position = trim($input['sidebar_position'] ?? 'right');
    $theme_mode = trim($input['theme_mode'] ?? 'light');

    // التحقق من صحة البيانات
    if (empty($primary_color) || empty($secondary_color) || empty($accent_color)) {
        throw new Exception('جميع الألوان مطلوبة');
    }

    // التحقق من صحة الألوان
    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $primary_color)) {
        throw new Exception('اللون الأساسي غير صحيح');
    }
    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $secondary_color)) {
        throw new Exception('اللون الثانوي غير صحيح');
    }
    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $accent_color)) {
        throw new Exception('اللون المميز غير صحيح');
    }

    // التحقق من القيم المسموحة
    $allowed_header_styles = ['top', 'sidebar'];
    $allowed_sidebar_positions = ['right', 'left'];
    $allowed_theme_modes = ['light', 'dark', 'auto'];

    if (!in_array($header_style, $allowed_header_styles)) {
        $header_style = 'top';
    }
    if (!in_array($sidebar_position, $allowed_sidebar_positions)) {
        $sidebar_position = 'right';
    }
    if (!in_array($theme_mode, $allowed_theme_modes)) {
        $theme_mode = 'light';
    }

    // إنشاء جدول إعدادات المظهر إذا لم يكن موجوداً
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS client_theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            primary_color VARCHAR(7) DEFAULT '#0d6efd',
            secondary_color VARCHAR(7) DEFAULT '#6c757d',
            accent_color VARCHAR(7) DEFAULT '#20c997',
            header_style ENUM('top', 'sidebar') DEFAULT 'top',
            sidebar_position ENUM('right', 'left') DEFAULT 'right',
            theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
        )
    ");

    // حفظ أو تحديث إعدادات المظهر
    $theme_stmt = $pdo->prepare("
        INSERT INTO client_theme_settings 
        (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        primary_color = VALUES(primary_color),
        secondary_color = VALUES(secondary_color),
        accent_color = VALUES(accent_color),
        header_style = VALUES(header_style),
        sidebar_position = VALUES(sidebar_position),
        theme_mode = VALUES(theme_mode),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    $result = $theme_stmt->execute([
        $client_id, $primary_color, $secondary_color, $accent_color, 
        $header_style, $sidebar_position, $theme_mode
    ]);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ إعدادات المظهر بنجاح',
            'data' => [
                'primary_color' => $primary_color,
                'secondary_color' => $secondary_color,
                'accent_color' => $accent_color,
                'header_style' => $header_style,
                'sidebar_position' => $sidebar_position,
                'theme_mode' => $theme_mode
            ]
        ]);
    } else {
        throw new Exception('فشل في حفظ الإعدادات');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
