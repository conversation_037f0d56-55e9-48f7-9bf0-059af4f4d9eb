<?php
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');

    session_start();
}

class SecurityManager {

    private $pdo;
    private $max_login_attempts = 5;
    private $lockout_time = 900;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->initSecurityTables();
    }

    private function initSecurityTables() {
        try {
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS login_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    user_type ENUM('admin', 'client', 'employee') NOT NULL,
                    username VARCHAR(255),
                    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT FALSE,
                    user_agent TEXT,
                    INDEX idx_ip_time (ip_address, attempt_time),
                    INDEX idx_username_time (username, attempt_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS suspicious_activities (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    activity_type VARCHAR(100) NOT NULL,
                    description TEXT,
                    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    blocked BOOLEAN DEFAULT FALSE,
                    INDEX idx_ip_activity (ip_address, activity_type),
                    INDEX idx_severity_time (severity, created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS blocked_ips (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL UNIQUE,
                    reason TEXT,
                    blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    blocked_until TIMESTAMP NULL,
                    permanent BOOLEAN DEFAULT FALSE,
                    INDEX idx_ip_until (ip_address, blocked_until)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

        } catch (PDOException $e) {
            error_log("Security tables creation failed: " . $e->getMessage());
        }
    }

    public function isIpBlocked($ip = null) {
        $ip = $ip ?: $this->getClientIp();

        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM blocked_ips
                WHERE ip_address = ?
                AND (permanent = 1 OR blocked_until > NOW())
            ");
            $stmt->execute([$ip]);

            return $stmt->fetch() !== false;

        } catch (PDOException $e) {
            error_log("IP block check failed: " . $e->getMessage());
            return false;
        }
    }

    public function checkLoginAttempts($username, $user_type, $ip = null) {
        $ip = $ip ?: $this->getClientIp();

        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as attempts
                FROM login_attempts
                WHERE (ip_address = ? OR username = ?)
                AND user_type = ?
                AND success = FALSE
                AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$ip, $username, $user_type, $this->lockout_time]);

            $result = $stmt->fetch();
            $attempts = $result['attempts'];

            if ($attempts >= $this->max_login_attempts) {
                $this->blockIp($ip, "تجاوز الحد الأقصى لمحاولات تسجيل الدخول", $this->lockout_time);
                return false;
            }

            return true;

        } catch (PDOException $e) {
            error_log("Login attempts check failed: " . $e->getMessage());
            return true;
        }
    }

    public function logLoginAttempt($username, $user_type, $success, $ip = null) {
        $ip = $ip ?: $this->getClientIp();

        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO login_attempts
                (ip_address, user_type, username, success, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $ip,
                $user_type,
                $username,
                $success,
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

        } catch (PDOException $e) {
            error_log("Login attempt logging failed: " . $e->getMessage());
        }
    }

    public function blockIp($ip, $reason, $duration = null) {
        try {
            $blocked_until = $duration ? date('Y-m-d H:i:s', time() + $duration) : null;

            $stmt = $this->pdo->prepare("
                INSERT INTO blocked_ips (ip_address, reason, blocked_until, permanent)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                reason = VALUES(reason),
                blocked_until = VALUES(blocked_until),
                blocked_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$ip, $reason, $blocked_until, $duration === null]);

            $this->logSuspiciousActivity($ip, 'ip_blocked', $reason, 'high');

        } catch (PDOException $e) {
            error_log("IP blocking failed: " . $e->getMessage());
        }
    }

    public function logSuspiciousActivity($ip, $activity_type, $description, $severity = 'medium') {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO suspicious_activities
                (ip_address, activity_type, description, severity)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$ip, $activity_type, $description, $severity]);

        } catch (PDOException $e) {
            error_log("Suspicious activity logging failed: " . $e->getMessage());
        }
    }

    public function getClientIp() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED',
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED',
                   'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP,
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    public function generateCsrfToken($form_name = 'default') {
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
        }

        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_tokens'][$form_name] = [
            'token' => $token,
            'timestamp' => time()
        ];

        $this->cleanupOldTokens();

        return $token;
    }

    public function validateCsrfToken($token, $form_name = 'default') {
        if (!isset($_SESSION['csrf_tokens'][$form_name])) {
            return false;
        }

        $stored_token = $_SESSION['csrf_tokens'][$form_name];

        if (time() - $stored_token['timestamp'] > 3600) {
            unset($_SESSION['csrf_tokens'][$form_name]);
            return false;
        }

        $valid = hash_equals($stored_token['token'], $token);

        if ($valid) {
            unset($_SESSION['csrf_tokens'][$form_name]);
        }

        return $valid;
    }

    private function cleanupOldTokens() {
        if (!isset($_SESSION['csrf_tokens'])) {
            return;
        }

        $current_time = time();
        foreach ($_SESSION['csrf_tokens'] as $form_name => $token_data) {
            if ($current_time - $token_data['timestamp'] > 3600) {
                unset($_SESSION['csrf_tokens'][$form_name]);
            }
        }
    }

    public function getCsrfTokenInput($form_name = 'default') {
        $token = $this->generateCsrfToken($form_name);
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }

    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }

        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    public function validatePassword($password) {
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password);
    }

    public function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }

    public function cleanupOldRecords() {
        try {
            $this->pdo->exec("
                DELETE FROM login_attempts
                WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");

            $this->pdo->exec("
                DELETE FROM suspicious_activities
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
            ");

            $this->pdo->exec("
                DELETE FROM blocked_ips
                WHERE permanent = FALSE AND blocked_until < NOW()
            ");

        } catch (PDOException $e) {
            error_log("Security cleanup failed: " . $e->getMessage());
        }
    }
}

if (isset($pdo) && !isset($security)) {
    $security = new SecurityManager($pdo);

    if ($security->isIpBlocked()) {
        http_response_code(403);
        die('تم حظر وصولك مؤقتاً بسبب أنشطة مشبوهة');
    }

    if (!isset($_SESSION['last_cleanup']) ||
        $_SESSION['last_cleanup'] < strtotime('-1 day')) {
        $security->cleanupOldRecords();
        $_SESSION['last_cleanup'] = time();
    }
}
