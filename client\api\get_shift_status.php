<?php
/**
 * API للحصول على حالة الوردية الحالية للموظف
 * يستخدم لتحديث واجهة المستخدم بشكل تلقائي
 */

header('Content-Type: application/json; charset=utf-8');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../../includes/shift_helpers.php';

// التحقق من تسجيل الدخول كموظف
if (!isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'غير مصرح - يجب تسجيل الدخول كموظف'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $employee_id = $_SESSION['employee_id'];
    
    // جلب حالة الحضور الحالية
    $attendance_status = isEmployeeCheckedIn($pdo, $employee_id);
    
    // جلب معلومات إضافية عن الوردية إذا كان مسجل حضور
    $shift_details = null;
    if ($attendance_status['is_checked_in']) {
        $stmt = $pdo->prepare("
            SELECT 
                s.shift_name,
                s.shift_date,
                s.start_time,
                s.end_time,
                sa.check_in_time,
                sa.check_out_time,
                sa.break_start_time,
                sa.break_end_time,
                TIMESTAMPDIFF(MINUTE, sa.check_in_time, CURRENT_TIMESTAMP) as minutes_worked,
                CASE 
                    WHEN sa.break_start_time IS NOT NULL AND sa.break_end_time IS NULL THEN 'on_break'
                    WHEN sa.check_out_time IS NOT NULL THEN 'checked_out'
                    ELSE 'working'
                END as current_status
            FROM shift_attendance sa
            JOIN shifts s ON sa.shift_id = s.shift_id
            WHERE sa.employee_id = ? 
            AND sa.check_in_time IS NOT NULL
            AND sa.check_out_time IS NULL
            ORDER BY sa.check_in_time DESC
            LIMIT 1
        ");
        
        $stmt->execute([$employee_id]);
        $shift_details = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // جلب الورديات القادمة
    $upcoming_shifts = [];
    $stmt = $pdo->prepare("
        SELECT 
            s.shift_name,
            s.shift_date,
            s.start_time,
            s.end_time,
            es.role_in_shift,
            es.is_mandatory
        FROM employee_shifts es
        JOIN shifts s ON es.shift_id = s.shift_id
        WHERE es.employee_id = ? 
        AND s.shift_date >= CURDATE()
        AND s.status IN ('scheduled', 'active')
        ORDER BY s.shift_date, s.start_time
        LIMIT 3
    ");
    
    $stmt->execute([$employee_id]);
    $upcoming_shifts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحضير الاستجابة
    $response = [
        'success' => true,
        'status' => [
            'is_checked_in' => $attendance_status['is_checked_in'],
            'status' => $attendance_status['status'],
            'message' => $attendance_status['message']
        ],
        'current_time' => date('Y-m-d H:i:s'),
        'upcoming_shifts' => $upcoming_shifts
    ];
    
    // إضافة تفاصيل الوردية الحالية إذا كان مسجل حضور
    if ($shift_details) {
        $response['current_shift'] = [
            'shift_name' => $shift_details['shift_name'],
            'shift_date' => $shift_details['shift_date'],
            'start_time' => $shift_details['start_time'],
            'end_time' => $shift_details['end_time'],
            'check_in_time' => date('H:i', strtotime($shift_details['check_in_time'])),
            'minutes_worked' => (int)$shift_details['minutes_worked'],
            'hours_worked' => round($shift_details['minutes_worked'] / 60, 2),
            'current_status' => $shift_details['current_status'],
            'is_on_break' => $shift_details['current_status'] === 'on_break',
            'break_start_time' => $shift_details['break_start_time'] ? date('H:i', strtotime($shift_details['break_start_time'])) : null
        ];
        
        // حساب الوقت المتبقي في الوردية
        $end_datetime = new DateTime($shift_details['shift_date'] . ' ' . $shift_details['end_time']);
        $now = new DateTime();
        
        if ($end_datetime > $now) {
            $diff = $now->diff($end_datetime);
            $response['current_shift']['time_remaining'] = [
                'hours' => $diff->h,
                'minutes' => $diff->i,
                'total_minutes' => ($diff->h * 60) + $diff->i
            ];
        } else {
            $response['current_shift']['time_remaining'] = [
                'hours' => 0,
                'minutes' => 0,
                'total_minutes' => 0,
                'overtime' => true
            ];
        }
    }
    
    // إضافة إحصائيات سريعة
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN s.shift_date = CURDATE() THEN 1 END) as today_shifts,
            COUNT(CASE WHEN s.shift_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_shifts,
            COUNT(CASE WHEN s.shift_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_shifts,
            SUM(CASE WHEN sa.status = 'present' AND s.shift_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN sa.actual_hours ELSE 0 END) as month_hours
        FROM employee_shifts es
        JOIN shifts s ON es.shift_id = s.shift_id
        LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
        WHERE es.employee_id = ?
    ");
    
    $stats_stmt->execute([$employee_id]);
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    $response['statistics'] = [
        'today_shifts' => (int)($stats['today_shifts'] ?? 0),
        'week_shifts' => (int)($stats['week_shifts'] ?? 0),
        'month_shifts' => (int)($stats['month_shifts'] ?? 0),
        'month_hours' => round($stats['month_hours'] ?? 0, 2)
    ];
    
    // إضافة تحذيرات أو إشعارات
    $notifications = [];
    
    // تحذير إذا كان في وردية لأكثر من 8 ساعات
    if ($shift_details && $shift_details['minutes_worked'] > 480) {
        $notifications[] = [
            'type' => 'warning',
            'message' => 'لقد تجاوزت 8 ساعات في الوردية',
            'icon' => 'fas fa-exclamation-triangle'
        ];
    }
    
    // تذكير بالاستراحة إذا عمل لأكثر من 4 ساعات بدون استراحة
    if ($shift_details && $shift_details['minutes_worked'] > 240 && !$shift_details['break_start_time']) {
        $notifications[] = [
            'type' => 'info',
            'message' => 'حان وقت الاستراحة - لقد عملت لأكثر من 4 ساعات',
            'icon' => 'fas fa-coffee'
        ];
    }
    
    // إشعار بقرب انتهاء الوردية
    if (isset($response['current_shift']['time_remaining']['total_minutes']) && 
        $response['current_shift']['time_remaining']['total_minutes'] <= 30 && 
        $response['current_shift']['time_remaining']['total_minutes'] > 0) {
        $notifications[] = [
            'type' => 'warning',
            'message' => 'ستنتهي ورديتك خلال ' . $response['current_shift']['time_remaining']['total_minutes'] . ' دقيقة',
            'icon' => 'fas fa-clock'
        ];
    }
    
    $response['notifications'] = $notifications;
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
