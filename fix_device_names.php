<?php
/**
 * سكريپت إصلاح أسماء الأجهزة
 * يصلح الأجهزة التي تحتوي على أسماء عملاء أو أسماء غير مناسبة
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح أسماء الأجهزة - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-warning text-dark'>
                    <h3 class='mb-0'>
                        <i class='fas fa-wrench me-2'></i>
                        إصلاح أسماء الأجهزة
                    </h3>
                </div>
                <div class='card-body'>";

try {
    echo "<h5>1. البحث عن الأجهزة التي تحتاج إصلاح</h5>";
    
    // البحث عن الأجهزة المشبوهة
    $suspicious_devices_query = $pdo->query("
        SELECT device_id, device_name, device_type, client_id
        FROM devices
        WHERE device_name REGEXP '^[أ-ي]+$'
        OR device_name IN (SELECT name FROM customers WHERE name IS NOT NULL)
        OR LENGTH(device_name) < 3
        OR device_name REGEXP '^[A-Za-z]{1,2}$'
    ");
    $suspicious_devices = $suspicious_devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($suspicious_devices)) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                تم العثور على " . count($suspicious_devices) . " جهاز يحتاج إصلاح
              </div>";
        
        echo "<div class='table-responsive'>
                <table class='table table-bordered'>
                    <thead class='table-light'>
                        <tr>
                            <th>ID</th>
                            <th>الاسم الحالي</th>
                            <th>النوع</th>
                            <th>الاسم الجديد</th>
                            <th>الإجراء</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        // أسماء الأجهزة المناسبة حسب النوع
        $device_names = [
            'PS5' => 'PlayStation 5',
            'PS4' => 'PlayStation 4', 
            'PC' => 'Gaming PC',
            'XBOX' => 'Xbox Series',
            'SWITCH' => 'Nintendo Switch',
            'VR' => 'VR Headset'
        ];
        
        $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
        $counter = 1;
        
        foreach ($suspicious_devices as $device) {
            $device_type = strtoupper($device['device_type']);
            $base_name = $device_names[$device_type] ?? $device_type;
            $new_name = $base_name . ' - ' . $counter;
            
            echo "<tr>";
            echo "<td>" . $device['device_id'] . "</td>";
            echo "<td style='color: red; font-weight: bold;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td>" . htmlspecialchars($device['device_type']) . "</td>";
            echo "<td style='color: green; font-weight: bold;'>" . htmlspecialchars($new_name) . "</td>";
            
            // تطبيق الإصلاح
            try {
                $update_stmt->execute([$new_name, $device['device_id']]);
                echo "<td><span class='badge bg-success'>تم الإصلاح</span></td>";
                $counter++;
            } catch (PDOException $e) {
                echo "<td><span class='badge bg-danger'>فشل الإصلاح</span></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</tbody></table></div>";
        
        echo "<div class='alert alert-success mt-3'>
                <i class='fas fa-check-circle me-2'></i>
                تم إصلاح أسماء الأجهزة بنجاح!
              </div>";
        
    } else {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                جميع أسماء الأجهزة صحيحة ولا تحتاج إصلاح
              </div>";
    }
    
    echo "<h5>2. التحقق من النتائج</h5>";
    
    // عرض الأجهزة بعد الإصلاح
    $devices_query = $pdo->query("
        SELECT device_id, device_name, device_type, status
        FROM devices
        ORDER BY device_type, device_name
        LIMIT 20
    ");
    $devices = $devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($devices)) {
        echo "<div class='table-responsive'>
                <table class='table table-sm'>
                    <thead class='table-light'>
                        <tr>
                            <th>ID</th>
                            <th>اسم الجهاز</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($devices as $device) {
            $status_class = 'danger';
            $status_text = 'مشغول';

            if ($device['status'] === 'available') {
                $status_class = 'success';
                $status_text = 'متاح';
            } elseif ($device['status'] === 'maintenance') {
                $status_class = 'warning';
                $status_text = 'صيانة';
            }
            
            echo "<tr>
                    <td>{$device['device_id']}</td>
                    <td>" . htmlspecialchars($device['device_name']) . "</td>
                    <td>" . htmlspecialchars($device['device_type']) . "</td>
                    <td><span class='badge bg-$status_class'>$status_text</span></td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
    echo "<h5>3. نصائح لتجنب المشكلة مستقبلاً</h5>";
    
    echo "<div class='alert alert-info'>
            <h6><i class='fas fa-lightbulb me-2'></i>أفضل الممارسات لتسمية الأجهزة:</h6>
            <ul class='mb-0'>
                <li><strong>استخدم أسماء وصفية:</strong> PlayStation 5 - الصالة الرئيسية</li>
                <li><strong>أضف أرقام للتمييز:</strong> Gaming PC - 1, Gaming PC - 2</li>
                <li><strong>اذكر الموقع:</strong> Xbox Series - الطابق الثاني</li>
                <li><strong>تجنب الأسماء العربية:</strong> لا تستخدم أسماء مثل 'وائل' أو 'أحمد'</li>
                <li><strong>كن متسقاً:</strong> استخدم نفس نمط التسمية لجميع الأجهزة</li>
            </ul>
          </div>";
    
    echo "<div class='alert alert-warning'>
            <h6><i class='fas fa-exclamation-triangle me-2'></i>تحذير مهم:</h6>
            <p class='mb-0'>تأكد من تدريب الموظفين على إدخال أسماء الأجهزة بشكل صحيح عند إضافة أجهزة جديدة أو تعديل الموجودة.</p>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='fix_data_display_issue.php' class='btn btn-info me-2'>
                <i class='fas fa-search me-1'></i>فحص شامل للبيانات
            </a>
            <a href='client/devices.php' class='btn btn-primary me-2'>
                <i class='fas fa-gamepad me-1'></i>إدارة الأجهزة
            </a>
            <a href='client/sessions.php' class='btn btn-success me-2'>
                <i class='fas fa-play-circle me-1'></i>اختبار الجلسات
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>يرجى التحقق من:</p>
            <ul>
                <li>إعدادات قاعدة البيانات</li>
                <li>صلاحيات المستخدم</li>
                <li>وجود الجداول المطلوبة</li>
            </ul>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
