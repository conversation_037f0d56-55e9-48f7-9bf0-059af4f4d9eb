/**
 * إدارة الورديات - JavaScript
 * تحسين تجربة المستخدم مع أزرار فتح/إنهاء الوردية
 */

document.addEventListener('DOMContentLoaded', function() {
    // تحديث حالة الوردية كل 30 ثانية
    setInterval(updateShiftStatus, 30000);
    
    // إضافة تأكيد لأزرار الوردية
    setupShiftButtons();
    
    // إضافة مؤشر الوقت الحالي
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
});

/**
 * إعداد أزرار الوردية مع التأكيدات
 */
function setupShiftButtons() {
    // زر فتح الوردية
    const startShiftBtn = document.querySelector('button[name="action"][value="start_shift"]');
    if (startShiftBtn) {
        startShiftBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            Swal.fire({
                title: 'فتح الوردية',
                text: 'هل أنت متأكد من فتح الوردية وتسجيل الحضور؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، فتح الوردية',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // إظهار مؤشر التحميل
                    Swal.fire({
                        title: 'جاري فتح الوردية...',
                        text: 'يرجى الانتظار',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    // إرسال النموذج
                    this.closest('form').submit();
                }
            });
        });
    }
    
    // زر إنهاء الوردية
    const endShiftBtn = document.querySelector('button[name="action"][value="end_shift"]');
    if (endShiftBtn) {
        endShiftBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            Swal.fire({
                title: 'إنهاء الوردية',
                text: 'هل أنت متأكد من إنهاء الوردية وتسجيل الانصراف؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، إنهاء الوردية',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // إظهار مؤشر التحميل
                    Swal.fire({
                        title: 'جاري إنهاء الوردية...',
                        text: 'يرجى الانتظار',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    // إرسال النموذج
                    this.closest('form').submit();
                }
            });
        });
    }
}

/**
 * تحديث حالة الوردية عبر AJAX
 */
function updateShiftStatus() {
    // التحقق من وجود قسم إدارة الوردية
    const shiftCard = document.querySelector('.card.border-primary');
    if (!shiftCard) return;
    
    fetch('api/get_shift_status.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateShiftDisplay(data.status);
            }
        })
        .catch(error => {
            console.log('تحديث حالة الوردية:', error);
        });
}

/**
 * تحديث عرض حالة الوردية
 */
function updateShiftDisplay(status) {
    const statusBadge = document.querySelector('.badge');
    const statusText = document.querySelector('.card-body h6');
    const statusSubtext = document.querySelector('.card-body small');
    
    if (!statusBadge || !statusText || !statusSubtext) return;
    
    if (status.is_checked_in) {
        statusBadge.className = 'badge bg-success fs-6 px-3 py-2';
        statusBadge.innerHTML = '<i class="fas fa-check-circle me-1"></i>في الوردية';
        statusText.textContent = status.shift_name;
        statusSubtext.innerHTML = `بدأت في: ${status.check_in_time}`;
        
        if (status.status === 'on_break') {
            statusSubtext.innerHTML += ' | <span class="text-warning">في استراحة</span>';
        }
    } else {
        statusBadge.className = 'badge bg-warning fs-6 px-3 py-2';
        statusBadge.innerHTML = '<i class="fas fa-clock me-1"></i>خارج الوردية';
        statusText.textContent = 'لم يتم فتح الوردية بعد';
        statusSubtext.textContent = 'اضغط على "فتح الوردية" لبدء العمل';
    }
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    // تحديث عنوان الصفحة
    const originalTitle = document.title.split(' - ')[1] || document.title;
    document.title = `${timeString} - ${originalTitle}`;
    
    // إضافة الوقت في مكان مناسب إذا وجد
    const timeDisplay = document.getElementById('current-time-display');
    if (timeDisplay) {
        timeDisplay.textContent = timeString;
    }
}

/**
 * إضافة تأثيرات بصرية للأزرار
 */
function addButtonEffects() {
    const shiftButtons = document.querySelectorAll('button[name="action"]');
    
    shiftButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

/**
 * إضافة إشعارات للموظف
 */
function showShiftNotifications() {
    // التحقق من وجود ورديات قادمة
    const now = new Date();
    const currentHour = now.getHours();
    
    // إشعار قبل نهاية الوردية بساعة
    if (currentHour >= 15 && currentHour < 16) { // مثال: إذا كانت الوردية تنتهي في 4 عصراً
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show';
        notification.innerHTML = `
            <i class="fas fa-clock me-2"></i>
            <strong>تذكير:</strong> ستنتهي ورديتك خلال ساعة واحدة.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(notification, container.firstChild);
        }
    }
}

/**
 * حفظ حالة الوردية في localStorage
 */
function saveShiftState(state) {
    localStorage.setItem('shift_state', JSON.stringify({
        ...state,
        timestamp: Date.now()
    }));
}

/**
 * جلب حالة الوردية من localStorage
 */
function getShiftState() {
    const saved = localStorage.getItem('shift_state');
    if (!saved) return null;
    
    const state = JSON.parse(saved);
    const now = Date.now();
    
    // إذا مر أكثر من 5 دقائق، احذف البيانات المحفوظة
    if (now - state.timestamp > 5 * 60 * 1000) {
        localStorage.removeItem('shift_state');
        return null;
    }
    
    return state;
}

// تشغيل التأثيرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addButtonEffects();
    showShiftNotifications();
});

// إضافة مستمع للأحداث المخصصة
document.addEventListener('shiftStatusChanged', function(e) {
    const { status, action } = e.detail;
    
    // إظهار إشعار نجاح
    if (action === 'start_shift') {
        Swal.fire({
            icon: 'success',
            title: 'تم فتح الوردية!',
            text: 'يمكنك الآن بدء العمل وإنشاء الجلسات',
            timer: 3000,
            showConfirmButton: false
        });
    } else if (action === 'end_shift') {
        Swal.fire({
            icon: 'success',
            title: 'تم إنهاء الوردية!',
            text: 'شكراً لك على عملك اليوم',
            timer: 3000,
            showConfirmButton: false
        });
    }
    
    // حفظ الحالة الجديدة
    saveShiftState(status);
});
