<?php
/**
 * تشخيص مشكلة عرض المبالغ في صفحة الماليات - PlayGood
 * المشكلة: اسم العميل يظهر بدلاً من المبلغ
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص مشكلة عرض المبالغ في صفحة الماليات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    
    // 1. فحص جداول الماليات
    echo "<h2>1. فحص وجود جداول الماليات</h2>";
    
    $tables_to_check = ['expense_types', 'expenses', 'income_types', 'additional_income'];
    $tables_exist = true;
    
    foreach ($tables_to_check as $table) {
        try {
            $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ جدول $table غير موجود: " . $e->getMessage() . "</p>";
            $tables_exist = false;
        }
    }
    
    if (!$tables_exist) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ جداول الماليات غير موجودة</h4>";
        echo "<p>يجب إنشاء جداول الماليات أولاً. قم بتشغيل ملف create_finances_tables.php</p>";
        echo "</div>";
        exit;
    }
    
    // 2. فحص البيانات في جداول الماليات
    echo "<h2>2. فحص البيانات في جداول الماليات</h2>";
    
    // فحص المصروفات
    echo "<h3>أ. فحص المصروفات</h3>";
    try {
        $expenses_query = $pdo->prepare("
            SELECT e.*, et.name as expense_type_name
            FROM expenses e
            JOIN expense_types et ON e.expense_type_id = et.id
            WHERE e.client_id = ?
            ORDER BY e.expense_date DESC
            LIMIT 5
        ");
        $expenses_query->execute([$client_id]);
        $expenses = $expenses_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($expenses)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>المبلغ</th>";
            echo "<th style='padding: 8px;'>التاريخ</th>";
            echo "<th style='padding: 8px;'>نوع البيانات</th>";
            echo "</tr>";
            
            foreach ($expenses as $expense) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $expense['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($expense['expense_type_name']) . "</td>";
                echo "<td style='padding: 8px; color: red; font-weight: bold;'>" . $expense['amount'] . "</td>";
                echo "<td style='padding: 8px;'>" . $expense['expense_date'] . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . gettype($expense['amount']) . " - " . (is_numeric($expense['amount']) ? "رقم ✅" : "ليس رقم ❌") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد مصروفات مسجلة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص المصروفات: " . $e->getMessage() . "</p>";
    }
    
    // فحص الإيرادات الإضافية
    echo "<h3>ب. فحص الإيرادات الإضافية</h3>";
    try {
        $income_query = $pdo->prepare("
            SELECT ai.*, it.name as income_type_name
            FROM additional_income ai
            JOIN income_types it ON ai.income_type_id = it.id
            WHERE ai.client_id = ?
            ORDER BY ai.income_date DESC
            LIMIT 5
        ");
        $income_query->execute([$client_id]);
        $income = $income_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($income)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>المبلغ</th>";
            echo "<th style='padding: 8px;'>التاريخ</th>";
            echo "<th style='padding: 8px;'>نوع البيانات</th>";
            echo "</tr>";
            
            foreach ($income as $inc) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $inc['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($inc['income_type_name']) . "</td>";
                echo "<td style='padding: 8px; color: green; font-weight: bold;'>" . $inc['amount'] . "</td>";
                echo "<td style='padding: 8px;'>" . $inc['income_date'] . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . gettype($inc['amount']) . " - " . (is_numeric($inc['amount']) ? "رقم ✅" : "ليس رقم ❌") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد إيرادات إضافية مسجلة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الإيرادات: " . $e->getMessage() . "</p>";
    }

    // 3. فحص إيرادات الجلسات
    echo "<h2>3. فحص إيرادات الجلسات</h2>";

    try {
        $sessions_income_query = $pdo->prepare("
            SELECT
                s.session_id,
                s.total_cost,
                d.device_name,
                c.name as customer_name,
                s.start_time,
                s.status
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = ?
            AND s.status = 'completed'
            AND s.total_cost > 0
            ORDER BY s.start_time DESC
            LIMIT 5
        ");
        $sessions_income_query->execute([$client_id]);
        $sessions_income = $sessions_income_query->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($sessions_income)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>Session ID</th>";
            echo "<th style='padding: 8px;'>الجهاز</th>";
            echo "<th style='padding: 8px;'>العميل</th>";
            echo "<th style='padding: 8px;'>التكلفة</th>";
            echo "<th style='padding: 8px;'>نوع البيانات</th>";
            echo "<th style='padding: 8px;'>فحص الخلط</th>";
            echo "</tr>";

            foreach ($sessions_income as $session) {
                $is_mixed = ($session['device_name'] === $session['customer_name']);
                $cost_is_numeric = is_numeric($session['total_cost']);

                echo "<tr" . ($is_mixed ? " style='background: #ffe6e6;'" : "") . ">";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . htmlspecialchars($session['device_name'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: green; font-weight: bold;'>" . $session['total_cost'] . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . gettype($session['total_cost']) . " - " . ($cost_is_numeric ? "رقم ✅" : "ليس رقم ❌") . "</td>";
                echo "<td style='padding: 8px; color: " . ($is_mixed ? "red" : "green") . ";'>" . ($is_mixed ? "مختلط ❌" : "طبيعي ✅") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات مكتملة بتكلفة</p>";
        }

    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص إيرادات الجلسات: " . $e->getMessage() . "</p>";
    }

    // 4. فحص الملخص المالي (نفس الاستعلامات المستخدمة في صفحة الماليات)
    echo "<h2>4. فحص الملخص المالي</h2>";

    try {
        $current_month = date('Y-m');

        // إجمالي المصروفات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM expenses
            WHERE client_id = ? AND DATE_FORMAT(expense_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_expenses = $stmt->fetchColumn();

        // إجمالي الإيرادات الإضافية
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_additional_income
            FROM additional_income
            WHERE client_id = ? AND DATE_FORMAT(income_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_additional_income = $stmt->fetchColumn();

        // إجمالي إيرادات الجلسات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(s.total_cost), 0) as total_sessions_income
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ?
            AND s.status = 'completed'
            AND DATE_FORMAT(s.start_time, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $total_sessions_income = $stmt->fetchColumn();

        // حساب صافي الربح
        $total_income = $total_sessions_income + $total_additional_income;
        $net_profit = $total_income - $total_expenses;

        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📊 الملخص المالي للشهر الحالي (" . date('F Y') . ")</h4>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>البند</th>";
        echo "<th style='padding: 8px;'>القيمة</th>";
        echo "<th style='padding: 8px;'>نوع البيانات</th>";
        echo "<th style='padding: 8px;'>التنسيق</th>";
        echo "</tr>";

        $financial_items = [
            ['إجمالي المصروفات', $total_expenses, 'danger'],
            ['إيرادات الجلسات', $total_sessions_income, 'success'],
            ['الإيرادات الإضافية', $total_additional_income, 'success'],
            ['صافي الربح', $net_profit, $net_profit >= 0 ? 'success' : 'danger']
        ];

        foreach ($financial_items as $item) {
            $color = $item[2] === 'danger' ? 'red' : 'green';
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $item[0] . "</td>";
            echo "<td style='padding: 8px; color: $color; font-weight: bold;'>" . $item[1] . "</td>";
            echo "<td style='padding: 8px; color: blue;'>" . gettype($item[1]) . " - " . (is_numeric($item[1]) ? "رقم ✅" : "ليس رقم ❌") . "</td>";
            echo "<td style='padding: 8px; color: $color;'>" . number_format($item[1], 2) . " ج.م</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";

    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الملخص المالي: " . $e->getMessage() . "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

// 5. التوصيات والحلول
echo "<h2>5. التوصيات والحلول</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بناءً على النتائج أعلاه:</h4>";
echo "<ol>";
echo "<li><strong>إذا كانت جميع المبالغ تظهر كأرقام:</strong> المشكلة قد تكون في عرض الصفحة أو JavaScript</li>";
echo "<li><strong>إذا كانت هناك بيانات مختلطة:</strong> استخدم ملف fix_data_display_mix.php لإصلاحها</li>";
echo "<li><strong>إذا كانت المبالغ 0:</strong> تحقق من إدخال البيانات في النماذج</li>";
echo "<li><strong>إذا كان نوع البيانات string بدلاً من numeric:</strong> هناك مشكلة في حفظ البيانات</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>راجع نتائج هذا التشخيص</li>";
echo "<li>إذا وُجدت مشاكل، شغل ملف الإصلاح المناسب</li>";
echo "<li>اختبر صفحة الماليات مرة أخرى</li>";
echo "<li>تحقق من ملفات JavaScript إذا استمرت المشكلة</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
