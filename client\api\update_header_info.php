<?php
/**
 * API لتحديث معلومات الـ header
 */

header('Content-Type: application/json; charset=utf-8');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مسجل دخول']);
    exit;
}

try {
    require_once '../../config/database.php';
    
    $is_employee = isset($_SESSION['employee_id']);
    $response = ['success' => true];
    
    if ($is_employee) {
        // جلب بيانات الموظف مع بيانات المحل
        $employee_id = $_SESSION['employee_id'];
        $stmt = $pdo->prepare("
            SELECT e.name as employee_name, e.role, c.business_name, c.owner_name
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.employee_id = ?
        ");
        $stmt->execute([$employee_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data) {
            $response['user_name'] = $data['employee_name'];
            $response['user_role'] = $data['role'];
            $response['business_name'] = $data['business_name'];
            $response['owner_name'] = $data['owner_name'];
            $response['user_type'] = 'employee';
        } else {
            throw new Exception('لم يتم العثور على بيانات الموظف');
        }
    } else {
        // جلب بيانات صاحب المحل
        $client_id = $_SESSION['client_id'];
        $stmt = $pdo->prepare("
            SELECT business_name, owner_name, name
            FROM clients
            WHERE client_id = ?
        ");
        $stmt->execute([$client_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data) {
            $response['business_name'] = $data['business_name'] ?? $data['name'] ?? 'لوحة التحكم';
            $response['owner_name'] = $data['owner_name'] ?? 'مالك المحل';
            $response['user_name'] = $response['owner_name'];
            $response['user_role'] = 'owner';
            $response['user_type'] = 'owner';
        } else {
            throw new Exception('لم يتم العثور على بيانات المحل');
        }
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
