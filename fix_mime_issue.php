<?php
/**
 * إصلاح مشكلة MIME type لملف CSS
 */

echo "<h1>🔧 إصلاح مشكلة MIME Type</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h2>1. تشخيص المشكلة</h2>";
echo "<p>المشكلة: ملف theme-css.php يرجع MIME type خاطئ (application/json بدلاً من text/css)</p>";

echo "<h2>2. اختبار ملف CSS الديناميكي</h2>";
$css_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/theme-css.php';
echo "<p>🔗 رابط CSS: <a href='$css_url' target='_blank'>$css_url</a></p>";

// اختبار headers
$headers = get_headers($css_url, 1);
if ($headers) {
    echo "<h3>Headers المرجعة:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    foreach ($headers as $key => $value) {
        if (is_array($value)) {
            foreach ($value as $v) {
                echo "$key: $v\n";
            }
        } else {
            echo "$key: $value\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p style='color: red;'>❌ فشل في جلب headers</p>";
}

echo "<h2>3. إنشاء ملف CSS ثابت محدث</h2>";

// إنشاء CSS ثابت من الإعدادات الحالية
try {
    require_once 'config/database.php';
    
    $stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
    $stmt->execute([$_SESSION['client_id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$settings) {
        $settings = [
            'primary_color' => '#dc3545',
            'secondary_color' => '#6c757d',
            'accent_color' => '#fd7e14',
            'header_style' => 'top',
            'sidebar_position' => 'right',
            'theme_mode' => 'light'
        ];
    }
    
    echo "<p style='color: green;'>✅ تم جلب الإعدادات من قاعدة البيانات</p>";
    
} catch (Exception $e) {
    $settings = [
        'primary_color' => '#dc3545',
        'secondary_color' => '#6c757d',
        'accent_color' => '#fd7e14',
        'header_style' => 'top',
        'sidebar_position' => 'right',
        'theme_mode' => 'light'
    ];
    echo "<p style='color: orange;'>⚠️ استخدام إعدادات افتراضية</p>";
}

// إنشاء CSS
$css_content = "/* CSS مخصص للمظهر - تم إنشاؤه تلقائياً */

:root {
    --custom-primary: {$settings['primary_color']};
    --custom-primary-light: " . lightenColor($settings['primary_color'], 20) . ";
    --custom-primary-dark: " . darkenColor($settings['primary_color'], 20) . ";
    --custom-secondary: {$settings['secondary_color']};
    --custom-accent: {$settings['accent_color']};
    --custom-accent-light: " . lightenColor($settings['accent_color'], 20) . ";
    --custom-accent-dark: " . darkenColor($settings['accent_color'], 20) . ";
    --custom-primary-rgb: " . hexToRgb($settings['primary_color']) . ";
    --custom-accent-rgb: " . hexToRgb($settings['accent_color']) . ";
}

/* الهيدر والنافبار */
.navbar-dark.bg-primary,
.navbar.bg-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%) !important;
}

.navbar-brand:hover {
    color: var(--custom-accent) !important;
}

.nav-link.active {
    background-color: rgba(var(--custom-accent-rgb), 0.2) !important;
    color: var(--custom-accent) !important;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%);
    border-color: var(--custom-primary);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--custom-primary-dark) 0%, var(--custom-primary) 100%);
    border-color: var(--custom-primary-dark);
}

.btn-success,
.btn-success-gradient {
    background: linear-gradient(135deg, var(--custom-accent) 0%, var(--custom-accent-dark) 100%);
    border-color: var(--custom-accent);
}

.btn-success:hover,
.btn-success-gradient:hover {
    background: linear-gradient(135deg, var(--custom-accent-dark) 0%, var(--custom-accent) 100%);
    border-color: var(--custom-accent-dark);
}

/* البطاقات والعناصر */
.card-header {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-light) 100%);
    color: white;
}

.stats-card {
    border-right-color: var(--custom-primary) !important;
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-right-color: var(--custom-accent) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* النماذج */
.form-control:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

.form-select:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

/* التبويبات */
.nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

.nav-pills .nav-link {
    color: var(--custom-primary);
}

.nav-pills .nav-link:hover {
    background-color: rgba(var(--custom-primary-rgb), 0.1);
}

/* التقدم */
.progress-bar {
    background: linear-gradient(90deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

/* الشارات */
.badge.bg-primary {
    background-color: var(--custom-primary) !important;
}

.badge.bg-success {
    background-color: var(--custom-accent) !important;
}

/* الروابط */
a {
    color: var(--custom-primary);
}

a:hover {
    color: var(--custom-primary-dark);
}

/* تحسينات عامة */
.text-primary {
    color: var(--custom-primary) !important;
}

.text-success {
    color: var(--custom-accent) !important;
}

.section-title {
    color: var(--custom-primary);
}
";

// حفظ CSS في ملف ثابت
$css_file = 'client/assets/css/custom-theme.css';
if (file_put_contents($css_file, $css_content)) {
    echo "<p style='color: green;'>✅ تم تحديث ملف CSS الثابت: $css_file</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في حفظ ملف CSS</p>";
}

echo "<h2>4. تحديث ملف .htaccess</h2>";
$htaccess_content = "
# إعدادات MIME types
AddType text/css .css
AddType text/css .php

# إعدادات خاصة لملف theme-css.php
<Files \"theme-css.php\">
    ForceType text/css
</Files>

# منع التخزين المؤقت لملفات CSS الديناميكية
<FilesMatch \"theme-css\.php$\">
    Header set Cache-Control \"no-cache, no-store, must-revalidate\"
    Header set Pragma \"no-cache\"
    Header set Expires 0
</FilesMatch>
";

$htaccess_file = 'client/api/.htaccess';
if (file_put_contents($htaccess_file, $htaccess_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء ملف .htaccess في مجلد API</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف .htaccess</p>";
}

echo "<h2>5. الحلول المطبقة</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
echo "<h3>✅ تم تطبيق الحلول التالية:</h3>";
echo "<ol>";
echo "<li><strong>ملف CSS ثابت محدث:</strong> client/assets/css/custom-theme.css</li>";
echo "<li><strong>ملف .htaccess:</strong> لإصلاح MIME type</li>";
echo "<li><strong>رابطين CSS في header.php:</strong> ثابت وديناميكي</li>";
echo "<li><strong>إعدادات محدثة:</strong> من قاعدة البيانات</li>";
echo "</ol>";
echo "</div>";

echo "<h2>6. اختبار النظام</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
echo "<h3>🔗 روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='client/quick-test.php' target='_blank'>اختبار سريع للمظهر</a></li>";
echo "<li><a href='client/settings.php' target='_blank'>صفحة الإعدادات</a></li>";
echo "<li><a href='client/dashboard.php' target='_blank'>لوحة التحكم</a></li>";
echo "<li><a href='$css_url' target='_blank'>ملف CSS الديناميكي</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>7. تعليمات إضافية</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<h3>⚠️ إذا لم تظهر التغييرات:</h3>";
echo "<ol>";
echo "<li><strong>امسح ذاكرة المتصفح:</strong> Ctrl+Shift+Delete</li>";
echo "<li><strong>أعد تشغيل الخادم:</strong> إذا كنت تستخدم XAMPP</li>";
echo "<li><strong>تحقق من وحدة تحكم المطور:</strong> F12 > Console</li>";
echo "<li><strong>جرب التصفح الخاص:</strong> للتأكد من عدم وجود تخزين مؤقت</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

// دوال مساعدة
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    return "$r, $g, $b";
}

function lightenColor($hex, $percent) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    $r = min(255, $r + ($percent * 255 / 100));
    $g = min(255, $g + ($percent * 255 / 100));
    $b = min(255, $b + ($percent * 255 / 100));
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}

function darkenColor($hex, $percent) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    $r = max(0, $r - ($percent * 255 / 100));
    $g = max(0, $g - ($percent * 255 / 100));
    $b = max(0, $b - ($percent * 255 / 100));
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

h1 {
    text-align: center;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h2, h3 {
    color: #333;
    margin-bottom: 15px;
}

h2 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-top: 30px;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

pre {
    direction: ltr;
    text-align: left;
}

div[style*="background"] {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}
</style>
