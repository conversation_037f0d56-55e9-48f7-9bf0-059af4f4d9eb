<?php
require_once '../config/database.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول وصلاحية الوصول لهذه الصفحة
checkAdminSession();

$success = '';
$error = '';

// إنشاء جدول إعدادات الأدمن إذا لم يكن موجوداً
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // إدراج الإعدادات الافتراضية
    $pdo->exec("
        INSERT IGNORE INTO admin_settings (setting_key, setting_value, setting_description) VALUES
        ('backup_enabled', '1', 'تفعيل صلاحية النسخ الاحتياطي للعملاء'),
        ('system_maintenance', '0', 'وضع الصيانة للنظام'),
        ('max_backup_files', '10', 'الحد الأقصى لعدد ملفات النسخ الاحتياطي المحفوظة')
    ");
} catch (Exception $e) {
    // تجاهل الأخطاء في حالة وجود الجدول مسبقاً
}

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_system_settings':
            try {
                // تحديث إعدادات النظام العامة
                $timezone = $_POST['timezone'] ?? 'Asia/Riyadh';
                $language = $_POST['language'] ?? 'ar';
                $currency = $_POST['currency'] ?? 'SAR';
                $date_format = $_POST['date_format'] ?? 'Y-m-d';

                // تحديث الإعدادات في قاعدة البيانات
                $settings_to_update = [
                    'timezone' => $timezone,
                    'language' => $language,
                    'currency' => $currency,
                    'date_format' => $date_format
                ];

                foreach ($settings_to_update as $key => $value) {
                    $stmt = $pdo->prepare("
                        INSERT INTO admin_settings (setting_key, setting_value)
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                    ");
                    $stmt->execute([$key, $value]);
                }

                $success = 'تم تحديث إعدادات النظام بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء تحديث الإعدادات: ' . $e->getMessage();
            }
            break;

        case 'update_client_permissions':
            try {
                $backup_enabled = isset($_POST['backup_enabled']) ? '1' : '0';
                $system_maintenance = isset($_POST['system_maintenance']) ? '1' : '0';
                $max_backup_files = intval($_POST['max_backup_files'] ?? 10);

                // تحديث إعدادات صلاحيات العملاء
                $permissions_settings = [
                    'backup_enabled' => $backup_enabled,
                    'system_maintenance' => $system_maintenance,
                    'max_backup_files' => $max_backup_files
                ];

                foreach ($permissions_settings as $key => $value) {
                    $stmt = $pdo->prepare("
                        INSERT INTO admin_settings (setting_key, setting_value)
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                    ");
                    $stmt->execute([$key, $value]);
                }

                $success = 'تم تحديث صلاحيات العملاء بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء تحديث الصلاحيات: ' . $e->getMessage();
            }
            break;

        case 'backup_database':
            try {
                // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                $backupDir = __DIR__ . '/../backups';
                if (!file_exists($backupDir)) {
                    mkdir($backupDir, 0777, true);
                }

                // إنشاء اسم الملف
                $date = date('Y-m-d_H-i-s');
                $backupFile = "{$backupDir}/" . DB_NAME . "_backup_{$date}.sql";

                // تحديد مسار mysqldump
                $mysqldumpPath = 'C:\\xampp\\mysql\\bin\\mysqldump.exe';
                if (!file_exists($mysqldumpPath)) {
                    // محاولة العثور على mysqldump في مسارات أخرى
                    $possiblePaths = [
                        'C:\\xampp\\mysql\\bin\\mysqldump.exe',
                        'C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe',
                        'C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysqldump.exe',
                        'mysqldump' // في حالة وجوده في PATH
                    ];

                    $mysqldumpPath = null;
                    foreach ($possiblePaths as $path) {
                        if ($path === 'mysqldump' || file_exists($path)) {
                            $mysqldumpPath = $path;
                            break;
                        }
                    }

                    if (!$mysqldumpPath) {
                        throw new Exception('لم يتم العثور على mysqldump. يرجى التأكد من تثبيت MySQL بشكل صحيح.');
                    }
                }

                // بناء الأمر
                $command = "\"{$mysqldumpPath}\" --user=" . DB_USER . " --host=" . DB_HOST;
                if (DB_PASS !== '') {
                    $command .= " --password=" . DB_PASS;
                }
                $command .= " --single-transaction --routines --triggers " . DB_NAME . " > \"{$backupFile}\"";

                // تنفيذ الأمر
                exec($command, $output, $return_var);

                if ($return_var === 0 && file_exists($backupFile) && filesize($backupFile) > 0) {
                    // تحديث تاريخ آخر نسخة احتياطية
                    $system_stats['last_backup'] = date('Y-m-d H:i:s');

                    // إنشاء رابط التحميل
                    $downloadLink = 'download_backup.php?file=' . urlencode(basename($backupFile));
                    $success = 'تم إنشاء النسخة الاحتياطية بنجاح. <a href="' . $downloadLink . '" class="btn btn-sm btn-outline-success ms-2"><i class="fas fa-download me-1"></i>تحميل</a>';
                } else {
                    throw new Exception('فشل في إنشاء النسخة الاحتياطية. كود الخطأ: ' . $return_var);
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage();
            }
            break;

        case 'delete_backup':
            try {
                $filename = $_POST['filename'] ?? '';
                if (empty($filename)) {
                    throw new Exception('اسم الملف غير محدد');
                }

                // التحقق من صحة اسم الملف
                if (!preg_match('/^[a-zA-Z0-9_-]+_backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.sql$/', $filename)) {
                    throw new Exception('اسم ملف غير صالح');
                }

                $backupDir = __DIR__ . '/../backups';
                $filePath = $backupDir . '/' . $filename;

                if (!file_exists($filePath)) {
                    throw new Exception('الملف غير موجود');
                }

                if (unlink($filePath)) {
                    $success = 'تم حذف النسخة الاحتياطية بنجاح';
                } else {
                    throw new Exception('فشل في حذف الملف');
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء حذف النسخة الاحتياطية: ' . $e->getMessage();
            }
            break;

        case 'clear_cache':
            try {
                // منطق مسح الذاكرة المؤقتة
                $success = 'تم مسح الذاكرة المؤقتة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء مسح الذاكرة المؤقتة: ' . $e->getMessage();
            }
            break;
    }
}

// جلب الإعدادات الحالية
$current_settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM admin_settings");
    while ($row = $stmt->fetch()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // إعدادات افتراضية في حالة عدم وجود الجدول
    $current_settings = [
        'backup_enabled' => '1',
        'system_maintenance' => '0',
        'max_backup_files' => '10',
        'timezone' => 'Asia/Riyadh',
        'language' => 'ar',
        'currency' => 'SAR',
        'date_format' => 'Y-m-d'
    ];
}

// جلب إحصائيات النظام
try {
    $system_stats = [];

    // حجم قاعدة البيانات
    $stmt = $pdo->query("
        SELECT
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
    ");
    $system_stats['db_size'] = $stmt->fetch()['db_size_mb'] ?? 0;

    // عدد الجداول
    $stmt = $pdo->query("
        SELECT COUNT(*) as table_count
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
    ");
    $system_stats['table_count'] = $stmt->fetch()['table_count'] ?? 0;

    // البحث عن آخر نسخة احتياطية
    $backupDir = __DIR__ . '/../backups';
    $system_stats['last_backup'] = 'لم يتم إنشاء نسخة احتياطية بعد';
    $system_stats['backup_files'] = [];

    if (file_exists($backupDir)) {
        $backupFiles = glob($backupDir . '/*.sql');
        if (!empty($backupFiles)) {
            // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
            usort($backupFiles, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });

            // آخر نسخة احتياطية
            $lastBackup = $backupFiles[0];
            $system_stats['last_backup'] = date('Y-m-d H:i:s', filemtime($lastBackup));

            // قائمة جميع النسخ الاحتياطية (أحدث 10 ملفات)
            foreach (array_slice($backupFiles, 0, 10) as $file) {
                $system_stats['backup_files'][] = [
                    'name' => basename($file),
                    'size' => filesize($file),
                    'date' => filemtime($file),
                    'path' => $file
                ];
            }
        }
    }

    // إصدار MySQL
    $stmt = $pdo->query("SELECT VERSION() as version");
    $system_stats['mysql_version'] = $stmt->fetch()['version'] ?? 'غير معروف';

    // إصدار PHP
    $system_stats['php_version'] = phpversion();

    // مساحة القرص المتاحة
    $system_stats['disk_space'] = round(disk_free_space('.') / 1024 / 1024 / 1024, 2);

} catch (Exception $e) {
    $system_stats = [
        'db_size' => 0,
        'table_count' => 0,
        'last_backup' => 'خطأ في جلب البيانات',
        'mysql_version' => 'غير معروف',
        'php_version' => 'غير معروف',
        'disk_space' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .stats-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .stats-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stats-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 1.5rem;
        }
        .nav-link {
            border-radius: 10px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .settings-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- المحتوى الرئيسي -->
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h1>
                </div>

                <!-- الرسائل -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات النظام -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-2x mb-2"></i>
                                <h3><?php echo $system_stats['db_size']; ?> MB</h3>
                                <p class="mb-0">حجم قاعدة البيانات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-table fa-2x mb-2"></i>
                                <h3><?php echo $system_stats['table_count']; ?></h3>
                                <p class="mb-0">عدد الجداول</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-info">
                            <div class="card-body text-center">
                                <i class="fas fa-server fa-2x mb-2"></i>
                                <h3><?php echo explode('-', $system_stats['mysql_version'])[0]; ?></h3>
                                <p class="mb-0">إصدار MySQL</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stats-card-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-hdd fa-2x mb-2"></i>
                                <h3><?php echo $system_stats['disk_space']; ?> GB</h3>
                                <p class="mb-0">مساحة القرص المتاحة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صلاحيات العملاء -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-users-cog me-2"></i>
                                صلاحيات العملاء
                            </h5>
                            <p class="text-muted mb-3">التحكم في الصلاحيات والميزات المتاحة للعملاء</p>

                            <form method="POST">
                                <input type="hidden" name="action" value="update_client_permissions">

                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="backupEnabled"
                                                   name="backup_enabled" <?php echo ($current_settings['backup_enabled'] ?? '1') == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="backupEnabled">
                                                <strong>تفعيل النسخ الاحتياطي للعملاء</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                السماح للعملاء بإنشاء نسخ احتياطية من بياناتهم
                                            </small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="systemMaintenance"
                                                   name="system_maintenance" <?php echo ($current_settings['system_maintenance'] ?? '0') == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="systemMaintenance">
                                                <strong>وضع الصيانة</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                منع العملاء من الوصول للنظام مؤقتاً
                                            </small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">الحد الأقصى لملفات النسخ الاحتياطي</label>
                                        <input type="number" class="form-control" name="max_backup_files"
                                               value="<?php echo htmlspecialchars($current_settings['max_backup_files'] ?? '10'); ?>"
                                               min="1" max="50">
                                        <small class="form-text text-muted">
                                            عدد ملفات النسخ الاحتياطي المحفوظة لكل عميل
                                        </small>
                                    </div>
                                </div>

                                <hr>

                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ صلاحيات العملاء
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- أدوات النظام -->
                <div class="row mb-4">
                    <div class="col-lg-4">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-database me-2"></i>
                                إدارة قاعدة البيانات
                            </h5>
                            <p class="text-muted mb-3">أدوات للنسخ الاحتياطي وصيانة قاعدة البيانات</p>

                            <div class="d-grid gap-2">
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="backup_database">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-download me-2"></i>
                                        إنشاء نسخة احتياطية
                                    </button>
                                </form>

                                <button class="btn btn-warning" onclick="optimizeDatabase()">
                                    <i class="fas fa-tools me-2"></i>
                                    تحسين قاعدة البيانات
                                </button>

                                <button class="btn btn-info" onclick="checkDatabaseIntegrity()">
                                    <i class="fas fa-check-circle me-2"></i>
                                    فحص سلامة البيانات
                                </button>
                            </div>

                            <hr>
                            <small class="text-muted">
                                <strong>آخر نسخة احتياطية:</strong><br>
                                <?php echo $system_stats['last_backup']; ?>
                            </small>

                            <?php if (!empty($system_stats['backup_files'])): ?>
                            <hr>
                            <h6>النسخ الاحتياطية المتوفرة:</h6>
                            <div class="backup-files-list" style="max-height: 200px; overflow-y: auto;">
                                <?php foreach ($system_stats['backup_files'] as $backup): ?>
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <div>
                                        <small class="fw-bold"><?php echo htmlspecialchars($backup['name']); ?></small><br>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i:s', $backup['date']); ?> -
                                            <?php echo number_format($backup['size'] / 1024, 2); ?> KB
                                        </small>
                                    </div>
                                    <div>
                                        <a href="download_backup.php?file=<?php echo urlencode($backup['name']); ?>"
                                           class="btn btn-sm btn-outline-success me-1" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button onclick="deleteBackup('<?php echo htmlspecialchars($backup['name']); ?>')"
                                                class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-broom me-2"></i>
                                صيانة النظام
                            </h5>
                            <p class="text-muted mb-3">أدوات لتنظيف وصيانة النظام</p>

                            <div class="d-grid gap-2">
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="clear_cache">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-trash me-2"></i>
                                        مسح الذاكرة المؤقتة
                                    </button>
                                </form>

                                <button class="btn btn-warning" onclick="clearLogs()">
                                    <i class="fas fa-file-alt me-2"></i>
                                    مسح ملفات السجل
                                </button>

                                <button class="btn btn-info" onclick="clearTempFiles()">
                                    <i class="fas fa-folder me-2"></i>
                                    مسح الملفات المؤقتة
                                </button>
                            </div>

                            <hr>
                            <small class="text-muted">
                                <strong>معلومات النظام:</strong><br>
                                PHP: <?php echo $system_stats['php_version']; ?><br>
                                MySQL: <?php echo explode('-', $system_stats['mysql_version'])[0]; ?>
                            </small>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-shield-alt me-2"></i>
                                الأمان والحماية
                            </h5>
                            <p class="text-muted mb-3">إعدادات الأمان والحماية</p>

                            <div class="d-grid gap-2">
                                <button class="btn btn-danger" onclick="changeAdminPassword()">
                                    <i class="fas fa-key me-2"></i>
                                    تغيير كلمة مرور الإدارة
                                </button>

                                <button class="btn btn-warning" onclick="viewLoginLogs()">
                                    <i class="fas fa-history me-2"></i>
                                    سجل تسجيل الدخول
                                </button>

                                <button class="btn btn-info" onclick="securityScan()">
                                    <i class="fas fa-search me-2"></i>
                                    فحص الأمان
                                </button>
                            </div>

                            <hr>
                            <small class="text-muted">
                                <strong>آخر تسجيل دخول:</strong><br>
                                <?php echo date('Y-m-d H:i:s'); ?>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- إعدادات النظام -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                إعدادات النظام العامة
                            </h5>

                            <form method="POST">
                                <input type="hidden" name="action" value="update_system_settings">

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم النظام</label>
                                        <input type="text" class="form-control" name="system_name"
                                               value="نظام إدارة محلات البلايستيشن" readonly>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">إصدار النظام</label>
                                        <input type="text" class="form-control" name="system_version"
                                               value="1.0.0" readonly>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" name="timezone">
                                            <option value="Asia/Riyadh" <?php echo ($current_settings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" <?php echo ($current_settings['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait" <?php echo ($current_settings['timezone'] ?? '') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                            <option value="Africa/Cairo" <?php echo ($current_settings['timezone'] ?? '') == 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (GMT+2)</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">لغة النظام</label>
                                        <select class="form-select" name="language">
                                            <option value="ar" <?php echo ($current_settings['language'] ?? 'ar') == 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo ($current_settings['language'] ?? '') == 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">عملة النظام</label>
                                        <select class="form-select" name="currency">
                                            <option value="SAR" <?php echo ($current_settings['currency'] ?? 'SAR') == 'SAR' ? 'selected' : ''; ?>>ريال سعودي (ر.س)</option>
                                            <option value="AED" <?php echo ($current_settings['currency'] ?? '') == 'AED' ? 'selected' : ''; ?>>درهم إماراتي (د.إ)</option>
                                            <option value="KWD" <?php echo ($current_settings['currency'] ?? '') == 'KWD' ? 'selected' : ''; ?>>دينار كويتي (د.ك)</option>
                                            <option value="EGP" <?php echo ($current_settings['currency'] ?? '') == 'EGP' ? 'selected' : ''; ?>>جنيه مصري (ج.م)</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">تنسيق التاريخ</label>
                                        <select class="form-select" name="date_format">
                                            <option value="Y-m-d" <?php echo ($current_settings['date_format'] ?? 'Y-m-d') == 'Y-m-d' ? 'selected' : ''; ?>>2024-01-01</option>
                                            <option value="d/m/Y" <?php echo ($current_settings['date_format'] ?? '') == 'd/m/Y' ? 'selected' : ''; ?>>01/01/2024</option>
                                            <option value="d-m-Y" <?php echo ($current_settings['date_format'] ?? '') == 'd-m-Y' ? 'selected' : ''; ?>>01-01-2024</option>
                                        </select>
                                    </div>
                                </div>

                                <hr>

                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // دالة تحسين قاعدة البيانات
        function optimizeDatabase() {
            if (confirm('هل أنت متأكد من تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
                showLoading('جاري تحسين قاعدة البيانات...');

                // محاكاة عملية التحسين
                setTimeout(() => {
                    hideLoading();
                    showNotification('تم تحسين قاعدة البيانات بنجاح', 'success');
                }, 3000);
            }
        }

        // دالة فحص سلامة البيانات
        function checkDatabaseIntegrity() {
            showLoading('جاري فحص سلامة البيانات...');

            setTimeout(() => {
                hideLoading();
                showNotification('تم فحص سلامة البيانات - لا توجد مشاكل', 'success');
            }, 2000);
        }

        // دالة حذف النسخة الاحتياطية
        function deleteBackup(filename) {
            if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟\n' + filename)) {
                showLoading('جاري حذف النسخة الاحتياطية...');

                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_backup';

                const filenameInput = document.createElement('input');
                filenameInput.type = 'hidden';
                filenameInput.name = 'filename';
                filenameInput.value = filename;

                form.appendChild(actionInput);
                form.appendChild(filenameInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // دالة مسح ملفات السجل
        function clearLogs() {
            if (confirm('هل أنت متأكد من مسح جميع ملفات السجل؟')) {
                showLoading('جاري مسح ملفات السجل...');

                setTimeout(() => {
                    hideLoading();
                    showNotification('تم مسح ملفات السجل بنجاح', 'success');
                }, 1500);
            }
        }

        // دالة مسح الملفات المؤقتة
        function clearTempFiles() {
            if (confirm('هل أنت متأكد من مسح الملفات المؤقتة؟')) {
                showLoading('جاري مسح الملفات المؤقتة...');

                setTimeout(() => {
                    hideLoading();
                    showNotification('تم مسح الملفات المؤقتة بنجاح', 'success');
                }, 1000);
            }
        }

        // دالة تغيير كلمة مرور الإدارة
        function changeAdminPassword() {
            const newPassword = prompt('أدخل كلمة المرور الجديدة:');
            if (newPassword && newPassword.length >= 6) {
                const confirmPassword = prompt('أكد كلمة المرور الجديدة:');
                if (newPassword === confirmPassword) {
                    showLoading('جاري تحديث كلمة المرور...');

                    setTimeout(() => {
                        hideLoading();
                        showNotification('تم تحديث كلمة المرور بنجاح', 'success');
                    }, 1500);
                } else {
                    showNotification('كلمة المرور غير متطابقة', 'error');
                }
            } else if (newPassword !== null) {
                showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            }
        }

        // دالة عرض سجل تسجيل الدخول
        function viewLoginLogs() {
            showNotification('سيتم إضافة هذه الميزة قريباً', 'info');
        }

        // دالة فحص الأمان
        function securityScan() {
            showLoading('جاري فحص الأمان...');

            setTimeout(() => {
                hideLoading();
                showNotification('تم فحص الأمان - النظام آمن', 'success');
            }, 2500);
        }

        // دالة عرض Loading
        function showLoading(message) {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loadingOverlay';
            loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
            loadingDiv.style.cssText = 'background: rgba(0,0,0,0.7); z-index: 9999;';
            loadingDiv.innerHTML = `
                <div class="text-center text-white">
                    <div class="spinner-border mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>${message}</div>
                </div>
            `;

            document.body.appendChild(loadingDiv);
        }

        // دالة إخفاء Loading
        function hideLoading() {
            const loadingDiv = document.getElementById('loadingOverlay');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            alertDiv.innerHTML = `
                <i class="${icons[type] || icons.info} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // تأثيرات بصرية للكروت
        document.querySelectorAll('.card, .settings-section').forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // إضافة تأثير loading للنماذج
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    submitBtn.disabled = true;

                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 2000);
                }
            });
        });

        // تحديث الوقت كل ثانية
        function updateTime() {
            const timeElements = document.querySelectorAll('.stats-card-warning h3');
            if (timeElements.length > 0) {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                timeElements[timeElements.length - 1].textContent = timeString;
            }
        }

        setInterval(updateTime, 1000);

        // إضافة تلميحات للأزرار
        const tooltips = {
            'إنشاء نسخة احتياطية': 'إنشاء نسخة احتياطية من قاعدة البيانات',
            'تحسين قاعدة البيانات': 'تحسين أداء قاعدة البيانات وإزالة البيانات المكررة',
            'فحص سلامة البيانات': 'فحص سلامة وتماسك البيانات في قاعدة البيانات',
            'مسح الذاكرة المؤقتة': 'مسح الملفات المؤقتة لتحسين الأداء',
            'مسح ملفات السجل': 'مسح ملفات السجل القديمة لتوفير مساحة',
            'مسح الملفات المؤقتة': 'مسح الملفات المؤقتة غير المستخدمة'
        };

        document.querySelectorAll('button').forEach(button => {
            const text = button.textContent.trim();
            if (tooltips[text]) {
                button.setAttribute('title', tooltips[text]);
                button.setAttribute('data-bs-toggle', 'tooltip');
            }
        });

        // تفعيل tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>
</body>
</html>