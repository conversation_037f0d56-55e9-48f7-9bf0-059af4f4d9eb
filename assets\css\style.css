/* تنسيقات عامة */
:root {
    --primary-color: #062d47;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #282e33;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

body {
    background-color: var(--light-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* تحسين الخطوط العربية */
body, .form-control, .form-select, .btn, .card, .table {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* تنسيق القائمة العلوية */
.navbar {
    box-shadow: var(--box-shadow-lg);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* تنسيق العناوين */
h1, h2, h3, h4, h5, h6 {
    color: var(--dark-color);
    font-weight: 600;
}

h1 {
    margin-bottom: 2rem;
    position: relative;
}

h1:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    position: absolute;
    bottom: -10px;
    right: 0;
}

/* تنسيق النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* تنسيق الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 0;
}

.table thead {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
}

.table thead th {
    font-weight: 600;
    border: none;
    padding: 1rem;
    text-align: center;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

/* تنسيق الأزرار */
.btn {
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #157347);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffca2c);
    color: var(--dark-color);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #3dd5f3);
    color: var(--dark-color);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #5a6268);
}

/* تنسيق البطاقات */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* تنسيق الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #856404;
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1), rgba(13, 202, 240, 0.05));
    color: #055160;
    border-right: 4px solid var(--info-color);
}

/* تنسيق القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
}

/* تنسيق المودال */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border: none;
    padding: 1rem 1.5rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar, .btn, .card-header {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    body {
        background: white;
    }
}

/* تنسيقات إضافية للإحصائيات */
.stats-card {
    background: linear-gradient(135deg, white, #f8f9fa);
    border-right: 4px solid var(--primary-color);
}

.stats-card:hover {
    border-right-color: var(--success-color);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* تنسيق شريط التقدم */
.progress {
    height: 8px;
    border-radius: 50px;
    background-color: rgba(13, 110, 253, 0.1);
}

.progress-bar {
    border-radius: 50px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* تحسين النصوص */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: #856404 !important; }
.text-info { color: #055160 !important; }

/* تنسيقات صفحات تسجيل الدخول والتسجيل - تصميم احترافي محسن مع دعم RTL */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    font-family: 'Segoe UI', 'Cairo', 'Tajawal', 'Amiri', sans-serif;
    direction: rtl;
    animation: gradientShift 15s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
    }
    25% {
        background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #667eea 60%, #764ba2 100%);
    }
    50% {
        background: linear-gradient(135deg, #f093fb 0%, #667eea 30%, #764ba2 60%, #f093fb 100%);
    }
    75% {
        background: linear-gradient(135deg, #667eea 0%, #f093fb 30%, #764ba2 60%, #667eea 100%);
    }
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.08) 0%, transparent 40%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.08) 50%, transparent 70%);
    animation: backgroundShift 25s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 0.4; transform: translateX(0) translateY(0) rotate(0deg); }
    25% { opacity: 0.6; transform: translateX(-15px) translateY(-15px) rotate(1deg); }
    50% { opacity: 0.5; transform: translateX(15px) translateY(15px) rotate(-1deg); }
    75% { opacity: 0.7; transform: translateX(-8px) translateY(8px) rotate(0.5deg); }
}

.auth-container {
    position: relative;
    z-index: 1;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px) saturate(200%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 28px;
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset,
        0 2px 0 rgba(255, 255, 255, 0.3) inset,
        0 0 40px rgba(102, 126, 234, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    animation: cardEntrance 0.8s ease-out;
}

@keyframes cardEntrance {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmerTop 3s ease-in-out infinite;
}

@keyframes shimmerTop {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

.auth-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 2px 0 rgba(255, 255, 255, 0.4) inset,
        0 0 60px rgba(102, 126, 234, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
}

.auth-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    border-radius: 28px 28px 0 0;
    overflow: hidden;
    animation: headerGlow 4s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
    }
    50% {
        background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #667eea 60%, #764ba2 100%);
        box-shadow: 0 0 40px rgba(118, 75, 162, 0.4);
    }
}

.auth-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
    animation: headerOverlay 6s ease-in-out infinite;
}

@keyframes headerOverlay {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.auth-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255,255,255,0.4) 20%,
        rgba(255,255,255,0.8) 50%,
        rgba(255,255,255,0.4) 80%,
        transparent 100%);
    animation: shimmerLine 4s ease-in-out infinite;
}

@keyframes shimmerLine {
    0%, 100% { opacity: 0.6; transform: scaleX(0.8); }
    50% { opacity: 1; transform: scaleX(1.2); }
}

.auth-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
    border: 3px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2.5rem;
    position: relative;
    animation: iconFloat 4s ease-in-out infinite;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 0 30px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.auth-icon:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow:
        0 20px 45px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        0 0 40px rgba(255, 255, 255, 0.2);
}

.auth-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.4),
        transparent 30%,
        rgba(255, 255, 255, 0.2) 60%,
        transparent 90%,
        rgba(255, 255, 255, 0.4));
    animation: iconGlow 3s linear infinite;
    z-index: -1;
}

.auth-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: iconPulse 2s ease-in-out infinite;
    z-index: -2;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0) scale(1) rotate(0deg); }
    25% { transform: translateY(-8px) scale(1.05) rotate(1deg); }
    50% { transform: translateY(-5px) scale(1.08) rotate(0deg); }
    75% { transform: translateY(-10px) scale(1.03) rotate(-1deg); }
}

@keyframes iconGlow {
    0% { opacity: 0.6; transform: rotate(0deg) scale(1); }
    50% { opacity: 1; transform: rotate(180deg) scale(1.05); }
    100% { opacity: 0.6; transform: rotate(360deg) scale(1); }
}

@keyframes iconPulse {
    0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

.auth-title {
    font-size: 2rem;
    font-weight: 900;
    margin: 0 0 0.8rem 0;
    text-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.8px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite;
    position: relative;
}

@keyframes titleGlow {
    0%, 100% {
        text-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
        transform: scale(1);
    }
    50% {
        text-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
        transform: scale(1.02);
    }
}

.auth-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
    margin: 0;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    animation: subtitleFade 2s ease-in-out infinite alternate;
}

@keyframes subtitleFade {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

.auth-card-body {
    padding: 2rem 2rem;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.auth-card-body::-webkit-scrollbar {
    width: 6px;
}

.auth-card-body::-webkit-scrollbar-track {
    background: transparent;
}

.auth-card-body::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.auth-card-body::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.auth-card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
    border-radius: 2px;
}

.auth-form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.auth-form-control {
    width: 100%;
    padding: 1rem 3.5rem 1rem 1rem;
    border: 2px solid rgba(102, 126, 234, 0.15);
    border-radius: 16px;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98),
        rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(15px);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 1px 0 rgba(255, 255, 255, 0.3) inset;
    color: #2d3748;
    direction: rtl;
    text-align: right;
    position: relative;
    overflow: hidden;
    min-height: 48px;
}

.auth-form-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(102, 126, 234, 0.1),
        transparent);
    transition: left 0.6s ease;
}

.auth-form-control:hover::before {
    left: 100%;
}

.auth-form-control::placeholder {
    color: transparent;
}

.auth-form-control:focus {
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 8px 20px rgba(102, 126, 234, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        0 1px 0 rgba(255, 255, 255, 0.4) inset;
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px) scale(1.01);
    outline: none;
}

.auth-form-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(102, 126, 234, 0.6);
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
}

.auth-form-control:focus + .auth-form-icon {
    color: rgba(102, 126, 234, 1);
    transform: translateY(-50%) scale(1.1);
    text-shadow: 0 0 6px rgba(102, 126, 234, 0.3);
}

.auth-form-label {
    position: absolute;
    right: 3rem;
    top: 1rem;
    color: rgba(102, 126, 234, 0.7);
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    padding: 0.2rem 0.6rem;
    border-radius: 6px;
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    z-index: 3;
    direction: rtl;
    text-align: right;
}

.auth-form-control:focus ~ .auth-form-label,
.auth-form-control:not(:placeholder-shown) ~ .auth-form-label {
    top: -0.6rem;
    right: 2.5rem;
    font-size: 0.8rem;
    color: rgba(102, 126, 234, 1);
    font-weight: 600;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(102, 126, 234, 0.05));
    border: 1px solid rgba(102, 126, 234, 0.2);
    transform: scale(0.9);
    text-shadow: 0 1px 2px rgba(102, 126, 234, 0.2);
}

.auth-btn {
    width: 100%;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
    background-size: 200% 200%;
    color: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset,
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
    cursor: pointer;
    animation: buttonGradient 3s ease-in-out infinite;
    margin-top: 1rem;
}

@keyframes buttonGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.auth-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:hover::after {
    opacity: 1;
}

.auth-btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.25) inset,
        0 1px 0 rgba(255, 255, 255, 0.3) inset,
        0 0 30px rgba(102, 126, 234, 0.2);
}

.auth-btn:active {
    transform: translateY(-2px) scale(1.01);
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.auth-link:hover::after {
    width: 100%;
}

.auth-link:hover {
    color: #0b5ed7;
    text-decoration: none;
}

/* روابط بديلة محسنة */
.auth-link-alt {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(102, 126, 234, 0.05));
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    color: rgba(102, 126, 234, 0.8);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    min-width: 100px;
}

.auth-link-alt i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.auth-link-alt span {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
}

.auth-link-alt:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(102, 126, 234, 0.1));
    border-color: rgba(102, 126, 234, 0.4);
    color: rgba(102, 126, 234, 1);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
    text-decoration: none;
}

.auth-link-alt:hover i {
    transform: scale(1.1);
    color: rgba(102, 126, 234, 1);
}

.auth-alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    animation: slideIn 0.3s ease;
    direction: rtl;
    text-align: right;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.auth-alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.auth-alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.auth-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    direction: rtl;
}

.auth-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 0.5rem;
    accent-color: rgba(102, 126, 234, 1);
    border-radius: 4px;
}

.auth-checkbox label {
    color: var(--dark-color);
    font-weight: 500;
    cursor: pointer;
    direction: rtl;
    text-align: right;
}

/* تحسينات للأجهزة المحمولة - صفحات المصادقة مع دعم RTL */
@media (max-width: 768px) {
    .auth-card-body {
        padding: 1.5rem 1rem;
        max-height: 75vh;
    }

    .auth-card-header {
        padding: 2rem 1.5rem;
    }

    .auth-container {
        padding: 1rem;
    }

    .auth-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .auth-title {
        font-size: 1.25rem;
    }

    .auth-form-control {
        padding: 0.9rem 3rem 0.9rem 0.9rem;
        font-size: 0.95rem;
        border-radius: 14px;
        min-height: 44px;
    }

    .auth-form-icon {
        right: 0.9rem;
        font-size: 1rem;
    }

    .auth-form-label {
        right: 2.8rem;
        font-size: 0.9rem;
        top: 0.9rem;
    }

    .auth-form-control:focus ~ .auth-form-label,
    .auth-form-control:not(:placeholder-shown) ~ .auth-form-label {
        right: 2.3rem;
        font-size: 0.75rem;
        top: -0.5rem;
    }

    .auth-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        border-radius: 14px;
    }

    .auth-form-group {
        margin-bottom: 1.2rem;
    }

    .auth-link-alt {
        padding: 0.75rem 1rem;
        min-width: 80px;
    }

    .auth-link-alt i {
        font-size: 1.25rem;
    }

    .auth-link-alt span {
        font-size: 0.8rem;
    }
}

/* تأثيرات إضافية للتحسين */
.auth-card {
    position: relative;
    overflow: visible;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-card:hover::before {
    opacity: 0.3;
}

/* تحسين النصوص في صفحات المصادقة */
.auth-page .text-white-50 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* تأثير التحميل */
.auth-btn.loading {
    position: relative;
    color: transparent;
}

.auth-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسين textarea في النماذج */
.auth-form-control[rows] {
    min-height: 70px;
    resize: vertical;
    padding-top: 1rem;
    line-height: 1.4;
}

/* تحسين المسافات بين العناصر */
.auth-alert {
    margin-bottom: 1.5rem;
}

.auth-checkbox {
    margin-bottom: 1.5rem;
    margin-top: 0.5rem;
}

/* تحسين المسافات في الصفوف */
.row .auth-form-floating {
    margin-bottom: 1.2rem;
}

@media (max-width: 768px) {
    .row .auth-form-floating {
        margin-bottom: 1rem;
    }
}

/* تأثير الضوء المتحرك */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.auth-btn::before {
    animation: shimmer 3s infinite;
}