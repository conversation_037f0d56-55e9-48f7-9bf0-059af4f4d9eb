<?php
/**
 * ملف اختبار نظام تخصيص المظهر
 * Test file for theme customization system
 */

echo "<h1>اختبار نظام تخصيص المظهر</h1>";

// اختبار الاتصال بقاعدة البيانات
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// اختبار إنشاء جدول إعدادات المظهر
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS client_theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            primary_color VARCHAR(7) DEFAULT '#0d6efd',
            secondary_color VARCHAR(7) DEFAULT '#6c757d',
            accent_color VARCHAR(7) DEFAULT '#20c997',
            header_style ENUM('top', 'sidebar') DEFAULT 'top',
            sidebar_position ENUM('right', 'left') DEFAULT 'right',
            theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id)
        )
    ");
    echo "<p style='color: green;'>✅ جدول إعدادات المظهر جاهز</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "</p>";
}

// اختبار إدراج بيانات تجريبية
try {
    $test_client_id = 1; // افتراض وجود عميل برقم 1
    
    $stmt = $pdo->prepare("
        INSERT INTO client_theme_settings 
        (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        primary_color = VALUES(primary_color),
        secondary_color = VALUES(secondary_color),
        accent_color = VALUES(accent_color),
        header_style = VALUES(header_style),
        sidebar_position = VALUES(sidebar_position),
        theme_mode = VALUES(theme_mode),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    $result = $stmt->execute([
        $test_client_id, 
        '#dc3545', // أحمر
        '#6c757d', // رمادي
        '#fd7e14', // برتقالي
        'sidebar', 
        'right', 
        'light'
    ]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ تم حفظ إعدادات تجريبية بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في حفظ الإعدادات التجريبية</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ تحذير: " . $e->getMessage() . "</p>";
}

// اختبار جلب الإعدادات
try {
    $stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
    $stmt->execute([$test_client_id]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<p style='color: green;'>✅ تم جلب الإعدادات بنجاح:</p>";
        echo "<ul>";
        echo "<li>اللون الأساسي: <span style='color: " . $settings['primary_color'] . "; font-weight: bold;'>" . $settings['primary_color'] . "</span></li>";
        echo "<li>اللون الثانوي: <span style='color: " . $settings['secondary_color'] . "; font-weight: bold;'>" . $settings['secondary_color'] . "</span></li>";
        echo "<li>اللون المميز: <span style='color: " . $settings['accent_color'] . "; font-weight: bold;'>" . $settings['accent_color'] . "</span></li>";
        echo "<li>نمط الهيدر: " . $settings['header_style'] . "</li>";
        echo "<li>موضع القائمة: " . $settings['sidebar_position'] . "</li>";
        echo "<li>نمط العرض: " . $settings['theme_mode'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ لم يتم العثور على إعدادات للعميل</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في جلب الإعدادات: " . $e->getMessage() . "</p>";
}

// اختبار الملفات المطلوبة
$required_files = [
    'client/settings.php' => 'صفحة الإعدادات',
    'client/api/theme-css.php' => 'ملف CSS الديناميكي',
    'client/api/save_theme_settings.php' => 'API حفظ الإعدادات',
    'client/api/get_theme_settings.php' => 'API جلب الإعدادات',
    'client/assets/js/theme-customizer.js' => 'JavaScript تخصيص المظهر',
    'client/theme-help.html' => 'صفحة المساعدة'
];

echo "<h2>فحص الملفات المطلوبة:</h2>";
foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p style='color: green;'>✅ $description ($file) - الحجم: " . number_format($size) . " بايت</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) - غير موجود</p>";
    }
}

// اختبار وظائف CSS
echo "<h2>اختبار CSS الديناميكي:</h2>";
if (file_exists('client/api/theme-css.php')) {
    echo "<p style='color: blue;'>ℹ️ يمكنك اختبار CSS الديناميكي بزيارة: <a href='client/api/theme-css.php' target='_blank'>client/api/theme-css.php</a></p>";
} else {
    echo "<p style='color: red;'>❌ ملف CSS الديناميكي غير موجود</p>";
}

// اختبار JavaScript
echo "<h2>اختبار JavaScript:</h2>";
if (file_exists('client/assets/js/theme-customizer.js')) {
    $js_content = file_get_contents('client/assets/js/theme-customizer.js');
    $functions_to_check = [
        'ThemeCustomizer' => 'كلاس إدارة المظهر',
        'updateColorPreview' => 'وظيفة معاينة الألوان',
        'saveThemeSettings' => 'وظيفة حفظ الإعدادات',
        'resetToDefaults' => 'وظيفة إعادة التعيين'
    ];
    
    foreach ($functions_to_check as $function => $description) {
        if (strpos($js_content, $function) !== false) {
            echo "<p style='color: green;'>✅ $description موجودة</p>";
        } else {
            echo "<p style='color: red;'>❌ $description غير موجودة</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ملف JavaScript غير موجود</p>";
}

echo "<h2>الخلاصة:</h2>";
echo "<p style='color: blue; font-weight: bold;'>نظام تخصيص المظهر جاهز للاستخدام! 🎨</p>";
echo "<p>يمكنك الآن:</p>";
echo "<ul>";
echo "<li>الانتقال إلى <a href='client/settings.php'>صفحة الإعدادات</a></li>";
echo "<li>اختيار تبويب 'تخصيص المظهر'</li>";
echo "<li>تخصيص الألوان وشكل الهيدر</li>";
echo "<li>مراجعة <a href='client/theme-help.html'>دليل الاستخدام</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>تم إنشاء هذا النظام بواسطة Augment Agent 🤖</p>";
?>
