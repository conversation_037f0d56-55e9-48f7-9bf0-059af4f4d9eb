<?php
/**
 * تطبيق تحديثات نظام تحكم الإدمن في صفحات العملاء
 * PlayGood - Gaming Center Management System
 */

// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'config/database.php';

// التحقق من الصلاحيات (يجب أن يكون المستخدم admin)
if (!isset($_SESSION['admin_id'])) {
    header('Location: admin/login.php');
    exit;
}

$page_title = "تطبيق تحديثات نظام تحكم الإدمن";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .result-box { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
        .success-box { border-left-color: #28a745; }
        .error-box { border-left-color: #dc3545; }
        .warning-box { border-left-color: #ffc107; }
        .step-card { border-left: 4px solid #007bff; }
        .step-completed { border-left-color: #28a745; }
        .step-error { border-left-color: #dc3545; }
    </style>
</head>
<body class="bg-light">

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        تطبيق تحديثات نظام تحكم الإدمن في صفحات العملاء
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_updates'])): ?>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            جاري تطبيق التحديثات...
                        </div>

                        <?php
                        $steps = [
                            [
                                'title' => 'إضافة الصفحات المفقودة',
                                'description' => 'إضافة صفحات الماليات والحضور والورديات',
                                'sql_file' => 'add_missing_client_pages.sql'
                            ]
                        ];

                        $total_success = 0;
                        $total_errors = 0;

                        foreach ($steps as $step_index => $step) {
                            echo "<div class='card step-card mb-3'>";
                            echo "<div class='card-header'>";
                            echo "<h5 class='mb-0'><i class='fas fa-step-forward me-2'></i>الخطوة " . ($step_index + 1) . ": " . $step['title'] . "</h5>";
                            echo "<small class='text-muted'>" . $step['description'] . "</small>";
                            echo "</div>";
                            echo "<div class='card-body'>";

                            try {
                                // قراءة ملف SQL
                                if (!file_exists($step['sql_file'])) {
                                    throw new Exception("ملف SQL غير موجود: " . $step['sql_file']);
                                }

                                $sql_content = file_get_contents($step['sql_file']);
                                if ($sql_content === false) {
                                    throw new Exception('لا يمكن قراءة ملف SQL: ' . $step['sql_file']);
                                }

                                // تقسيم الاستعلامات
                                $queries = array_filter(array_map('trim', explode(';', $sql_content)));
                                
                                $step_success = 0;
                                $step_errors = 0;

                                foreach ($queries as $query) {
                                    if (empty($query) || strpos($query, '--') === 0) {
                                        continue;
                                    }

                                    try {
                                        $stmt = $pdo->prepare($query);
                                        $stmt->execute();
                                        
                                        // إذا كان الاستعلام SELECT، عرض النتائج
                                        if (stripos($query, 'SELECT') === 0) {
                                            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                            if (!empty($result)) {
                                                echo "<div class='result-box'>";
                                                echo "<div class='table-responsive'>";
                                                echo "<table class='table table-sm table-striped'>";
                                                
                                                // عرض الرؤوس
                                                if (!empty($result)) {
                                                    echo "<thead class='table-dark'><tr>";
                                                    foreach (array_keys($result[0]) as $header) {
                                                        echo "<th>" . htmlspecialchars($header) . "</th>";
                                                    }
                                                    echo "</tr></thead>";
                                                    
                                                    // عرض البيانات
                                                    echo "<tbody>";
                                                    foreach ($result as $row) {
                                                        echo "<tr>";
                                                        foreach ($row as $value) {
                                                            echo "<td>" . htmlspecialchars($value) . "</td>";
                                                        }
                                                        echo "</tr>";
                                                    }
                                                    echo "</tbody>";
                                                }
                                                echo "</table></div></div>";
                                            }
                                        }
                                        
                                        $step_success++;
                                    } catch (PDOException $e) {
                                        $step_errors++;
                                        echo "<div class='result-box error-box'>";
                                        echo "<strong>خطأ في الاستعلام:</strong><br>";
                                        echo "<code>" . htmlspecialchars(substr($query, 0, 100)) . "...</code><br>";
                                        echo "<small class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</small>";
                                        echo "</div>";
                                    }
                                }

                                // عرض نتيجة الخطوة
                                if ($step_errors === 0) {
                                    echo "<div class='alert alert-success'>";
                                    echo "<i class='fas fa-check-circle me-2'></i>";
                                    echo "تم تنفيذ الخطوة بنجاح! ($step_success استعلام)";
                                    echo "</div>";
                                    
                                    // تحديث تصميم البطاقة للنجاح
                                    echo "<script>document.querySelector('.step-card:last-of-type').classList.add('step-completed');</script>";
                                } else {
                                    echo "<div class='alert alert-warning'>";
                                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                    echo "تم تنفيذ الخطوة مع بعض الأخطاء! (نجح: $step_success، فشل: $step_errors)";
                                    echo "</div>";
                                }

                                $total_success += $step_success;
                                $total_errors += $step_errors;

                            } catch (Exception $e) {
                                echo "<div class='alert alert-danger'>";
                                echo "<i class='fas fa-times-circle me-2'></i>";
                                echo "فشلت الخطوة: " . htmlspecialchars($e->getMessage());
                                echo "</div>";
                                
                                // تحديث تصميم البطاقة للخطأ
                                echo "<script>document.querySelector('.step-card:last-of-type').classList.add('step-error');</script>";
                                
                                $total_errors++;
                            }

                            echo "</div></div>";
                        }

                        // النتيجة النهائية
                        if ($total_errors === 0) {
                            echo "<div class='alert alert-success mt-4'>";
                            echo "<h5><i class='fas fa-check-circle me-2'></i>تم تطبيق جميع التحديثات بنجاح!</h5>";
                            echo "<p>تم تنفيذ <strong>$total_success</strong> استعلام بنجاح.</p>";
                            echo "<p class='mb-0'>يمكنك الآن استخدام النظام المحدث لإدارة صلاحيات العملاء.</p>";
                            echo "</div>";
                            
                            echo "<div class='text-center mt-4'>";
                            echo "<a href='admin/client_permissions.php' class='btn btn-primary me-2'>";
                            echo "<i class='fas fa-users-cog me-1'></i>إدارة صلاحيات العملاء";
                            echo "</a>";
                            echo "<a href='admin/dashboard.php' class='btn btn-secondary'>";
                            echo "<i class='fas fa-home me-1'></i>لوحة تحكم الإدمن";
                            echo "</a>";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-warning mt-4'>";
                            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تم التطبيق مع بعض الأخطاء</h5>";
                            echo "<p>نجح: <strong>$total_success</strong> استعلام، فشل: <strong>$total_errors</strong> استعلام.</p>";
                            echo "<p class='mb-0'>يرجى مراجعة الأخطاء أعلاه وإصلاحها.</p>";
                            echo "</div>";
                        }
                        ?>

                    <?php else: ?>
                        
                        <!-- معلومات حول التحديثات -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>حول هذه التحديثات</h5>
                            <p>ستقوم هذه العملية بتطبيق التحديثات التالية على نظام تحكم الإدمن:</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-plus-circle text-success me-1"></i>الصفحات المضافة:</h6>
                                    <ul class="small">
                                        <li><strong>الماليات:</strong> إدارة الماليات، أنواع المصروفات/الإيرادات، التقارير المالية، كشف الرواتب</li>
                                        <li><strong>الحضور:</strong> نظام الحضور والانصراف، الحضور السريع، تقارير الحضور، إدارة الإضافي</li>
                                        <li><strong>الورديات:</strong> إدارة الورديات، تعيين الورديات، تقارير الورديات، إعدادات الورديات، جدولة العمل</li>
                                    </ul>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6><i class="fas fa-cogs text-primary me-1"></i>التحسينات:</h6>
                                    <ul class="small">
                                        <li>تحسين تصنيف الصفحات حسب الفئات</li>
                                        <li>إضافة أوصاف مفصلة للصفحات</li>
                                        <li>تحديث أسماء الفئات في واجهة الإدارة</li>
                                        <li>جميع الصفحات الجديدة غير مفعلة افتراضياً</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>ملاحظات مهمة:</h6>
                            <ul class="mb-0">
                                <li>تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التطبيق</li>
                                <li>جميع الصفحات الجديدة ستكون غير مفعلة افتراضياً للعملاء</li>
                                <li>يمكن للإدمن تفعيل الصفحات لاحقاً من صفحة إدارة الصلاحيات</li>
                                <li>لن تتأثر الصفحات والصلاحيات الموجودة حالياً</li>
                            </ul>
                        </div>

                        <!-- نموذج التأكيد -->
                        <form method="POST" class="text-center">
                            <button type="submit" name="apply_updates" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket me-2"></i>
                                تطبيق التحديثات
                            </button>
                        </form>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
