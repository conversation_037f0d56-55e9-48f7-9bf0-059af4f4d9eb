<?php
/**
 * نظام الحماية المتقدم للجلسات - Session Security Manager
 * يمنع سرقة الجلسات (Session Hijacking) ويوفر حماية شاملة
 * PlayGood Gaming Center Management System
 */

class SessionSecurityManager {
    
    private $pdo;
    private $security_level = 'high'; // low, medium, high
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->initSecurityTables();
    }
    
    /**
     * إنشاء جداول الأمان المطلوبة
     */
    private function initSecurityTables() {
        try {
            // جدول تتبع الجلسات الآمنة
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS secure_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(128) NOT NULL,
                    user_id INT NOT NULL,
                    user_type ENUM('admin', 'client', 'employee') NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent_hash VARCHAR(64) NOT NULL,
                    security_token VARCHAR(64) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_valid BOOLEAN DEFAULT TRUE,
                    INDEX idx_session_id (session_id),
                    INDEX idx_user_id (user_id, user_type),
                    INDEX idx_security_token (security_token)
                ) ENGINE=InnoDB
            ");
            
            // جدول تسجيل محاولات الاختراق
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS session_security_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(128),
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT,
                    attack_type ENUM('hijack_attempt', 'invalid_token', 'ip_mismatch', 'agent_mismatch', 'expired_session') NOT NULL,
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_attack_type (attack_type),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB
            ");
            
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء جداول الأمان: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء جلسة آمنة جديدة
     */
    public function createSecureSession($user_id, $user_type) {
        try {
            // إنهاء أي جلسات سابقة للمستخدم
            $this->invalidateUserSessions($user_id, $user_type);
            
            // إنشاء معرف جلسة آمن
            $session_id = session_id();
            $security_token = $this->generateSecurityToken();
            $ip_address = $this->getClientIP();
            $user_agent_hash = $this->hashUserAgent();
            
            // حفظ بيانات الجلسة الآمنة
            $stmt = $this->pdo->prepare("
                INSERT INTO secure_sessions 
                (session_id, user_id, user_type, ip_address, user_agent_hash, security_token)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $session_id,
                $user_id,
                $user_type,
                $ip_address,
                $user_agent_hash,
                $security_token
            ]);
            
            // حفظ الرمز الأمني في الجلسة
            $_SESSION['security_token'] = $security_token;
            $_SESSION['session_fingerprint'] = $this->generateSessionFingerprint();
            
            return $security_token;
            
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء جلسة آمنة: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public function validateSession() {
        if (!isset($_SESSION['security_token']) || !isset($_SESSION['session_fingerprint'])) {
            $this->logSecurityEvent('invalid_token', 'رمز الأمان غير موجود');
            return false;
        }
        
        $session_id = session_id();
        $security_token = $_SESSION['security_token'];
        $current_ip = $this->getClientIP();
        $current_agent_hash = $this->hashUserAgent();
        $current_fingerprint = $this->generateSessionFingerprint();
        
        try {
            // البحث عن الجلسة في قاعدة البيانات
            $stmt = $this->pdo->prepare("
                SELECT * FROM secure_sessions 
                WHERE session_id = ? AND security_token = ? AND is_valid = TRUE
            ");
            $stmt->execute([$session_id, $security_token]);
            $session_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session_data) {
                $this->logSecurityEvent('hijack_attempt', 'جلسة غير صحيحة أو منتهية الصلاحية');
                return false;
            }
            
            // التحقق من IP Address
            if ($session_data['ip_address'] !== $current_ip) {
                $this->logSecurityEvent('ip_mismatch', 
                    "تغيير IP: من {$session_data['ip_address']} إلى {$current_ip}");
                $this->invalidateSession($session_id);
                return false;
            }
            
            // التحقق من User Agent
            if ($session_data['user_agent_hash'] !== $current_agent_hash) {
                $this->logSecurityEvent('agent_mismatch', 'تغيير User Agent');
                $this->invalidateSession($session_id);
                return false;
            }
            
            // التحقق من Session Fingerprint
            if ($_SESSION['session_fingerprint'] !== $current_fingerprint) {
                $this->logSecurityEvent('hijack_attempt', 'تغيير بصمة الجلسة');
                $this->invalidateSession($session_id);
                return false;
            }
            
            // تحديث آخر نشاط
            $this->updateLastActivity($session_id);
            
            return true;
            
        } catch (PDOException $e) {
            error_log("خطأ في التحقق من الجلسة: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنهاء جلسة آمنة
     */
    public function destroySecureSession() {
        $session_id = session_id();
        
        try {
            // إلغاء صحة الجلسة في قاعدة البيانات
            $stmt = $this->pdo->prepare("
                UPDATE secure_sessions 
                SET is_valid = FALSE 
                WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            
            // مسح بيانات الجلسة
            session_unset();
            session_destroy();
            
            return true;
            
        } catch (PDOException $e) {
            error_log("خطأ في إنهاء الجلسة: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنهاء جميع جلسات المستخدم
     */
    private function invalidateUserSessions($user_id, $user_type) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE secure_sessions 
                SET is_valid = FALSE 
                WHERE user_id = ? AND user_type = ? AND is_valid = TRUE
            ");
            $stmt->execute([$user_id, $user_type]);
            
        } catch (PDOException $e) {
            error_log("خطأ في إنهاء جلسات المستخدم: " . $e->getMessage());
        }
    }
    
    /**
     * إنهاء جلسة محددة
     */
    private function invalidateSession($session_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE secure_sessions 
                SET is_valid = FALSE 
                WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            
        } catch (PDOException $e) {
            error_log("خطأ في إنهاء الجلسة: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث آخر نشاط للجلسة
     */
    private function updateLastActivity($session_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE secure_sessions 
                SET last_activity = CURRENT_TIMESTAMP 
                WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            
        } catch (PDOException $e) {
            error_log("خطأ في تحديث آخر نشاط: " . $e->getMessage());
        }
    }
    
    /**
     * توليد رمز أمان عشوائي
     */
    private function generateSecurityToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * الحصول على IP الحقيقي للعميل
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تشفير User Agent
     */
    private function hashUserAgent() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        return hash('sha256', $user_agent);
    }
    
    /**
     * توليد بصمة الجلسة
     */
    private function generateSessionFingerprint() {
        $data = [
            $this->getClientIP(),
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? ''
        ];
        
        return hash('sha256', implode('|', $data));
    }
    
    /**
     * تسجيل حدث أمني
     */
    private function logSecurityEvent($attack_type, $details) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO session_security_logs 
                (session_id, ip_address, user_agent, attack_type, details)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                session_id(),
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $attack_type,
                $details
            ]);
            
        } catch (PDOException $e) {
            error_log("خطأ في تسجيل الحدث الأمني: " . $e->getMessage());
        }
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanupExpiredSessions() {
        try {
            // إنهاء الجلسات القديمة (أكثر من ساعة)
            $stmt = $this->pdo->prepare("
                UPDATE secure_sessions 
                SET is_valid = FALSE 
                WHERE last_activity < DATE_SUB(NOW(), INTERVAL 1 HOUR) 
                AND is_valid = TRUE
            ");
            $stmt->execute();
            
            // حذف السجلات القديمة (أكثر من شهر)
            $stmt = $this->pdo->prepare("
                DELETE FROM session_security_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 MONTH)
            ");
            $stmt->execute();
            
        } catch (PDOException $e) {
            error_log("خطأ في تنظيف الجلسات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على إحصائيات الأمان
     */
    public function getSecurityStats() {
        try {
            $stats = [];
            
            // عدد الجلسات النشطة
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM secure_sessions WHERE is_valid = TRUE");
            $stats['active_sessions'] = $stmt->fetchColumn();
            
            // عدد محاولات الاختراق اليوم
            $stmt = $this->pdo->query("
                SELECT COUNT(*) FROM session_security_logs 
                WHERE DATE(created_at) = CURDATE()
            ");
            $stats['attacks_today'] = $stmt->fetchColumn();
            
            // أكثر أنواع الهجمات
            $stmt = $this->pdo->query("
                SELECT attack_type, COUNT(*) as count 
                FROM session_security_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY attack_type 
                ORDER BY count DESC
            ");
            $stats['attack_types'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
            
        } catch (PDOException $e) {
            error_log("خطأ في الحصول على إحصائيات الأمان: " . $e->getMessage());
            return [];
        }
    }
}
