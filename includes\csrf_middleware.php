<?php
/**
 * CSRF Protection Middleware
 * يتم تطبيقه تلقائياً على جميع طلبات POST
 */

class CSRFMiddleware {
    
    private $security;
    private $excluded_paths = [
        '/api/public/',
        '/webhook/',
        '/cron/'
    ];
    
    public function __construct($security) {
        $this->security = $security;
    }
    
    /**
     * فحص CSRF للطلبات
     */
    public function validateRequest() {
        // تطبيق الحماية فقط على طلبات POST, PUT, DELETE
        if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            return true;
        }
        
        // تجاهل المسارات المستثناة
        $current_path = $_SERVER['REQUEST_URI'] ?? '';
        foreach ($this->excluded_paths as $excluded_path) {
            if (strpos($current_path, $excluded_path) !== false) {
                return true;
            }
        }
        
        // فحص وجود CSRF token
        $csrf_token = $this->getTokenFromRequest();
        if (!$csrf_token) {
            $this->handleCSRFFailure('CSRF token مفقود');
            return false;
        }
        
        // التحقق من صحة الـ token
        $form_name = $_POST['form_name'] ?? 'default';
        if (!$this->security->validateCsrfToken($csrf_token, $form_name)) {
            $this->handleCSRFFailure('CSRF token غير صالح أو منتهي الصلاحية');
            return false;
        }
        
        return true;
    }
    
    /**
     * الحصول على CSRF token من الطلب
     */
    private function getTokenFromRequest() {
        // فحص POST data
        if (isset($_POST['csrf_token'])) {
            return $_POST['csrf_token'];
        }
        
        // فحص headers
        $headers = getallheaders();
        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        if (isset($headers['X-Requested-With']) && 
            isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        return null;
    }
    
    /**
     * التعامل مع فشل CSRF
     */
    private function handleCSRFFailure($reason) {
        // تسجيل المحاولة المشبوهة
        $ip = $this->security->getClientIp();
        $this->security->logSuspiciousActivity(
            $ip, 
            'csrf_failure', 
            $reason . ' - ' . $_SERVER['REQUEST_URI'], 
            'high'
        );
        
        // إرسال رد مناسب
        http_response_code(403);
        
        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'طلب غير صالح',
                'message' => 'انتهت صلاحية الجلسة. يرجى إعادة تحميل الصفحة.',
                'csrf_error' => true
            ]);
        } else {
            // إعادة توجيه إلى صفحة خطأ أو نفس الصفحة مع رسالة
            $_SESSION['csrf_error'] = 'انتهت صلاحية الجلسة. يرجى المحاولة مرة أخرى.';
            
            $referer = $_SERVER['HTTP_REFERER'] ?? '/';
            header("Location: $referer");
        }
        
        exit;
    }
    
    /**
     * فحص ما إذا كان الطلب AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * إضافة CSRF token تلقائياً للنماذج
     */
    public static function injectCSRFToken($html, $security, $form_name = 'default') {
        // البحث عن جميع النماذج وإضافة CSRF token
        $pattern = '/(<form[^>]*method\s*=\s*["\']post["\'][^>]*>)/i';
        
        $replacement = function($matches) use ($security, $form_name) {
            $form_tag = $matches[1];
            $csrf_input = $security->getCsrfTokenInput($form_name);
            return $form_tag . "\n" . $csrf_input;
        };
        
        return preg_replace_callback($pattern, $replacement, $html);
    }
}

/**
 * دالة مساعدة لإنشاء CSRF token في النماذج
 */
function csrf_token($form_name = 'default') {
    global $security;
    if (isset($security)) {
        return $security->generateCsrfToken($form_name);
    }
    return '';
}

/**
 * دالة مساعدة لإنشاء hidden input للـ CSRF
 */
function csrf_field($form_name = 'default') {
    global $security;
    if (isset($security)) {
        return $security->getCsrfTokenInput($form_name);
    }
    return '';
}

/**
 * JavaScript helper للـ CSRF في AJAX requests
 */
function csrf_js_helper() {
    global $security;
    $token = isset($security) ? $security->generateCsrfToken('ajax') : '';
    
    return "
    <script>
    // CSRF Helper for AJAX requests
    window.csrfToken = '" . htmlspecialchars($token) . "';
    
    // Add CSRF token to all AJAX requests
    if (typeof jQuery !== 'undefined') {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader('X-CSRF-Token', window.csrfToken);
                }
            }
        });
    }
    
    // For vanilla JavaScript fetch requests
    window.fetchWithCSRF = function(url, options = {}) {
        options.headers = options.headers || {};
        if (!options.method || !['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(options.method.toUpperCase())) {
            options.headers['X-CSRF-Token'] = window.csrfToken;
        }
        return fetch(url, options);
    };
    
    // Auto-refresh CSRF token every 30 minutes
    setInterval(function() {
        fetch('/api/refresh-csrf-token', {
            method: 'POST',
            headers: {
                'X-CSRF-Token': window.csrfToken,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                window.csrfToken = data.token;
            }
        })
        .catch(console.error);
    }, 30 * 60 * 1000); // 30 minutes
    </script>";
}

// تطبيق CSRF middleware تلقائياً
if (isset($security)) {
    $csrf_middleware = new CSRFMiddleware($security);
    
    // فحص الطلب الحالي
    if (!$csrf_middleware->validateRequest()) {
        // تم التعامل مع الخطأ في الـ middleware
        exit;
    }
}
?>
