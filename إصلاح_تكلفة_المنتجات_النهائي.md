# إصلاح مشكلة حساب تكلفة المنتجات في الجلسات - الحل النهائي

## المشكلة الأساسية
كانت المشكلة الرئيسية هي: **"لا يتم حساب تكلفة المنتجات المضافة داخل إجمالي التكلفة"**

## السبب الجذري
تم اكتشاف تضارب في بنية قاعدة البيانات بين ملفات API المختلفة:

### المشكلة في `add_session_product.php`
```sql
-- الكود القديم (المشكلة)
INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) 
VALUES (?, ?, ?, ?, ?)
```

### المشكلة في `sessions.php` و `delete_session_product.php`
```sql
-- الكود المتوقع
SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
FROM session_products sp WHERE sp.session_id = ?
```

**النتيجة**: عدم تطابق أسماء الأعمدة أدى إلى عدم حساب تكلفة المنتجات.

## الحلول المطبقة

### 1. توحيد بنية قاعدة البيانات
**الملف**: `client/api/add_session_product.php`

```php
// الحل الجديد - استخدام عمود واحد للسعر
$stmt = $pdo->prepare("
    INSERT INTO session_products (session_id, product_id, quantity, price) 
    VALUES (?, ?, ?, ?)
");
$stmt->execute([$session_id, $product_id, $quantity, $price]);
```

### 2. تحديث حساب التكلفة الإجمالية
**الملف**: `client/api/add_session_product.php`

```php
// حساب التكلفة الكاملة (وقت + منتجات)
$stmt = $pdo->prepare("
    SELECT
        s.session_id,
        s.start_time,
        d.single_rate,
        d.hourly_rate,
        COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost,
        TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
    FROM sessions s
    JOIN devices d ON s.device_id = d.device_id
    LEFT JOIN session_products sp ON s.session_id = sp.session_id
    WHERE s.session_id = ?
    GROUP BY s.session_id, s.start_time, d.single_rate, d.hourly_rate
");

// حساب التكلفة الكلية - استخدام single_rate أولاً
$hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
$time_cost = $session['duration_minutes'] > 0 && $hourly_rate > 0 ? 
    ceil($session['duration_minutes'] / 60) * $hourly_rate : 0;
$total_cost = $time_cost + $session['products_cost'];
```

### 3. تحديث API حذف المنتجات
**الملف**: `client/api/delete_session_product.php`

تم تطبيق نفس منطق حساب التكلفة الكاملة لضمان الاتساق.

### 4. إضافة التحديث التلقائي للتكلفة
**الملف**: `client/sessions.php`

```javascript
// تحديث الوقت والتكلفة كل ثانية
function updateTimers() {
    // تحديث الوقت
    document.querySelectorAll('.timer').forEach(function(timer) {
        // كود تحديث الوقت...
    });
    
    // تحديث التكلفة للجلسات النشطة (كل 30 ثانية)
    updateSessionCosts();
}

// دالة تحديث تكلفة الجلسات النشطة
function updateSessionCosts() {
    // تحديث التكلفة كل 30 ثانية فقط لتجنب الضغط على الخادم
    const now = Date.now();
    if (now - lastCostUpdate < 30000) return;
    
    document.querySelectorAll('.session-cost[data-session-id]').forEach(function(costElement) {
        // حساب التكلفة الحالية وتحديثها
        fetch(`api/get_session_products.php?session_id=${sessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const productsCost = data.products.reduce((total, product) => {
                        return total + (product.quantity * product.price);
                    }, 0);
                    const totalCost = timeCost + productsCost;
                    costElement.textContent = totalCost.toFixed(2) + ' ج.م';
                }
            });
    });
}
```

### 5. إضافة البيانات المطلوبة للتحديث التلقائي
**الملف**: `client/sessions.php`

```php
// إضافة data-hourly-rate للعنصر
<strong class="text-primary session-cost" 
       data-session-id="<?php echo $session['session_id']; ?>" 
       data-hourly-rate="<?php echo $hourly_rate; ?>">
    <?php echo number_format($total_cost, 2); ?> ج.م
</strong>
```

## الميزات الجديدة

### 1. حساب دقيق للتكلفة
- ✅ تكلفة الوقت محسوبة بناءً على `single_rate` أولاً ثم `hourly_rate`
- ✅ تكلفة المنتجات محسوبة بدقة من `quantity * price`
- ✅ التكلفة الإجمالية = تكلفة الوقت + تكلفة المنتجات

### 2. التحديث التلقائي
- ✅ تحديث الوقت كل ثانية
- ✅ تحديث التكلفة كل 30 ثانية (لتجنب الضغط على الخادم)
- ✅ تحديث فوري عند إضافة/حذف المنتجات

### 3. استجابة API محسنة
```json
{
    "success": true,
    "message": "تم إضافة المنتج بنجاح",
    "total_cost": "15.50",
    "products_cost": "10.00",
    "time_cost": "5.50",
    "session_id": 123,
    "product_id": 456
}
```

## اختبار الحل

تم إنشاء ملف اختبار شامل: `test_product_cost_fix.php`

### خطوات الاختبار:
1. **التحقق من البيانات الأساسية**: جلسة نشطة + منتج متاح
2. **إضافة منتج للجلسة**: عبر API مع التحقق من الاستجابة
3. **جلب منتجات الجلسة**: التأكد من حساب التكلفة بشكل صحيح
4. **التحقق من النتيجة النهائية**: مقارنة التكلفة المحسوبة

## النتيجة النهائية

✅ **تم حل المشكلة بالكامل**:
- تكلفة المنتجات تحسب وتعرض بشكل صحيح
- التكلفة الإجمالية تشمل تكلفة الوقت والمنتجات
- التحديث التلقائي يعمل بسلاسة
- جميع APIs تعمل بشكل متسق

## ملفات تم تعديلها:
1. `client/api/add_session_product.php` - إصلاح بنية قاعدة البيانات وحساب التكلفة
2. `client/api/delete_session_product.php` - توحيد منطق حساب التكلفة
3. `client/sessions.php` - إضافة التحديث التلقائي وdata attributes
4. `test_product_cost_fix.php` - ملف اختبار شامل (جديد)

## للاختبار:
قم بزيارة: `http://localhost/playgood/test_product_cost_fix.php`
