<?php
/**
 * اختبار إعدادات طريقة الحساب الجديدة
 * يتيح للعميل الاختيار بين طريقتين للمحاسبة
 */

require_once 'config/database.php';
require_once 'includes/billing_helper.php';

echo "<h1>اختبار إعدادات طريقة الحساب</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // اختبار الدوال المساعدة
    echo "<h3>🧪 اختبار الدوال المساعدة</h3>";
    
    // افتراض معرف عميل للاختبار
    $test_client_id = 1;
    $test_durations = [15, 30, 45, 60, 75, 90, 120]; // دقائق مختلفة
    $test_hourly_rate = 20; // 20 جنيه/ساعة
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 مقارنة طرق الحساب</h4>";
    echo "<table style='width: 100%; border-collapse: collapse; margin-top: 15px;'>";
    echo "<thead>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>المدة</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الوقت الفعلي</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>تقريب الساعة</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الفرق</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>التوفير %</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($test_durations as $duration) {
        $comparison = compareBillingMethods($duration, $test_hourly_rate);
        
        $actual_cost = $comparison['actual_time']['cost'];
        $rounded_cost = $comparison['hourly_rounding']['cost'];
        $difference = $comparison['difference']['amount'];
        $percentage = $comparison['difference']['percentage'];
        
        // تحديد لون الصف
        $row_color = $difference > 0 ? '#d4edda' : ($difference < 0 ? '#f8d7da' : '#fff3cd');
        
        echo "<tr style='background: $row_color;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>$duration دقيقة</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($actual_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . number_format($rounded_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
        
        if ($difference > 0) {
            echo "<span style='color: green; font-weight: bold;'>-" . number_format($difference, 2) . " ج.م</span>";
        } elseif ($difference < 0) {
            echo "<span style='color: red; font-weight: bold;'>+" . number_format(abs($difference), 2) . " ج.م</span>";
        } else {
            echo "<span style='color: gray;'>لا يوجد فرق</span>";
        }
        
        echo "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>";
        
        if ($percentage > 0) {
            echo "<span style='color: green; font-weight: bold;'>" . number_format($percentage, 1) . "%</span>";
        } else {
            echo "<span style='color: gray;'>0%</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // اختبار الإعدادات الحالية
    echo "<h3>⚙️ اختبار الإعدادات الحالية</h3>";
    
    $current_method = getBillingMethod($pdo, $test_client_id);
    echo "<p><strong>طريقة الحساب الحالية للعميل $test_client_id:</strong> ";
    
    if ($current_method === 'actual_time') {
        echo "<span style='color: #17a2b8; font-weight: bold;'>الوقت الفعلي</span>";
    } else {
        echo "<span style='color: #ffc107; font-weight: bold;'>تقريب الساعة</span>";
    }
    echo "</p>";
    
    // اختبار حساب التكلفة مع الإعداد الحالي
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>💰 اختبار حساب التكلفة مع الإعداد الحالي</h4>";
    
    $test_duration = 45; // 45 دقيقة
    $cost_details = calculateTimeCostWithDetails($pdo, $test_client_id, $test_duration, $test_hourly_rate);
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border-left: 5px solid #007bff;'>";
    echo "<p><strong>مثال:</strong> جلسة مدتها $test_duration دقيقة بسعر $test_hourly_rate ج.م/ساعة</p>";
    echo "<p><strong>طريقة الحساب:</strong> " . ($cost_details['billing_method'] === 'actual_time' ? 'الوقت الفعلي' : 'تقريب الساعة') . "</p>";
    echo "<p><strong>التكلفة:</strong> <span style='color: #28a745; font-weight: bold; font-size: 18px;'>" . number_format($cost_details['cost'], 2) . " ج.م</span></p>";
    echo "<p><strong>تفاصيل الحساب:</strong> " . $cost_details['calculation_note'] . "</p>";
    echo "</div>";
    echo "</div>";
    
    // اختبار تغيير الإعداد
    echo "<h3>🔄 اختبار تغيير الإعداد</h3>";
    
    echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";
    
    // زر تغيير إلى الوقت الفعلي
    echo "<div style='flex: 1;'>";
    echo "<button onclick='changeBillingMethod(\"actual_time\")' style='width: 100%; padding: 15px; background: #17a2b8; color: white; border: none; border-radius: 8px; font-size: 16px; cursor: pointer;'>";
    echo "<i class='fas fa-clock'></i> تغيير إلى الوقت الفعلي";
    echo "</button>";
    echo "</div>";
    
    // زر تغيير إلى تقريب الساعة
    echo "<div style='flex: 1;'>";
    echo "<button onclick='changeBillingMethod(\"hourly_rounding\")' style='width: 100%; padding: 15px; background: #ffc107; color: #212529; border: none; border-radius: 8px; font-size: 16px; cursor: pointer;'>";
    echo "<i class='fas fa-hourglass-half'></i> تغيير إلى تقريب الساعة";
    echo "</button>";
    echo "</div>";
    
    echo "</div>";
    
    // روابط للاختبار
    echo "<hr>";
    echo "<h3>🔗 روابط للاختبار</h3>";
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #bee5eb;'>";
    echo "<h4>📝 خطوات الاختبار:</h4>";
    echo "<ol>";
    echo "<li><a href='client/invoice_settings.php' target='_blank' style='color: #007bff; text-decoration: underline;'>انتقل لصفحة إعدادات الفاتورة</a> وغير طريقة الحساب</li>";
    echo "<li><a href='client/sessions.php' target='_blank' style='color: #007bff; text decoration: underline;'>انتقل لصفحة الجلسات</a> وابدأ جلسة جديدة</li>";
    echo "<li>انتظر بضع دقائق ثم أنه الجلسة لرؤية الفرق في التكلفة</li>";
    echo "<li>جرب تغيير الإعداد ومقارنة النتائج</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4>✅ الميزات الجديدة:</h4>";
    echo "<ul>";
    echo "<li><strong>مرونة في الحساب:</strong> يمكن للعميل اختيار الطريقة المناسبة لعمله</li>";
    echo "<li><strong>شفافية:</strong> العميل يعرف بالضبط كيف يتم حساب التكلفة</li>";
    echo "<li><strong>عدالة:</strong> خيار الوقت الفعلي يضمن دفع العميل للوقت الذي قضاه فقط</li>";
    echo "<li><strong>مرونة تجارية:</strong> خيار تقريب الساعة للمحلات التي تفضل النظام التقليدي</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ تم تطبيق إعدادات طريقة الحساب بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>الآن يمكن للعملاء اختيار طريقة الحساب المفضلة لديهم.</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<script>
function changeBillingMethod(method) {
    if (confirm('هل تريد تغيير طريقة الحساب؟ سيؤثر هذا على جميع الجلسات الجديدة.')) {
        // هنا يمكن إضافة AJAX call لتغيير الإعداد
        alert('لتغيير الإعداد، انتقل إلى صفحة إعدادات الفاتورة');
        window.open('client/invoice_settings.php', '_blank');
    }
}
</script>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background: white;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}
</style>
