# ميزة تحديد نوع اللعب (فردي/زوجي)

## نظرة عامة
تم إضافة ميزة جديدة لتحديد نوع اللعب عند بدء الجلسة، حيث يمكن للمستخدم اختيار ما إذا كانت الجلسة فردية (لاعب واحد) أم زوجية (أكثر من لاعب).

## التغييرات المطلوبة

### 1. قاعدة البيانات
- تم إضافة عمود `game_type` إلى جدول `sessions`
- نوع العمود: `ENUM('single','multiplayer')`
- القيمة الافتراضية: `'single'`
- تم إضافة فهرس للأداء: `idx_sessions_game_type`

### 2. الملفات المحدثة

#### أ. ملفات قاعدة البيانات:
- `station.sql` - تحديث هيكل جدول sessions
- `add_game_type_column.sql` - سكريبت إضافة العمود للمستخدمين الحاليين
- `run_game_type_update.php` - ملف تشغيل التحديث

#### ب. ملفات الواجهة:
- `client/sessions.php` - إضافة خيارات نوع اللعب في النماذج وعرض النوع في الجلسات

#### ج. ملفات API:
- `client/api/update_session.php` - دعم تحديث نوع اللعب
- `client/api/get_session_details.php` - إرجاع نوع اللعب في تفاصيل الجلسة

## كيفية الاستخدام

### 1. إنشاء جلسة جديدة:
1. اضغط على "إضافة جلسة جديدة"
2. اختر الجهاز والعميل
3. حدد نوع الجلسة (مفتوحة/محددة الوقت)
4. **اختر نوع اللعب:**
   - فردي (لاعب واحد) - الخيار الافتراضي
   - زوجي (أكثر من لاعب)
5. أضف أي ملاحظات
6. اضغط "بدء الجلسة"

### 2. تعديل جلسة موجودة:
1. اضغط على "تعديل الجلسة" في بطاقة الجلسة النشطة
2. يمكنك تغيير نوع اللعب من فردي إلى زوجي أو العكس
3. احفظ التعديلات

### 3. عرض نوع اللعب:
- **الجلسات النشطة:** يظهر نوع اللعب كشارة ملونة في بطاقة الجلسة
  - فردي: شارة رمادية مع أيقونة مستخدم واحد
  - زوجي: شارة زرقاء مع أيقونة مستخدمين متعددين
- **الجلسات المكتملة:** يظهر نوع اللعب في عمود منفصل في الجدول

## التثبيت للمستخدمين الحاليين

### الطريقة الأولى - تشغيل ملف PHP:
1. افتح المتصفح واذهب إلى: `http://your-domain/playgood/run_game_type_update.php`
2. ستظهر رسالة تأكيد نجاح التحديث

### الطريقة الثانية - تشغيل SQL مباشرة:
```sql
-- إضافة العمود
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS game_type ENUM('single','multiplayer') DEFAULT 'single' COMMENT 'نوع اللعب: فردي أو زوجي';

-- تحديث الجلسات الموجودة
UPDATE sessions 
SET game_type = 'single' 
WHERE game_type IS NULL;

-- إضافة فهرس للأداء
ALTER TABLE sessions 
ADD INDEX IF NOT EXISTS idx_sessions_game_type (game_type);
```

## الاختبار
- استخدم ملف `test_game_type.php` للتأكد من أن الميزة تعمل بشكل صحيح
- يمكنك إنشاء جلسات تجريبية لاختبار كلا النوعين

## الميزات المضافة

### 1. واجهة المستخدم:
- خيارات راديو لاختيار نوع اللعب في نموذج إنشاء الجلسة
- خيارات راديو لتعديل نوع اللعب في نموذج التعديل
- عرض نوع اللعب بشارات ملونة في الجلسات النشطة
- عمود نوع اللعب في جدول الجلسات المكتملة

### 2. قاعدة البيانات:
- حفظ نوع اللعب مع كل جلسة
- دعم البحث والفلترة حسب نوع اللعب
- إحصائيات نوع اللعب

### 3. API:
- دعم إرسال نوع اللعب عند إنشاء الجلسة
- دعم تحديث نوع اللعب
- إرجاع نوع اللعب في تفاصيل الجلسة

## ملاحظات مهمة
- القيمة الافتراضية لنوع اللعب هي "فردي"
- جميع الجلسات الموجودة ستكون من النوع "فردي" بعد التحديث
- الميزة متوافقة مع جميع الميزات الموجودة (إضافة منتجات، تعديل الجلسة، إلخ)
- لا تؤثر على حساب التكلفة أو المدة

## استكشاف الأخطاء
إذا لم تظهر الميزة:
1. تأكد من تشغيل ملف التحديث `run_game_type_update.php`
2. تحقق من وجود العمود في قاعدة البيانات باستخدام `test_game_type.php`
3. امسح cache المتصفح وأعد تحميل الصفحة
4. تحقق من سجلات الأخطاء في PHP
