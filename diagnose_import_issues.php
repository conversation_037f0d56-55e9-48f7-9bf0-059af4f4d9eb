<?php
/**
 * أداة تشخيص مشاكل استيراد قاعدة البيانات
 * تساعد في تحديد أسباب فشل عملية الاستيراد
 */

require_once 'config/database.php';

// دالة فحص ملف SQL
function analyzeSQLFile($filePath) {
    if (!file_exists($filePath)) {
        return ['error' => 'الملف غير موجود'];
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        return ['error' => 'فشل في قراءة الملف'];
    }
    
    $analysis = [
        'file_size' => filesize($filePath),
        'file_size_mb' => round(filesize($filePath) / 1024 / 1024, 2),
        'encoding' => mb_detect_encoding($content, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true),
        'line_count' => substr_count($content, "\n") + 1,
        'semicolon_count' => substr_count($content, ';'),
        'create_table_count' => preg_match_all('/CREATE TABLE/i', $content),
        'insert_count' => preg_match_all('/INSERT INTO/i', $content),
        'drop_table_count' => preg_match_all('/DROP TABLE/i', $content),
        'comment_lines' => preg_match_all('/^--/m', $content),
        'has_foreign_keys' => preg_match('/FOREIGN KEY/i', $content) ? true : false,
        'has_triggers' => preg_match('/CREATE TRIGGER/i', $content) ? true : false,
        'has_procedures' => preg_match('/CREATE PROCEDURE/i', $content) ? true : false,
        'charset_declarations' => preg_match_all('/CHARSET=/i', $content),
        'collation_declarations' => preg_match_all('/COLLATE=/i', $content)
    ];
    
    // فحص الأخطاء الشائعة
    $potential_issues = [];
    
    if ($analysis['encoding'] !== 'UTF-8') {
        $potential_issues[] = 'تشفير الملف ليس UTF-8 (' . $analysis['encoding'] . ')';
    }
    
    if ($analysis['file_size'] > 50 * 1024 * 1024) {
        $potential_issues[] = 'حجم الملف كبير جداً (أكبر من 50 ميجابايت)';
    }
    
    if ($analysis['semicolon_count'] > 10000) {
        $potential_issues[] = 'عدد كبير من الاستعلامات قد يسبب timeout';
    }
    
    if (preg_match('/ENGINE=MyISAM/i', $content)) {
        $potential_issues[] = 'يحتوي على جداول MyISAM (قد تحتاج تحويل لـ InnoDB)';
    }
    
    $analysis['potential_issues'] = $potential_issues;
    
    return $analysis;
}

// دالة فحص إعدادات قاعدة البيانات
function checkDatabaseSettings() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        $settings = [];
        
        // فحص المتغيرات المهمة
        $variables = [
            'max_allowed_packet',
            'innodb_buffer_pool_size',
            'wait_timeout',
            'interactive_timeout',
            'max_execution_time',
            'foreign_key_checks',
            'sql_mode',
            'character_set_server',
            'collation_server'
        ];
        
        foreach ($variables as $var) {
            $stmt = $pdo->query("SHOW VARIABLES LIKE '$var'");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) {
                $settings[$var] = $result['Value'];
            }
        }
        
        return $settings;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشاكل استيراد قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { border: none; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0 !important; }
        .diagnostic-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .btn { border-radius: 25px; font-weight: 600; }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-10'>
            <div class='card'>
                <div class='card-header text-center py-4'>
                    <h2 class='mb-0'><i class='fas fa-stethoscope me-3'></i>تشخيص مشاكل استيراد قاعدة البيانات</h2>
                    <p class='mb-0 mt-2 opacity-75'>فحص شامل لتحديد أسباب فشل الاستيراد</p>
                </div>
                <div class='card-body p-4'>";

// فحص ملف station.sql
echo "<div class='diagnostic-card'>
        <h5><i class='fas fa-file-alt me-2'></i>فحص ملف station.sql</h5>";

$sqlFile = 'station.sql';
$analysis = analyzeSQLFile($sqlFile);

if (isset($analysis['error'])) {
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-triangle me-2'></i>{$analysis['error']}
          </div>";
} else {
    echo "<div class='row'>
            <div class='col-md-6'>
                <h6>معلومات الملف:</h6>
                <ul class='list-group list-group-flush'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>حجم الملف</span>
                        <span class='badge bg-info'>{$analysis['file_size_mb']} MB</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>التشفير</span>
                        <span class='badge " . ($analysis['encoding'] === 'UTF-8' ? 'bg-success' : 'bg-warning') . "'>{$analysis['encoding']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>عدد الأسطر</span>
                        <span class='badge bg-secondary'>" . number_format($analysis['line_count']) . "</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>عدد الاستعلامات</span>
                        <span class='badge bg-primary'>" . number_format($analysis['semicolon_count']) . "</span>
                    </li>
                </ul>
            </div>
            <div class='col-md-6'>
                <h6>محتوى الملف:</h6>
                <ul class='list-group list-group-flush'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>إنشاء جداول</span>
                        <span class='badge bg-success'>{$analysis['create_table_count']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>إدراج بيانات</span>
                        <span class='badge bg-info'>{$analysis['insert_count']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>حذف جداول</span>
                        <span class='badge bg-warning'>{$analysis['drop_table_count']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>تعليقات</span>
                        <span class='badge bg-secondary'>{$analysis['comment_lines']}</span>
                    </li>
                </ul>
            </div>
          </div>";
    
    if (!empty($analysis['potential_issues'])) {
        echo "<div class='alert alert-warning mt-3'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>مشاكل محتملة:</h6>
                <ul class='mb-0'>";
        foreach ($analysis['potential_issues'] as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul></div>";
    } else {
        echo "<div class='alert alert-success mt-3'>
                <i class='fas fa-check-circle me-2'></i>لا توجد مشاكل واضحة في الملف
              </div>";
    }
}

echo "</div>";

// فحص إعدادات قاعدة البيانات
echo "<div class='diagnostic-card'>
        <h5><i class='fas fa-database me-2'></i>فحص إعدادات قاعدة البيانات</h5>";

$dbSettings = checkDatabaseSettings();

if (isset($dbSettings['error'])) {
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-triangle me-2'></i>خطأ في الاتصال: {$dbSettings['error']}
          </div>";
} else {
    echo "<div class='row'>
            <div class='col-md-6'>
                <h6>إعدادات الذاكرة والحجم:</h6>
                <ul class='list-group list-group-flush'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>max_allowed_packet</span>
                        <span class='badge bg-info'>" . ($dbSettings['max_allowed_packet'] ?? 'غير محدد') . "</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>innodb_buffer_pool_size</span>
                        <span class='badge bg-info'>" . ($dbSettings['innodb_buffer_pool_size'] ?? 'غير محدد') . "</span>
                    </li>
                </ul>
            </div>
            <div class='col-md-6'>
                <h6>إعدادات الوقت والتشفير:</h6>
                <ul class='list-group list-group-flush'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>wait_timeout</span>
                        <span class='badge bg-secondary'>" . ($dbSettings['wait_timeout'] ?? 'غير محدد') . "</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>character_set_server</span>
                        <span class='badge " . (($dbSettings['character_set_server'] ?? '') === 'utf8mb4' ? 'bg-success' : 'bg-warning') . "'>" . ($dbSettings['character_set_server'] ?? 'غير محدد') . "</span>
                    </li>
                </ul>
            </div>
          </div>";
}

echo "</div>";

// توصيات الحلول
echo "<div class='diagnostic-card'>
        <h5><i class='fas fa-lightbulb me-2'></i>توصيات الحلول</h5>
        <div class='alert alert-info'>
            <h6>لحل مشاكل استيراد قاعدة البيانات:</h6>
            <ol>
                <li><strong>تأكد من تشفير الملف:</strong> يجب أن يكون الملف بتشفير UTF-8</li>
                <li><strong>قسم الملف الكبير:</strong> إذا كان الملف أكبر من 50 ميجابايت، قسمه لملفات أصغر</li>
                <li><strong>زيادة حدود الخادم:</strong> في ملف my.cnf أو my.ini:
                    <pre class='mt-2'>max_allowed_packet = 64M
wait_timeout = 600
interactive_timeout = 600</pre>
                </li>
                <li><strong>استخدم أداة محسنة:</strong> استخدم الأداة المحسنة أدناه للاستيراد</li>
            </ol>
        </div>
      </div>";

echo "<div class='text-center mt-4'>
        <a href='improved_import.php' class='btn btn-success me-2'>
            <i class='fas fa-upload me-1'></i>استيراد محسن
        </a>
        <a href='reimport_database.php' class='btn btn-primary me-2'>
            <i class='fas fa-database me-1'></i>إعادة الاستيراد
        </a>
        <a href='client/dashboard.php' class='btn btn-secondary'>
            <i class='fas fa-home me-1'></i>العودة للوحة التحكم
        </a>
      </div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
