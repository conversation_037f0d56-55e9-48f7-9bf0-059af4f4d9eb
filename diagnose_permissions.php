<?php
/**
 * ملف تشخيص مشكلة صلاحيات العملاء
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص مشكلة صلاحيات العملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 1000px; margin: 2rem auto; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-danger text-white'>";
echo "<h1 class='h3 mb-0'><i class='fas fa-bug me-2'></i>تشخيص مشكلة صلاحيات العملاء</h1>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<h2>1. التحقق من الاتصال بقاعدة البيانات</h2>";
    
    if (!$pdo) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    echo "<h2>2. التحقق من وجود الجداول المطلوبة</h2>";
    
    // التحقق من جدول client_pages
    $stmt = $pdo->query("SHOW TABLES LIKE 'client_pages'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ جدول client_pages موجود</p>";
        
        $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM client_pages");
        $count = $count_stmt->fetch()['count'];
        echo "<p class='info'>📊 عدد الصفحات: $count</p>";
        
        if ($count == 0) {
            echo "<p class='warning'>⚠️ الجدول فارغ - يحتاج إلى بيانات</p>";
        }
    } else {
        echo "<p class='error'>❌ جدول client_pages غير موجود</p>";
    }
    
    // التحقق من جدول client_page_permissions
    $stmt = $pdo->query("SHOW TABLES LIKE 'client_page_permissions'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ جدول client_page_permissions موجود</p>";
        
        $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM client_page_permissions");
        $count = $count_stmt->fetch()['count'];
        echo "<p class='info'>📊 عدد الصلاحيات: $count</p>";
        
        if ($count == 0) {
            echo "<p class='warning'>⚠️ الجدول فارغ - يحتاج إلى بيانات</p>";
        }
    } else {
        echo "<p class='error'>❌ جدول client_page_permissions غير موجود</p>";
    }
    
    echo "<h2>3. التحقق من العملاء الموجودين</h2>";
    
    $clients_stmt = $pdo->query("SELECT client_id, business_name, is_active FROM clients ORDER BY client_id");
    $clients = $clients_stmt->fetchAll();
    
    if (empty($clients)) {
        echo "<p class='error'>❌ لا يوجد عملاء في النظام</p>";
    } else {
        echo "<p class='success'>✅ يوجد " . count($clients) . " عميل في النظام</p>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>معرف العميل</th><th>اسم المحل</th><th>الحالة</th></tr></thead>";
        echo "<tbody>";
        foreach ($clients as $client) {
            $status = $client['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            echo "<tr><td>{$client['client_id']}</td><td>{$client['business_name']}</td><td>$status</td></tr>";
        }
        echo "</tbody></table>";
    }
    
    echo "<h2>4. اختبار دالة hasPagePermission</h2>";
    
    if (!empty($clients)) {
        $test_client_id = $clients[0]['client_id'];
        
        // محاكاة جلسة العميل
        session_start();
        $_SESSION['client_id'] = $test_client_id;
        
        // تضمين ملف التحقق من الصلاحيات
        require_once __DIR__ . '/client/includes/auth.php';
        
        $test_pages = ['dashboard', 'sessions', 'devices', 'customers', 'employees', 'cafeteria'];
        
        echo "<p class='info'>🧪 اختبار الصلاحيات للعميل رقم $test_client_id:</p>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>الصفحة</th><th>الصلاحية</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($test_pages as $page) {
            $has_permission = hasPagePermission($page);
            $status = $has_permission ? '<span class="badge bg-success">مسموح</span>' : '<span class="badge bg-danger">غير مسموح</span>';
            echo "<tr><td>$page</td><td>$status</td></tr>";
        }
        
        echo "</tbody></table>";
    }

    echo "<h2>5. التحقق من البيانات في الجداول</h2>";

    // عرض الصفحات المتاحة
    try {
        $pages_stmt = $pdo->query("SELECT page_name, page_label, is_default, is_active FROM client_pages ORDER BY category, page_label");
        $pages = $pages_stmt->fetchAll();

        if (!empty($pages)) {
            echo "<h3>الصفحات المتاحة:</h3>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>اسم الصفحة</th><th>التسمية</th><th>افتراضية</th><th>نشطة</th></tr></thead>";
            echo "<tbody>";

            foreach ($pages as $page) {
                $is_default = $page['is_default'] ? '<span class="badge bg-primary">نعم</span>' : '<span class="badge bg-secondary">لا</span>';
                $is_active = $page['is_active'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-danger">لا</span>';
                echo "<tr><td>{$page['page_name']}</td><td>{$page['page_label']}</td><td>$is_default</td><td>$is_active</td></tr>";
            }

            echo "</tbody></table>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في قراءة الصفحات: " . $e->getMessage() . "</p>";
    }

    // عرض صلاحيات العملاء
    try {
        if (!empty($clients)) {
            $permissions_stmt = $pdo->query("
                SELECT cpp.client_id, cp.page_name, cpp.is_enabled
                FROM client_page_permissions cpp
                JOIN client_pages cp ON cpp.page_id = cp.page_id
                ORDER BY cpp.client_id, cp.page_name
            ");
            $permissions = $permissions_stmt->fetchAll();

            if (!empty($permissions)) {
                echo "<h3>صلاحيات العملاء:</h3>";
                echo "<table class='table table-sm'>";
                echo "<thead><tr><th>معرف العميل</th><th>الصفحة</th><th>مفعلة</th></tr></thead>";
                echo "<tbody>";

                foreach ($permissions as $perm) {
                    $is_enabled = $perm['is_enabled'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-danger">لا</span>';
                    echo "<tr><td>{$perm['client_id']}</td><td>{$perm['page_name']}</td><td>$is_enabled</td></tr>";
                }

                echo "</tbody></table>";
            } else {
                echo "<p class='warning'>⚠️ لا توجد صلاحيات محددة للعملاء</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في قراءة الصلاحيات: " . $e->getMessage() . "</p>";
    }

    echo "<h2>6. الحلول المقترحة</h2>";

    echo "<div class='alert alert-info'>";
    echo "<h4>💡 خطوات الإصلاح:</h4>";
    echo "<ol>";
    echo "<li><strong>إذا كانت الجداول غير موجودة:</strong> قم بتشغيل <a href='setup_client_page_permissions.php' target='_blank'>ملف الإعداد</a></li>";
    echo "<li><strong>إذا كانت الجداول فارغة:</strong> قم بتشغيل الاستعلامات لإدراج البيانات الافتراضية</li>";
    echo "<li><strong>إذا كانت الصلاحيات غير محددة:</strong> قم بمنح الصلاحيات الافتراضية للعملاء</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='mt-4'>";
    echo "<a href='setup_client_page_permissions.php' class='btn btn-primary me-2'>إعداد النظام</a>";
    echo "<a href='admin/client_permissions.php' class='btn btn-success me-2'>إدارة الصلاحيات</a>";
    echo "<a href='client/dashboard.php' class='btn btn-secondary'>لوحة تحكم العميل</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء التشخيص</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
