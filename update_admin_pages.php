<?php
/**
 * تحديث صفحات الإدمن - إضافة جميع الصفحات الموجودة فعلياً
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تحديث صفحات الإدمن</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 1000px; margin-top: 2rem; }
        .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .step { margin-bottom: 1rem; padding: 1rem; border-radius: 10px; }
        .step.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        .table { font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header text-center'>
                <h3><i class='fas fa-sync-alt me-2'></i>تحديث صفحات الإدمن</h3>
                <p class='mb-0'>إضافة جميع الصفحات الموجودة فعلياً في مجلد admin</p>
            </div>
            <div class='card-body'>";

try {
    echo "<div class='step info'>
            <h5><i class='fas fa-info-circle me-2'></i>بدء عملية التحديث</h5>
            <p>جاري فحص وإضافة جميع صفحات الإدمن الموجودة فعلياً...</p>
          </div>";

    // 1. فحص الصفحات الموجودة فعلياً
    echo "<div class='step info'>
            <h6><i class='fas fa-search me-2'></i>الخطوة 1: فحص الصفحات الموجودة</h6>";
    
    $admin_dir = 'admin/';
    $php_files = glob($admin_dir . '*.php');
    $existing_pages = [];
    
    foreach ($php_files as $file) {
        $page_name = basename($file, '.php');
        // استثناء صفحات تسجيل الدخول والخروج
        if (!in_array($page_name, ['login', 'logout'])) {
            $existing_pages[] = $page_name;
        }
    }
    
    echo "<p><i class='fas fa-file me-1'></i>تم العثور على <strong>" . count($existing_pages) . "</strong> صفحة PHP في مجلد admin</p>";
    echo "<div class='row'>";
    foreach (array_chunk($existing_pages, 4) as $chunk) {
        foreach ($chunk as $page) {
            echo "<div class='col-md-3'><span class='badge bg-primary me-1'>$page.php</span></div>";
        }
    }
    echo "</div>";
    echo "</div>";

    // 2. تنفيذ ملف التحديث
    echo "<div class='step info'>
            <h6><i class='fas fa-database me-2'></i>الخطوة 2: تحديث قاعدة البيانات</h6>";
    
    $sql_file = 'update_admin_pages_complete.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL للتحديث غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL للتحديث");
    }
    
    // تنفيذ الملف
    try {
        $pdo->exec($sql_content);
        echo "<p class='text-success'><i class='fas fa-check me-1'></i>تم تنفيذ ملف التحديث بنجاح</p>";
    } catch (Exception $e) {
        // في حالة فشل التنفيذ الكامل، نحاول تقسيم الاستعلامات
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>فشل التنفيذ الكامل، جاري المحاولة بطريقة أخرى...</p>";
        
        $queries = explode(';', $sql_content);
        $executed_queries = 0;
        $failed_queries = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^(--|\/\*|\s*$)/', $query)) {
                try {
                    $pdo->exec($query);
                    $executed_queries++;
                } catch (Exception $e) {
                    $failed_queries++;
                    if (!strpos($e->getMessage(), 'already exists') && !strpos($e->getMessage(), 'Duplicate entry')) {
                        echo "<div class='alert alert-warning'>خطأ في الاستعلام: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                }
            }
        }
        
        echo "<p class='text-info'><i class='fas fa-info me-1'></i>تم تنفيذ $executed_queries استعلام، فشل $failed_queries استعلام</p>";
    }
    
    echo "</div>";

    // 3. عرض النتائج
    echo "<div class='step info'>
            <h6><i class='fas fa-chart-bar me-2'></i>الخطوة 3: عرض النتائج</h6>";
    
    // عدد الصفحات الإجمالي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_pages WHERE is_active = 1");
    $total_pages = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-file me-1'></i>إجمالي الصفحات في النظام: <strong>$total_pages</strong></p>";
    
    // عدد الصلاحيات الممنوحة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_page_permissions");
    $total_permissions = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-shield-alt me-1'></i>إجمالي الصلاحيات الممنوحة: <strong>$total_permissions</strong></p>";
    
    echo "</div>";

    // 4. عرض الصفحات حسب الفئة
    echo "<div class='card'>
            <div class='card-header'><h5><i class='fas fa-list me-2'></i>الصفحات حسب الفئة</h5></div>
            <div class='card-body'>";
    
    $stmt = $pdo->query("
        SELECT 
            category,
            COUNT(*) as page_count,
            GROUP_CONCAT(page_label ORDER BY page_label SEPARATOR '، ') as pages
        FROM admin_pages 
        WHERE is_active = 1 
        GROUP BY category 
        ORDER BY category
    ");
    $categories = $stmt->fetchAll();
    
    if (!empty($categories)) {
        echo "<div class='table-responsive'>
                <table class='table table-striped'>
                    <thead>
                        <tr>
                            <th>الفئة</th>
                            <th>عدد الصفحات</th>
                            <th>الصفحات</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        $category_names = [
            'main' => 'الصفحات الأساسية',
            'clients' => 'إدارة العملاء',
            'reports' => 'التقارير والإحصائيات',
            'settings' => 'الإعدادات',
            'admin' => 'إدارة النظام',
            'system' => 'النظام والنسخ الاحتياطية',
            'testing' => 'الاختبار',
            'maintenance' => 'الصيانة'
        ];
        
        foreach ($categories as $category) {
            $category_display = $category_names[$category['category']] ?? $category['category'];
            echo "<tr>
                    <td><span class='badge bg-secondary'>{$category_display}</span></td>
                    <td><span class='badge bg-primary'>{$category['page_count']}</span></td>
                    <td><small>{$category['pages']}</small></td>
                  </tr>";
        }
        
        echo "    </tbody>
                </table>
              </div>";
    }
    echo "</div></div>";

    // 5. عرض صلاحيات المديرين
    echo "<div class='card'>
            <div class='card-header'><h5><i class='fas fa-users-cog me-2'></i>صلاحيات المديرين</h5></div>
            <div class='card-body'>";
    
    $stmt = $pdo->query("
        SELECT 
            a.username,
            a.full_name,
            a.role,
            COUNT(app.id) as granted_permissions
        FROM admins a
        LEFT JOIN admin_page_permissions app ON a.admin_id = app.admin_id
        WHERE a.is_active = 1
        GROUP BY a.admin_id
        ORDER BY a.role DESC, a.full_name
    ");
    $admin_permissions = $stmt->fetchAll();
    
    if (!empty($admin_permissions)) {
        echo "<div class='table-responsive'>
                <table class='table table-striped'>
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>الدور</th>
                            <th>عدد الصلاحيات</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($admin_permissions as $admin) {
            $role_badge = $admin['role'] === 'super_admin' ? 'bg-danger' : 'bg-primary';
            $role_text = $admin['role'] === 'super_admin' ? 'مدير عام' : 'مدير';
            
            echo "<tr>
                    <td><code>{$admin['username']}</code></td>
                    <td>{$admin['full_name']}</td>
                    <td><span class='badge {$role_badge}'>{$role_text}</span></td>
                    <td><span class='badge bg-success'>{$admin['granted_permissions']}</span></td>
                  </tr>";
        }
        
        echo "    </tbody>
                </table>
              </div>";
    }
    echo "</div></div>";

    // 6. النتيجة النهائية
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle me-2'></i>تم التحديث بنجاح!</h5>
            <p>تم إضافة جميع صفحات الإدمن الموجودة فعلياً إلى نظام الصلاحيات. يمكنك الآن:</p>
            <ul>
                <li>إدارة صلاحيات جميع الصفحات من <a href='admin/admin_permissions.php' class='btn btn-sm btn-primary'>صفحة إدارة الصلاحيات</a></li>
                <li>تخصيص الوصول لكل صفحة على حدة</li>
                <li>التحكم في من يمكنه الوصول لصفحات الاختبار والصيانة</li>
            </ul>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-circle me-2'></i>حدث خطأ!</h5>
            <p class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "        </div>
        </div>
        
        <div class='text-center mt-4'>
            <a href='test_admin_permissions.php' class='btn btn-warning me-2'>
                <i class='fas fa-test-tube me-2'></i>اختبار النظام
            </a>
            <a href='admin/admin_permissions.php' class='btn btn-success me-2'>
                <i class='fas fa-user-cog me-2'></i>إدارة الصلاحيات
            </a>
            <a href='admin/dashboard.php' class='btn btn-primary'>
                <i class='fas fa-arrow-left me-2'></i>لوحة التحكم
            </a>
        </div>
    </div>
    
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
