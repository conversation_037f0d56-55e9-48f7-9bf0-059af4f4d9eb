<?php
/**
 * سكريبت تنظيف البيانات التجريبية المنتهية الصلاحية
 * يتم تشغيله دورياً عبر Cron Job أو يدوياً
 */

require_once '../config/trial-config.php';

// التحقق من صحة الطلب (اختياري - لحماية إضافية)
$allowed_ips = ['127.0.0.1', '::1']; // السماح للخادم المحلي فقط
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !isset($_GET['force'])) {
    http_response_code(403);
    die('غير مسموح');
}

// تسجيل بداية عملية التنظيف
$log_message = "[" . date('Y-m-d H:i:s') . "] بدء عملية تنظيف البيانات التجريبية\n";
error_log($log_message, 3, '../logs/trial-cleanup.log');

try {
    // الحصول على قائمة الحسابات التجريبية المنتهية الصلاحية
    $stmt = $pdo->prepare("
        SELECT trial_id, business_name, email, trial_end 
        FROM trial_clients 
        WHERE trial_end < NOW()
    ");
    $stmt->execute();
    $expired_trials = $stmt->fetchAll();
    
    $deleted_count = 0;
    
    foreach ($expired_trials as $trial) {
        // تسجيل الحساب المراد حذفه
        $log_message = "[" . date('Y-m-d H:i:s') . "] حذف الحساب التجريبي: {$trial['business_name']} ({$trial['email']}) - انتهت في: {$trial['trial_end']}\n";
        error_log($log_message, 3, '../logs/trial-cleanup.log');
        
        // حذف البيانات المرتبطة (سيتم حذفها تلقائياً بسبب CASCADE)
        $delete_stmt = $pdo->prepare("DELETE FROM trial_clients WHERE trial_id = ?");
        if ($delete_stmt->execute([$trial['trial_id']])) {
            $deleted_count++;
        }
    }
    
    // تنظيف إضافي للجلسات المعلقة (أكثر من 24 ساعة)
    $cleanup_sessions = $pdo->prepare("
        DELETE FROM trial_sessions 
        WHERE status = 'active' AND start_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $cleanup_sessions->execute();
    $cleaned_sessions = $cleanup_sessions->rowCount();
    
    // تسجيل نتائج التنظيف
    $log_message = "[" . date('Y-m-d H:i:s') . "] انتهاء عملية التنظيف - تم حذف {$deleted_count} حساب تجريبي و {$cleaned_sessions} جلسة معلقة\n";
    error_log($log_message, 3, '../logs/trial-cleanup.log');
    
    // إرجاع النتيجة
    $result = [
        'success' => true,
        'deleted_trials' => $deleted_count,
        'cleaned_sessions' => $cleaned_sessions,
        'message' => "تم تنظيف {$deleted_count} حساب تجريبي منتهي الصلاحية و {$cleaned_sessions} جلسة معلقة"
    ];
    
} catch (Exception $e) {
    // تسجيل الخطأ
    $error_message = "[" . date('Y-m-d H:i:s') . "] خطأ في عملية التنظيف: " . $e->getMessage() . "\n";
    error_log($error_message, 3, '../logs/trial-cleanup.log');
    
    $result = [
        'success' => false,
        'error' => $e->getMessage(),
        'message' => 'حدث خطأ أثناء تنظيف البيانات التجريبية'
    ];
}

// إرجاع النتيجة كـ JSON إذا كان الطلب عبر AJAX
if (isset($_GET['json']) || (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false)) {
    header('Content-Type: application/json');
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
} else {
    // عرض النتيجة كـ HTML
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تنظيف البيانات التجريبية - PlayGood</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
        <style>
            body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
            .container { max-width: 600px; margin-top: 50px; }
            .card { border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card">
                <div class="card-header text-center">
                    <h4><i class="fas fa-broom me-2"></i>تنظيف البيانات التجريبية</h4>
                </div>
                <div class="card-body text-center">
                    <?php if ($result['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $result['message']; ?>
                        </div>
                        <p class="text-muted">
                            تم حذف <?php echo $result['deleted_trials']; ?> حساب تجريبي<br>
                            تم تنظيف <?php echo $result['cleaned_sessions']; ?> جلسة معلقة
                        </p>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $result['message']; ?>
                        </div>
                        <p class="text-muted">الخطأ: <?php echo htmlspecialchars($result['error']); ?></p>
                    <?php endif; ?>
                    
                    <a href="../index.php" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
