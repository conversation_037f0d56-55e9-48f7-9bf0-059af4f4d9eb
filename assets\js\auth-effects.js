/**
 * ملف الجافا سكريبت للتأثيرات التفاعلية المتقدمة في صفحات المصادقة
 * PlayGood Authentication Effects
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // تأثير تتبع الماوس للإضاءة
    function initMouseTracking() {
        const authCards = document.querySelectorAll('.auth-card-enhanced');
        
        authCards.forEach(card => {
            // إضافة عنصر الضوء المتحرك
            const spotlight = document.createElement('div');
            spotlight.className = 'auth-spotlight';
            card.appendChild(spotlight);
            
            card.addEventListener('mousemove', function(e) {
                const rect = card.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;
                
                spotlight.style.setProperty('--mouse-x', x + '%');
                spotlight.style.setProperty('--mouse-y', y + '%');
            });
        });
    }
    
    // تأثير الموجات عند النقر
    function initRippleEffect() {
        const buttons = document.querySelectorAll('.auth-btn-modern');
        
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                const ripple = document.createElement('div');
                ripple.className = 'auth-ripple';
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }
    
    // تأثير النجوم المتحركة
    function initStarsEffect() {
        const authPage = document.querySelector('.auth-page');
        if (authPage) {
            const stars = document.createElement('div');
            stars.className = 'auth-stars';
            authPage.appendChild(stars);
        }
    }
    
    // تأثير الضباب
    function initFogEffect() {
        const authPage = document.querySelector('.auth-page');
        if (authPage) {
            const fog = document.createElement('div');
            fog.className = 'auth-fog';
            authPage.appendChild(fog);
        }
    }
    
    // تأثير الهولوجرام للبطاقات
    function initHologramEffect() {
        const authCards = document.querySelectorAll('.auth-card-enhanced');
        
        authCards.forEach(card => {
            card.classList.add('auth-hologram');
        });
    }
    
    // تأثير الكريستال للحقول
    function initCrystalEffect() {
        const formControls = document.querySelectorAll('.auth-form-control');
        
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.classList.add('auth-crystal');
            });
            
            control.addEventListener('blur', function() {
                this.classList.remove('auth-crystal');
            });
        });
    }
    
    // تأثير الإضاءة الجانبية للأزرار
    function initSideGlowEffect() {
        const buttons = document.querySelectorAll('.auth-btn-modern');
        
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.classList.add('auth-side-glow');
            });
            
            button.addEventListener('mouseleave', function() {
                this.classList.remove('auth-side-glow');
            });
        });
    }
    
    // تأثير الظهور للأخطاء - بدون اهتزاز
    function initErrorEffect() {
        const errorAlerts = document.querySelectorAll('.auth-alert-danger');

        errorAlerts.forEach(alert => {
            alert.style.animation = 'errorFadeIn 0.4s ease-out';
            setTimeout(() => {
                alert.style.animation = '';
            }, 400);
        });
    }
    
    // تأثير الكتابة المتقدم
    function initAdvancedTypingEffect() {
        const inputs = document.querySelectorAll('.auth-form-control');
        
        inputs.forEach(input => {
            let typingTimer;
            
            input.addEventListener('keydown', function() {
                this.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            });
            
            input.addEventListener('keyup', function() {
                clearTimeout(typingTimer);
                
                typingTimer = setTimeout(() => {
                    this.style.boxShadow = '';
                }, 300);
            });
        });
    }
    
    // تأثير التحميل المحسن
    function initLoadingEffect() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('.auth-btn-modern');
                if (submitBtn) {
                    submitBtn.classList.add('auth-loading');
                    
                    // إضافة تأثير النبض
                    submitBtn.style.animation = 'pulse 1s infinite';
                }
            });
        });
    }
    
    // تأثير التحقق من صحة البيانات البصري
    function initVisualValidation() {
        const inputs = document.querySelectorAll('.auth-form-control');
        
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                const isValid = this.checkValidity();
                const icon = this.parentElement.querySelector('.auth-form-icon-enhanced');
                
                if (this.value.length > 0) {
                    if (isValid) {
                        this.style.borderColor = '#198754';
                        this.style.boxShadow = '0 0 0 3px rgba(25, 135, 84, 0.1)';
                        if (icon) {
                            icon.style.color = '#198754';
                            icon.className = icon.className.replace(/fa-\w+/, 'fa-check-circle');
                        }
                    } else {
                        this.style.borderColor = '#dc3545';
                        this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
                        if (icon) {
                            icon.style.color = '#dc3545';
                            icon.className = icon.className.replace(/fa-\w+/, 'fa-exclamation-circle');
                        }
                    }
                } else {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                    if (icon) {
                        icon.style.color = '';
                        // إعادة الأيقونة الأصلية
                        if (input.type === 'email') {
                            icon.className = icon.className.replace(/fa-\w+/, 'fa-envelope');
                        } else if (input.type === 'password') {
                            icon.className = icon.className.replace(/fa-\w+/, 'fa-lock');
                        } else if (input.type === 'text') {
                            icon.className = icon.className.replace(/fa-\w+/, 'fa-user');
                        }
                    }
                }
            });
        });
    }
    
    // تأثير التركيز المحسن
    function initEnhancedFocus() {
        const formGroups = document.querySelectorAll('.auth-form-floating');
        
        formGroups.forEach(group => {
            const input = group.querySelector('.auth-form-control');
            const label = group.querySelector('.auth-form-label');
            const icon = group.querySelector('.auth-form-icon-enhanced');
            
            if (input) {
                input.addEventListener('focus', function() {
                    group.style.transform = 'scale(1.02)';
                    group.style.zIndex = '10';
                    
                    if (label) {
                        label.style.color = 'var(--auth-primary)';
                        label.style.fontWeight = '600';
                    }
                    
                    if (icon) {
                        icon.style.transform = 'translateY(-50%) scale(1.2)';
                        icon.style.color = 'var(--auth-primary)';
                    }
                });
                
                input.addEventListener('blur', function() {
                    group.style.transform = '';
                    group.style.zIndex = '';
                    
                    if (label) {
                        label.style.color = '';
                        label.style.fontWeight = '';
                    }
                    
                    if (icon) {
                        icon.style.transform = '';
                        icon.style.color = '';
                    }
                });
            }
        });
    }
    
    // تأثير الانتقال السلس بين الصفحات
    function initPageTransition() {
        const links = document.querySelectorAll('.auth-link-modern, .auth-link-alt');
        
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const href = this.getAttribute('href');
                
                // تأثير التلاشي
                document.body.style.opacity = '0';
                document.body.style.transform = 'scale(0.95)';
                document.body.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    window.location.href = href;
                }, 300);
            });
        });
    }
    
    // تهيئة جميع التأثيرات
    function initAllEffects() {
        // التحقق من دعم المتصفح للتأثيرات المتقدمة
        if (window.CSS && CSS.supports('backdrop-filter', 'blur(10px)')) {
            initMouseTracking();
            initHologramEffect();
            initCrystalEffect();
            initSideGlowEffect();
        }
        
        initRippleEffect();
        initStarsEffect();
        initFogEffect();
        initErrorEffect();
        initAdvancedTypingEffect();
        initLoadingEffect();
        initVisualValidation();
        initEnhancedFocus();
        initPageTransition();
        
        // إضافة تأثير الدخول للصفحة
        document.body.style.opacity = '0';
        document.body.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            document.body.style.transition = 'all 0.5s ease';
            document.body.style.opacity = '1';
            document.body.style.transform = 'scale(1)';
        }, 100);
    }
    
    // تشغيل التأثيرات
    initAllEffects();
    
    // إضافة أنماط CSS إضافية
    const additionalStyles = `
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes errorFadeIn {
            0% { opacity: 0; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        body {
            transition: all 0.3s ease;
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = additionalStyles;
    document.head.appendChild(styleSheet);
});
