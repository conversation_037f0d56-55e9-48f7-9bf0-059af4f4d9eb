# إضافة الصفحات المطلوبة لنظام صلاحيات العملاء - PlayGood

## نظرة عامة
تم إضافة الصفحات المطلوبة التالية لنظام صلاحيات العملاء:
- **الأوردرات** - إدارة الطلبات والأوردرات المستقلة
- **الماليات** - إدارة الأمور المالية العامة
- **الإيرادات** - إدارة وتتبع الإيرادات
- **المصروفات** - إدارة وتتبع المصروفات
- **الموظفين** - إدارة بيانات وصلاحيات الموظفين
- **الغرف** - تنظيم الأجهزة في غرف وقاعات

## الملفات المحدثة

### 1. ملفات قاعدة البيانات
- `create_client_page_permissions_system.sql` - تم تحديثه لإضافة صفحات الإيرادات والمصروفات
- `add_missing_client_pages.sql` - تم تحديثه لإضافة الصفحات المطلوبة فقط

### 2. ملفات الإعداد
- `setup_missing_client_pages.php` - تم تحديثه لإضافة الصفحات المطلوبة

## طريقة التشغيل

### الخطوة 1: إضافة الصفحات الجديدة
قم بزيارة الرابط التالي لإضافة الصفحات المطلوبة:
```
http://localhost/playgood/setup_missing_client_pages.php
```

### الخطوة 2: إدارة صلاحيات العملاء
بعد إضافة الصفحات، يمكنك إدارة صلاحيات العملاء من:
```
http://localhost/playgood/admin/client_permissions.php
```

## الصفحات المضافة

### 1. صفحة الأوردرات
- **المعرف:** `orders`
- **الملف:** `orders.php`
- **الفئة:** `orders`
- **الوصف:** إدارة الطلبات والأوردرات المستقلة
- **افتراضية:** لا

### 2. صفحة الماليات
- **المعرف:** `finances`
- **الملف:** `finances.php`
- **الفئة:** `finances`
- **الوصف:** إدارة الأمور المالية العامة
- **افتراضية:** لا

### 3. صفحة الإيرادات
- **المعرف:** `revenues`
- **الملف:** `revenues.php`
- **الفئة:** `finances`
- **الوصف:** إدارة وتتبع الإيرادات
- **افتراضية:** لا

### 4. صفحة المصروفات
- **المعرف:** `expenses`
- **الملف:** `expenses.php`
- **الفئة:** `finances`
- **الوصف:** إدارة وتتبع المصروفات
- **افتراضية:** لا

### 5. صفحة الموظفين
- **المعرف:** `employees`
- **الملف:** `employees.php`
- **الفئة:** `employees`
- **الوصف:** إدارة بيانات وصلاحيات الموظفين
- **افتراضية:** لا

### 6. صفحة الغرف
- **المعرف:** `rooms`
- **الملف:** `rooms.php`
- **الفئة:** `devices`
- **الوصف:** تنظيم الأجهزة في غرف وقاعات
- **افتراضية:** لا

## كيفية الاستخدام

### 1. تفعيل الصفحات للعملاء
1. اذهب إلى صفحة إدارة صلاحيات العملاء
2. اختر العميل من القائمة المنسدلة
3. ستظهر جميع الصفحات مجمعة حسب الفئات:
   - **إدارة الطلبات:** الأوردرات
   - **الإدارة المالية:** الماليات، الإيرادات، المصروفات
   - **إدارة الموظفين:** الموظفين
   - **الأجهزة والغرف:** الغرف
4. استخدم مفاتيح التبديل لتفعيل أو تعطيل الصفحات
5. التغييرات تُحفظ تلقائياً

### 2. فهم الشارات
- **🏠 افتراضي:** صفحة متاحة افتراضياً للعملاء الجدد
- **⚙️ اختياري:** صفحة تحتاج موافقة الأدمن للوصول إليها (جميع الصفحات المضافة اختيارية)

### 3. إعادة تعيين الصلاحيات
يمكنك إعادة تعيين صلاحيات أي عميل إلى الإعدادات الافتراضية باستخدام زر "إعادة تعيين للافتراضي"

## ملاحظات مهمة

### 1. الصفحات الافتراضية
جميع الصفحات المضافة هي **اختيارية** (غير افتراضية)، مما يعني:
- لن تظهر للعملاء الجدد تلقائياً
- يجب تفعيلها يدوياً لكل عميل حسب احتياجاته
- يمكن تعديل هذا السلوك من قاعدة البيانات إذا لزم الأمر

### 2. إنشاء الصفحات الفعلية
هذا النظام يدير **صلاحيات الوصول** فقط. يجب إنشاء الصفحات الفعلية في مجلد العميل:
- `client/orders.php`
- `client/finances.php`
- `client/revenues.php`
- `client/expenses.php`
- `client/employees.php`
- `client/rooms.php`

### 3. التحقق من الصلاحيات
تأكد من أن كل صفحة تتحقق من صلاحيات الوصول باستخدام:
```php
// في بداية كل صفحة
require_once 'includes/auth.php';
checkPagePermission('page_name'); // استبدل page_name بالمعرف الصحيح
```

## الدعم والمساعدة
إذا واجهت أي مشاكل:
1. تأكد من تشغيل `setup_client_page_permissions.php` أولاً
2. تحقق من وجود جداول `client_pages` و `client_page_permissions` في قاعدة البيانات
3. تأكد من صلاحيات الأدمن للوصول لصفحة إدارة الصلاحيات

## الخطوات التالية
بعد إضافة الصفحات وتفعيلها للعملاء، يمكنك:
1. إنشاء الصفحات الفعلية في مجلد العميل
2. تخصيص محتوى كل صفحة حسب احتياجات النظام
3. إضافة المزيد من الصفحات إذا لزم الأمر باستخدام نفس الطريقة
