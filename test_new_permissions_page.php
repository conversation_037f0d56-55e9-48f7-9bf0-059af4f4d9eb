<?php
/**
 * اختبار صفحة إدارة الصلاحيات الجديدة - PlayGood
 * هذا الملف لاختبار الصفحة المنفصلة لإدارة صلاحيات الموظفين
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار صفحة إدارة الصلاحيات الجديدة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .test-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container py-4'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-shield-alt me-2'></i>اختبار صفحة إدارة الصلاحيات الجديدة</h3>
        </div>
        <div class='card-body'>";

try {
    // 1. اختبار وجود الملفات الجديدة
    echo "<div class='test-section'>
            <h5><i class='fas fa-file-code me-2'></i>اختبار وجود الملفات الجديدة</h5>";
    
    $new_files = [
        'client/employee_permissions.php' => 'صفحة إدارة الصلاحيات المنفصلة',
        'client/assets/css/permissions.css' => 'ملف CSS المخصص للصلاحيات'
    ];
    
    foreach ($new_files as $file => $description) {
        if (file_exists($file)) {
            $file_size = number_format(filesize($file) / 1024, 2);
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>$description موجود - حجم الملف: {$file_size} KB
                  </div>";
        } else {
            echo "<div class='test-result test-error'>
                    <i class='fas fa-times me-2'></i>$description غير موجود ($file)
                  </div>";
        }
    }
    echo "</div>";
    
    // 2. اختبار التحديثات في صفحة الموظفين
    echo "<div class='test-section'>
            <h5><i class='fas fa-edit me-2'></i>اختبار التحديثات في صفحة الموظفين</h5>";
    
    if (file_exists('client/employees.php')) {
        $employees_content = file_get_contents('client/employees.php');
        
        // تحقق من وجود الرابط الجديد
        if (strpos($employees_content, 'employee_permissions.php?id=') !== false) {
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>تم تحديث زر الصلاحيات ليوجه للصفحة الجديدة
                  </div>";
        } else {
            echo "<div class='test-result test-error'>
                    <i class='fas fa-times me-2'></i>لم يتم العثور على الرابط الجديد في صفحة الموظفين
                  </div>";
        }
        
        // تحقق من إزالة النوافذ المنبثقة
        if (strpos($employees_content, 'permissionsModal') === false) {
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>تم إزالة النوافذ المنبثقة للصلاحيات بنجاح
                  </div>";
        } else {
            echo "<div class='test-result test-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>لا تزال هناك بقايا من النوافذ المنبثقة
                  </div>";
        }
        
    } else {
        echo "<div class='test-result test-error'>
                <i class='fas fa-times me-2'></i>ملف employees.php غير موجود
              </div>";
    }
    echo "</div>";
    
    // 3. اختبار الموظفين المتاحين للاختبار
    echo "<div class='test-section'>
            <h5><i class='fas fa-users me-2'></i>الموظفين المتاحين للاختبار</h5>";
    
    try {
        $employees = $pdo->query("
            SELECT id, name, role, custom_permissions 
            FROM employees 
            ORDER BY name 
            LIMIT 10
        ")->fetchAll();
        
        if (empty($employees)) {
            echo "<div class='test-result test-warning'>
                    <i class='fas fa-info-circle me-2'></i>لا يوجد موظفين في النظام للاختبار
                    <br><small>يمكنك إضافة موظف جديد من صفحة الموظفين لاختبار النظام</small>
                  </div>";
        } else {
            echo "<div class='test-result test-info'>
                    <i class='fas fa-info-circle me-2'></i>تم العثور على " . count($employees) . " موظف للاختبار:
                  </div>";
            
            foreach ($employees as $employee) {
                $permission_type = $employee['custom_permissions'] ? 'صلاحيات مخصصة' : 'صلاحيات الدور';
                $badge_class = $employee['custom_permissions'] ? 'bg-info' : 'bg-secondary';
                
                echo "<div class='test-result test-success'>
                        <i class='fas fa-user me-2'></i>
                        <strong>{$employee['name']}</strong> ({$employee['role']}) - 
                        <span class='badge $badge_class'>$permission_type</span>
                        <br><small>
                            <a href='client/employee_permissions.php?id={$employee['id']}' target='_blank' class='btn btn-sm btn-outline-primary mt-1'>
                                <i class='fas fa-shield-alt me-1'></i>اختبار إدارة الصلاحيات
                            </a>
                        </small>
                      </div>";
            }
        }
    } catch (PDOException $e) {
        echo "<div class='test-result test-error'>
                <i class='fas fa-exclamation-triangle me-2'></i>خطأ في جلب بيانات الموظفين: {$e->getMessage()}
              </div>";
    }
    echo "</div>";
    
    // 4. اختبار الميزات الجديدة
    echo "<div class='test-section'>
            <h5><i class='fas fa-star me-2'></i>الميزات الجديدة في الصفحة المنفصلة</h5>";
    
    $new_features = [
        'واجهة مستقلة وثابتة' => 'لا توجد مشاكل النوافذ المنبثقة',
        'تصميم محسن ومتجاوب' => 'يعمل بشكل مثالي على جميع الأجهزة',
        'أدوات تحكم متقدمة' => 'تحديد حسب الدور، الصفحات الأساسية، إلخ',
        'معاينة مباشرة' => 'عرض الصلاحيات والصفحات المحددة فوراً',
        'إحصائيات تفاعلية' => 'عدادات تتحدث تلقائياً',
        'تجربة مستخدم محسنة' => 'تأثيرات بصرية وانتقالات سلسة'
    ];
    
    foreach ($new_features as $feature => $description) {
        echo "<div class='test-result test-success'>
                <i class='fas fa-check me-2'></i>
                <strong>$feature:</strong> $description
              </div>";
    }
    echo "</div>";
    
    // 5. اختبار التوافق مع النظام الحالي
    echo "<div class='test-section'>
            <h5><i class='fas fa-cogs me-2'></i>اختبار التوافق مع النظام الحالي</h5>";
    
    // تحقق من وجود الجداول المطلوبة
    $required_tables = ['permissions', 'pages', 'employee_permissions', 'employee_pages'];
    $all_tables_exist = true;
    
    foreach ($required_tables as $table) {
        try {
            $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<div class='test-result test-success'>
                    <i class='fas fa-check me-2'></i>جدول $table متوفر ويعمل
                  </div>";
        } catch (PDOException $e) {
            $all_tables_exist = false;
            echo "<div class='test-result test-error'>
                    <i class='fas fa-times me-2'></i>جدول $table غير متوفر
                  </div>";
        }
    }
    
    if ($all_tables_exist) {
        echo "<div class='test-result test-success'>
                <i class='fas fa-thumbs-up me-2'></i>
                <strong>جميع الجداول متوفرة - النظام جاهز للاستخدام!</strong>
              </div>";
    } else {
        echo "<div class='test-result test-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                بعض الجداول مفقودة - قم بتشغيل setup_employee_permissions.php أولاً
              </div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-result test-error'>
            <i class='fas fa-exclamation-triangle me-2'></i>خطأ عام في الاختبار: {$e->getMessage()}
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='client/employees.php' class='btn btn-primary me-2' target='_blank'>
                <i class='fas fa-users me-2'></i>فتح صفحة الموظفين
            </a>
            <a href='setup_employee_permissions.php' class='btn btn-secondary me-2' target='_blank'>
                <i class='fas fa-cog me-2'></i>إعداد النظام
            </a>
            <a href='test_permissions_system.php' class='btn btn-info'>
                <i class='fas fa-vial me-2'></i>اختبار النظام الكامل
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
