/* ملف CSS للتأثيرات المتحركة المتقدمة - PlayGood */
/* تأثيرات حركية احترافية وجذابة */

/* تأثيرات الدخول للصفحة - مخففة */
@keyframes pageEnter {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-page {
    animation: pageEnter 0.6s ease-out;
}

/* تأثيرات البطاقة - مخففة */
@keyframes cardSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-card-enhanced {
    animation: cardSlideIn 0.8s ease-out 0.2s both;
}

/* تأثيرات الأيقونة - مخففة */
@keyframes iconFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.auth-icon {
    animation: iconFadeIn 0.6s ease-out 0.4s both;
}

/* تأثيرات العنوان - مخففة */
@keyframes titleFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-title {
    animation: titleFadeIn 0.5s ease-out 0.6s both;
}

/* تأثيرات النماذج - مخففة */
@keyframes formSlideUp {
    0% {
        opacity: 0;
        transform: translateY(15px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form-floating {
    animation: formSlideUp 0.4s ease-out both;
}

.auth-form-floating:nth-child(1) { animation-delay: 0.8s; }
.auth-form-floating:nth-child(2) { animation-delay: 0.9s; }
.auth-form-floating:nth-child(3) { animation-delay: 1.0s; }
.auth-form-floating:nth-child(4) { animation-delay: 1.1s; }
.auth-form-floating:nth-child(5) { animation-delay: 1.2s; }
.auth-form-floating:nth-child(6) { animation-delay: 1.3s; }

/* تأثيرات الأزرار - مخففة */
@keyframes buttonFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-btn-modern {
    animation: buttonFadeIn 0.5s ease-out 1.4s both;
}

/* تأثيرات الروابط */
@keyframes linkFadeIn {
    0% {
        opacity: 0;
        transform: translateY(15px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-link-modern,
.auth-link-alt {
    animation: linkFadeIn 0.6s ease-out both;
}

.auth-link-modern { animation-delay: 3s; }
.auth-link-alt:nth-child(1) { animation-delay: 3.2s; }
.auth-link-alt:nth-child(2) { animation-delay: 3.4s; }

/* تأثيرات التنبيهات */
@keyframes alertBounce {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.3);
    }
    50% {
        opacity: 0.8;
        transform: translateY(10px) scale(1.05);
    }
    70% {
        transform: translateY(-5px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-alert {
    animation: alertBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* تأثيرات الجسيمات */
@keyframes particleFloat {
    0% {
        opacity: 0;
        transform: translateY(100vh) translateX(0) rotate(0deg) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) translateX(10px) rotate(45deg) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateY(50vh) translateX(-20px) rotate(180deg) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) translateX(30px) rotate(315deg) scale(0.8);
    }
    100% {
        opacity: 0;
        transform: translateY(-10vh) translateX(0) rotate(360deg) scale(0);
    }
}

.floating-particle {
    animation: particleFloat linear infinite;
}

/* تأثيرات الخلفية المتحركة */
@keyframes backgroundPulse {
    0%, 100% {
        background-size: 100% 100%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-size: 110% 110%;
        filter: hue-rotate(90deg);
    }
    50% {
        background-size: 120% 120%;
        filter: hue-rotate(180deg);
    }
    75% {
        background-size: 110% 110%;
        filter: hue-rotate(270deg);
    }
}

.auth-page {
    animation: backgroundPulse 30s ease-in-out infinite;
}

/* تأثيرات التركيز المتقدمة */
@keyframes focusRipple {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

.auth-form-control:focus {
    animation: focusRipple 0.6s ease-out;
}

/* تأثيرات الهوفر للأيقونات */
@keyframes iconSpin {
    0% { transform: translateY(-50%) rotate(0deg) scale(1); }
    25% { transform: translateY(-50%) rotate(90deg) scale(1.1); }
    50% { transform: translateY(-50%) rotate(180deg) scale(1.2); }
    75% { transform: translateY(-50%) rotate(270deg) scale(1.1); }
    100% { transform: translateY(-50%) rotate(360deg) scale(1); }
}

.auth-form-icon-enhanced:hover {
    animation: iconSpin 0.8s ease-in-out;
}

/* تأثيرات النجاح */
@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

.auth-alert-success {
    animation: successPulse 2s ease-in-out;
}

/* تأثيرات الخطأ - بدون اهتزاز */
@keyframes errorFadeIn {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.auth-alert-danger {
    animation: errorFadeIn 0.4s ease-out;
}

/* تأثيرات التحميل المتقدمة */
@keyframes loadingWave {
    0% {
        transform: scale(1) rotate(0deg);
        border-radius: 20px;
    }
    25% {
        transform: scale(1.02) rotate(1deg);
        border-radius: 25px;
    }
    50% {
        transform: scale(1.05) rotate(0deg);
        border-radius: 30px;
    }
    75% {
        transform: scale(1.02) rotate(-1deg);
        border-radius: 25px;
    }
    100% {
        transform: scale(1) rotate(0deg);
        border-radius: 20px;
    }
}

.auth-loading {
    animation: loadingWave 2s ease-in-out infinite;
}

/* تحسينات الأداء */
.auth-page *,
.auth-page *::before,
.auth-page *::after {
    will-change: transform, opacity;
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .auth-page {
        animation-duration: 0.8s;
    }
    
    .auth-card-enhanced {
        animation-duration: 1s;
        animation-delay: 0.2s;
    }
    
    .auth-icon {
        animation-duration: 1.2s;
        animation-delay: 0.5s;
    }
    
    .auth-title {
        animation: none; /* إزالة تأثير الكتابة على الأجهزة المحمولة */
        opacity: 1;
        width: auto;
    }
    
    .floating-particle {
        display: none; /* إخفاء الجسيمات على الأجهزة المحمولة */
    }
}
