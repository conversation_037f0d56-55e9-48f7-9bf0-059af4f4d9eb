<?php
require_once 'config/database.php';

echo "<h1>إصلاح مشكلة عرض أسماء الأجهزة في الفواتير</h1>";

try {
    // 1. فحص المشكلة الحالية
    echo "<h2>1. فحص المشكلة الحالية</h2>";
    
    $current_data = $pdo->query("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            s.device_id,
            c.name as customer_name,
            d.device_name,
            d.device_type
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = 1
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الفواتير: " . count($current_data) . "</p>";
    
    // عرض البيانات الحالية
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Invoice</th><th>Customer Name</th><th>Device Name</th><th>Device Type</th><th>المشكلة</th></tr>";
    
    $problems = [];
    foreach ($current_data as $row) {
        $issue = '';
        if ($row['customer_name'] === $row['device_name']) {
            $issue = 'اسم العميل = اسم الجهاز';
            $problems[] = $row;
        } elseif (empty($row['device_name'])) {
            $issue = 'اسم الجهاز فارغ';
            $problems[] = $row;
        }
        
        echo "<tr>";
        echo "<td>" . $row['invoice_number'] . "</td>";
        echo "<td>" . htmlspecialchars($row['customer_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . htmlspecialchars($row['device_name'] ?? 'فارغ') . "</td>";
        echo "<td>" . htmlspecialchars($row['device_type'] ?? 'غير محدد') . "</td>";
        echo "<td style='color: " . ($issue ? 'red' : 'green') . ";'>" . ($issue ?: 'صحيح') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. إصلاح المشاكل
    if (count($problems) > 0) {
        echo "<h2>2. إصلاح المشاكل (" . count($problems) . " مشكلة)</h2>";
        
        foreach ($problems as $problem) {
            $device_id = $problem['session_id']; // استخدام session_id للحصول على device_id
            
            // جلب معلومات الجهاز
            $device_info = $pdo->prepare("
                SELECT d.device_id, d.device_name, d.device_type 
                FROM sessions s 
                JOIN devices d ON s.device_id = d.device_id 
                WHERE s.session_id = ?
            ");
            $device_info->execute([$problem['session_id']]);
            $device = $device_info->fetch(PDO::FETCH_ASSOC);
            
            if ($device) {
                // إنشاء اسم جهاز جديد
                $new_device_name = $device['device_type'] . "_" . $device['device_id'];
                
                // تحديث اسم الجهاز
                $update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
                $result = $update_stmt->execute([$new_device_name, $device['device_id']]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ تم تحديث الجهاز {$device['device_id']}: '{$device['device_name']}' → '$new_device_name'</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في تحديث الجهاز {$device['device_id']}</p>";
                }
            }
        }
    } else {
        echo "<h2>2. لا توجد مشاكل للإصلاح</h2>";
        echo "<p style='color: green;'>✅ جميع أسماء الأجهزة صحيحة</p>";
    }
    
    // 3. التحقق من النتائج بعد الإصلاح
    echo "<h2>3. النتائج بعد الإصلاح</h2>";
    
    $fixed_data = $pdo->query("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            c.name as customer_name,
            d.device_name,
            d.device_type
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = 1
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e8f5e8;'><th>Invoice</th><th>Customer Name</th><th>Device Name</th><th>Device Type</th><th>الحالة</th></tr>";
    
    $remaining_issues = 0;
    foreach ($fixed_data as $row) {
        $status = '✅ صحيح';
        $color = 'green';
        
        if ($row['customer_name'] === $row['device_name']) {
            $status = '❌ لا يزال متطابق';
            $color = 'red';
            $remaining_issues++;
        } elseif (empty($row['device_name'])) {
            $status = '❌ لا يزال فارغ';
            $color = 'red';
            $remaining_issues++;
        }
        
        echo "<tr>";
        echo "<td>" . $row['invoice_number'] . "</td>";
        echo "<td>" . htmlspecialchars($row['customer_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . htmlspecialchars($row['device_name'] ?? 'فارغ') . "</td>";
        echo "<td>" . htmlspecialchars($row['device_type'] ?? 'غير محدد') . "</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. النتيجة النهائية
    echo "<h2>4. النتيجة النهائية</h2>";
    
    if ($remaining_issues == 0) {
        echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
        echo "<h3 style='color: green;'>🎉 تم إصلاح جميع المشاكل بنجاح!</h3>";
        echo "<p>الآن جميع الفواتير تعرض أسماء الأجهزة بشكل صحيح ومختلف عن أسماء العملاء.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffeeee; padding: 20px; border-radius: 10px; text-align: center;'>";
        echo "<h3 style='color: red;'>⚠️ لا تزال هناك $remaining_issues مشكلة</h3>";
        echo "<p>قد تحتاج إلى إصلاح يدوي إضافي.</p>";
        echo "</div>";
    }
    
    // 5. اختبار سريع للعرض
    echo "<h2>5. اختبار العرض في الفواتير</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>محاكاة عرض الفاتورة:</h4>";
    
    foreach ($fixed_data as $invoice) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>فاتورة رقم:</strong> " . $invoice['invoice_number'] . "<br>";
        echo "<strong>العميل:</strong> " . htmlspecialchars($invoice['customer_name'] ?? 'غير مسجل') . "<br>";
        
        // تطبيق نفس منطق العرض من صفحة الفواتير
        $device_display = $invoice['device_name'] ?? 'غير محدد';
        if (empty(trim($device_display)) || $device_display === $invoice['customer_name']) {
            $device_display = 'جهاز غير محدد';
        }
        
        echo "<strong>الجهاز:</strong> " . htmlspecialchars($device_display) . "<br>";
        echo "<strong>نوع الجهاز:</strong> " . htmlspecialchars($invoice['device_type'] ?? 'غير محدد');
        echo "</div>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<h3>الخطوات التالية:</h3>";
echo "<p>1. تحديث صفحة الفواتير لرؤية التغييرات</p>";
echo "<p>2. التأكد من أن أسماء الأجهزة تظهر بشكل مختلف عن أسماء العملاء</p>";
echo "<br>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='test_names_display.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الأسماء</a>";
echo "</div>";
?>
