<?php
// Test database connection and basic functionality
require_once 'config/database.php';

echo "<h1>اختبار الاتصال بقاعدة البيانات</h1>";

try {
    // Test database connection
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // Test tables existence
    $tables = ['clients', 'devices', 'sessions', 'rooms', 'employees', 'customers'];
    echo "<h3>فحص الجداول:</h3>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM $table");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            echo "<li>✅ جدول $table: $count سجل</li>";
        } catch (PDOException $e) {
            echo "<li>❌ جدول $table: غير موجود أو خطأ</li>";
        }
    }
    echo "</ul>";
    
    // Test sample client data
    echo "<h3>بيانات العملاء:</h3>";
    $stmt = $pdo->prepare("SELECT client_id, business_name, owner_name, email FROM clients LIMIT 5");
    $stmt->execute();
    $clients = $stmt->fetchAll();
    
    if (count($clients) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المحل</th><th>اسم المالك</th><th>البريد الإلكتروني</th></tr>";
        foreach ($clients as $client) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($client['client_id']) . "</td>";
            echo "<td>" . htmlspecialchars($client['business_name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['owner_name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['email']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات عملاء</p>";
    }
    
    // Test sample device data
    echo "<h3>بيانات الأجهزة:</h3>";
    $stmt = $pdo->prepare("SELECT device_id, device_name, device_type, status, client_id FROM devices LIMIT 5");
    $stmt->execute();
    $devices = $stmt->fetchAll();
    
    if (count($devices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم الجهاز</th><th>النوع</th><th>الحالة</th><th>معرف العميل</th></tr>";
        foreach ($devices as $device) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($device['device_id']) . "</td>";
            echo "<td>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td>" . htmlspecialchars($device['device_type']) . "</td>";
            echo "<td>" . htmlspecialchars($device['status']) . "</td>";
            echo "<td>" . htmlspecialchars($device['client_id']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات أجهزة</p>";
    }
    
    echo "<h3>✅ جميع الاختبارات تمت بنجاح!</h3>";
    echo "<p><a href='client/login.php'>الذهاب إلى صفحة تسجيل الدخول</a></p>";
    echo "<p><a href='client/employee-login.php'>الذهاب إلى صفحة تسجيل دخول الموظفين</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
