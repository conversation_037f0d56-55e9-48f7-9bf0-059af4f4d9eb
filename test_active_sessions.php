<?php
/**
 * اختبار الجلسات النشطة في لوحة التحكم
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<h1>اختبار الجلسات النشطة - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// محاكاة تسجيل الدخول إذا لم يكن مسجل
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    echo "<p style='color: orange;'>⚠️ تم تعيين جلسة تجريبية (client_id = 1)</p>";
}

$client_id = $_SESSION['client_id'];

echo "<h2>1. فحص الجلسات في قاعدة البيانات</h2>";

try {
    // فحص جدول sessions
    $sessions_check = $pdo->query("SHOW TABLES LIKE 'sessions'");
    if ($sessions_check->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول sessions غير موجود</p>";
        echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشغيل سكريپت الإصلاح</a></p>";
    } else {
        echo "<p style='color: green;'>✅ جدول sessions موجود</p>";
        
        // عرض جميع الجلسات
        $all_sessions = $pdo->prepare("
            SELECT s.*, d.device_name, d.device_type 
            FROM sessions s 
            JOIN devices d ON s.device_id = d.device_id 
            WHERE d.client_id = ? 
            ORDER BY s.start_time DESC 
            LIMIT 10
        ");
        $all_sessions->execute([$client_id]);
        $sessions = $all_sessions->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>آخر 10 جلسات:</h3>";
        if (count($sessions) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>الجهاز</th>";
            echo "<th style='padding: 10px;'>الحالة</th>";
            echo "<th style='padding: 10px;'>وقت البدء</th>";
            echo "<th style='padding: 10px;'>وقت الانتهاء</th>";
            echo "<th style='padding: 10px;'>المدة</th>";
            echo "</tr>";
            
            foreach ($sessions as $session) {
                $status_color = '';
                switch ($session['status']) {
                    case 'active':
                        $status_color = 'background: #28a745; color: white;';
                        break;
                    case 'completed':
                        $status_color = 'background: #007bff; color: white;';
                        break;
                    case 'cancelled':
                        $status_color = 'background: #dc3545; color: white;';
                        break;
                }
                
                $duration = '';
                if ($session['end_time']) {
                    $start = new DateTime($session['start_time']);
                    $end = new DateTime($session['end_time']);
                    $diff = $start->diff($end);
                    $duration = $diff->h . 'h ' . $diff->i . 'm';
                } elseif ($session['status'] === 'active') {
                    $start = new DateTime($session['start_time']);
                    $now = new DateTime();
                    $diff = $start->diff($now);
                    $duration = $diff->h . 'h ' . $diff->i . 'm (جارية)';
                }
                
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($session['device_name']) . " (" . htmlspecialchars($session['device_type']) . ")</td>";
                echo "<td style='padding: 10px; $status_color'>" . $session['status'] . "</td>";
                echo "<td style='padding: 10px;'>" . $session['start_time'] . "</td>";
                echo "<td style='padding: 10px;'>" . ($session['end_time'] ?: 'لم تنته') . "</td>";
                echo "<td style='padding: 10px;'>$duration</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات</p>";
        }
    }
    
    echo "<h2>2. فحص الجلسات النشطة تحديداً</h2>";
    
    $active_sessions = $pdo->prepare("
        SELECT s.*, d.device_name, d.device_type, d.hourly_rate,
               TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
               (TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) * d.hourly_rate / 60) as current_cost
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
        AND s.status = 'active'
        ORDER BY s.start_time DESC
    ");
    $active_sessions->execute([$client_id]);
    $active = $active_sessions->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>عدد الجلسات النشطة:</strong> " . count($active) . "</p>";
    
    if (count($active) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #28a745; color: white;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>الجهاز</th>";
        echo "<th style='padding: 10px;'>وقت البدء</th>";
        echo "<th style='padding: 10px;'>المدة (دقيقة)</th>";
        echo "<th style='padding: 10px;'>التكلفة الحالية</th>";
        echo "</tr>";
        
        foreach ($active as $session) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $session['session_id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($session['device_name']) . "</td>";
            echo "<td style='padding: 10px;'>" . $session['start_time'] . "</td>";
            echo "<td style='padding: 10px;'>" . $session['duration_minutes'] . "</td>";
            echo "<td style='padding: 10px;'>" . number_format($session['current_cost'], 2) . " ج.م</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة حالياً</p>";
        
        echo "<h3>إنشاء جلسة تجريبية نشطة:</h3>";
        
        if (isset($_GET['create_test_session'])) {
            // البحث عن جهاز متاح
            $available_device = $pdo->prepare("SELECT * FROM devices WHERE client_id = ? AND status = 'available' LIMIT 1");
            $available_device->execute([$client_id]);
            $device = $available_device->fetch(PDO::FETCH_ASSOC);
            
            if ($device) {
                // إنشاء جلسة تجريبية
                $insert_session = $pdo->prepare("
                    INSERT INTO sessions (device_id, start_time, status, hourly_rate) 
                    VALUES (?, NOW(), 'active', ?)
                ");
                $insert_session->execute([$device['device_id'], $device['hourly_rate']]);
                
                // تحديث حالة الجهاز
                $update_device = $pdo->prepare("UPDATE devices SET status = 'occupied' WHERE device_id = ?");
                $update_device->execute([$device['device_id']]);
                
                echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية نشطة على جهاز: " . htmlspecialchars($device['device_name']) . "</p>";
                echo "<p><a href='?' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث الصفحة</a></p>";
            } else {
                echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة لإنشاء جلسة تجريبية</p>";
            }
        } else {
            echo "<p><a href='?create_test_session=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء جلسة تجريبية نشطة</a></p>";
        }
    }
    
    echo "<h2>3. اختبار API إحصائيات لوحة التحكم</h2>";
    
    if (isset($_GET['test_api'])) {
        $api_url = 'http://localhost/playgood/client/api/get_dashboard_stats.php';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Cookie: ' . session_name() . '=' . session_id()
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>HTTP Code:</strong> $http_code</p>";
        echo "<p><strong>API Response:</strong></p>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                echo "<p style='color: green;'>✅ API يعمل بنجاح!</p>";
                echo "<p><strong>الجلسات النشطة من API:</strong> " . ($data['active_sessions'] ?? 0) . "</p>";
            } else {
                echo "<p style='color: red;'>❌ API فشل أو أرجع خطأ</p>";
            }
        }
    } else {
        echo "<p><a href='?test_api=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار API</a></p>";
    }
    
    echo "<h2>4. فحص الأجهزة</h2>";
    
    $devices_stats = $pdo->prepare("
        SELECT status, COUNT(*) as count 
        FROM devices 
        WHERE client_id = ? 
        GROUP BY status
    ");
    $devices_stats->execute([$client_id]);
    $devices_by_status = $devices_stats->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>حالة الجهاز</th>";
    echo "<th style='padding: 10px;'>العدد</th>";
    echo "</tr>";
    
    foreach ($devices_by_status as $status) {
        $status_color = '';
        switch ($status['status']) {
            case 'available':
                $status_color = 'background: #28a745; color: white;';
                break;
            case 'occupied':
                $status_color = 'background: #ffc107; color: black;';
                break;
            case 'maintenance':
                $status_color = 'background: #dc3545; color: white;';
                break;
        }
        
        echo "<tr>";
        echo "<td style='padding: 10px; $status_color'>" . $status['status'] . "</td>";
        echo "<td style='padding: 10px;'>" . $status['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>5. روابط مفيدة</h2>";
echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";
echo "<p><a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a></p>";
echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>سكريپت الإصلاح الشامل</a></p>";

echo "</div>";
?>
