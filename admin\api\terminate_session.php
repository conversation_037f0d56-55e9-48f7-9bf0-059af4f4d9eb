<?php
/**
 * API لإنهاء الجلسات الآمنة
 * PlayGood Gaming Center Management System
 */

require_once '../../config/database.php';
require_once '../../includes/auth_guard.php';

// حماية API endpoint
protectApiEndpoint($pdo, 'admin');

header('Content-Type: application/json');

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['session_id'])) {
        throw new Exception('معرف الجلسة مطلوب');
    }
    
    $session_id = $input['session_id'];
    
    // التحقق من وجود الجلسة
    $stmt = $pdo->prepare("
        SELECT * FROM secure_sessions 
        WHERE session_id = ? AND is_valid = TRUE
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('الجلسة غير موجودة أو منتهية الصلاحية');
    }
    
    // إنهاء الجلسة
    $stmt = $pdo->prepare("
        UPDATE secure_sessions 
        SET is_valid = FALSE 
        WHERE session_id = ?
    ");
    $stmt->execute([$session_id]);
    
    // تسجيل الحدث الأمني
    $stmt = $pdo->prepare("
        INSERT INTO session_security_logs 
        (session_id, ip_address, user_agent, attack_type, details)
        VALUES (?, ?, ?, 'admin_termination', ?)
    ");
    $stmt->execute([
        $session_id,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        'تم إنهاء الجلسة بواسطة المدير: ' . $_SESSION['admin_name']
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إنهاء الجلسة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
