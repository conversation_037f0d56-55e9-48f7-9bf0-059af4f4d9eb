<?php
/**
 * إصلاح بسيط لمشكلة حذف التصنيفات
 * يعمل مع جميع إصدارات MySQL
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح بسيط - حذف التصنيفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-success text-white">
            <h1><i class="fas fa-tools me-2"></i>إصلاح بسيط - مشكلة حذف التصنيفات</h1>
        </div>
        <div class="card-body">

<?php
$fixes_applied = 0;
$errors = [];

try {
    echo "<h2><i class='fas fa-database me-2'></i>1. فحص الجداول</h2>";
    
    // فحص جدول categories
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>جدول categories موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول categories غير موجود!</p>";
        $errors[] = "جدول categories غير موجود";
    }
    
    // فحص جدول cafeteria_items
    $stmt = $pdo->query("SHOW TABLES LIKE 'cafeteria_items'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>جدول cafeteria_items موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول cafeteria_items غير موجود!</p>";
        $errors[] = "جدول cafeteria_items غير موجود";
    }
    
    echo "<h2><i class='fas fa-link me-2'></i>2. فحص القيود المرجعية</h2>";
    
    // فحص القيود بطريقة بسيطة
    try {
        $constraints = $pdo->query("
            SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'cafeteria_items'
            AND REFERENCED_TABLE_NAME = 'categories'
        ")->fetchAll();
        
        if (count($constraints) > 0) {
            echo "<div class='alert alert-warning'>";
            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تم العثور على قيود مرجعية:</h5>";
            foreach ($constraints as $constraint) {
                echo "<p><strong>القيد:</strong> {$constraint['CONSTRAINT_NAME']}<br>";
                echo "<strong>العمود:</strong> {$constraint['COLUMN_NAME']} → {$constraint['REFERENCED_COLUMN_NAME']}</p>";
            }
            echo "<p class='warning'>هذه القيود قد تمنع حذف التصنيفات</p>";
            echo "</div>";
            
            // محاولة إزالة القيود
            echo "<h3>إزالة القيود المرجعية:</h3>";
            foreach ($constraints as $constraint) {
                try {
                    $pdo->exec("ALTER TABLE cafeteria_items DROP FOREIGN KEY {$constraint['CONSTRAINT_NAME']}");
                    echo "<p class='success'><i class='fas fa-check me-2'></i>تم إزالة القيد: {$constraint['CONSTRAINT_NAME']}</p>";
                    $fixes_applied++;
                } catch (PDOException $e) {
                    echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في إزالة القيد {$constraint['CONSTRAINT_NAME']}: " . $e->getMessage() . "</p>";
                    $errors[] = $e->getMessage();
                }
            }
        } else {
            echo "<p class='success'><i class='fas fa-check me-2'></i>لا توجد قيود مرجعية مشكلة</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يمكن فحص القيود المرجعية: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2><i class='fas fa-table me-2'></i>3. فحص البيانات</h2>";
    
    // عد التصنيفات
    $categories_count = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
    echo "<p class='info'><i class='fas fa-info me-2'></i>عدد التصنيفات: $categories_count</p>";
    
    // عد المنتجات
    $items_count = $pdo->query("SELECT COUNT(*) FROM cafeteria_items")->fetchColumn();
    echo "<p class='info'><i class='fas fa-info me-2'></i>عدد المنتجات: $items_count</p>";
    
    // فحص التصنيفات التي يمكن حذفها
    if ($categories_count > 0) {
        echo "<h5>التصنيفات وحالة الحذف:</h5>";
        $categories = $pdo->query("SELECT category_id, name, client_id FROM categories ORDER BY name")->fetchAll();
        
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead class='table-dark'><tr><th>المعرف</th><th>الاسم</th><th>عدد المنتجات</th><th>يمكن حذفه؟</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($categories as $category) {
            // عد المنتجات المرتبطة
            $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM cafeteria_items WHERE category = ? AND client_id = ?");
            $count_stmt->execute([$category['name'], $category['client_id']]);
            $product_count = $count_stmt->fetchColumn();
            
            echo "<tr>";
            echo "<td>{$category['category_id']}</td>";
            echo "<td>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td>$product_count</td>";
            
            if ($product_count > 0) {
                echo "<td class='error'><i class='fas fa-times me-1'></i>لا (يحتوي على منتجات)</td>";
            } else {
                echo "<td class='success'><i class='fas fa-check me-1'></i>نعم</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
    }
    
    echo "<h2><i class='fas fa-test-tube me-2'></i>4. اختبار الحذف</h2>";
    
    // إنشاء تصنيف تجريبي
    $test_name = 'تصنيف تجريبي ' . date('H:i:s');
    $stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
    $stmt->execute([$test_name]);
    $test_id = $pdo->lastInsertId();
    
    echo "<p class='info'><i class='fas fa-plus me-2'></i>تم إنشاء تصنيف تجريبي: '$test_name' (معرف: $test_id)</p>";
    
    // محاولة حذف التصنيف
    try {
        $delete_stmt = $pdo->prepare("DELETE FROM categories WHERE category_id = ? AND client_id = 1");
        $delete_stmt->execute([$test_id]);
        
        if ($delete_stmt->rowCount() > 0) {
            echo "<div class='alert alert-success'>";
            echo "<h4><i class='fas fa-check-circle me-2'></i>نجح الاختبار!</h4>";
            echo "<p>تم حذف التصنيف التجريبي بنجاح. مشكلة حذف التصنيفات محلولة!</p>";
            echo "</div>";
            $fixes_applied++;
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل الحذف - لم يتم حذف أي صف</p>";
            $errors[] = "فشل في حذف التصنيف التجريبي";
        }
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>فشل الاختبار</h5>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        
        if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
            echo "<p class='error'>السبب: لا تزال هناك قيود مرجعية تمنع الحذف</p>";
        }
        echo "</div>";
        $errors[] = $e->getMessage();
    }
    
    echo "<h2><i class='fas fa-clipboard-check me-2'></i>5. النتائج والتوصيات</h2>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card border-info'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>ملخص النتائج</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>الإصلاحات المطبقة:</strong> $fixes_applied</p>";
    echo "<p><strong>الأخطاء:</strong> " . count($errors) . "</p>";
    
    if ($fixes_applied > 0 && count($errors) == 0) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>تم الإصلاح بنجاح!";
        echo "</div>";
    } elseif (count($errors) > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>تم الإصلاح جزئياً";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card border-primary'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5><i class='fas fa-lightbulb me-2'></i>التوصيات</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    if (count($errors) == 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>النظام يعمل بشكل صحيح</p>";
        echo "<p>يمكنك الآن حذف التصنيفات في صفحة الكافتيريا</p>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>قد تحتاج لمراجعة يدوية</p>";
        echo "<p>راجع الأخطاء أدناه واتصل بالدعم الفني</p>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    if (count($errors) > 0) {
        echo "<div class='mt-3'>";
        echo "<h5 class='error'>تفاصيل الأخطاء:</h5>";
        echo "<ul class='list-group'>";
        foreach ($errors as $error) {
            echo "<li class='list-group-item list-group-item-danger'>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='mt-4'>";
    echo "<h3><i class='fas fa-arrow-right me-2'></i>الخطوات التالية</h3>";
    echo "<div class='d-grid gap-2 d-md-flex justify-content-md-start'>";
    echo "<a href='client/cafeteria.php' class='btn btn-primary'><i class='fas fa-coffee me-2'></i>اختبار صفحة الكافتيريا</a>";
    echo "<a href='client/dashboard.php' class='btn btn-success'><i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ في الإصلاح</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
