<?php
session_start();
require_once 'includes/trial-auth.php';

// التحقق من تسجيل الدخول
requireTrialLogin();

$trial_id = $_SESSION['trial_id'];
$business_name = $_SESSION['trial_business_name'];

// الحصول على الوقت المتبقي
$timeRemaining = getTrialTimeRemaining($trial_id);

// الحصول على قائمة منتجات الكافتيريا
try {
    $stmt = $pdo->prepare("
        SELECT * FROM trial_cafeteria_items 
        WHERE trial_id = ? 
        ORDER BY category, name
    ");
    $stmt->execute([$trial_id]);
    $cafeteria_items = $stmt->fetchAll();
} catch (PDOException $e) {
    $cafeteria_items = [];
    $error = 'حدث خطأ في جلب بيانات منتجات الكافتيريا';
}

// تجميع المنتجات حسب الفئة
$categories = [];
foreach ($cafeteria_items as $item) {
    $categories[$item['category']][] = $item;
}

// معالجة إضافة منتج جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_item'])) {
    $name = trim($_POST['name'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $category = trim($_POST['category'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (!empty($name) && $price > 0 && !empty($category)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO trial_cafeteria_items (trial_id, name, price, category, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            if ($stmt->execute([$trial_id, $name, $price, $category, $description])) {
                $success = 'تم إضافة المنتج بنجاح!';
                header('Location: trial-cafeteria.php?success=1');
                exit;
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة المنتج';
        }
    } else {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    }
}

if (isset($_GET['success'])) {
    if ($_GET['success'] == '1') {
        $success = 'تم إضافة المنتج بنجاح!';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكافتيريا - التجربة المجانية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            --danger-gradient: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .trial-navbar {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .trial-timer {
            background: var(--warning-gradient);
            color: #2d3748;
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .trial-timer.danger {
            background: var(--danger-gradient);
            color: white;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .trial-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 1rem;
        }

        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .page-header h2 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }

        .category-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .category-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }

        .product-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .product-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #48bb78;
            margin-bottom: 0.5rem;
        }

        .product-description {
            color: #718096;
            font-size: 0.9rem;
        }

        .add-product-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-left: 1rem;
        }

        .icon-hot { background: linear-gradient(135deg, #ed8936, #dd6b20); }
        .icon-cold { background: linear-gradient(135deg, #4299e1, #3182ce); }
        .icon-food { background: linear-gradient(135deg, #48bb78, #38a169); }
        .icon-snacks { background: linear-gradient(135deg, #9f7aea, #805ad5); }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .category-section {
                padding: 1rem;
            }
            
            .add-product-card {
                padding: 1.5rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الوقت المتبقي -->
    <div class="trial-timer <?php echo $timeRemaining['total_seconds'] < 1800 ? 'danger' : ''; ?>" id="trialTimer">
        <i class="fas fa-clock me-2"></i>
        الوقت المتبقي للتجربة: <span id="timeDisplay"><?php echo $timeRemaining['hours']; ?>:<?php echo sprintf('%02d', $timeRemaining['minutes']); ?>:<?php echo sprintf('%02d', $timeRemaining['seconds']); ?></span>
    </div>

    <!-- شريط التنقل -->
    <nav class="trial-navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <span class="navbar-brand">
                        <i class="fas fa-gamepad me-2"></i>
                        PlayGood
                    </span>
                    <span class="trial-badge">تجربة مجانية</span>
                </div>
                <div class="d-flex align-items-center">
                    <a href="trial-dashboard.php" class="back-btn me-3">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="trial-logout.php" class="back-btn">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="trial-dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">إدارة الكافتيريا</li>
                </ol>
            </nav>
            <h2>
                <i class="fas fa-coffee me-2 text-info"></i>
                إدارة الكافتيريا
            </h2>
            <p class="text-muted mb-0">إدارة منتجات الكافتيريا في <?php echo htmlspecialchars($business_name); ?></p>
        </div>

        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إضافة منتج جديد -->
        <div class="add-product-card">
            <h4 class="mb-3">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </h4>
            <form method="POST">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="name" class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="price" class="form-label">السعر (جنيه)</label>
                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-control" id="category" name="category" required>
                            <option value="">اختر الفئة</option>
                            <option value="مشروبات ساخنة">مشروبات ساخنة</option>
                            <option value="مشروبات باردة">مشروبات باردة</option>
                            <option value="مأكولات">مأكولات</option>
                            <option value="تسالي">تسالي</option>
                            <option value="حلويات">حلويات</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <input type="text" class="form-control" id="description" name="description" placeholder="اختياري">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" name="add_item" class="btn btn-light w-100">
                            <i class="fas fa-plus me-1"></i>
                            إضافة
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- قائمة المنتجات حسب الفئة -->
        <?php if (empty($categories)): ?>
            <div class="text-center py-5">
                <i class="fas fa-coffee fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات مضافة بعد</h5>
                <p class="text-muted">ابدأ بإضافة أول منتج باستخدام النموذج أعلاه</p>
            </div>
        <?php else: ?>
            <?php foreach ($categories as $category_name => $items): ?>
                <div class="category-section">
                    <h3 class="category-title">
                        <?php
                        $icon_class = 'icon-food';
                        $icon = 'fas fa-utensils';
                        
                        if (strpos($category_name, 'ساخنة') !== false) {
                            $icon_class = 'icon-hot';
                            $icon = 'fas fa-fire';
                        } elseif (strpos($category_name, 'باردة') !== false) {
                            $icon_class = 'icon-cold';
                            $icon = 'fas fa-snowflake';
                        } elseif (strpos($category_name, 'تسالي') !== false || strpos($category_name, 'حلويات') !== false) {
                            $icon_class = 'icon-snacks';
                            $icon = 'fas fa-cookie-bite';
                        }
                        ?>
                        <span class="category-icon <?php echo $icon_class; ?>">
                            <i class="<?php echo $icon; ?>"></i>
                        </span>
                        <?php echo htmlspecialchars($category_name); ?>
                        <small class="text-muted">(<?php echo count($items); ?> منتج)</small>
                    </h3>
                    
                    <div class="products-grid">
                        <?php foreach ($items as $item): ?>
                            <div class="product-card">
                                <div class="product-name"><?php echo htmlspecialchars($item['name']); ?></div>
                                <div class="product-price">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    <?php echo number_format($item['price'], 2); ?> جنيه
                                </div>
                                <?php if (!empty($item['description'])): ?>
                                    <div class="product-description"><?php echo htmlspecialchars($item['description']); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عداد الوقت المتبقي
        let totalSeconds = <?php echo $timeRemaining['total_seconds']; ?>;
        
        function updateTimer() {
            if (totalSeconds <= 0) {
                alert('انتهت صلاحية التجربة المجانية!');
                window.location.href = 'trial-login.php?error=trial_expired';
                return;
            }
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            document.getElementById('timeDisplay').textContent = 
                hours + ':' + 
                (minutes < 10 ? '0' : '') + minutes + ':' + 
                (seconds < 10 ? '0' : '') + seconds;
            
            const timerElement = document.getElementById('trialTimer');
            if (totalSeconds < 1800) {
                timerElement.classList.add('danger');
            }
            
            totalSeconds--;
        }
        
        setInterval(updateTimer, 1000);
    </script>
</body>
</html>
