/* تعطيل تأثيرات الوقوف Hover لجميع العناصر */
.auth-page *:hover {
    transform: none !important;
    animation: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

/* تعطيل الحركات عند تركيز الحقول */
.auth-form-floating .auth-form-control:focus,
.auth-form-floating .auth-form-control:focus + .auth-form-icon-enhanced {
    transform: none !important;
    box-shadow: none !important;
}

/* تعطيل تحركات الروابط */
.auth-link-modern:hover,
.auth-link-alt:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* تعطيل أيقونات الروابط */
.auth-link-alt:hover i {
    transform: none !important;
}

/* تعطيل حركة الأزرار */
.auth-btn-modern:hover,
.auth-btn-modern:active {
    transform: none !important;
    background-position: initial !important;
    animation: none !important;
}

/* تعطيل تأثير الإضاءة للأزرار */
.auth-btn-modern:hover::before {
    left: -100% !important;
}

/* تعطيل حركة الخلفية إذا لزم الأمر */
.auth-page::before {
    animation: none !important;
}

/* تعطيل أي تأثير حركة آخر */
* {
    transition: none !important;
}
