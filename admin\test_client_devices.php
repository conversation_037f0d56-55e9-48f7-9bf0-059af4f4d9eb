<?php
/**
 * اختبار صفحة إدارة أجهزة العملاء
 * PlayGood - نظام إدارة محلات البلايستيشن
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار إدارة أجهزة العملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";
echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h5 class='mb-0'><i class='fas fa-desktop me-2'></i>اختبار إدارة أجهزة العملاء</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // 1. فحص الاتصال بقاعدة البيانات
    echo "<h6>1. فحص الاتصال بقاعدة البيانات</h6>";
    $stmt = $pdo->query("SELECT 1");
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>الاتصال بقاعدة البيانات نجح</div>";
    
    // 2. فحص جدول العملاء
    echo "<h6>2. فحص جدول العملاء</h6>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM clients");
    $clients_count = $stmt->fetch()['count'];
    echo "<div class='alert alert-info'><i class='fas fa-users me-2'></i>عدد العملاء: {$clients_count}</div>";
    
    // 3. فحص جدول الأجهزة
    echo "<h6>3. فحص جدول الأجهزة</h6>";
    $stmt = $pdo->query("DESCRIBE devices");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // 4. فحص الأجهزة الموجودة
    echo "<h6>4. فحص الأجهزة الموجودة</h6>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM devices");
    $devices_count = $stmt->fetch()['count'];
    echo "<div class='alert alert-info'><i class='fas fa-desktop me-2'></i>عدد الأجهزة: {$devices_count}</div>";
    
    if ($devices_count > 0) {
        $stmt = $pdo->query("SELECT d.*, c.business_name FROM devices d LEFT JOIN clients c ON d.client_id = c.client_id LIMIT 5");
        $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>اسم الجهاز</th><th>النوع</th><th>العميل</th><th>السعر العادي</th><th>السعر الفردي</th><th>السعر الجماعي</th><th>الحالة</th></tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($devices as $device) {
            echo "<tr>";
            echo "<td>{$device['device_id']}</td>";
            echo "<td>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td>{$device['device_type']}</td>";
            echo "<td>" . htmlspecialchars($device['business_name'] ?? 'غير محدد') . "</td>";
            echo "<td>{$device['hourly_rate']}</td>";
            echo "<td>" . ($device['single_rate'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($device['multi_rate'] ?? 'غير محدد') . "</td>";
            echo "<td>{$device['status']}</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
    // 5. اختبار الروابط
    echo "<h6>5. اختبار الروابط</h6>";
    
    if ($clients_count > 0) {
        $stmt = $pdo->query("SELECT client_id, business_name FROM clients LIMIT 3");
        $test_clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='row'>";
        foreach ($test_clients as $client) {
            echo "<div class='col-md-4 mb-3'>";
            echo "<div class='card'>";
            echo "<div class='card-body text-center'>";
            echo "<h6 class='card-title'>" . htmlspecialchars($client['business_name']) . "</h6>";
            echo "<a href='client_devices.php?client_id={$client['client_id']}' class='btn btn-primary btn-sm'>";
            echo "<i class='fas fa-desktop me-1'></i>إدارة الأجهزة";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "لا توجد عملاء في النظام. يرجى إضافة عملاء أولاً.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-circle me-2'></i>";
    echo "خطأ: " . $e->getMessage();
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='clients.php' class='btn btn-success me-2'><i class='fas fa-users me-1'></i>إدارة العملاء</a>";
echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-dashboard me-1'></i>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
