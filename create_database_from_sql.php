<?php
/**
 * سكريپت إنشاء قاعدة البيانات من ملف station.sql
 * يقوم بإنشاء قاعدة البيانات والجداول من الصفر
 */

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-database me-2'></i>
                        إنشاء قاعدة البيانات من ملف SQL
                    </h3>
                </div>
                <div class='card-body'>";

// التحقق من وجود ملف SQL
$sql_file = 'station.sql';
if (!file_exists($sql_file)) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>ملف قاعدة البيانات غير موجود!</h5>
            <p>لم يتم العثور على ملف <code>station.sql</code> في المجلد الحالي.</p>
            <p>يرجى التأكد من وجود الملف في مجلد playgood وإعادة المحاولة.</p>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>العودة للوحة التحكم
            </a>
          </div>";
} else {
    try {
        // قراءة إعدادات قاعدة البيانات
        $config_file = 'config/database.php';
        if (!file_exists($config_file)) {
            throw new Exception('ملف إعدادات قاعدة البيانات غير موجود: ' . $config_file);
        }
        
        // استخراج إعدادات قاعدة البيانات
        $config_content = file_get_contents($config_file);
        preg_match('/host=([^;]+)/', $config_content, $host_match);
        preg_match('/dbname=([^;]+)/', $config_content, $dbname_match);
        preg_match('/\$username\s*=\s*[\'"]([^\'"]+)[\'"]/', $config_content, $username_match);
        preg_match('/\$password\s*=\s*[\'"]([^\'"]*)[\'"]/', $config_content, $password_match);
        
        $host = $host_match[1] ?? 'localhost';
        $dbname = $dbname_match[1] ?? 'station';
        $username = $username_match[1] ?? 'root';
        $password = $password_match[1] ?? '';
        
        echo "<div class='alert alert-info'>
                <h6><i class='fas fa-info-circle me-2'></i>إعدادات قاعدة البيانات:</h6>
                <ul class='mb-0'>
                    <li>الخادم: $host</li>
                    <li>قاعدة البيانات: $dbname</li>
                    <li>المستخدم: $username</li>
                </ul>
              </div>";
        
        // الاتصال بالخادم بدون تحديد قاعدة بيانات
        $pdo_server = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم الاتصال بخادم قاعدة البيانات بنجاح</p>";
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo_server->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p class='text-success'><i class='fas fa-plus me-2'></i>تم إنشاء قاعدة البيانات '$dbname' (أو كانت موجودة مسبقاً)</p>";
        
        // الاتصال بقاعدة البيانات المحددة
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p class='text-success'><i class='fas fa-link me-2'></i>تم الاتصال بقاعدة البيانات '$dbname' بنجاح</p>";
        
        // قراءة ملف SQL
        $sql_content = file_get_contents($sql_file);
        if ($sql_content === false) {
            throw new Exception('فشل في قراءة ملف SQL');
        }
        
        echo "<p class='text-info'><i class='fas fa-file-alt me-2'></i>تم قراءة ملف SQL بنجاح (" . number_format(strlen($sql_content)) . " حرف)</p>";
        
        // إزالة التعليقات والأسطر الفارغة
        $sql_content = preg_replace('/^--.*$/m', '', $sql_content);
        $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        $queries = array_filter($queries, function($query) {
            $query = trim($query);
            return !empty($query) && 
                   !preg_match('/^(SET|START|COMMIT)/i', $query) &&
                   !preg_match('/^\/\*.*\*\//s', $query);
        });
        
        echo "<p class='text-info'><i class='fas fa-list me-2'></i>تم العثور على " . count($queries) . " استعلام صالح</p>";
        
        // تعطيل فحص المفاتيح الخارجية
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        $pdo->exec("SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO'");
        
        echo "<div class='alert alert-warning'>
                <i class='fas fa-cogs me-2'></i>جاري تنفيذ الاستعلامات...
              </div>";
        
        echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background: #f8f9fa; border-radius: 5px;'>";
        
        $executed = 0;
        $errors = 0;
        $tables_created = 0;
        $data_inserted = 0;
        
        foreach ($queries as $index => $query) {
            $query = trim($query);
            if (empty($query)) continue;
            
            try {
                $pdo->exec($query);
                $executed++;
                
                // تحديد نوع الاستعلام
                if (preg_match('/^CREATE TABLE/i', $query)) {
                    $tables_created++;
                    preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $query, $matches);
                    $table_name = $matches[1] ?? 'غير معروف';
                    echo "<small class='text-success'><i class='fas fa-table me-1'></i>تم إنشاء جدول: <strong>$table_name</strong></small><br>";
                } elseif (preg_match('/^INSERT INTO/i', $query)) {
                    $data_inserted++;
                    if ($data_inserted % 5 == 0) {
                        echo "<small class='text-info'><i class='fas fa-database me-1'></i>تم إدراج $data_inserted مجموعة بيانات...</small><br>";
                    }
                } elseif (preg_match('/^ALTER TABLE/i', $query)) {
                    preg_match('/ALTER TABLE\s+`?(\w+)`?/i', $query, $matches);
                    $table_name = $matches[1] ?? 'غير معروف';
                    echo "<small class='text-warning'><i class='fas fa-edit me-1'></i>تم تعديل جدول: <strong>$table_name</strong></small><br>";
                }
                
                // عرض تقدم العملية
                if ($executed % 20 == 0) {
                    echo "<small class='text-primary'><i class='fas fa-spinner me-1'></i>تم تنفيذ $executed استعلام...</small><br>";
                }
                
            } catch (PDOException $e) {
                $errors++;
                $error_msg = $e->getMessage();
                
                // تجاهل بعض الأخطاء غير المهمة
                if (strpos($error_msg, 'Duplicate entry') !== false || 
                    strpos($error_msg, 'already exists') !== false) {
                    echo "<small class='text-muted'><i class='fas fa-info-circle me-1'></i>تم تجاهل: " . substr($error_msg, 0, 50) . "...</small><br>";
                } else {
                    echo "<small class='text-danger'><i class='fas fa-exclamation-triangle me-1'></i>خطأ في الاستعلام " . ($index + 1) . ": " . htmlspecialchars(substr($error_msg, 0, 100)) . "...</small><br>";
                }
                
                // زيادة حد الأخطاء المسموح به إلى 50
                if ($errors > 50) {
                    echo "<small class='text-danger'><strong><i class='fas fa-stop me-1'></i>تم إيقاف العملية بسبب كثرة الأخطاء (أكثر من 50 خطأ)</strong></small><br>";
                    break;
                }
            }
        }
        
        echo "</div>";
        
        // إعادة تفعيل فحص المفاتيح الخارجية
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        // التحقق من الجداول المنشأة
        $tables_result = $pdo->query("SHOW TABLES");
        $created_tables = $tables_result->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<div class='alert alert-success mt-4'>
                <h4><i class='fas fa-check-circle me-2'></i>تم إنشاء قاعدة البيانات بنجاح! 🎉</h4>
                <div class='row'>
                    <div class='col-md-6'>
                        <h6>إحصائيات العملية:</h6>
                        <ul class='list-group'>
                            <li class='list-group-item d-flex justify-content-between'>
                                <span>الاستعلامات المنفذة</span>
                                <span class='badge bg-primary'>$executed</span>
                            </li>
                            <li class='list-group-item d-flex justify-content-between'>
                                <span>الجداول المنشأة</span>
                                <span class='badge bg-success'>$tables_created</span>
                            </li>
                            <li class='list-group-item d-flex justify-content-between'>
                                <span>البيانات المدرجة</span>
                                <span class='badge bg-info'>$data_inserted</span>
                            </li>
                            <li class='list-group-item d-flex justify-content-between'>
                                <span>الأخطاء</span>
                                <span class='badge bg-warning'>$errors</span>
                            </li>
                        </ul>
                    </div>
                    <div class='col-md-6'>
                        <h6>الجداول المنشأة (" . count($created_tables) . "):</h6>
                        <div style='max-height: 200px; overflow-y: auto;'>
                            <ul class='list-group list-group-flush'>";
        
        foreach ($created_tables as $table) {
            echo "<li class='list-group-item py-1'><i class='fas fa-table me-2 text-success'></i>$table</li>";
        }
        
        echo "                </ul>
                        </div>
                    </div>
                </div>
              </div>";
        
        // تشغيل إصلاحات تلقائية
        echo "<div class='alert alert-info'>
                <h6><i class='fas fa-tools me-2'></i>تطبيق إصلاحات تلقائية...</h6>
              </div>";
        
        // إصلاح أسماء الأجهزة
        $device_fixes = [
            ['old' => 'جهاز 1', 'new' => 'PlayStation 5 - Station 1'],
            ['old' => 'جهاز 2', 'new' => 'PlayStation 4 - Station 2'],
            ['old' => 'جهاز 3', 'new' => 'Xbox Series - Station 3'],
            ['old' => 'جهاز 4', 'new' => 'Gaming PC - Station 4'],
            ['old' => 'جهاز 5', 'new' => 'PlayStation 4 - Station 5'],
            ['old' => 'ps 1', 'new' => 'PlayStation 4 - Station 1']
        ];
        
        $fix_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_name = ?");
        $fixed_count = 0;
        
        foreach ($device_fixes as $fix) {
            try {
                $fix_stmt->execute([$fix['new'], $fix['old']]);
                if ($fix_stmt->rowCount() > 0) {
                    echo "<small class='text-success'><i class='fas fa-check me-1'></i>تم إصلاح: {$fix['old']} → {$fix['new']}</small><br>";
                    $fixed_count++;
                }
            } catch (PDOException $e) {
                // تجاهل الأخطاء في الإصلاحات
            }
        }
        
        if ($fixed_count > 0) {
            echo "<p class='text-success'><i class='fas fa-wrench me-2'></i>تم إصلاح $fixed_count اسم جهاز</p>";
        }
        
        echo "<div class='text-center mt-4'>
                <a href='reset_and_fix_database.php' class='btn btn-warning me-2'>
                    <i class='fas fa-tools me-1'></i>تشغيل إصلاحات شاملة
                </a>
                <a href='client/dashboard.php' class='btn btn-primary me-2'>
                    <i class='fas fa-home me-1'></i>لوحة التحكم
                </a>
                <a href='client/sessions.php' class='btn btn-success me-2'>
                    <i class='fas fa-play-circle me-1'></i>اختبار الجلسات
                </a>
                <a href='client/reports.php' class='btn btn-info'>
                    <i class='fas fa-chart-bar me-1'></i>اختبار التقارير
                </a>
              </div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
                <h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في إنشاء قاعدة البيانات!</h5>
                <p><strong>تفاصيل الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
                <h6>الحلول المقترحة:</h6>
                <ul>
                    <li>تأكد من صحة إعدادات قاعدة البيانات في ملف <code>config/database.php</code></li>
                    <li>تأكد من تشغيل خادم MySQL/MariaDB</li>
                    <li>تأكد من صلاحيات المستخدم لإنشاء قواعد البيانات</li>
                    <li>تأكد من وجود ملف <code>station.sql</code> في المجلد الصحيح</li>
                </ul>
              </div>";
        
        echo "<div class='text-center mt-4'>
                <a href='client/dashboard.php' class='btn btn-secondary'>
                    <i class='fas fa-home me-1'></i>العودة للوحة التحكم
                </a>
              </div>";
    }
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
