<?php
// إصلاح البيانات الموجودة في جدول session_products
session_start();
require_once 'config/database.php';

echo "<h1>إصلاح البيانات الموجودة في جدول session_products</h1>";

try {
    // فحص البيانات الحالية
    $stmt = $pdo->query("
        SELECT 
            sp.*,
            ci.name as product_name,
            ci.price as current_product_price
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        ORDER BY sp.id DESC
        LIMIT 10
    ");
    $existing_products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>البيانات الحالية (آخر 10 سجلات):</h2>";
    
    if (count($existing_products) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المنتج</th><th>الكمية</th><th>unit_price</th><th>price</th><th>total_price</th><th>total</th><th>السعر الحالي</th><th>الحالة</th></tr>";
        
        $problems_found = 0;
        $fixed_count = 0;
        
        foreach ($existing_products as $product) {
            $unit_price = $product['unit_price'] ?? 0;
            $price = $product['price'] ?? 0;
            $total_price = $product['total_price'] ?? 0;
            $total = $product['total'] ?? 0;
            $quantity = $product['quantity'] ?? 1;
            $current_price = $product['current_product_price'] ?? 0;
            
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>" . htmlspecialchars($product['product_name'] ?? 'غير معروف') . "</td>";
            echo "<td>$quantity</td>";
            echo "<td>$unit_price</td>";
            echo "<td>$price</td>";
            echo "<td>$total_price</td>";
            echo "<td>$total</td>";
            echo "<td>$current_price</td>";
            
            // تحديد المشاكل
            $has_problem = false;
            $status = [];
            
            if ($unit_price == 0 && $price == 0) {
                $has_problem = true;
                $status[] = "لا يوجد سعر";
                $problems_found++;
            }
            
            if ($total_price == 0 && $total == 0) {
                $has_problem = true;
                $status[] = "لا يوجد إجمالي";
            }
            
            if ($has_problem) {
                echo "<td style='color: red;'>" . implode(', ', $status) . "</td>";
                
                // محاولة الإصلاح
                $fix_price = $unit_price > 0 ? $unit_price : ($price > 0 ? $price : $current_price);
                $fix_total = $fix_price * $quantity;
                
                if ($fix_price > 0) {
                    try {
                        $update_stmt = $pdo->prepare("
                            UPDATE session_products 
                            SET unit_price = ?, total_price = ?
                            WHERE id = ?
                        ");
                        $update_stmt->execute([$fix_price, $fix_total, $product['id']]);
                        $fixed_count++;
                        echo " <span style='color: green;'>(تم الإصلاح)</span>";
                    } catch (Exception $e) {
                        echo " <span style='color: red;'>(فشل الإصلاح: {$e->getMessage()})</span>";
                    }
                }
            } else {
                echo "<td style='color: green;'>صحيح</td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<h2>ملخص الإصلاح:</h2>";
        echo "<p>المشاكل الموجودة: <strong>$problems_found</strong></p>";
        echo "<p>السجلات المصلحة: <strong style='color: green;'>$fixed_count</strong></p>";
        
    } else {
        echo "<p>لا توجد بيانات في الجدول</p>";
    }
    
    // إضافة منتج تجريبي للاختبار
    echo "<h2>اختبار إضافة منتج جديد:</h2>";
    
    // البحث عن جلسة نشطة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if ($session) {
        $session_id = $session['session_id'];
        
        // البحث عن منتج
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 AND price > 0 LIMIT 1");
        $product = $stmt->fetch();
        
        if ($product) {
            $quantity = 1;
            $unit_price = $product['price'];
            $total_price = $unit_price * $quantity;
            
            // إضافة المنتج
            $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$session_id, $product['id'], $quantity, $unit_price, $total_price]);
            
            $new_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إضافة منتج تجريبي برقم: $new_id</p>";
            echo "<p>التفاصيل: {$product['name']} × $quantity = $total_price ج.م</p>";
            
            // اختبار جلب البيانات
            $stmt = $pdo->prepare("
                SELECT 
                    sp.quantity,
                    COALESCE(sp.unit_price, sp.price, 0) as price,
                    COALESCE(sp.total_price, sp.total, sp.quantity * COALESCE(sp.unit_price, sp.price, 0)) as total,
                    ci.name as product_name
                FROM session_products sp
                LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
                WHERE sp.id = ?
            ");
            $stmt->execute([$new_id]);
            $test_result = $stmt->fetch();
            
            if ($test_result) {
                echo "<p><strong>نتيجة الاختبار:</strong></p>";
                echo "<ul>";
                echo "<li>اسم المنتج: {$test_result['product_name']}</li>";
                echo "<li>الكمية: {$test_result['quantity']}</li>";
                echo "<li>السعر: {$test_result['price']} ج.م</li>";
                echo "<li>الإجمالي: {$test_result['total']} ج.م</li>";
                echo "</ul>";
                
                if ($test_result['price'] > 0 && $test_result['total'] > 0) {
                    echo "<p style='color: green;'>✅ الاختبار نجح - البيانات صحيحة</p>";
                } else {
                    echo "<p style='color: red;'>❌ الاختبار فشل - البيانات غير صحيحة</p>";
                }
            }
            
            // حذف المنتج التجريبي
            $pdo->prepare("DELETE FROM session_products WHERE id = ?")->execute([$new_id]);
            echo "<p style='color: blue;'>ℹ️ تم حذف المنتج التجريبي</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد منتجات متاحة للاختبار</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
