<?php
/**
 * أداة استيراد محسنة لقاعدة البيانات
 * تتعامل مع المشاكل الشائعة في عملية الاستيراد
 */

require_once 'config/database.php';

// دالة تقسيم استعلامات SQL بطريقة محسنة
function splitSQLQueries($sql) {
    // إزالة التعليقات
    $sql = preg_replace('/--.*$/m', '', $sql);
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
    
    // تقسيم الاستعلامات مع مراعاة النصوص المحاطة بعلامات اقتباس
    $queries = [];
    $current_query = '';
    $in_string = false;
    $string_char = '';
    $escaped = false;
    
    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];
        
        if ($escaped) {
            $current_query .= $char;
            $escaped = false;
            continue;
        }
        
        if ($char === '\\') {
            $current_query .= $char;
            $escaped = true;
            continue;
        }
        
        if (!$in_string && ($char === '"' || $char === "'")) {
            $in_string = true;
            $string_char = $char;
            $current_query .= $char;
        } elseif ($in_string && $char === $string_char) {
            $in_string = false;
            $current_query .= $char;
        } elseif (!$in_string && $char === ';') {
            $query = trim($current_query);
            if (!empty($query)) {
                $queries[] = $query;
            }
            $current_query = '';
        } else {
            $current_query .= $char;
        }
    }
    
    // إضافة الاستعلام الأخير إذا لم ينته بفاصلة منقوطة
    $query = trim($current_query);
    if (!empty($query)) {
        $queries[] = $query;
    }
    
    return array_filter($queries, function($query) {
        $query = trim($query);
        return !empty($query) && !preg_match('/^(--|\/\*)/', $query);
    });
}

// دالة للتحقق من الأخطاء التي يمكن تجاهلها
function isIgnorableError($errorMsg) {
    $ignorableErrors = [
        'Duplicate entry',
        'already exists',
        'Unknown database',
        'Table \'.*\' already exists',
        'Duplicate key name',
        'Multiple primary key defined',
        'Can\'t DROP \'.*\'; check that column/key exists'
    ];
    
    foreach ($ignorableErrors as $pattern) {
        if (preg_match('/' . $pattern . '/i', $errorMsg)) {
            return true;
        }
    }
    
    return false;
}

// دالة تحويل تشفير الملف
function convertFileEncoding($content) {
    $encoding = mb_detect_encoding($content, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
    
    if ($encoding !== 'UTF-8') {
        $content = mb_convert_encoding($content, 'UTF-8', $encoding);
    }
    
    return $content;
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>استيراد محسن لقاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { border: none; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0 !important; }
        .progress-container { background: #f8f9fa; border-radius: 10px; padding: 1rem; margin: 1rem 0; }
        .log-container { max-height: 400px; overflow-y: auto; background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 10px; font-family: monospace; }
        .btn { border-radius: 25px; font-weight: 600; }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-10'>
            <div class='card'>
                <div class='card-header text-center py-4'>
                    <h2 class='mb-0'><i class='fas fa-rocket me-3'></i>استيراد محسن لقاعدة البيانات</h2>
                    <p class='mb-0 mt-2 opacity-75'>أداة متقدمة لاستيراد قاعدة البيانات مع معالجة الأخطاء</p>
                </div>
                <div class='card-body p-4'>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_import'])) {
    try {
        // التحقق من وجود الملف
        $sqlFile = $_POST['sql_file'] ?? 'station.sql';
        
        if (!file_exists($sqlFile)) {
            throw new Exception("ملف SQL غير موجود: $sqlFile");
        }
        
        echo "<div class='alert alert-info'>
                <h5><i class='fas fa-play me-2'></i>بدء عملية الاستيراد المحسن</h5>
                <p>جاري معالجة الملف: <strong>$sqlFile</strong></p>
              </div>";
        
        // قراءة وتحويل تشفير الملف
        $sqlContent = file_get_contents($sqlFile);
        $sqlContent = convertFileEncoding($sqlContent);
        
        echo "<div class='progress-container'>
                <p><i class='fas fa-file-alt me-2'></i>حجم الملف: " . round(strlen($sqlContent) / 1024 / 1024, 2) . " MB</p>
              </div>";
        
        // تقسيم الاستعلامات
        $queries = splitSQLQueries($sqlContent);
        $totalQueries = count($queries);
        
        echo "<div class='progress-container'>
                <p><i class='fas fa-list me-2'></i>عدد الاستعلامات: " . number_format($totalQueries) . "</p>
                <div class='progress mb-3'>
                    <div class='progress-bar progress-bar-striped progress-bar-animated' role='progressbar' style='width: 0%' id='progressBar'></div>
                </div>
              </div>";
        
        echo "<div class='log-container' id='logContainer'>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        // إعدادات محسنة لقاعدة البيانات
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        $pdo->exec("SET SQL_MODE = ''");
        $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("SET CHARACTER SET utf8mb4");
        $pdo->exec("SET SESSION sql_mode = ''");
        $pdo->exec("SET SESSION wait_timeout = 600");
        $pdo->exec("SET SESSION interactive_timeout = 600");
        
        echo "<div class='text-success'>[INFO] تم تطبيق الإعدادات المحسنة لقاعدة البيانات</div>";
        
        $executed = 0;
        $errors = 0;
        $ignored = 0;
        $batchSize = 100; // معالجة الاستعلامات في مجموعات
        
        for ($i = 0; $i < $totalQueries; $i += $batchSize) {
            $batch = array_slice($queries, $i, $batchSize);
            
            foreach ($batch as $index => $query) {
                $query = trim($query);
                if (empty($query)) continue;
                
                $actualIndex = $i + $index;
                
                try {
                    $pdo->exec($query);
                    $executed++;
                    
                    if ($executed % 50 == 0) {
                        $progress = round(($actualIndex / $totalQueries) * 100, 1);
                        echo "<div class='text-info'>[PROGRESS] تم تنفيذ $executed استعلام ($progress%)</div>";
                        echo "<script>
                                document.getElementById('progressBar').style.width = '$progress%';
                                document.getElementById('progressBar').textContent = '$progress%';
                                document.getElementById('logContainer').scrollTop = document.getElementById('logContainer').scrollHeight;
                              </script>";
                        flush();
                    }
                    
                } catch (PDOException $e) {
                    $errorMsg = $e->getMessage();
                    
                    if (isIgnorableError($errorMsg)) {
                        $ignored++;
                        if ($ignored % 10 == 0) {
                            echo "<div class='text-warning'>[IGNORED] تم تجاهل $ignored خطأ غير مهم</div>";
                        }
                    } else {
                        $errors++;
                        echo "<div class='text-danger'>[ERROR] استعلام " . ($actualIndex + 1) . ": " . htmlspecialchars(substr($errorMsg, 0, 100)) . "...</div>";
                        
                        // إيقاف العملية عند 100 خطأ حقيقي
                        if ($errors > 100) {
                            echo "<div class='text-danger'>[FATAL] تم إيقاف العملية بسبب كثرة الأخطاء</div>";
                            break 2;
                        }
                    }
                }
            }
            
            // استراحة قصيرة بين المجموعات
            usleep(10000); // 10ms
        }
        
        // إعادة تفعيل فحص المفاتيح الخارجية
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        echo "<div class='text-success'>[COMPLETE] تم الانتهاء من عملية الاستيراد</div>";
        echo "</div>";
        
        // عرض النتائج
        $successRate = $totalQueries > 0 ? round(($executed / $totalQueries) * 100, 1) : 0;
        
        if ($errors == 0) {
            echo "<div class='alert alert-success mt-4'>
                    <h4><i class='fas fa-check-circle me-2'></i>تم الاستيراد بنجاح! 🎉</h4>
                    <div class='row'>
                        <div class='col-md-3 text-center'>
                            <h5 class='text-success'>$executed</h5>
                            <small>استعلام منفذ</small>
                        </div>
                        <div class='col-md-3 text-center'>
                            <h5 class='text-warning'>$ignored</h5>
                            <small>خطأ متجاهل</small>
                        </div>
                        <div class='col-md-3 text-center'>
                            <h5 class='text-danger'>$errors</h5>
                            <small>خطأ حقيقي</small>
                        </div>
                        <div class='col-md-3 text-center'>
                            <h5 class='text-info'>$successRate%</h5>
                            <small>معدل النجاح</small>
                        </div>
                    </div>
                  </div>";
        } else {
            echo "<div class='alert alert-warning mt-4'>
                    <h4><i class='fas fa-exclamation-triangle me-2'></i>تم الاستيراد مع بعض الأخطاء</h4>
                    <p>تم تنفيذ $executed استعلام بنجاح من أصل $totalQueries، مع $errors خطأ حقيقي و $ignored خطأ متجاهل.</p>
                    <p>معدل النجاح: <strong>$successRate%</strong></p>
                  </div>";
        }
        
        echo "<div class='text-center mt-4'>
                <a href='client/dashboard.php' class='btn btn-primary me-2'>
                    <i class='fas fa-home me-1'></i>لوحة التحكم
                </a>
                <a href='diagnose_import_issues.php' class='btn btn-info me-2'>
                    <i class='fas fa-stethoscope me-1'></i>تشخيص المشاكل
                </a>
                <a href='reset_and_fix_database.php' class='btn btn-warning'>
                    <i class='fas fa-tools me-1'></i>إصلاحات شاملة
                </a>
              </div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
                <h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في الاستيراد!</h5>
                <p><strong>تفاصيل الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
              </div>";
    }
    
} else {
    // عرض نموذج الاستيراد
    echo "<div class='alert alert-info'>
            <h5><i class='fas fa-info-circle me-2'></i>أداة الاستيراد المحسن</h5>
            <p>هذه الأداة تستخدم تقنيات متقدمة لاستيراد قاعدة البيانات مع معالجة الأخطاء الشائعة:</p>
            <ul>
                <li>تحويل تشفير الملف تلقائياً</li>
                <li>تقسيم الاستعلامات بطريقة ذكية</li>
                <li>تجاهل الأخطاء غير المهمة</li>
                <li>معالجة الاستعلامات في مجموعات</li>
                <li>عرض تقدم العملية في الوقت الفعلي</li>
            </ul>
          </div>";
    
    echo "<form method='POST' class='mt-4'>
            <div class='mb-3'>
                <label for='sql_file' class='form-label'>ملف SQL للاستيراد:</label>
                <input type='text' class='form-control' id='sql_file' name='sql_file' value='station.sql' required>
                <div class='form-text'>أدخل مسار ملف SQL المراد استيراده</div>
            </div>
            
            <div class='alert alert-warning'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>تحذير مهم:</h6>
                <p>هذه العملية ستقوم بتعديل قاعدة البيانات الحالية. تأكد من عمل نسخة احتياطية قبل المتابعة.</p>
            </div>
            
            <div class='text-center'>
                <button type='submit' name='start_import' class='btn btn-success btn-lg'>
                    <i class='fas fa-rocket me-2'></i>بدء الاستيراد المحسن
                </button>
            </div>
          </form>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
