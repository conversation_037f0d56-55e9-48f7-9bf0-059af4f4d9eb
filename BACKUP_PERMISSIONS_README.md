# نظام التحكم في صلاحيات النسخ الاحتياطي للعملاء (محدث)

## نظرة عامة
تم تطوير نظام شامل ومتقدم للتحكم في صلاحيات النسخ الاحتياطي، يوفر مستويين من التحكم:
1. **التحكم العام**: تفعيل/تعطيل الميزة لجميع العملاء
2. **التحكم الفردي**: تفعيل/تعطيل الميزة لعملاء محددين

## الميزات الجديدة المضافة

### 🆕 التحكم الفردي في العملاء
- إمكانية تفعيل/تعطيل النسخ الاحتياطي لكل عميل على حدة
- حقل `backup_enabled` في جدول العملاء
- واجهة إدارة متقدمة في صفحة العملاء
- اختبارات شاملة للتأكد من عمل النظام

## الميزات الجديدة

### 1. جدول إعدادات الأدمن (`admin_settings`)
- **الغرض**: تخزين إعدادات النظام العامة
- **الحقول**:
  - `setting_id`: المعرف الفريد
  - `setting_key`: مفتاح الإعداد
  - `setting_value`: قيمة الإعداد
  - `setting_description`: وصف الإعداد
  - `created_at`, `updated_at`: تواريخ الإنشاء والتحديث

### 2. الإعدادات المتاحة
- **`backup_enabled`**: تفعيل/تعطيل النسخ الاحتياطي للعملاء (1/0)
- **`system_maintenance`**: وضع الصيانة للنظام (1/0)
- **`max_backup_files`**: الحد الأقصى لعدد ملفات النسخ الاحتياطي (رقم)

### 3. ملف الدوال المساعدة (`includes/backup_permissions.php`) - محدث
#### الدوال الأساسية:
- `isBackupEnabledGlobally()`: التحقق من الإعداد العام للنسخ الاحتياطي
- `isBackupEnabledForClient($client_id)`: التحقق من صلاحية عميل محدد
- `isBackupEnabled()`: دالة للتوافق مع النظام القديم
- `isSystemInMaintenance()`: التحقق من وضع الصيانة
- `getMaxBackupFiles()`: الحصول على الحد الأقصى للملفات

#### دوال إدارة العملاء الجديدة:
- `setClientBackupPermission($client_id, $enabled)`: تفعيل/تعطيل النسخ الاحتياطي لعميل
- `getClientBackupStatus($client_id)`: الحصول على حالة النسخ الاحتياطي لعميل
- `ensureBackupColumnExists()`: التأكد من وجود حقل backup_enabled
- `getClientsWithBackupStatus()`: جلب قائمة العملاء مع حالة النسخ الاحتياطي
- `getClientBackupDisabledMessage($client_id)`: رسالة تعطيل مخصصة للعميل

#### دوال مساعدة:
- `getAdminSettings()`: جلب جميع إعدادات الأدمن
- `updateAdminSetting($key, $value)`: تحديث إعداد معين
- `getBackupDisabledMessage()`: رسالة تعطيل النسخ الاحتياطي العامة
- `getMaintenanceModeMessage()`: رسالة وضع الصيانة

## التحديثات على الملفات

### 1. صفحة إعدادات الأدمن (`admin/settings.php`)
- إضافة قسم "صلاحيات العملاء"
- مفاتيح تبديل لتفعيل/تعطيل النسخ الاحتياطي العام
- إعداد وضع الصيانة
- تحديد الحد الأقصى لملفات النسخ الاحتياطي
- حفظ الإعدادات في قاعدة البيانات

### 2. صفحة إدارة العملاء (`admin/clients.php`) - جديد
- إضافة عمود "النسخ الاحتياطي" في جدول العملاء
- أزرار تفعيل/تعطيل لكل عميل على حدة
- عرض حالة النسخ الاحتياطي بصرياً (مفعل/معطل)
- معالجة طلبات تغيير صلاحيات العملاء
- التأكد من وجود حقل backup_enabled تلقائياً

### 3. صفحة إعدادات العميل (`client/settings.php`) - محدث
- التحقق من صلاحية العميل المحدد بدلاً من الإعداد العام
- إخفاء تبويب النسخ الاحتياطي عند تعطيل صلاحية العميل
- عرض رسائل تنبيهية مخصصة للعميل
- تعطيل الأزرار في وضع الصيانة

### 4. معالج النسخ الاحتياطي (`client/backup_handler.php`) - محدث
- التحقق من صلاحية العميل المحدد قبل تنفيذ أي عملية
- منع الوصول عند تعطيل صلاحية العميل
- منع العمليات في وضع الصيانة
- تطبيق الحد الأقصى لعدد الملفات لكل عميل

## كيفية الاستخدام

### للأدمن - الإعدادات العامة:
1. الدخول إلى صفحة إعدادات الأدمن (`admin/settings.php`)
2. البحث عن قسم "صلاحيات العملاء"
3. تفعيل/تعطيل النسخ الاحتياطي عموماً باستخدام المفتاح
4. تعيين وضع الصيانة إذا لزم الأمر
5. تحديد الحد الأقصى لعدد ملفات النسخ الاحتياطي
6. النقر على "حفظ صلاحيات العملاء"

### للأدمن - إدارة العملاء الفردية:
1. الدخول إلى صفحة إدارة العملاء (`admin/clients.php`)
2. البحث عن العميل المطلوب في الجدول
3. النظر إلى عمود "النسخ الاحتياطي" لمعرفة الحالة الحالية
4. النقر على زر التفعيل/التعطيل (أيقونة قاعدة البيانات أو المنع)
5. تأكيد العملية في النافذة المنبثقة

### للعميل:
- **عند تفعيل الصلاحية**: يظهر تبويب "النسخ الاحتياطي" في الإعدادات
- **عند تعطيل الصلاحية**: يختفي التبويب ويظهر تنبيه مخصص في حالة الوصول المباشر
- **عند تعطيل الإعداد العام**: تتعطل الميزة لجميع العملاء بغض النظر عن صلاحياتهم الفردية
- **في وضع الصيانة**: تظهر رسالة تنبيه وتتعطل الأزرار

## الاختبار

### ملفات الاختبار المتاحة:

#### 1. `add_client_backup_column.php`
ملف لإضافة حقل النسخ الاحتياطي إلى جدول العملاء:
- إضافة حقل `backup_enabled` إلى جدول `clients`
- عرض بنية الجدول الحالية
- عرض العملاء مع حالة النسخ الاحتياطي
- اختبار الدوال الأساسية

#### 2. `test_backup_permissions.php`
اختبار الإعدادات العامة:
- إنشاء جدول الإعدادات العامة
- إدراج الإعدادات الافتراضية
- اختبار الدوال العامة
- عرض الإعدادات الحالية
- تحديث الإعدادات

#### 3. `test_client_backup_permissions.php`
اختبار شامل للميزة الجديدة:
- اختبار دوال العملاء الفردية
- عرض جدول العملاء مع صلاحياتهم
- تفعيل/تعطيل صلاحيات العملاء
- اختبار سيناريوهات مختلفة
- عرض الرسائل المخصصة

### خطوات الاختبار الشاملة:
1. تشغيل `add_client_backup_column.php` لإضافة الحقل الجديد
2. تشغيل `test_client_backup_permissions.php` لاختبار الميزة الجديدة
3. اختبار تغيير الإعدادات من صفحة الأدمن
4. اختبار تغيير صلاحيات العملاء من صفحة إدارة العملاء
5. التحقق من تأثير التغييرات على صفحات العملاء

## الأمان

### التحققات المطبقة:
- التحقق من تسجيل دخول الأدمن قبل تعديل الإعدادات
- التحقق من تسجيل دخول العميل قبل الوصول للنسخ الاحتياطي
- التحقق من الصلاحيات في كل طلب
- منع الوصول المباشر للملفات الحساسة

### رسائل الخطأ:
- `403 Forbidden`: عند تعطيل الميزة
- `503 Service Unavailable`: في وضع الصيانة
- `401 Unauthorized`: عند عدم تسجيل الدخول

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `includes/backup_permissions.php`: دوال التحقق من الصلاحيات
- `test_backup_permissions.php`: ملف اختبار الميزة
- `BACKUP_PERMISSIONS_README.md`: هذا الملف

### ملفات محدثة:
- `admin/settings.php`: إضافة قسم صلاحيات العملاء
- `client/settings.php`: إضافة التحقق من الصلاحيات
- `client/backup_handler.php`: إضافة التحقق من الصلاحيات

## المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- صلاحيات إنشاء الجداول في قاعدة البيانات

## الدعم الفني
في حالة وجود مشاكل أو استفسارات، يرجى التحقق من:
1. صلاحيات قاعدة البيانات
2. وجود ملف `includes/backup_permissions.php`
3. إعدادات PHP (خاصة `exec()` function)
4. مسار `mysqldump` في النظام

## التطوير المستقبلي
يمكن إضافة المزيد من الإعدادات مثل:
- تحديد أوقات معينة للنسخ الاحتياطي
- إشعارات البريد الإلكتروني
- جدولة النسخ الاحتياطي التلقائي
- تشفير ملفات النسخ الاحتياطي
