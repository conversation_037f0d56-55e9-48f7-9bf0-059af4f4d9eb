<?php
/**
 * سكريبت فحص وإصلاح الأخطاء الشائعة
 * يتحقق من المشاكل المحتملة ويصلحها
 */

require_once 'config/database.php';

echo "<h1>فحص وإصلاح الأخطاء - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$errors_found = 0;
$fixes_applied = 0;

try {
    echo "<h2>1. فحص قاعدة البيانات</h2>";
    
    // فحص وجود الجداول المطلوبة
    $required_tables = ['clients', 'devices', 'sessions', 'rooms', 'employees', 'customers', 'cafeteria_items'];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ جدول $table موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
                $errors_found++;
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في فحص جدول $table: " . $e->getMessage() . "</p>";
            $errors_found++;
        }
    }
    
    echo "<h2>2. فحص الأعمدة المطلوبة</h2>";
    
    // فحص أعمدة جدول sessions
    try {
        $stmt = $pdo->query("DESCRIBE sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_session_columns = ['session_id', 'device_id', 'start_time', 'end_time', 'status', 'client_id', 'total_cost'];
        
        foreach ($required_session_columns as $column) {
            if (in_array($column, $columns)) {
                echo "<p style='color: green;'>✅ عمود sessions.$column موجود</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ عمود sessions.$column غير موجود</p>";
                $errors_found++;
                
                // محاولة إضافة العمود
                try {
                    switch ($column) {
                        case 'client_id':
                            $pdo->exec("ALTER TABLE sessions ADD COLUMN client_id INT NULL");
                            echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
                            $fixes_applied++;
                            break;
                        case 'total_cost':
                            $pdo->exec("ALTER TABLE sessions ADD COLUMN total_cost DECIMAL(10,2) DEFAULT 0.00");
                            echo "<p style='color: green;'>✅ تم إضافة عمود total_cost</p>";
                            $fixes_applied++;
                            break;
                    }
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ فشل في إضافة عمود $column: " . $e->getMessage() . "</p>";
                }
            }
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص أعمدة sessions: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
    
    echo "<h2>3. فحص البيانات</h2>";
    
    // فحص الجلسات بدون client_id
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM sessions WHERE client_id IS NULL");
        $sessions_without_client = $stmt->fetchColumn();
        
        if ($sessions_without_client > 0) {
            echo "<p style='color: orange;'>⚠️ يوجد $sessions_without_client جلسة بدون معرف عميل</p>";
            
            // إصلاح البيانات
            try {
                $pdo->exec("UPDATE sessions s 
                           JOIN devices d ON s.device_id = d.device_id 
                           SET s.client_id = d.client_id 
                           WHERE s.client_id IS NULL");
                echo "<p style='color: green;'>✅ تم إصلاح معرفات العملاء في الجلسات</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إصلاح معرفات العملاء: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ جميع الجلسات لها معرف عميل صحيح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص بيانات الجلسات: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
    
    // فحص الأجهزة بدون client_id
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM devices WHERE client_id IS NULL");
        $devices_without_client = $stmt->fetchColumn();
        
        if ($devices_without_client > 0) {
            echo "<p style='color: orange;'>⚠️ يوجد $devices_without_client جهاز بدون معرف عميل</p>";
            
            // إصلاح البيانات
            try {
                $pdo->exec("UPDATE devices SET client_id = 1 WHERE client_id IS NULL");
                echo "<p style='color: green;'>✅ تم تعيين معرف العميل الافتراضي للأجهزة</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إصلاح معرفات العملاء للأجهزة: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ جميع الأجهزة لها معرف عميل صحيح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص بيانات الأجهزة: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
    
    echo "<h2>4. فحص الملفات</h2>";
    
    // فحص وجود الملفات المطلوبة
    $required_files = [
        'client/dashboard.php',
        'client/sessions.php',
        'client/devices.php',
        'client/includes/header.php',
        'client/includes/footer.php',
        'config/database.php'
    ];
    
    foreach ($required_files as $file) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ ملف $file موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ ملف $file غير موجود</p>";
            $errors_found++;
        }
    }
    
    echo "<h2>5. اختبار الاستعلامات</h2>";
    
    // اختبار استعلام لوحة التحكم
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_devices,
                   SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_devices,
                   SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_devices,
                   SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_devices
            FROM devices 
            WHERE client_id = ?
        ");
        $stmt->execute([1]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<p style='color: green;'>✅ استعلام إحصائيات الأجهزة يعمل بشكل صحيح</p>";
            echo "<p>إجمالي الأجهزة: " . $result['total_devices'] . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أجهزة مسجلة</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام الأجهزة: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
    
    // اختبار استعلام الجلسات
    try {
        $stmt = $pdo->prepare("
            SELECT s.*, d.device_name, d.hourly_rate
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ? AND s.status = 'active'
            LIMIT 1
        ");
        $stmt->execute([1]);
        $result = $stmt->fetch();
        
        echo "<p style='color: green;'>✅ استعلام الجلسات النشطة يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام الجلسات: " . $e->getMessage() . "</p>";
        $errors_found++;
    }
    
    echo "<h2>النتائج النهائية</h2>";
    
    if ($errors_found == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ ممتاز! لم يتم العثور على أي أخطاء</h3>";
        echo "<p>النظام جاهز للاستخدام</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ تم العثور على $errors_found مشكلة</h3>";
        echo "<p>تم إصلاح $fixes_applied مشكلة تلقائياً</p>";
        echo "</div>";
    }
    
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li><a href='client/dashboard.php'>اختبار لوحة التحكم</a></li>";
    echo "<li><a href='client/sessions.php'>اختبار صفحة الجلسات</a></li>";
    echo "<li><a href='client/devices.php'>اختبار صفحة الأجهزة</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ عام</h2>";
    echo "<p style='color: red;'>$e->getMessage()</p>";
}

echo "</div>";
?>
