# ===== INCLUDES DIRECTORY PROTECTION =====
# Block direct access to include files
Require all denied

# Block all file access
<Files "*">
    Require all denied
</Files>

# Disable directory browsing
Options -Indexes

# Block script execution from web
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
</IfModule>
