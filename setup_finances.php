<?php
/**
 * سكريپت إعداد سريع لصفحتي التقارير والماليات
 * يقوم بإنشاء الجداول المطلوبة وإعداد البيانات الافتراضية
 */

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/database.php')) {
    die('ملف إعدادات قاعدة البيانات غير موجود. يرجى التأكد من وجود config/database.php');
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد صفحتي التقارير والماليات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        .setup-card { border-left: 4px solid #007bff; }
        .success-card { border-left-color: #28a745; }
        .error-card { border-left-color: #dc3545; }
        .step-number { 
            background: #007bff; 
            color: white; 
            border-radius: 50%; 
            width: 30px; 
            height: 30px; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            margin-left: 10px;
        }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card setup-card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-cogs me-2'></i>
                        إعداد صفحتي التقارير والماليات
                    </h3>
                </div>
                <div class='card-body'>";

$errors = [];
$success_steps = [];

try {
    // الخطوة 1: التحقق من الاتصال بقاعدة البيانات
    echo "<div class='mb-4'>
            <h5><span class='step-number'>1</span>التحقق من الاتصال بقاعدة البيانات</h5>";
    
    $pdo->query("SELECT 1");
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم الاتصال بقاعدة البيانات بنجاح</div>";
    $success_steps[] = "database_connection";
    
    // الخطوة 2: التحقق من وجود الجداول الأساسية
    echo "</div><div class='mb-4'>
            <h5><span class='step-number'>2</span>التحقق من الجداول الأساسية</h5>";
    
    $required_tables = ['clients', 'sessions', 'devices', 'customers'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>جدول $table موجود</p>";
        } catch (PDOException $e) {
            $missing_tables[] = $table;
            echo "<p class='text-danger mb-1'><i class='fas fa-times me-2'></i>جدول $table غير موجود</p>";
        }
    }
    
    if (empty($missing_tables)) {
        echo "<div class='alert alert-success mt-2'><i class='fas fa-check me-2'></i>جميع الجداول الأساسية موجودة</div>";
        $success_steps[] = "basic_tables";
    } else {
        echo "<div class='alert alert-warning mt-2'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                بعض الجداول الأساسية مفقودة: " . implode(', ', $missing_tables) . "
              </div>";
        $errors[] = "missing_basic_tables";
    }
    
    // الخطوة 3: إنشاء جداول الماليات
    echo "</div><div class='mb-4'>
            <h5><span class='step-number'>3</span>إنشاء جداول الماليات</h5>";
    
    // جدول أنواع المصروفات
    $expense_types_sql = "
        CREATE TABLE IF NOT EXISTS expense_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_expense_types_client (client_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($expense_types_sql);
    echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>جدول أنواع المصروفات (expense_types)</p>";
    
    // جدول المصروفات
    $expenses_sql = "
        CREATE TABLE IF NOT EXISTS expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            expense_type_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            expense_date DATE NOT NULL,
            receipt_number VARCHAR(50),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_expenses_client (client_id),
            INDEX idx_expenses_type (expense_type_id),
            INDEX idx_expenses_date (expense_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($expenses_sql);
    echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>جدول المصروفات (expenses)</p>";
    
    // جدول أنواع الإيرادات
    $income_types_sql = "
        CREATE TABLE IF NOT EXISTS income_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_income_types_client (client_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($income_types_sql);
    echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>جدول أنواع الإيرادات (income_types)</p>";
    
    // جدول الإيرادات الإضافية
    $additional_income_sql = "
        CREATE TABLE IF NOT EXISTS additional_income (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            income_type_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            income_date DATE NOT NULL,
            receipt_number VARCHAR(50),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_additional_income_client (client_id),
            INDEX idx_additional_income_type (income_type_id),
            INDEX idx_additional_income_date (income_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($additional_income_sql);
    echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>جدول الإيرادات الإضافية (additional_income)</p>";
    
    echo "<div class='alert alert-success mt-2'><i class='fas fa-check me-2'></i>تم إنشاء جميع جداول الماليات بنجاح</div>";
    $success_steps[] = "finance_tables";
    
    // الخطوة 4: إدراج البيانات الافتراضية
    echo "</div><div class='mb-4'>
            <h5><span class='step-number'>4</span>إدراج البيانات الافتراضية</h5>";
    
    // الحصول على جميع العملاء
    $clients_query = $pdo->query("SELECT client_id FROM clients");
    $clients = $clients_query->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($clients)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد عملاء في قاعدة البيانات</div>";
    } else {
        foreach ($clients as $client_id) {
            // أنواع المصروفات الافتراضية
            $default_expense_types = [
                ['name' => 'فواتير الكهرباء', 'description' => 'فواتير استهلاك الكهرباء الشهرية'],
                ['name' => 'فواتير الإنترنت', 'description' => 'فواتير خدمة الإنترنت الشهرية'],
                ['name' => 'إيجار المحل', 'description' => 'إيجار المحل الشهري'],
                ['name' => 'صيانة الأجهزة', 'description' => 'تكاليف صيانة وإصلاح الأجهزة'],
                ['name' => 'مشتريات الكافتيريا', 'description' => 'شراء منتجات ومشروبات الكافتيريا'],
                ['name' => 'رواتب الموظفين', 'description' => 'رواتب ومكافآت الموظفين'],
                ['name' => 'مصروفات إدارية', 'description' => 'مصروفات إدارية متنوعة'],
                ['name' => 'تسويق وإعلان', 'description' => 'تكاليف التسويق والإعلان']
            ];
            
            foreach ($default_expense_types as $type) {
                $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM expense_types WHERE client_id = ? AND name = ?");
                $check_stmt->execute([$client_id, $type['name']]);
                
                if ($check_stmt->fetchColumn() == 0) {
                    $insert_stmt = $pdo->prepare("INSERT INTO expense_types (client_id, name, description) VALUES (?, ?, ?)");
                    $insert_stmt->execute([$client_id, $type['name'], $type['description']]);
                }
            }
            
            // أنواع الإيرادات الافتراضية
            $default_income_types = [
                ['name' => 'إيرادات الجلسات', 'description' => 'إيرادات من جلسات الألعاب'],
                ['name' => 'مبيعات الكافتيريا', 'description' => 'إيرادات من مبيعات الكافتيريا'],
                ['name' => 'خدمات إضافية', 'description' => 'إيرادات من خدمات إضافية أخرى'],
                ['name' => 'تأجير قاعات', 'description' => 'إيرادات من تأجير القاعات للمناسبات'],
                ['name' => 'بيع أكسسوارات', 'description' => 'إيرادات من بيع أكسسوارات الألعاب']
            ];
            
            foreach ($default_income_types as $type) {
                $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM income_types WHERE client_id = ? AND name = ?");
                $check_stmt->execute([$client_id, $type['name']]);
                
                if ($check_stmt->fetchColumn() == 0) {
                    $insert_stmt = $pdo->prepare("INSERT INTO income_types (client_id, name, description) VALUES (?, ?, ?)");
                    $insert_stmt->execute([$client_id, $type['name'], $type['description']]);
                }
            }
        }
        
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إدراج البيانات الافتراضية لجميع العملاء</div>";
        $success_steps[] = "default_data";
    }
    
    // الخطوة 5: التحقق من الملفات
    echo "</div><div class='mb-4'>
            <h5><span class='step-number'>5</span>التحقق من الملفات المطلوبة</h5>";
    
    $required_files = [
        'client/reports.php' => 'صفحة التقارير',
        'client/finances.php' => 'صفحة الماليات'
    ];
    
    $missing_files = [];
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>$description ($file)</p>";
        } else {
            $missing_files[] = $file;
            echo "<p class='text-danger mb-1'><i class='fas fa-times me-2'></i>$description ($file) - مفقود</p>";
        }
    }
    
    if (empty($missing_files)) {
        echo "<div class='alert alert-success mt-2'><i class='fas fa-check me-2'></i>جميع الملفات المطلوبة موجودة</div>";
        $success_steps[] = "required_files";
    } else {
        echo "<div class='alert alert-danger mt-2'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                بعض الملفات مفقودة: " . implode(', ', $missing_files) . "
              </div>";
        $errors[] = "missing_files";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
    $errors[] = "database_error";
}

// عرض النتيجة النهائية
if (empty($errors)) {
    echo "<div class='alert alert-success success-card'>
            <h4><i class='fas fa-check-circle me-2'></i>تم الإعداد بنجاح! 🎉</h4>
            <p>تم إنشاء وإعداد صفحتي التقارير والماليات بنجاح. يمكنك الآن:</p>
            <ul class='mb-3'>
                <li>الوصول لصفحة التقارير من القائمة الرئيسية</li>
                <li>الوصول لصفحة الماليات من القائمة الرئيسية</li>
                <li>إضافة المصروفات والإيرادات</li>
                <li>عرض التقارير المفصلة</li>
                <li>تصدير البيانات بصيغ مختلفة</li>
            </ul>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/reports.php' class='btn btn-primary me-2'>
                <i class='fas fa-chart-bar me-1'></i>صفحة التقارير
            </a>
            <a href='client/finances.php' class='btn btn-success me-2'>
                <i class='fas fa-money-bill-wave me-1'></i>صفحة الماليات
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
} else {
    echo "<div class='alert alert-danger error-card'>
            <h4><i class='fas fa-exclamation-triangle me-2'></i>يوجد مشاكل تحتاج لحل</h4>
            <p>يرجى حل المشاكل التالية وإعادة تشغيل السكريپت:</p>
            <ul>";
    
    foreach ($errors as $error) {
        switch ($error) {
            case 'missing_basic_tables':
                echo "<li>بعض الجداول الأساسية مفقودة - يرجى تشغيل سكريپت إنشاء قاعدة البيانات الأساسية</li>";
                break;
            case 'missing_files':
                echo "<li>بعض الملفات المطلوبة مفقودة - يرجى التأكد من رفع جميع الملفات</li>";
                break;
            case 'database_error':
                echo "<li>خطأ في قاعدة البيانات - يرجى التحقق من إعدادات الاتصال</li>";
                break;
        }
    }
    
    echo "</ul>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <button onclick='location.reload()' class='btn btn-warning me-2'>
                <i class='fas fa-redo me-1'></i>إعادة المحاولة
            </button>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>العودة للوحة التحكم
            </a>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
