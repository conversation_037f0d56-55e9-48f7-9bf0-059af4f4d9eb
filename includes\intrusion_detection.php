<?php
/**
 * نظام كشف ومنع التسلل (IDS/IPS)
 * يراقب الأنشطة المشبوهة ويتخذ إجراءات وقائية
 */

class IntrusionDetectionSystem {
    
    private $pdo;
    private $security;
    private $threat_levels = [
        'low' => 1,
        'medium' => 3,
        'high' => 7,
        'critical' => 15
    ];
    
    public function __construct($pdo, $security) {
        $this->pdo = $pdo;
        $this->security = $security;
        $this->initializeIDS();
    }
    
    /**
     * تهيئة نظام كشف التسلل
     */
    private function initializeIDS() {
        try {
            // جدول التهديدات المكتشفة
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS detected_threats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    threat_type VARCHAR(100) NOT NULL,
                    threat_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
                    description TEXT,
                    request_uri TEXT,
                    user_agent TEXT,
                    request_data JSON,
                    action_taken VARCHAR(100),
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ip_level (ip_address, threat_level),
                    INDEX idx_type_time (threat_type, detected_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            // جدول قواعد الكشف
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS detection_rules (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    rule_name VARCHAR(100) NOT NULL UNIQUE,
                    rule_pattern TEXT NOT NULL,
                    threat_type VARCHAR(100) NOT NULL,
                    threat_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            // إدراج قواعد الكشف الأساسية
            $this->insertDefaultRules();
            
        } catch (PDOException $e) {
            error_log("IDS initialization failed: " . $e->getMessage());
        }
    }
    
    /**
     * إدراج قواعد الكشف الافتراضية
     */
    private function insertDefaultRules() {
        $default_rules = [
            [
                'rule_name' => 'sql_injection_basic',
                'rule_pattern' => '/(union|select|insert|update|delete|drop|create|alter|exec|script)/i',
                'threat_type' => 'sql_injection',
                'threat_level' => 'high'
            ],
            [
                'rule_name' => 'xss_script_tags',
                'rule_pattern' => '/<script[^>]*>.*?<\/script>/i',
                'threat_type' => 'xss',
                'threat_level' => 'high'
            ],
            [
                'rule_name' => 'path_traversal',
                'rule_pattern' => '/(\.\.[\/\\\\]|\.\.%2f|\.\.%5c)/i',
                'threat_type' => 'path_traversal',
                'threat_level' => 'medium'
            ],
            [
                'rule_name' => 'command_injection',
                'rule_pattern' => '/(;|\||&|`|\$\(|\${)/i',
                'threat_type' => 'command_injection',
                'threat_level' => 'critical'
            ],
            [
                'rule_name' => 'file_inclusion',
                'rule_pattern' => '/(include|require|file_get_contents|fopen|readfile)/i',
                'threat_type' => 'file_inclusion',
                'threat_level' => 'high'
            ],
            [
                'rule_name' => 'suspicious_user_agent',
                'rule_pattern' => '/(sqlmap|nmap|nikto|burp|acunetix|nessus|openvas|w3af)/i',
                'threat_type' => 'scanning_tool',
                'threat_level' => 'critical'
            ]
        ];
        
        try {
            foreach ($default_rules as $rule) {
                $stmt = $this->pdo->prepare("
                    INSERT IGNORE INTO detection_rules 
                    (rule_name, rule_pattern, threat_type, threat_level) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([
                    $rule['rule_name'],
                    $rule['rule_pattern'],
                    $rule['threat_type'],
                    $rule['threat_level']
                ]);
            }
        } catch (PDOException $e) {
            error_log("Default rules insertion failed: " . $e->getMessage());
        }
    }
    
    /**
     * فحص الطلب الحالي للتهديدات
     */
    public function scanCurrentRequest() {
        $ip = $this->security->getClientIp();
        $threats_detected = [];
        
        // جمع بيانات الطلب
        $request_data = [
            'GET' => $_GET,
            'POST' => $_POST,
            'COOKIE' => $_COOKIE,
            'SERVER' => [
                'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? '',
                'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'HTTP_REFERER' => $_SERVER['HTTP_REFERER'] ?? ''
            ]
        ];
        
        // فحص ضد قواعد الكشف
        $rules = $this->getActiveRules();
        
        foreach ($rules as $rule) {
            $pattern = $rule['rule_pattern'];
            $threat_found = false;
            $matched_data = [];
            
            // فحص جميع بيانات الطلب
            foreach ($request_data as $source => $data) {
                if ($this->scanDataAgainstRule($data, $pattern, $matched_data)) {
                    $threat_found = true;
                }
            }
            
            if ($threat_found) {
                $threat = [
                    'rule_name' => $rule['rule_name'],
                    'threat_type' => $rule['threat_type'],
                    'threat_level' => $rule['threat_level'],
                    'matched_data' => $matched_data,
                    'description' => "تم اكتشاف {$rule['threat_type']} باستخدام القاعدة {$rule['rule_name']}"
                ];
                
                $threats_detected[] = $threat;
                
                // تسجيل التهديد
                $this->logThreat($ip, $threat, $request_data);
            }
        }
        
        // اتخاذ إجراءات بناءً على مستوى التهديد
        if (!empty($threats_detected)) {
            $this->handleThreats($ip, $threats_detected);
        }
        
        return $threats_detected;
    }
    
    /**
     * فحص البيانات ضد قاعدة معينة
     */
    private function scanDataAgainstRule($data, $pattern, &$matched_data) {
        if (is_array($data)) {
            $found = false;
            foreach ($data as $key => $value) {
                if ($this->scanDataAgainstRule($value, $pattern, $matched_data)) {
                    $found = true;
                }
            }
            return $found;
        }
        
        if (is_string($data) && preg_match($pattern, $data, $matches)) {
            $matched_data[] = [
                'data' => $data,
                'matches' => $matches
            ];
            return true;
        }
        
        return false;
    }
    
    /**
     * الحصول على القواعد النشطة
     */
    private function getActiveRules() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM detection_rules 
                WHERE is_active = TRUE 
                ORDER BY threat_level DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Failed to get active rules: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تسجيل التهديد
     */
    private function logThreat($ip, $threat, $request_data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO detected_threats 
                (ip_address, threat_type, threat_level, description, 
                 request_uri, user_agent, request_data) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $ip,
                $threat['threat_type'],
                $threat['threat_level'],
                $threat['description'],
                $_SERVER['REQUEST_URI'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($request_data)
            ]);
            
        } catch (PDOException $e) {
            error_log("Threat logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * التعامل مع التهديدات المكتشفة
     */
    private function handleThreats($ip, $threats) {
        $total_threat_score = 0;
        $highest_level = 'low';
        
        // حساب نقاط التهديد الإجمالية
        foreach ($threats as $threat) {
            $total_threat_score += $this->threat_levels[$threat['threat_level']];
            
            if ($this->threat_levels[$threat['threat_level']] > $this->threat_levels[$highest_level]) {
                $highest_level = $threat['threat_level'];
            }
        }
        
        // اتخاذ إجراءات بناءً على مستوى التهديد
        if ($highest_level === 'critical' || $total_threat_score >= 10) {
            // حظر فوري
            $this->security->blockIp($ip, 'تهديدات أمنية خطيرة مكتشفة', 86400); // 24 ساعة
            $this->terminateRequest('تم حظر وصولك بسبب أنشطة مشبوهة');
            
        } elseif ($highest_level === 'high' || $total_threat_score >= 7) {
            // حظر مؤقت
            $this->security->blockIp($ip, 'تهديدات أمنية عالية مكتشفة', 3600); // ساعة واحدة
            $this->terminateRequest('تم حظر وصولك مؤقتاً بسبب أنشطة مشبوهة');
            
        } elseif ($total_threat_score >= 3) {
            // تحذير وتسجيل
            $this->security->logSuspiciousActivity($ip, 'multiple_threats', 
                'تم اكتشاف تهديدات متعددة', 'medium');
        }
    }
    
    /**
     * إنهاء الطلب بسبب تهديد
     */
    private function terminateRequest($message) {
        http_response_code(403);
        
        // تسجيل محاولة الوصول
        error_log("Security threat blocked: " . $_SERVER['REMOTE_ADDR'] . " - " . $_SERVER['REQUEST_URI']);
        
        // إرسال رد مناسب
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
            header('Content-Type: application/json');
            echo json_encode(['error' => $message]);
        } else {
            echo "<!DOCTYPE html>
            <html dir='rtl'>
            <head>
                <title>وصول محظور</title>
                <meta charset='utf-8'>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .error { color: #d32f2f; font-size: 18px; }
                </style>
            </head>
            <body>
                <h1>وصول محظور</h1>
                <p class='error'>$message</p>
                <p>إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بالدعم الفني.</p>
            </body>
            </html>";
        }
        
        exit;
    }
    
    /**
     * الحصول على إحصائيات التهديدات
     */
    public function getThreatStatistics($days = 7) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    threat_type,
                    threat_level,
                    COUNT(*) as count,
                    COUNT(DISTINCT ip_address) as unique_ips
                FROM detected_threats 
                WHERE detected_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY threat_type, threat_level
                ORDER BY count DESC
            ");
            
            $stmt->execute([$days]);
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Failed to get threat statistics: " . $e->getMessage());
            return [];
        }
    }
}

// تهيئة نظام كشف التسلل
if (isset($pdo) && isset($security)) {
    $ids = new IntrusionDetectionSystem($pdo, $security);
    
    // فحص الطلب الحالي (إلا إذا كان طلب AJAX أو API)
    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strpos($_SERVER['REQUEST_URI'], '/api/') === false) {
        $ids->scanCurrentRequest();
    }
}
?>
