# ===== UPLOADS DIRECTORY PROTECTION =====
# Block execution of scripts
<Files "*">
    Set<PERSON><PERSON><PERSON> default-handler
</Files>

# Disable PHP execution
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Block dangerous file types
<FilesMatch "\.(php|php3|php4|php5|phtml|asp|aspx|jsp|js|vbs|exe|com|bat|cmd|scr|pif|msi|dll|sh|pl|py|rb|jar|war|ear|htaccess|htpasswd)$">
    Require all denied
</FilesMatch>

# Allow only specific image and document types
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|txt|doc|docx|xls|xlsx|csv|zip)$">
    Require all granted
</FilesMatch>

# Security headers for files
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set Content-Security-Policy "default-src 'none'; img-src 'self'; object-src 'none';"
</IfModule>

# Disable directory browsing
Options -Indexes

# Force download for certain file types
<FilesMatch "\.(pdf|doc|docx|xls|xlsx|zip)$">
    Header set Content-Disposition "attachment"
</FilesMatch>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType application/pdf .pdf
    AddType text/plain .txt
</IfModule>
