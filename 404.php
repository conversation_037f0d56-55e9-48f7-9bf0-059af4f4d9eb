<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 900;
            line-height: 1;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .error-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-error {
            padding: 1rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateY(-2px);
        }
        
        .floating-icons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-icon {
            position: absolute;
            color: rgba(255, 255, 255, 0.1);
            font-size: 2rem;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 10%; right: 15%; animation-delay: 3s; }
        .floating-icon:nth-child(5) { top: 50%; left: 5%; animation-delay: 4s; }
        .floating-icon:nth-child(6) { top: 60%; right: 5%; animation-delay: 5s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
            75% { transform: translateY(-15px) rotate(270deg); }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-description {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-error {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-icons">
        <i class="fas fa-gamepad floating-icon"></i>
        <i class="fas fa-desktop floating-icon"></i>
        <i class="fas fa-users floating-icon"></i>
        <i class="fas fa-chart-bar floating-icon"></i>
        <i class="fas fa-cog floating-icon"></i>
        <i class="fas fa-star floating-icon"></i>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        <p class="error-description">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            يمكنك العودة إلى الصفحة الرئيسية أو تصفح أقسام الموقع.
        </p>
        
        <div class="error-actions">
            <a href="/" class="btn-error btn-primary">
                <i class="fas fa-home"></i>
                العودة للرئيسية
            </a>
            <a href="javascript:history.back()" class="btn-error btn-secondary">
                <i class="fas fa-arrow-right"></i>
                الصفحة السابقة
            </a>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const errorCode = document.querySelector('.error-code');
            
            // Add pulse effect to error code
            setInterval(() => {
                errorCode.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    errorCode.style.transform = 'scale(1)';
                }, 200);
            }, 3000);
            
            // Add hover effect to floating icons
            const floatingIcons = document.querySelectorAll('.floating-icon');
            floatingIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.color = 'rgba(255, 255, 255, 0.3)';
                    this.style.transform = 'scale(1.2)';
                });
                
                icon.addEventListener('mouseleave', function() {
                    this.style.color = 'rgba(255, 255, 255, 0.1)';
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
