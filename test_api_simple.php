<?php
/**
 * اختبار بسيط لـ API البحث عن العملاء
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 اختبار API البحث - مبسط</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص العملاء
    echo "<h2>1. العملاء الموجودين</h2>";
    
    $customers_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
    echo "<p>عدد العملاء: <strong>$customers_count</strong></p>";
    
    if ($customers_count == 0) {
        echo "<p style='color: orange;'>⚠️ لا يوجد عملاء - سيتم إضافة عملاء تجريبيين</p>";
        
        // إضافة عملاء تجريبيين
        $sample_customers = [
            ['أحمد محمد', '01234567890', '<EMAIL>'],
            ['فاطمة علي', '01987654321', '<EMAIL>'],
            ['محمد حسن', '01122334455', '<EMAIL>']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, ?)");
        
        foreach ($sample_customers as $customer) {
            try {
                $insert_stmt->execute([$customer[0], $customer[1], $customer[2], 1]);
                echo "<p style='color: green;'>✅ تم إضافة: {$customer[0]}</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة {$customer[0]}: " . $e->getMessage() . "</p>";
            }
        }
        
        $customers_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
        echo "<p>عدد العملاء بعد الإضافة: <strong>$customers_count</strong></p>";
    }
    
    // 2. اختبار البحث المباشر
    echo "<h2>2. اختبار البحث المباشر</h2>";
    
    $search_query = 'أحمد';
    $search_term = "%{$search_query}%";
    
    try {
        $search_stmt = $pdo->prepare("
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = 1
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC
            LIMIT 5
        ");
        
        $search_stmt->execute([$search_term, $search_term, $search_term]);
        $results = $search_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>البحث عن: <strong>$search_query</strong></p>";
        echo "<p>عدد النتائج: <strong>" . count($results) . "</strong></p>";
        
        if (count($results) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم</th>";
            echo "<th style='padding: 8px;'>الهاتف</th>";
            echo "</tr>";
            
            foreach ($results as $result) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $result['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($result['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($result['phone']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في البحث: " . $e->getMessage() . "</p>";
    }
    
    // 3. محاكاة API
    echo "<h2>3. محاكاة API</h2>";
    
    try {
        // محاكاة طلب API
        $api_query = 'أحمد';
        $api_search_term = "%{$api_query}%";
        
        // نفس الكود المستخدم في API
        $api_stmt = $pdo->prepare("
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = ?
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC
            LIMIT 10
        ");
        
        $api_stmt->execute([1, $api_search_term, $api_search_term, $api_search_term]);
        $api_results = $api_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنظيف البيانات
        foreach ($api_results as &$customer) {
            $customer['name'] = htmlspecialchars($customer['name'] ?? '');
            $customer['phone'] = htmlspecialchars($customer['phone'] ?? '');
            $customer['email'] = htmlspecialchars($customer['email'] ?? '');
        }
        
        echo "<p>استعلام API للبحث عن: <strong>$api_query</strong></p>";
        echo "<p>عدد النتائج: <strong>" . count($api_results) . "</strong></p>";
        
        if (count($api_results) > 0) {
            echo "<h4>نتائج API (JSON):</h4>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; direction: ltr;'>";
            echo json_encode($api_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            echo "</pre>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في محاكاة API: " . $e->getMessage() . "</p>";
    }
    
    // 4. اختبار تفاعلي
    echo "<h2>4. اختبار تفاعلي</h2>";
    
    echo '<div style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">';
    echo '<h4>اختبار البحث المباشر:</h4>';
    echo '<input type="text" id="liveSearch" placeholder="ابحث عن عميل..." style="width: 300px; padding: 10px; margin: 10px 0;">';
    echo '<button onclick="testSearch()" style="padding: 10px 20px; margin: 10px;">بحث</button>';
    echo '<div id="liveResults" style="border: 1px solid #ccc; min-height: 100px; padding: 10px; background: #f9f9f9; margin: 10px 0;"></div>';
    echo '</div>';
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/api/search-customers.php?q=أحمد' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>اختبار API مباشرة</a>";
echo "<a href='client/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>

<script>
function testSearch() {
    const query = document.getElementById('liveSearch').value.trim();
    const resultsDiv = document.getElementById('liveResults');
    
    if (query.length < 2) {
        resultsDiv.innerHTML = '<p style="color: #666;">اكتب حرفين على الأقل للبحث</p>';
        return;
    }
    
    resultsDiv.innerHTML = '<p style="color: #007bff;"><i>جاري البحث...</i></p>';
    
    fetch(`client/api/search-customers.php?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            
            try {
                const data = JSON.parse(text);
                
                if (Array.isArray(data) && data.length > 0) {
                    let html = '<h5 style="color: green;">✅ تم العثور على ' + data.length + ' نتيجة:</h5>';
                    html += '<ul>';
                    data.forEach(customer => {
                        html += `<li><strong>${customer.name}</strong> - ${customer.phone || 'لا يوجد هاتف'}</li>`;
                    });
                    html += '</ul>';
                    resultsDiv.innerHTML = html;
                } else if (Array.isArray(data)) {
                    resultsDiv.innerHTML = '<p style="color: orange;">⚠️ لم يتم العثور على نتائج</p>';
                } else {
                    resultsDiv.innerHTML = '<p style="color: red;">❌ استجابة غير متوقعة: ' + JSON.stringify(data) + '</p>';
                }
            } catch (error) {
                resultsDiv.innerHTML = '<p style="color: red;">❌ خطأ في تحليل JSON:<br>' + error.message + '<br><br>الاستجابة الخام:<br><pre>' + text + '</pre></p>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<p style="color: red;">❌ خطأ في الطلب: ' + error.message + '</p>';
        });
}

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('liveSearch');
    searchInput.value = 'أحمد';
    
    setTimeout(() => {
        testSearch();
    }, 1000);
});
</script>
