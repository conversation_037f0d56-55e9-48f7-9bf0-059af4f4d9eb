<?php
if (!defined("AUTH_GUARD")) define("AUTH_GUARD", true);

function protectAdminPage($pdo) {
    if (session_status() === PHP_SESSION_NONE) session_start();
    if (!isset($_SESSION["admin_id"])) {
        header("Location: login.php");
        exit;
    }
    return true;
}

function protectClientPage($pdo) {
    if (session_status() === PHP_SESSION_NONE) session_start();
    if (!isset($_SESSION["client_id"])) {
        header("Location: login.php");
        exit;
    }
    return true;
}

function protectEmployeePage($pdo) {
    if (session_status() === PHP_SESSION_NONE) session_start();
    if (!isset($_SESSION["employee_id"])) {
        header("Location: employee-login.php");
        exit;
    }
    return true;
}

function protectApiEndpoint($pdo, $user_type = "client") {
    if (session_status() === PHP_SESSION_NONE) session_start();
    $valid = false;
    switch ($user_type) {
        case "admin": $valid = isset($_SESSION["admin_id"]); break;
        case "client": $valid = isset($_SESSION["client_id"]); break;
        case "employee": $valid = isset($_SESSION["employee_id"]); break;
    }
    if (!$valid) {
        http_response_code(401);
        header("Content-Type: application/json");
        echo json_encode(["success" => false, "error" => "غير مصرح"]);
        exit;
    }
    return true;
}

function secureLogout($pdo, $url = null) {
    if (session_status() === PHP_SESSION_NONE) session_start();
    session_destroy();
    if ($url) {
        header("Location: $url");
        exit;
    }
}

function createSecureUserSession($pdo, $user_id, $user_type) { return true; }
function destroySecureUserSession($pdo) { return true; }
function addSecurityHeaders() {
    header("X-Frame-Options: DENY");
    header("X-Content-Type-Options: nosniff");
}
addSecurityHeaders();
