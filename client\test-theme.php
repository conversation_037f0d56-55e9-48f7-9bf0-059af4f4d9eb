<?php
/**
 * صفحة اختبار نظام تخصيص المظهر
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين جلسة تجريبية إذا لم تكن موجودة
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
    $_SESSION['client_name'] = 'مركز الألعاب التجريبي';
    $_SESSION['owner_name'] = 'المالك التجريبي';
}

require_once '../config/database.php';

$page_title = "اختبار نظام المظهر";
$active_page = "test";

// إنشاء جدول إعدادات المظهر إذا لم يكن موجوداً
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS client_theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            primary_color VARCHAR(7) DEFAULT '#0d6efd',
            secondary_color VARCHAR(7) DEFAULT '#6c757d',
            accent_color VARCHAR(7) DEFAULT '#20c997',
            header_style ENUM('top', 'sidebar') DEFAULT 'top',
            sidebar_position ENUM('right', 'left') DEFAULT 'right',
            theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id)
        )
    ");
    
    // إدراج إعدادات تجريبية
    $stmt = $pdo->prepare("
        INSERT INTO client_theme_settings 
        (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        primary_color = VALUES(primary_color),
        secondary_color = VALUES(secondary_color),
        accent_color = VALUES(accent_color),
        header_style = VALUES(header_style),
        sidebar_position = VALUES(sidebar_position),
        theme_mode = VALUES(theme_mode),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    $stmt->execute([
        $_SESSION['client_id'], 
        '#dc3545', // أحمر
        '#6c757d', // رمادي
        '#fd7e14', // برتقالي
        'top', 
        'right', 
        'light'
    ]);
    
} catch (Exception $e) {
    // تجاهل الأخطاء
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-palette me-2"></i>اختبار نظام تخصيص المظهر
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه صفحة اختبار لنظام تخصيص المظهر. يجب أن ترى الألوان المخصصة مطبقة على العناصر أدناه.
                    </div>

                    <!-- اختبار الأزرار -->
                    <div class="mb-4">
                        <h5>اختبار الأزرار:</h5>
                        <button class="btn btn-primary me-2">زر أساسي</button>
                        <button class="btn btn-success me-2">زر نجاح</button>
                        <button class="btn btn-secondary me-2">زر ثانوي</button>
                        <button class="btn btn-success-gradient me-2">زر متدرج</button>
                    </div>

                    <!-- اختبار البطاقات -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary">150</h4>
                                    <p class="text-muted">العملاء</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-gamepad fa-2x text-success mb-2"></i>
                                    <h4 class="text-success">25</h4>
                                    <p class="text-muted">الجلسات النشطة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card stats-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning">1,250 ج.م</h4>
                                    <p class="text-muted">الإيرادات اليوم</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختبار النماذج -->
                    <div class="mb-4">
                        <h5>اختبار النماذج:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="testInput" placeholder="اختبار">
                                    <label for="testInput">حقل اختبار</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="testSelect">
                                        <option selected>اختر خيار</option>
                                        <option value="1">خيار 1</option>
                                        <option value="2">خيار 2</option>
                                    </select>
                                    <label for="testSelect">قائمة اختبار</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختبار التبويبات -->
                    <div class="mb-4">
                        <h5>اختبار التبويبات:</h5>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab">
                                    الرئيسية
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab">
                                    الملف الشخصي
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab">
                                    اتصل بنا
                                </button>
                            </li>
                        </ul>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel">
                                <p>محتوى تبويب الرئيسية</p>
                            </div>
                            <div class="tab-pane fade" id="pills-profile" role="tabpanel">
                                <p>محتوى تبويب الملف الشخصي</p>
                            </div>
                            <div class="tab-pane fade" id="pills-contact" role="tabpanel">
                                <p>محتوى تبويب اتصل بنا</p>
                            </div>
                        </div>
                    </div>

                    <!-- اختبار الجدول -->
                    <div class="mb-4">
                        <h5>اختبار الجدول:</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>السعر</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>جهاز PS5</td>
                                        <td>PlayStation 5</td>
                                        <td><span class="badge bg-success">متاح</span></td>
                                        <td>30 ج.م/ساعة</td>
                                    </tr>
                                    <tr>
                                        <td>جهاز PS4</td>
                                        <td>PlayStation 4</td>
                                        <td><span class="badge bg-primary">مشغول</span></td>
                                        <td>20 ج.م/ساعة</td>
                                    </tr>
                                    <tr>
                                        <td>جهاز PC</td>
                                        <td>PC Gaming</td>
                                        <td><span class="badge bg-warning">صيانة</span></td>
                                        <td>25 ج.م/ساعة</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- اختبار شريط التقدم -->
                    <div class="mb-4">
                        <h5>اختبار شريط التقدم:</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                        </div>
                    </div>

                    <!-- روابط الاختبار -->
                    <div class="text-center">
                        <a href="settings.php" class="btn btn-primary me-2">
                            <i class="fas fa-cog me-1"></i>انتقل لصفحة الإعدادات
                        </a>
                        <a href="dashboard.php" class="btn btn-success">
                            <i class="fas fa-home me-1"></i>العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// اختبار تحميل CSS المخصص
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 اختبار نظام المظهر');
    
    // التحقق من تحميل CSS المخصص
    const customCSS = document.querySelector('link[href*="theme-css.php"]');
    if (customCSS) {
        console.log('✅ تم تحميل CSS المخصص:', customCSS.href);
    } else {
        console.log('❌ لم يتم تحميل CSS المخصص');
    }
    
    // التحقق من المتغيرات المخصصة
    const rootStyles = getComputedStyle(document.documentElement);
    const primaryColor = rootStyles.getPropertyValue('--custom-primary');
    
    if (primaryColor) {
        console.log('✅ المتغيرات المخصصة متاحة. اللون الأساسي:', primaryColor);
    } else {
        console.log('❌ المتغيرات المخصصة غير متاحة');
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
