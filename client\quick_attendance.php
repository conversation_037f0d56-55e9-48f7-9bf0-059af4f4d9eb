<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once '../includes/shift_helpers.php';

// التحقق من تسجيل الدخول كموظف فقط
if (!isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

$employee_id = $_SESSION['employee_id'];
$client_id = $_SESSION['client_id'];

$page_title = "تسجيل الحضور السريع";
$active_page = "quick_attendance";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'check_in':
                    // البحث عن الوردية النشطة للموظف
                    $stmt = $pdo->prepare("
                        SELECT es.assignment_id, es.shift_id, s.shift_name
                        FROM employee_shifts es
                        JOIN shifts s ON es.shift_id = s.shift_id
                        WHERE es.employee_id = ? AND s.shift_date = CURDATE() 
                        AND s.status IN ('scheduled', 'active')
                        AND NOT EXISTS (
                            SELECT 1 FROM shift_attendance sa 
                            WHERE sa.assignment_id = es.assignment_id 
                            AND sa.check_in_time IS NOT NULL
                        )
                        ORDER BY s.start_time ASC
                        LIMIT 1
                    ");
                    $stmt->execute([$employee_id]);
                    $assignment = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($assignment) {
                        // إنشاء سجل حضور جديد
                        $stmt = $pdo->prepare("
                            INSERT INTO shift_attendance 
                            (assignment_id, shift_id, employee_id, check_in_time, status, recorded_by)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'present', ?)
                        ");
                        $stmt->execute([
                            $assignment['assignment_id'],
                            $assignment['shift_id'],
                            $employee_id,
                            $employee_id
                        ]);
                        
                        // تحديث حالة الوردية إلى نشطة
                        $stmt = $pdo->prepare("UPDATE shifts SET status = 'active' WHERE shift_id = ?");
                        $stmt->execute([$assignment['shift_id']]);
                        
                        $_SESSION['success'] = "تم تسجيل الحضور بنجاح للوردية: " . $assignment['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا توجد وردية مجدولة اليوم أو تم تسجيل الحضور مسبقاً";
                    }
                    break;

                case 'check_out':
                    // البحث عن سجل الحضور النشط
                    $stmt = $pdo->prepare("
                        SELECT sa.attendance_id, sa.shift_id, s.shift_name
                        FROM shift_attendance sa
                        JOIN shifts s ON sa.shift_id = s.shift_id
                        WHERE sa.employee_id = ? AND sa.check_in_time IS NOT NULL 
                        AND sa.check_out_time IS NULL
                        ORDER BY sa.check_in_time DESC
                        LIMIT 1
                    ");
                    $stmt->execute([$employee_id]);
                    $attendance = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($attendance) {
                        // تسجيل الانصراف
                        $stmt = $pdo->prepare("
                            UPDATE shift_attendance 
                            SET check_out_time = CURRENT_TIMESTAMP, recorded_by = ?
                            WHERE attendance_id = ?
                        ");
                        $stmt->execute([$employee_id, $attendance['attendance_id']]);
                        
                        // حساب ساعات العمل باستخدام الدالة المساعدة
                        $calculation_result = calculateAttendanceHours($pdo, $attendance['attendance_id']);
                        if (isset($calculation_result['error'])) {
                            error_log('خطأ في حساب ساعات العمل: ' . $calculation_result['error']);
                        }
                        
                        $_SESSION['success'] = "تم تسجيل الانصراف بنجاح من الوردية: " . $attendance['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا يوجد سجل حضور نشط";
                    }
                    break;

                case 'start_break':
                    $stmt = $pdo->prepare("
                        UPDATE shift_attendance 
                        SET break_start_time = CURRENT_TIMESTAMP
                        WHERE employee_id = ? AND check_in_time IS NOT NULL 
                        AND check_out_time IS NULL AND break_start_time IS NULL
                    ");
                    $stmt->execute([$employee_id]);
                    
                    if ($stmt->rowCount() > 0) {
                        $_SESSION['success'] = "تم بدء الاستراحة";
                    } else {
                        $_SESSION['error'] = "لا يمكن بدء الاستراحة";
                    }
                    break;

                case 'end_break':
                    $stmt = $pdo->prepare("
                        UPDATE shift_attendance 
                        SET break_end_time = CURRENT_TIMESTAMP
                        WHERE employee_id = ? AND break_start_time IS NOT NULL 
                        AND break_end_time IS NULL
                    ");
                    $stmt->execute([$employee_id]);
                    
                    if ($stmt->rowCount() > 0) {
                        $_SESSION['success'] = "تم انتهاء الاستراحة";
                    } else {
                        $_SESSION['error'] = "لا يمكن إنهاء الاستراحة";
                    }
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: quick_attendance.php');
    exit;
}

// جلب حالة الحضور الحالية
$attendance_status = isEmployeeCheckedIn($pdo, $employee_id);

// جلب معلومات الموظف
$employee_stmt = $pdo->prepare("SELECT name, role FROM employees WHERE id = ?");
$employee_stmt->execute([$employee_id]);
$employee_info = $employee_stmt->fetch(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-check me-2"></i><?php echo $page_title; ?>
                    </h4>
                    <p class="mb-0">مرحباً، <?php echo htmlspecialchars($employee_info['name']); ?></p>
                </div>
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- حالة الحضور الحالية -->
                    <div class="alert <?php echo $attendance_status['is_checked_in'] ? 'alert-success' : 'alert-warning'; ?> text-center">
                        <h5>
                            <i class="fas fa-info-circle me-2"></i>حالة الحضور الحالية
                        </h5>
                        <p class="mb-0"><?php echo $attendance_status['message']; ?></p>
                        
                        <?php if ($attendance_status['is_checked_in']): ?>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <strong>الوردية:</strong><br>
                                    <?php echo htmlspecialchars($attendance_status['shift_name']); ?>
                                </div>
                                <div class="col-6">
                                    <strong>وقت الحضور:</strong><br>
                                    <?php echo date('H:i', strtotime($attendance_status['check_in_time'])); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="d-grid gap-3">
                        <?php if (!$attendance_status['is_checked_in']): ?>
                            <!-- زر تسجيل الحضور -->
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="check_in">
                                <button type="submit" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الحضور
                                </button>
                            </form>
                        <?php else: ?>
                            <!-- أزرار الاستراحة والانصراف -->
                            <?php if ($attendance_status['status'] == 'checked_in'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="start_break">
                                    <button type="submit" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-coffee me-2"></i>بدء الاستراحة
                                    </button>
                                </form>
                            <?php elseif ($attendance_status['status'] == 'on_break'): ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-coffee me-2"></i>أنت في استراحة منذ 
                                    <?php echo date('H:i', strtotime($attendance_status['break_start_time'])); ?>
                                </div>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="end_break">
                                    <button type="submit" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-play me-2"></i>انتهاء الاستراحة
                                    </button>
                                </form>
                            <?php endif; ?>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="check_out">
                                <button type="submit" class="btn btn-danger btn-lg w-100" 
                                        onclick="return confirm('هل أنت متأكد من تسجيل الانصراف؟')">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الانصراف
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>

                    <!-- روابط سريعة -->
                    <hr>
                    <div class="text-center">
                        <h6>روابط سريعة</h6>
                        <div class="btn-group" role="group">
                            <a href="sessions.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-gamepad me-1"></i>الجلسات
                            </a>
                            <a href="attendance.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-chart-line me-1"></i>تفاصيل الحضور
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center text-muted">
                    <small>
                        <i class="fas fa-clock me-1"></i>
                        آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الصفحة كل 30 ثانية
setTimeout(function() {
    location.reload();
}, 30000);

// إضافة الوقت الحالي في العنوان
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG');
    document.title = `${timeString} - تسجيل الحضور السريع`;
}

setInterval(updateCurrentTime, 1000);
updateCurrentTime();
</script>

<?php include 'includes/footer.php'; ?>
