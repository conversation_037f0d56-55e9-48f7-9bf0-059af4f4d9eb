<?php
class Client {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * إضافة عميل جديد
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO clients (owner_name, phone, email, address, is_active, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $data['owner_name'],
                $data['phone'],
                $data['email'] ?? null,
                $data['address'] ?? null,
                $data['is_active'] ?? 1
            ]);
            
            if ($result) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة العميل بنجاح',
                    'client_id' => $this->pdo->lastInsertId()
                ];
            }
            
            return ['success' => false, 'message' => 'فشل في إضافة العميل'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات العميل
     */
    public function update($client_id, $data) {
        try {
            $sql = "UPDATE clients SET name = ?, phone = ?, email = ?, address = ?, is_active = ?, updated_at = NOW() WHERE client_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $data['owner_name'],
                $data['phone'],
                $data['email'] ?? null,
                $data['address'] ?? null,
                $data['is_active'] ?? 1,
                $client_id
            ]);
            
            if ($result && $stmt->rowCount() > 0) {
                return ['success' => true, 'message' => 'تم تحديث بيانات العميل بنجاح'];
            }
            
            return ['success' => false, 'message' => 'لم يتم العثور على العميل أو لم تتغير البيانات'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * حذف عميل
     */
    public function delete($client_id) {
        try {
            // التحقق من وجود حجوزات للعميل
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM rentals WHERE client_id = ?");
            $stmt->execute([$client_id]);
            $rental_count = $stmt->fetch()['count'];
            
            if ($rental_count > 0) {
                return ['success' => false, 'message' => 'لا يمكن حذف العميل لوجود حجوزات مرتبطة به'];
            }
            
            $sql = "DELETE FROM clients WHERE client_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$client_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                return ['success' => true, 'message' => 'تم حذف العميل بنجاح'];
            }
            
            return ['success' => false, 'message' => 'لم يتم العثور على العميل'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * تغيير حالة العميل (نشط/غير نشط)
     */
    public function toggleStatus($client_id) {
        try {
            $sql = "UPDATE clients SET is_active = NOT is_active, updated_at = NOW() WHERE client_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$client_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                return ['success' => true, 'message' => 'تم تغيير حالة العميل بنجاح'];
            }
            
            return ['success' => false, 'message' => 'لم يتم العثور على العميل'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * البحث عن العملاء مع الفلترة والتصفح
     */
    public function search($search = '', $status_filter = 'all', $page = 1, $per_page = 10) {
        try {
            $offset = ($page - 1) * $per_page;
            $where_conditions = [];
            $params = [];
            
            if (!empty($search)) {
                $where_conditions[] = "(name LIKE ? OR phone LIKE ? OR email LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            if ($status_filter !== 'all') {
                $where_conditions[] = "is_active = ?";
                $params[] = $status_filter == 'active' ? 1 : 0;
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            // عدد العملاء الإجمالي
            $count_sql = "SELECT COUNT(*) as total FROM clients $where_clause";
            $count_stmt = $this->pdo->prepare($count_sql);
            $count_stmt->execute($params);
            $total = $count_stmt->fetch()['total'];
            
            // جلب العملاء
            $sql = "SELECT * FROM clients $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $clients = $stmt->fetchAll();
            
            return [
                'success' => true,
                'clients' => $clients,
                'total' => $total,
                'total_pages' => ceil($total / $per_page),
                'current_page' => $page
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في البحث: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على عميل بواسطة client_id
     */
    public function getById($client_id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
            $stmt->execute([$client_id]);
            $client = $stmt->fetch();
            
            if ($client) {
                return ['success' => true, 'client' => $client];
            }
            
            return ['success' => false, 'message' => 'لم يتم العثور على العميل'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على جميع العملاء النشطين
     */
    public function getActiveClients() {
        try {
            $stmt = $this->pdo->query("SELECT client_id, name, phone FROM clients WHERE is_active = 1 ORDER BY owner_name");
            return ['success' => true, 'clients' => $stmt->fetchAll()];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
        }
    }
    
    /**
     * التحقق من وجود رقم الهاتف
     */
    public function phoneExists($phone, $exclude_client_id = null) {
        try {
            $sql = "SELECT COUNT(*) as count FROM clients WHERE phone = ?";
            $params = [$phone];
            
            if ($exclude_client_id) {
                $sql .= " AND client_id != ?";
                $params[] = $exclude_client_id;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch()['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     */
    public function emailExists($email, $exclude_client_id = null) {
        try {
            if (empty($email)) return false;
            
            $sql = "SELECT COUNT(*) as count FROM clients WHERE email = ?";
            $params = [$email];
            
            if ($exclude_client_id) {
                $sql .= " AND client_id != ?";
                $params[] = $exclude_client_id;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch()['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات العملاء
     */
    public function getStats() {
        try {
            $stats = [];
            
            // إجمالي العملاء
            $stmt = $this->pdo->query("SELECT COUNT(*) as total_clients FROM clients");
            $stats['total_clients'] = $stmt->fetch()['total_clients'];
            
            // العملاء النشطين
            $stmt = $this->pdo->query("SELECT COUNT(*) as active_clients FROM clients WHERE is_active = 1");
            $stats['active_clients'] = $stmt->fetch()['active_clients'];
            
            // العملاء الجدد هذا الشهر
            $stmt = $this->pdo->query("SELECT COUNT(*) as new_clients FROM clients WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
            $stats['new_clients'] = $stmt->fetch()['new_clients'];
            
            // العملاء الجدد اليوم
            $stmt = $this->pdo->query("SELECT COUNT(*) as today_clients FROM clients WHERE DATE(created_at) = CURDATE()");
            $stats['today_clients'] = $stmt->fetch()['today_clients'];
            
            return ['success' => true, 'stats' => $stats];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()];
        }
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public function validateData($data, $client_id = null) {
        $errors = [];
        
        // التحقق من الاسم
        if (empty($data['owner_name']) || strlen(trim($data['owner_name'])) < 2) {
            $errors[] = 'اسم العميل مطلوب ويجب أن يكون على الأقل حرفين';
        }
        
        // التحقق من رقم الهاتف
        if (empty($data['phone'])) {
            $errors[] = 'رقم الهاتف مطلوب';
        } elseif (!preg_match('/^[0-9+\-\s()]+$/', $data['phone'])) {
            $errors[] = 'رقم الهاتف غير صحيح';
        } elseif ($this->phoneExists($data['phone'], $client_id)) {
            $errors[] = 'رقم الهاتف مسجل مسبقاً لعميل آخر';
        }
        
        // التحقق من البريد الإلكتروني
        if (!empty($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'البريد الإلكتروني غير صحيح';
            } elseif ($this->emailExists($data['email'], $client_id)) {
                $errors[] = 'البريد الإلكتروني مسجل مسبقاً لعميل آخر';
            }
        }
        
        return $errors;
    }
}