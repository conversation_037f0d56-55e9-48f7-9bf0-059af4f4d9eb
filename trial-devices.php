<?php
session_start();
require_once 'includes/trial-auth.php';

// التحقق من تسجيل الدخول
requireTrialLogin();

$trial_id = $_SESSION['trial_id'];
$business_name = $_SESSION['trial_business_name'];

// الحصول على الوقت المتبقي
$timeRemaining = getTrialTimeRemaining($trial_id);

// الحصول على قائمة الأجهزة
try {
    $stmt = $pdo->prepare("
        SELECT * FROM trial_devices 
        WHERE trial_id = ? 
        ORDER BY device_name
    ");
    $stmt->execute([$trial_id]);
    $devices = $stmt->fetchAll();
} catch (PDOException $e) {
    $devices = [];
    $error = 'حدث خطأ في جلب بيانات الأجهزة';
}

// معالجة إضافة جهاز جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_device'])) {
    $device_name = trim($_POST['device_name'] ?? '');
    $device_type = $_POST['device_type'] ?? '';
    $hourly_rate = floatval($_POST['hourly_rate'] ?? 0);
    
    if (!empty($device_name) && !empty($device_type) && $hourly_rate > 0) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO trial_devices (trial_id, device_name, device_type, hourly_rate) 
                VALUES (?, ?, ?, ?)
            ");
            if ($stmt->execute([$trial_id, $device_name, $device_type, $hourly_rate])) {
                $success = 'تم إضافة الجهاز بنجاح!';
                // إعادة تحميل الصفحة لعرض الجهاز الجديد
                header('Location: trial-devices.php?success=1');
                exit;
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الجهاز';
        }
    } else {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    }
}

// معالجة تحديث حالة الجهاز
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $device_id = intval($_POST['device_id'] ?? 0);
    $new_status = $_POST['status'] ?? '';
    
    if ($device_id > 0 && in_array($new_status, ['available', 'occupied', 'maintenance'])) {
        try {
            $stmt = $pdo->prepare("
                UPDATE trial_devices 
                SET status = ? 
                WHERE device_id = ? AND trial_id = ?
            ");
            if ($stmt->execute([$new_status, $device_id, $trial_id])) {
                $success = 'تم تحديث حالة الجهاز بنجاح!';
                header('Location: trial-devices.php?success=2');
                exit;
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تحديث حالة الجهاز';
        }
    }
}

if (isset($_GET['success'])) {
    if ($_GET['success'] == '1') {
        $success = 'تم إضافة الجهاز بنجاح!';
    } elseif ($_GET['success'] == '2') {
        $success = 'تم تحديث حالة الجهاز بنجاح!';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأجهزة - التجربة المجانية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            --danger-gradient: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .trial-navbar {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .trial-timer {
            background: var(--warning-gradient);
            color: #2d3748;
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .trial-timer.danger {
            background: var(--danger-gradient);
            color: white;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .trial-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 1rem;
        }

        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .page-header h2 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }

        .device-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-5px);
        }

        .device-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .device-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2d3748;
        }

        .device-type {
            background: var(--primary-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .device-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            border: none;
        }

        .status-available {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .status-occupied {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }

        .status-maintenance {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }

        .device-rate {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }

        .add-device-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .device-card {
                padding: 1rem;
            }
            
            .add-device-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الوقت المتبقي -->
    <div class="trial-timer <?php echo $timeRemaining['total_seconds'] < 1800 ? 'danger' : ''; ?>" id="trialTimer">
        <i class="fas fa-clock me-2"></i>
        الوقت المتبقي للتجربة: <span id="timeDisplay"><?php echo $timeRemaining['hours']; ?>:<?php echo sprintf('%02d', $timeRemaining['minutes']); ?>:<?php echo sprintf('%02d', $timeRemaining['seconds']); ?></span>
    </div>

    <!-- شريط التنقل -->
    <nav class="trial-navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <span class="navbar-brand">
                        <i class="fas fa-gamepad me-2"></i>
                        PlayGood
                    </span>
                    <span class="trial-badge">تجربة مجانية</span>
                </div>
                <div class="d-flex align-items-center">
                    <a href="trial-dashboard.php" class="back-btn me-3">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="trial-logout.php" class="back-btn">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="trial-dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">إدارة الأجهزة</li>
                </ol>
            </nav>
            <h2>
                <i class="fas fa-desktop me-2 text-primary"></i>
                إدارة الأجهزة
            </h2>
            <p class="text-muted mb-0">إدارة أجهزة الألعاب في <?php echo htmlspecialchars($business_name); ?></p>
        </div>

        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إضافة جهاز جديد -->
        <div class="add-device-card">
            <h4 class="mb-3">
                <i class="fas fa-plus me-2"></i>
                إضافة جهاز جديد
            </h4>
            <form method="POST">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="device_name" class="form-label">اسم الجهاز</label>
                        <input type="text" class="form-control" id="device_name" name="device_name" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="device_type" class="form-label">نوع الجهاز</label>
                        <select class="form-control" id="device_type" name="device_type" required>
                            <option value="">اختر النوع</option>
                            <option value="PS4">PlayStation 4</option>
                            <option value="PS5">PlayStation 5</option>
                            <option value="Xbox">Xbox</option>
                            <option value="PC">PC</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="hourly_rate" class="form-label">السعر بالساعة (جنيه)</label>
                        <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" step="0.01" min="0" required>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" name="add_device" class="btn btn-light w-100">
                            <i class="fas fa-plus me-1"></i>
                            إضافة
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- قائمة الأجهزة -->
        <div class="row">
            <?php if (empty($devices)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-desktop fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أجهزة مضافة بعد</h5>
                        <p class="text-muted">ابدأ بإضافة أول جهاز باستخدام النموذج أعلاه</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($devices as $device): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="device-card">
                            <div class="device-header">
                                <div>
                                    <div class="device-name"><?php echo htmlspecialchars($device['device_name']); ?></div>
                                    <span class="device-type"><?php echo htmlspecialchars($device['device_type']); ?></span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="device-rate">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    <?php echo number_format($device['hourly_rate'], 2); ?> جنيه/ساعة
                                </div>
                            </div>

                            <form method="POST" class="d-flex align-items-center gap-2">
                                <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                <select name="status" class="form-control form-control-sm device-status status-<?php echo $device['status']; ?>" onchange="this.form.submit()">
                                    <option value="available" <?php echo $device['status'] === 'available' ? 'selected' : ''; ?>>متاح</option>
                                    <option value="occupied" <?php echo $device['status'] === 'occupied' ? 'selected' : ''; ?>>مشغول</option>
                                    <option value="maintenance" <?php echo $device['status'] === 'maintenance' ? 'selected' : ''; ?>>صيانة</option>
                                </select>
                                <input type="hidden" name="update_status" value="1">
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عداد الوقت المتبقي
        let totalSeconds = <?php echo $timeRemaining['total_seconds']; ?>;
        
        function updateTimer() {
            if (totalSeconds <= 0) {
                alert('انتهت صلاحية التجربة المجانية!');
                window.location.href = 'trial-login.php?error=trial_expired';
                return;
            }
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            document.getElementById('timeDisplay').textContent = 
                hours + ':' + 
                (minutes < 10 ? '0' : '') + minutes + ':' + 
                (seconds < 10 ? '0' : '') + seconds;
            
            const timerElement = document.getElementById('trialTimer');
            if (totalSeconds < 1800) {
                timerElement.classList.add('danger');
            }
            
            totalSeconds--;
        }
        
        setInterval(updateTimer, 1000);
    </script>
</body>
</html>
