<?php
/**
 * إضافة خيار "ساعة كاملة من أول دقيقة" لطرق حساب التكلفة
 */

require_once 'config/database.php';

echo "<h1>إضافة خيار ساعة كاملة من أول دقيقة</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // التحقق من العمود الحالي
    echo "<h3>🔍 فحص العمود الحالي</h3>";
    
    $column_info = $pdo->query("SHOW COLUMNS FROM invoice_settings WHERE Field = 'billing_method'")->fetch(PDO::FETCH_ASSOC);
    
    if ($column_info) {
        echo "<p><strong>نوع العمود الحالي:</strong> " . htmlspecialchars($column_info['Type']) . "</p>";
        echo "<p><strong>القيمة الافتراضية:</strong> " . htmlspecialchars($column_info['Default'] ?? 'NULL') . "</p>";
        
        // التحقق من وجود القيمة الجديدة
        if (strpos($column_info['Type'], 'first_minute_full_hour') !== false) {
            echo "<p style='color: green;'>✅ الخيار الجديد موجود بالفعل</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ الخيار الجديد غير موجود. سيتم إضافته.</p>";
            
            // تعديل العمود لإضافة الخيار الجديد
            $alter_sql = "
                ALTER TABLE invoice_settings 
                MODIFY COLUMN billing_method ENUM(
                    'actual_time', 
                    'hourly_rounding', 
                    'first_minute_full_hour'
                ) DEFAULT 'actual_time' 
                COMMENT 'طريقة حساب التكلفة: actual_time = الوقت الفعلي, hourly_rounding = تقريب للساعة الكاملة, first_minute_full_hour = ساعة كاملة من أول دقيقة'
            ";
            
            $pdo->exec($alter_sql);
            echo "<p style='color: green;'>✅ تم إضافة الخيار الجديد بنجاح</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ العمود billing_method غير موجود</p>";
        exit;
    }
    
    // عرض العمود المحدث
    echo "<h3>📋 العمود المحدث</h3>";
    
    $updated_column = $pdo->query("SHOW COLUMNS FROM invoice_settings WHERE Field = 'billing_method'")->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p><strong>نوع العمود:</strong> " . htmlspecialchars($updated_column['Type']) . "</p>";
    echo "<p><strong>القيمة الافتراضية:</strong> " . htmlspecialchars($updated_column['Default']) . "</p>";
    echo "<p><strong>التعليق:</strong> " . htmlspecialchars($updated_column['Comment']) . "</p>";
    echo "</div>";
    
    // شرح الخيارات الثلاثة
    echo "<h3>📖 شرح طرق الحساب الثلاثة</h3>";
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    // الخيار الأول: الوقت الفعلي
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #17a2b8;'>";
    echo "<h4 style='color: #0c5460; margin-top: 0;'>⏱️ actual_time</h4>";
    echo "<p><strong>الوقت الفعلي</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>حساب دقيق بناءً على الدقائق الفعلية</li>";
    echo "<li>مثال: 45 دقيقة = 0.75 ساعة</li>";
    echo "<li>الأكثر عدالة للعملاء</li>";
    echo "</ul>";
    echo "</div>";
    
    // الخيار الثاني: تقريب الساعة
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; border-left: 5px solid #ffc107;'>";
    echo "<h4 style='color: #856404; margin-top: 0;'>🕐 hourly_rounding</h4>";
    echo "<p><strong>تقريب الساعة</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>تقريب أي جزء من الساعة للساعة الكاملة</li>";
    echo "<li>مثال: 45 دقيقة = ساعة كاملة</li>";
    echo "<li>النظام التقليدي</li>";
    echo "</ul>";
    echo "</div>";
    
    // الخيار الثالث: ساعة كاملة من أول دقيقة
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; border-left: 5px solid #dc3545;'>";
    echo "<h4 style='color: #721c24; margin-top: 0;'>⚡ first_minute_full_hour</h4>";
    echo "<p><strong>ساعة كاملة من أول دقيقة</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>بمجرد بدء الجلسة يحسب ساعة كاملة</li>";
    echo "<li>مثال: دقيقة واحدة = ساعة كاملة</li>";
    echo "<li>للمحلات التي تطبق هذا النظام</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    // مقارنة الأسعار
    echo "<h3>💰 مقارنة الأسعار (بافتراض 20 ج.م/ساعة)</h3>";
    
    $test_durations = [1, 5, 15, 30, 45, 60, 75, 90];
    $hourly_rate = 20;
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);'>";
    echo "<thead>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>المدة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>الوقت الفعلي</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>تقريب الساعة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd;'>ساعة من أول دقيقة</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($test_durations as $duration) {
        // حساب التكلفة بالطرق الثلاثة
        $actual_cost = ($duration / 60) * $hourly_rate;
        $rounded_cost = ceil($duration / 60) * $hourly_rate;
        $first_minute_cost = $duration > 0 ? $hourly_rate : 0; // ساعة كاملة من أول دقيقة
        
        echo "<tr style='background: " . ($duration % 2 == 0 ? '#f8f9fa' : 'white') . ";'>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; font-weight: bold;'>";
        
        if ($duration < 60) {
            echo "$duration دقيقة";
        } else {
            $hours = floor($duration / 60);
            $minutes = $duration % 60;
            if ($minutes > 0) {
                echo "$hours ساعة و $minutes دقيقة";
            } else {
                echo "$hours ساعة";
            }
        }
        
        echo "</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #17a2b8; font-weight: bold;'>" . number_format($actual_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #ffc107; font-weight: bold;'>" . number_format($rounded_cost, 2) . " ج.م</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;'>" . number_format($first_minute_cost, 2) . " ج.م</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // ملاحظات مهمة
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; border-left: 5px solid #6c757d; margin: 20px 0;'>";
    echo "<h4 style='color: #495057;'>📝 ملاحظات مهمة:</h4>";
    echo "<ul>";
    echo "<li><strong>الوقت الفعلي:</strong> الأكثر عدالة، العميل يدفع فقط مقابل الوقت الذي قضاه</li>";
    echo "<li><strong>تقريب الساعة:</strong> نظام متوسط، مناسب للمحلات التقليدية</li>";
    echo "<li><strong>ساعة من أول دقيقة:</strong> نظام صارم، مناسب للمحلات التي تريد ضمان حد أدنى من الربح</li>";
    echo "<li>يمكن للعميل تغيير الطريقة في أي وقت من إعدادات الفاتورة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ تم إضافة خيار ساعة كاملة من أول دقيقة بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>الآن لديك ثلاثة خيارات لحساب التكلفة.</strong></p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4>📋 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>تعديل الدالة المساعدة لتشمل الخيار الجديد</li>";
    echo "<li>تعديل صفحة إعدادات الفاتورة</li>";
    echo "<li>تعديل API حفظ الإعدادات</li>";
    echo "<li>اختبار الخيار الجديد</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    background: white;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}

@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}
</style>
