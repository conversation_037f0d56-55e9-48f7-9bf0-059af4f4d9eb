-- إصلا<PERSON> مشكلة عمود s.status في جدول sessions - PlayGood
-- تشغيل هذا الملف لإصلاح الأخطاء المتعلقة بعمود الحالة

-- 1. التأكد من وجود عمود status في جدول sessions
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS status ENUM('active','completed','cancelled') NOT NULL DEFAULT 'active';

-- 2. إضافة الأعمدة المفقودة الأخرى إذا لم تكن موجودة
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS client_id INT NULL,
ADD COLUMN IF NOT EXISTS customer_id INT NULL,
ADD COLUMN IF NOT EXISTS total_cost DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS game_type ENUM('single','multiplayer') DEFAULT 'single',
ADD COLUMN IF NOT EXISTS created_by INT NULL,
ADD COLUMN IF NOT EXISTS updated_by INT NULL;

-- 3. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_client_id ON sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_sessions_customer_id ON sessions(customer_id);
CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time);

-- 4. تحديث الجلسات الموجودة لتكون لها حالة افتراضية
UPDATE sessions 
SET status = 'active' 
WHERE status IS NULL OR status = '';

-- 5. تحديث client_id للجلسات الموجودة إذا كان فارغاً
UPDATE sessions s
JOIN devices d ON s.device_id = d.device_id
SET s.client_id = d.client_id
WHERE s.client_id IS NULL;

-- 6. عرض النتيجة
SELECT 'تم إصلاح جدول sessions بنجاح - يمكن الآن الوصول للصفحات بدون أخطاء' as result;

-- 7. اختبار الاستعلامات
-- اختبار استعلام العملاء
SELECT 'اختبار استعلام العملاء' as test_name;
SELECT 
    c.*,
    COUNT(DISTINCT s.session_id) as sessions_count,
    COALESCE(SUM(CASE WHEN i.invoice_id IS NOT NULL THEN i.total_cost ELSE 0 END), 0) as total_spent
FROM customers c
LEFT JOIN sessions s ON c.customer_id = s.customer_id 
    AND (s.status = 'completed' OR s.status IS NULL)
LEFT JOIN invoices i ON s.session_id = i.session_id
WHERE c.client_id = 1
GROUP BY 
    c.customer_id,
    c.name,
    c.phone,
    c.email,
    c.notes,
    c.created_at
ORDER BY c.created_at DESC
LIMIT 1;

-- اختبار استعلام الأجهزة
SELECT 'اختبار استعلام الأجهزة' as test_name;
SELECT d.*, r.room_name,
    (SELECT COUNT(*) FROM sessions s WHERE s.device_id = d.device_id AND (s.status = 'active' OR s.status IS NULL)) as active_sessions_count
FROM devices d
LEFT JOIN rooms r ON d.room_id = r.room_id
WHERE d.client_id = 1
ORDER BY d.device_name
LIMIT 1;

-- اختبار استعلام الجلسات النشطة
SELECT 'اختبار استعلام الجلسات النشطة' as test_name;
SELECT s.*,
       d.device_name,
       d.device_type,
       d.hourly_rate,
       d.single_rate,
       d.multi_rate,
       r.room_name,
       TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
       COALESCE(s.game_type, 'single') as game_type
FROM sessions s
JOIN devices d ON s.device_id = d.device_id
LEFT JOIN rooms r ON d.room_id = r.room_id
WHERE d.client_id = 1 AND (s.status = 'active' OR s.status IS NULL)
ORDER BY s.start_time DESC
LIMIT 1;

-- اختبار استعلام الجلسات المكتملة
SELECT 'اختبار استعلام الجلسات المكتملة' as test_name;
SELECT s.*,
       d.device_name,
       d.device_type,
       d.hourly_rate,
       d.single_rate,
       d.multi_rate,
       r.room_name,
       TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
       COALESCE(s.total_cost, 0) as final_cost,
       COALESCE(s.game_type, 'single') as game_type
FROM sessions s
JOIN devices d ON s.device_id = d.device_id
LEFT JOIN rooms r ON d.room_id = r.room_id
WHERE d.client_id = 1 AND (s.status = 'completed' OR s.status IS NULL)
ORDER BY s.end_time DESC
LIMIT 1;

SELECT 'جميع الاختبارات تمت بنجاح - تم إصلاح المشكلة' as final_result;
