<?php
/**
 * سكريپت إصلاح جدول التصنيفات
 * يتحقق من وجود الجدول ويصلح المشاكل
 */

require_once 'config/database.php';

echo "<h1>إصلاح جدول التصنيفات - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

$fixes_applied = 0;
$errors = [];

try {
    echo "<h2>1. فحص جدول categories</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ جدول categories غير موجود - جاري الإنشاء...</p>";
        
        // إنشاء الجدول
        $create_table_sql = "
            CREATE TABLE categories (
                category_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_category_client (name, client_id),
                INDEX idx_categories_client_id (client_id),
                INDEX idx_categories_name (name)
            )
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول categories</p>";
        $fixes_applied++;
        
        // إضافة تصنيفات تجريبية
        $sample_categories = [
            ['مشروبات', 'المشروبات الباردة والساخنة'],
            ['وجبات خفيفة', 'الوجبات السريعة والخفيفة'],
            ['حلويات', 'الحلويات والكيك'],
            ['مقبلات', 'المقبلات والسلطات'],
            ['وجبات رئيسية', 'الوجبات الرئيسية والأطباق الساخنة']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO categories (name, description, client_id) VALUES (?, ?, 1)");
        foreach ($sample_categories as $category) {
            try {
                $insert_stmt->execute($category);
                echo "<p style='color: green;'>✅ تم إضافة التصنيف: " . $category[0] . "</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>⚠️ تصنيف " . $category[0] . ": " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: green;'>✅ جدول categories موجود</p>";
    }
    
    echo "<h2>2. فحص هيكل الجدول</h2>";
    
    // فحص أعمدة الجدول
    $stmt = $pdo->query("DESCRIBE categories");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $column_names = array_column($columns, 'Field');
    
    echo "<h3>الأعمدة الموجودة:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    // التحقق من الأعمدة المطلوبة
    $required_columns = [
        'category_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(100) NOT NULL',
        'description' => 'TEXT',
        'client_id' => 'INT NOT NULL DEFAULT 1',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    echo "<h3>فحص الأعمدة المطلوبة:</h3>";
    
    foreach ($required_columns as $column_name => $column_type) {
        if (in_array($column_name, $column_names)) {
            echo "<p style='color: green;'>✅ عمود $column_name موجود</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ عمود $column_name غير موجود - جاري الإضافة...</p>";
            
            try {
                switch ($column_name) {
                    case 'client_id':
                        $pdo->exec("ALTER TABLE categories ADD COLUMN client_id INT NOT NULL DEFAULT 1");
                        echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
                        $fixes_applied++;
                        break;
                    case 'description':
                        $pdo->exec("ALTER TABLE categories ADD COLUMN description TEXT");
                        echo "<p style='color: green;'>✅ تم إضافة عمود description</p>";
                        $fixes_applied++;
                        break;
                    case 'created_at':
                        $pdo->exec("ALTER TABLE categories ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                        echo "<p style='color: green;'>✅ تم إضافة عمود created_at</p>";
                        $fixes_applied++;
                        break;
                    case 'updated_at':
                        $pdo->exec("ALTER TABLE categories ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        echo "<p style='color: green;'>✅ تم إضافة عمود updated_at</p>";
                        $fixes_applied++;
                        break;
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
                $errors[] = $e->getMessage();
            }
        }
    }
    
    echo "<h2>3. إضافة فهارس</h2>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_categories_client_id ON categories(client_id)" => "فهرس client_id",
        "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)" => "فهرس name"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ $description: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. تحديث البيانات</h2>";
    
    // التأكد من أن جميع التصنيفات لها client_id
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE client_id IS NULL OR client_id = 0");
        $categories_without_client = $stmt->fetchColumn();
        
        if ($categories_without_client > 0) {
            $pdo->exec("UPDATE categories SET client_id = 1 WHERE client_id IS NULL OR client_id = 0");
            echo "<p style='color: green;'>✅ تم تحديث $categories_without_client تصنيف بمعرف العميل الافتراضي</p>";
            $fixes_applied++;
        } else {
            echo "<p style='color: green;'>✅ جميع التصنيفات لها معرف عميل صحيح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ تحديث البيانات: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. إضافة بيانات تجريبية (إذا كان الجدول فارغ)</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE client_id = 1");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        echo "<p style='color: blue;'>ℹ️ لا توجد تصنيفات للعميل - جاري إضافة بيانات تجريبية...</p>";
        
        $sample_categories = [
            ['مشروبات باردة', 'العصائر والمشروبات الغازية والمياه'],
            ['مشروبات ساخنة', 'الشاي والقهوة والمشروبات الساخنة'],
            ['وجبات خفيفة', 'البسكويت والشيبس والمكسرات'],
            ['حلويات', 'الكيك والحلويات الشرقية والغربية'],
            ['ساندويتشات', 'الساندويتشات والبرجر'],
            ['مقبلات', 'السلطات والمقبلات الباردة والساخنة'],
            ['وجبات رئيسية', 'الأطباق الرئيسية والوجبات الكاملة'],
            ['آيس كريم', 'الآيس كريم والمثلجات']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO categories (name, description, client_id) VALUES (?, ?, 1)");
        
        foreach ($sample_categories as $category) {
            try {
                $insert_stmt->execute($category);
                echo "<p style='color: green;'>✅ تم إضافة التصنيف: " . $category[0] . "</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة التصنيف " . $category[0] . ": " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ يوجد $category_count تصنيف في النظام</p>";
    }
    
    echo "<h2>6. اختبار الاستعلامات</h2>";
    
    // اختبار إضافة تصنيف جديد
    try {
        $test_category_name = 'تصنيف تجريبي ' . date('H:i:s');
        $test_stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
        $test_stmt->execute([$test_category_name]);
        
        echo "<p style='color: green;'>✅ اختبار إضافة تصنيف جديد نجح</p>";
        
        // حذف التصنيف التجريبي
        $test_id = $pdo->lastInsertId();
        $pdo->prepare("DELETE FROM categories WHERE category_id = ?")->execute([$test_id]);
        echo "<p style='color: green;'>✅ تم حذف التصنيف التجريبي</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار إضافة التصنيف: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    // اختبار جلب التصنيفات
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE client_id = 1");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✅ استعلام التصنيفات يعمل - يوجد " . $result['count'] . " تصنيف</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام التصنيفات: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    
    echo "<h2>النتائج النهائية</h2>";
    
    if (count($errors) == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم إصلاح جدول التصنيفات بنجاح!</h3>";
        echo "<ul>";
        echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
        echo "<li>لا توجد أخطاء</li>";
        echo "<li>يمكن الآن إضافة التصنيفات بنجاح</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ تم الإصلاح مع بعض التحذيرات</h3>";
        echo "<ul>";
        echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
        echo "<li>عدد الأخطاء: " . count($errors) . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>اختبر النظام الآن:</h3>";
    echo "<p><a href='client/cafeteria.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة الكافتيريا</a></p>";
    echo "<p><a href='client/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getFile() . " في السطر " . $e->getLine() . "</p>";
}

echo "</div>";
?>
