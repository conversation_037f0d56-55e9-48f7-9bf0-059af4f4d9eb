<?php
/**
 * سكريبت إنشاء جداول الماليات لمشروع PlayGood
 * يجب تشغيل هذا الملف مرة واحدة لإنشاء الجداول المطلوبة
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء جداول الماليات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <h1 class='text-center mb-4'>إنشاء جداول الماليات</h1>
    <div class='row justify-content-center'>
        <div class='col-md-8'>";

try {
    echo "<div class='alert alert-info'>جاري إنشاء جداول الماليات...</div>";

    // جدول أنواع المصروفات
    $expense_types_sql = "
        CREATE TABLE IF NOT EXISTS expense_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_expense_types_client (client_id),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($expense_types_sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول أنواع المصروفات (expense_types)</p>";

    // جدول المصروفات
    $expenses_sql = "
        CREATE TABLE IF NOT EXISTS expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            expense_type_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            expense_date DATE NOT NULL,
            receipt_number VARCHAR(50),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_expenses_client (client_id),
            INDEX idx_expenses_type (expense_type_id),
            INDEX idx_expenses_date (expense_date),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
            FOREIGN KEY (expense_type_id) REFERENCES expense_types(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($expenses_sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول المصروفات (expenses)</p>";

    // جدول أنواع الإيرادات
    $income_types_sql = "
        CREATE TABLE IF NOT EXISTS income_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_income_types_client (client_id),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($income_types_sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول أنواع الإيرادات (income_types)</p>";

    // جدول الإيرادات الإضافية
    $additional_income_sql = "
        CREATE TABLE IF NOT EXISTS additional_income (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            income_type_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            income_date DATE NOT NULL,
            receipt_number VARCHAR(50),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_additional_income_client (client_id),
            INDEX idx_additional_income_type (income_type_id),
            INDEX idx_additional_income_date (income_date),
            FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
            FOREIGN KEY (income_type_id) REFERENCES income_types(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($additional_income_sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول الإيرادات الإضافية (additional_income)</p>";

    // إدراج أنواع مصروفات افتراضية
    echo "<div class='alert alert-info mt-3'>جاري إدراج البيانات الافتراضية...</div>";

    // الحصول على جميع العملاء
    $clients_query = $pdo->query("SELECT client_id FROM clients");
    $clients = $clients_query->fetchAll(PDO::FETCH_COLUMN);

    foreach ($clients as $client_id) {
        // أنواع المصروفات الافتراضية
        $default_expense_types = [
            ['name' => 'فواتير الكهرباء', 'description' => 'فواتير استهلاك الكهرباء الشهرية'],
            ['name' => 'فواتير الإنترنت', 'description' => 'فواتير خدمة الإنترنت الشهرية'],
            ['name' => 'إيجار المحل', 'description' => 'إيجار المحل الشهري'],
            ['name' => 'صيانة الأجهزة', 'description' => 'تكاليف صيانة وإصلاح الأجهزة'],
            ['name' => 'مشتريات الكافتيريا', 'description' => 'شراء منتجات ومشروبات الكافتيريا'],
            ['name' => 'رواتب الموظفين', 'description' => 'رواتب ومكافآت الموظفين'],
            ['name' => 'مصروفات إدارية', 'description' => 'مصروفات إدارية متنوعة'],
            ['name' => 'تسويق وإعلان', 'description' => 'تكاليف التسويق والإعلان']
        ];

        foreach ($default_expense_types as $type) {
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM expense_types WHERE client_id = ? AND name = ?");
            $check_stmt->execute([$client_id, $type['name']]);
            
            if ($check_stmt->fetchColumn() == 0) {
                $insert_stmt = $pdo->prepare("INSERT INTO expense_types (client_id, name, description) VALUES (?, ?, ?)");
                $insert_stmt->execute([$client_id, $type['name'], $type['description']]);
            }
        }

        // أنواع الإيرادات الافتراضية
        $default_income_types = [
            ['name' => 'إيرادات الجلسات', 'description' => 'إيرادات من جلسات الألعاب'],
            ['name' => 'مبيعات الكافتيريا', 'description' => 'إيرادات من مبيعات الكافتيريا'],
            ['name' => 'خدمات إضافية', 'description' => 'إيرادات من خدمات إضافية أخرى'],
            ['name' => 'تأجير قاعات', 'description' => 'إيرادات من تأجير القاعات للمناسبات'],
            ['name' => 'بيع أكسسوارات', 'description' => 'إيرادات من بيع أكسسوارات الألعاب']
        ];

        foreach ($default_income_types as $type) {
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM income_types WHERE client_id = ? AND name = ?");
            $check_stmt->execute([$client_id, $type['name']]);
            
            if ($check_stmt->fetchColumn() == 0) {
                $insert_stmt = $pdo->prepare("INSERT INTO income_types (client_id, name, description) VALUES (?, ?, ?)");
                $insert_stmt->execute([$client_id, $type['name'], $type['description']]);
            }
        }
    }

    echo "<p class='text-success'>✅ تم إدراج البيانات الافتراضية بنجاح</p>";

    echo "<div class='alert alert-success mt-4'>
            <h5>تم إنشاء جداول الماليات بنجاح! 🎉</h5>
            <p>تم إنشاء الجداول التالية:</p>
            <ul>
                <li>expense_types - أنواع المصروفات</li>
                <li>expenses - المصروفات</li>
                <li>income_types - أنواع الإيرادات</li>
                <li>additional_income - الإيرادات الإضافية</li>
            </ul>
            <p class='mb-0'>يمكنك الآن استخدام صفحة الماليات بشكل كامل.</p>
          </div>";

    echo "<div class='text-center mt-4'>
            <a href='client/finances.php' class='btn btn-primary me-2'>
                <i class='fas fa-money-bill-wave'></i> الذهاب لصفحة الماليات
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home'></i> العودة للوحة التحكم
            </a>
          </div>";

} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5>حدث خطأ أثناء إنشاء الجداول!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>يرجى التأكد من:</p>
            <ul>
                <li>صحة إعدادات قاعدة البيانات</li>
                <li>وجود صلاحيات كافية لإنشاء الجداول</li>
                <li>وجود جدول clients في قاعدة البيانات</li>
            </ul>
          </div>";
}

echo "        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
