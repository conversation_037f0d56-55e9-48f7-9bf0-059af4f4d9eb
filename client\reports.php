<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة التقارير
    if (!hasPagePermission('reports')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة التقارير';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('reports')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('view_reports')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "التقارير";
$active_page = "reports";

// تحديد الفترة الزمنية (افتراضياً آخر 30 يوم)
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');
$report_type = $_GET['report_type'] ?? 'summary';

// التحقق من صحة التواريخ
if (!strtotime($start_date) || !strtotime($end_date)) {
    $start_date = date('Y-m-d', strtotime('-30 days'));
    $end_date = date('Y-m-d');
}

// التأكد من أن تاريخ البداية أقل من تاريخ النهاية
if (strtotime($start_date) > strtotime($end_date)) {
    $temp = $start_date;
    $start_date = $end_date;
    $end_date = $temp;
}

try {
    // إحصائيات عامة للفترة المحددة
    $stats_query = $pdo->prepare("
        SELECT
            COUNT(s.session_id) as total_sessions,
            COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_sessions,
            COUNT(CASE WHEN s.status = 'cancelled' THEN 1 END) as cancelled_sessions,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN s.status = 'completed' THEN s.total_cost END), 0) as avg_session_cost,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) END), 0) as total_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
        AND DATE(s.start_time) BETWEEN ? AND ?
    ");
    $stats_query->execute([$client_id, $start_date, $end_date]);
    $stats = $stats_query->fetch(PDO::FETCH_ASSOC);

    // إحصائيات الأجهزة
    $devices_stats_query = $pdo->prepare("
        SELECT
            d.device_name,
            d.device_type,
            COUNT(s.session_id) as sessions_count,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as device_revenue,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) END), 0) as total_minutes
        FROM devices d
        LEFT JOIN sessions s ON d.device_id = s.device_id
            AND DATE(s.start_time) BETWEEN ? AND ?
        WHERE d.client_id = ?
        GROUP BY d.device_id, d.device_name, d.device_type
        ORDER BY device_revenue DESC
    ");
    $devices_stats_query->execute([$start_date, $end_date, $client_id]);
    $devices_stats = $devices_stats_query->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات يومية للرسم البياني
    $daily_stats_query = $pdo->prepare("
        SELECT
            DATE(s.start_time) as report_date,
            COUNT(s.session_id) as daily_sessions,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as daily_revenue
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
        AND DATE(s.start_time) BETWEEN ? AND ?
        GROUP BY DATE(s.start_time)
        ORDER BY report_date ASC
    ");
    $daily_stats_query->execute([$client_id, $start_date, $end_date]);
    $daily_stats = $daily_stats_query->fetchAll(PDO::FETCH_ASSOC);

    // أفضل العملاء
    $top_customers_query = $pdo->prepare("
        SELECT
            c.name as customer_name,
            c.phone as customer_phone,
            COUNT(s.session_id) as sessions_count,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as customer_revenue
        FROM customers c
        JOIN sessions s ON c.customer_id = s.customer_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ?
        AND DATE(s.start_time) BETWEEN ? AND ?
        AND s.status = 'completed'
        GROUP BY c.customer_id, c.name, c.phone
        ORDER BY customer_revenue DESC
        LIMIT 10
    ");
    $top_customers_query->execute([$client_id, $start_date, $end_date]);
    $top_customers = $top_customers_query->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $_SESSION['error'] = "حدث خطأ في جلب بيانات التقرير: " . $e->getMessage();
    $stats = ['total_sessions' => 0, 'completed_sessions' => 0, 'cancelled_sessions' => 0,
              'total_revenue' => 0, 'avg_session_cost' => 0, 'total_minutes' => 0];
    $devices_stats = [];
    $daily_stats = [];
    $top_customers = [];
}

require_once 'includes/header.php';
?>

<!-- CSS خاص بدعم تصدير PDF العربي -->
<link rel="stylesheet" href="../assets/css/arabic-pdf-support.css">

<style>
.report-card {
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

.export-buttons .btn {
    margin: 0 5px;
}

/* تحسينات للطباعة والتصدير */
@media print {
    .no-print {
        display: none !important;
    }

    .container-fluid {
        padding: 0 !important;
        margin: 0 !important;
    }

    .card {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
    }

    .stats-number {
        font-size: 18pt !important;
        color: #000 !important;
    }

    .table {
        font-size: 10pt !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 8px !important;
    }
}

/* تحسينات لتصدير PDF */
.pdf-export-ready {
    background: white !important;
    color: #000 !important;
    font-family: 'Arial', 'Helvetica', sans-serif !important;
}

.pdf-export-ready .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
}

.pdf-export-ready .stats-number {
    color: #333 !important;
    font-weight: bold !important;
}

.pdf-export-ready .text-primary {
    color: #0066cc !important;
}

.pdf-export-ready .text-success {
    color: #28a745 !important;
}

.pdf-export-ready .text-danger {
    color: #dc3545 !important;
}

@media print {
    .no-print {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- العنوان والفلاتر -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
        <h1 class="h2 mb-0">
            <i class="fas fa-chart-bar me-2 text-primary"></i>التقارير والإحصائيات
        </h1>
        <div class="btn-toolbar no-print">
            <div class="export-buttons me-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-danger btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportEnhancedArabicPDF()">
                            <i class="fas fa-image me-2 text-success"></i>PDF متقدم (دعم عربي كامل)
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                            <i class="fas fa-camera me-2 text-info"></i>PDF صورة (جودة عالية)
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportSimplePDF()">
                            <i class="fas fa-font me-2 text-warning"></i>PDF بسيط (نص إنجليزي)
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToPDFAlternative()">
                            <i class="fas fa-file-alt me-2 text-secondary"></i>PDF تقليدي
                        </a></li>
                    </ul>
                </div>
                <button onclick="exportToExcel()" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel me-1"></i>تصدير Excel
                </button>
                <button onclick="printReportEnhanced()" class="btn btn-info btn-sm">
                    <i class="fas fa-print me-1"></i>طباعة محسنة
                </button>
            </div>
            <a href="dashboard.php" class="btn btn-primary btn-sm">
                <i class="fas fa-home me-1"></i>لوحة التحكم
            </a>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="card mb-4 no-print">
        <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر التقرير</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date"
                           value="<?php echo htmlspecialchars($start_date); ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="<?php echo htmlspecialchars($end_date); ?>">
                </div>
                <div class="col-md-3">
                    <label for="report_type" class="form-label">نوع التقرير</label>
                    <select class="form-select" id="report_type" name="report_type">
                        <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>ملخص عام</option>
                        <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>تفصيلي</option>
                        <option value="devices" <?php echo $report_type === 'devices' ? 'selected' : ''; ?>>تقرير الأجهزة</option>
                        <option value="customers" <?php echo $report_type === 'customers' ? 'selected' : ''; ?>>تقرير العملاء</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card report-card border-primary h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-play-circle fa-3x text-primary"></i>
                    </div>
                    <h6 class="card-title text-primary">إجمالي الجلسات</h6>
                    <div class="stats-number text-primary"><?php echo number_format($stats['total_sessions']); ?></div>
                    <small class="text-muted">
                        مكتملة: <?php echo number_format($stats['completed_sessions']); ?> |
                        ملغية: <?php echo number_format($stats['cancelled_sessions']); ?>
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card report-card border-success h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                    </div>
                    <h6 class="card-title text-success">إجمالي الإيرادات</h6>
                    <div class="stats-number text-success"><?php echo number_format($stats['total_revenue'], 2); ?></div>
                    <small class="text-muted">جنيه مصري</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card report-card border-info h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-calculator fa-3x text-info"></i>
                    </div>
                    <h6 class="card-title text-info">متوسط تكلفة الجلسة</h6>
                    <div class="stats-number text-info"><?php echo number_format($stats['avg_session_cost'], 2); ?></div>
                    <small class="text-muted">جنيه مصري</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card report-card border-warning h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-clock fa-3x text-warning"></i>
                    </div>
                    <h6 class="card-title text-warning">إجمالي ساعات اللعب</h6>
                    <div class="stats-number text-warning"><?php echo number_format($stats['total_minutes'] / 60, 1); ?></div>
                    <small class="text-muted">ساعة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني للإيرادات اليومية -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإيرادات اليومية</h6>
        </div>
        <div class="card-body">
            <div class="chart-container">
                <canvas id="dailyRevenueChart"></canvas>
            </div>
        </div>
    </div>

    <div class="row g-3 mb-4">
        <!-- إحصائيات الأجهزة -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-gamepad me-2"></i>أداء الأجهزة</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($devices_stats)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الجهاز</th>
                                        <th>النوع</th>
                                        <th>عدد الجلسات</th>
                                        <th>الإيرادات</th>
                                        <th>ساعات التشغيل</th>
                                        <th>الأداء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($devices_stats as $device): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($device['device_name']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($device['device_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo number_format($device['sessions_count']); ?></td>
                                            <td class="text-success">
                                                <strong><?php echo number_format($device['device_revenue'], 2); ?> ج.م</strong>
                                            </td>
                                            <td><?php echo number_format($device['total_minutes'] / 60, 1); ?> ساعة</td>
                                            <td>
                                                <?php
                                                $performance_percentage = $stats['total_revenue'] > 0
                                                    ? ($device['device_revenue'] / $stats['total_revenue']) * 100
                                                    : 0;
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-info" role="progressbar"
                                                         style="width: <?php echo $performance_percentage; ?>%">
                                                        <?php echo number_format($performance_percentage, 1); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات أجهزة للفترة المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أفضل العملاء -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>أفضل العملاء</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($top_customers)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($top_customers as $index => $customer): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($customer['customer_name']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($customer['customer_phone']); ?></small>
                                        <div class="small text-info">
                                            <?php echo number_format($customer['sessions_count']); ?> جلسة
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-success rounded-pill">
                                            #<?php echo $index + 1; ?>
                                        </span>
                                        <div class="fw-bold text-success">
                                            <?php echo number_format($customer['customer_revenue'], 2); ?> ج.م
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات عملاء للفترة المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين مكتبات الرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<!-- مكتبة html2canvas لتحويل HTML إلى صورة -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<!-- ملف تحسينات تصدير PDF العربي -->
<script src="../assets/js/arabic-pdf-export.js"></script>

<script>
// بيانات الرسم البياني
const dailyData = <?php echo json_encode($daily_stats); ?>;

// إعداد الرسم البياني للإيرادات اليومية
const ctx = document.getElementById('dailyRevenueChart').getContext('2d');
const dailyRevenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyData.map(item => {
            const date = new Date(item.report_date);
            return date.toLocaleDateString('ar-EG', { month: 'short', day: 'numeric' });
        }),
        datasets: [{
            label: 'الإيرادات اليومية (ج.م)',
            data: dailyData.map(item => parseFloat(item.daily_revenue)),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }, {
            label: 'عدد الجلسات',
            data: dailyData.map(item => parseInt(item.daily_sessions)),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: 'الإيرادات والجلسات اليومية'
            },
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'الإيرادات (ج.م)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'عدد الجلسات'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// وظيفة تصدير PDF محسنة مع دعم اللغة العربية
function exportToPDF() {
    // عرض مؤشر التحميل
    Swal.fire({
        title: 'جاري إنشاء التقرير...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    // استخدام html2canvas لتحويل المحتوى إلى صورة ثم PDF
    const reportContent = document.querySelector('.container-fluid');
    const elementsToHide = document.querySelectorAll('.no-print, .btn-toolbar, .export-buttons');

    // إخفاء العناصر غير المرغوب فيها
    elementsToHide.forEach(el => el.style.display = 'none');

    html2canvas(reportContent, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: reportContent.scrollWidth,
        height: reportContent.scrollHeight
    }).then(canvas => {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 210; // A4 width in mm
        const pageHeight = 295; // A4 height in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // إضافة الصفحة الأولى
        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            doc.addPage();
            doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        const fileName = `تقرير_الإحصائيات_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        // إظهار العناصر مرة أخرى
        elementsToHide.forEach(el => el.style.display = '');

        // إغلاق مؤشر التحميل
        Swal.close();

        // رسالة نجاح
        Swal.fire({
            icon: 'success',
            title: 'تم إنشاء التقرير بنجاح!',
            text: 'تم تحميل ملف PDF',
            timer: 2000,
            showConfirmButton: false
        });

    }).catch(error => {
        console.error('خطأ في إنشاء PDF:', error);

        // إظهار العناصر مرة أخرى في حالة الخطأ
        elementsToHide.forEach(el => el.style.display = '');

        // إغلاق مؤشر التحميل
        Swal.close();

        // رسالة خطأ
        Swal.fire({
            icon: 'error',
            title: 'خطأ في إنشاء التقرير',
            text: 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
            confirmButtonText: 'موافق'
        });
    });
}

// وظيفة تصدير PDF بديلة (نص عربي مباشر)
function exportToPDFAlternative() {
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        // تعيين الخط (استخدام خط افتراضي يدعم Unicode)
        doc.setFont('helvetica');

        // إضافة العنوان الرئيسي
        doc.setFontSize(18);
        doc.text('PlayGood - Report', 105, 20, { align: 'center' });

        doc.setFontSize(14);
        doc.text('Statistics Report', 105, 30, { align: 'center' });

        // إضافة الفترة الزمنية
        doc.setFontSize(12);
        doc.text(`Period: <?php echo $start_date; ?> to <?php echo $end_date; ?>`, 105, 45, { align: 'center' });

        // إضافة خط فاصل
        doc.line(20, 55, 190, 55);

        // إضافة الإحصائيات الرئيسية
        let yPosition = 70;
        doc.setFontSize(14);
        doc.text('Main Statistics:', 20, yPosition);

        yPosition += 15;
        doc.setFontSize(11);

        // البيانات الإحصائية
        const stats = [
            ['Total Sessions', '<?php echo number_format($stats['total_sessions']); ?>'],
            ['Completed Sessions', '<?php echo number_format($stats['completed_sessions']); ?>'],
            ['Cancelled Sessions', '<?php echo number_format($stats['cancelled_sessions']); ?>'],
            ['Total Revenue', '<?php echo number_format($stats['total_revenue'], 2); ?> EGP'],
            ['Average Session Cost', '<?php echo number_format($stats['avg_session_cost'], 2); ?> EGP'],
            ['Total Playing Hours', '<?php echo number_format($stats['total_minutes'] / 60, 1); ?> Hours']
        ];

        stats.forEach(([label, value]) => {
            doc.text(label + ':', 25, yPosition);
            doc.text(value, 120, yPosition);
            yPosition += 10;
        });

        // إضافة معلومات الأجهزة إذا كانت متوفرة
        <?php if (!empty($devices_stats)): ?>
        yPosition += 10;
        doc.line(20, yPosition, 190, yPosition);
        yPosition += 10;

        doc.setFontSize(14);
        doc.text('Devices Statistics:', 20, yPosition);
        yPosition += 15;

        doc.setFontSize(10);
        doc.text('Device Name', 25, yPosition);
        doc.text('Type', 80, yPosition);
        doc.text('Sessions', 120, yPosition);
        doc.text('Revenue', 160, yPosition);
        yPosition += 8;

        doc.line(20, yPosition, 190, yPosition);
        yPosition += 5;

        <?php foreach ($devices_stats as $device): ?>
        doc.text('<?php echo addslashes($device['device_name']); ?>', 25, yPosition);
        doc.text('<?php echo addslashes($device['device_type']); ?>', 80, yPosition);
        doc.text('<?php echo $device['sessions_count']; ?>', 120, yPosition);
        doc.text('<?php echo number_format($device['device_revenue'], 2); ?>', 160, yPosition);
        yPosition += 8;

        if (yPosition > 270) {
            doc.addPage();
            yPosition = 20;
        }
        <?php endforeach; ?>
        <?php endif; ?>

        // إضافة تاريخ الإنشاء
        doc.setFontSize(8);
        doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, 285);

        // حفظ الملف
        const fileName = `PlayGood_Report_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        // رسالة نجاح
        Swal.fire({
            icon: 'success',
            title: 'PDF Report Created!',
            text: 'Report downloaded successfully',
            timer: 2000,
            showConfirmButton: false
        });

    } catch (error) {
        console.error('Error creating PDF:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to create PDF report',
            confirmButtonText: 'OK'
        });
    }
}

// وظيفة تصدير Excel محسنة مع دعم اللغة العربية
function exportToExcel() {
    // عرض مؤشر التحميل
    Swal.fire({
        title: 'جاري إنشاء ملف Excel...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

    // بيانات الإحصائيات الرئيسية
    const statsData = [
        ['الإحصائية', 'القيمة'],
        ['إجمالي الجلسات', <?php echo $stats['total_sessions']; ?>],
        ['الجلسات المكتملة', <?php echo $stats['completed_sessions']; ?>],
        ['الجلسات الملغية', <?php echo $stats['cancelled_sessions']; ?>],
        ['إجمالي الإيرادات (ج.م)', <?php echo $stats['total_revenue']; ?>],
        ['متوسط تكلفة الجلسة (ج.م)', <?php echo $stats['avg_session_cost']; ?>],
        ['إجمالي ساعات اللعب', <?php echo $stats['total_minutes'] / 60; ?>]
    ];

    const statsSheet = XLSX.utils.aoa_to_sheet(statsData);
    XLSX.utils.book_append_sheet(wb, statsSheet, 'الإحصائيات الرئيسية');

    // بيانات الأجهزة
    <?php if (!empty($devices_stats)): ?>
    const devicesData = [
        ['اسم الجهاز', 'النوع', 'عدد الجلسات', 'الإيرادات (ج.م)', 'ساعات التشغيل']
        <?php foreach ($devices_stats as $device): ?>
        ,['<?php echo addslashes($device['device_name']); ?>',
          '<?php echo addslashes($device['device_type']); ?>',
          <?php echo $device['sessions_count']; ?>,
          <?php echo $device['device_revenue']; ?>,
          <?php echo $device['total_minutes'] / 60; ?>]
        <?php endforeach; ?>
    ];

    const devicesSheet = XLSX.utils.aoa_to_sheet(devicesData);
    XLSX.utils.book_append_sheet(wb, devicesSheet, 'إحصائيات الأجهزة');
    <?php endif; ?>

    // بيانات العملاء
    <?php if (!empty($top_customers)): ?>
    const customersData = [
        ['اسم العميل', 'رقم الهاتف', 'عدد الجلسات', 'الإيرادات (ج.م)']
        <?php foreach ($top_customers as $customer): ?>
        ,['<?php echo addslashes($customer['customer_name']); ?>',
          '<?php echo addslashes($customer['customer_phone']); ?>',
          <?php echo $customer['sessions_count']; ?>,
          <?php echo $customer['customer_revenue']; ?>]
        <?php endforeach; ?>
    ];

    const customersSheet = XLSX.utils.aoa_to_sheet(customersData);
    XLSX.utils.book_append_sheet(wb, customersSheet, 'أفضل العملاء');
    <?php endif; ?>

    // بيانات الإيرادات اليومية
    const dailyRevenueData = [
        ['التاريخ', 'عدد الجلسات', 'الإيرادات (ج.م)']
        <?php foreach ($daily_stats as $day): ?>
        ,['<?php echo $day['report_date']; ?>',
          <?php echo $day['daily_sessions']; ?>,
          <?php echo $day['daily_revenue']; ?>]
        <?php endforeach; ?>
    ];

    const dailySheet = XLSX.utils.aoa_to_sheet(dailyRevenueData);
    XLSX.utils.book_append_sheet(wb, dailySheet, 'الإيرادات اليومية');

    // حفظ الملف
    const fileName = `تقرير_الإحصائيات_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    // إغلاق مؤشر التحميل
    Swal.close();

    // رسالة نجاح
    Swal.fire({
        icon: 'success',
        title: 'تم إنشاء ملف Excel بنجاح!',
        html: `
            <div class="text-center">
                <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                <p>تم تحميل ملف Excel مع دعم كامل للغة العربية</p>
                <small class="text-muted">الملف: ${fileName}</small>
            </div>
        `,
        timer: 3000,
        showConfirmButton: false
    });

    } catch (error) {
        console.error('خطأ في إنشاء Excel:', error);

        // إغلاق مؤشر التحميل
        Swal.close();

        // رسالة خطأ
        Swal.fire({
            icon: 'error',
            title: 'خطأ في إنشاء ملف Excel',
            text: 'حدث خطأ أثناء إنشاء الملف. يرجى المحاولة مرة أخرى.',
            confirmButtonText: 'موافق'
        });
    }
}

// دالة طباعة محسنة مع دعم اللغة العربية
function printReportEnhanced() {
    // تحضير الصفحة للطباعة
    document.body.classList.add('pdf-export-ready');

    // إخفاء العناصر غير المرغوب فيها
    const elementsToHide = document.querySelectorAll('.no-print, .btn-toolbar, .export-buttons');
    elementsToHide.forEach(el => {
        el.style.display = 'none';
        el.setAttribute('data-hidden-for-print', 'true');
    });

    // إضافة عنوان للطباعة
    const printTitle = document.createElement('div');
    printTitle.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 15px;">
            <h1 style="color: #333; font-size: 24px; margin-bottom: 10px;">PlayGood - تقرير الإحصائيات</h1>
            <p style="color: #666; font-size: 14px; margin: 0;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>
    `;
    printTitle.setAttribute('data-print-header', 'true');

    const container = document.querySelector('.container-fluid');
    container.insertBefore(printTitle, container.firstChild);

    // تحسين الخطوط للطباعة
    const style = document.createElement('style');
    style.innerHTML = `
        @media print {
            @page {
                size: A4;
                margin: 2cm;
                direction: rtl;
            }

            body {
                font-family: 'Arial', 'Helvetica', sans-serif !important;
                font-size: 12pt !important;
                line-height: 1.4 !important;
                color: #000 !important;
                background: white !important;
                direction: rtl !important;
            }

            .container-fluid {
                padding: 0 !important;
                margin: 0 !important;
                max-width: none !important;
            }

            .card {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px !important;
                border: 1px solid #333 !important;
            }

            .stats-number {
                font-size: 16pt !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            .table {
                font-size: 10pt !important;
                border-collapse: collapse !important;
            }

            .table th,
            .table td {
                border: 1px solid #000 !important;
                padding: 8px !important;
            }

            .chart-container {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }
    `;
    style.setAttribute('data-print-style', 'true');
    document.head.appendChild(style);

    // طباعة الصفحة
    setTimeout(() => {
        window.print();

        // تنظيف بعد الطباعة
        setTimeout(() => {
            // إزالة العناصر المضافة
            document.querySelectorAll('[data-print-header="true"]').forEach(el => el.remove());
            document.querySelectorAll('[data-print-style="true"]').forEach(el => el.remove());

            // إظهار العناصر المخفية
            document.querySelectorAll('[data-hidden-for-print="true"]').forEach(el => {
                el.style.display = '';
                el.removeAttribute('data-hidden-for-print');
            });

            // إزالة كلاس التصدير
            document.body.classList.remove('pdf-export-ready');
        }, 1000);
    }, 500);
}

// تحديث التقرير تلقائياً كل 5 دقائق
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000); // 5 دقائق

// إضافة تأثيرات تفاعلية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.report-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>