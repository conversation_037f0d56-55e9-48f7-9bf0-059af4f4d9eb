<?php
/**
 * اختبار الحذف الذكي للمنتجات (تقليل الكمية أو حذف السجل)
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار الحذف الذكي للمنتجات</h1>";
echo "<hr>";

try {
    echo "<h2>1. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $pdo->exec("INSERT INTO sessions (device_id, start_time, status, client_id) VALUES (1, NOW(), 'active', 1)");
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: green;'>✅ تم العثور على جلسة نشطة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاؤه
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        // إنشاء منتج تجريبي
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('كوكاكولا', 3.50, 'مشروبات', 1)");
        $product_id = $pdo->lastInsertId();
        $product_name = 'كوكاكولا';
        $product_price = 3.50;
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: green;'>✅ تم العثور على منتج: $product_name ($product_id)</p>";
    }
    
    echo "<h2>2. إضافة منتجات بكميات مختلفة</h2>";
    
    // حذف أي منتجات موجودة مسبقاً
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ?")->execute([$session_id]);
    
    // إضافة منتجات بكميات مختلفة
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, 5, $product_price]); // كمية 5
    $stmt->execute([$session_id, $product_id, 1, $product_price]); // كمية 1
    $stmt->execute([$session_id, $product_id, 3, $product_price]); // كمية 3
    
    echo "<p style='color: green;'>✅ تم إضافة 3 سجلات للمنتج بكميات مختلفة (5، 1، 3)</p>";
    
    // عرض المنتجات الحالية
    $stmt = $pdo->prepare("
        SELECT sp.*, ci.name as product_name 
        FROM session_products sp 
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id 
        WHERE sp.session_id = ? 
        ORDER BY sp.id
    ");
    $stmt->execute([$session_id]);
    $session_products = $stmt->fetchAll();
    
    echo "<p><strong>المنتجات الحالية في الجلسة:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID السجل</th><th>اسم المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th><th>إجراء</th></tr>";
    foreach ($session_products as $sp) {
        $total = $sp['quantity'] * $sp['price'];
        echo "<tr>";
        echo "<td>{$sp['id']}</td>";
        echo "<td>{$sp['product_name']}</td>";
        echo "<td>{$sp['quantity']}</td>";
        echo "<td>{$sp['price']}</td>";
        echo "<td>{$total}</td>";
        echo "<td><button onclick='testDelete({$session_id}, {$sp['product_id']})' class='btn btn-danger btn-sm'>حذف</button></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. اختبار الحذف الذكي</h2>";
    echo "<p><strong>كيف يعمل الحذف الذكي:</strong></p>";
    echo "<ul>";
    echo "<li>إذا كانت الكمية أكثر من 1، سيتم تقليل الكمية بوحدة واحدة</li>";
    echo "<li>إذا كانت الكمية 1، سيتم حذف السجل كاملاً</li>";
    echo "<li>يتم التعامل مع أول سجل موجود للمنتج</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحذف الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار الحذف</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3" onclick="refreshProducts()">
                            <i class="fas fa-refresh me-2"></i>تحديث قائمة المنتجات
                        </button>
                        <div id="products-list">
                            <!-- سيتم تحميل المنتجات هنا -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>سجل الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            <!-- سيتم ملء السجل هنا -->
                        </div>
                        <button class="btn btn-secondary btn-sm mt-2" onclick="clearLog()">
                            <i class="fas fa-trash me-2"></i>مسح السجل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    const testProductId = <?php echo $product_id; ?>;
    
    // دالة تسجيل الأحداث
    function log(message, type = 'info') {
        const logDiv = document.getElementById('test-log');
        const timestamp = new Date().toLocaleTimeString('ar-EG');
        const colors = {
            'info': '#007bff',
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107'
        };
        
        logDiv.innerHTML += `<div style="color: ${colors[type]}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function clearLog() {
        document.getElementById('test-log').innerHTML = '';
    }
    
    // دالة تحديث قائمة المنتجات
    function refreshProducts() {
        log('تحديث قائمة المنتجات...', 'info');
        
        fetch(`client/api/get_session_products.php?session_id=${testSessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const productsDiv = document.getElementById('products-list');
                    
                    if (data.products.length > 0) {
                        let html = '<div class="list-group">';
                        data.products.forEach(product => {
                            html += `
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">${product.product_name}</h6>
                                        <small class="text-muted">الكمية: ${product.quantity} × ${product.price} ج.م</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="me-3 fw-bold">${product.total} ج.م</span>
                                        <button class="btn btn-danger btn-sm" onclick="testDelete(${testSessionId}, ${product.product_id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                        productsDiv.innerHTML = html;
                        
                        log(`تم تحميل ${data.products.length} منتج`, 'success');
                    } else {
                        productsDiv.innerHTML = '<p class="text-muted">لا توجد منتجات</p>';
                        log('لا توجد منتجات في الجلسة', 'warning');
                    }
                } else {
                    log(`خطأ في تحميل المنتجات: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                log(`خطأ في الشبكة: ${error.message}`, 'error');
            });
    }
    
    // دالة اختبار الحذف
    function testDelete(sessionId, productId) {
        log(`بدء اختبار حذف المنتج ${productId} من الجلسة ${sessionId}`, 'warning');
        
        const deleteData = {
            session_id: sessionId,
            product_id: productId
        };
        
        fetch('client/api/delete_session_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(deleteData)
        })
        .then(response => response.text())
        .then(responseText => {
            log(`استجابة خام: ${responseText}`, 'info');
            
            try {
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    log(`✅ نجح الحذف: ${data.message}`, 'success');
                    log(`الإجراء: ${data.action}`, 'info');
                    log(`الكمية الجديدة: ${data.new_quantity}`, 'info');
                    log(`التكلفة الجديدة: ${data.total_cost} ج.م`, 'info');
                    
                    // تحديث قائمة المنتجات
                    setTimeout(refreshProducts, 500);
                } else {
                    log(`❌ فشل الحذف: ${data.error}`, 'error');
                }
            } catch (e) {
                log(`❌ خطأ في تحليل الاستجابة: ${e.message}`, 'error');
            }
        })
        .catch(error => {
            log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
        });
    }
    
    // تحميل المنتجات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        log('بدء اختبار الحذف الذكي', 'info');
        refreshProducts();
    });
    </script>
</body>
</html>
