<?php
/**
 * إصلاح مشكلة عمود expected_end_time المفقود في جدول sessions
 * PlayGood Gaming Center Management System
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'station'; // أو 'playgood' حسب قاعدة البيانات المستخدمة
$username = 'root';
$password = '';

echo "<h1>إصلاح مشكلة عمود expected_end_time</h1>";
echo "<hr>";

try {
    // محاولة الاتصال بقاعدة البيانات station أولاً
    try {
        $pdo = new PDO("mysql:host=$host;dbname=station;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات: station</p>";
        $dbname = 'station';
    } catch (PDOException $e) {
        // إذا فشل، جرب playgood
        try {
            $pdo = new PDO("mysql:host=$host;dbname=playgood;charset=utf8mb4", $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات: playgood</p>";
            $dbname = 'playgood';
        } catch (PDOException $e2) {
            throw new Exception("فشل في الاتصال بكلا قاعدتي البيانات: " . $e2->getMessage());
        }
    }

    echo "<h2>1. فحص جدول sessions</h2>";
    
    // فحص وجود جدول sessions
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول sessions غير موجود في قاعدة البيانات");
    }
    echo "<p style='color: green;'>✅ جدول sessions موجود</p>";

    // فحص أعمدة جدول sessions
    $stmt = $pdo->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الأعمدة الموجودة حالياً:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";

    echo "<h2>2. إضافة الأعمدة المفقودة</h2>";
    
    $required_columns = [
        'expected_end_time' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "وقت الانتهاء المتوقع للجلسة"',
        'notes' => 'TEXT NULL DEFAULT NULL COMMENT "ملاحظات الجلسة"',
        'game_type' => 'ENUM("single","multiplayer") DEFAULT "single" COMMENT "نوع اللعب: فردي أو زوجي"',
        'client_id' => 'INT NULL DEFAULT NULL COMMENT "معرف العميل"',
        'customer_id' => 'INT NULL DEFAULT NULL COMMENT "معرف الزبون"',
        'total_cost' => 'DECIMAL(10,2) DEFAULT 0.00 COMMENT "التكلفة الإجمالية"',
        'created_by' => 'INT NULL DEFAULT NULL COMMENT "منشئ الجلسة"',
        'updated_by' => 'INT NULL DEFAULT NULL COMMENT "محدث الجلسة"'
    ];

    $fixes_applied = 0;
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            echo "<p style='color: orange;'>⚠️ عمود '$column_name' غير موجود - جاري الإضافة...</p>";
            
            try {
                $sql = "ALTER TABLE sessions ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column_name' بنجاح</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود '$column_name': " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column_name' موجود مسبقاً</p>";
        }
    }

    echo "<h2>3. فحص الفهارس والمفاتيح الخارجية</h2>";
    
    // إضافة الفهارس إذا لم تكن موجودة
    $indexes_to_add = [
        'idx_sessions_client_id' => 'CREATE INDEX idx_sessions_client_id ON sessions(client_id)',
        'idx_sessions_customer_id' => 'CREATE INDEX idx_sessions_customer_id ON sessions(customer_id)',
        'idx_sessions_status' => 'CREATE INDEX idx_sessions_status ON sessions(status)',
        'idx_sessions_start_time' => 'CREATE INDEX idx_sessions_start_time ON sessions(start_time)'
    ];

    foreach ($indexes_to_add as $index_name => $index_sql) {
        try {
            // فحص وجود الفهرس
            $stmt = $pdo->query("SHOW INDEX FROM sessions WHERE Key_name = '$index_name'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($index_sql);
                echo "<p style='color: green;'>✅ تم إضافة فهرس '$index_name'</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: blue;'>ℹ️ فهرس '$index_name' موجود مسبقاً</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ تحذير: لم يتم إضافة فهرس '$index_name': " . $e->getMessage() . "</p>";
        }
    }

    echo "<h2>4. اختبار إنشاء جلسة جديدة</h2>";
    
    // اختبار إنشاء جلسة تجريبية
    try {
        // البحث عن جهاز متاح للاختبار
        $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
        $device = $stmt->fetch();
        
        if ($device) {
            $test_sql = "INSERT INTO sessions 
                (device_id, client_id, customer_id, start_time, expected_end_time, status, notes, game_type, created_by) 
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 1 HOUR), 'active', 'جلسة اختبار', 'single', ?)";
            
            $stmt = $pdo->prepare($test_sql);
            $stmt->execute([
                $device['device_id'],
                1, // client_id
                null, // customer_id
                1 // created_by
            ]);
            
            $test_session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة اختبار برقم: $test_session_id</p>";
            
            // حذف الجلسة التجريبية
            $pdo->exec("DELETE FROM sessions WHERE session_id = $test_session_id");
            echo "<p style='color: blue;'>ℹ️ تم حذف الجلسة التجريبية</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أجهزة متاحة للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ فشل في اختبار إنشاء الجلسة: " . $e->getMessage() . "</p>";
    }

    echo "<hr>";
    echo "<h2>ملخص الإصلاحات</h2>";
    echo "<p style='color: green; font-size: 18px;'><strong>تم تطبيق $fixes_applied إصلاح بنجاح</strong></p>";
    
    if ($fixes_applied > 0) {
        echo "<p style='color: blue;'>✅ يمكنك الآن استخدام النظام بدون مشاكل في عمود expected_end_time</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ جميع الأعمدة موجودة مسبقاً - لا حاجة لإصلاحات</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>وجود قاعدة البيانات (station أو playgood)</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
p {
    margin: 10px 0;
    padding: 8px;
    border-radius: 4px;
}
ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>
