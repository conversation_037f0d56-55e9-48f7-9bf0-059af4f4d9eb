# نظام تخصيص المظهر والألوان
## Theme Customization System

### نظرة عامة
تم إضافة نظام شامل لتخصيص مظهر الموقع يسمح للعملاء بتخصيص الألوان وشكل الهيدر حسب تفضيلاتهم.

### الميزات الرئيسية

#### 1. تخصيص الألوان
- **اللون الأساسي**: يؤثر على الهيدر والأزرار الرئيسية
- **اللون الثانوي**: يؤثر على النصوص الثانوية والحدود
- **اللون المميز**: يؤثر على عناصر التمييز والتأكيدات

#### 2. أنماط الهيدر
- **هيدر علوي مثبت**: النمط التقليدي في أعلى الصفحة
- **قائمة جانبية**: قائمة تنقل جانبية (يمين أو يسار)

#### 3. ألوان محددة مسبقاً
- الأزرق الكلاسيكي
- الأحمر النشط
- الأخضر الطبيعي
- البنفسجي الملكي
- البرتقالي المشرق
- الأزرق السماوي
- الرمادي الداكن

#### 4. أنماط العرض
- النمط الفاتح
- النمط الداكن
- التلقائي (حسب إعدادات النظام)

### الملفات المضافة

#### 1. قاعدة البيانات
```sql
CREATE TABLE client_theme_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    primary_color VARCHAR(7) DEFAULT '#0d6efd',
    secondary_color VARCHAR(7) DEFAULT '#6c757d',
    accent_color VARCHAR(7) DEFAULT '#20c997',
    header_style ENUM('top', 'sidebar') DEFAULT 'top',
    sidebar_position ENUM('right', 'left') DEFAULT 'right',
    theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_client (client_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);
```

#### 2. ملفات PHP
- `client/api/theme-css.php`: ملف CSS ديناميكي لتطبيق الألوان المخصصة
- `client/api/save_theme_settings.php`: API لحفظ إعدادات المظهر
- `client/api/get_theme_settings.php`: API لجلب إعدادات المظهر

#### 3. ملفات JavaScript
- `client/assets/js/theme-customizer.js`: نظام إدارة تخصيص المظهر

#### 4. تحديثات الملفات الموجودة
- `client/settings.php`: إضافة تبويب تخصيص المظهر
- `client/includes/header.php`: تضمين CSS المخصص

### كيفية الاستخدام

#### 1. الوصول لإعدادات المظهر
1. انتقل إلى صفحة الإعدادات
2. اختر تبويب "تخصيص المظهر"

#### 2. تخصيص الألوان
1. استخدم منتقي الألوان لاختيار الألوان المفضلة
2. أو اختر من الألوان المحددة مسبقاً
3. شاهد المعاينة الفورية للتغييرات

#### 3. تغيير نمط الهيدر
1. اختر بين "هيدر علوي مثبت" أو "قائمة جانبية"
2. في حالة اختيار القائمة الجانبية، حدد الموضع (يمين/يسار)

#### 4. حفظ الإعدادات
1. اضغط على "حفظ إعدادات المظهر"
2. ستتم إعادة تحميل الصفحة لتطبيق التغييرات

### الميزات التقنية

#### 1. CSS ديناميكي
- يتم إنشاء CSS مخصص لكل عميل
- يدعم المتغيرات CSS للمرونة
- يتضمن ألوان مشتقة (فاتح/داكن)

#### 2. معاينة فورية
- تحديث المعاينة عند تغيير الألوان
- عرض تأثير الألوان على العناصر المختلفة

#### 3. استجابة للأجهزة المحمولة
- القائمة الجانبية تتحول لهيدر علوي على الشاشات الصغيرة
- تحسينات خاصة للأجهزة اللوحية والهواتف

#### 4. حفظ آمن
- التحقق من صحة الألوان
- حماية من SQL Injection
- معالجة الأخطاء الشاملة

### إعادة التعيين
يمكن إعادة تعيين جميع الإعدادات للقيم الافتراضية باستخدام زر "إعادة تعيين للافتراضي".

### الدعم والصيانة
- جميع الإعدادات محفوظة في قاعدة البيانات
- يمكن استعادة الإعدادات عند إعادة تسجيل الدخول
- النظام متوافق مع جميع المتصفحات الحديثة

### ملاحظات مهمة
1. تأكد من وجود صلاحيات الكتابة في مجلد `client/api/`
2. يُنصح بعمل نسخة احتياطية قبل تطبيق تغييرات كبيرة
3. بعض التغييرات قد تتطلب إعادة تحميل الصفحة لتظهر بالكامل

### استكشاف الأخطاء
- إذا لم تظهر الألوان، تحقق من تحميل ملف `theme-css.php`
- إذا لم تحفظ الإعدادات، تحقق من صلاحيات قاعدة البيانات
- في حالة مشاكل القائمة الجانبية، تحقق من CSS المخصص
