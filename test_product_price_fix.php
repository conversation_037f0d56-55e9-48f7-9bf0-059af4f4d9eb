<?php
// اختبار إصلاح مشكلة عرض أسعار المنتجات
session_start();

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // معرف عميل تجريبي
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح أسعار المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-flask text-primary"></i>
                    اختبار إصلاح أسعار المنتجات
                </h1>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <p>هذا الاختبار يتحقق من:</p>
                        <ul>
                            <li>جلب المنتجات من API</li>
                            <li>عرض الأسعار بشكل صحيح (رقم وليس 0)</li>
                            <li>التأكد من أن السعر يُرسل كرقم في JavaScript</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-play"></i> تشغيل الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testProductPrices()">
                            <i class="fas fa-play"></i> اختبار أسعار المنتجات
                        </button>
                        <div id="test-results" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> عرض المنتجات</h5>
                    </div>
                    <div class="card-body">
                        <div id="products-display" class="row"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testProductPrices() {
            const resultsDiv = document.getElementById('test-results');
            const productsDiv = document.getElementById('products-display');
            
            resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</div>';
            productsDiv.innerHTML = '';
            
            fetch('client/api/get_products.php?page=1')
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('API Response:', data);
                    
                    if (data.success) {
                        const products = data.products || data.data.products;
                        
                        if (products && products.length > 0) {
                            let testResults = '<div class="alert alert-success"><h6><i class="fas fa-check"></i> نتائج الاختبار:</h6><ul>';
                            let allPricesValid = true;
                            
                            products.forEach((product, index) => {
                                const priceType = typeof product.price;
                                const priceValue = product.price;
                                const isValidPrice = priceType === 'number' && priceValue > 0;
                                
                                if (!isValidPrice) allPricesValid = false;
                                
                                testResults += `<li>المنتج ${index + 1}: ${product.name} - السعر: ${priceValue} (نوع: ${priceType}) ${isValidPrice ? '✅' : '❌'}</li>`;
                            });
                            
                            testResults += '</ul>';
                            testResults += allPricesValid ? 
                                '<p class="text-success"><strong>✅ جميع الأسعار صحيحة!</strong></p>' : 
                                '<p class="text-danger"><strong>❌ يوجد أسعار غير صحيحة!</strong></p>';
                            testResults += '</div>';
                            
                            resultsDiv.innerHTML = testResults;
                            
                            // عرض المنتجات
                            const productsHtml = products.slice(0, 6).map(product => `
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">${product.name}</h6>
                                            <p class="card-text text-primary fw-bold">
                                                ${product.price_formatted || product.price} ج.م
                                            </p>
                                            <small class="text-muted">
                                                السعر الخام: ${product.price} (${typeof product.price})
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            `).join('');
                            
                            productsDiv.innerHTML = productsHtml;
                            
                        } else {
                            resultsDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> لا توجد منتجات للاختبار</div>';
                        }
                    } else {
                        throw new Error(data.error || 'فشل في جلب المنتجات');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    resultsDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                });
        }
    </script>
</body>
</html>
