<?php
/**
 * ملف اختبار صلاحيات النسخ الاحتياطي
 * لاختبار الميزة الجديدة للتحكم في صلاحيات العملاء
 */

require_once 'config/database.php';
require_once 'includes/backup_permissions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار صلاحيات النسخ الاحتياطي</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h4 class='mb-0'><i class='fas fa-test-tube me-2'></i>اختبار صلاحيات النسخ الاحتياطي</h4>";
echo "</div>";
echo "<div class='card-body'>";

// اختبار إنشاء الجدول
echo "<h5 class='text-success'>1. اختبار إنشاء جدول admin_settings:</h5>";
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إنشاء الجدول بنجاح</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage() . "</div>";
}

// اختبار إدراج الإعدادات الافتراضية
echo "<h5 class='text-success'>2. اختبار إدراج الإعدادات الافتراضية:</h5>";
try {
    $pdo->exec("
        INSERT IGNORE INTO admin_settings (setting_key, setting_value, setting_description) VALUES
        ('backup_enabled', '1', 'تفعيل صلاحية النسخ الاحتياطي للعملاء'),
        ('system_maintenance', '0', 'وضع الصيانة للنظام'),
        ('max_backup_files', '10', 'الحد الأقصى لعدد ملفات النسخ الاحتياطي المحفوظة')
    ");
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إدراج الإعدادات الافتراضية بنجاح</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage() . "</div>";
}

// اختبار الدوال
echo "<h5 class='text-success'>3. اختبار الدوال:</h5>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card border-info'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title'>حالة النسخ الاحتياطي</h6>";
$backupEnabled = isBackupEnabled();
if ($backupEnabled) {
    echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>مفعل</span>";
} else {
    echo "<span class='badge bg-danger fs-6'><i class='fas fa-times me-1'></i>معطل</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card border-warning'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title'>وضع الصيانة</h6>";
$maintenanceMode = isSystemInMaintenance();
if ($maintenanceMode) {
    echo "<span class='badge bg-warning fs-6'><i class='fas fa-tools me-1'></i>نشط</span>";
} else {
    echo "<span class='badge bg-success fs-6'><i class='fas fa-check me-1'></i>غير نشط</span>";
}
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='mt-3'>";
echo "<div class='card border-secondary'>";
echo "<div class='card-body'>";
echo "<h6 class='card-title'>الحد الأقصى لملفات النسخ الاحتياطي</h6>";
$maxFiles = getMaxBackupFiles();
echo "<span class='badge bg-secondary fs-6'>" . $maxFiles . " ملف</span>";
echo "</div>";
echo "</div>";
echo "</div>";

// عرض جميع الإعدادات
echo "<h5 class='text-success mt-4'>4. جميع الإعدادات الحالية:</h5>";
$allSettings = getAdminSettings();
echo "<div class='table-responsive'>";
echo "<table class='table table-striped'>";
echo "<thead class='table-dark'>";
echo "<tr><th>المفتاح</th><th>القيمة</th></tr>";
echo "</thead>";
echo "<tbody>";
foreach ($allSettings as $key => $value) {
    echo "<tr>";
    echo "<td><code>" . htmlspecialchars($key) . "</code></td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "</tr>";
}
echo "</tbody>";
echo "</table>";
echo "</div>";

// اختبار الرسائل
echo "<h5 class='text-success mt-4'>5. اختبار الرسائل:</h5>";
echo "<h6>رسالة تعطيل النسخ الاحتياطي:</h6>";
echo getBackupDisabledMessage();

echo "<h6>رسالة وضع الصيانة:</h6>";
echo getMaintenanceModeMessage();

// نموذج اختبار تحديث الإعدادات
echo "<h5 class='text-success mt-4'>6. اختبار تحديث الإعدادات:</h5>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_update'])) {
    $testKey = $_POST['test_key'];
    $testValue = $_POST['test_value'];
    
    if (updateAdminSetting($testKey, $testValue)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم تحديث الإعداد بنجاح</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>فشل في تحديث الإعداد</div>";
    }
}

echo "<form method='POST' class='mt-3'>";
echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<select name='test_key' class='form-select' required>";
echo "<option value=''>اختر الإعداد</option>";
echo "<option value='backup_enabled'>تفعيل النسخ الاحتياطي</option>";
echo "<option value='system_maintenance'>وضع الصيانة</option>";
echo "<option value='max_backup_files'>الحد الأقصى للملفات</option>";
echo "</select>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<input type='text' name='test_value' class='form-control' placeholder='القيمة الجديدة' required>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<button type='submit' name='test_update' class='btn btn-primary'>";
echo "<i class='fas fa-save me-2'></i>تحديث";
echo "</button>";
echo "</div>";
echo "</div>";
echo "</form>";

echo "</div>"; // card-body
echo "</div>"; // card

echo "<div class='mt-3 text-center'>";
echo "<a href='admin/settings.php' class='btn btn-success me-2'>";
echo "<i class='fas fa-cog me-2'></i>إعدادات الأدمن";
echo "</a>";
echo "<a href='client/settings.php' class='btn btn-info'>";
echo "<i class='fas fa-user-cog me-2'></i>إعدادات العميل";
echo "</a>";
echo "</div>";

echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
