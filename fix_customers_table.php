<?php
/**
 * سكريپت إصلاح جدول العملاء
 * يتحقق من هيكل الجدول ويصلحه
 */

require_once 'config/database.php';

echo "<h1>إصلاح جدول العملاء - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    echo "<h2>1. فحص جدول customers</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول customers غير موجود</p>";
        
        // إنشاء الجدول
        $create_table_sql = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                client_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customers_client_id (client_id),
                INDEX idx_customers_phone (phone)
            )
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول customers موجود</p>";
    }
    
    echo "<h2>2. فحص هيكل الجدول</h2>";
    
    // فحص أعمدة الجدول
    $stmt = $pdo->query("DESCRIBE customers");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $column_names = array_column($columns, 'Field');
    
    echo "<h3>الأعمدة الموجودة:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    // التحقق من الأعمدة المطلوبة
    $required_columns = [
        'customer_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(100) NOT NULL',
        'phone' => 'VARCHAR(20)',
        'email' => 'VARCHAR(100)',
        'client_id' => 'INT NOT NULL'
    ];
    
    echo "<h3>فحص الأعمدة المطلوبة:</h3>";
    
    foreach ($required_columns as $column_name => $column_type) {
        if (in_array($column_name, $column_names)) {
            echo "<p style='color: green;'>✅ عمود $column_name موجود</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ عمود $column_name غير موجود - جاري الإضافة...</p>";
            
            try {
                switch ($column_name) {
                    case 'customer_id':
                        // إذا لم يكن هناك عمود ID، نضيفه
                        if (!in_array('id', $column_names)) {
                            $pdo->exec("ALTER TABLE customers ADD COLUMN customer_id INT AUTO_INCREMENT PRIMARY KEY FIRST");
                            echo "<p style='color: green;'>✅ تم إضافة عمود customer_id</p>";
                        }
                        break;
                    case 'client_id':
                        $pdo->exec("ALTER TABLE customers ADD COLUMN client_id INT NOT NULL DEFAULT 1");
                        echo "<p style='color: green;'>✅ تم إضافة عمود client_id</p>";
                        break;
                    case 'name':
                        $pdo->exec("ALTER TABLE customers ADD COLUMN name VARCHAR(100) NOT NULL");
                        echo "<p style='color: green;'>✅ تم إضافة عمود name</p>";
                        break;
                    case 'phone':
                        $pdo->exec("ALTER TABLE customers ADD COLUMN phone VARCHAR(20)");
                        echo "<p style='color: green;'>✅ تم إضافة عمود phone</p>";
                        break;
                    case 'email':
                        $pdo->exec("ALTER TABLE customers ADD COLUMN email VARCHAR(100)");
                        echo "<p style='color: green;'>✅ تم إضافة عمود email</p>";
                        break;
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>3. التحقق من وجود عمود id القديم</h2>";
    
    if (in_array('id', $column_names) && !in_array('customer_id', $column_names)) {
        echo "<p style='color: blue;'>ℹ️ يوجد عمود id بدلاً من customer_id</p>";
        
        // إضافة عمود customer_id وتحديث البيانات
        try {
            $pdo->exec("ALTER TABLE customers ADD COLUMN customer_id INT");
            $pdo->exec("UPDATE customers SET customer_id = id");
            $pdo->exec("ALTER TABLE customers MODIFY customer_id INT AUTO_INCREMENT PRIMARY KEY");
            echo "<p style='color: green;'>✅ تم إنشاء عمود customer_id من عمود id</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ سنستخدم عمود id الموجود</p>";
        }
    }
    
    echo "<h2>4. إضافة فهارس</h2>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_customers_client_id ON customers(client_id)" => "فهرس client_id",
        "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)" => "فهرس phone"
    ];
    
    foreach ($indexes as $sql => $description) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ $description</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ $description: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>5. إضافة بيانات تجريبية (إذا كان الجدول فارغ)</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM customers");
    $customer_count = $stmt->fetchColumn();
    
    if ($customer_count == 0) {
        echo "<p style='color: blue;'>ℹ️ الجدول فارغ - جاري إضافة بيانات تجريبية...</p>";
        
        $sample_customers = [
            ['أحمد محمد', '01234567890', '<EMAIL>'],
            ['فاطمة علي', '01234567891', '<EMAIL>'],
            ['محمد حسن', '01234567892', '<EMAIL>'],
            ['سارة أحمد', '01234567893', '<EMAIL>'],
            ['عمر خالد', '01234567894', '<EMAIL>']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, 1)");
        
        foreach ($sample_customers as $customer) {
            try {
                $insert_stmt->execute($customer);
                echo "<p style='color: green;'>✅ تم إضافة العميل: " . $customer[0] . "</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة العميل " . $customer[0] . ": " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ يوجد $customer_count عميل في الجدول</p>";
    }
    
    echo "<h2>6. اختبار الاستعلامات</h2>";
    
    // اختبار استعلام العملاء
    try {
        $test_query = "SELECT * FROM customers WHERE client_id = 1 LIMIT 5";
        $stmt = $pdo->query($test_query);
        $customers = $stmt->fetchAll();
        
        echo "<p style='color: green;'>✅ استعلام العملاء يعمل بشكل صحيح</p>";
        echo "<p>تم جلب " . count($customers) . " عميل</p>";
        
        if (count($customers) > 0) {
            echo "<h4>عينة من العملاء:</h4>";
            echo "<ul>";
            foreach ($customers as $customer) {
                $id_field = isset($customer['customer_id']) ? $customer['customer_id'] : $customer['id'];
                echo "<li>ID: $id_field - " . htmlspecialchars($customer['name']) . " - " . htmlspecialchars($customer['phone']) . "</li>";
            }
            echo "</ul>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار استعلام العملاء: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>✅ تم الانتهاء من إصلاح جدول العملاء</h2>";
    echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار لوحة التحكم</a></p>";
    echo "<p><a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>اختبار صفحة الجلسات</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "</div>";
?>
