<?php
/**
 * إضافة عمود طريقة الحساب لجدول invoice_settings
 * يسمح للعميل بالاختيار بين:
 * - hourly_rounding: المحاسبة من أول دقيقة بسعر الساعة (تقريب للساعة الكاملة)
 * - actual_time: الحساب على حسب الوقت الفعلي
 */

require_once 'config/database.php';

echo "<h1>إضافة إعداد طريقة حساب التكلفة</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // التحقق من وجود جدول invoice_settings
    echo "<h3>🔍 التحقق من وجود جدول invoice_settings</h3>";
    
    $table_check = $pdo->query("SHOW TABLES LIKE 'invoice_settings'");
    if ($table_check->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول invoice_settings غير موجود. سيتم إنشاؤه أولاً.</p>";
        
        // إنشاء الجدول
        $create_table_sql = "
            CREATE TABLE invoice_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                header_color VARCHAR(7) DEFAULT '#dc3545',
                footer_text TEXT DEFAULT 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
                footer_color VARCHAR(7) DEFAULT '#000000',
                company_address TEXT,
                company_phone VARCHAR(20),
                show_qr_code BOOLEAN DEFAULT TRUE,
                billing_method ENUM('hourly_rounding', 'actual_time') DEFAULT 'actual_time' COMMENT 'طريقة حساب التكلفة',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_client_settings (client_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول invoice_settings مع العمود الجديد</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول invoice_settings موجود</p>";
        
        // التحقق من وجود العمود billing_method
        echo "<h3>🔍 التحقق من وجود عمود billing_method</h3>";
        
        $column_check = $pdo->query("SHOW COLUMNS FROM invoice_settings LIKE 'billing_method'");
        if ($column_check->rowCount() == 0) {
            echo "<p style='color: orange;'>⚠️ عمود billing_method غير موجود. سيتم إضافته.</p>";
            
            // إضافة العمود
            $add_column_sql = "
                ALTER TABLE invoice_settings 
                ADD COLUMN billing_method ENUM('hourly_rounding', 'actual_time') DEFAULT 'actual_time' 
                COMMENT 'طريقة حساب التكلفة: hourly_rounding = تقريب للساعة الكاملة, actual_time = الوقت الفعلي'
                AFTER show_qr_code
            ";
            
            $pdo->exec($add_column_sql);
            echo "<p style='color: green;'>✅ تم إضافة عمود billing_method بنجاح</p>";
        } else {
            echo "<p style='color: green;'>✅ عمود billing_method موجود بالفعل</p>";
        }
    }
    
    // عرض هيكل الجدول الحالي
    echo "<h3>📋 هيكل جدول invoice_settings الحالي</h3>";
    
    $columns = $pdo->query("DESCRIBE invoice_settings")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<thead>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>اسم العمود</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>النوع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>القيمة الافتراضية</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>ملاحظات</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
        
        if ($column['Field'] == 'billing_method') {
            echo "<strong style='color: green;'>العمود الجديد لطريقة الحساب</strong>";
        } else {
            echo htmlspecialchars($column['Comment'] ?? '-');
        }
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // تحديث الإعدادات الموجودة للعملاء الحاليين
    echo "<h3>🔄 تحديث الإعدادات للعملاء الحاليين</h3>";
    
    $existing_settings = $pdo->query("SELECT client_id, billing_method FROM invoice_settings")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($existing_settings) > 0) {
        echo "<p>تم العثور على " . count($existing_settings) . " عميل لديه إعدادات فواتير:</p>";
        
        $updated_count = 0;
        foreach ($existing_settings as $setting) {
            if (is_null($setting['billing_method'])) {
                // تحديث العملاء الذين ليس لديهم إعداد billing_method
                $update_stmt = $pdo->prepare("UPDATE invoice_settings SET billing_method = 'actual_time' WHERE client_id = ?");
                $update_stmt->execute([$setting['client_id']]);
                $updated_count++;
            }
        }
        
        if ($updated_count > 0) {
            echo "<p style='color: green;'>✅ تم تحديث إعدادات $updated_count عميل ليستخدم 'actual_time' كإعداد افتراضي</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ جميع العملاء لديهم إعداد billing_method محدد بالفعل</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد إعدادات فواتير للعملاء حالياً</p>";
    }
    
    // شرح طرق الحساب
    echo "<hr>";
    echo "<h3>📖 شرح طرق الحساب</h3>";
    
    echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";
    
    // طريقة التقريب للساعة
    echo "<div style='flex: 1; background: #fff3cd; padding: 20px; border-radius: 10px; border-left: 5px solid #ffc107;'>";
    echo "<h4 style='color: #856404; margin-top: 0;'>🕐 hourly_rounding</h4>";
    echo "<p><strong>المحاسبة من أول دقيقة بسعر الساعة</strong></p>";
    echo "<ul>";
    echo "<li>يتم تقريب أي جزء من الساعة للساعة الكاملة</li>";
    echo "<li>مثال: 10 دقائق = ساعة كاملة</li>";
    echo "<li>مثال: 75 دقيقة = ساعتان كاملتان</li>";
    echo "<li>مناسب للمحلات التي تفضل النظام التقليدي</li>";
    echo "</ul>";
    echo "</div>";
    
    // طريقة الوقت الفعلي
    echo "<div style='flex: 1; background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #17a2b8;'>";
    echo "<h4 style='color: #0c5460; margin-top: 0;'>⏱️ actual_time</h4>";
    echo "<p><strong>الحساب على حسب الوقت الفعلي</strong></p>";
    echo "<ul>";
    echo "<li>يتم حساب التكلفة بناءً على الدقائق الفعلية</li>";
    echo "<li>مثال: 10 دقائق = 1/6 ساعة</li>";
    echo "<li>مثال: 75 دقيقة = 1.25 ساعة</li>";
    echo "<li>أكثر عدالة وشفافية للعملاء</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ تم إعداد طريقة حساب التكلفة بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>الآن يمكن للعملاء اختيار طريقة الحساب المفضلة لديهم من إعدادات الفاتورة.</strong></p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4>📝 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>تعديل صفحة إعدادات الفاتورة لإضافة خيار طريقة الحساب</li>";
    echo "<li>تعديل API حفظ الإعدادات</li>";
    echo "<li>تعديل جميع أماكن حساب التكلفة لتعتمد على الإعداد المحدد</li>";
    echo "<li>اختبار النظام مع كلا الطريقتين</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background: white;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}
</style>
