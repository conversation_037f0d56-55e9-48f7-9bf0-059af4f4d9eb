<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات - فقط المديرين يمكنهم إدارة الموظفين
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_employees')) {
    header('Location: dashboard.php?error=no_permission');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الموظف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: employees.php?error=invalid_employee');
    exit;
}

$employee_id = (int)$_GET['id'];

// جلب بيانات الموظف
try {
    $employee_query = $pdo->prepare("
        SELECT * FROM employees 
        WHERE id = ? AND client_id = ?
    ");
    $employee_query->execute([$employee_id, $client_id]);
    $employee = $employee_query->fetch();
    
    if (!$employee) {
        header('Location: employees.php?error=employee_not_found');
        exit;
    }
} catch (PDOException $e) {
    header('Location: employees.php?error=database_error');
    exit;
}

// معالجة تحديث الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions'])) {
    try {
        $use_custom_permissions = isset($_POST['use_custom_permissions']) ? 1 : 0;
        $selected_permissions = $_POST['permissions'] ?? [];
        $selected_pages = $_POST['pages'] ?? [];

        $pdo->beginTransaction();

        // تحديث نوع الصلاحيات
        $stmt = $pdo->prepare("UPDATE employees SET custom_permissions = ? WHERE id = ? AND client_id = ?");
        $stmt->execute([$use_custom_permissions, $employee_id, $client_id]);

        if ($use_custom_permissions) {
            // حذف الصلاحيات الحالية
            $stmt = $pdo->prepare("DELETE FROM employee_permissions WHERE employee_id = ?");
            $stmt->execute([$employee_id]);

            // إضافة الصلاحيات الجديدة
            if (!empty($selected_permissions)) {
                $stmt = $pdo->prepare("INSERT INTO employee_permissions (employee_id, permission_id) VALUES (?, ?)");
                foreach ($selected_permissions as $permission_id) {
                    $stmt->execute([$employee_id, $permission_id]);
                }
            }

            // حذف الصفحات الحالية
            $stmt = $pdo->prepare("DELETE FROM employee_pages WHERE employee_id = ?");
            $stmt->execute([$employee_id]);

            // إضافة الصفحات الجديدة
            if (!empty($selected_pages)) {
                $stmt = $pdo->prepare("INSERT INTO employee_pages (employee_id, page_id) VALUES (?, ?)");
                foreach ($selected_pages as $page_id) {
                    $stmt->execute([$employee_id, $page_id]);
                }
            }
        }

        $pdo->commit();
        $_SESSION['success'] = "تم تحديث صلاحيات الموظف بنجاح";
        
        // إعادة جلب بيانات الموظف المحدثة
        $employee_query->execute([$employee_id, $client_id]);
        $employee = $employee_query->fetch();
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        $_SESSION['error'] = "حدث خطأ أثناء تحديث الصلاحيات: " . $e->getMessage();
    }
}

// جلب جميع الصلاحيات المتاحة
$permissions = [];
$pages = [];
try {
    $permissions = $pdo->query("
        SELECT * FROM permissions
        WHERE is_active = 1
        ORDER BY category, permission_label
    ")->fetchAll();

    $pages = $pdo->query("
        SELECT * FROM pages
        WHERE is_active = 1
        ORDER BY category, page_label
    ")->fetchAll();
} catch (PDOException $e) {
    $permissions = [];
    $pages = [];
}

// تجميع الصلاحيات حسب الفئة
$permissions_by_category = [];
foreach ($permissions as $permission) {
    $permissions_by_category[$permission['category']][] = $permission;
}

// تجميع الصفحات حسب الفئة
$pages_by_category = [];
foreach ($pages as $page) {
    $pages_by_category[$page['category']][] = $page;
}

// جلب صلاحيات الموظف الحالية
$current_permissions = $pdo->prepare("
    SELECT permission_id FROM employee_permissions WHERE employee_id = ?
");
$current_permissions->execute([$employee_id]);
$current_permission_ids = $current_permissions->fetchAll(PDO::FETCH_COLUMN);

// جلب صفحات الموظف الحالية
$current_pages = $pdo->prepare("
    SELECT page_id FROM employee_pages WHERE employee_id = ?
");
$current_pages->execute([$employee_id]);
$current_page_ids = $current_pages->fetchAll(PDO::FETCH_COLUMN);

$page_title = "إدارة الصلاحيات (صفحة منفصلة) - " . $employee['name'];
$active_page = "employees";

require_once 'includes/header.php';
?>

<!-- تضمين ملف CSS المخصص للصلاحيات -->
<link rel="stylesheet" href="assets/css/permissions.css">

<style>
.employee-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.employee-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

.employee-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 20px;
}

.permission-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
}

.section-body {
    padding: 25px;
}

.quick-actions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.save-section {
    position: sticky;
    bottom: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
    padding: 20px;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .employee-header {
        text-align: center;
    }
    
    .employee-avatar {
        margin: 0 auto 15px;
    }
    
    .permission-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- مؤشر الصفحة المنفصلة -->
    <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-external-link-alt fa-2x me-3"></i>
            <div>
                <h6 class="alert-heading mb-1">
                    <i class="fas fa-star me-1"></i>صفحة إدارة الصلاحيات المنفصلة
                </h6>
                <p class="mb-0">
                    تم تطوير هذه الصفحة لتكون منفصلة بدلاً من النوافذ المنبثقة لتوفير مساحة أكبر وتجربة أفضل في إدارة الصلاحيات
                </p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- رأس الصفحة مع معلومات الموظف -->
    <div class="employee-header">
        <div class="d-flex align-items-center">
            <div class="employee-avatar">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="flex-grow-1">
                <h2 class="mb-2">
                    <i class="fas fa-shield-alt me-2"></i>
                    إدارة صلاحيات: <?php echo htmlspecialchars($employee['name']); ?>
                </h2>
                <p class="mb-2 opacity-75">
                    <i class="fas fa-info-circle me-1"></i>
                    صفحة منفصلة لإدارة جميع صلاحيات الموظف بشكل مفصل ومنظم
                </p>
                <div class="row">
                    <div class="col-md-3">
                        <small class="opacity-75">الوظيفة:</small><br>
                        <span class="badge bg-light text-dark fs-6">
                            <?php echo match($employee['role']) {
                                'manager' => 'مدير',
                                'cashier' => 'كاشير',
                                'waiter' => 'ويتر',
                                'cleaner' => 'عامل نظافة',
                                default => $employee['role']
                            }; ?>
                        </span>
                    </div>
                    <div class="col-md-3">
                        <small class="opacity-75">رقم الهاتف:</small><br>
                        <span><?php echo htmlspecialchars($employee['phone']); ?></span>
                    </div>
                    <div class="col-md-3">
                        <small class="opacity-75">تاريخ التعيين:</small><br>
                        <span><?php echo date('Y-m-d', strtotime($employee['hire_date'])); ?></span>
                    </div>
                    <div class="col-md-3">
                        <small class="opacity-75">نوع الصلاحيات:</small><br>
                        <span class="badge <?php echo $employee['custom_permissions'] ? 'bg-info' : 'bg-secondary'; ?>">
                            <?php echo $employee['custom_permissions'] ? 'صلاحيات مخصصة' : 'صلاحيات الدور'; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div>
                <a href="employees.php" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
                </a>
                <div class="mt-2">
                    <small class="text-light opacity-75">
                        <i class="fas fa-external-link-alt me-1"></i>
                        صفحة منفصلة - لا حاجة لنوافذ منبثقة
                    </small>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="permissionsForm">
        <input type="hidden" name="employee_id" value="<?php echo $employee_id; ?>">
        
        <!-- قسم نوع الصلاحيات -->
        <div class="permission-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>نوع نظام الصلاحيات
                </h5>
            </div>
            <div class="section-body">
                <div class="custom-permissions-toggle">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox"
                               name="use_custom_permissions"
                               id="customPermissions"
                               <?php echo $employee['custom_permissions'] ? 'checked' : ''; ?>
                               onchange="toggleCustomPermissions()">
                        <label class="form-check-label" for="customPermissions">
                            <strong><i class="fas fa-magic me-2"></i>استخدام صلاحيات مخصصة</strong>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    عند التفعيل: يمكنك تحديد صلاحيات وصفحات مخصصة للموظف<br>
                                    <i class="fas fa-info-circle me-1"></i>
                                    عند عدم التفعيل: سيتم استخدام صلاحيات الدور التقليدية فقط
                                </small>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى الصلاحيات المخصصة -->
        <div id="customPermissionsContent" style="display: <?php echo $employee['custom_permissions'] ? 'block' : 'none'; ?>">

            <!-- إحصائيات سريعة -->
            <div class="permission-section">
                <div class="section-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات الصلاحيات
                    </h5>
                </div>
                <div class="section-body">
                    <div class="permission-stats">
                        <div class="permission-stat">
                            <div class="stat-number" id="selectedPermissionsCount"><?php echo count($current_permission_ids); ?></div>
                            <div class="stat-label">صلاحية محددة</div>
                        </div>
                        <div class="permission-stat">
                            <div class="stat-number"><?php echo count($permissions); ?></div>
                            <div class="stat-label">إجمالي الصلاحيات</div>
                        </div>
                        <div class="permission-stat">
                            <div class="stat-number" id="selectedPagesCount"><?php echo count($current_page_ids); ?></div>
                            <div class="stat-label">صفحة مسموحة</div>
                        </div>
                        <div class="permission-stat">
                            <div class="stat-number"><?php echo count($pages); ?></div>
                            <div class="stat-label">إجمالي الصفحات</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم الصلاحيات -->
            <div class="permission-section">
                <div class="section-header">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>الصلاحيات المتاحة
                    </h5>
                </div>
                <div class="section-body">
                    <!-- أدوات التحكم السريع -->
                    <div class="quick-actions">
                        <h6 class="mb-3"><i class="fas fa-magic me-2"></i>أدوات التحكم السريع</h6>
                        <div class="permission-controls">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double me-1"></i>تحديد جميع الصلاحيات
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllPermissions()">
                                <i class="fas fa-times me-1"></i>إلغاء تحديد الكل
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="selectByCategory('devices')">
                                <i class="fas fa-gamepad me-1"></i>صلاحيات الأجهزة
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="selectByCategory('sessions')">
                                <i class="fas fa-play-circle me-1"></i>صلاحيات الجلسات
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="selectByCategory('cafeteria')">
                                <i class="fas fa-coffee me-1"></i>صلاحيات الكافتيريا
                            </button>
                        </div>
                    </div>

                    <!-- شبكة الصلاحيات -->
                    <div class="permission-grid">
                        <?php foreach ($permissions_by_category as $category => $category_permissions): ?>
                            <div class="permission-category" data-category="<?php echo $category; ?>">
                                <div class="permission-category-header">
                                    <span>
                                        <i class="<?php echo match($category) {
                                            'devices' => 'fas fa-gamepad',
                                            'sessions' => 'fas fa-play-circle',
                                            'rooms' => 'fas fa-home',
                                            'customers' => 'fas fa-users',
                                            'cafeteria' => 'fas fa-coffee',
                                            'finances' => 'fas fa-chart-line',
                                            'reports' => 'fas fa-chart-bar',
                                            'employees' => 'fas fa-user-tie',
                                            'settings' => 'fas fa-cog',
                                            default => 'fas fa-key'
                                        }; ?> category-icon me-2"></i>
                                        <?php echo match($category) {
                                            'devices' => 'إدارة الأجهزة',
                                            'sessions' => 'إدارة الجلسات',
                                            'rooms' => 'إدارة الغرف',
                                            'customers' => 'إدارة العملاء',
                                            'cafeteria' => 'إدارة الكافتيريا',
                                            'finances' => 'الماليات',
                                            'reports' => 'التقارير',
                                            'employees' => 'إدارة الموظفين',
                                            'settings' => 'الإعدادات',
                                            default => ucfirst($category)
                                        }; ?>
                                    </span>
                                    <span class="badge bg-light text-dark"><?php echo count($category_permissions); ?></span>
                                </div>
                                <div class="permission-category-body">
                                    <?php foreach ($category_permissions as $permission): ?>
                                        <div class="permission-item <?php echo in_array($permission['permission_id'], $current_permission_ids) ? 'selected' : ''; ?>">
                                            <div class="form-check">
                                                <input class="form-check-input permission-checkbox"
                                                       type="checkbox"
                                                       name="permissions[]"
                                                       value="<?php echo $permission['permission_id']; ?>"
                                                       id="perm_<?php echo $permission['permission_id']; ?>"
                                                       data-category="<?php echo $category; ?>"
                                                       <?php echo in_array($permission['permission_id'], $current_permission_ids) ? 'checked' : ''; ?>
                                                       onchange="updatePermissionItem(this)">
                                                <label class="form-check-label" for="perm_<?php echo $permission['permission_id']; ?>">
                                                    <strong><?php echo htmlspecialchars($permission['permission_label']); ?></strong>
                                                    <div class="permission-description"><?php echo htmlspecialchars($permission['permission_description']); ?></div>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- قسم الصفحات -->
            <div class="permission-section">
                <div class="section-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file me-2"></i>الصفحات المسموح بالوصول إليها
                    </h5>
                </div>
                <div class="section-body">
                    <!-- أدوات التحكم السريع للصفحات -->
                    <div class="quick-actions">
                        <h6 class="mb-3"><i class="fas fa-magic me-2"></i>أدوات التحكم في الصفحات</h6>
                        <div class="permission-controls">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllPages()">
                                <i class="fas fa-check-double me-1"></i>تحديد جميع الصفحات
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllPages()">
                                <i class="fas fa-times me-1"></i>إلغاء تحديد الكل
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="selectEssentialPages()">
                                <i class="fas fa-star me-1"></i>الصفحات الأساسية فقط
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="selectByRole('<?php echo $employee['role']; ?>')">
                                <i class="fas fa-user-tag me-1"></i>صفحات مناسبة للدور
                            </button>
                        </div>
                    </div>

                    <!-- شبكة الصفحات -->
                    <div class="permission-grid">
                        <?php foreach ($pages_by_category as $category => $category_pages): ?>
                            <div class="permission-category" data-category="<?php echo $category; ?>">
                                <div class="permission-category-header">
                                    <span>
                                        <i class="<?php echo match($category) {
                                            'devices' => 'fas fa-gamepad',
                                            'sessions' => 'fas fa-play-circle',
                                            'rooms' => 'fas fa-home',
                                            'customers' => 'fas fa-users',
                                            'cafeteria' => 'fas fa-coffee',
                                            'finances' => 'fas fa-chart-line',
                                            'reports' => 'fas fa-chart-bar',
                                            'employees' => 'fas fa-user-tie',
                                            'settings' => 'fas fa-cog',
                                            default => 'fas fa-file'
                                        }; ?> category-icon me-2"></i>
                                        <?php echo match($category) {
                                            'devices' => 'صفحات الأجهزة',
                                            'sessions' => 'صفحات الجلسات',
                                            'rooms' => 'صفحات الغرف',
                                            'customers' => 'صفحات العملاء',
                                            'cafeteria' => 'صفحات الكافتيريا',
                                            'finances' => 'صفحات الماليات',
                                            'reports' => 'صفحات التقارير',
                                            'employees' => 'صفحات الموظفين',
                                            'settings' => 'صفحات الإعدادات',
                                            default => ucfirst($category)
                                        }; ?>
                                    </span>
                                    <span class="badge bg-light text-dark"><?php echo count($category_pages); ?></span>
                                </div>
                                <div class="permission-category-body">
                                    <?php foreach ($category_pages as $page): ?>
                                        <div class="permission-item <?php echo in_array($page['page_id'], $current_page_ids) ? 'selected' : ''; ?>">
                                            <div class="form-check">
                                                <input class="form-check-input page-checkbox"
                                                       type="checkbox"
                                                       name="pages[]"
                                                       value="<?php echo $page['page_id']; ?>"
                                                       id="page_<?php echo $page['page_id']; ?>"
                                                       data-category="<?php echo $category; ?>"
                                                       data-essential="<?php echo in_array($page['page_name'], ['dashboard', 'sessions', 'devices']) ? 'true' : 'false'; ?>"
                                                       data-role-suitable="<?php echo match($employee['role']) {
                                                           'manager' => 'true',
                                                           'cashier' => in_array($page['page_name'], ['dashboard', 'sessions', 'cafeteria', 'reports']) ? 'true' : 'false',
                                                           'waiter' => in_array($page['page_name'], ['dashboard', 'cafeteria', 'sessions']) ? 'true' : 'false',
                                                           'cleaner' => in_array($page['page_name'], ['dashboard']) ? 'true' : 'false',
                                                           default => 'false'
                                                       }; ?>"
                                                       <?php echo in_array($page['page_id'], $current_page_ids) ? 'checked' : ''; ?>
                                                       onchange="updatePermissionItem(this)">
                                                <label class="form-check-label" for="page_<?php echo $page['page_id']; ?>">
                                                    <strong>
                                                        <i class="<?php echo $page['page_icon']; ?> me-2"></i>
                                                        <?php echo htmlspecialchars($page['page_label']); ?>
                                                    </strong>
                                                    <div class="permission-description"><?php echo htmlspecialchars($page['page_url']); ?></div>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- معاينة الصلاحيات المحددة -->
            <div class="permission-section">
                <div class="section-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة الصلاحيات المحددة
                    </h5>
                </div>
                <div class="section-body">
                    <div class="permissions-preview">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-key me-2"></i>الصلاحيات المحددة:</h6>
                                <div id="selectedPermissions">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-file me-2"></i>الصفحات المسموحة:</h6>
                                <div id="selectedPages">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الحفظ -->
        <div class="save-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات - صفحة منفصلة
                    </h6>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        سيتم تطبيق التغييرات فوراً بعد الحفظ وسيحتاج الموظف لتسجيل الدخول مرة أخرى
                        <br>
                        <i class="fas fa-check-circle me-1"></i>
                        <strong>مزايا الصفحة المنفصلة:</strong> مساحة أكبر، تنظيم أفضل، وسهولة في الاستخدام
                    </small>
                </div>
                <div>
                    <a href="employees.php" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" name="update_permissions" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// تبديل عرض الصلاحيات المخصصة
function toggleCustomPermissions() {
    const checkbox = document.getElementById('customPermissions');
    const content = document.getElementById('customPermissionsContent');

    if (checkbox && content) {
        if (checkbox.checked) {
            content.style.display = 'block';
            content.classList.add('slide-down');
            updatePermissionsPreview();
        } else {
            content.style.display = 'none';
            content.classList.remove('slide-down');
        }
    }
}

// تحديث عنصر الصلاحية عند التغيير
function updatePermissionItem(checkbox) {
    const item = checkbox.closest('.permission-item');
    if (checkbox.checked) {
        item.classList.add('selected');
    } else {
        item.classList.remove('selected');
    }

    updatePermissionsPreview();
    updateStats();
}

// تحديد جميع الصلاحيات
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
}

// إلغاء تحديد جميع الصلاحيات
function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        updatePermissionItem(checkbox);
    });
}

// تحديد صلاحيات حسب الفئة
function selectByCategory(category) {
    const checkboxes = document.querySelectorAll(`input[data-category="${category}"].permission-checkbox`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
}

// تحديد جميع الصفحات
function selectAllPages() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
}

// إلغاء تحديد جميع الصفحات
function deselectAllPages() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        updatePermissionItem(checkbox);
    });
}

// تحديد الصفحات الأساسية
function selectEssentialPages() {
    const checkboxes = document.querySelectorAll('input[data-essential="true"].page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
}

// تحديد صفحات مناسبة للدور
function selectByRole(role) {
    const checkboxes = document.querySelectorAll('input[data-role-suitable="true"].page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
}

// تحديث معاينة الصلاحيات
function updatePermissionsPreview() {
    const selectedPermissions = document.getElementById('selectedPermissions');
    const selectedPages = document.getElementById('selectedPages');

    // جمع الصلاحيات المحددة
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox:checked');
    const pageCheckboxes = document.querySelectorAll('.page-checkbox:checked');

    // عرض الصلاحيات
    let permissionsHtml = '';
    if (permissionCheckboxes.length > 0) {
        permissionCheckboxes.forEach(checkbox => {
            const label = checkbox.nextElementSibling.querySelector('strong').textContent;
            permissionsHtml += `<span class="badge bg-primary me-1 mb-1">${label}</span>`;
        });
    } else {
        permissionsHtml = '<span class="text-muted">لا توجد صلاحيات محددة</span>';
    }

    // عرض الصفحات
    let pagesHtml = '';
    if (pageCheckboxes.length > 0) {
        pageCheckboxes.forEach(checkbox => {
            const label = checkbox.nextElementSibling.querySelector('strong').textContent;
            pagesHtml += `<span class="badge bg-success me-1 mb-1">${label}</span>`;
        });
    } else {
        pagesHtml = '<span class="text-muted">لا توجد صفحات محددة</span>';
    }

    selectedPermissions.innerHTML = permissionsHtml;
    selectedPages.innerHTML = pagesHtml;
}

// تحديث الإحصائيات
function updateStats() {
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox:checked');
    const pageCheckboxes = document.querySelectorAll('.page-checkbox:checked');

    const permissionsCount = document.getElementById('selectedPermissionsCount');
    const pagesCount = document.getElementById('selectedPagesCount');

    if (permissionsCount) permissionsCount.textContent = permissionCheckboxes.length;
    if (pagesCount) pagesCount.textContent = pageCheckboxes.length;
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المعاينة الأولية
    updatePermissionsPreview();
    updateStats();

    // إضافة مستمعي الأحداث لجميع checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        if (checkbox.classList.contains('permission-checkbox') || checkbox.classList.contains('page-checkbox')) {
            checkbox.addEventListener('change', function() {
                updatePermissionItem(this);
            });
        }
    });

    // تحسين تجربة المستخدم - إضافة تأثيرات hover
    const permissionItems = document.querySelectorAll('.permission-item');
    permissionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إخفاء الرسائل تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
