/* ملف CSS للتحكم في شدة التأثيرات - PlayGood */
/* إعدادات مخففة للحركات والتأثيرات */

/* تخفيف تأثيرات الخلفية */
.auth-page {
    background-size: 200% 200% !important;
    animation-duration: 25s !important;
}

.auth-page::before {
    animation-duration: 30s !important;
    opacity: 0.4 !important;
}

/* تخفيف تأثيرات البطاقة */
.auth-card-enhanced {
    transition: all 0.3s ease !important;
    animation-duration: 0.6s !important;
}

.auth-card-enhanced:hover {
    transform: translateY(-5px) scale(1.01) !important;
}

.auth-card-enhanced::before {
    animation-duration: 6s !important;
}

.auth-card-enhanced::after {
    animation-duration: 12s !important;
    opacity: 0.3 !important;
}

/* تخفيف تأثيرات الأيقونة */
.auth-icon {
    animation-duration: 15s !important;
    transition: all 0.3s ease !important;
}

.auth-icon:hover {
    transform: scale(1.03) !important;
}

.auth-icon::before {
    animation-duration: 6s !important;
}

/* تخفيف تأثيرات النماذج */
.auth-form-control {
    transition: all 0.3s ease !important;
}

.auth-form-control:focus {
    transform: translateY(-1px) scale(1.005) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.auth-form-control:hover {
    transform: none !important;
}

/* تخفيف تأثيرات الأيقونات */
.auth-form-icon-enhanced {
    transition: all 0.2s ease !important;
}

.auth-form-icon-enhanced:hover {
    transform: translateY(-50%) scale(1.05) !important;
}

/* تخفيف تأثيرات الأزرار */
.auth-btn-modern {
    transition: all 0.3s ease !important;
    animation-duration: 8s !important;
}

.auth-btn-modern:hover {
    transform: translateY(-2px) scale(1.01) !important;
}

.auth-btn-modern::before {
    transition: left 0.6s ease !important;
}

/* تخفيف تأثيرات الروابط */
.auth-link-modern {
    transition: all 0.2s ease !important;
}

.auth-link-modern:hover {
    transform: translateY(-1px) scale(1.01) !important;
}

.auth-link-alt {
    transition: all 0.2s ease !important;
}

.auth-link-alt:hover {
    transform: translateY(-2px) scale(1.02) !important;
}

/* تخفيف تأثيرات العناوين */
.auth-title {
    animation-duration: 12s !important;
}

.auth-subtitle {
    animation-duration: 6s !important;
}

/* تخفيف تأثيرات التنبيهات */
.auth-alert {
    animation-duration: 0.4s !important;
}

/* إزالة التأثيرات المعقدة على الأجهزة المحمولة */
@media (max-width: 768px) {
    .auth-page,
    .auth-page::before,
    .auth-page::after {
        animation: none !important;
    }
    
    .auth-card-enhanced {
        animation: none !important;
        backdrop-filter: blur(15px) !important;
    }
    
    .auth-card-enhanced:hover {
        transform: translateY(-3px) scale(1.005) !important;
    }
    
    .auth-icon {
        animation: none !important;
    }
    
    .auth-icon::before {
        animation: none !important;
    }
    
    .auth-title,
    .auth-subtitle {
        animation: none !important;
    }
    
    .auth-btn-modern {
        animation: none !important;
    }
    
    .floating-particle {
        display: none !important;
    }
    
    .auth-particles,
    .auth-stars,
    .auth-fog {
        display: none !important;
    }
}

/* إلغاء جميع تأثيرات الاهتزاز */
@keyframes shake,
@keyframes errorShake,
@keyframes vibrate {
    0%, 100% { transform: translateX(0) !important; }
}

/* إعدادات للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .auth-page,
    .auth-page::before,
    .auth-page::after {
        animation: none !important;
    }
    
    .auth-card-enhanced,
    .auth-icon,
    .auth-title,
    .auth-subtitle,
    .auth-btn-modern {
        animation: none !important;
    }
    
    .floating-particle,
    .auth-particles,
    .auth-stars,
    .auth-fog {
        display: none !important;
    }
}

/* تحسينات الأداء */
.auth-page * {
    will-change: auto !important;
}

/* تخفيف تأثيرات الجسيمات */
.auth-particles {
    opacity: 0.3 !important;
    animation-duration: 35s !important;
}

.floating-particle {
    animation-duration: 25s !important;
}

/* تخفيف تأثيرات النجوم */
.auth-stars {
    opacity: 0.4 !important;
    animation-duration: 30s !important;
}

/* تخفيف تأثيرات الضباب */
.auth-fog {
    opacity: 0.2 !important;
    animation-duration: 20s !important;
}

/* تخفيف تأثيرات الهولوجرام */
.auth-hologram::before {
    animation-duration: 8s !important;
    opacity: 0.3 !important;
}

/* تخفيف تأثيرات الكريستال */
.auth-crystal {
    animation-duration: 6s !important;
}

/* تخفيف تأثيرات الإضاءة الجانبية */
.auth-side-glow::before,
.auth-side-glow::after {
    animation-duration: 3s !important;
    opacity: 0.2 !important;
}

/* إعدادات خاصة للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .auth-card-enhanced {
        backdrop-filter: blur(10px) !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    }
    
    .auth-form-control:focus {
        transform: none !important;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
    }
    
    .auth-btn-modern:hover {
        transform: none !important;
    }
    
    .auth-link-modern:hover,
    .auth-link-alt:hover {
        transform: none !important;
    }
}

/* إعدادات للأجهزة ذات الأداء المنخفض */
@media (max-width: 768px) and (max-height: 600px) {
    .auth-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        background-attachment: scroll !important;
    }
    
    .auth-card-enhanced {
        background: rgba(255, 255, 255, 0.9) !important;
        backdrop-filter: none !important;
        border: 1px solid rgba(102, 126, 234, 0.2) !important;
    }
}

/* منع جميع تأثيرات الاهتزاز والحركة الجانبية */
* {
    animation-name: none !important;
}

*[style*="shake"],
*[class*="shake"],
.shake,
.vibrate,
.wobble {
    animation: none !important;
    transform: none !important;
}

/* تحسينات إضافية للأداء */
.auth-page,
.auth-card-enhanced,
.auth-form-control,
.auth-btn-modern {
    transform: translateZ(0) !important; /* تفعيل تسريع الأجهزة */
}

/* تقليل استخدام المعالج الرسومي */
@media (max-width: 768px) {
    * {
        transform: none !important;
        filter: none !important;
        backdrop-filter: none !important;
        box-shadow: none !important;
    }
    
    .auth-card-enhanced {
        background: rgba(255, 255, 255, 0.95) !important;
        border: 2px solid rgba(102, 126, 234, 0.3) !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    }
    
    .auth-form-control:focus {
        border-color: var(--auth-primary) !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    }
    
    .auth-btn-modern {
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3) !important;
    }
}
