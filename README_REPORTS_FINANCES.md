# صفحتا التقارير والماليات - مشروع PlayGood

تم إنشاء صفحتين جديدتين لإدارة التقارير والماليات في مشروع PlayGood.

## 📊 صفحة التقارير (reports.php)

### المميزات:
- **تقارير شاملة**: عرض إحصائيات مفصلة للجلسات والإيرادات
- **فلترة بالتاريخ**: إمكانية تحديد فترة زمنية محددة للتقرير
- **أنواع تقارير متعددة**: ملخص عام، تفصيلي، تقرير الأجهزة، تقرير العملاء
- **رسوم بيانية تفاعلية**: رسم بياني للإيرادات اليومية باستخدام Chart.js
- **تصدير متعدد الصيغ**: تصدير PDF، Excel، وطباعة
- **تحديث تلقائي**: تحديث البيانات كل 5 دقائق

### الإحصائيات المعروضة:
- إجمالي الجلسات (مكتملة، ملغية)
- إجمالي الإيرادات
- متوسط تكلفة الجلسة
- إجمالي ساعات اللعب
- أداء الأجهزة
- أفضل العملاء
- الإيرادات اليومية

### كيفية الاستخدام:
1. اذهب إلى `client/reports.php`
2. اختر الفترة الزمنية المطلوبة
3. حدد نوع التقرير
4. اضغط "تطبيق الفلاتر"
5. استخدم أزرار التصدير لحفظ التقرير

## 💰 صفحة الماليات (finances.php)

### المميزات:
- **إدارة المصروفات**: إضافة وتتبع جميع المصروفات
- **إدارة الإيرادات الإضافية**: تسجيل إيرادات غير الجلسات
- **أنواع المصروفات والإيرادات**: تصنيف مرن للعمليات المالية
- **ملخص مالي شامل**: عرض الوضع المالي للشهر الحالي
- **واجهة سهلة الاستخدام**: نماذج منبثقة لإضافة البيانات

### الوظائف الرئيسية:
- إضافة مصروف جديد
- إضافة إيراد إضافي جديد
- إنشاء أنواع مصروفات مخصصة
- إنشاء أنواع إيرادات مخصصة
- عرض المصروفات والإيرادات الحديثة
- حساب صافي الربح تلقائياً

### الملخص المالي يشمل:
- إجمالي المصروفات (الشهر الحالي)
- إيرادات الجلسات (الشهر الحالي)
- الإيرادات الإضافية (الشهر الحالي)
- صافي الربح (الشهر الحالي)

## 🗄️ قاعدة البيانات

### الجداول الجديدة:

#### expense_types (أنواع المصروفات)
- `id`: المعرف الفريد
- `client_id`: معرف العميل
- `name`: اسم نوع المصروف
- `description`: وصف النوع
- `is_active`: حالة النشاط
- `created_at`, `updated_at`: تواريخ الإنشاء والتحديث

#### expenses (المصروفات)
- `id`: المعرف الفريد
- `client_id`: معرف العميل
- `expense_type_id`: نوع المصروف
- `amount`: المبلغ
- `description`: وصف المصروف
- `expense_date`: تاريخ المصروف
- `receipt_number`: رقم الإيصال
- `created_by`: من أضاف المصروف
- `created_at`, `updated_at`: تواريخ الإنشاء والتحديث

#### income_types (أنواع الإيرادات)
- `id`: المعرف الفريد
- `client_id`: معرف العميل
- `name`: اسم نوع الإيراد
- `description`: وصف النوع
- `is_active`: حالة النشاط
- `created_at`, `updated_at`: تواريخ الإنشاء والتحديث

#### additional_income (الإيرادات الإضافية)
- `id`: المعرف الفريد
- `client_id`: معرف العميل
- `income_type_id`: نوع الإيراد
- `amount`: المبلغ
- `description`: وصف الإيراد
- `income_date`: تاريخ الإيراد
- `receipt_number`: رقم الإيصال
- `created_by`: من أضاف الإيراد
- `created_at`, `updated_at`: تواريخ الإنشاء والتحديث

## 🚀 التثبيت والإعداد

### 1. إنشاء جداول قاعدة البيانات:
```bash
# اذهب إلى المتصفح وافتح:
http://localhost/playgood/create_finances_tables.php
```

### 2. التحقق من الصلاحيات:
تأكد من أن المستخدمين لديهم الصلاحيات المناسبة:
- `view_reports`: لعرض صفحة التقارير
- `view_finances`: لعرض صفحة الماليات

### 3. الوصول للصفحات:
- التقارير: `client/reports.php`
- الماليات: `client/finances.php`

## 📋 أنواع المصروفات الافتراضية:
- فواتير الكهرباء
- فواتير الإنترنت
- إيجار المحل
- صيانة الأجهزة
- مشتريات الكافتيريا
- رواتب الموظفين
- مصروفات إدارية
- تسويق وإعلان

## 📋 أنواع الإيرادات الافتراضية:
- إيرادات الجلسات
- مبيعات الكافتيريا
- خدمات إضافية
- تأجير قاعات
- بيع أكسسوارات

## 🔧 المتطلبات التقنية:
- PHP 7.4+
- MySQL 5.7+
- Bootstrap 5.3
- Chart.js (للرسوم البيانية)
- jsPDF (لتصدير PDF)
- SheetJS (لتصدير Excel)

## 📱 التوافق:
- متوافق مع جميع الأجهزة (Responsive Design)
- يدعم الطباعة مع إخفاء العناصر غير المطلوبة
- واجهة باللغة العربية مع دعم RTL

## 🔒 الأمان:
- التحقق من صلاحيات المستخدم
- حماية من SQL Injection
- تشفير البيانات الحساسة
- التحقق من صحة المدخلات

## 📞 الدعم:
في حالة وجود مشاكل أو استفسارات، يرجى التحقق من:
1. إعدادات قاعدة البيانات
2. صلاحيات المستخدم
3. وجود جميع الجداول المطلوبة
4. سجلات الأخطاء في PHP

---
**تم تطوير هذه الصفحات بواسطة Augment Agent لمشروع PlayGood**
