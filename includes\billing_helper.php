<?php
/**
 * دالة مساعدة لحساب التكلفة بناءً على إعدادات العميل
 */

/**
 * حساب تكلفة الوقت بناءً على إعدادات العميل
 * 
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $client_id معرف العميل
 * @param int $duration_minutes مدة الجلسة بالدقائق
 * @param float $hourly_rate السعر بالساعة
 * @return float التكلفة المحسوبة
 */
function calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate) {
    try {
        // جلب إعدادات الفاتورة للعميل
        $settings_stmt = $pdo->prepare("SELECT billing_method FROM invoice_settings WHERE client_id = ?");
        $settings_stmt->execute([$client_id]);
        $settings = $settings_stmt->fetch(PDO::FETCH_ASSOC);
        
        // الإعداد الافتراضي هو الوقت الفعلي
        $billing_method = $settings['billing_method'] ?? 'actual_time';
        
        // حساب التكلفة بناءً على طريقة الحساب
        if ($billing_method === 'hourly_rounding') {
            // تقريب للساعة الكاملة
            return $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
        } elseif ($billing_method === 'first_minute_full_hour') {
            // ساعة كاملة من أول دقيقة
            return $duration_minutes > 0 && $hourly_rate > 0 ? $hourly_rate : 0;
        } else {
            // الوقت الفعلي (actual_time)
            return $duration_minutes > 0 && $hourly_rate > 0 ? ($duration_minutes / 60) * $hourly_rate : 0;
        }
        
    } catch (PDOException $e) {
        // في حالة الخطأ، استخدم الوقت الفعلي كإعداد افتراضي
        error_log("Error getting billing method for client $client_id: " . $e->getMessage());
        return $duration_minutes > 0 && $hourly_rate > 0 ? ($duration_minutes / 60) * $hourly_rate : 0;
    }
}

/**
 * جلب طريقة الحساب للعميل
 * 
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $client_id معرف العميل
 * @return string طريقة الحساب ('actual_time' أو 'hourly_rounding')
 */
function getBillingMethod($pdo, $client_id) {
    try {
        $settings_stmt = $pdo->prepare("SELECT billing_method FROM invoice_settings WHERE client_id = ?");
        $settings_stmt->execute([$client_id]);
        $settings = $settings_stmt->fetch(PDO::FETCH_ASSOC);
        
        return $settings['billing_method'] ?? 'actual_time';
        
    } catch (PDOException $e) {
        error_log("Error getting billing method for client $client_id: " . $e->getMessage());
        return 'actual_time'; // الإعداد الافتراضي
    }
}

/**
 * حساب التكلفة مع تفاصيل إضافية
 * 
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $client_id معرف العميل
 * @param int $duration_minutes مدة الجلسة بالدقائق
 * @param float $hourly_rate السعر بالساعة
 * @return array مصفوفة تحتوي على التكلفة وتفاصيل الحساب
 */
function calculateTimeCostWithDetails($pdo, $client_id, $duration_minutes, $hourly_rate) {
    try {
        $billing_method = getBillingMethod($pdo, $client_id);
        
        if ($billing_method === 'hourly_rounding') {
            $rounded_hours = ceil($duration_minutes / 60);
            $cost = $rounded_hours * $hourly_rate;

            return [
                'cost' => $cost,
                'billing_method' => $billing_method,
                'duration_minutes' => $duration_minutes,
                'rounded_hours' => $rounded_hours,
                'actual_hours' => $duration_minutes / 60,
                'hourly_rate' => $hourly_rate,
                'calculation_note' => "تم تقريب {$duration_minutes} دقيقة إلى {$rounded_hours} ساعة"
            ];
        } elseif ($billing_method === 'first_minute_full_hour') {
            $cost = $duration_minutes > 0 ? $hourly_rate : 0;

            return [
                'cost' => $cost,
                'billing_method' => $billing_method,
                'duration_minutes' => $duration_minutes,
                'charged_hours' => $duration_minutes > 0 ? 1 : 0,
                'actual_hours' => $duration_minutes / 60,
                'hourly_rate' => $hourly_rate,
                'calculation_note' => $duration_minutes > 0 ? "تم حساب ساعة كاملة من أول دقيقة ({$duration_minutes} دقيقة)" : "لا توجد مدة للحساب"
            ];
        } else {
            $actual_hours = $duration_minutes / 60;
            $cost = $actual_hours * $hourly_rate;

            return [
                'cost' => $cost,
                'billing_method' => $billing_method,
                'duration_minutes' => $duration_minutes,
                'actual_hours' => $actual_hours,
                'hourly_rate' => $hourly_rate,
                'calculation_note' => "تم حساب {$duration_minutes} دقيقة = " . number_format($actual_hours, 2) . " ساعة"
            ];
        }
        
    } catch (Exception $e) {
        error_log("Error in calculateTimeCostWithDetails: " . $e->getMessage());
        
        // إرجاع حساب افتراضي في حالة الخطأ
        $actual_hours = $duration_minutes / 60;
        $cost = $actual_hours * $hourly_rate;
        
        return [
            'cost' => $cost,
            'billing_method' => 'actual_time',
            'duration_minutes' => $duration_minutes,
            'actual_hours' => $actual_hours,
            'hourly_rate' => $hourly_rate,
            'calculation_note' => "حساب افتراضي: {$duration_minutes} دقيقة = " . number_format($actual_hours, 2) . " ساعة",
            'error' => $e->getMessage()
        ];
    }
}

/**
 * تحديث إعداد طريقة الحساب للعميل
 * 
 * @param PDO $pdo اتصال قاعدة البيانات
 * @param int $client_id معرف العميل
 * @param string $billing_method طريقة الحساب الجديدة
 * @return bool نجح التحديث أم لا
 */
function updateBillingMethod($pdo, $client_id, $billing_method) {
    try {
        // التحقق من صحة طريقة الحساب
        if (!in_array($billing_method, ['actual_time', 'hourly_rounding', 'first_minute_full_hour'])) {
            throw new InvalidArgumentException("طريقة حساب غير صحيحة: $billing_method");
        }
        
        // التحقق من وجود إعدادات للعميل
        $check_stmt = $pdo->prepare("SELECT id FROM invoice_settings WHERE client_id = ?");
        $check_stmt->execute([$client_id]);
        $existing = $check_stmt->fetch();
        
        if ($existing) {
            // تحديث الإعداد الموجود
            $update_stmt = $pdo->prepare("UPDATE invoice_settings SET billing_method = ?, updated_at = CURRENT_TIMESTAMP WHERE client_id = ?");
            $update_stmt->execute([$billing_method, $client_id]);
        } else {
            // إنشاء إعدادات جديدة
            $insert_stmt = $pdo->prepare("INSERT INTO invoice_settings (client_id, billing_method) VALUES (?, ?)");
            $insert_stmt->execute([$client_id, $billing_method]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error updating billing method for client $client_id: " . $e->getMessage());
        return false;
    }
}

/**
 * مقارنة التكلفة بين الطرق الثلاثة
 *
 * @param int $duration_minutes مدة الجلسة بالدقائق
 * @param float $hourly_rate السعر بالساعة
 * @return array مقارنة بين الطرق الثلاثة
 */
function compareBillingMethods($duration_minutes, $hourly_rate) {
    // حساب بالوقت الفعلي
    $actual_cost = ($duration_minutes / 60) * $hourly_rate;

    // حساب بالتقريب
    $rounded_cost = ceil($duration_minutes / 60) * $hourly_rate;

    // حساب ساعة كاملة من أول دقيقة
    $first_minute_cost = $duration_minutes > 0 ? $hourly_rate : 0;

    // الفرق مع الوقت الفعلي
    $rounded_difference = $rounded_cost - $actual_cost;
    $first_minute_difference = $first_minute_cost - $actual_cost;

    $rounded_percentage = $actual_cost > 0 ? (($rounded_difference / $actual_cost) * 100) : 0;
    $first_minute_percentage = $actual_cost > 0 ? (($first_minute_difference / $actual_cost) * 100) : 0;

    return [
        'duration_minutes' => $duration_minutes,
        'hourly_rate' => $hourly_rate,
        'actual_time' => [
            'cost' => $actual_cost,
            'hours' => $duration_minutes / 60,
            'description' => 'الوقت الفعلي'
        ],
        'hourly_rounding' => [
            'cost' => $rounded_cost,
            'hours' => ceil($duration_minutes / 60),
            'description' => 'تقريب الساعة',
            'difference_from_actual' => $rounded_difference,
            'percentage_difference' => $rounded_percentage
        ],
        'first_minute_full_hour' => [
            'cost' => $first_minute_cost,
            'hours' => $duration_minutes > 0 ? 1 : 0,
            'description' => 'ساعة كاملة من أول دقيقة',
            'difference_from_actual' => $first_minute_difference,
            'percentage_difference' => $first_minute_percentage
        ],
        'comparison' => [
            'cheapest' => min($actual_cost, $rounded_cost, $first_minute_cost),
            'most_expensive' => max($actual_cost, $rounded_cost, $first_minute_cost),
            'max_savings_with_actual' => max($rounded_difference, $first_minute_difference)
        ]
    ];
}
?>
