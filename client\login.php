<?php
require_once '../config/database.php';

// تضمين نظام الحماية المتقدم
require_once '../includes/secure_session_middleware.php';

if (!class_exists('SecurityManager')) {
    require_once '../includes/security.php';
}

if (!isset($security)) {
    $security = new SecurityManager($pdo);
}

if (isset($_SESSION['client_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $security->sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';

    if (!$security->validateCsrfToken($csrf_token, 'client_login')) {
        $error = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
    } elseif (!empty($email) && !empty($password)) {
        if (!$security->validateEmail($email)) {
            $error = 'البريد الإلكتروني غير صالح';
            $security->logSuspiciousActivity($security->getClientIp(), 'invalid_email', "محاولة تسجيل دخول ببريد إلكتروني غير صالح: $email");
        } else {
            if (!$security->checkLoginAttempts($email, 'client')) {
                $error = 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً.';
            } else {
                $stmt = $pdo->prepare("SELECT * FROM clients WHERE email = ? AND is_active = 1");
                $stmt->execute([$email]);
                $client = $stmt->fetch();

                if ($client && password_verify($password, $client['password_hash'])) {
                    $security->logLoginAttempt($email, 'client', true);

                    $_SESSION['client_id'] = $client['client_id'];
                    $_SESSION['client_name'] = $client['business_name'];
                    $_SESSION['owner_name'] = $client['owner_name'];
                    $_SESSION['permissions'] = ['dashboard_access', 'cafeteria_access', 'devices_access', 'customers_access'];

                    session_regenerate_id(true);

                    // محاولة إنشاء جلسة آمنة (اختياري)
                    try {
                        // createSecureUserSession - معطل مؤقتاً
                    } catch (Exception $e) {
                        error_log("تحذير: فشل في إنشاء جلسة آمنة: " . $e->getMessage());
                    }

                    header('Location: dashboard.php');
                    exit;
                } else {
                    $security->logLoginAttempt($email, 'client', false);
                    $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                }
            }
        }
    } else {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول العميل - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/auth.css" rel="stylesheet">
    <link href="../assets/css/auth-modern.css" rel="stylesheet">
    <link href="../assets/css/auth-spacing.css" rel="stylesheet">
    <link href="../assets/css/auth-enhanced.css" rel="stylesheet">
    <link href="../assets/css/auth-animations.css" rel="stylesheet">
    <link href="../assets/css/auth-minimal.css" rel="stylesheet">
    <link href="../assets/css/no-shake.css" rel="stylesheet">
</head>
<body class="auth-page">
    <!-- تأثيرات الخلفية -->
    <div class="auth-particles"></div>

    <div class="container auth-container">
        <div class="row justify-content-center min-vh-100 align-items-center py-3">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card auth-card-enhanced">
                    <div class="auth-card-header">
                        <div class="auth-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <h3 class="auth-title">دخول المحل</h3>
                        <p class="auth-subtitle">مرحباً بك في نظام PlayGood الاحترافي</p>
                    </div>

                    <div class="auth-card-body">
                        <?php if ($error): ?>
                            <div class="auth-alert auth-alert-danger">
                                <i class="fas fa-exclamation-triangle ms-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $security->generateCsrfToken('client_login'); ?>">
                            <input type="hidden" name="form_name" value="client_login">

                            <div class="auth-form-floating">
                                <input type="email" name="email" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-envelope auth-form-icon-enhanced"></i>
                                <label class="auth-form-label">البريد الإلكتروني</label>
                            </div>

                            <div class="auth-form-floating">
                                <input type="password" name="password" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-lock auth-form-icon-enhanced"></i>
                                <label class="auth-form-label">كلمة المرور</label>
                            </div>

                            <div class="auth-checkbox">
                                <input type="checkbox" name="remember" id="remember">
                                <label for="remember">تذكرني</label>
                            </div>

                            <button type="submit" class="auth-btn auth-btn-modern">
                                <i class="fas fa-sign-in-alt ms-2"></i>
                                دخول
                            </button>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">ليس لديك حساب؟</p>
                            <a href="../register.php" class="auth-link auth-link-modern">سجل محلك الآن</a>
                        </div>

                        <div class="text-center mt-5 pt-4" style="border-top: 1px solid rgba(102, 126, 234, 0.1);">
                            <p class="text-muted small mb-3" style="font-weight: 500;">أو تسجيل الدخول كـ</p>
                            <div class="d-flex justify-content-center gap-4 flex-wrap">
                                <a href="employee-login.php" class="auth-link-alt">
                                    <i class="fas fa-users"></i>
                                    <span>موظف</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <p class="text-white-50 small">
                        <i class="fas fa-shield-alt ms-1"></i>
                        جميع البيانات محمية ومشفرة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/auth-effects.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const submitBtn = form.querySelector('.auth-btn');
            const originalText = submitBtn.innerHTML;

            const loadingMessages = [
                'جاري تسجيل الدخول...',
                'جاري التحقق من البيانات...',
                'جاري الاتصال بالخادم...',
                'تم التحقق، جاري التوجيه...'
            ];

            form.addEventListener('submit', function() {
                submitBtn.classList.add('auth-loading');
                submitBtn.disabled = true;

                let messageIndex = 0;
                const messageInterval = setInterval(() => {
                    if (messageIndex < loadingMessages.length) {
                        submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingMessages[messageIndex]}`;
                        messageIndex++;
                    } else {
                        clearInterval(messageInterval);
                    }
                }, 800);
            });

            const inputs = document.querySelectorAll('.auth-form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                    this.style.transform = '';
                });

                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = 'var(--auth-primary)';
                        this.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)';
                    } else {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                    }
                });
            });

            const icons = document.querySelectorAll('.auth-form-icon-enhanced');
            icons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-50%) scale(1.3) rotate(10deg)';
                    this.style.color = 'var(--auth-accent)';
                });

                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-50%) scale(1) rotate(0deg)';
                    this.style.color = 'var(--auth-primary)';
                });
            });

            submitBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.05)';
            });

            submitBtn.addEventListener('mouseleave', function() {
                if (!this.disabled) {
                    this.style.transform = '';
                }
            });

            const card = document.querySelector('.auth-card-enhanced');
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });

            let typingTimer;
            inputs.forEach(input => {
                input.addEventListener('keyup', function() {
                    clearTimeout(typingTimer);
                    this.style.borderColor = '#ffc107';

                    typingTimer = setTimeout(() => {
                        if (this.value.length > 0) {
                            this.style.borderColor = 'var(--auth-primary)';
                        } else {
                            this.style.borderColor = '';
                        }
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>