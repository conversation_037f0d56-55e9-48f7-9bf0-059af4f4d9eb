<?php
/**
 * إعداد جداول الحماية المطلوبة
 * يجب تشغيل هذا الملف مرة واحدة لإنشاء الجداول الأمنية
 */

require_once 'config/database.php';

try {
    echo "<h2>إعداد جداول الحماية...</h2>";
    
    // جدول محاولات تسجيل الدخول
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            user_type ENUM('admin', 'client', 'employee') NOT NULL,
            username VARCHAR(255),
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN DEFAULT FALSE,
            user_agent TEXT,
            INDEX idx_ip_time (ip_address, attempt_time),
            INDEX idx_username_time (username, attempt_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول login_attempts<br>";
    
    // جدول الأنشطة المشبوهة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS suspicious_activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            activity_type VARCHAR(100) NOT NULL,
            description TEXT,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            blocked BOOLEAN DEFAULT FALSE,
            INDEX idx_ip_activity (ip_address, activity_type),
            INDEX idx_severity_time (severity, created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول suspicious_activities<br>";
    
    // جدول الـ IP المحظورة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            reason TEXT,
            blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            blocked_until TIMESTAMP NULL,
            permanent BOOLEAN DEFAULT FALSE,
            INDEX idx_ip_until (ip_address, blocked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول blocked_ips<br>";
    
    // جدول التهديدات المكتشفة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS detected_threats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            threat_type VARCHAR(100) NOT NULL,
            threat_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
            description TEXT,
            request_uri TEXT,
            user_agent TEXT,
            request_data JSON,
            action_taken VARCHAR(100),
            detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip_level (ip_address, threat_level),
            INDEX idx_type_time (threat_type, detected_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول detected_threats<br>";
    
    // جدول قواعد الكشف
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS detection_rules (
            id INT AUTO_INCREMENT PRIMARY KEY,
            rule_name VARCHAR(100) NOT NULL UNIQUE,
            rule_pattern TEXT NOT NULL,
            threat_type VARCHAR(100) NOT NULL,
            threat_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول detection_rules<br>";
    
    // جدول محاولات SQL Injection
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS sql_injection_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            user_id INT NULL,
            query_attempted TEXT NOT NULL,
            parameters JSON NULL,
            detected_patterns TEXT,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            blocked BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip_time (ip_address, created_at),
            INDEX idx_severity (severity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول sql_injection_attempts<br>";
    
    // جدول البيانات المشفرة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS encrypted_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(100) NOT NULL,
            record_id INT NOT NULL,
            field_name VARCHAR(100) NOT NULL,
            encrypted_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_record_field (table_name, record_id, field_name),
            INDEX idx_table_record (table_name, record_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول encrypted_data<br>";
    
    // جدول سجل النسخ الاحتياطية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS backup_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            backup_id VARCHAR(100) NOT NULL UNIQUE,
            backup_type ENUM('full', 'database', 'files') NOT NULL,
            file_size BIGINT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INT NULL,
            status ENUM('completed', 'failed', 'corrupted') DEFAULT 'completed',
            INDEX idx_backup_id (backup_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ تم إنشاء جدول backup_log<br>";
    
    // إدراج قواعد الكشف الافتراضية
    $default_rules = [
        [
            'rule_name' => 'sql_injection_basic',
            'rule_pattern' => '/(union|select|insert|update|delete|drop|create|alter|exec|script)/i',
            'threat_type' => 'sql_injection',
            'threat_level' => 'high'
        ],
        [
            'rule_name' => 'xss_script_tags',
            'rule_pattern' => '/<script[^>]*>.*?<\/script>/i',
            'threat_type' => 'xss',
            'threat_level' => 'high'
        ],
        [
            'rule_name' => 'path_traversal',
            'rule_pattern' => '/(\.\.[\/\\\\]|\.\.%2f|\.\.%5c)/i',
            'threat_type' => 'path_traversal',
            'threat_level' => 'medium'
        ],
        [
            'rule_name' => 'command_injection',
            'rule_pattern' => '/(;|\||&|`|\$\(|\${)/i',
            'threat_type' => 'command_injection',
            'threat_level' => 'critical'
        ],
        [
            'rule_name' => 'file_inclusion',
            'rule_pattern' => '/(include|require|file_get_contents|fopen|readfile)/i',
            'threat_type' => 'file_inclusion',
            'threat_level' => 'high'
        ],
        [
            'rule_name' => 'suspicious_user_agent',
            'rule_pattern' => '/(sqlmap|nmap|nikto|burp|acunetix|nessus|openvas|w3af)/i',
            'threat_type' => 'scanning_tool',
            'threat_level' => 'critical'
        ]
    ];
    
    foreach ($default_rules as $rule) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO detection_rules 
            (rule_name, rule_pattern, threat_type, threat_level) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $rule['rule_name'],
            $rule['rule_pattern'],
            $rule['threat_type'],
            $rule['threat_level']
        ]);
    }
    echo "✅ تم إدراج قواعد الكشف الافتراضية<br>";
    
    // إنشاء مجلدات الحماية
    $directories = [
        'uploads',
        'quarantine',
        'backups',
        'backups/secure'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ تم إنشاء مجلد $dir<br>";
        }
    }
    
    // إنشاء ملفات الحماية
    if (!file_exists('config/.encryption_key')) {
        $key = random_bytes(32);
        file_put_contents('config/.encryption_key', base64_encode($key));
        chmod('config/.encryption_key', 0600);
        echo "✅ تم إنشاء مفتاح التشفير<br>";
    }
    
    if (!file_exists('config/.backup_key')) {
        $key = random_bytes(32);
        file_put_contents('config/.backup_key', base64_encode($key));
        chmod('config/.backup_key', 0600);
        echo "✅ تم إنشاء مفتاح النسخ الاحتياطي<br>";
    }
    
    echo "<br><h3 style='color: green;'>✅ تم إعداد جميع جداول ومكونات الحماية بنجاح!</h3>";
    echo "<p><strong>يمكنك الآن:</strong></p>";
    echo "<ul>";
    echo "<li><a href='client/login.php'>تسجيل دخول العميل</a></li>";
    echo "<li><a href='admin/login.php'>تسجيل دخول الإدمن</a></li>";
    echo "<li><a href='security_test_suite.php'>اختبار النظام الأمني</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ في إعداد الجداول:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
