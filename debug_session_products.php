<?php
/**
 * تشخيص مشكلة عدم ظهور المنتجات في modal تعديل الجلسة
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>تشخيص مشكلة عدم ظهور المنتجات</h1>";
echo "<hr>";

try {
    echo "<h2>1. فحص الجداول والبيانات</h2>";
    
    // فحص جدول sessions
    $stmt = $pdo->query("SELECT session_id, device_id, status FROM sessions WHERE status = 'active' LIMIT 5");
    $sessions = $stmt->fetchAll();
    
    echo "<h3>الجلسات النشطة:</h3>";
    if (count($sessions) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>معرف الجلسة</th><th>معرف الجهاز</th><th>الحالة</th><th>المنتجات</th></tr>";
        
        foreach ($sessions as $session) {
            // فحص المنتجات لكل جلسة
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ?");
            $stmt->execute([$session['session_id']]);
            $products_count = $stmt->fetch()['count'];
            
            echo "<tr>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['device_id']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>$products_count منتج</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختيار أول جلسة للاختبار
        $test_session_id = $sessions[0]['session_id'];
        echo "<p style='color: blue;'>ℹ️ سنستخدم الجلسة رقم $test_session_id للاختبار</p>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة - سأنشئ واحدة للاختبار</p>";
        
        // إنشاء جلسة تجريبية
        $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
        $device = $stmt->fetch();
        
        if ($device) {
            $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
            $stmt->execute([$device['device_id'], 1, 1]);
            $test_session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $test_session_id</p>";
        } else {
            throw new Exception("لا توجد أجهزة متاحة");
        }
    }
    
    echo "<h2>2. فحص منتجات الجلسة $test_session_id</h2>";
    
    // فحص المنتجات الموجودة
    $stmt = $pdo->prepare("
        SELECT 
            sp.id,
            sp.session_id,
            sp.product_id,
            sp.quantity,
            sp.price,
            ci.name as product_name,
            (sp.quantity * sp.price) as total
        FROM session_products sp
        LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id
        WHERE sp.session_id = ?
        ORDER BY sp.created_at DESC
    ");
    $stmt->execute([$test_session_id]);
    $products = $stmt->fetchAll();
    
    if (count($products) > 0) {
        echo "<h3>المنتجات الموجودة:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['product_name']}</td>";
            echo "<td>{$product['quantity']}</td>";
            echo "<td>{$product['price']} ج.م</td>";
            echo "<td>{$product['total']} ج.م</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات مضافة للجلسة - سأضيف بعض المنتجات للاختبار</p>";
        
        // إضافة منتجات تجريبية
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 3");
        $available_products = $stmt->fetchAll();
        
        if (count($available_products) > 0) {
            foreach ($available_products as $product) {
                $quantity = rand(1, 3);
                $unit_price = $product['price'];
                $total_price = $unit_price * $quantity;
                $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$test_session_id, $product['id'], $quantity, $unit_price, $total_price]);
                echo "<p style='color: green;'>✅ تم إضافة منتج: {$product['name']}</p>";
            }
        } else {
            // إنشاء منتجات تجريبية
            $test_products = [
                ['كوكاكولا', 3.50, 'مشروبات'],
                ['شيبس', 2.00, 'وجبات خفيفة'],
                ['قهوة', 5.00, 'مشروبات ساخنة']
            ];
            
            foreach ($test_products as $product) {
                $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
                $stmt->execute([$product[0], $product[1], $product[2], 1]);
                $product_id = $pdo->lastInsertId();
                
                $quantity = rand(1, 3);
                $unit_price = $product[1];
                $total_price = $unit_price * $quantity;
                $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$test_session_id, $product_id, $quantity, $unit_price, $total_price]);
                echo "<p style='color: green;'>✅ تم إنشاء وإضافة منتج: {$product[0]}</p>";
            }
        }
    }
    
    echo "<h2>3. اختبار API جلب المنتجات</h2>";
    
    // اختبار API مباشرة
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/get_session_products.php?session_id=' . $test_session_id;
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    // اختبار API عبر cURL
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";
    echo "<h3>استجابة API:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $json_response = json_decode($response, true);
    if ($json_response) {
        if (isset($json_response['success']) && $json_response['success']) {
            echo "<p style='color: green; font-size: 18px;'><strong>✅ API يعمل بشكل صحيح!</strong></p>";
            echo "<p>عدد المنتجات المسترجعة: " . count($json_response['products']) . "</p>";
        } else {
            echo "<p style='color: red; font-size: 18px;'><strong>❌ API أرجع خطأ:</strong> " . ($json_response['error'] ?? 'خطأ غير معروف') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ الاستجابة ليست JSON صحيح</p>";
    }
    
    echo "<h2>4. اختبار JavaScript</h2>";
    ?>
    
    <div id="js-test-area" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
        <h3>اختبار تحميل المنتجات عبر JavaScript:</h3>
        <button onclick="testLoadProducts()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            اختبار تحميل المنتجات
        </button>
        <div id="js-results" style="margin-top: 15px;"></div>
        <div id="products-display" style="margin-top: 15px; border: 1px solid #ddd; border-radius: 5px; padding: 15px; background: white;"></div>
    </div>
    
    <script>
    const testSessionId = <?php echo $test_session_id; ?>;
    
    function testLoadProducts() {
        const resultsDiv = document.getElementById('js-results');
        const displayDiv = document.getElementById('products-display');
        
        resultsDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المنتجات...</p>';
        displayDiv.innerHTML = '';
        
        fetch(`client/api/get_session_products.php?session_id=${testSessionId}`)
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(responseText => {
                console.log('Raw response:', responseText);
                
                try {
                    const data = JSON.parse(responseText);
                    console.log('Parsed data:', data);
                    
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <p style="color: green;"><strong>✅ تم تحميل المنتجات بنجاح!</strong></p>
                            <p>عدد المنتجات: ${data.products.length}</p>
                        `;
                        
                        if (data.products.length > 0) {
                            const totalCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);
                            
                            displayDiv.innerHTML = `
                                <h4>المنتجات المحملة:</h4>
                                <div style="border: 1px solid #eee; border-radius: 3px;">
                                    ${data.products.map(product => `
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #f0f0f0;">
                                            <div>
                                                <h6 style="margin: 0; font-weight: bold;">${product.product_name}</h6>
                                                <small style="color: #666;">${product.quantity} × ${product.price} ج.م</small>
                                            </div>
                                            <div>
                                                <span style="font-weight: bold; color: #007bff;">${product.total} ج.م</span>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                                    <div style="display: flex; justify-content: space-between;">
                                        <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                        <span style="font-weight: bold; color: #007bff;">${totalCost.toFixed(2)} ج.م</span>
                                    </div>
                                </div>
                            `;
                        } else {
                            displayDiv.innerHTML = '<p style="color: #666; text-align: center;">لا توجد منتجات مضافة</p>';
                        }
                    } else {
                        resultsDiv.innerHTML = `
                            <p style="color: red;"><strong>❌ خطأ:</strong> ${data.error}</p>
                        `;
                    }
                } catch (jsonError) {
                    resultsDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ خطأ في تحليل JSON:</strong> ${jsonError.message}</p>
                        <details>
                            <summary>الاستجابة الخام</summary>
                            <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    `;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `
                    <p style="color: red;"><strong>❌ خطأ في الشبكة:</strong> ${error.message}</p>
                `;
            });
    }
    
    // تشغيل الاختبار تلقائياً
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(testLoadProducts, 1000);
    });
    </script>
    
    <?php
    
    echo "<h2>5. فحص console المتصفح</h2>";
    echo "<p>افتح أدوات المطور في المتصفح (F12) وتحقق من:</p>";
    echo "<ul>";
    echo "<li><strong>Console:</strong> ابحث عن أي أخطاء JavaScript</li>";
    echo "<li><strong>Network:</strong> تحقق من طلبات API وحالة الاستجابة</li>";
    echo "<li><strong>Elements:</strong> تأكد من وجود العنصر <code>edit_session_products</code></li>";
    echo "</ul>";
    
    echo "<h2>6. الحلول المقترحة</h2>";
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h3>إذا كانت المشكلة في JavaScript:</h3>";
    echo "<ol>";
    echo "<li>تأكد من استدعاء <code>loadEditSessionProducts(sessionId)</code> عند فتح modal</li>";
    echo "<li>تحقق من وجود العنصر <code>edit_session_products</code> في DOM</li>";
    echo "<li>تأكد من عدم وجود أخطاء في console</li>";
    echo "</ol>";
    
    echo "<h3>إذا كانت المشكلة في API:</h3>";
    echo "<ol>";
    echo "<li>تحقق من صحة معرف الجلسة</li>";
    echo "<li>تأكد من وجود منتجات في جدول session_products</li>";
    echo "<li>تحقق من صلاحيات قاعدة البيانات</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
table {
    font-family: Arial, sans-serif;
    font-size: 14px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    font-weight: bold;
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
