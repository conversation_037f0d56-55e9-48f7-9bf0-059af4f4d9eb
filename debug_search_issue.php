<?php
/**
 * تشخيص مشكلة البحث في لوحة التحكم
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>تشخيص مشكلة البحث</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-warning text-dark'>
            <h3><i class='fas fa-bug me-2'></i>تشخيص مشكلة البحث</h3>
        </div>
        <div class='card-body'>";

try {
    echo "<h5>1. فحص الجلسة</h5>";
    echo "<p><strong>Client ID:</strong> " . ($_SESSION['client_id'] ?? 'غير محدد') . "</p>";
    echo "<p><strong>Employee ID:</strong> " . ($_SESSION['employee_id'] ?? 'غير محدد') . "</p>";
    
    echo "<h5>2. فحص قاعدة البيانات</h5>";
    
    // فحص جدول العملاء
    $customers_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
    echo "<p><strong>عدد العملاء الإجمالي:</strong> $customers_count</p>";
    
    $client_customers_count = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE client_id = ?");
    $client_customers_count->execute([$_SESSION['client_id']]);
    $client_count = $client_customers_count->fetchColumn();
    echo "<p><strong>عدد عملاء Client ID {$_SESSION['client_id']}:</strong> $client_count</p>";
    
    if ($client_count > 0) {
        echo "<h6>عينة من العملاء:</h6>";
        $sample_customers = $pdo->prepare("SELECT customer_id, name, phone FROM customers WHERE client_id = ? LIMIT 5");
        $sample_customers->execute([$_SESSION['client_id']]);
        $customers = $sample_customers->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<ul class='list-group'>";
        foreach ($customers as $customer) {
            echo "<li class='list-group-item'>";
            echo "<strong>ID:</strong> {$customer['customer_id']} | ";
            echo "<strong>الاسم:</strong> " . htmlspecialchars($customer['name']) . " | ";
            echo "<strong>الهاتف:</strong> " . htmlspecialchars($customer['phone']);
            echo "</li>";
        }
        echo "</ul>";
    }
    
    echo "<h5>3. اختبار API مباشرة</h5>";
    
    if ($client_count > 0) {
        // اختبار API مع أول عميل
        $first_customer = $pdo->prepare("SELECT name FROM customers WHERE client_id = ? LIMIT 1");
        $first_customer->execute([$_SESSION['client_id']]);
        $customer_name = $first_customer->fetchColumn();
        
        if ($customer_name) {
            $test_query = substr($customer_name, 0, 3);
            echo "<p>اختبار البحث عن: <strong>$test_query</strong></p>";
            
            // محاكاة استدعاء API
            $search_term = "%{$test_query}%";
            $search_stmt = $pdo->prepare("
                SELECT customer_id as id, name, phone, email
                FROM customers
                WHERE client_id = ?
                AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
                ORDER BY name ASC
                LIMIT 10
            ");
            
            $search_stmt->execute([$_SESSION['client_id'], $search_term, $search_term, $search_term]);
            $results = $search_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>نتائج البحث:</strong> " . count($results) . " نتيجة</p>";
            
            if (!empty($results)) {
                echo "<div class='table-responsive'>";
                echo "<table class='table table-sm'>";
                echo "<thead><tr><th>ID</th><th>الاسم</th><th>الهاتف</th></tr></thead>";
                echo "<tbody>";
                foreach ($results as $result) {
                    echo "<tr>";
                    echo "<td>{$result['id']}</td>";
                    echo "<td>" . htmlspecialchars($result['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($result['phone']) . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
                echo "</div>";
            }
        }
    }
    
    echo "<h5>4. اختبار الملفات</h5>";
    
    $api_file = 'client/api/search-customers.php';
    if (file_exists($api_file)) {
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>ملف API موجود: $api_file</p>";
    } else {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>ملف API غير موجود: $api_file</p>";
    }
    
    echo "<h5>5. اختبار البحث التفاعلي</h5>";
    
    echo "<div class='card bg-light'>
            <div class='card-body'>
                <div class='row'>
                    <div class='col-md-8'>
                        <input type='text' id='testSearch' class='form-control' placeholder='اكتب للبحث...'>
                    </div>
                    <div class='col-md-4'>
                        <button onclick='performSearch()' class='btn btn-primary w-100'>بحث</button>
                    </div>
                </div>
                <div id='searchOutput' class='mt-3'></div>
            </div>
          </div>";
    
    echo "<h5>6. اختبار Console</h5>";
    echo "<div class='alert alert-info'>
            <p>افتح Developer Tools (F12) وتحقق من Console للأخطاء.</p>
            <p>تحقق من Network tab لرؤية طلبات API.</p>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/dashboard.php' class='btn btn-primary me-2'>لوحة التحكم</a>
            <a href='test_dashboard_search.php' class='btn btn-success'>اختبار متقدم</a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "        </div>
    </div>
</div>

<script>
function performSearch() {
    const query = document.getElementById('testSearch').value.trim();
    const output = document.getElementById('searchOutput');
    
    if (query.length < 2) {
        output.innerHTML = '<div class=\"alert alert-warning\">يرجى إدخال على الأقل حرفين</div>';
        return;
    }
    
    output.innerHTML = '<div class=\"alert alert-info\">جاري البحث...</div>';
    
    console.log('بدء البحث عن:', query);
    
    const url = 'client/api/search-customers.php?q=' + encodeURIComponent(query);
    console.log('URL:', url);

    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
                throw new Error(`HTTP \${response.status}: \${response.statusText}`);
            }
            
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            
            try {
                const data = JSON.parse(text);
                console.log('Parsed data:', data);
                
                if (Array.isArray(data)) {
                    if (data.length === 0) {
                        output.innerHTML = '<div class=\"alert alert-warning\">لا توجد نتائج</div>';
                    } else {
                        let html = '<div class=\"alert alert-success\">تم العثور على ' + data.length + ' نتيجة:</div>';
                        html += '<ul class=\"list-group\">';
                        
                        data.forEach(item => {
                            html += '<li class="list-group-item">' +
                                        '<strong>ID:</strong> ' + item.id + ' | ' +
                                        '<strong>الاسم:</strong> ' + item.name + ' | ' +
                                        '<strong>الهاتف:</strong> ' + (item.phone || 'غير محدد') +
                                     '</li>';
                        });
                        
                        html += '</ul>';
                        output.innerHTML = html;
                    }
                } else {
                    output.innerHTML = '<div class=\"alert alert-danger\">البيانات ليست مصفوفة: ' + JSON.stringify(data) + '</div>';
                }
            } catch (e) {
                console.error('JSON parse error:', e);
                output.innerHTML = '<div class=\"alert alert-danger\">خطأ في تحليل JSON:<br><pre>' + text + '</pre></div>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            output.innerHTML = '<div class=\"alert alert-danger\">خطأ في الشبكة: ' + error.message + '</div>';
        });
}

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة');
    console.log('Session info from PHP:', {
        client_id: <?php echo json_encode($_SESSION['client_id'] ?? null); ?>,
        employee_id: <?php echo json_encode($_SESSION['employee_id'] ?? null); ?>
    });
});
</script>

</body>
</html>";
?>
