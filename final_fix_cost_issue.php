<?php
/**
 * الإصلاح النهائي لمشكلة التكلفة - PlayGood
 * حل شامل لمشكلة ظهور اسم العميل بدلاً من التكلفة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 الإصلاح النهائي لمشكلة التكلفة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. إصلاح شامل للبيانات
    echo "<h2>1. إصلاح شامل للبيانات</h2>";
    
    $fixes_applied = 0;
    
    try {
        // أ. إصلاح أسماء الأجهزة المشبوهة
        echo "<h4>أ. إصلاح أسماء الأجهزة:</h4>";
        
        $suspicious_devices = $pdo->query("
            SELECT device_id, device_name, device_type
            FROM devices 
            WHERE client_id = 1 
            AND (
                device_name REGEXP '^[أ-ي\\s]+$' 
                OR device_name IN (SELECT name FROM customers WHERE client_id = 1)
                OR LENGTH(device_name) < 3
            )
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($suspicious_devices) > 0) {
            $device_names = [
                'PS5' => 'PlayStation 5',
                'PS4' => 'PlayStation 4', 
                'PC' => 'Gaming PC',
                'XBOX' => 'Xbox Series',
                'SWITCH' => 'Nintendo Switch'
            ];
            
            $update_device = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
            $counter = 1;
            
            foreach ($suspicious_devices as $device) {
                $device_type = strtoupper($device['device_type']);
                $base_name = $device_names[$device_type] ?? $device_type;
                $new_name = $base_name . ' - ' . $counter;
                
                $update_device->execute([$new_name, $device['device_id']]);
                echo "<p style='color: green;'>✅ تم تحديث: " . htmlspecialchars($device['device_name']) . " → $new_name</p>";
                $fixes_applied++;
                $counter++;
            }
        } else {
            echo "<p style='color: green;'>✅ أسماء الأجهزة صحيحة</p>";
        }
        
        // ب. إصلاح الأسعار المفقودة
        echo "<h4>ب. إصلاح الأسعار المفقودة:</h4>";
        
        $devices_no_rates = $pdo->query("
            SELECT device_id, device_name, device_type
            FROM devices 
            WHERE client_id = 1 
            AND (single_rate IS NULL OR single_rate <= 0)
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($devices_no_rates) > 0) {
            $default_rates = [
                'PS5' => 25.00,
                'PS4' => 20.00,
                'PC' => 30.00,
                'XBOX' => 25.00,
                'SWITCH' => 20.00
            ];
            
            $update_rate = $pdo->prepare("UPDATE devices SET single_rate = ?, multi_rate = ? WHERE device_id = ?");
            
            foreach ($devices_no_rates as $device) {
                $device_type = strtoupper($device['device_type']);
                $single_rate = $default_rates[$device_type] ?? 25.00;
                $multi_rate = $single_rate * 1.6; // 60% زيادة للجماعي
                
                $update_rate->execute([$single_rate, $multi_rate, $device['device_id']]);
                echo "<p style='color: green;'>✅ تم تحديث أسعار: " . htmlspecialchars($device['device_name']) . " → $single_rate ج.م</p>";
                $fixes_applied++;
            }
        } else {
            echo "<p style='color: green;'>✅ جميع الأسعار موجودة</p>";
        }
        
        // ج. إضافة أجهزة وعملاء تجريبيين إذا لزم الأمر
        echo "<h4>ج. التأكد من وجود بيانات كافية:</h4>";
        
        $devices_count = $pdo->query("SELECT COUNT(*) FROM devices WHERE client_id = 1")->fetchColumn();
        $customers_count = $pdo->query("SELECT COUNT(*) FROM customers WHERE client_id = 1")->fetchColumn();
        
        echo "<p>عدد الأجهزة: $devices_count، عدد العملاء: $customers_count</p>";
        
        if ($devices_count < 2) {
            $sample_devices = [
                ['PlayStation 5 Pro', 'PS5', 'available', 30.00, 50.00],
                ['Gaming PC Ultimate', 'PC', 'available', 35.00, 60.00]
            ];
            
            $insert_device = $pdo->prepare("
                INSERT INTO devices (device_name, device_type, status, single_rate, multi_rate, client_id) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($sample_devices as $device) {
                $insert_device->execute([
                    $device[0], $device[1], $device[2], 
                    $device[3], $device[4], 1
                ]);
                echo "<p style='color: green;'>✅ تم إضافة جهاز: {$device[0]}</p>";
                $fixes_applied++;
            }
        }
        
        if ($customers_count < 2) {
            $sample_customers = [
                ['أحمد محمد علي', '01234567890', '<EMAIL>'],
                ['فاطمة حسن محمود', '01987654321', '<EMAIL>']
            ];
            
            $insert_customer = $pdo->prepare("
                INSERT INTO customers (name, phone, email, client_id) 
                VALUES (?, ?, ?, ?)
            ");
            
            foreach ($sample_customers as $customer) {
                $insert_customer->execute([
                    $customer[0], $customer[1], $customer[2], 1
                ]);
                echo "<p style='color: green;'>✅ تم إضافة عميل: {$customer[0]}</p>";
                $fixes_applied++;
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح البيانات: " . $e->getMessage() . "</p>";
    }
    
    // 2. اختبار شامل للنظام
    echo "<h2>2. اختبار شامل للنظام</h2>";
    
    try {
        // إنشاء جلسة تجريبية للاختبار
        echo "<h4>أ. إنشاء جلسة تجريبية:</h4>";
        
        // البحث عن جهاز متاح
        $device = $pdo->query("
            SELECT device_id, device_name, single_rate 
            FROM devices 
            WHERE client_id = 1 AND status = 'available' 
            ORDER BY device_id 
            LIMIT 1
        ")->fetch(PDO::FETCH_ASSOC);
        
        // البحث عن عميل
        $customer = $pdo->query("
            SELECT customer_id, name 
            FROM customers 
            WHERE client_id = 1 
            ORDER BY customer_id 
            LIMIT 1
        ")->fetch(PDO::FETCH_ASSOC);
        
        if ($device) {
            // حذف الجلسات التجريبية السابقة
            $pdo->query("DELETE FROM sessions WHERE client_id = 1 AND status = 'active' AND start_time < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
            
            // إنشاء جلسة جديدة
            $insert_session = $pdo->prepare("
                INSERT INTO sessions (device_id, customer_id, start_time, status, client_id) 
                VALUES (?, ?, DATE_SUB(NOW(), INTERVAL 30 MINUTE), 'active', ?)
            ");
            $insert_session->execute([
                $device['device_id'], 
                $customer['customer_id'] ?? null, 
                1
            ]);
            
            $session_id = $pdo->lastInsertId();
            
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية رقم: $session_id</p>";
            echo "<p><strong>الجهاز:</strong> " . htmlspecialchars($device['device_name']) . "</p>";
            echo "<p><strong>العميل:</strong> " . htmlspecialchars($customer['name'] ?? 'غير محدد') . "</p>";
            echo "<p><strong>السعر:</strong> " . $device['single_rate'] . " ج.م/ساعة</p>";
            
            // اختبار حساب التكلفة
            echo "<h4>ب. اختبار حساب التكلفة:</h4>";
            
            $test_session = $pdo->query("
                SELECT s.*,
                       d.device_name,
                       d.single_rate,
                       c.name as customer_name,
                       TIMESTAMPDIFF(MINUTE, s.start_time, NOW()) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                LEFT JOIN customers c ON s.customer_id = c.customer_id
                WHERE s.session_id = $session_id
            ")->fetch(PDO::FETCH_ASSOC);
            
            if ($test_session) {
                $duration_minutes = $test_session['duration_minutes'] ?? 0;
                $hourly_rate = $test_session['single_rate'] ?? 0;
                $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                
                echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h5>🧮 نتائج الاختبار:</h5>";
                echo "<p><strong>اسم الجهاز:</strong> <span style='color: blue;'>" . htmlspecialchars($test_session['device_name']) . "</span></p>";
                echo "<p><strong>اسم العميل:</strong> <span style='color: red;'>" . htmlspecialchars($test_session['customer_name'] ?? 'غير محدد') . "</span></p>";
                echo "<p><strong>المدة:</strong> $duration_minutes دقيقة</p>";
                echo "<p><strong>السعر/ساعة:</strong> $hourly_rate ج.م</p>";
                echo "<p><strong>التكلفة المحسوبة:</strong> <span style='background: yellow; padding: 5px; font-weight: bold;'>$time_cost ج.م</span></p>";
                
                // فحص المشاكل
                $issues = [];
                if ($test_session['device_name'] === $test_session['customer_name']) {
                    $issues[] = "اسم الجهاز = اسم العميل";
                }
                if (!is_numeric($time_cost)) {
                    $issues[] = "التكلفة ليست رقم";
                }
                if ($hourly_rate <= 0) {
                    $issues[] = "السعر صفر أو سالب";
                }
                
                if (count($issues) > 0) {
                    echo "<p style='color: red;'><strong>مشاكل مكتشفة:</strong> " . implode(', ', $issues) . "</p>";
                } else {
                    echo "<p style='color: green;'><strong>✅ لا توجد مشاكل - النظام يعمل بشكل صحيح!</strong></p>";
                }
                
                echo "</div>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
    
    // 3. تعليمات الاختبار النهائي
    echo "<h2>3. تعليمات الاختبار النهائي</h2>";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🎯 خطوات الاختبار النهائي:</h4>";
    echo "<ol>";
    echo "<li><strong>اختبر صفحة الجلسات العادية:</strong><br>";
    echo "<a href='client/sessions.php' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>صفحة الجلسات</a></li>";
    
    echo "<li><strong>اختبر صفحة الجلسات مع التشخيص:</strong><br>";
    echo "<a href='client/sessions.php?debug=1' target='_blank' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>صفحة الجلسات + تشخيص</a></li>";
    
    echo "<li><strong>اختبر ملف التشخيص المباشر:</strong><br>";
    echo "<a href='debug_cost_direct.php' target='_blank' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>التشخيص المباشر</a></li>";
    
    echo "<li><strong>اختبر صفحة الفواتير:</strong><br>";
    echo "<a href='client/invoices.php' target='_blank' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>صفحة الفواتير</a></li>";
    echo "</ol>";
    echo "</div>";
    
    // 4. ملخص الإصلاحات
    echo "<h2>4. ملخص الإصلاحات المطبقة</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>✅ تم تطبيق الإصلاحات التالية:</h4>";
    echo "<ul>";
    echo "<li>إصلاح أسماء الأجهزة المشبوهة</li>";
    echo "<li>إصلاح الأسعار المفقودة أو الصفر</li>";
    echo "<li>إضافة بيانات تجريبية إذا لزم الأمر</li>";
    echo "<li>إضافة تشخيص مباشر في صفحة الجلسات</li>";
    echo "<li>إنشاء جلسة تجريبية للاختبار</li>";
    echo "</ul>";
    echo "<p><strong>إجمالي الإصلاحات المطبقة:</strong> $fixes_applied</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";
?>
