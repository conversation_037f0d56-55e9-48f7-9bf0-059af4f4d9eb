<?php
/**
 * إصلاح مشاكل الجلسات ولوحة التحكم - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشاكل الجلسات ولوحة التحكم</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص مشكلة تكلفة الجلسات
    echo "<h2>1. فحص مشكلة تكلفة الجلسات</h2>";
    
    try {
        // فحص الجلسات النشطة
        $active_sessions = $pdo->query("
            SELECT s.session_id, s.start_time, s.customer_id, s.total_cost,
                   d.device_name, d.single_rate, d.multi_rate,
                   c.name as customer_name,
                   TIMESTAMPDIFF(MINUTE, s.start_time, NOW()) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE s.status = 'active' AND d.client_id = 1
            LIMIT 5
        ")->fetchAll();
        
        echo "<p>عدد الجلسات النشطة: <strong>" . count($active_sessions) . "</strong></p>";
        
        if (count($active_sessions) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID الجلسة</th>";
            echo "<th style='padding: 8px;'>الجهاز</th>";
            echo "<th style='padding: 8px;'>العميل</th>";
            echo "<th style='padding: 8px;'>المدة (دقيقة)</th>";
            echo "<th style='padding: 8px;'>السعر/ساعة</th>";
            echo "<th style='padding: 8px;'>التكلفة المحسوبة</th>";
            echo "<th style='padding: 8px;'>التكلفة المحفوظة</th>";
            echo "</tr>";
            
            foreach ($active_sessions as $session) {
                $hourly_rate = $session['single_rate'] ?? 0;
                $duration_minutes = $session['duration_minutes'] ?? 0;
                $calculated_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($session['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px;'>" . $duration_minutes . "</td>";
                echo "<td style='padding: 8px;'>" . $hourly_rate . " ج.م</td>";
                echo "<td style='padding: 8px; color: blue;'>" . number_format($calculated_cost, 2) . " ج.م</td>";
                echo "<td style='padding: 8px; color: red;'>" . number_format($session['total_cost'] ?? 0, 2) . " ج.م</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p style='color: blue;'>ℹ️ التكلفة المحسوبة = ceil(المدة بالدقائق ÷ 60) × السعر بالساعة</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة حالياً</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص مشكلة البحث عن العملاء
    echo "<h2>2. فحص مشكلة البحث عن العملاء</h2>";
    
    try {
        // فحص العملاء الموجودين
        $customers_count = $pdo->query("SELECT COUNT(*) FROM customers WHERE client_id = 1")->fetchColumn();
        echo "<p>عدد العملاء: <strong>$customers_count</strong></p>";
        
        if ($customers_count == 0) {
            echo "<p style='color: orange;'>⚠️ لا يوجد عملاء - سيتم إضافة عملاء تجريبيين</p>";
            
            // إضافة عملاء تجريبيين
            $sample_customers = [
                ['أحمد محمد', '01234567890', '<EMAIL>'],
                ['فاطمة علي', '01987654321', '<EMAIL>'],
                ['محمد حسن', '01122334455', '<EMAIL>'],
                ['سارة أحمد', '01555666777', '<EMAIL>']
            ];
            
            $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, ?)");
            
            foreach ($sample_customers as $customer) {
                try {
                    $insert_stmt->execute([$customer[0], $customer[1], $customer[2], 1]);
                    echo "<p style='color: green;'>✅ تم إضافة: {$customer[0]}</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ خطأ في إضافة {$customer[0]}: " . $e->getMessage() . "</p>";
                }
            }
            
            $customers_count = $pdo->query("SELECT COUNT(*) FROM customers WHERE client_id = 1")->fetchColumn();
            echo "<p>عدد العملاء بعد الإضافة: <strong>$customers_count</strong></p>";
        }
        
        // اختبار البحث
        $search_queries = ['أحمد', 'محمد', '012'];
        
        foreach ($search_queries as $query) {
            echo "<h4>اختبار البحث عن: \"$query\"</h4>";
            
            try {
                $search_stmt = $pdo->prepare("
                    SELECT customer_id as id, name, phone, email
                    FROM customers
                    WHERE client_id = 1
                    AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
                    ORDER BY name ASC
                    LIMIT 5
                ");
                
                $search_term = "%{$query}%";
                $search_stmt->execute([$search_term, $search_term, $search_term]);
                $results = $search_stmt->fetchAll();
                
                if (count($results) > 0) {
                    echo "<p style='color: green;'>✅ تم العثور على " . count($results) . " نتيجة</p>";
                    echo "<ul>";
                    foreach ($results as $result) {
                        echo "<li><strong>" . htmlspecialchars($result['name']) . "</strong> - " . htmlspecialchars($result['phone']) . "</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p style='color: orange;'>⚠️ لم يتم العثور على نتائج</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في البحث: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص العملاء: " . $e->getMessage() . "</p>";
    }
    
    // 3. اختبار API البحث
    echo "<h2>3. اختبار API البحث</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/search-customers.php?q=أحمد';
    echo "<p>رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    // محاكاة API
    try {
        $query = 'أحمد';
        $search_term = "%{$query}%";
        
        $api_stmt = $pdo->prepare("
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = 1
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC
            LIMIT 10
        ");
        
        $api_stmt->execute([$search_term, $search_term, $search_term]);
        $api_results = $api_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنظيف البيانات
        foreach ($api_results as &$customer) {
            $customer['name'] = htmlspecialchars($customer['name'] ?? '');
            $customer['phone'] = htmlspecialchars($customer['phone'] ?? '');
            $customer['email'] = htmlspecialchars($customer['email'] ?? '');
        }
        
        echo "<p>نتائج API للبحث عن \"$query\": <strong>" . count($api_results) . "</strong> نتيجة</p>";
        
        if (count($api_results) > 0) {
            echo "<h5>JSON المتوقع:</h5>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; direction: ltr;'>";
            echo json_encode($api_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            echo "</pre>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في محاكاة API: " . $e->getMessage() . "</p>";
    }
    
    // 4. اختبار تفاعلي للبحث
    echo "<h2>4. اختبار تفاعلي للبحث</h2>";
    
    echo '<div style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">';
    echo '<h4>محاكاة نموذج لوحة التحكم:</h4>';
    echo '<div class="mb-3">';
    echo '<label for="test_customer_search" class="form-label">البحث عن عميل:</label>';
    echo '<div class="input-group">';
    echo '<input type="text" class="form-control" id="test_customer_search" placeholder="ابحث عن عميل..." style="width: 300px; padding: 10px;">';
    echo '<button type="button" class="btn btn-outline-secondary" onclick="clearTestCustomer()">مسح</button>';
    echo '</div>';
    echo '<input type="hidden" id="test_selected_customer_id">';
    echo '<div id="test_customer_results" class="list-group mt-2" style="display: none; border: 1px solid #ccc; max-height: 200px; overflow-y: auto;"></div>';
    echo '</div>';
    echo '</div>';
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "<a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/api/search-customers.php?q=أحمد' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>اختبار API</a>";
echo "</div>";

echo "</div>";
?>

<script>
// اختبار البحث التفاعلي
let testSearchTimeout;

document.getElementById('test_customer_search').addEventListener('input', function() {
    const query = this.value.trim();
    
    clearTimeout(testSearchTimeout);
    
    if (query.length < 2) {
        document.getElementById('test_customer_results').style.display = 'none';
        return;
    }
    
    testSearchTimeout = setTimeout(() => {
        testSearchCustomers(query);
    }, 300);
});

function testSearchCustomers(query) {
    const resultsDiv = document.getElementById('test_customer_results');
    resultsDiv.innerHTML = '<div class="list-group-item">جاري البحث...</div>';
    resultsDiv.style.display = 'block';
    
    fetch(`client/api/search-customers.php?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            
            try {
                const customers = JSON.parse(text);
                
                if (Array.isArray(customers)) {
                    if (customers.length === 0) {
                        resultsDiv.innerHTML = '<div class="list-group-item text-muted">لا توجد نتائج</div>';
                    } else {
                        resultsDiv.innerHTML = customers.map(customer => `
                            <button type="button" class="list-group-item list-group-item-action"
                                    onclick="selectTestCustomer(${customer.id}, '${customer.name}', '${customer.phone || ''}')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${customer.name}</strong>
                                        ${customer.phone ? `<br><small class="text-muted">${customer.phone}</small>` : ''}
                                    </div>
                                </div>
                            </button>
                        `).join('');
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في الاستجابة: ' + JSON.stringify(customers) + '</div>';
                }
                
                resultsDiv.style.display = 'block';
            } catch (error) {
                resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في تحليل JSON: ' + error.message + '<br><pre>' + text + '</pre></div>';
                resultsDiv.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('خطأ في البحث عن العملاء:', error);
            resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في الطلب: ' + error.message + '</div>';
            resultsDiv.style.display = 'block';
        });
}

function selectTestCustomer(id, name, phone) {
    document.getElementById('test_selected_customer_id').value = id;
    document.getElementById('test_customer_search').value = `${name}${phone ? ' - ' + phone : ''}`;
    document.getElementById('test_customer_results').style.display = 'none';
    
    alert(`تم اختيار العميل:\nID: ${id}\nالاسم: ${name}\nالهاتف: ${phone || 'غير محدد'}`);
}

function clearTestCustomer() {
    document.getElementById('test_selected_customer_id').value = '';
    document.getElementById('test_customer_search').value = '';
    document.getElementById('test_customer_results').style.display = 'none';
}

// إخفاء نتائج البحث عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('#test_customer_search') && !e.target.closest('#test_customer_results')) {
        document.getElementById('test_customer_results').style.display = 'none';
    }
});

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const searchInput = document.getElementById('test_customer_search');
        if (searchInput) {
            searchInput.value = 'أحمد';
            testSearchCustomers('أحمد');
        }
    }, 2000);
});
</script>
