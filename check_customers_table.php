<?php
/**
 * فحص جدول العملاء - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 فحص جدول العملاء - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص وجود جدول customers
    echo "<h2>1. فحص وجود جدول customers</h2>";
    
    $tables_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($tables_check->rowCount() > 0) {
        echo "<p style='color: green;'>✅ جدول customers موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ جدول customers غير موجود</p>";
        
        // إنشاء جدول customers
        echo "<h3>إنشاء جدول customers</h3>";
        $create_customers = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(255),
                notes TEXT,
                client_id INT NOT NULL DEFAULT 1,
                created_by INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_customers_client (client_id),
                INDEX idx_customers_name (name),
                INDEX idx_customers_phone (phone)
            )
        ";
        $pdo->exec($create_customers);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
    }
    
    // فحص هيكل الجدول
    echo "<h2>2. هيكل جدول customers</h2>";
    
    $columns_check = $pdo->query("DESCRIBE customers");
    $existing_columns = [];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم العمود</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    while ($column = $columns_check->fetch()) {
        $existing_columns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص الأعمدة المطلوبة
    echo "<h2>3. فحص الأعمدة المطلوبة</h2>";
    
    $required_columns = [
        'customer_id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(255) NOT NULL',
        'phone' => 'VARCHAR(20)',
        'email' => 'VARCHAR(255)',
        'notes' => 'TEXT',
        'client_id' => 'INT NOT NULL DEFAULT 1',
        'created_by' => 'INT DEFAULT 1',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    $fixes_applied = 0;
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                if ($column_name === 'customer_id') {
                    // تخطي إضافة PRIMARY KEY إذا كان موجود
                    continue;
                }
                
                $sql = "ALTER TABLE customers ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ تم إضافة عمود: $column_name</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود</p>";
        }
    }
    
    // اختبار إضافة عميل تجريبي
    echo "<h2>4. اختبار إضافة عميل تجريبي</h2>";
    
    try {
        $test_stmt = $pdo->prepare("
            INSERT INTO customers (name, phone, email, notes, client_id, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $test_data = [
            'عميل تجريبي',
            '01234567890',
            '<EMAIL>',
            'عميل تجريبي للاختبار',
            1,
            1
        ];
        
        $test_stmt->execute($test_data);
        $customer_id = $pdo->lastInsertId();
        
        echo "<p style='color: green;'>✅ تم إضافة عميل تجريبي بنجاح - ID: $customer_id</p>";
        
        // حذف العميل التجريبي
        $delete_stmt = $pdo->prepare("DELETE FROM customers WHERE customer_id = ?");
        $delete_stmt->execute([$customer_id]);
        echo "<p style='color: blue;'>ℹ️ تم حذف العميل التجريبي</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار إضافة العميل: " . $e->getMessage() . "</p>";
        
        // تحليل الخطأ
        echo "<h3>تحليل الخطأ:</h3>";
        echo "<ul>";
        
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            echo "<li>الجدول أو العمود غير موجود</li>";
        }
        
        if (strpos($e->getMessage(), "Duplicate entry") !== false) {
            echo "<li>قيمة مكررة في عمود فريد</li>";
        }
        
        if (strpos($e->getMessage(), "cannot be null") !== false) {
            echo "<li>قيمة NULL في عمود مطلوب</li>";
        }
        
        if (strpos($e->getMessage(), "foreign key") !== false) {
            echo "<li>مشكلة في المفتاح الخارجي</li>";
        }
        
        echo "</ul>";
    }
    
    // عرض العملاء الموجودين
    echo "<h2>5. العملاء الموجودين</h2>";
    
    try {
        $customers_stmt = $pdo->query("SELECT * FROM customers ORDER BY created_at DESC LIMIT 10");
        $customers = $customers_stmt->fetchAll();
        
        if (count($customers) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>الاسم</th>";
            echo "<th style='padding: 10px;'>الهاتف</th>";
            echo "<th style='padding: 10px;'>البريد</th>";
            echo "<th style='padding: 10px;'>تاريخ الإضافة</th>";
            echo "</tr>";
            
            foreach ($customers as $customer) {
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['phone'] ?? '') . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['email'] ?? '') . "</td>";
                echo "<td style='padding: 10px;'>" . $customer['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا يوجد عملاء في النظام</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في جلب العملاء: " . $e->getMessage() . "</p>";
    }
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 ملخص الفحص</h3>";
    echo "<ul>";
    echo "<li>جدول customers: " . (in_array('customers', $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN)) ? "موجود ✅" : "غير موجود ❌") . "</li>";
    echo "<li>الأعمدة المطلوبة: " . count($existing_columns) . " عمود</li>";
    echo "<li>الإصلاحات المطبقة: $fixes_applied</li>";
    echo "<li>اختبار الإضافة: " . (isset($customer_id) ? "نجح ✅" : "فشل ❌") . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر إضافة العملاء الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/customers.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة العملاء</a>";
echo "<a href='simple_fix_all.php' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>سكريپت الإصلاح الشامل</a>";
echo "</div>";

echo "</div>";
?>
