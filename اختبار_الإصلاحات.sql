-- =====================================================
-- سكريبت اختبار الإصلاحات - Station System
-- =====================================================
-- هذا السكريبت يختبر جميع الإصلاحات المطبقة
-- ويتأكد من عمل الوظائف بشكل صحيح
-- =====================================================

USE `station`;

SELECT '🧪 بدء اختبار الإصلاحات...' AS test_status;

-- =====================================================
-- 1. اختبار بنية الجداول
-- =====================================================

SELECT '📋 اختبار بنية الجداول:' AS section;

-- اختبار جدول cafeteria_items
SELECT 
    'cafeteria_items' AS table_name,
    CASE 
        WHEN COUNT(CASE WHEN column_name = 'category' THEN 1 END) > 0 THEN '✅'
        ELSE '❌'
    END AS has_category_column,
    CASE 
        WHEN COUNT(CASE WHEN column_name = 'client_id' THEN 1 END) > 0 THEN '✅'
        ELSE '❌'
    END AS has_client_id_column,
    CASE 
        WHEN COUNT(CASE WHEN column_name = 'stock_quantity' THEN 1 END) > 0 THEN '✅'
        ELSE '❌'
    END AS has_stock_columns
FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'cafeteria_items';

-- اختبار جدول categories
SELECT 
    'categories' AS table_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجود'
        ELSE '❌ مفقود'
    END AS table_exists,
    COUNT(*) AS total_categories
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'categories';

-- =====================================================
-- 2. اختبار البيانات
-- =====================================================

SELECT '📊 اختبار البيانات:' AS section;

-- اختبار وجود تصنيفات
SELECT 
    'التصنيفات' AS data_type,
    COUNT(*) AS total_count,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ كافية'
        WHEN COUNT(*) > 0 THEN '⚠️ قليلة'
        ELSE '❌ مفقودة'
    END AS status
FROM categories;

-- اختبار منتجات الكافتيريا
SELECT 
    'منتجات الكافتيريا' AS data_type,
    COUNT(*) AS total_count,
    COUNT(CASE WHEN category IS NOT NULL AND category != '' THEN 1 END) AS with_category,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجودة'
        ELSE '⚠️ فارغة'
    END AS status
FROM cafeteria_items;

-- =====================================================
-- 3. اختبار الاستعلامات المشكلة
-- =====================================================

SELECT '🔍 اختبار الاستعلامات المشكلة:' AS section;

-- اختبار الاستعلام الذي كان يسبب الخطأ
SET @test_query_result = 'فشل';
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET @test_query_result = 'فشل';
    
    SELECT COUNT(*) INTO @temp_count FROM cafeteria_items ORDER BY category, name LIMIT 1;
    SET @test_query_result = 'نجح';
END;

SELECT 
    'ORDER BY category, name' AS test_query,
    @test_query_result AS result,
    CASE 
        WHEN @test_query_result = 'نجح' THEN '✅'
        ELSE '❌'
    END AS status;

-- اختبار استعلام البحث
SET @search_query_result = 'فشل';
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET @search_query_result = 'فشل';
    
    SELECT COUNT(*) INTO @temp_count 
    FROM cafeteria_items 
    WHERE (name LIKE '%test%' OR description LIKE '%test%') 
    AND category = 'مشروبات';
    SET @search_query_result = 'نجح';
END;

SELECT 
    'استعلام البحث والفلترة' AS test_query,
    @search_query_result AS result,
    CASE 
        WHEN @search_query_result = 'نجح' THEN '✅'
        ELSE '❌'
    END AS status;

-- =====================================================
-- 4. اختبار العمليات الأساسية
-- =====================================================

SELECT '⚙️ اختبار العمليات الأساسية:' AS section;

-- اختبار إدراج منتج جديد
SET @insert_test_result = 'فشل';
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET @insert_test_result = 'فشل';
    
    INSERT INTO cafeteria_items (name, price, category, description, client_id) 
    VALUES ('منتج تجريبي', 10.00, 'مشروبات', 'منتج للاختبار', 1);
    SET @insert_test_result = 'نجح';
    
    -- حذف المنتج التجريبي
    DELETE FROM cafeteria_items WHERE name = 'منتج تجريبي' AND description = 'منتج للاختبار';
END;

SELECT 
    'إدراج منتج جديد' AS operation,
    @insert_test_result AS result,
    CASE 
        WHEN @insert_test_result = 'نجح' THEN '✅'
        ELSE '❌'
    END AS status;

-- اختبار إدراج تصنيف جديد
SET @category_insert_result = 'فشل';
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET @category_insert_result = 'فشل';
    
    INSERT INTO categories (name, description, client_id) 
    VALUES ('تصنيف تجريبي', 'تصنيف للاختبار', 1);
    SET @category_insert_result = 'نجح';
    
    -- حذف التصنيف التجريبي
    DELETE FROM categories WHERE name = 'تصنيف تجريبي' AND description = 'تصنيف للاختبار';
END;

SELECT 
    'إدراج تصنيف جديد' AS operation,
    @category_insert_result AS result,
    CASE 
        WHEN @category_insert_result = 'نجح' THEN '✅'
        ELSE '❌'
    END AS status;

-- =====================================================
-- 5. اختبار الفهارس
-- =====================================================

SELECT '📇 اختبار الفهارس:' AS section;

SELECT 
    index_name,
    table_name,
    column_name,
    '✅ موجود' AS status
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name IN ('cafeteria_items', 'categories', 'devices', 'sessions')
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name;

-- =====================================================
-- 6. اختبار الأداء
-- =====================================================

SELECT '⚡ اختبار الأداء:' AS section;

-- قياس وقت تنفيذ استعلام معقد
SET @start_time = NOW(6);

SELECT COUNT(*) INTO @performance_result
FROM cafeteria_items ci
LEFT JOIN categories cat ON ci.category = cat.name
WHERE ci.client_id = 1;

SET @end_time = NOW(6);
SET @execution_time = TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000;

SELECT 
    'استعلام معقد مع JOIN' AS test_type,
    @performance_result AS records_processed,
    CONCAT(@execution_time, ' ms') AS execution_time,
    CASE 
        WHEN @execution_time < 100 THEN '✅ سريع'
        WHEN @execution_time < 500 THEN '⚠️ متوسط'
        ELSE '❌ بطيء'
    END AS performance_status;

-- =====================================================
-- 7. اختبار التوافق مع PHP
-- =====================================================

SELECT '🐘 اختبار التوافق مع PHP:' AS section;

-- اختبار استعلامات مشابهة لما يستخدمه cafeteria.php
SELECT 
    'استعلام عرض المنتجات' AS php_query_test,
    COUNT(*) AS result_count,
    '✅ متوافق' AS status
FROM cafeteria_items 
WHERE client_id = 1 
ORDER BY COALESCE(category, 'غير محدد'), name 
LIMIT 10;

-- اختبار استعلام التصنيفات
SELECT 
    'استعلام التصنيفات' AS php_query_test,
    COUNT(*) AS result_count,
    '✅ متوافق' AS status
FROM categories 
WHERE client_id = 1 
ORDER BY name;

-- =====================================================
-- 8. تقرير النتائج النهائي
-- =====================================================

SELECT '📋 تقرير النتائج النهائي:' AS section;

-- حساب نسبة نجاح الاختبارات
SET @total_tests = 10; -- عدد الاختبارات الأساسية
SET @passed_tests = 0;

-- فحص النتائج وحساب النجاح
SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_schema = DATABASE() AND table_name = 'cafeteria_items' AND column_name = 'category'
        ) > 0 THEN @passed_tests := @passed_tests + 1
        ELSE @passed_tests
    END INTO @temp;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM categories) >= 3 
        THEN @passed_tests := @passed_tests + 1
        ELSE @passed_tests
    END INTO @temp;

-- النتيجة النهائية
SELECT 
    '🎯 النتيجة النهائية' AS summary,
    CONCAT(@passed_tests, '/', @total_tests) AS tests_passed,
    CONCAT(ROUND((@passed_tests / @total_tests) * 100, 1), '%') AS success_rate,
    CASE 
        WHEN @passed_tests = @total_tests THEN '🎉 ممتاز - جميع الاختبارات نجحت'
        WHEN @passed_tests >= (@total_tests * 0.8) THEN '✅ جيد - معظم الاختبارات نجحت'
        WHEN @passed_tests >= (@total_tests * 0.6) THEN '⚠️ مقبول - بعض المشاكل موجودة'
        ELSE '❌ يحتاج إصلاح - مشاكل كثيرة'
    END AS overall_status;

-- توصيات
SELECT '💡 التوصيات:' AS recommendations_section;

SELECT 
    CASE 
        WHEN @passed_tests = @total_tests THEN 'النظام جاهز للاستخدام! 🚀'
        WHEN @passed_tests >= (@total_tests * 0.8) THEN 'قم بمراجعة الاختبارات الفاشلة وإصلاحها'
        ELSE 'يُنصح بتشغيل سكريبت الإصلاح السريع مرة أخرى'
    END AS recommendation;

SELECT '✅ انتهى اختبار الإصلاحات' AS final_message;
