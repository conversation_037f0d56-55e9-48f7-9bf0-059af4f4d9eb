/**
 * نظام تخصيص المظهر والألوان
 * Theme Customizer System
 */

class ThemeCustomizer {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCurrentTheme();
    }

    bindEvents() {
        // أحداث تغيير الألوان
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="color"]')) {
                this.updateColorPreview();
            }
        });

        // أحداث الألوان المحددة مسبقاً
        document.querySelectorAll('.preset-color').forEach(button => {
            button.addEventListener('click', (e) => {
                this.applyPresetColors(e.target);
            });
        });

        // أحداث تغيير نمط الهيدر
        const headerStyleSelect = document.getElementById('header_style');
        if (headerStyleSelect) {
            headerStyleSelect.addEventListener('change', () => {
                this.updateSidebarPositionState();
                this.previewHeaderStyle();
            });
        }

        // زر إعادة التعيين
        const resetBtn = document.getElementById('resetThemeBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToDefaults();
            });
        }

        // حفظ الإعدادات
        const themeForm = document.getElementById('themeForm');
        if (themeForm) {
            themeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveThemeSettings();
            });
        }
    }

    updateColorPreview() {
        const primaryColor = document.getElementById('primary_color')?.value || '#0d6efd';
        const secondaryColor = document.getElementById('secondary_color')?.value || '#6c757d';
        const accentColor = document.getElementById('accent_color')?.value || '#20c997';

        // تحديث المعاينة
        const previewHeader = document.querySelector('.preview-header');
        const primaryBtn = document.querySelector('.preview-content .btn:nth-child(1)');
        const secondaryBtn = document.querySelector('.preview-content .btn:nth-child(2)');
        const accentBtn = document.querySelector('.preview-content .btn:nth-child(3)');

        if (previewHeader) previewHeader.style.backgroundColor = primaryColor;
        if (primaryBtn) primaryBtn.style.backgroundColor = primaryColor;
        if (secondaryBtn) secondaryBtn.style.backgroundColor = secondaryColor;
        if (accentBtn) accentBtn.style.backgroundColor = accentColor;

        // تحديث قيم النص
        this.updateColorTextInputs();
    }

    updateColorTextInputs() {
        const colorInputs = ['primary_color', 'secondary_color', 'accent_color'];
        
        colorInputs.forEach(inputId => {
            const colorInput = document.getElementById(inputId);
            const textInput = colorInput?.nextElementSibling;
            
            if (colorInput && textInput) {
                textInput.value = colorInput.value;
            }
        });
    }

    applyPresetColors(button) {
        const primaryColor = button.dataset.primary;
        const secondaryColor = button.dataset.secondary;
        const accentColor = button.dataset.accent;

        // تحديث قيم الألوان
        const primaryInput = document.getElementById('primary_color');
        const secondaryInput = document.getElementById('secondary_color');
        const accentInput = document.getElementById('accent_color');

        if (primaryInput) primaryInput.value = primaryColor;
        if (secondaryInput) secondaryInput.value = secondaryColor;
        if (accentInput) accentInput.value = accentColor;

        // تحديث المعاينة
        this.updateColorPreview();

        // إظهار علامة الاختيار
        this.updatePresetSelection(button);

        // تأثير بصري
        this.animateButton(button);
    }

    updatePresetSelection(selectedButton) {
        // إخفاء جميع علامات الاختيار
        document.querySelectorAll('.preset-color .fas.fa-check').forEach(icon => {
            icon.style.display = 'none';
        });

        // إظهار علامة الاختيار على الزر المحدد
        const selectedIcon = selectedButton.querySelector('.fas.fa-check');
        if (selectedIcon) {
            selectedIcon.style.display = 'inline';
        }
    }

    animateButton(button) {
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 150);
    }

    updateSidebarPositionState() {
        const headerStyleSelect = document.getElementById('header_style');
        const sidebarPositionSelect = document.getElementById('sidebar_position');
        const sidebarPositionDiv = sidebarPositionSelect?.closest('.col-md-6');

        if (!headerStyleSelect || !sidebarPositionSelect || !sidebarPositionDiv) return;

        const headerStyle = headerStyleSelect.value;

        if (headerStyle === 'sidebar') {
            sidebarPositionDiv.style.opacity = '1';
            sidebarPositionSelect.disabled = false;
        } else {
            sidebarPositionDiv.style.opacity = '0.5';
            sidebarPositionSelect.disabled = true;
        }
    }

    previewHeaderStyle() {
        const headerStyle = document.getElementById('header_style')?.value;
        
        if (headerStyle === 'sidebar') {
            this.showSidebarPreview();
        } else {
            this.showTopHeaderPreview();
        }
    }

    showSidebarPreview() {
        // يمكن إضافة معاينة للقائمة الجانبية هنا
        console.log('معاينة القائمة الجانبية');
    }

    showTopHeaderPreview() {
        // يمكن إضافة معاينة للهيدر العلوي هنا
        console.log('معاينة الهيدر العلوي');
    }

    resetToDefaults() {
        if (!confirm('هل تريد إعادة تعيين جميع إعدادات المظهر إلى الافتراضية؟')) {
            return;
        }

        // الألوان الافتراضية
        const defaults = {
            primary_color: '#0d6efd',
            secondary_color: '#6c757d',
            accent_color: '#20c997',
            header_style: 'top',
            sidebar_position: 'right',
            theme_mode: 'light'
        };

        // تطبيق القيم الافتراضية
        Object.keys(defaults).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = defaults[key];
            }
        });

        // تحديث المعاينة
        this.updateColorPreview();
        this.updateSidebarPositionState();

        // إزالة علامات الاختيار من الألوان المحددة مسبقاً
        document.querySelectorAll('.preset-color .fas.fa-check').forEach(icon => {
            icon.style.display = 'none';
        });

        // إظهار علامة الاختيار على اللون الافتراضي (الأول)
        const firstPresetButton = document.querySelector('.preset-color');
        if (firstPresetButton) {
            this.updatePresetSelection(firstPresetButton);
        }
    }

    async saveThemeSettings() {
        const formData = new FormData();
        
        // جمع البيانات من النموذج
        const fields = ['primary_color', 'secondary_color', 'accent_color', 'header_style', 'sidebar_position', 'theme_mode'];
        
        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                formData.append(field, element.value);
            }
        });

        try {
            // إظهار مؤشر التحميل
            this.showLoading();

            const response = await fetch('api/save_theme_settings.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('تم حفظ إعدادات المظهر بنجاح', 'success');
                
                // إعادة تحميل CSS المخصص
                this.reloadCustomCSS();
                
                // إعادة تحميل الصفحة بعد ثانيتين لتطبيق التغييرات
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                this.showNotification('خطأ: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Error saving theme settings:', error);
            this.showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadCurrentTheme() {
        try {
            const response = await fetch('api/get_theme_settings.php');
            const result = await response.json();

            if (result.success && result.data) {
                this.applyThemeData(result.data);
            }
        } catch (error) {
            console.error('Error loading theme settings:', error);
        }
    }

    applyThemeData(data) {
        // تطبيق البيانات على النموذج
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element && data[key]) {
                element.value = data[key];
            }
        });

        // تحديث المعاينة
        this.updateColorPreview();
        this.updateSidebarPositionState();
    }

    reloadCustomCSS() {
        const customCSSLink = document.querySelector('link[href*="theme-css.php"]');
        if (customCSSLink) {
            const newHref = customCSSLink.href.split('?')[0] + '?v=' + Date.now();
            customCSSLink.href = newHref;
        }
    }

    showLoading() {
        // يمكن تحسين هذه الدالة لعرض مؤشر تحميل أفضل
        const submitBtn = document.querySelector('#themeForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        }
    }

    hideLoading() {
        const submitBtn = document.querySelector('#themeForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ إعدادات المظهر';
        }
    }

    showNotification(message, type) {
        // يمكن تحسين هذه الدالة لعرض إشعارات أفضل
        if (type === 'success') {
            alert('✅ ' + message);
        } else {
            alert('❌ ' + message);
        }
    }
}

// تهيئة نظام تخصيص المظهر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new ThemeCustomizer();
});
