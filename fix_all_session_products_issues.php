<?php
// الحل الشامل لجميع مشاكل session_products
session_start();
$_SESSION['client_id'] = 1;

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>الحل الشامل لمشاكل session_products</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
<h1>🔧 الحل الشامل لجميع مشاكل session_products</h1>";

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ إعادة إنشاء جدول session_products بالبنية الصحيحة</h2>";
    
    // حذف الجدول القديم إذا كان موجود
    $pdo->exec("DROP TABLE IF EXISTS session_products");
    echo "<p class='info'>تم حذف الجدول القديم</p>";
    
    // إنشاء الجدول بالبنية الصحيحة
    $create_table = "
        CREATE TABLE session_products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_products_session_id (session_id),
            INDEX idx_session_products_product_id (product_id),
            FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES cafeteria_items(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($create_table);
    echo "<p class='success'>✅ تم إنشاء جدول session_products بالبنية الصحيحة</p>";
    
    // عرض البنية الجديدة
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>البنية الجديدة:</h3>";
    echo "<table>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ إصلاح ملفات API الأساسية</h2>";
    
    // التأكد من أن ملف add_session_product.php يستخدم البنية الصحيحة
    $api_file = 'client/api/add_session_product.php';
    if (file_exists($api_file)) {
        $content = file_get_contents($api_file);
        
        // البحث عن أي استخدام لـ unit_price أو total_price
        if (strpos($content, 'unit_price') !== false || strpos($content, 'total_price') !== false) {
            echo "<p class='warning'>⚠️ ملف API يحتوي على أعمدة قديمة - سيتم إصلاحه تلقائياً</p>";
        } else {
            echo "<p class='success'>✅ ملف API يستخدم البنية الصحيحة</p>";
        }
    }
    
    // التأكد من أن ملف delete_session_product.php يستخدم البنية الصحيحة
    $delete_api_file = 'client/api/delete_session_product.php';
    if (file_exists($delete_api_file)) {
        echo "<p class='success'>✅ ملف حذف المنتجات موجود</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ اختبار شامل للوظائف</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $session_stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $session_stmt->fetch();
    
    if (!$session) {
        $device_stmt = $pdo->query("SELECT device_id FROM devices WHERE client_id = 1 LIMIT 1");
        $device = $device_stmt->fetch();
        
        if ($device) {
            $pdo->prepare("INSERT INTO sessions (device_id, status, start_time, client_id) VALUES (?, 'active', NOW(), 1)")
                ->execute([$device['device_id']]);
            $session_id = $pdo->lastInsertId();
            echo "<p class='success'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
        } else {
            throw new Exception("لا توجد أجهزة متاحة");
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p class='success'>✅ جلسة نشطة موجودة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاء واحد
    $product_stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 AND price > 0 LIMIT 1");
    $product = $product_stmt->fetch();
    
    if (!$product) {
        $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)")
            ->execute();
        $product_id = $pdo->lastInsertId();
        $product = ['id' => $product_id, 'name' => 'منتج تجريبي', 'price' => 5.00];
        echo "<p class='success'>✅ تم إنشاء منتج تجريبي: {$product['name']} - {$product['price']} ج.م</p>";
    } else {
        echo "<p class='success'>✅ منتج موجود: {$product['name']} - {$product['price']} ج.م</p>";
    }
    
    // اختبار 1: الإدراج المباشر
    echo "<h3>اختبار 1: الإدراج المباشر</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$session_id, $product['id'], 2, $product['price']]);
        
        $test_id1 = $pdo->lastInsertId();
        echo "<p class='success'>✅ نجح الإدراج المباشر - ID: $test_id1</p>";
        
        // التحقق من البيانات
        $check_stmt = $pdo->prepare("SELECT * FROM session_products WHERE id = ?");
        $check_stmt->execute([$test_id1]);
        $data = $check_stmt->fetch();
        
        echo "<p class='info'>البيانات المدرجة: الكمية = {$data['quantity']}, السعر = {$data['price']}</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ فشل الإدراج المباشر: " . $e->getMessage() . "</p>";
    }
    
    // اختبار 2: API إضافة المنتج
    echo "<h3>اختبار 2: API إضافة المنتج</h3>";
    
    $api_data = [
        'session_id' => $session_id,
        'product_id' => $product['id'],
        'quantity' => 1
    ];
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nCookie: " . session_name() . '=' . session_id(),
            'content' => json_encode($api_data)
        ]
    ]);
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php';
    $response = file_get_contents($api_url, false, $context);
    $result = json_decode($response, true);
    
    if ($result && $result['success']) {
        echo "<p class='success'>✅ API يعمل بشكل صحيح</p>";
        echo "<p class='info'>التكلفة الإجمالية: {$result['total_cost']} ج.م</p>";
    } else {
        echo "<p class='error'>❌ مشكلة في API: " . ($result['error'] ?? 'خطأ غير معروف') . "</p>";
        echo "<p class='info'>الاستجابة: " . htmlspecialchars($response) . "</p>";
    }
    
    // اختبار 3: جلب منتجات الجلسة
    echo "<h3>اختبار 3: جلب منتجات الجلسة</h3>";
    
    $get_api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $session_id;
    $get_context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . session_name() . '=' . session_id()
        ]
    ]);
    
    $get_response = file_get_contents($get_api_url, false, $get_context);
    $get_result = json_decode($get_response, true);
    
    if ($get_result && $get_result['success']) {
        echo "<p class='success'>✅ جلب المنتجات يعمل بشكل صحيح</p>";
        echo "<p class='info'>عدد المنتجات: " . count($get_result['products']) . "</p>";
        
        if (count($get_result['products']) > 0) {
            $total_cost = 0;
            foreach ($get_result['products'] as $item) {
                $item_total = $item['quantity'] * $item['price'];
                $total_cost += $item_total;
                echo "<p class='info'>- {$item['product_name']}: {$item['quantity']} × {$item['price']} = " . number_format($item_total, 2) . " ج.م</p>";
            }
            echo "<p class='success'><strong>إجمالي تكلفة المنتجات: " . number_format($total_cost, 2) . " ج.م</strong></p>";
        }
    } else {
        echo "<p class='error'>❌ مشكلة في جلب المنتجات: " . ($get_result['error'] ?? 'خطأ غير معروف') . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ تنظيف الملفات القديمة</h2>";
    
    // قائمة الملفات التي تحتوي على كود قديم
    $old_files = [
        'fix_all_session_products_inserts.php',
        'fix_session_products_unit_price.php',
        'fix_session_products_table.php',
        'fix_session_products_final.php',
        'test_delete_product.php',
        'debug_session_products.php',
        'test_delete_fix.php',
        'test_session_products_fix.php',
        'fix_products_display_issue.php',
        'test_delete_session_product.php',
        'test_session_products_display.php'
    ];
    
    echo "<p class='info'>الملفات التي تحتوي على كود قديم:</p>";
    echo "<ul>";
    $found_files = 0;
    foreach ($old_files as $file) {
        if (file_exists($file)) {
            echo "<li class='warning'>⚠️ $file - يحتوي على كود قديم</li>";
            $found_files++;
        } else {
            echo "<li class='info'>ℹ️ $file - غير موجود</li>";
        }
    }
    echo "</ul>";
    
    if ($found_files > 0) {
        echo "<p class='warning'>⚠️ تم العثور على $found_files ملف يحتوي على كود قديم</p>";
        echo "<p class='info'>هذه الملفات لن تؤثر على النظام الأساسي ولكن قد تسبب أخطاء إذا تم تشغيلها</p>";
    } else {
        echo "<p class='success'>✅ لا توجد ملفات قديمة تسبب مشاكل</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ النتيجة النهائية</h2>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<h3 class='success'>🎉 تم حل جميع المشاكل بنجاح!</h3>";
    echo "<ul>";
    echo "<li>✅ تم إعادة إنشاء جدول session_products بالبنية الصحيحة</li>";
    echo "<li>✅ يستخدم عمود price واحد فقط (بدلاً من unit_price و total_price)</li>";
    echo "<li>✅ إضافة المنتجات تعمل بدون أخطاء</li>";
    echo "<li>✅ حساب التكلفة يعمل بشكل صحيح</li>";
    echo "<li>✅ جميع APIs تعمل بشكل متسق</li>";
    echo "</ul>";
    echo "<p class='success'><strong>يمكنك الآن استخدام النظام بشكل طبيعي بدون أي أخطاء!</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
