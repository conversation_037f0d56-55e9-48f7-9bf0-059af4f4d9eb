<?php
/**
 * تشخيص متقدم لمشكلة التكلفة - PlayGood
 * فحص دقيق لسبب ظهور اسم العميل بدلاً من التكلفة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص متقدم لمشكلة التكلفة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص هيكل جدول الأجهزة
    echo "<h2>1. فحص هيكل جدول الأجهزة</h2>";
    
    try {
        $columns = $pdo->query("DESCRIBE devices")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>اسم العمود</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>NULL</th>";
        echo "<th style='padding: 8px;'>المفتاح</th>";
        echo "<th style='padding: 8px;'>القيمة الافتراضية</th>";
        echo "</tr>";
        
        $has_hourly_rate = false;
        $has_single_rate = false;
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Key'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Default'] . "</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'hourly_rate') $has_hourly_rate = true;
            if ($column['Field'] === 'single_rate') $has_single_rate = true;
        }
        echo "</table>";
        
        echo "<p><strong>العمود hourly_rate موجود:</strong> " . ($has_hourly_rate ? "✅ نعم" : "❌ لا") . "</p>";
        echo "<p><strong>العمود single_rate موجود:</strong> " . ($has_single_rate ? "✅ نعم" : "❌ لا") . "</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص هيكل الجدول: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص بيانات الأجهزة الفعلية
    echo "<h2>2. فحص بيانات الأجهزة الفعلية</h2>";
    
    try {
        $devices = $pdo->query("
            SELECT device_id, device_name, device_type, 
                   single_rate, multi_rate, 
                   CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns 
                                   WHERE table_name = 'devices' AND column_name = 'hourly_rate') 
                        THEN hourly_rate ELSE NULL END as hourly_rate
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الأجهزة: <strong>" . count($devices) . "</strong></p>";
        
        if (count($devices) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>single_rate</th>";
            echo "<th style='padding: 8px;'>multi_rate</th>";
            echo "<th style='padding: 8px;'>hourly_rate</th>";
            echo "</tr>";
            
            foreach ($devices as $device) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . ($device['single_rate'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: green;'>" . ($device['multi_rate'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . ($device['hourly_rate'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص بيانات الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 3. محاكاة الاستعلام المستخدم في صفحة الجلسات
    echo "<h2>3. محاكاة الاستعلام المستخدم في صفحة الجلسات</h2>";
    
    try {
        // نفس الاستعلام المستخدم في sessions.php
        $sessions_query = "
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   c.name as customer_name,
                   c.phone as customer_phone,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1 AND s.status = 'active'
            ORDER BY s.start_time DESC
        ";
        
        $sessions = $pdo->query($sessions_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات النشطة: <strong>" . count($sessions) . "</strong></p>";
        
        if (count($sessions) > 0) {
            foreach ($sessions as $session) {
                echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 15px 0; border-radius: 8px; background: #f8f9fa;'>";
                echo "<h4 style='color: #007bff;'>🎮 جلسة رقم: " . $session['session_id'] . "</h4>";
                
                // عرض البيانات الأساسية
                echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;'>";
                
                echo "<div>";
                echo "<h5 style='color: #28a745;'>📊 بيانات الجلسة:</h5>";
                echo "<p><strong>اسم الجهاز:</strong> " . htmlspecialchars($session['device_name']) . "</p>";
                echo "<p><strong>نوع الجهاز:</strong> " . htmlspecialchars($session['device_type']) . "</p>";
                echo "<p><strong>اسم العميل:</strong> " . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</p>";
                echo "<p><strong>المدة (دقيقة):</strong> " . ($session['duration_minutes'] ?? 0) . "</p>";
                echo "</div>";
                
                echo "<div>";
                echo "<h5 style='color: #dc3545;'>💰 بيانات الأسعار:</h5>";
                echo "<p><strong>hourly_rate:</strong> <span style='color: red;'>" . ($session['hourly_rate'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>single_rate:</strong> <span style='color: blue;'>" . ($session['single_rate'] ?? 'NULL') . "</span></p>";
                echo "<p><strong>multi_rate:</strong> <span style='color: green;'>" . ($session['multi_rate'] ?? 'NULL') . "</span></p>";
                echo "</div>";
                
                echo "</div>";
                
                // حساب التكلفة خطوة بخطوة
                echo "<h5 style='color: #6f42c1;'>🧮 حساب التكلفة خطوة بخطوة:</h5>";
                
                $duration_minutes = $session['duration_minutes'] ?? 0;
                $hourly_rate_from_db = $session['hourly_rate'] ?? null;
                $single_rate_from_db = $session['single_rate'] ?? null;
                
                echo "<p><strong>الخطوة 1:</strong> المدة = $duration_minutes دقيقة</p>";
                echo "<p><strong>الخطوة 2:</strong> hourly_rate من قاعدة البيانات = " . ($hourly_rate_from_db ?? 'NULL') . "</p>";
                echo "<p><strong>الخطوة 3:</strong> single_rate من قاعدة البيانات = " . ($single_rate_from_db ?? 'NULL') . "</p>";
                
                // تطبيق نفس المنطق المستخدم في الكود
                $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                echo "<p><strong>الخطوة 4:</strong> السعر المستخدم = $hourly_rate (single_rate أولاً، ثم hourly_rate)</p>";
                
                $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                echo "<p><strong>الخطوة 5:</strong> تكلفة الوقت = ceil($duration_minutes ÷ 60) × $hourly_rate = <span style='color: purple; font-weight: bold;'>$time_cost ج.م</span></p>";
                
                // فحص المنتجات
                try {
                    $products_stmt = $pdo->prepare("
                        SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                        FROM session_products sp
                        WHERE sp.session_id = ?
                    ");
                    $products_stmt->execute([$session['session_id']]);
                    $products_cost = $products_stmt->fetchColumn() ?: 0;
                    
                    echo "<p><strong>الخطوة 6:</strong> تكلفة المنتجات = $products_cost ج.م</p>";
                    
                    $total_cost = $time_cost + $products_cost;
                    echo "<p><strong>النتيجة النهائية:</strong> <span style='background: yellow; padding: 5px; font-weight: bold; font-size: 18px;'>$total_cost ج.م</span></p>";
                    
                } catch (PDOException $e) {
                    echo "<p style='color: red;'><strong>خطأ في جلب المنتجات:</strong> " . $e->getMessage() . "</p>";
                    echo "<p><strong>النتيجة النهائية:</strong> <span style='background: yellow; padding: 5px; font-weight: bold; font-size: 18px;'>$time_cost ج.م</span></p>";
                }
                
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
            
            // إنشاء جلسة تجريبية
            echo "<h4>إنشاء جلسة تجريبية للاختبار:</h4>";
            
            try {
                // البحث عن جهاز متاح
                $device = $pdo->query("
                    SELECT device_id, device_name, single_rate 
                    FROM devices 
                    WHERE client_id = 1 AND status = 'available' 
                    LIMIT 1
                ")->fetch(PDO::FETCH_ASSOC);
                
                if ($device) {
                    // إنشاء جلسة تجريبية
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO sessions (device_id, start_time, status, client_id) 
                        VALUES (?, NOW(), 'active', ?)
                    ");
                    $insert_stmt->execute([$device['device_id'], 1]);
                    
                    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية للجهاز: " . htmlspecialchars($device['device_name']) . "</p>";
                    echo "<p><a href='debug_cost_issue.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تحميل الصفحة</a></p>";
                } else {
                    echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة لإنشاء جلسة تجريبية</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إنشاء جلسة تجريبية: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في محاكاة الاستعلام: " . $e->getMessage() . "</p>";
    }
    
    // 4. فحص المتغيرات في PHP
    echo "<h2>4. فحص المتغيرات في PHP</h2>";
    
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>🔧 معلومات تقنية:</h5>";
    echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";
    echo "<p><strong>إصدار MySQL:</strong> " . $pdo->query('SELECT VERSION()')->fetchColumn() . "</p>";
    echo "<p><strong>المنطقة الزمنية:</strong> " . date_default_timezone_get() . "</p>";
    echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 بناءً على النتائج أعلاه:</h4>";
echo "<ol>";
echo "<li><strong>إذا كانت التكلفة تظهر صحيحة هنا:</strong> المشكلة في عرض الصفحة أو JavaScript</li>";
echo "<li><strong>إذا كانت التكلفة 0:</strong> تحقق من قيم single_rate في الأجهزة</li>";
echo "<li><strong>إذا كان hourly_rate = NULL:</strong> هذا طبيعي، سيتم استخدام single_rate</li>";
echo "<li><strong>إذا كان اسم العميل يظهر بدلاً من الرقم:</strong> مشكلة في الكود أو قاعدة البيانات</li>";
echo "</ol>";
echo "</div>";

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='quick_data_fix.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إصلاح البيانات</a>";
echo "<a href='fix_display_issue.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>التشخيص العام</a>";
echo "</div>";

echo "</div>";
?>
