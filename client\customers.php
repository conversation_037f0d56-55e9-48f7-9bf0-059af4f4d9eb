<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة العملاء
    if (!hasPagePermission('customers')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة العملاء';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - التحقق من الوصول للصفحة والصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('customers')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_customers')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة العملاء";
$active_page = "customers";

// إضافة عميل جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من البيانات المطلوبة
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $notes = trim($_POST['notes'] ?? '');

        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception('اسم العميل مطلوب');
        }

        if (empty($phone)) {
            throw new Exception('رقم الهاتف مطلوب');
        }

        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }

        // التحقق من عدم تكرار رقم الهاتف
        $check_phone = $pdo->prepare("SELECT customer_id FROM customers WHERE phone = ? AND client_id = ?");
        $check_phone->execute([$phone, $client_id]);
        if ($check_phone->rowCount() > 0) {
            throw new Exception('رقم الهاتف موجود مسبقاً');
        }

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!empty($email)) {
            $check_email = $pdo->prepare("SELECT customer_id FROM customers WHERE email = ? AND client_id = ?");
            $check_email->execute([$email, $client_id]);
            if ($check_email->rowCount() > 0) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً');
            }
        }

        // إضافة العميل (بدون عمود created_by لتجنب مشكلة المفتاح الخارجي)
        $stmt = $pdo->prepare("
            INSERT INTO customers (
                name, phone, email, notes, client_id, created_at
            ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        $result = $stmt->execute([
            $name,
            $phone,
            $email ?: null,
            $notes ?: null,
            $client_id
        ]);

        if ($result) {
            $customer_id = $pdo->lastInsertId();
            $_SESSION['success'] = "تم إضافة العميل بنجاح - رقم العميل: $customer_id";
        } else {
            throw new Exception('فشل في إضافة العميل');
        }

        header('Location: customers.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
        error_log('خطأ في إضافة العميل: ' . $e->getMessage());
    } catch (PDOException $e) {
        $_SESSION['error'] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        error_log('خطأ PDO في إضافة العميل: ' . $e->getMessage());
    }
}

// جلب العملاء
$stmt = $pdo->prepare("
    SELECT
        c.*,
        COUNT(DISTINCT s.session_id) as sessions_count,
        COALESCE(SUM(CASE WHEN i.invoice_id IS NOT NULL THEN i.total_cost ELSE 0 END), 0) as total_spent
    FROM customers c
    LEFT JOIN sessions s ON c.customer_id = s.customer_id
        AND (s.status = 'completed' OR s.status IS NULL)
    LEFT JOIN invoices i ON s.session_id = i.session_id
    WHERE c.client_id = ?
    GROUP BY
        c.customer_id,
        c.name,
        c.phone,
        c.email,
        c.notes,
        c.created_at
    ORDER BY c.created_at DESC
");
$stmt->execute([$client_id]);
$customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-plus-circle me-2"></i>إضافة عميل جديد
            </button>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>قائمة العملاء</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد الجلسات</th>
                            <th>إجمالي المصروفات</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($customers as $customer): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($customer['name']); ?></td>
                                <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                <td><?php echo $customer['email'] ? htmlspecialchars($customer['email']) : '-'; ?></td>
                                <td><?php echo $customer['sessions_count']; ?></td>
                                <td><?php echo number_format($customer['total_spent'], 2); ?> ج.م</td>
                                <td><?php echo date('Y-m-d', strtotime($customer['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-info btn-sm" onclick="viewCustomerDetails(<?php echo $customer['customer_id']; ?>)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="editCustomer(<?php echo $customer['customer_id']; ?>)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteCustomer(<?php echo $customer['customer_id']; ?>, '<?php echo htmlspecialchars($customer['name'], ENT_QUOTES); ?>')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة عميل -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="customers.php" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الاسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required
                               minlength="2" maxlength="255" placeholder="أدخل اسم العميل">
                        <div class="invalid-feedback">
                            يرجى إدخال اسم العميل (2-255 حرف)
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" name="phone" required
                               pattern="[0-9+\-\s]+" minlength="10" maxlength="20"
                               placeholder="مثال: 01234567890">
                        <div class="invalid-feedback">
                            يرجى إدخال رقم هاتف صحيح (10-20 رقم)
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email"
                               placeholder="مثال: <EMAIL>">
                        <div class="invalid-feedback">
                            يرجى إدخال بريد إلكتروني صحيح
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3"
                                  maxlength="1000" placeholder="ملاحظات إضافية عن العميل (اختياري)"></textarea>
                        <div class="form-text">الحد الأقصى 1000 حرف</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action="customers.php"]');
    const submitBtn = document.querySelector('#submitBtn, button[type="submit"]');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // إزالة رسائل الخطأ السابقة
            form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

            let isValid = true;

            // التحقق من الاسم
            const nameInput = form.querySelector('input[name="name"]');
            if (!nameInput.value.trim() || nameInput.value.trim().length < 2) {
                nameInput.classList.add('is-invalid');
                isValid = false;
            }

            // التحقق من رقم الهاتف
            const phoneInput = form.querySelector('input[name="phone"]');
            const phonePattern = /^[0-9+\-\s]{10,20}$/;
            if (!phoneInput.value.trim() || !phonePattern.test(phoneInput.value.trim())) {
                phoneInput.classList.add('is-invalid');
                isValid = false;
            }

            // التحقق من البريد الإلكتروني (إذا تم إدخاله)
            const emailInput = form.querySelector('input[name="email"]');
            if (emailInput.value.trim()) {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(emailInput.value.trim())) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                }
            }

            if (isValid) {
                // تعطيل الزر لمنع الإرسال المتكرر
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
                }

                // إرسال النموذج
                form.submit();
            } else {
                // عرض رسالة خطأ
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يرجى التحقق من البيانات المدخلة وإصلاح الأخطاء',
                    confirmButtonText: 'حسناً'
                });
            }
        });

        // التحقق الفوري أثناء الكتابة
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    // إزالة رسالة الخطأ إذا تم إصلاح البيانات
                    if (this.name === 'name' && this.value.trim().length >= 2) {
                        this.classList.remove('is-invalid');
                    } else if (this.name === 'phone' && /^[0-9+\-\s]{10,20}$/.test(this.value.trim())) {
                        this.classList.remove('is-invalid');
                    } else if (this.name === 'email' && (!this.value.trim() || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.value.trim()))) {
                        this.classList.remove('is-invalid');
                    }
                }
            });
        });
    }
});

function viewCustomerDetails(customerId) {
    // إضافة كود عرض تفاصيل العميل
    window.location.href = `customer_details.php?id=${customerId}`;
}

function editCustomer(customerId) {
    window.location.href = `edit_customer.php?id=${customerId}`;
}

function deleteCustomer(customerId, customerName) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل أنت متأكد من حذف العميل "${customerName}"؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `delete_customer.php?id=${customerId}`;
        }
    });
}

// إعادة تعيين النموذج عند إغلاق المودال
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addCustomerModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            const form = this.querySelector('form');
            if (form) {
                form.reset();
                form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i>إضافة العميل';
                }
            }
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>