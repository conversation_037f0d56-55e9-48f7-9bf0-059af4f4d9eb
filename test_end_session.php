<?php
/**
 * صفحة اختبار إنهاء الجلسة
 * للتحقق من المشاكل في إنهاء الجلسات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<h1>اختبار إنهاء الجلسة - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// تحديد معرف العميل للاختبار
$client_id = 1;

try {
    echo "<h2>1. فحص الجلسات النشطة</h2>";
    
    $active_sessions_query = $pdo->prepare("
        SELECT 
            s.session_id,
            s.device_id,
            s.start_time,
            s.status,
            d.device_name,
            d.client_id,
            TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ? AND s.status = 'active'
        ORDER BY s.start_time DESC
    ");
    $active_sessions_query->execute([$client_id]);
    $active_sessions = $active_sessions_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($active_sessions) > 0) {
        echo "<p style='color: green;'>✅ يوجد " . count($active_sessions) . " جلسة نشطة</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>معرف الجلسة</th>";
        echo "<th style='padding: 10px;'>الجهاز</th>";
        echo "<th style='padding: 10px;'>وقت البدء</th>";
        echo "<th style='padding: 10px;'>المدة</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>اختبار الإنهاء</th>";
        echo "</tr>";
        
        foreach ($active_sessions as $session) {
            echo "<tr>";
            echo "<td style='padding: 10px; text-align: center;'>" . $session['session_id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($session['device_name']) . "</td>";
            echo "<td style='padding: 10px;'>" . $session['start_time'] . "</td>";
            echo "<td style='padding: 10px;'>" . $session['duration_minutes'] . " دقيقة</td>";
            echo "<td style='padding: 10px;'>" . $session['status'] . "</td>";
            echo "<td style='padding: 10px; text-align: center;'>";
            echo "<a href='client/sessions.php?action=end&session_id=" . $session['session_id'] . "' ";
            echo "style='background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;' ";
            echo "onclick='return confirm(\"هل تريد إنهاء الجلسة؟\")'>إنهاء</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة حالياً</p>";
        
        // إنشاء جلسة تجريبية للاختبار
        echo "<h3>إنشاء جلسة تجريبية للاختبار:</h3>";
        
        // البحث عن جهاز متاح
        $available_device_query = $pdo->prepare("
            SELECT device_id, device_name 
            FROM devices 
            WHERE client_id = ? AND status = 'available' 
            LIMIT 1
        ");
        $available_device_query->execute([$client_id]);
        $available_device = $available_device_query->fetch(PDO::FETCH_ASSOC);
        
        if ($available_device) {
            echo "<p>جهاز متاح: " . htmlspecialchars($available_device['device_name']) . "</p>";
            echo "<a href='?create_test_session=" . $available_device['device_id'] . "' ";
            echo "style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
            echo "إنشاء جلسة تجريبية</a>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة متاحة</p>";
        }
    }
    
    // إنشاء جلسة تجريبية
    if (isset($_GET['create_test_session']) && is_numeric($_GET['create_test_session'])) {
        $device_id = $_GET['create_test_session'];
        
        echo "<h2>2. إنشاء جلسة تجريبية</h2>";
        
        try {
            $pdo->beginTransaction();
            
            // إنشاء الجلسة
            $create_session = $pdo->prepare("
                INSERT INTO sessions (device_id, client_id, start_time, status, created_by)
                VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)
            ");
            $create_session->execute([$device_id, $client_id, $client_id]);
            $new_session_id = $pdo->lastInsertId();
            
            // تحديث حالة الجهاز
            $update_device = $pdo->prepare("UPDATE devices SET status = 'occupied' WHERE device_id = ?");
            $update_device->execute([$device_id]);
            
            $pdo->commit();
            
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية برقم: $new_session_id</p>";
            echo "<p><a href='?' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث الصفحة</a></p>";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "<p style='color: red;'>❌ فشل في إنشاء الجلسة: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>3. فحص جدول sessions</h2>";
    
    // فحص هيكل جدول sessions
    $describe_sessions = $pdo->query("DESCRIBE sessions");
    $sessions_columns = $describe_sessions->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>أعمدة جدول sessions:</h3>";
    echo "<ul>";
    foreach ($sessions_columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . " (" . $column['Null'] . ")</li>";
    }
    echo "</ul>";
    
    echo "<h2>4. فحص جدول devices</h2>";
    
    $devices_query = $pdo->prepare("
        SELECT device_id, device_name, status, client_id 
        FROM devices 
        WHERE client_id = ? 
        ORDER BY device_name
    ");
    $devices_query->execute([$client_id]);
    $devices = $devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices) > 0) {
        echo "<p style='color: green;'>✅ يوجد " . count($devices) . " جهاز</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>معرف الجهاز</th>";
        echo "<th style='padding: 10px;'>اسم الجهاز</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "</tr>";
        
        foreach ($devices as $device) {
            $status_color = $device['status'] === 'available' ? 'green' : ($device['status'] === 'occupied' ? 'orange' : 'red');
            echo "<tr>";
            echo "<td style='padding: 10px; text-align: center;'>" . $device['device_id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($device['device_name']) . "</td>";
            echo "<td style='padding: 10px; color: $status_color; font-weight: bold;'>" . $device['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد أجهزة مسجلة</p>";
    }
    
    echo "<h2>5. اختبار رابط الإنهاء</h2>";
    
    if (isset($_GET['action']) && $_GET['action'] === 'end' && isset($_GET['session_id'])) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🔍 تم استلام طلب إنهاء الجلسة</h3>";
        echo "<p><strong>المعاملات المستلمة:</strong></p>";
        echo "<ul>";
        echo "<li>action: " . htmlspecialchars($_GET['action']) . "</li>";
        echo "<li>session_id: " . htmlspecialchars($_GET['session_id']) . "</li>";
        echo "</ul>";
        
        // محاولة العثور على الجلسة
        $test_session_query = $pdo->prepare("
            SELECT s.*, d.device_name, d.client_id
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE s.session_id = ? AND s.status = 'active' AND d.client_id = ?
        ");
        $test_session_query->execute([$_GET['session_id'], $client_id]);
        $test_session = $test_session_query->fetch(PDO::FETCH_ASSOC);
        
        if ($test_session) {
            echo "<p style='color: green;'>✅ تم العثور على الجلسة النشطة</p>";
            echo "<p><strong>تفاصيل الجلسة:</strong></p>";
            echo "<pre>" . print_r($test_session, true) . "</pre>";
            
            echo "<p><a href='client/sessions.php?action=end&session_id=" . $_GET['session_id'] . "' ";
            echo "style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;' ";
            echo "onclick='return confirm(\"هل تريد إنهاء الجلسة فعلياً؟\")'>إنهاء الجلسة الآن</a></p>";
            
        } else {
            echo "<p style='color: red;'>❌ لم يتم العثور على جلسة نشطة بهذا المعرف</p>";
            
            // البحث عن الجلسة بدون شرط الحالة
            $any_session_query = $pdo->prepare("
                SELECT s.*, d.device_name, d.client_id
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                WHERE s.session_id = ? AND d.client_id = ?
            ");
            $any_session_query->execute([$_GET['session_id'], $client_id]);
            $any_session = $any_session_query->fetch(PDO::FETCH_ASSOC);
            
            if ($any_session) {
                echo "<p style='color: orange;'>⚠️ الجلسة موجودة لكن حالتها: " . $any_session['status'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ الجلسة غير موجودة نهائياً</p>";
            }
        }
        echo "</div>";
    }
    
    echo "<h2>6. روابط مفيدة</h2>";
    echo "<p><a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a></p>";
    echo "<p><a href='client/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";
    echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح شامل</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getFile() . " في السطر " . $e->getLine() . "</p>";
}

echo "</div>";
?>
