<?php
/**
 * سكريپت إعادة تهيئة وإصلاح قاعدة البيانات
 * يقوم بإصلاح جميع المشاكل في البيانات وإعادة تنظيمها
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعادة تهيئة وإصلاح قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        .step-card { border-left: 4px solid #007bff; margin-bottom: 20px; }
        .success-step { border-left-color: #28a745; }
        .error-step { border-left-color: #dc3545; }
        .warning-step { border-left-color: #ffc107; }
        .step-number { 
            background: #007bff; 
            color: white; 
            border-radius: 50%; 
            width: 30px; 
            height: 30px; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            margin-left: 10px;
        }
    </style>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card step-card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-database me-2'></i>
                        إعادة تهيئة وإصلاح قاعدة البيانات
                    </h3>
                </div>
                <div class='card-body'>";

$errors = [];
$success_steps = [];

try {
    // الخطوة 1: إصلاح أسماء الأجهزة
    echo "<div class='step-card'>
            <div class='card-header bg-info text-white'>
                <h5><span class='step-number'>1</span>إصلاح أسماء الأجهزة</h5>
            </div>
            <div class='card-body'>";
    
    // إصلاح أسماء الأجهزة المشبوهة
    $device_fixes = [
        ['old_name' => 'جهاز 1', 'new_name' => 'PlayStation 5 - Station 1', 'type' => 'PS5'],
        ['old_name' => 'جهاز 2', 'new_name' => 'PlayStation 4 - Station 2', 'type' => 'PS4'],
        ['old_name' => 'جهاز 3', 'new_name' => 'Xbox Series - Station 3', 'type' => 'Xbox'],
        ['old_name' => 'جهاز 4', 'new_name' => 'Gaming PC - Station 4', 'type' => 'PC'],
        ['old_name' => 'جهاز 5', 'new_name' => 'PlayStation 4 - Station 5', 'type' => 'PS4'],
        ['old_name' => 'ps 1', 'new_name' => 'PlayStation 4 - Station 1', 'type' => 'PS4']
    ];
    
    $device_update_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_name = ?");
    $fixed_devices = 0;
    
    foreach ($device_fixes as $fix) {
        $device_update_stmt->execute([$fix['new_name'], $fix['old_name']]);
        if ($device_update_stmt->rowCount() > 0) {
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تغيير '{$fix['old_name']}' إلى '{$fix['new_name']}'</p>";
            $fixed_devices++;
        }
    }
    
    // إصلاح الأجهزة الفارغة أو غير المناسبة
    $empty_devices_query = $pdo->query("
        SELECT device_id, device_name, device_type, client_id 
        FROM devices 
        WHERE device_name = '' OR device_name IS NULL OR LENGTH(device_name) < 3
    ");
    $empty_devices = $empty_devices_query->fetchAll(PDO::FETCH_ASSOC);
    
    $counter = 1;
    foreach ($empty_devices as $device) {
        $type_names = [
            'PS5' => 'PlayStation 5',
            'PS4' => 'PlayStation 4',
            'Xbox' => 'Xbox Series',
            'PC' => 'Gaming PC'
        ];
        
        $base_name = $type_names[$device['device_type']] ?? 'Gaming Device';
        $new_name = $base_name . ' - Station ' . $counter;
        
        $device_update_stmt->execute([$new_name, $device['device_name']]);
        echo "<p class='text-info'><i class='fas fa-edit me-2'></i>تم إصلاح جهاز فارغ إلى '$new_name'</p>";
        $counter++;
        $fixed_devices++;
    }
    
    echo "<div class='alert alert-success'><i class='fas fa-check-circle me-2'></i>تم إصلاح $fixed_devices جهاز</div>";
    echo "</div></div>";
    
    // الخطوة 2: إصلاح ربط الجلسات بالعملاء
    echo "<div class='step-card'>
            <div class='card-header bg-warning text-dark'>
                <h5><span class='step-number'>2</span>إصلاح ربط الجلسات بالعملاء</h5>
            </div>
            <div class='card-body'>";
    
    // البحث عن الجلسات بدون عملاء
    $sessions_without_customers = $pdo->query("
        SELECT session_id, device_id, start_time, client_id
        FROM sessions 
        WHERE customer_id IS NULL AND status IN ('active', 'completed')
    ");
    $sessions = $sessions_without_customers->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        // إنشاء عميل افتراضي لكل client
        $clients_query = $pdo->query("SELECT DISTINCT client_id FROM sessions WHERE customer_id IS NULL");
        $clients = $clients_query->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($clients as $client_id) {
            // التحقق من وجود عميل افتراضي
            $default_customer_check = $pdo->prepare("
                SELECT customer_id FROM customers 
                WHERE client_id = ? AND name = 'عميل افتراضي'
            ");
            $default_customer_check->execute([$client_id]);
            $default_customer = $default_customer_check->fetchColumn();
            
            if (!$default_customer) {
                // إنشاء عميل افتراضي
                $create_default_customer = $pdo->prepare("
                    INSERT INTO customers (client_id, name, phone, email, notes) 
                    VALUES (?, 'عميل افتراضي', '00000000000', '<EMAIL>', 'عميل افتراضي للجلسات غير المرتبطة')
                ");
                $create_default_customer->execute([$client_id]);
                $default_customer = $pdo->lastInsertId();
                echo "<p class='text-info'><i class='fas fa-user-plus me-2'></i>تم إنشاء عميل افتراضي للعميل $client_id</p>";
            }
            
            // ربط الجلسات بالعميل الافتراضي
            $update_sessions = $pdo->prepare("
                UPDATE sessions 
                SET customer_id = ? 
                WHERE client_id = ? AND customer_id IS NULL
            ");
            $update_sessions->execute([$default_customer, $client_id]);
            $updated_count = $update_sessions->rowCount();
            
            if ($updated_count > 0) {
                echo "<p class='text-success'><i class='fas fa-link me-2'></i>تم ربط $updated_count جلسة بالعميل الافتراضي</p>";
            }
        }
    } else {
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>جميع الجلسات مرتبطة بعملاء</p>";
    }
    
    echo "</div></div>";
    
    // الخطوة 3: إصلاح جدول الفواتير
    echo "<div class='step-card'>
            <div class='card-header bg-success text-white'>
                <h5><span class='step-number'>3</span>فحص جدول الفواتير</h5>
            </div>
            <div class='card-body'>";

    // التحقق من بنية جدول الفواتير
    $columns_check = $pdo->query("DESCRIBE invoices");
    $columns = $columns_check->fetchAll(PDO::FETCH_ASSOC);

    echo "<h6>أعمدة جدول الفواتير:</h6>";
    echo "<ul class='list-group list-group-flush'>";

    $has_invoice_id = false;
    $has_id = false;

    foreach ($columns as $column) {
        $field = $column['Field'];
        $type = $column['Type'];
        $key = $column['Key'];
        $extra = $column['Extra'];

        if ($field === 'invoice_id') $has_invoice_id = true;
        if ($field === 'id') $has_id = true;

        $icon = ($key === 'PRI') ? 'fas fa-key text-warning' : 'fas fa-table text-muted';
        $badge = ($extra === 'auto_increment') ? '<span class="badge bg-info ms-2">AUTO_INCREMENT</span>' : '';

        echo "<li class='list-group-item'><i class='$icon me-2'></i>$field ($type) $badge</li>";
    }
    echo "</ul>";

    if ($has_invoice_id) {
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول الفواتير يستخدم invoice_id كمفتاح أساسي (صحيح)</p>";
    } else {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>جدول الفواتير لا يحتوي على invoice_id</p>";
    }

    echo "</div></div>";
    
    // الخطوة 4: تنظيف البيانات المكررة
    echo "<div class='step-card'>
            <div class='card-header bg-secondary text-white'>
                <h5><span class='step-number'>4</span>تنظيف البيانات المكررة</h5>
            </div>
            <div class='card-body'>";
    
    // حذف العملاء المكررين (نفس الاسم والهاتف)
    $duplicate_customers = $pdo->query("
        SELECT name, phone, client_id, COUNT(*) as count
        FROM customers 
        GROUP BY name, phone, client_id 
        HAVING COUNT(*) > 1
    ");
    $duplicates = $duplicate_customers->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($duplicates as $duplicate) {
        // الاحتفاظ بأول عميل وحذف الباقي
        $keep_customer = $pdo->prepare("
            SELECT customer_id FROM customers 
            WHERE name = ? AND phone = ? AND client_id = ? 
            ORDER BY customer_id ASC LIMIT 1
        ");
        $keep_customer->execute([$duplicate['name'], $duplicate['phone'], $duplicate['client_id']]);
        $keep_id = $keep_customer->fetchColumn();
        
        // حذف المكررات
        $delete_duplicates = $pdo->prepare("
            DELETE FROM customers 
            WHERE name = ? AND phone = ? AND client_id = ? AND customer_id != ?
        ");
        $delete_duplicates->execute([$duplicate['name'], $duplicate['phone'], $duplicate['client_id'], $keep_id]);
        $deleted_count = $delete_duplicates->rowCount();
        
        if ($deleted_count > 0) {
            echo "<p class='text-warning'><i class='fas fa-trash me-2'></i>تم حذف $deleted_count عميل مكرر للعميل '{$duplicate['name']}'</p>";
        }
    }
    
    echo "</div></div>";
    
    // الخطوة 5: التحقق النهائي
    echo "<div class='step-card success-step'>
            <div class='card-header bg-success text-white'>
                <h5><span class='step-number'>5</span>التحقق النهائي</h5>
            </div>
            <div class='card-body'>";
    
    // إحصائيات نهائية
    $stats = [];
    
    $stats['devices'] = $pdo->query("SELECT COUNT(*) FROM devices")->fetchColumn();
    $stats['customers'] = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
    $stats['sessions'] = $pdo->query("SELECT COUNT(*) FROM sessions")->fetchColumn();
    $stats['invoices'] = $pdo->query("SELECT COUNT(*) FROM invoices")->fetchColumn();
    $stats['sessions_with_customers'] = $pdo->query("SELECT COUNT(*) FROM sessions WHERE customer_id IS NOT NULL")->fetchColumn();
    
    echo "<div class='row'>
            <div class='col-md-6'>
                <h6>إحصائيات قاعدة البيانات:</h6>
                <ul class='list-group'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>الأجهزة</span>
                        <span class='badge bg-primary'>{$stats['devices']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>العملاء</span>
                        <span class='badge bg-info'>{$stats['customers']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>الجلسات</span>
                        <span class='badge bg-warning'>{$stats['sessions']}</span>
                    </li>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>الفواتير</span>
                        <span class='badge bg-success'>{$stats['invoices']}</span>
                    </li>
                </ul>
            </div>
            <div class='col-md-6'>
                <h6>حالة البيانات:</h6>
                <ul class='list-group'>
                    <li class='list-group-item d-flex justify-content-between'>
                        <span>الجلسات المرتبطة بعملاء</span>
                        <span class='badge bg-success'>{$stats['sessions_with_customers']}/{$stats['sessions']}</span>
                    </li>
                </ul>
            </div>
          </div>";
    
    echo "</div></div>";
    
    echo "<div class='alert alert-success mt-4'>
            <h4><i class='fas fa-check-circle me-2'></i>تم إصلاح قاعدة البيانات بنجاح! 🎉</h4>
            <p>تم تطبيق جميع الإصلاحات المطلوبة. يمكنك الآن استخدام النظام بشكل طبيعي.</p>
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='client/sessions.php' class='btn btn-primary me-2'>
                <i class='fas fa-play-circle me-1'></i>اختبار صفحة الجلسات
            </a>
            <a href='client/invoices.php' class='btn btn-success me-2'>
                <i class='fas fa-file-invoice me-1'></i>اختبار صفحة الفواتير
            </a>
            <a href='client/reports.php' class='btn btn-info me-2'>
                <i class='fas fa-chart-bar me-1'></i>اختبار صفحة التقارير
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home me-1'></i>لوحة التحكم
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ!</h5>
            <p>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>يرجى التحقق من إعدادات قاعدة البيانات والمحاولة مرة أخرى.</p>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
