<?php
require_once 'config/database.php';

echo "<h1>اختبار نظام الأوردرات المستقلة</h1>";

try {
    echo "<h2>1. التحقق من بنية قاعدة البيانات</h2>";
    
    // فحص جدول orders
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ جدول orders موجود</p>";
        
        $stmt = $pdo->query("DESCRIBE orders");
        $orders_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>أعمدة جدول orders:</strong> " . implode(', ', $orders_columns) . "</p>";
    } else {
        echo "<p style='color: red;'>✗ جدول orders غير موجود</p>";
    }
    
    // فحص جدول order_items
    $stmt = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ جدول order_items موجود</p>";
        
        $stmt = $pdo->query("DESCRIBE order_items");
        $order_items_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>أعمدة جدول order_items:</strong> " . implode(', ', $order_items_columns) . "</p>";
    } else {
        echo "<p style='color: red;'>✗ جدول order_items غير موجود</p>";
    }
    
    echo "<h2>2. التحقق من الصلاحيات</h2>";
    
    // فحص صلاحيات الأوردرات
    $stmt = $pdo->query("SELECT * FROM permissions WHERE category = 'orders'");
    $orders_permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orders_permissions)) {
        echo "<p style='color: green;'>✓ صلاحيات الأوردرات موجودة:</p>";
        echo "<ul>";
        foreach ($orders_permissions as $permission) {
            echo "<li>{$permission['permission_label']} ({$permission['permission_name']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ صلاحيات الأوردرات غير موجودة</p>";
    }
    
    // فحص صفحات الأوردرات
    $stmt = $pdo->query("SELECT * FROM pages WHERE category = 'orders'");
    $orders_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orders_pages)) {
        echo "<p style='color: green;'>✓ صفحات الأوردرات موجودة:</p>";
        echo "<ul>";
        foreach ($orders_pages as $page) {
            echo "<li>{$page['page_label']} ({$page['page_name']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ صفحات الأوردرات غير موجودة</p>";
    }
    
    echo "<h2>3. التحقق من الملفات</h2>";
    
    $required_files = [
        'client/orders.php' => 'صفحة إدارة الأوردرات',
        'client/api/create_order.php' => 'API إنشاء أوردر',
        'client/api/get_orders.php' => 'API جلب الأوردرات',
        'client/api/get_order_details.php' => 'API تفاصيل الأوردر',
        'client/api/update_order.php' => 'API تحديث الأوردر'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✓ {$description}: {$file}</p>";
        } else {
            echo "<p style='color: red;'>✗ {$description}: {$file} غير موجود</p>";
        }
    }
    
    echo "<h2>4. اختبار إنشاء أوردر تجريبي</h2>";
    
    // التحقق من وجود منتجات
    $stmt = $pdo->query("SELECT COUNT(*) FROM cafeteria_items WHERE client_id = 1");
    $products_count = $stmt->fetchColumn();
    
    if ($products_count > 0) {
        echo "<p style='color: green;'>✓ يوجد {$products_count} منتج في الكافتيريا</p>";
        
        // إنشاء أوردر تجريبي
        $order_number = 'TEST-' . date('YmdHis');
        
        $pdo->beginTransaction();
        
        // إنشاء الأوردر
        $stmt = $pdo->prepare("
            INSERT INTO orders (client_id, order_number, total_amount, status, payment_method, notes, created_by) 
            VALUES (1, ?, 0, 'pending', 'cash', 'أوردر اختبار النظام', 1)
        ");
        $stmt->execute([$order_number]);
        $order_id = $pdo->lastInsertId();
        
        // إضافة منتجات للأوردر
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 3");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $total_amount = 0;
        foreach ($products as $product) {
            $quantity = rand(1, 3);
            $item_total = $product['price'] * $quantity;
            $total_amount += $item_total;
            
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price, total_price) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$order_id, $product['id'], $quantity, $product['price'], $item_total]);
            
            echo "<p>- تم إضافة: {$product['name']} × {$quantity} = {$item_total} ر.س</p>";
        }
        
        // تحديث مجموع الأوردر
        $stmt = $pdo->prepare("UPDATE orders SET total_amount = ? WHERE id = ?");
        $stmt->execute([$total_amount, $order_id]);
        
        $pdo->commit();
        
        echo "<p style='color: green;'>✓ تم إنشاء أوردر تجريبي بنجاح</p>";
        echo "<p><strong>رقم الأوردر:</strong> {$order_number}</p>";
        echo "<p><strong>المجموع الكلي:</strong> {$total_amount} ر.س</p>";
        
    } else {
        echo "<p style='color: orange;'>- لا توجد منتجات في الكافتيريا لإنشاء أوردر تجريبي</p>";
    }
    
    echo "<h2>5. اختبار APIs</h2>";
    
    // اختبار API جلب الأوردرات
    echo "<p>اختبار API جلب الأوردرات...</p>";
    $api_url = "http://localhost/playgood/client/api/get_orders.php?limit=5";
    echo "<p><a href='{$api_url}' target='_blank'>فتح API جلب الأوردرات</a></p>";
    
    // اختبار API جلب المنتجات
    echo "<p>اختبار API جلب المنتجات...</p>";
    $api_url = "http://localhost/playgood/client/api/get_products.php";
    echo "<p><a href='{$api_url}' target='_blank'>فتح API جلب المنتجات</a></p>";
    
    echo "<h2>6. إحصائيات النظام</h2>";
    
    // إحصائيات الأوردرات
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
            COALESCE(SUM(total_amount), 0) as total_revenue
        FROM orders 
        WHERE client_id = 1
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الإحصائية</th><th>القيمة</th></tr>";
    echo "<tr><td>إجمالي الأوردرات</td><td>{$stats['total_orders']}</td></tr>";
    echo "<tr><td>قيد الانتظار</td><td>{$stats['pending_orders']}</td></tr>";
    echo "<tr><td>مكتملة</td><td>{$stats['completed_orders']}</td></tr>";
    echo "<tr><td>ملغية</td><td>{$stats['cancelled_orders']}</td></tr>";
    echo "<tr><td>إجمالي الإيرادات</td><td>{$stats['total_revenue']} ر.س</td></tr>";
    echo "</table>";
    
    echo "<h2 style='color: green;'>✅ اكتمل اختبار النظام بنجاح!</h2>";
    
    echo "<h3>الروابط المفيدة:</h3>";
    echo "<ul>";
    echo "<li><a href='client/orders.php' style='color: #007bff;'>صفحة إدارة الأوردرات</a></li>";
    echo "<li><a href='client/cafeteria.php' style='color: #007bff;'>صفحة الكافتيريا</a></li>";
    echo "<li><a href='client/dashboard.php' style='color: #007bff;'>لوحة التحكم</a></li>";
    echo "<li><a href='client/employees.php' style='color: #007bff;'>إدارة الموظفين والصلاحيات</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo "<h2 style='color: red;'>❌ حدث خطأ أثناء الاختبار:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
