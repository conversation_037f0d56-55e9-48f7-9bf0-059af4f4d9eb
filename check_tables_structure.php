<?php
require_once 'config/database.php';

echo "<h1>فحص هيكل الجداول لإصلاح ربط الأسماء</h1>";

try {
    // 1. فحص هيكل جدول customers
    echo "<h2>1. هيكل جدول customers</h2>";
    $stmt = $pdo->query("DESCRIBE customers");
    $customers_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($customers_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. فحص هيكل جدول devices
    echo "<h2>2. هيكل جدول devices</h2>";
    $stmt = $pdo->query("DESCRIBE devices");
    $devices_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($devices_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. فحص هيكل جدول sessions
    echo "<h2>3. هيكل جدول sessions</h2>";
    $stmt = $pdo->query("DESCRIBE sessions");
    $sessions_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($sessions_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. فحص بيانات العملاء
    echo "<h2>4. عينة من بيانات العملاء</h2>";
    $stmt = $pdo->query("SELECT * FROM customers LIMIT 5");
    $customers_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($customers_data) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach (array_keys($customers_data[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        foreach ($customers_data as $customer) {
            echo "<tr>";
            foreach ($customer as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات عملاء</p>";
    }
    
    // 5. فحص بيانات الأجهزة
    echo "<h2>5. عينة من بيانات الأجهزة</h2>";
    $stmt = $pdo->query("SELECT * FROM devices LIMIT 5");
    $devices_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($devices_data) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach (array_keys($devices_data[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        foreach ($devices_data as $device) {
            echo "<tr>";
            foreach ($device as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات أجهزة</p>";
    }
    
    // 6. فحص الربط الحالي في الجلسات
    echo "<h2>6. فحص الربط الحالي في الجلسات</h2>";
    $stmt = $pdo->query("
        SELECT 
            s.session_id,
            s.customer_id,
            s.device_id,
            c.name as customer_name,
            d.device_name,
            d.device_type
        FROM sessions s
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        LEFT JOIN devices d ON s.device_id = d.device_id
        WHERE s.status = 'completed'
        LIMIT 5
    ");
    $sessions_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($sessions_data) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Session ID</th><th>Customer ID</th><th>Device ID</th><th>Customer Name</th><th>Device Name</th><th>Device Type</th>";
        echo "</tr>";
        foreach ($sessions_data as $session) {
            echo "<tr>";
            echo "<td>" . $session['session_id'] . "</td>";
            echo "<td>" . ($session['customer_id'] ?? 'NULL') . "</td>";
            echo "<td>" . $session['device_id'] . "</td>";
            echo "<td>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($session['device_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($session['device_type'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد جلسات مكتملة</p>";
    }
    
    // 7. اختبار الاستعلام الحالي للفواتير
    echo "<h2>7. اختبار الاستعلام الحالي للفواتير</h2>";
    $stmt = $pdo->query("
        SELECT 
            i.invoice_id,
            i.invoice_number,
            s.session_id,
            s.customer_id,
            s.device_id,
            c.name as customer_name,
            d.device_name,
            d.device_type
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        JOIN devices d ON s.device_id = d.device_id
        LIMIT 5
    ");
    $invoices_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices_data) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Invoice ID</th><th>Invoice Number</th><th>Session ID</th><th>Customer ID</th><th>Device ID</th><th>Customer Name</th><th>Device Name</th><th>Device Type</th>";
        echo "</tr>";
        foreach ($invoices_data as $invoice) {
            echo "<tr>";
            echo "<td>" . $invoice['invoice_id'] . "</td>";
            echo "<td>" . $invoice['invoice_number'] . "</td>";
            echo "<td>" . $invoice['session_id'] . "</td>";
            echo "<td>" . ($invoice['customer_id'] ?? 'NULL') . "</td>";
            echo "<td>" . $invoice['device_id'] . "</td>";
            echo "<td style='" . (empty($invoice['customer_name']) ? 'color: red;' : 'color: green;') . "'>" . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='" . (empty($invoice['device_name']) ? 'color: red;' : 'color: green;') . "'>" . htmlspecialchars($invoice['device_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($invoice['device_type'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد فواتير</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>الخطوة التالية:</strong> بناءً على النتائج أعلاه، سيتم إصلاح الروابط في استعلامات الفواتير</p>";
?>
