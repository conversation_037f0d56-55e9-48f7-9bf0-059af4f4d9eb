# دليل الأمان الشامل - PlayGood System

## 🛡️ نظرة عامة على الحماية المطبقة

تم تطبيق نظام حماية شامل ومتعدد الطبقات يتضمن أحدث معايير الأمان لحماية الموقع من جميع أنواع الهجمات الإلكترونية.

## 🔐 مكونات النظام الأمني

### 1. نظام المصادقة المحسن (Enhanced Authentication)

**الملفات:** `includes/security.php`

**المميزات:**
- حماية من هجمات Brute Force
- تتبع محاولات تسجيل الدخول الفاشلة
- حظر IP تلقائي بعد تجاوز الحد المسموح
- تشفير كلمات المرور بـ Argon2ID
- تجديد معرف الجلسة لمنع Session Fixation

**الإعدادات:**
- الحد الأقصى لمحاولات تسجيل الدخول: 5 محاولات
- مدة الحظر: 15 دقيقة
- انتهاء صلاحية الجلسة: ساعة واحدة

### 2. حماية CSRF (Cross-Site Request Forgery)

**الملفات:** `includes/csrf_middleware.php`

**المميزات:**
- إنشاء tokens فريدة لكل نموذج
- التحقق التلقائي من جميع طلبات POST
- انتهاء صلاحية الـ tokens بعد ساعة واحدة
- دعم AJAX requests
- حماية من إعادة استخدام الـ tokens

### 3. نظام كشف ومنع التسلل (IDS/IPS)

**الملفات:** `includes/intrusion_detection.php`

**المميزات:**
- كشف محاولات SQL Injection
- كشف هجمات XSS
- كشف Path Traversal
- كشف Command Injection
- كشف أدوات الفحص الأمني
- نظام تقييم التهديدات
- حظر تلقائي للـ IPs المشبوهة

### 4. حماية رفع الملفات

**الملفات:** `includes/file_upload_security.php`

**المميزات:**
- فحص امتدادات الملفات
- فحص MIME types
- فحص محتوى الملفات للكود الضار
- عزل الملفات المشبوهة
- حماية من ملفات PHP التنفيذية
- فحص أبعاد الصور
- تحديد حجم الملفات المسموح

### 5. حماية قاعدة البيانات

**الملفات:** `includes/database_security.php`

**المميزات:**
- تشفير البيانات الحساسة
- كشف محاولات SQL Injection
- استعلامات آمنة مع Prepared Statements
- تسجيل المحاولات المشبوهة
- حماية من استعلامات خطيرة

### 6. Content Security Policy (CSP)

**الملفات:** `includes/content_security.php`

**المميزات:**
- منع تنفيذ الـ scripts غير المصرح بها
- حماية من XSS attacks
- تنظيف المحتوى تلقائياً
- استخدام nonce للـ scripts الآمنة
- headers أمان شاملة

### 7. النسخ الاحتياطي الآمن

**الملفات:** `includes/secure_backup.php`

**المميزات:**
- تشفير النسخ الاحتياطية
- نسخ احتياطي شامل للبيانات والملفات
- استعادة آمنة للبيانات
- تنظيف النسخ القديمة تلقائياً
- فحص سلامة النسخ الاحتياطية

## 🔧 ملفات الحماية (.htaccess)

### الملف الرئيسي (/.htaccess)
- Security headers شاملة
- منع الوصول للملفات الحساسة
- حماية من هجمات شائعة
- تصفية User Agents المشبوهة

### مجلد الإعدادات (/config/.htaccess)
- منع الوصول الكامل
- تعطيل تنفيذ PHP
- حماية ملفات الإعدادات

### مجلد الـ Includes (/includes/.htaccess)
- منع الوصول المباشر
- حماية ملفات PHP المساعدة

### مجلد الرفع (/uploads/.htaccess)
- السماح بالصور والمستندات فقط
- منع تنفيذ الـ scripts
- فرض تحميل الملفات

### مجلد الحجر الصحي (/quarantine/.htaccess)
- منع الوصول الكامل
- تسجيل محاولات الوصول

## 📊 لوحة مراقبة الأمان

**الملف:** `admin/security_dashboard.php`

**المميزات:**
- مراقبة التهديدات في الوقت الفعلي
- عرض IPs المحظورة
- تتبع محاولات تسجيل الدخول الفاشلة
- إحصائيات أمنية شاملة

## 🧪 اختبار النظام الأمني

**الملف:** `security_test_suite.php`

**الاختبارات المتضمنة:**
- اختبار نظام CSRF
- اختبار كشف SQL Injection
- اختبار كشف XSS
- اختبار حماية رفع الملفات
- اختبار تشفير البيانات
- اختبار النسخ الاحتياطي
- فحص إعدادات الخادم

## 🚀 كيفية الاستخدام

### 1. تفعيل النظام الأمني

```php
// في بداية كل ملف PHP
require_once 'config/database.php';
require_once 'includes/security.php';
require_once 'includes/content_security.php';
require_once 'includes/csrf_middleware.php';
```

### 2. استخدام CSRF Protection

```php
// في النماذج
echo csrf_field('form_name');

// في معالجة النماذج
if (!$security->validateCsrfToken($_POST['csrf_token'], 'form_name')) {
    die('طلب غير صالح');
}
```

### 3. رفع الملفات الآمن

```php
$file_security = new FileUploadSecurity();
$result = $file_security->secureUpload($_FILES['file'], ['image']);

if ($result['valid']) {
    echo "تم رفع الملف بنجاح: " . $result['file_info']['uploaded_name'];
} else {
    echo "خطأ: " . implode(', ', $result['errors']);
}
```

### 4. إنشاء نسخة احتياطية

```php
$backup_system = new SecureBackupSystem($pdo);
$result = $backup_system->createFullBackup();

if ($result['success']) {
    echo "تم إنشاء النسخة الاحتياطية: " . $result['backup_id'];
}
```

## ⚙️ الإعدادات المطلوبة

### إعدادات PHP
```ini
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_only_cookies = 1
file_uploads = On
upload_max_filesize = 10M
post_max_size = 10M
```

### إعدادات MySQL
```sql
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';
```

## 🔍 مراقبة الأمان

### الجداول الأمنية المنشأة تلقائياً:
- `login_attempts` - محاولات تسجيل الدخول
- `suspicious_activities` - الأنشطة المشبوهة
- `blocked_ips` - IPs المحظورة
- `detected_threats` - التهديدات المكتشفة
- `sql_injection_attempts` - محاولات SQL Injection
- `backup_log` - سجل النسخ الاحتياطية

### ملفات السجلات:
- `logs/security.log` - سجل الأمان العام
- `logs/backup_access.log` - محاولات الوصول للنسخ الاحتياطية
- `quarantine/quarantine_log.json` - سجل الملفات المعزولة

## 🛠️ الصيانة الدورية

### يومياً:
- مراجعة لوحة مراقبة الأمان
- فحص التهديدات الجديدة
- مراجعة IPs المحظورة

### أسبوعياً:
- تشغيل اختبار الأمان الشامل
- مراجعة سجلات الأمان
- تنظيف الملفات المؤقتة

### شهرياً:
- إنشاء نسخة احتياطية كاملة
- مراجعة وتحديث قواعد الأمان
- فحص سلامة النسخ الاحتياطية

## 🚨 التعامل مع الحوادث الأمنية

### في حالة اكتشاف تهديد:
1. مراجعة لوحة مراقبة الأمان
2. تحليل نوع التهديد ومصدره
3. حظر IP المصدر إذا لزم الأمر
4. مراجعة سجلات النشاط
5. تحديث قواعد الحماية

### في حالة اختراق محتمل:
1. تغيير جميع كلمات المرور
2. مراجعة ملفات النظام
3. استعادة نسخة احتياطية نظيفة
4. تحديث جميع مكونات النظام
5. إجراء فحص أمني شامل

## 📞 الدعم الفني

للحصول على المساعدة في المسائل الأمنية:
- مراجعة هذا الدليل أولاً
- تشغيل اختبار الأمان الشامل
- فحص سجلات الأمان
- التواصل مع فريق التطوير

---

**تم إنشاء هذا النظام الأمني وفقاً لأفضل الممارسات العالمية في أمان التطبيقات الويب**
