<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل عملية تسجيل الخروج في قاعدة البيانات (اختياري)
if (isset($_SESSION['employee_id'])) {
    try {
        require_once '../config/database.php';

        $stmt = $pdo->prepare("UPDATE employees SET last_logout = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$_SESSION['employee_id']]);
    } catch (PDOException $e) {
        // تجاهل الأخطاء في تسجيل الخروج
    }
}

// حذف جميع متغيرات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل دخول الموظفين
header('Location: employee-login.php?logout=success');
exit;
?>