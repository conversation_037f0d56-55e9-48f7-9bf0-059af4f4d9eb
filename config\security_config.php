<?php
/**
 * إعدادات الأمان المتقدمة
 * PlayGood Gaming Center Management System
 */

// منع الوصول المباشر
if (!defined('SECURITY_CONFIG')) {
    define('SECURITY_CONFIG', true);
}

/**
 * إعدادات الجلسة الآمنة
 */
class SecurityConfig {
    
    // إعدادات الجلسة
    const SESSION_LIFETIME = 3600; // ساعة واحدة
    const SESSION_REGENERATE_INTERVAL = 300; // 5 دقائق
    const SESSION_CLEANUP_PROBABILITY = 1; // 1%
    
    // إعدادات الحماية من الهجمات
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOCKOUT_DURATION = 900; // 15 دقيقة
    const MAX_SUSPICIOUS_ACTIVITIES = 10;
    const SUSPICIOUS_ACTIVITY_WINDOW = 3600; // ساعة واحدة
    
    // إعدادات CSRF
    const CSRF_TOKEN_LIFETIME = 3600; // ساعة واحدة
    const CSRF_TOKEN_LENGTH = 32;
    
    // إعدادات كلمات المرور
    const MIN_PASSWORD_LENGTH = 8;
    const PASSWORD_REQUIRE_UPPERCASE = true;
    const PASSWORD_REQUIRE_LOWERCASE = true;
    const PASSWORD_REQUIRE_NUMBERS = true;
    const PASSWORD_REQUIRE_SYMBOLS = false;
    
    // إعدادات IP والشبكة
    const ALLOW_MULTIPLE_SESSIONS_SAME_IP = false;
    const TRACK_USER_AGENT_CHANGES = true;
    const STRICT_IP_VALIDATION = true;
    
    /**
     * تطبيق إعدادات الجلسة الآمنة
     */
    public static function applySecureSessionSettings() {
        // إعدادات أساسية للجلسة
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.gc_maxlifetime', self::SESSION_LIFETIME);
        ini_set('session.gc_probability', self::SESSION_CLEANUP_PROBABILITY);
        ini_set('session.gc_divisor', 100);
        
        // منع تثبيت الجلسة
        ini_set('session.use_trans_sid', 0);
        ini_set('session.use_strict_mode', 1);
        
        // تشفير معرف الجلسة
        ini_set('session.hash_function', 'sha256');
        ini_set('session.hash_bits_per_character', 6);
        
        // إعدادات إضافية للأمان
        ini_set('session.cookie_lifetime', 0); // انتهاء مع إغلاق المتصفح
        ini_set('session.entropy_length', 32);
        ini_set('session.entropy_file', '/dev/urandom');
    }
    
    /**
     * إعدادات PHP الآمنة
     */
    public static function applySecurePHPSettings() {
        // إخفاء معلومات PHP
        ini_set('expose_php', 0);
        
        // منع عرض الأخطاء في الإنتاج
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            ini_set('display_errors', 0);
            ini_set('display_startup_errors', 0);
            error_reporting(0);
        }
        
        // إعدادات رفع الملفات
        ini_set('file_uploads', 1);
        ini_set('upload_max_filesize', '10M');
        ini_set('max_file_uploads', 5);
        
        // منع تنفيذ الأكواد الخطيرة
        ini_set('allow_url_fopen', 0);
        ini_set('allow_url_include', 0);
        
        // إعدادات الذاكرة والوقت
        ini_set('memory_limit', '128M');
        ini_set('max_execution_time', 30);
        ini_set('max_input_time', 30);
        
        // إعدادات POST
        ini_set('post_max_size', '20M');
        ini_set('max_input_vars', 1000);
    }
    
    /**
     * Headers الأمان
     */
    public static function setSecurityHeaders() {
        // منع clickjacking
        header('X-Frame-Options: DENY');
        
        // منع MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // تفعيل XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "frame-ancestors 'none';";
        header("Content-Security-Policy: $csp");
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // HTTPS enforcement (إذا كان متاحاً)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // منع caching للصفحات الحساسة
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        
        // إضافة header مخصص للتعرف على النظام
        header('X-Powered-By: PlayGood-Security-System');
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < self::MIN_PASSWORD_LENGTH) {
            $errors[] = "كلمة المرور يجب أن تكون " . self::MIN_PASSWORD_LENGTH . " أحرف على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_LOWERCASE && !preg_match('/[a-z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_NUMBERS && !preg_match('/[0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_SYMBOLS && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل";
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * توليد رمز عشوائي آمن
     */
    public static function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * تنظيف المدخلات
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    public static function validatePhone($phone) {
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        return strlen($phone) >= 10 && strlen($phone) <= 15;
    }
    
    /**
     * الحصول على IP الحقيقي
     */
    public static function getRealIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تسجيل حدث أمني
     */
    public static function logSecurityEvent($event_type, $description, $severity = 'medium') {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'event_type' => $event_type,
            'description' => $description,
            'severity' => $severity,
            'session_id' => session_id(),
            'user_id' => $_SESSION['admin_id'] ?? $_SESSION['client_id'] ?? $_SESSION['employee_id'] ?? null
        ];
        
        $log_file = __DIR__ . '/../logs/security_' . date('Y-m-d') . '.log';
        $log_dir = dirname($log_file);
        
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        file_put_contents($log_file, json_encode($log_entry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * تطبيق جميع إعدادات الأمان
     */
    public static function initialize() {
        self::applySecureSessionSettings();
        self::applySecurePHPSettings();
        self::setSecurityHeaders();
    }
}

// تطبيق إعدادات الأمان تلقائياً
SecurityConfig::initialize();
