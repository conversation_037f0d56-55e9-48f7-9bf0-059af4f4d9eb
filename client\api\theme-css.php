<?php
/**
 * ملف CSS ديناميكي لتطبيق إعدادات المظهر المخصصة
 */

// منع أي إخراج قبل headers
ob_start();

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين نوع المحتوى كـ CSS
header('Content-Type: text/css; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    // تعيين معرف عميل افتراضي للاختبار
    $_SESSION['client_id'] = 1;
}

$client_id = $_SESSION['client_id'];

// إعدادات افتراضية
$theme_settings = [
    'primary_color' => '#dc3545',
    'secondary_color' => '#6c757d',
    'accent_color' => '#fd7e14',
    'header_style' => 'top',
    'sidebar_position' => 'right',
    'theme_mode' => 'light'
];

try {
    require_once __DIR__ . '/../../config/database.php';

    // جلب إعدادات المظهر للعميل
    $theme_stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
    $theme_stmt->execute([$client_id]);
    $user_settings = $theme_stmt->fetch(PDO::FETCH_ASSOC);

    // استخدام إعدادات المستخدم إذا وجدت
    if ($user_settings) {
        $theme_settings = $user_settings;
    }

} catch (Exception $e) {
    // استخدام الإعدادات الافتراضية في حالة الخطأ
    // لا نطبع أي شيء هنا لتجنب كسر CSS
}

// تحويل الألوان إلى RGB للاستخدام في التدرجات
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2))
    ];
}

function lightenColor($hex, $percent) {
    $rgb = hexToRgb($hex);
    $rgb['r'] = min(255, $rgb['r'] + ($percent * 255 / 100));
    $rgb['g'] = min(255, $rgb['g'] + ($percent * 255 / 100));
    $rgb['b'] = min(255, $rgb['b'] + ($percent * 255 / 100));
    return sprintf("#%02x%02x%02x", $rgb['r'], $rgb['g'], $rgb['b']);
}

function darkenColor($hex, $percent) {
    $rgb = hexToRgb($hex);
    $rgb['r'] = max(0, $rgb['r'] - ($percent * 255 / 100));
    $rgb['g'] = max(0, $rgb['g'] - ($percent * 255 / 100));
    $rgb['b'] = max(0, $rgb['b'] - ($percent * 255 / 100));
    return sprintf("#%02x%02x%02x", $rgb['r'], $rgb['g'], $rgb['b']);
}

$primary = $theme_settings['primary_color'];
$secondary = $theme_settings['secondary_color'];
$accent = $theme_settings['accent_color'];
$header_style = $theme_settings['header_style'];
$sidebar_position = $theme_settings['sidebar_position'];
$theme_mode = $theme_settings['theme_mode'];

// ألوان مشتقة
$primary_light = lightenColor($primary, 20);
$primary_dark = darkenColor($primary, 20);
$accent_light = lightenColor($accent, 20);
$accent_dark = darkenColor($accent, 20);

$primary_rgb = hexToRgb($primary);
$accent_rgb = hexToRgb($accent);

?>
/* CSS مخصص للمظهر - تم إنشاؤه تلقائياً */

:root {
    --custom-primary: <?php echo $primary; ?>;
    --custom-primary-light: <?php echo $primary_light; ?>;
    --custom-primary-dark: <?php echo $primary_dark; ?>;
    --custom-secondary: <?php echo $secondary; ?>;
    --custom-accent: <?php echo $accent; ?>;
    --custom-accent-light: <?php echo $accent_light; ?>;
    --custom-accent-dark: <?php echo $accent_dark; ?>;
    --custom-primary-rgb: <?php echo $primary_rgb['r'] . ', ' . $primary_rgb['g'] . ', ' . $primary_rgb['b']; ?>;
    --custom-accent-rgb: <?php echo $accent_rgb['r'] . ', ' . $accent_rgb['g'] . ', ' . $accent_rgb['b']; ?>;
}

/* تطبيق الألوان المخصصة */

/* الهيدر والنافبار */
.navbar-dark.bg-primary,
.navbar.bg-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%) !important;
}

.navbar-brand:hover {
    color: var(--custom-accent) !important;
}

.nav-link.active {
    background-color: rgba(var(--custom-accent-rgb), 0.2) !important;
    color: var(--custom-accent) !important;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%);
    border-color: var(--custom-primary);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--custom-primary-dark) 0%, var(--custom-primary) 100%);
    border-color: var(--custom-primary-dark);
}

.btn-success,
.btn-success-gradient {
    background: linear-gradient(135deg, var(--custom-accent) 0%, var(--custom-accent-dark) 100%);
    border-color: var(--custom-accent);
}

.btn-success:hover,
.btn-success-gradient:hover {
    background: linear-gradient(135deg, var(--custom-accent-dark) 0%, var(--custom-accent) 100%);
    border-color: var(--custom-accent-dark);
}

/* البطاقات والعناصر */
.card-header {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-light) 100%);
    color: white;
}

.stats-card {
    border-right-color: var(--custom-primary) !important;
}

.stats-card:hover {
    border-right-color: var(--custom-accent) !important;
}

/* الروابط */
a {
    color: var(--custom-primary);
}

a:hover {
    color: var(--custom-primary-dark);
}

/* النماذج */
.form-control:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

.form-select:focus {
    border-color: var(--custom-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--custom-primary-rgb), 0.25);
}

/* التبويبات */
.nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

.nav-pills .nav-link {
    color: var(--custom-primary);
}

.nav-pills .nav-link:hover {
    background-color: rgba(var(--custom-primary-rgb), 0.1);
}

/* الجداول */
.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(var(--custom-primary-rgb), 0.05);
}

.table th {
    border-bottom-color: var(--custom-primary);
}

/* الشارات */
.badge.bg-primary {
    background-color: var(--custom-primary) !important;
}

.badge.bg-success {
    background-color: var(--custom-accent) !important;
}

/* التقدم */
.progress-bar {
    background: linear-gradient(90deg, var(--custom-primary) 0%, var(--custom-accent) 100%);
}

/* الإشعارات */
.alert-primary {
    background-color: rgba(var(--custom-primary-rgb), 0.1);
    border-color: var(--custom-primary);
    color: var(--custom-primary-dark);
}

.alert-success {
    background-color: rgba(var(--custom-accent-rgb), 0.1);
    border-color: var(--custom-accent);
    color: var(--custom-accent-dark);
}

<?php if ($header_style === 'sidebar'): ?>
/* تطبيق نمط القائمة الجانبية */
body {
    padding-<?php echo $sidebar_position === 'right' ? 'right' : 'left'; ?>: 280px;
    transition: padding 0.3s ease;
}

.navbar {
    position: fixed;
    top: 0;
    <?php echo $sidebar_position; ?>: 0;
    width: 280px;
    height: 100vh;
    flex-direction: column;
    padding: 0;
    z-index: 1030;
    box-shadow: <?php echo $sidebar_position === 'right' ? '-2px' : '2px'; ?> 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.navbar .container-fluid {
    flex-direction: column;
    height: 100%;
    padding: 20px;
    align-items: stretch;
}

.navbar-brand {
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.5rem;
    padding: 15px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.navbar-nav {
    flex-direction: column;
    width: 100%;
    flex-grow: 1;
}

.navbar-nav .nav-item {
    width: 100%;
    margin-bottom: 5px;
}

.navbar-nav .nav-link {
    padding: 15px 20px;
    border-radius: 10px;
    margin: 0;
    text-align: <?php echo $sidebar_position === 'right' ? 'right' : 'left'; ?>;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    <?php if ($sidebar_position === 'left'): ?>
    flex-direction: row;
    <?php else: ?>
    flex-direction: row-reverse;
    <?php endif; ?>
}

.navbar-nav .nav-link i {
    <?php if ($sidebar_position === 'left'): ?>
    margin-right: 10px;
    margin-left: 0;
    <?php else: ?>
    margin-left: 10px;
    margin-right: 0;
    <?php endif; ?>
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateX(<?php echo $sidebar_position === 'right' ? '-5px' : '5px'; ?>);
}

.navbar-nav .nav-link.active {
    background-color: rgba(var(--custom-accent-rgb), 0.3) !important;
    color: var(--custom-accent) !important;
    box-shadow: 0 4px 8px rgba(var(--custom-accent-rgb), 0.2);
}

.navbar-toggler {
    display: none;
}

/* إخفاء spacer للهيدر المثبت */
div[style*="margin-top: 80px"] {
    display: none;
}

/* تعديل القائمة المنسدلة للمستخدم */
.navbar-nav .dropdown {
    margin-top: auto;
    width: 100%;
}

.navbar-nav .dropdown-menu {
    position: static !important;
    transform: none !important;
    width: 100%;
    margin-top: 10px;
    box-shadow: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    body {
        padding-<?php echo $sidebar_position === 'right' ? 'right' : 'left'; ?>: 0;
    }
    
    .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: auto;
        flex-direction: row;
    }
    
    .navbar .container-fluid {
        flex-direction: row;
        height: auto;
        padding: 10px 20px;
    }
    
    .navbar-brand {
        margin-bottom: 0;
        text-align: left;
        font-size: 1.2rem;
    }
    
    .navbar-nav {
        flex-direction: row;
        width: auto;
    }
    
    .navbar-nav .nav-item {
        width: auto;
        margin-bottom: 0;
    }
    
    .navbar-nav .nav-link {
        padding: 8px 12px;
        text-align: center;
    }
    
    .navbar-toggler {
        display: block;
    }
    
    div[style*="margin-top: 80px"] {
        display: block;
    }
}
<?php endif; ?>

<?php if ($theme_mode === 'dark'): ?>
/* النمط الداكن */
body {
    background-color: #1a1a1a;
    color: #ffffff;
}

.card {
    background-color: #2d2d2d;
    border-color: #404040;
}

.card-header {
    background: linear-gradient(135deg, var(--custom-primary) 0%, var(--custom-primary-dark) 100%);
    border-bottom-color: #404040;
}

.table {
    color: #ffffff;
}

.table th,
.table td {
    border-color: #404040;
}

.form-control,
.form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus,
.form-select:focus {
    background-color: #2d2d2d;
    color: #ffffff;
}

.dropdown-menu {
    background-color: #2d2d2d;
    border-color: #404040;
}

.dropdown-item {
    color: #ffffff;
}

.dropdown-item:hover {
    background-color: #404040;
    color: #ffffff;
}
<?php endif; ?>
