<?php
/**
 * مجموعة اختبارات شاملة لنظام الحماية
 * يختبر جميع مكونات الأمان المطبقة
 */

require_once 'config/database.php';
require_once 'includes/security.php';
require_once 'includes/database_security.php';
require_once 'includes/intrusion_detection.php';
require_once 'includes/file_upload_security.php';
require_once 'includes/content_security.php';
require_once 'includes/secure_backup.php';

// التحقق من صلاحيات الوصول (للإدمن فقط)
if (!isset($_SESSION['admin_id'])) {
    die('غير مصرح لك بالوصول لهذه الصفحة');
}

$test_results = [];

/**
 * اختبار نظام الحماية الأساسي
 */
function testBasicSecurity() {
    global $security, $test_results;
    
    $tests = [
        'csrf_token_generation' => function() use ($security) {
            $token = $security->generateCsrfToken('test');
            return !empty($token) && strlen($token) === 64;
        },
        
        'csrf_token_validation' => function() use ($security) {
            $token = $security->generateCsrfToken('test');
            return $security->validateCsrfToken($token, 'test');
        },
        
        'ip_detection' => function() use ($security) {
            $ip = $security->getClientIp();
            return filter_var($ip, FILTER_VALIDATE_IP) !== false;
        },
        
        'input_sanitization' => function() use ($security) {
            $malicious_input = '<script>alert("xss")</script>';
            $sanitized = $security->sanitizeInput($malicious_input);
            return strpos($sanitized, '<script>') === false;
        },
        
        'password_hashing' => function() use ($security) {
            $password = 'TestPassword123!';
            $hash = $security->hashPassword($password);
            return password_verify($password, $hash);
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    $test_results['basic_security'] = $results;
}

/**
 * اختبار نظام كشف التسلل
 */
function testIntrusionDetection() {
    global $ids, $test_results;
    
    $tests = [
        'sql_injection_detection' => function() use ($ids) {
            // محاكاة طلب مشبوه
            $_GET['test'] = "1' OR '1'='1";
            $_POST['test'] = "UNION SELECT * FROM users";
            
            $threats = $ids->scanCurrentRequest();
            return !empty($threats);
        },
        
        'xss_detection' => function() use ($ids) {
            $_GET['test'] = '<script>alert("xss")</script>';
            $_POST['test'] = 'javascript:alert(1)';
            
            $threats = $ids->scanCurrentRequest();
            return !empty($threats);
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    // تنظيف المتغيرات
    unset($_GET['test'], $_POST['test']);
    
    $test_results['intrusion_detection'] = $results;
}

/**
 * اختبار حماية رفع الملفات
 */
function testFileUploadSecurity() {
    global $test_results;
    
    $file_security = new FileUploadSecurity();
    
    $tests = [
        'dangerous_extension_detection' => function() use ($file_security) {
            $fake_file = [
                'name' => 'malicious.php',
                'tmp_name' => '',
                'size' => 1000,
                'error' => 0
            ];
            
            // محاكاة ملف مرفوع
            $temp_file = tempnam(sys_get_temp_dir(), 'test');
            file_put_contents($temp_file, '<?php echo "test"; ?>');
            $fake_file['tmp_name'] = $temp_file;
            
            $result = $file_security->validateUpload($fake_file);
            unlink($temp_file);
            
            return !$result['valid'];
        },
        
        'malicious_content_detection' => function() use ($file_security) {
            $fake_file = [
                'name' => 'test.txt',
                'tmp_name' => '',
                'size' => 1000,
                'error' => 0
            ];
            
            $temp_file = tempnam(sys_get_temp_dir(), 'test');
            file_put_contents($temp_file, '<?php system($_GET["cmd"]); ?>');
            $fake_file['tmp_name'] = $temp_file;
            
            $result = $file_security->validateUpload($fake_file);
            unlink($temp_file);
            
            return !$result['valid'];
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    $test_results['file_upload_security'] = $results;
}

/**
 * اختبار حماية قاعدة البيانات
 */
function testDatabaseSecurity() {
    global $db_security, $test_results;
    
    $tests = [
        'sql_injection_detection' => function() use ($db_security) {
            $malicious_query = "SELECT * FROM users WHERE id = '1' OR '1'='1'";
            $patterns = $db_security->detectSqlInjection($malicious_query);
            return !empty($patterns);
        },
        
        'data_encryption' => function() use ($db_security) {
            $original_data = 'sensitive information';
            $encrypted = $db_security->encrypt($original_data);
            $decrypted = $db_security->decrypt($encrypted);
            return $original_data === $decrypted;
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    $test_results['database_security'] = $results;
}

/**
 * اختبار النسخ الاحتياطي الآمن
 */
function testSecureBackup() {
    global $pdo, $test_results;
    
    $backup_system = new SecureBackupSystem($pdo);
    
    $tests = [
        'backup_creation' => function() use ($backup_system) {
            $result = $backup_system->createFullBackup(false); // بدون ملفات للاختبار
            return $result['success'];
        },
        
        'backup_list' => function() use ($backup_system) {
            $backups = $backup_system->getBackupList();
            return is_array($backups);
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    $test_results['secure_backup'] = $results;
}

/**
 * فحص إعدادات الخادم
 */
function testServerConfiguration() {
    global $test_results;
    
    $tests = [
        'php_version' => function() {
            return version_compare(PHP_VERSION, '7.4.0', '>=');
        },
        
        'openssl_extension' => function() {
            return extension_loaded('openssl');
        },
        
        'pdo_extension' => function() {
            return extension_loaded('pdo');
        },
        
        'session_security' => function() {
            return ini_get('session.cookie_httponly') && 
                   ini_get('session.use_only_cookies');
        },
        
        'file_uploads_disabled_in_sensitive_dirs' => function() {
            return file_exists('config/.htaccess') && 
                   file_exists('includes/.htaccess');
        }
    ];
    
    $results = [];
    foreach ($tests as $test_name => $test_function) {
        try {
            $results[$test_name] = $test_function() ? 'PASS' : 'FAIL';
        } catch (Exception $e) {
            $results[$test_name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    $test_results['server_configuration'] = $results;
}

// تشغيل جميع الاختبارات
testBasicSecurity();
testIntrusionDetection();
testFileUploadSecurity();
testDatabaseSecurity();
testSecureBackup();
testServerConfiguration();

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير اختبار الأمان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-error { color: #fd7e14; }
        .security-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    تقرير اختبار الأمان الشامل
                </h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    تم تشغيل جميع اختبارات الأمان في <?php echo date('Y-m-d H:i:s'); ?>
                </div>
            </div>
        </div>

        <?php foreach ($test_results as $category => $tests): ?>
            <div class="card security-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <?php 
                        $category_names = [
                            'basic_security' => 'الحماية الأساسية',
                            'intrusion_detection' => 'كشف التسلل',
                            'file_upload_security' => 'حماية رفع الملفات',
                            'database_security' => 'حماية قاعدة البيانات',
                            'secure_backup' => 'النسخ الاحتياطي الآمن',
                            'server_configuration' => 'إعدادات الخادم'
                        ];
                        echo $category_names[$category] ?? $category;
                        ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($tests as $test_name => $result): ?>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><?php echo str_replace('_', ' ', $test_name); ?></span>
                                    <span class="<?php 
                                        echo strpos($result, 'PASS') !== false ? 'test-pass' : 
                                            (strpos($result, 'FAIL') !== false ? 'test-fail' : 'test-error'); 
                                    ?>">
                                        <?php if (strpos($result, 'PASS') !== false): ?>
                                            <i class="fas fa-check-circle"></i> نجح
                                        <?php elseif (strpos($result, 'FAIL') !== false): ?>
                                            <i class="fas fa-times-circle"></i> فشل
                                        <?php else: ?>
                                            <i class="fas fa-exclamation-triangle"></i> خطأ
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <?php if (strpos($result, 'ERROR:') !== false): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars($result); ?></small>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>

        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">ملخص النتائج</h5>
            </div>
            <div class="card-body">
                <?php
                $total_tests = 0;
                $passed_tests = 0;
                $failed_tests = 0;
                $error_tests = 0;

                foreach ($test_results as $category => $tests) {
                    foreach ($tests as $result) {
                        $total_tests++;
                        if (strpos($result, 'PASS') !== false) {
                            $passed_tests++;
                        } elseif (strpos($result, 'FAIL') !== false) {
                            $failed_tests++;
                        } else {
                            $error_tests++;
                        }
                    }
                }

                $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
                ?>
                
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary"><?php echo $total_tests; ?></h4>
                        <p>إجمالي الاختبارات</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success"><?php echo $passed_tests; ?></h4>
                        <p>نجح</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger"><?php echo $failed_tests; ?></h4>
                        <p>فشل</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning"><?php echo $error_tests; ?></h4>
                        <p>خطأ</p>
                    </div>
                </div>
                
                <div class="progress mt-3">
                    <div class="progress-bar bg-success" style="width: <?php echo $success_rate; ?>%">
                        معدل النجاح: <?php echo $success_rate; ?>%
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
