<?php
/**
 * إصلاح مشكلة خلط أسماء العملاء مع المبالغ - PlayGood
 * يصلح المشكلة حيث تظهر أسماء العملاء في حقول المبالغ
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشكلة خلط أسماء العملاء مع المبالغ</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    $fixes_applied = 0;
    
    // 1. فحص وإصلاح جدول المصروفات
    echo "<h2>1. فحص وإصلاح جدول المصروفات</h2>";
    
    try {
        // البحث عن مصروفات بأسماء في حقل المبلغ
        $problematic_expenses = $pdo->prepare("
            SELECT id, amount, expense_type_id, expense_date, description
            FROM expenses 
            WHERE client_id = ? 
            AND (
                amount REGEXP '^[أ-ي\\s]+$' 
                OR amount IN (SELECT name FROM customers WHERE client_id = ?)
                OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$'
            )
        ");
        $problematic_expenses->execute([$client_id, $client_id]);
        $bad_expenses = $problematic_expenses->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_expenses)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_expenses) . " مصروف بمبالغ غير صحيحة</p>";
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>المبلغ الحالي (خطأ)</th>";
            echo "<th style='padding: 8px;'>الإجراء</th>";
            echo "<th style='padding: 8px;'>النتيجة</th>";
            echo "</tr>";
            
            foreach ($bad_expenses as $expense) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $expense['id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($expense['amount']) . "</td>";
                
                // تحديد نوع المشكلة والحل
                if (preg_match('/^[أ-ي\s]+$/', $expense['amount'])) {
                    // اسم عربي - محاولة تقدير مبلغ افتراضي
                    $default_amount = 50.00; // مبلغ افتراضي
                    
                    $update_stmt = $pdo->prepare("UPDATE expenses SET amount = ? WHERE id = ?");
                    $update_stmt->execute([$default_amount, $expense['id']]);
                    
                    echo "<td style='padding: 8px; color: blue;'>تم تعيين مبلغ افتراضي</td>";
                    echo "<td style='padding: 8px; color: green;'>$default_amount ج.م</td>";
                    $fixes_applied++;
                    
                } elseif (empty($expense['amount']) || $expense['amount'] === '0') {
                    // مبلغ فارغ أو صفر - حذف السجل
                    $delete_stmt = $pdo->prepare("DELETE FROM expenses WHERE id = ?");
                    $delete_stmt->execute([$expense['id']]);
                    
                    echo "<td style='padding: 8px; color: red;'>تم حذف السجل</td>";
                    echo "<td style='padding: 8px; color: red;'>محذوف</td>";
                    $fixes_applied++;
                    
                } else {
                    // محاولة استخراج رقم من النص
                    $cleaned_amount = preg_replace('/[^0-9.]/', '', $expense['amount']);
                    if (is_numeric($cleaned_amount) && $cleaned_amount > 0) {
                        $update_stmt = $pdo->prepare("UPDATE expenses SET amount = ? WHERE id = ?");
                        $update_stmt->execute([$cleaned_amount, $expense['id']]);
                        
                        echo "<td style='padding: 8px; color: blue;'>تم تنظيف المبلغ</td>";
                        echo "<td style='padding: 8px; color: green;'>$cleaned_amount ج.م</td>";
                        $fixes_applied++;
                    } else {
                        // لا يمكن إصلاحه - حذف
                        $delete_stmt = $pdo->prepare("DELETE FROM expenses WHERE id = ?");
                        $delete_stmt->execute([$expense['id']]);
                        
                        echo "<td style='padding: 8px; color: red;'>لا يمكن إصلاحه - تم الحذف</td>";
                        echo "<td style='padding: 8px; color: red;'>محذوف</td>";
                        $fixes_applied++;
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: green;'>✅ جميع مبالغ المصروفات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح المصروفات: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص وإصلاح جدول الإيرادات الإضافية
    echo "<h2>2. فحص وإصلاح جدول الإيرادات الإضافية</h2>";
    
    try {
        // البحث عن إيرادات بأسماء في حقل المبلغ
        $problematic_income = $pdo->prepare("
            SELECT id, amount, income_type_id, income_date, description
            FROM additional_income 
            WHERE client_id = ? 
            AND (
                amount REGEXP '^[أ-ي\\s]+$' 
                OR amount IN (SELECT name FROM customers WHERE client_id = ?)
                OR NOT amount REGEXP '^[0-9]+\.?[0-9]*$'
            )
        ");
        $problematic_income->execute([$client_id, $client_id]);
        $bad_income = $problematic_income->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_income)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_income) . " إيراد بمبالغ غير صحيحة</p>";
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>المبلغ الحالي (خطأ)</th>";
            echo "<th style='padding: 8px;'>الإجراء</th>";
            echo "<th style='padding: 8px;'>النتيجة</th>";
            echo "</tr>";
            
            foreach ($bad_income as $income) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $income['id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($income['amount']) . "</td>";
                
                // نفس منطق المصروفات
                if (preg_match('/^[أ-ي\s]+$/', $income['amount'])) {
                    $default_amount = 100.00; // مبلغ افتراضي للإيرادات
                    
                    $update_stmt = $pdo->prepare("UPDATE additional_income SET amount = ? WHERE id = ?");
                    $update_stmt->execute([$default_amount, $income['id']]);
                    
                    echo "<td style='padding: 8px; color: blue;'>تم تعيين مبلغ افتراضي</td>";
                    echo "<td style='padding: 8px; color: green;'>$default_amount ج.م</td>";
                    $fixes_applied++;
                    
                } elseif (empty($income['amount']) || $income['amount'] === '0') {
                    $delete_stmt = $pdo->prepare("DELETE FROM additional_income WHERE id = ?");
                    $delete_stmt->execute([$income['id']]);
                    
                    echo "<td style='padding: 8px; color: red;'>تم حذف السجل</td>";
                    echo "<td style='padding: 8px; color: red;'>محذوف</td>";
                    $fixes_applied++;
                    
                } else {
                    $cleaned_amount = preg_replace('/[^0-9.]/', '', $income['amount']);
                    if (is_numeric($cleaned_amount) && $cleaned_amount > 0) {
                        $update_stmt = $pdo->prepare("UPDATE additional_income SET amount = ? WHERE id = ?");
                        $update_stmt->execute([$cleaned_amount, $income['id']]);
                        
                        echo "<td style='padding: 8px; color: blue;'>تم تنظيف المبلغ</td>";
                        echo "<td style='padding: 8px; color: green;'>$cleaned_amount ج.م</td>";
                        $fixes_applied++;
                    } else {
                        $delete_stmt = $pdo->prepare("DELETE FROM additional_income WHERE id = ?");
                        $delete_stmt->execute([$income['id']]);
                        
                        echo "<td style='padding: 8px; color: red;'>لا يمكن إصلاحه - تم الحذف</td>";
                        echo "<td style='padding: 8px; color: red;'>محذوف</td>";
                        $fixes_applied++;
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: green;'>✅ جميع مبالغ الإيرادات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح الإيرادات: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص وإصلاح تكاليف الجلسات
    echo "<h2>3. فحص وإصلاح تكاليف الجلسات</h2>";
    
    try {
        $problematic_sessions = $pdo->prepare("
            SELECT s.session_id, s.total_cost, d.device_name, c.name as customer_name
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = ? 
            AND s.status = 'completed'
            AND (
                s.total_cost REGEXP '^[أ-ي\\s]+$' 
                OR s.total_cost IN (SELECT name FROM customers WHERE client_id = ?)
                OR (s.total_cost IS NOT NULL AND NOT s.total_cost REGEXP '^[0-9]+\.?[0-9]*$')
            )
        ");
        $problematic_sessions->execute([$client_id, $client_id]);
        $bad_sessions = $problematic_sessions->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($bad_sessions)) {
            echo "<p style='color: orange;'>⚠️ وُجد " . count($bad_sessions) . " جلسة بتكاليف غير صحيحة</p>";
            
            foreach ($bad_sessions as $session) {
                // إعادة حساب التكلفة بناءً على المدة والسعر
                $recalc_stmt = $pdo->prepare("
                    SELECT 
                        TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
                        d.single_rate, d.hourly_rate
                    FROM sessions s
                    JOIN devices d ON s.device_id = d.device_id
                    WHERE s.session_id = ?
                ");
                $recalc_stmt->execute([$session['session_id']]);
                $session_data = $recalc_stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($session_data) {
                    $duration_minutes = $session_data['duration_minutes'] ?? 0;
                    $hourly_rate = $session_data['single_rate'] ?? $session_data['hourly_rate'] ?? 0;
                    
                    if ($duration_minutes > 0 && $hourly_rate > 0) {
                        $new_cost = ceil($duration_minutes / 60) * $hourly_rate;
                        
                        $update_stmt = $pdo->prepare("UPDATE sessions SET total_cost = ? WHERE session_id = ?");
                        $update_stmt->execute([$new_cost, $session['session_id']]);
                        
                        echo "<p style='color: blue;'>🔧 تم إعادة حساب تكلفة الجلسة (ID: {$session['session_id']}) من '{$session['total_cost']}' إلى '$new_cost ج.م'</p>";
                        $fixes_applied++;
                    }
                }
            }
        } else {
            echo "<p style='color: green;'>✅ جميع تكاليف الجلسات صحيحة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في إصلاح تكاليف الجلسات: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

// ملخص الإصلاحات
echo "<h2>📊 ملخص الإصلاحات</h2>";
echo "<div style='background: " . ($fixes_applied > 0 ? "#d4edda" : "#fff3cd") . "; border: 1px solid " . ($fixes_applied > 0 ? "#c3e6cb" : "#ffeaa7") . "; color: " . ($fixes_applied > 0 ? "#155724" : "#856404") . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>" . ($fixes_applied > 0 ? "✅ تم تطبيق الإصلاحات" : "ℹ️ لا توجد إصلاحات مطلوبة") . "</h4>";
echo "<p><strong>عدد الإصلاحات المطبقة:</strong> $fixes_applied</p>";
if ($fixes_applied > 0) {
    echo "<p>تم إصلاح مشكلة خلط أسماء العملاء مع المبالغ بنجاح.</p>";
    echo "<p><strong>الآن:</strong> قم بزيارة صفحة الماليات للتحقق من النتائج.</p>";
} else {
    echo "<p>لم يتم العثور على مشاكل في خلط البيانات.</p>";
}
echo "</div>";

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔄 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>قم بزيارة صفحة الماليات: <a href='client/finances.php' target='_blank'>client/finances.php</a></li>";
echo "<li>تحقق من أن المبالغ تظهر كأرقام وليس أسماء</li>";
echo "<li>إذا استمرت المشكلة، تحقق من طريقة إدخال البيانات في النماذج</li>";
echo "<li>تأكد من أن حقول المبالغ في النماذج من نوع 'number'</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
