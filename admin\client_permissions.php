<?php
// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config/database.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول وصلاحية الوصول لهذه الصفحة
checkAdminSession();

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_POST['action'] === 'toggle_permission') {
            $client_id = intval($_POST['client_id']);
            $page_id = intval($_POST['page_id']);
            $is_enabled = $_POST['is_enabled'] === 'true' ? 1 : 0;
            
            // التحقق من وجود الصلاحية
            $stmt = $pdo->prepare("SELECT id FROM client_page_permissions WHERE client_id = ? AND page_id = ?");
            $stmt->execute([$client_id, $page_id]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // تحديث الصلاحية الموجودة
                $stmt = $pdo->prepare("UPDATE client_page_permissions SET is_enabled = ?, granted_by = ?, updated_at = NOW() WHERE client_id = ? AND page_id = ?");
                $stmt->execute([$is_enabled, $_SESSION['admin_id'], $client_id, $page_id]);
            } else {
                // إضافة صلاحية جديدة
                $stmt = $pdo->prepare("INSERT INTO client_page_permissions (client_id, page_id, is_enabled, granted_by) VALUES (?, ?, ?, ?)");
                $stmt->execute([$client_id, $page_id, $is_enabled, $_SESSION['admin_id']]);
            }
            
            echo json_encode(['success' => true, 'message' => 'تم تحديث الصلاحية بنجاح']);
            exit;
        }
        
        if ($_POST['action'] === 'reset_to_default') {
            $client_id = intval($_POST['client_id']);
            
            // حذف جميع الصلاحيات المخصصة للعميل
            $stmt = $pdo->prepare("DELETE FROM client_page_permissions WHERE client_id = ?");
            $stmt->execute([$client_id]);
            
            // إضافة الصلاحيات الافتراضية
            $stmt = $pdo->prepare("
                INSERT INTO client_page_permissions (client_id, page_id, is_enabled, granted_by)
                SELECT ?, page_id, TRUE, ?
                FROM client_pages
                WHERE is_default = TRUE AND is_active = TRUE
            ");
            $stmt->execute([$client_id, $_SESSION['admin_id']]);
            
            echo json_encode(['success' => true, 'message' => 'تم إعادة تعيين الصلاحيات إلى الافتراضية']);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
        exit;
    }
}

// جلب قائمة العملاء
$clients_stmt = $pdo->query("
    SELECT client_id, business_name, owner_name, is_active, subscription_plan
    FROM clients
    ORDER BY business_name
");
$clients = $clients_stmt->fetchAll();

// جلب العميل المحدد
$selected_client_id = $_GET['client_id'] ?? ($clients[0]['client_id'] ?? null);
$selected_client = null;

if ($selected_client_id) {
    $stmt = $pdo->prepare("SELECT * FROM clients WHERE client_id = ?");
    $stmt->execute([$selected_client_id]);
    $selected_client = $stmt->fetch();
}

// جلب صلاحيات العميل المحدد
$permissions = [];
if ($selected_client_id) {
    $stmt = $pdo->prepare("
        SELECT 
            cp.page_id,
            cp.page_name,
            cp.page_label,
            cp.page_icon,
            cp.category,
            cp.description,
            cp.is_default,
            COALESCE(cpp.is_enabled, cp.is_default) as has_permission,
            cpp.granted_at,
            cpp.updated_at
        FROM client_pages cp
        LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
        WHERE cp.is_active = TRUE
        ORDER BY cp.category, cp.page_label
    ");
    $stmt->execute([$selected_client_id]);
    $permissions = $stmt->fetchAll();
}

// تجميع الصلاحيات حسب الفئة
$grouped_permissions = [];
foreach ($permissions as $permission) {
    $grouped_permissions[$permission['category']][] = $permission;
}

// أسماء الفئات بالعربية مع الأيقونات - شامل لجميع صفحات الموقع
$category_names = [
    'main' => ['name' => 'الصفحات الأساسية', 'icon' => 'fas fa-home'],
    'devices' => ['name' => 'الأجهزة والغرف', 'icon' => 'fas fa-gamepad'],
    'sessions' => ['name' => 'الجلسات والعملاء', 'icon' => 'fas fa-play-circle'],
    'customers' => ['name' => 'إدارة العملاء', 'icon' => 'fas fa-users'],
    'cafeteria' => ['name' => 'الكافتيريا والمنتجات', 'icon' => 'fas fa-coffee'],
    'orders' => ['name' => 'إدارة الأوردرات والطلبات', 'icon' => 'fas fa-shopping-cart'],
    'employees' => ['name' => 'إدارة الموظفين', 'icon' => 'fas fa-user-tie'],
    'attendance' => ['name' => 'الحضور والانصراف', 'icon' => 'fas fa-user-check'],
    'shifts' => ['name' => 'إدارة الورديات', 'icon' => 'fas fa-calendar-alt'],
    'finances' => ['name' => 'الإدارة المالية والإيرادات والمصروفات', 'icon' => 'fas fa-money-bill-wave'],
    'reports' => ['name' => 'التقارير والإحصائيات', 'icon' => 'fas fa-chart-bar'],
    'invoices' => ['name' => 'الفواتير والمحاسبة', 'icon' => 'fas fa-file-invoice'],
    'inventory' => ['name' => 'إدارة المخزون', 'icon' => 'fas fa-boxes'],
    'reservations' => ['name' => 'الحجوزات والمواعيد', 'icon' => 'fas fa-calendar-check'],
    'settings' => ['name' => 'الإعدادات والتخصيص', 'icon' => 'fas fa-cog'],
    'backup' => ['name' => 'النسخ الاحتياطي', 'icon' => 'fas fa-database'],
    'permissions' => ['name' => 'الصلاحيات والأمان', 'icon' => 'fas fa-shield-alt'],
    'general' => ['name' => 'عام', 'icon' => 'fas fa-folder']
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صلاحيات العملاء - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .client-selector {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .permission-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s;
        }
        .permission-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        .permission-switch {
            transform: scale(1.2);
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .default-badge {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            font-size: 0.7em;
            padding: 0.3em 0.6em;
            border-radius: 15px;
        }
        .custom-badge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            font-size: 0.7em;
            padding: 0.3em 0.6em;
            border-radius: 15px;
        }
        .new-page-badge {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #333;
            font-size: 0.6em;
            padding: 0.2em 0.5em;
            border-radius: 10px;
            font-weight: bold;
        }
        .category-stats {
            font-size: 0.8em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                نظام إدارة محلات البلايستيشن
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                </span>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <!-- تضمين القائمة الجانبية العائمة -->
    <?php include 'includes/sidebar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- عنوان الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-user-shield me-2"></i>
                        إدارة صلاحيات العملاء
                    </h1>
                    <a href="clients.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة العملاء
                    </a>
                </div>

                <!-- معلومات النظام الشامل -->
                <div class="alert alert-success mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1">
                                <i class="fas fa-shield-alt me-2"></i>
                                نظام صلاحيات شامل لجميع صفحات الموقع
                            </h6>
                            <p class="mb-0 small">
                                يتضمن النظام الآن <strong>جميع صفحات الموقع</strong> مقسمة إلى فئات منظمة:
                                <strong>الأساسية، الأجهزة، الجلسات، العملاء، الكافتيريا، الأوردرات، الموظفين، الحضور، الورديات، الماليات، التقارير، الفواتير، المخزون، الحجوزات، الإعدادات، النسخ الاحتياطي، الصلاحيات</strong>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-success">شامل</span>
                            <small class="text-muted d-block mt-1">جميع صفحات الموقع</small>
                            <a href="../setup_complete_client_permissions.php" class="btn btn-sm btn-outline-primary mt-1">
                                <i class="fas fa-plus me-1"></i>إضافة الصفحات المفقودة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- اختيار العميل -->
                <div class="card client-selector mb-4">
                    <div class="card-body">
                        <h5 class="mb-3">
                            <i class="fas fa-user-check me-2"></i>
                            اختيار العميل
                        </h5>
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <select class="form-select" name="client_id" onchange="this.form.submit()">
                                    <option value="">اختر العميل...</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo $client['client_id']; ?>" 
                                                <?php echo $selected_client_id == $client['client_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($client['business_name']); ?> - 
                                            <?php echo htmlspecialchars($client['owner_name']); ?>
                                            <?php if (!$client['is_active']): ?>
                                                (غير نشط)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php if ($selected_client): ?>
                            <div class="col-md-4">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success btn-sm" onclick="enableAllOptionalPages()">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        تفعيل جميع الصفحات الاختيارية
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" onclick="enableCategoryPages('employees')">
                                        <i class="fas fa-user-tie me-1"></i>
                                        تفعيل صفحات الموظفين
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="resetToDefault()">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين للافتراضي
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>

                <?php if ($selected_client && !empty($permissions)): ?>
                <!-- معلومات العميل -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>اسم المحل:</strong><br>
                                <?php echo htmlspecialchars($selected_client['business_name']); ?>
                            </div>
                            <div class="col-md-3">
                                <strong>اسم المالك:</strong><br>
                                <?php echo htmlspecialchars($selected_client['owner_name']); ?>
                            </div>
                            <div class="col-md-3">
                                <strong>خطة الاشتراك:</strong><br>
                                <span class="badge bg-info"><?php echo $selected_client['subscription_plan']; ?></span>
                            </div>
                            <div class="col-md-3">
                                <strong>الحالة:</strong><br>
                                <span class="badge <?php echo $selected_client['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $selected_client['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صلاحيات الصفحات -->
                <?php foreach ($grouped_permissions as $category => $category_permissions): ?>
                <div class="category-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="<?php echo $category_names[$category]['icon'] ?? 'fas fa-folder'; ?> me-2"></i>
                            <?php echo $category_names[$category]['name'] ?? $category; ?>
                        </h5>
                        <div class="category-stats">
                            <?php
                            $enabled_count = array_sum(array_column($category_permissions, 'has_permission'));
                            $total_count = count($category_permissions);
                            $optional_pages_in_category = array_filter($category_permissions, function($page) {
                                return !$page['is_default'];
                            });
                            ?>
                            <span class="badge bg-primary me-1"><?php echo $enabled_count; ?>/<?php echo $total_count; ?> مفعل</span>
                            <?php if (count($optional_pages_in_category) > 0): ?>
                                <span class="badge bg-warning text-dark"><?php echo count($optional_pages_in_category); ?> اختياري</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <?php foreach ($category_permissions as $permission): ?>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="permission-card p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <i class="<?php echo $permission['page_icon']; ?> me-2"></i>
                                        <?php echo htmlspecialchars($permission['page_label']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($permission['description']); ?>
                                    </small>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input permission-switch" 
                                           type="checkbox" 
                                           id="permission_<?php echo $permission['page_id']; ?>"
                                           <?php echo $permission['has_permission'] ? 'checked' : ''; ?>
                                           onchange="togglePermission(<?php echo $selected_client_id; ?>, <?php echo $permission['page_id']; ?>, this.checked)">
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <?php echo $permission['page_name']; ?>.php
                                </small>
                                <div>
                                    <?php
                                    // عرض نوع الصفحة حسب الفئة
                                    $category_badges = [
                                        'employees' => ['text' => 'موظفين', 'class' => 'bg-info'],
                                        'attendance' => ['text' => 'حضور', 'class' => 'bg-primary'],
                                        'shifts' => ['text' => 'ورديات', 'class' => 'bg-secondary'],
                                        'finances' => ['text' => 'مالي', 'class' => 'bg-success'],
                                        'orders' => ['text' => 'أوردر', 'class' => 'bg-warning text-dark'],
                                        'inventory' => ['text' => 'مخزون', 'class' => 'bg-dark'],
                                        'reservations' => ['text' => 'حجز', 'class' => 'bg-purple'],
                                        'backup' => ['text' => 'نسخ', 'class' => 'bg-danger']
                                    ];

                                    if (isset($category_badges[$permission['category']])):
                                        $badge = $category_badges[$permission['category']];
                                    ?>
                                        <span class="badge <?php echo $badge['class']; ?> me-1" style="font-size: 0.6em;">
                                            <?php echo $badge['text']; ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($permission['is_default']): ?>
                                        <span class="default-badge">افتراضي</span>
                                    <?php else: ?>
                                        <span class="custom-badge">اختياري</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>

                <?php elseif ($selected_client): ?>
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h5>لا توجد صفحات متاحة</h5>
                    <p>لم يتم العثور على صفحات متاحة لهذا العميل. تأكد من تشغيل ملف إعداد النظام أولاً.</p>
                </div>
                <?php else: ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <h5>اختر عميلاً لإدارة صلاحياته</h5>
                    <p>يرجى اختيار عميل من القائمة أعلاه لعرض وإدارة صلاحيات الصفحات الخاصة به.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePermission(clientId, pageId, isEnabled) {
            fetch('client_permissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=toggle_permission&client_id=${clientId}&page_id=${pageId}&is_enabled=${isEnabled}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message, 'error');
                    // إعادة تعيين الحالة السابقة للمفتاح
                    document.getElementById(`permission_${pageId}`).checked = !isEnabled;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
                document.getElementById(`permission_${pageId}`).checked = !isEnabled;
            });
        }

        function resetToDefault() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الصلاحيات إلى الإعدادات الافتراضية؟')) {
                const clientId = <?php echo $selected_client_id ?? 0; ?>;

                fetch('client_permissions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=reset_to_default&client_id=${clientId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showNotification(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                });
            }
        }

        function enableAllOptionalPages() {
            if (confirm('هل تريد تفعيل جميع الصفحات الاختيارية (غير الافتراضية) لهذا العميل؟')) {
                const clientId = <?php echo $selected_client_id ?? 0; ?>;
                const allSwitches = document.querySelectorAll('.permission-switch');
                let optionalPages = [];

                // جمع جميع الصفحات الاختيارية غير المفعلة
                allSwitches.forEach(switchElement => {
                    if (!switchElement.checked) {
                        // التحقق من أن الصفحة اختيارية (ليست افتراضية)
                        const card = switchElement.closest('.permission-card');
                        const isOptional = card.querySelector('.custom-badge');
                        if (isOptional) {
                            optionalPages.push(switchElement);
                        }
                    }
                });

                if (optionalPages.length === 0) {
                    showNotification('جميع الصفحات الاختيارية مفعلة بالفعل', 'info');
                    return;
                }

                let completedRequests = 0;
                let successCount = 0;

                showNotification(`جاري تفعيل ${optionalPages.length} صفحة اختيارية...`, 'info');

                optionalPages.forEach(pageElement => {
                    const pageId = pageElement.id.replace('permission_', '');

                    fetch('client_permissions.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=toggle_permission&client_id=${clientId}&page_id=${pageId}&is_enabled=true`
                    })
                    .then(response => response.json())
                    .then(data => {
                        completedRequests++;
                        if (data.success) {
                            successCount++;
                            pageElement.checked = true;
                        }

                        if (completedRequests === optionalPages.length) {
                            showNotification(`تم تفعيل ${successCount} من ${optionalPages.length} صفحة اختيارية`, 'success');
                            setTimeout(() => location.reload(), 2000);
                        }
                    })
                    .catch(error => {
                        completedRequests++;
                        console.error('Error:', error);
                    });
                });
            }
        }

        function enableCategoryPages(category) {
            const categoryNames = {
                'employees': 'الموظفين',
                'attendance': 'الحضور والانصراف',
                'shifts': 'الورديات',
                'finances': 'الماليات',
                'orders': 'الأوردرات',
                'inventory': 'المخزون',
                'reservations': 'الحجوزات',
                'backup': 'النسخ الاحتياطي'
            };

            const categoryName = categoryNames[category] || category;

            if (confirm(`هل تريد تفعيل جميع صفحات ${categoryName} لهذا العميل؟`)) {
                const clientId = <?php echo $selected_client_id ?? 0; ?>;
                const categorySection = document.querySelector(`[data-category="${category}"]`);

                if (!categorySection) {
                    // البحث في جميع الأقسام
                    const allSwitches = document.querySelectorAll('.permission-switch');
                    let categoryPages = [];

                    allSwitches.forEach(switchElement => {
                        const card = switchElement.closest('.permission-card');
                        const pageInfo = card.querySelector('small').textContent;

                        // فحص إذا كانت الصفحة تنتمي للفئة المطلوبة
                        if (pageInfo.includes(category) ||
                            (category === 'employees' && (pageInfo.includes('employee') || pageInfo.includes('موظف'))) ||
                            (category === 'attendance' && pageInfo.includes('attendance')) ||
                            (category === 'shifts' && pageInfo.includes('shift'))) {
                            if (!switchElement.checked) {
                                categoryPages.push(switchElement);
                            }
                        }
                    });

                    if (categoryPages.length === 0) {
                        showNotification(`جميع صفحات ${categoryName} مفعلة بالفعل`, 'info');
                        return;
                    }

                    let completedRequests = 0;
                    let successCount = 0;

                    showNotification(`جاري تفعيل ${categoryPages.length} صفحة من فئة ${categoryName}...`, 'info');

                    categoryPages.forEach(pageElement => {
                        const pageId = pageElement.id.replace('permission_', '');

                        fetch('client_permissions.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `action=toggle_permission&client_id=${clientId}&page_id=${pageId}&is_enabled=true`
                        })
                        .then(response => response.json())
                        .then(data => {
                            completedRequests++;
                            if (data.success) {
                                successCount++;
                                pageElement.checked = true;
                            }

                            if (completedRequests === categoryPages.length) {
                                showNotification(`تم تفعيل ${successCount} من ${categoryPages.length} صفحة من فئة ${categoryName}`, 'success');
                                setTimeout(() => location.reload(), 2000);
                            }
                        })
                        .catch(error => {
                            completedRequests++;
                            console.error('Error:', error);
                        });
                    });
                }
            }
        }

        function showNotification(message, type) {
            let alertClass = 'alert-danger';
            if (type === 'success') alertClass = 'alert-success';
            else if (type === 'info') alertClass = 'alert-info';
            else if (type === 'warning') alertClass = 'alert-warning';

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, type === 'info' ? 3000 : 5000);
        }
    </script>
</body>
</html>
