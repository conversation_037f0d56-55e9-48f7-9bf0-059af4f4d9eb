<?php
/**
 * اختبار صفحة تعديل الموظف مع الصلاحيات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار صفحة تعديل الموظف</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card shadow'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-user-edit me-2'></i>اختبار صفحة تعديل الموظف</h3>
        </div>
        <div class='card-body'>";

try {
    // فحص وجود الجداول
    echo "<h5><i class='fas fa-database me-2'></i>فحص قاعدة البيانات</h5>";
    
    $required_tables = ['employees', 'permissions', 'pages', 'employee_permissions', 'employee_pages'];
    $all_tables_exist = true;
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>جدول $table موجود</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>جدول $table غير موجود</p>";
            $all_tables_exist = false;
        }
    }
    
    if (!$all_tables_exist) {
        echo "<div class='alert alert-danger'>
                <p><strong>خطأ:</strong> بعض الجداول المطلوبة غير موجودة.</p>
                <p><a href='setup_permissions_quick.php' class='btn btn-warning'>إعداد النظام</a></p>
              </div>";
    } else {
        // فحص الموظفين
        echo "<h5><i class='fas fa-users me-2'></i>فحص الموظفين</h5>";
        
        $stmt = $pdo->query("SELECT id, name, role, custom_permissions FROM employees LIMIT 5");
        $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($employees) > 0) {
            echo "<div class='table-responsive'>
                    <table class='table table-striped'>
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>الاسم</th>
                                <th>الدور</th>
                                <th>صلاحيات مخصصة</th>
                                <th>اختبار</th>
                            </tr>
                        </thead>
                        <tbody>";
            
            foreach ($employees as $emp) {
                $custom_status = $emp['custom_permissions'] ? 'نعم' : 'لا';
                $custom_class = $emp['custom_permissions'] ? 'success' : 'secondary';
                
                echo "<tr>
                        <td>{$emp['id']}</td>
                        <td>{$emp['name']}</td>
                        <td>{$emp['role']}</td>
                        <td><span class='badge bg-$custom_class'>$custom_status</span></td>
                        <td>
                            <a href='edit_employee.php?id={$emp['id']}' class='btn btn-sm btn-primary' target='_blank'>
                                <i class='fas fa-edit me-1'></i>تعديل
                            </a>
                        </td>
                      </tr>";
            }
            
            echo "        </tbody>
                    </table>
                  </div>";
        } else {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد موظفين في النظام</p>";
        }
        
        // فحص الصلاحيات
        echo "<h5><i class='fas fa-shield-alt me-2'></i>فحص الصلاحيات</h5>";
        
        $stmt = $pdo->query("SELECT category, COUNT(*) as count FROM permissions WHERE is_active = 1 GROUP BY category");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($categories) > 0) {
            echo "<div class='row'>";
            foreach ($categories as $cat) {
                echo "<div class='col-md-4 mb-2'>
                        <div class='card border-primary'>
                            <div class='card-body text-center'>
                                <h6 class='card-title'>{$cat['category']}</h6>
                                <p class='card-text'>{$cat['count']} صلاحية</p>
                            </div>
                        </div>
                      </div>";
            }
            echo "</div>";
        } else {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد صلاحيات في النظام</p>";
        }
        
        // فحص الصفحات
        echo "<h5><i class='fas fa-file me-2'></i>فحص الصفحات</h5>";
        
        $stmt = $pdo->query("SELECT category, COUNT(*) as count FROM pages WHERE is_active = 1 GROUP BY category");
        $page_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($page_categories) > 0) {
            echo "<div class='row'>";
            foreach ($page_categories as $cat) {
                echo "<div class='col-md-4 mb-2'>
                        <div class='card border-info'>
                            <div class='card-body text-center'>
                                <h6 class='card-title'>{$cat['category']}</h6>
                                <p class='card-text'>{$cat['count']} صفحة</p>
                            </div>
                        </div>
                      </div>";
            }
            echo "</div>";
        } else {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد صفحات في النظام</p>";
        }
        
        // اختبار الدوال
        echo "<h5><i class='fas fa-code me-2'></i>اختبار الدوال</h5>";
        
        require_once 'includes/employee-auth.php';
        
        $functions_to_test = [
            'employeeHasCustomPermission',
            'employeeCanAccessPage', 
            'getEmployeeCustomPermissions'
        ];
        
        foreach ($functions_to_test as $func) {
            if (function_exists($func)) {
                echo "<p class='success'><i class='fas fa-check me-2'></i>دالة $func موجودة</p>";
            } else {
                echo "<p class='error'><i class='fas fa-times me-2'></i>دالة $func غير موجودة</p>";
            }
        }
        
        echo "<div class='alert alert-success mt-4'>
                <h6><i class='fas fa-check-circle me-2'></i>النظام جاهز للاستخدام!</h6>
                <p>يمكنك الآن استخدام صفحة تعديل الموظف مع نظام الصلاحيات المتقدم.</p>
              </div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h6><i class='fas fa-exclamation-circle me-2'></i>خطأ في قاعدة البيانات</h6>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='employees.php' class='btn btn-primary me-2'>
                <i class='fas fa-users me-2'></i>إدارة الموظفين
            </a>
            <a href='setup_permissions_quick.php' class='btn btn-warning me-2'>
                <i class='fas fa-cogs me-2'></i>إعداد النظام
            </a>
            <a href='check_permissions_system.php' class='btn btn-info'>
                <i class='fas fa-search me-2'></i>فحص مفصل
            </a>
        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
