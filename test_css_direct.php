<?php
/**
 * اختبار مباشر لملف CSS
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين معرف عميل افتراضي
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>اختبار ملف CSS المخصص</h1>";

// اختبار ملف CSS مباشرة
echo "<h2>1. اختبار تحميل ملف CSS</h2>";

$css_file_path = 'client/api/theme-css.php';

if (file_exists($css_file_path)) {
    echo "<p style='color: green;'>✅ ملف CSS موجود</p>";
    
    // تشغيل الملف وجلب المحتوى
    ob_start();
    include $css_file_path;
    $css_content = ob_get_clean();
    
    if (!empty($css_content)) {
        echo "<p style='color: green;'>✅ ملف CSS ينتج محتوى (" . strlen($css_content) . " حرف)</p>";
        
        // عرض جزء من CSS
        echo "<h3>محتوى CSS:</h3>";
        echo "<textarea style='width: 100%; height: 300px; font-family: monospace; direction: ltr;'>";
        echo htmlspecialchars($css_content);
        echo "</textarea>";
        
        // إنشاء ملف CSS ثابت للاختبار
        $static_css_file = 'test_theme.css';
        file_put_contents($static_css_file, $css_content);
        echo "<p style='color: blue;'>ℹ️ تم إنشاء ملف CSS ثابت: <a href='$static_css_file' target='_blank'>$static_css_file</a></p>";
        
    } else {
        echo "<p style='color: red;'>❌ ملف CSS لا ينتج أي محتوى</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف CSS غير موجود</p>";
}

// اختبار قاعدة البيانات
echo "<h2>2. اختبار قاعدة البيانات</h2>";
try {
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجدول
    $table_check = $pdo->query("SHOW TABLES LIKE 'client_theme_settings'");
    if ($table_check->rowCount() > 0) {
        echo "<p style='color: green;'>✅ جدول client_theme_settings موجود</p>";
        
        // جلب الإعدادات
        $stmt = $pdo->prepare("SELECT * FROM client_theme_settings WHERE client_id = ?");
        $stmt->execute([$_SESSION['client_id']]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            echo "<p style='color: green;'>✅ إعدادات موجودة:</p>";
            echo "<ul>";
            foreach ($settings as $key => $value) {
                if (strpos($key, 'color') !== false) {
                    echo "<li>$key: <span style='color: $value; font-weight: bold;'>$value</span></li>";
                } else {
                    echo "<li>$key: $value</li>";
                }
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد إعدادات للعميل " . $_SESSION['client_id'] . "</p>";
            
            // إنشاء إعدادات افتراضية
            echo "<p>إنشاء إعدادات افتراضية...</p>";
            $stmt = $pdo->prepare("
                INSERT INTO client_theme_settings 
                (client_id, primary_color, secondary_color, accent_color, header_style, sidebar_position, theme_mode)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $result = $stmt->execute([
                $_SESSION['client_id'], 
                '#dc3545', // أحمر
                '#6c757d', // رمادي
                '#fd7e14', // برتقالي
                'top', 
                'right', 
                'light'
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ تم إنشاء إعدادات افتراضية</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء الإعدادات</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ جدول client_theme_settings غير موجود</p>";
        
        // إنشاء الجدول
        echo "<p>إنشاء الجدول...</p>";
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS client_theme_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                primary_color VARCHAR(7) DEFAULT '#0d6efd',
                secondary_color VARCHAR(7) DEFAULT '#6c757d',
                accent_color VARCHAR(7) DEFAULT '#20c997',
                header_style ENUM('top', 'sidebar') DEFAULT 'top',
                sidebar_position ENUM('right', 'left') DEFAULT 'right',
                theme_mode ENUM('light', 'dark', 'auto') DEFAULT 'light',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_client (client_id)
            )
        ");
        echo "<p style='color: green;'>✅ تم إنشاء الجدول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار صفحة HTML مع CSS
echo "<h2>3. اختبار صفحة HTML مع CSS</h2>";
$test_html = '
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المظهر المخصص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="test_theme.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-gamepad me-2"></i>مركز الألعاب
            </a>
        </div>
    </nav>
    
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4 class="text-primary">اختبار اللون الأساسي</h4>
                        <button class="btn btn-primary">زر أساسي</button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4 class="text-success">اختبار اللون المميز</h4>
                        <button class="btn btn-success">زر نجاح</button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>اختبار اللون الثانوي</h4>
                        <button class="btn btn-secondary">زر ثانوي</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
';

$test_html_file = 'test_theme_page.html';
file_put_contents($test_html_file, $test_html);
echo "<p style='color: blue;'>ℹ️ تم إنشاء صفحة اختبار: <a href='$test_html_file' target='_blank'>$test_html_file</a></p>";

echo "<h2>4. الخطوات التالية</h2>";
echo "<ol>";
echo "<li><a href='$test_html_file' target='_blank'>افتح صفحة الاختبار</a> لترى الألوان المخصصة</li>";
echo "<li><a href='client/test-theme.php' target='_blank'>جرب صفحة الاختبار الكاملة</a></li>";
echo "<li><a href='client/settings.php' target='_blank'>انتقل لصفحة الإعدادات</a> لتخصيص الألوان</li>";
echo "<li>امسح ذاكرة التخزين المؤقت (Ctrl+F5) إذا لم تظهر التغييرات</li>";
echo "</ol>";
?>
