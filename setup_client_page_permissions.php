<?php
/**
 * ملف إعداد نظام صلاحيات الصفحات للعملاء
 * يجب تشغيل هذا الملف مرة واحدة لإعداد النظام
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد نظام صلاحيات الصفحات للعملاء</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 800px; margin: 2rem auto; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h1 class='h3 mb-0'><i class='fas fa-cog me-2'></i>إعداد نظام صلاحيات الصفحات للعملاء</h1>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<h2>1. التحقق من الاتصال بقاعدة البيانات</h2>";
    
    if (!$pdo) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    echo "<h2>2. قراءة ملف SQL</h2>";
    
    $sql_file = 'create_client_page_permissions_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if (!$sql_content) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    echo "<p class='success'>✅ تم قراءة ملف SQL بنجاح</p>";
    
    echo "<h2>3. تنفيذ الاستعلامات</h2>";
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql_content);
    $executed_count = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // تجاهل التعليقات والأسطر الفارغة
        if (empty($statement) || 
            strpos($statement, '--') === 0 || 
            strpos($statement, '/*') === 0 ||
            strtoupper(substr($statement, 0, 9)) === 'DELIMITER') {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed_count++;
            
            // عرض نوع الاستعلام المنفذ
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                $table_name = $matches[1] ?? 'غير معروف';
                echo "<p class='success'>✅ تم إنشاء الجدول: $table_name</p>";
            } elseif (stripos($statement, 'INSERT') !== false) {
                echo "<p class='success'>✅ تم إدراج البيانات</p>";
            } elseif (stripos($statement, 'CREATE OR REPLACE VIEW') !== false) {
                preg_match('/CREATE OR REPLACE VIEW\s+(\w+)/i', $statement, $matches);
                $view_name = $matches[1] ?? 'غير معروف';
                echo "<p class='success'>✅ تم إنشاء العرض: $view_name</p>";
            } elseif (stripos($statement, 'CREATE PROCEDURE') !== false) {
                preg_match('/CREATE PROCEDURE.*?(\w+)/i', $statement, $matches);
                $proc_name = $matches[1] ?? 'غير معروف';
                echo "<p class='success'>✅ تم إنشاء الإجراء المخزن: $proc_name</p>";
            } elseif (stripos($statement, 'CREATE TRIGGER') !== false) {
                preg_match('/CREATE TRIGGER.*?(\w+)/i', $statement, $matches);
                $trigger_name = $matches[1] ?? 'غير معروف';
                echo "<p class='success'>✅ تم إنشاء المشغل: $trigger_name</p>";
            }
            
        } catch (PDOException $e) {
            // تجاهل أخطاء "already exists"
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p class='warning'>⚠️ العنصر موجود مسبقاً (تم تجاهله)</p>";
            } else {
                echo "<p class='error'>❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>" . 
                     htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
            }
        }
    }
    
    echo "<p class='success'><strong>تم تنفيذ $executed_count استعلام بنجاح</strong></p>";
    
    echo "<h2>4. التحقق من الجداول المنشأة</h2>";
    
    $required_tables = ['client_pages', 'client_page_permissions'];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ الجدول $table موجود</p>";
            
            // عرض عدد السجلات
            $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch()['count'];
            echo "<p style='margin-left: 20px;'>📊 عدد السجلات: $count</p>";
        } else {
            echo "<p class='error'>❌ الجدول $table غير موجود</p>";
        }
    }
    
    echo "<h2>5. التحقق من البيانات</h2>";
    
    // عرض الصفحات المتاحة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM client_pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    echo "<p class='success'>📄 عدد الصفحات المتاحة: $pages_count</p>";
    
    // عرض الصفحات الافتراضية
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM client_pages WHERE is_default = 1 AND is_active = 1");
    $default_pages_count = $stmt->fetch()['count'];
    echo "<p class='success'>🏠 عدد الصفحات الافتراضية: $default_pages_count</p>";
    
    // عرض العملاء الذين لديهم صلاحيات
    $stmt = $pdo->query("SELECT COUNT(DISTINCT client_id) as count FROM client_page_permissions");
    $clients_with_permissions = $stmt->fetch()['count'];
    echo "<p class='success'>👥 عدد العملاء الذين لديهم صلاحيات: $clients_with_permissions</p>";
    
    echo "<h2>6. اختبار النظام</h2>";
    
    // اختبار دالة التحقق من الصلاحيات
    $stmt = $pdo->query("SELECT client_id FROM clients LIMIT 1");
    $test_client = $stmt->fetch();
    
    if ($test_client) {
        $client_id = $test_client['client_id'];
        
        // اختبار الصفحات المسموحة
        $stmt = $pdo->prepare("
            SELECT cp.page_name, COALESCE(cpp.is_enabled, cp.is_default) as has_permission
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.is_active = TRUE
            LIMIT 5
        ");
        $stmt->execute([$client_id]);
        $test_permissions = $stmt->fetchAll();
        
        echo "<p class='success'>🧪 اختبار الصلاحيات للعميل رقم $client_id:</p>";
        echo "<ul>";
        foreach ($test_permissions as $perm) {
            $status = $perm['has_permission'] ? '✅ مسموح' : '❌ غير مسموح';
            echo "<li>{$perm['page_name']}: $status</li>";
        }
        echo "</ul>";
    }
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h4>🎉 تم إعداد النظام بنجاح!</h4>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li>الوصول إلى صفحة إدارة صلاحيات العملاء من لوحة تحكم الأدمن</li>";
    echo "<li>تخصيص الصفحات المتاحة لكل عميل</li>";
    echo "<li>إعادة تعيين الصلاحيات إلى الإعدادات الافتراضية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='admin/client_permissions.php' class='btn btn-primary me-2'>إدارة صلاحيات العملاء</a>";
    echo "<a href='admin/dashboard.php' class='btn btn-secondary'>لوحة تحكم الأدمن</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ أثناء الإعداد</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الحلول المقترحة:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من صحة إعدادات قاعدة البيانات في ملف config/database.php</li>";
    echo "<li>تأكد من وجود ملف create_client_page_permissions_system.sql</li>";
    echo "<li>تأكد من أن المستخدم لديه صلاحيات إنشاء الجداول</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
