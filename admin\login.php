<?php
require_once '../config/database.php';

// تضمين نظام الحماية المتقدم
require_once '../includes/secure_session_middleware.php';

// تهيئة نظام الحماية الأساسي
if (!class_exists('SecurityManager')) {
    require_once '../includes/security.php';
}

// إنشاء مثيل من مدير الحماية إذا لم يكن موجوداً
if (!isset($security)) {
    $security = new SecurityManager($pdo);
}

// التحقق من تسجيل الدخول المسبق
if (isset($_SESSION['admin_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $security->sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';

    // التحقق من CSRF Token
    if (!$security->validateCsrfToken($csrf_token)) {
        $error = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
    } elseif (!empty($username) && !empty($password)) {
        // فحص محاولات تسجيل الدخول
        if (!$security->checkLoginAttempts($username, 'admin')) {
            $error = 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً.';
        } else {
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE (username = ? OR email = ?) AND is_active = 1");
            $stmt->execute([$username, $username]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password_hash'])) {
                // تسجيل محاولة ناجحة
                $security->logLoginAttempt($username, 'admin', true);

                $_SESSION['admin_id'] = $admin['admin_id'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];

                // تحديث آخر دخول
                $stmt = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
                $stmt->execute([$admin['admin_id']]);

                // تجديد معرف الجلسة لمنع Session Fixation
                session_regenerate_id(true);

                // محاولة إنشاء جلسة آمنة (اختياري)
                try {
                    // createSecureUserSession - معطل مؤقتاً
                } catch (Exception $e) {
                    error_log("تحذير: فشل في إنشاء جلسة آمنة: " . $e->getMessage());
                }

                header('Location: dashboard.php');
                exit;
            } else {
                // تسجيل محاولة فاشلة
                $security->logLoginAttempt($username, 'admin', false);
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        }
    } else {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول المدير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/auth.css" rel="stylesheet">
    <link href="../assets/css/auth-modern.css" rel="stylesheet">
    <link href="../assets/css/auth-spacing.css" rel="stylesheet">
</head>
<body class="auth-page">
    <!-- تأثيرات الخلفية -->
    <div class="auth-particles"></div>

    <div class="container auth-container">
        <div class="row justify-content-center min-vh-100 align-items-center">
            <div class="col-md-5 col-lg-4">
                <div class="auth-card auth-card-enhanced">
                    <div class="auth-card-header">
                        <div class="auth-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h3 class="auth-title">دخول المدير</h3>
                        <p class="auth-subtitle">لوحة تحكم PlayGood الاحترافية</p>
                    </div>

                    <div class="auth-card-body">
                        <?php if ($error): ?>
                            <div class="auth-alert auth-alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="adminLoginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $security->generateCsrfToken(); ?>">

                            <div class="auth-form-group">
                                <input type="text" name="username" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-user-tie auth-form-icon"></i>
                                <label class="auth-form-label">اسم المستخدم أو البريد الإلكتروني</label>
                            </div>

                            <div class="auth-form-group">
                                <input type="password" name="password" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-lock auth-form-icon"></i>
                                <label class="auth-form-label">كلمة المرور</label>
                            </div>

                            <div class="auth-checkbox">
                                <input type="checkbox" name="remember" id="remember">
                                <label for="remember">تذكرني</label>
                            </div>

                            <button type="submit" class="auth-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>

                        <!-- روابط إضافية -->
                        <div class="text-center mt-5 pt-4" style="border-top: 1px solid rgba(102, 126, 234, 0.1);">
                            <p class="text-muted small mb-3" style="font-weight: 500;">أو تسجيل الدخول كـ</p>
                            <div class="d-flex justify-content-center gap-4 flex-wrap">
                                <a href="../client/login.php" class="auth-link-alt">
                                    <i class="fas fa-store"></i>
                                    <span>صاحب المحل</span>
                                </a>
                                <a href="../client/employee-login.php" class="auth-link-alt">
                                    <i class="fas fa-user-tie"></i>
                                    <span>موظف</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="text-center mt-4">
                    <p class="text-white-50 small">
                        <i class="fas fa-shield-alt me-1"></i>
                        لوحة تحكم آمنة ومحمية
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('adminLoginForm');
            const submitBtn = form.querySelector('.auth-btn');
            const originalText = submitBtn.innerHTML;

            form.addEventListener('submit', function() {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
                submitBtn.disabled = true;
            });

            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('.auth-form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>