<?php
/**
 * اختبار API إضافة المنتجات للجلسات
 * PlayGood Gaming Center Management System
 */

echo "<h1>اختبار API إضافة المنتجات للجلسات</h1>";
echo "<hr>";

// بدء الجلسة
session_start();

// تعيين معرف العميل للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ تم تعيين معرف العميل للاختبار: 1</p>";
}

try {
    // الاتصال بقاعدة البيانات
    require_once 'config/database.php';
    
    echo "<h2>1. فحص قاعدة البيانات</h2>";
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";

    // فحص الجداول المطلوبة
    $required_tables = ['sessions', 'cafeteria_items', 'session_products', 'devices'];
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
        }
    }

    echo "<h2>2. البحث عن بيانات للاختبار</h2>";
    
    // البحث عن جلسة نشطة
    $stmt = $pdo->query("SELECT session_id, device_id FROM sessions WHERE status = 'active' LIMIT 1");
    $active_session = $stmt->fetch();
    
    if ($active_session) {
        echo "<p style='color: green;'>✅ وجدت جلسة نشطة: {$active_session['session_id']}</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة - سأنشئ واحدة للاختبار</p>";
        
        // البحث عن جهاز متاح
        $stmt = $pdo->query("SELECT device_id FROM devices WHERE status = 'available' LIMIT 1");
        $available_device = $stmt->fetch();
        
        if ($available_device) {
            // إنشاء جلسة تجريبية
            $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, start_time, status, created_by) VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)");
            $stmt->execute([$available_device['device_id'], $_SESSION['client_id'], $_SESSION['client_id']]);
            $test_session_id = $pdo->lastInsertId();
            
            $active_session = ['session_id' => $test_session_id, 'device_id' => $available_device['device_id']];
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $test_session_id</p>";
        } else {
            throw new Exception("لا توجد أجهزة متاحة لإنشاء جلسة تجريبية");
        }
    }
    
    // البحث عن منتج
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = ? LIMIT 1");
    $stmt->execute([$_SESSION['client_id']]);
    $test_product = $stmt->fetch();
    
    if ($test_product) {
        echo "<p style='color: green;'>✅ وجدت منتج للاختبار: {$test_product['name']} - {$test_product['price']} ج.م</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات - سأنشئ واحد للاختبار</p>";
        
        $stmt = $pdo->prepare("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES (?, ?, ?, ?)");
        $stmt->execute(['منتج تجريبي', 5.00, 'مشروبات', $_SESSION['client_id']]);
        $test_product_id = $pdo->lastInsertId();
        
        $test_product = ['id' => $test_product_id, 'name' => 'منتج تجريبي', 'price' => 5.00];
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: {$test_product['name']}</p>";
    }

    echo "<h2>3. اختبار API مباشرة</h2>";
    
    // بيانات الاختبار
    $test_data = [
        'session_id' => $active_session['session_id'],
        'product_id' => $test_product['id'],
        'quantity' => 2
    ];
    
    echo "<h3>بيانات الاختبار:</h3>";
    echo "<ul>";
    echo "<li>معرف الجلسة: {$test_data['session_id']}</li>";
    echo "<li>معرف المنتج: {$test_data['product_id']}</li>";
    echo "<li>الكمية: {$test_data['quantity']}</li>";
    echo "</ul>";

    // محاولة إضافة المنتج مباشرة في قاعدة البيانات
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$test_data['session_id'], $test_data['product_id'], $test_data['quantity'], $test_product['price']]);
        
        $inserted_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إدراج المنتج في قاعدة البيانات مباشرة - ID: $inserted_id</p>";
        
        // حذف الإدراج التجريبي
        $pdo->exec("DELETE FROM session_products WHERE id = $inserted_id");
        echo "<p style='color: blue;'>ℹ️ تم حذف الإدراج التجريبي</p>";
        
        $pdo->commit();
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo "<p style='color: red;'>❌ فشل في الإدراج المباشر: " . $e->getMessage() . "</p>";
    }

    echo "<h2>4. اختبار API عبر cURL</h2>";
    
    // اختبار API عبر cURL
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/add_session_product.php';
    echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($test_data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Cookie: ' . session_name() . '=' . session_id()
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($curl);
    curl_close($curl);
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ خطأ cURL: $curl_error</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";
        echo "<h3>استجابة API:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        // محاولة فك تشفير JSON
        $json_response = json_decode($response, true);
        if ($json_response) {
            echo "<h3>البيانات المفكوكة:</h3>";
            echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
            print_r($json_response);
            echo "</pre>";
            
            if (isset($json_response['success']) && $json_response['success']) {
                echo "<p style='color: green; font-size: 18px;'><strong>✅ API يعمل بشكل صحيح!</strong></p>";
            } else {
                echo "<p style='color: red; font-size: 18px;'><strong>❌ API أرجع خطأ</strong></p>";
            }
        } else {
            echo "<p style='color: red;'>❌ الاستجابة ليست JSON صحيح</p>";
            echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
        }
    }

    echo "<h2>5. اختبار JavaScript</h2>";
    ?>
    
    <div id="js-test-result" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;"></div>
    
    <button onclick="testAPI()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار API عبر JavaScript
    </button>
    
    <script>
    async function testAPI() {
        const resultDiv = document.getElementById('js-test-result');
        resultDiv.innerHTML = '<p style="color: blue;">⏳ جاري الاختبار...</p>';
        
        const testData = {
            session_id: <?php echo $test_data['session_id']; ?>,
            product_id: <?php echo $test_data['product_id']; ?>,
            quantity: <?php echo $test_data['quantity']; ?>
        };
        
        try {
            const response = await fetch('client/api/add_session_product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            });
            
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <p style="color: green;"><strong>✅ نجح الاختبار!</strong></p>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>اسم المنتج:</strong> ${data.product_name}</p>
                        <p><strong>التكلفة الإجمالية:</strong> ${data.total_cost} ج.م</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ فشل الاختبار</strong></p>
                        <p><strong>الخطأ:</strong> ${data.error}</p>
                        <pre style="background: #ffe6e6; padding: 10px; border-radius: 5px;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (jsonError) {
                resultDiv.innerHTML = `
                    <p style="color: red;"><strong>❌ خطأ في تحليل JSON</strong></p>
                    <p><strong>خطأ JSON:</strong> ${jsonError.message}</p>
                    <p><strong>الاستجابة الخام:</strong></p>
                    <pre style="background: #ffe6e6; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                `;
            }
            
        } catch (error) {
            resultDiv.innerHTML = `
                <p style="color: red;"><strong>❌ خطأ في الشبكة</strong></p>
                <p><strong>الخطأ:</strong> ${error.message}</p>
            `;
        }
    }
    </script>
    
    <?php

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
p {
    margin: 8px 0;
    padding: 8px;
    border-radius: 4px;
}
ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>
