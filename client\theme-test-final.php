<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["client_id"])) {
    $_SESSION["client_id"] = 1;
    $_SESSION["user_id"] = 1;
    $_SESSION["client_name"] = "مركز الألعاب";
    $_SESSION["owner_name"] = "صاحب المحل";
}

$page_title = "اختبار المظهر المخصص";
$active_page = "test";
require_once "includes/header.php";
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-palette me-2"></i>اختبار المظهر المخصص
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        إذا كنت ترى هذه الرسالة بألوان مخصصة، فإن النظام يعمل بشكل صحيح!
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <button class="btn btn-primary w-100 mb-2">زر أساسي</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success w-100 mb-2">زر نجاح</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-secondary w-100 mb-2">زر ثانوي</button>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="settings.php" class="btn btn-primary me-2">
                            <i class="fas fa-cog me-1"></i>انتقل للإعدادات
                        </a>
                        <a href="dashboard.php" class="btn btn-success">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once "includes/footer.php"; ?>
