<?php
require_once 'config/database.php';

echo "<h1>اختبار إصلاح مشكلة الفواتير</h1>";

try {
    // 1. اختبار عرض الفواتير
    echo "<h2>1. اختبار عرض الفواتير</h2>";
    
    $invoices_query = $pdo->prepare("
        SELECT 
            i.*,
            s.session_id,
            s.start_time,
            s.end_time,
            d.device_name,
            d.device_type,
            r.room_name,
            c.name as customer_name,
            c.phone as customer_phone,
            TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE d.client_id = 1
        ORDER BY i.created_at DESC
        LIMIT 5
    ");
    
    $invoices_query->execute();
    $invoices = $invoices_query->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ تم جلب الفواتير بنجاح - العدد: " . count($invoices) . "</p>";
    
    if (count($invoices) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Invoice ID</th><th>Invoice Number</th><th>Session ID</th><th>Device</th><th>Customer</th><th>Total Cost</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($invoices as $invoice) {
            echo "<tr>";
            echo "<td>" . $invoice['invoice_id'] . "</td>";
            echo "<td>" . $invoice['invoice_number'] . "</td>";
            echo "<td>" . $invoice['session_id'] . "</td>";
            echo "<td>" . htmlspecialchars($invoice['device_name']) . "</td>";
            echo "<td>" . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . number_format($invoice['total_cost'], 2) . " ج.م</td>";
            echo "<td>" . $invoice['payment_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. اختبار عرض فاتورة واحدة
    if (count($invoices) > 0) {
        echo "<h2>2. اختبار عرض فاتورة واحدة</h2>";
        $test_invoice_id = $invoices[0]['invoice_id'];
        
        $single_invoice_query = $pdo->prepare("
            SELECT
                i.*,
                s.session_id,
                s.start_time,
                s.end_time,
                s.notes,
                d.device_name,
                d.device_type,
                d.hourly_rate,
                r.room_name,
                c.name as customer_name,
                c.phone as customer_phone,
                c.email as customer_email,
                cl.business_name,
                cl.address as business_address,
                cl.phone as business_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            LEFT JOIN clients cl ON d.client_id = cl.client_id
            WHERE i.invoice_id = ? AND d.client_id = 1
        ");
        
        $single_invoice_query->execute([$test_invoice_id]);
        $single_invoice = $single_invoice_query->fetch(PDO::FETCH_ASSOC);
        
        if ($single_invoice) {
            echo "<p style='color: green;'>✅ تم جلب الفاتورة الواحدة بنجاح - رقم الفاتورة: " . $single_invoice['invoice_number'] . "</p>";
            
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>تفاصيل الفاتورة:</h4>";
            echo "<ul>";
            echo "<li><strong>رقم الفاتورة:</strong> " . $single_invoice['invoice_number'] . "</li>";
            echo "<li><strong>الجلسة:</strong> " . $single_invoice['session_id'] . "</li>";
            echo "<li><strong>الجهاز:</strong> " . htmlspecialchars($single_invoice['device_name']) . "</li>";
            echo "<li><strong>العميل:</strong> " . htmlspecialchars($single_invoice['customer_name'] ?? 'غير محدد') . "</li>";
            echo "<li><strong>التكلفة الإجمالية:</strong> " . number_format($single_invoice['total_cost'], 2) . " ج.م</li>";
            echo "<li><strong>حالة الدفع:</strong> " . $single_invoice['payment_status'] . "</li>";
            echo "<li><strong>تاريخ الإنشاء:</strong> " . $single_invoice['created_at'] . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // اختبار رابط الفاتورة
            echo "<p><a href='client/invoice.php?id=" . $test_invoice_id . "' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض الفاتورة</a></p>";
            
        } else {
            echo "<p style='color: red;'>❌ فشل في جلب الفاتورة الواحدة</p>";
        }
    }
    
    // 3. اختبار تحديث حالة الدفع
    if (count($invoices) > 0) {
        echo "<h2>3. اختبار تحديث حالة الدفع</h2>";
        $test_invoice_id = $invoices[0]['invoice_id'];
        $current_status = $invoices[0]['payment_status'];
        
        echo "<p>الفاتورة رقم $test_invoice_id - الحالة الحالية: <strong>$current_status</strong></p>";
        
        // اختبار التحقق من وجود الفاتورة
        $check_stmt = $pdo->prepare("
            SELECT i.invoice_id, i.payment_status, s.session_id
            FROM invoices i
            JOIN sessions s ON i.session_id = s.session_id
            JOIN devices d ON s.device_id = d.device_id
            WHERE i.invoice_id = ? AND d.client_id = 1
        ");
        $check_stmt->execute([$test_invoice_id]);
        $invoice_check = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice_check) {
            echo "<p style='color: green;'>✅ تم التحقق من وجود الفاتورة بنجاح</p>";
            echo "<p>يمكن تحديث حالة الدفع عبر API: <code>client/api/update_payment_status.php</code></p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في التحقق من وجود الفاتورة</p>";
        }
    }
    
    // 4. اختبار الإحصائيات
    echo "<h2>4. اختبار إحصائيات الفواتير</h2>";
    
    $stats_query = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            SUM(CASE WHEN i.payment_status = 'paid' THEN i.total_cost ELSE 0 END) as paid_amount,
            SUM(CASE WHEN i.payment_status = 'pending' THEN i.total_cost ELSE 0 END) as pending_amount,
            COUNT(CASE WHEN i.payment_status = 'paid' THEN 1 END) as paid_count,
            COUNT(CASE WHEN i.payment_status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN i.payment_status = 'cancelled' THEN 1 END) as cancelled_count
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = 1
    ");
    $stats_query->execute();
    $stats = $stats_query->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 إحصائيات الفواتير:</h4>";
    echo "<ul>";
    echo "<li><strong>إجمالي الفواتير:</strong> " . $stats['total_invoices'] . "</li>";
    echo "<li><strong>الفواتير المدفوعة:</strong> " . $stats['paid_count'] . " (المبلغ: " . number_format($stats['paid_amount'], 2) . " ج.م)</li>";
    echo "<li><strong>الفواتير المعلقة:</strong> " . $stats['pending_count'] . " (المبلغ: " . number_format($stats['pending_amount'], 2) . " ج.م)</li>";
    echo "<li><strong>الفواتير الملغية:</strong> " . $stats['cancelled_count'] . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p style='color: green;'>✅ تم جلب الإحصائيات بنجاح</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>كود الخطأ: " . $e->getCode() . "</p>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<h3>🎉 نتيجة الاختبار</h3>";
echo "<p style='color: green; font-size: 18px; font-weight: bold;'>تم إصلاح مشكلة الفواتير بنجاح!</p>";
echo "<p>جميع الاستعلامات تعمل بشكل صحيح ولا توجد أخطاء في أعمدة قاعدة البيانات.</p>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='client/invoices.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الفواتير</a>";
echo "<a href='client/sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الجلسات</a>";
echo "</div>";
?>
