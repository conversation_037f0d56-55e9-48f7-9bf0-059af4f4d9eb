# ===== COMPLETE ACCESS DENIAL =====
# Block all access to configuration directory
Require all denied

# Additional protection layers
<Files "*">
    Require all denied
</Files>

# Block all file types
<FilesMatch ".*">
    Require all denied
</FilesMatch>

# Block directory browsing
Options -Indexes

# Disable script execution
<IfModule mod_php.c>
    php_flag engine off
</IfModule>

# Remove server signature
ServerSignature Off

# Security headers for any accidental access
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
