<?php
// تشغيل تحديث قاعدة البيانات لإضافة عمود نوع اللعب

require_once 'config/database.php';

try {
    echo "<h2>تحديث قاعدة البيانات - إضافة نوع اللعب</h2>";
    
    // إضافة عمود game_type إلى جدول sessions
    echo "<p>جاري إضافة عمود نوع اللعب...</p>";
    $pdo->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS game_type ENUM('single','multiplayer') DEFAULT 'single' COMMENT 'نوع اللعب: فردي أو زوجي'");
    echo "<p style='color: green;'>✅ تم إضافة عمود game_type بنجاح</p>";
    
    // تحديث الجلسات الموجودة لتكون فردية بشكل افتراضي
    echo "<p>جاري تحديث الجلسات الموجودة...</p>";
    $result = $pdo->exec("UPDATE sessions SET game_type = 'single' WHERE game_type IS NULL");
    echo "<p style='color: green;'>✅ تم تحديث $result جلسة موجودة</p>";
    
    // إضافة فهرس لتحسين الأداء
    echo "<p>جاري إضافة فهرس للأداء...</p>";
    try {
        $pdo->exec("ALTER TABLE sessions ADD INDEX IF NOT EXISTS idx_sessions_game_type (game_type)");
        echo "<p style='color: green;'>✅ تم إضافة الفهرس بنجاح</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠️ الفهرس موجود مسبقاً</p>";
        } else {
            throw $e;
        }
    }
    
    // التحقق من النتيجة
    $stmt = $pdo->query("SHOW COLUMNS FROM sessions LIKE 'game_type'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 تم التحديث بنجاح! يمكنك الآن استخدام ميزة تحديد نوع اللعب</p>";
        
        // عرض إحصائيات
        $stats = $pdo->query("SELECT game_type, COUNT(*) as count FROM sessions GROUP BY game_type")->fetchAll();
        echo "<h3>إحصائيات الجلسات حسب نوع اللعب:</h3>";
        echo "<ul>";
        foreach ($stats as $stat) {
            $type_name = $stat['game_type'] == 'single' ? 'فردي' : 'زوجي';
            echo "<li>{$type_name}: {$stat['count']} جلسة</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة العمود</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<br><a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل لصفحة الجلسات</a>";
?>
