-- تحديث نظام صلاحيات الإدمن - إضافة جميع الصفحات الموجودة
-- هذا الملف يضيف جميع صفحات الإدمن الموجودة فعلياً في المشروع

-- حذف الصفحات الموجودة لإعادة إدراجها بشكل كامل
DELETE FROM admin_page_permissions;
DELETE FROM admin_pages;

-- إدراج جميع الصفحات الموجودة فعلياً في مجلد admin
INSERT INTO admin_pages (page_name, page_label, page_url, page_icon, category, description, is_default, required_role) VALUES

-- الصفحات الأساسية
('dashboard', 'لوحة التحكم الرئيسية', 'dashboard.php', 'fas fa-dashboard', 'main', 'لوحة التحكم الرئيسية مع الإحصائيات والمعلومات العامة', TRUE, 'any'),
('profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة الملف الشخصي للإدمن وتعديل البيانات', TRUE, 'any'),

-- إدارة العملاء
('clients', 'إدارة العملاء', 'clients.php', 'fas fa-users', 'clients', 'عرض وإدارة قائمة العملاء وإضافة عملاء جدد', TRUE, 'any'),
('client_permissions', 'صلاحيات العملاء', 'client_permissions.php', 'fas fa-user-shield', 'clients', 'إدارة صلاحيات الوصول للصفحات للعملاء', TRUE, 'admin'),
('client_devices', 'أجهزة العملاء', 'client_devices.php', 'fas fa-desktop', 'clients', 'إدارة وعرض أجهزة العملاء المختلفة', TRUE, 'any'),

-- التقارير والإحصائيات
('reports', 'التقارير والإحصائيات', 'reports.php', 'fas fa-chart-bar', 'reports', 'عرض التقارير المالية والإحصائيات المفصلة', TRUE, 'any'),

-- الإعدادات والإدارة
('settings', 'إعدادات النظام', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام العامة والتحكم في الميزات', TRUE, 'admin'),

-- إدارة المديرين والصلاحيات (Super Admin فقط)
('admin_permissions', 'صلاحيات المديرين', 'admin_permissions.php', 'fas fa-user-cog', 'admin', 'إدارة صلاحيات المديرين للوصول للصفحات', FALSE, 'super_admin'),

-- النسخ الاحتياطية والصيانة
('download_backup', 'تحميل النسخ الاحتياطية', 'download_backup.php', 'fas fa-download', 'system', 'تحميل ملفات النسخ الاحتياطية', FALSE, 'admin'),

-- صفحات الاختبار والصيانة (Super Admin فقط)
('test_client_devices', 'اختبار أجهزة العملاء', 'test_client_devices.php', 'fas fa-vial', 'testing', 'صفحة اختبار وظائف أجهزة العملاء', FALSE, 'super_admin'),
('ensure_device_columns', 'صيانة أعمدة الأجهزة', 'ensure_device_columns.php', 'fas fa-tools', 'maintenance', 'صفحة صيانة وإصلاح أعمدة جدول الأجهزة', FALSE, 'super_admin'),

-- صفحات إضافية للمستقبل
('system_logs', 'سجلات النظام', 'system_logs.php', 'fas fa-file-alt', 'admin', 'عرض سجلات النظام والأنشطة (للتطوير المستقبلي)', FALSE, 'super_admin'),
('admins', 'إدارة المديرين', 'admins.php', 'fas fa-user-tie', 'admin', 'إضافة وإدارة المديرين (للتطوير المستقبلي)', FALSE, 'super_admin'),
('backup', 'إدارة النسخ الاحتياطية', 'backup.php', 'fas fa-hdd', 'system', 'إنشاء وإدارة النسخ الاحتياطية (للتطوير المستقبلي)', FALSE, 'admin');

-- منح الصلاحيات الافتراضية للمديرين الحاليين
INSERT IGNORE INTO admin_page_permissions (admin_id, page_id, is_enabled)
SELECT a.admin_id, ap.page_id, TRUE
FROM admins a
CROSS JOIN admin_pages ap
WHERE a.is_active = 1 
AND ap.is_active = 1
AND (
    -- Super Admin يحصل على جميع الصفحات
    a.role = 'super_admin' OR 
    -- Admin عادي يحصل على الصفحات الافتراضية المناسبة لدوره
    (ap.is_default = TRUE AND (ap.required_role = 'any' OR (ap.required_role = 'admin' AND a.role IN ('super_admin', 'admin'))))
);

-- عرض النتائج
SELECT 'تم تحديث جميع صفحات الإدمن بنجاح!' as message;
SELECT COUNT(*) as total_pages FROM admin_pages WHERE is_active = 1;
SELECT COUNT(*) as total_permissions FROM admin_page_permissions;

-- عرض الصفحات حسب الفئة
SELECT 
    category as 'الفئة',
    COUNT(*) as 'عدد الصفحات',
    GROUP_CONCAT(page_label SEPARATOR ', ') as 'الصفحات'
FROM admin_pages 
WHERE is_active = 1 
GROUP BY category 
ORDER BY category;

-- عرض صلاحيات المديرين
SELECT 
    a.username as 'اسم المستخدم',
    a.full_name as 'الاسم الكامل',
    a.role as 'الدور',
    COUNT(app.id) as 'عدد الصلاحيات الممنوحة'
FROM admins a
LEFT JOIN admin_page_permissions app ON a.admin_id = app.admin_id
WHERE a.is_active = 1
GROUP BY a.admin_id
ORDER BY a.role DESC, a.full_name;
