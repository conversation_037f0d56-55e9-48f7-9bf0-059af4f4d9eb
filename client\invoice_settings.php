<?php
/**
 * صفحة إعدادات الفاتورة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: ../login.php');
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

$page_title = "إعدادات الفاتورة";
$active_page = "settings";

// جلب الإعدادات الحالية
try {
    $settings_stmt = $pdo->prepare("SELECT * FROM invoice_settings WHERE client_id = ?");
    $settings_stmt->execute([$client_id]);
    $settings = $settings_stmt->fetch(PDO::FETCH_ASSOC);
    
    // إعدادات افتراضية إذا لم توجد
    if (!$settings) {
        $settings = [
            'header_color' => '#dc3545',
            'footer_text' => 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
            'footer_color' => '#000000',
            'company_address' => 'شارع فريد - الأحساء',
            'company_phone' => '01026362111',
            'show_qr_code' => true,
            'billing_method' => 'actual_time'
        ];
    }
} catch (PDOException $e) {
    $_SESSION['error'] = "حدث خطأ في جلب الإعدادات: " . $e->getMessage();
    $settings = [
        'header_color' => '#dc3545',
        'footer_text' => 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى',
        'footer_color' => '#000000',
        'company_address' => 'شارع فريد - الأحساء',
        'company_phone' => '01026362111',
        'show_qr_code' => true,
        'billing_method' => 'actual_time'
    ];
}

require_once 'includes/header.php';
?>

<style>
.settings-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.preview-card {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    background: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.color-preview {
    width: 50px;
    height: 30px;
    border-radius: 5px;
    border: 1px solid #ccc;
    display: inline-block;
    margin-left: 10px;
}

.invoice-preview {
    max-width: 400px;
    margin: 0 auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    font-size: 12px;
}

.preview-header {
    padding: 10px;
    color: white;
    text-align: center;
    font-weight: bold;
}

.preview-body {
    padding: 15px;
    text-align: center;
}

.preview-footer {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #ddd;
    font-size: 11px;
}
</style>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
        <h1 class="h2 mb-0">
            <i class="fas fa-cog me-2 text-primary"></i>إعدادات الفاتورة
        </h1>
        <div class="btn-toolbar">
            <a href="invoices.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-file-invoice me-1"></i>الفواتير
            </a>
            <a href="dashboard.php" class="btn btn-primary">
                <i class="fas fa-home me-1"></i>لوحة التحكم
            </a>
        </div>
    </div>

    <div class="row">
        <!-- نموذج الإعدادات -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>تخصيص مظهر الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="headerColor" class="form-label">
                                        <i class="fas fa-paint-brush me-1"></i>لون رأس الفاتورة
                                    </label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" 
                                               id="headerColor" value="<?php echo $settings['header_color']; ?>" 
                                               onchange="updatePreview()">
                                        <span class="color-preview ms-2" id="headerColorPreview" 
                                              style="background-color: <?php echo $settings['header_color']; ?>"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="footerColor" class="form-label">
                                        <i class="fas fa-font me-1"></i>لون نص التذييل
                                    </label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" 
                                               id="footerColor" value="<?php echo $settings['footer_color']; ?>" 
                                               onchange="updatePreview()">
                                        <span class="color-preview ms-2" id="footerColorPreview" 
                                              style="background-color: <?php echo $settings['footer_color']; ?>"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="footerText" class="form-label">
                                <i class="fas fa-comment me-1"></i>نص التذييل
                            </label>
                            <textarea class="form-control" id="footerText" rows="3" 
                                      onchange="updatePreview()" onkeyup="updatePreview()"
                                      placeholder="أدخل النص الذي سيظهر في أسفل الفاتورة"><?php echo htmlspecialchars($settings['footer_text']); ?></textarea>
                            <div class="form-text">سيظهر هذا النص في أسفل كل فاتورة</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="companyAddress" class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>عنوان الشركة
                                    </label>
                                    <input type="text" class="form-control" id="companyAddress" 
                                           value="<?php echo htmlspecialchars($settings['company_address'] ?? ''); ?>"
                                           placeholder="أدخل عنوان الشركة">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="companyPhone" class="form-label">
                                        <i class="fas fa-phone me-1"></i>هاتف الشركة
                                    </label>
                                    <input type="text" class="form-control" id="companyPhone" 
                                           value="<?php echo htmlspecialchars($settings['company_phone'] ?? ''); ?>"
                                           placeholder="أدخل رقم هاتف الشركة">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calculator me-1"></i>طريقة حساب التكلفة
                            </label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="billingMethod" id="actualTime"
                                               value="actual_time" <?php echo ($settings['billing_method'] ?? 'actual_time') === 'actual_time' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="actualTime">
                                            <i class="fas fa-clock me-1"></i>الوقت الفعلي
                                        </label>
                                        <small class="form-text text-muted d-block">حساب دقيق بناءً على الدقائق الفعلية</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="billingMethod" id="hourlyRounding"
                                               value="hourly_rounding" <?php echo ($settings['billing_method'] ?? 'actual_time') === 'hourly_rounding' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="hourlyRounding">
                                            <i class="fas fa-hourglass-half me-1"></i>تقريب الساعة
                                        </label>
                                        <small class="form-text text-muted d-block">تقريب أي جزء من الساعة للساعة الكاملة</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="billingMethod" id="firstMinuteFullHour"
                                               value="first_minute_full_hour" <?php echo ($settings['billing_method'] ?? 'actual_time') === 'first_minute_full_hour' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="firstMinuteFullHour">
                                            <i class="fas fa-bolt me-1"></i>ساعة من أول دقيقة
                                        </label>
                                        <small class="form-text text-muted d-block">ساعة كاملة بمجرد بدء الجلسة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info mt-2">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    <strong>مثال (45 دقيقة بسعر 20 ج.م/ساعة):</strong><br>
                                    • <strong>الوقت الفعلي:</strong> 15.00 ج.م (0.75 ساعة) |
                                    • <strong>تقريب الساعة:</strong> 20.00 ج.م (ساعة كاملة) |
                                    • <strong>ساعة من أول دقيقة:</strong> 20.00 ج.م (ساعة كاملة)
                                </small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showQrCode"
                                       <?php echo $settings['show_qr_code'] ? 'checked' : ''; ?> onchange="updatePreview()">
                                <label class="form-check-label" for="showQrCode">
                                    <i class="fas fa-qrcode me-1"></i>إظهار رمز QR في الفاتورة
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="resetSettings()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                            <button type="button" class="btn btn-success" onclick="saveSettings()">
                                <i class="fas fa-save me-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معاينة الفاتورة -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="invoice-preview" id="invoicePreview">
                        <div class="preview-header" id="previewHeader" 
                             style="background-color: <?php echo $settings['header_color']; ?>">
                            فاتورة رقم 20241201001
                        </div>
                        <div class="preview-body">
                            <div style="margin: 10px 0;">
                                <div style="width: 40px; height: 40px; background-color: <?php echo $settings['header_color']; ?>; 
                                           border-radius: 50%; margin: 0 auto 10px; display: flex; align-items: center; 
                                           justify-content: center; color: white; font-size: 16px;" id="previewLogo">
                                    <i class="fas fa-gamepad"></i>
                                </div>
                                <strong>مركز الألعاب</strong>
                            </div>
                            <div style="font-size: 10px; margin: 10px 0;">
                                <div>العميل: أحمد محمد</div>
                                <div>الجهاز: PS5 Pro</div>
                                <div>المدة: 2 ساعة</div>
                                <div style="background: <?php echo $settings['header_color']; ?>; color: white; 
                                           padding: 5px; margin: 5px 0;" id="previewTotal">
                                    المجموع: 120.00 جنيه
                                </div>
                            </div>
                        </div>
                        <div class="preview-footer" id="previewFooter" 
                             style="color: <?php echo $settings['footer_color']; ?>">
                            <?php echo htmlspecialchars($settings['footer_text']); ?>
                        </div>
                        <div id="previewQr" style="text-align: center; padding: 10px; font-size: 8px; 
                                                   <?php echo $settings['show_qr_code'] ? '' : 'display: none;'; ?>">
                            <div style="width: 30px; height: 30px; background: #000; margin: 0 auto 5px;"></div>
                            QR Code
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-link me-2"></i>روابط سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="invoice.php?session_id=1" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-eye me-1"></i>معاينة فاتورة حقيقية
                        </a>
                        <a href="invoices.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-list me-1"></i>جميع الفواتير
                        </a>
                        <a href="sessions.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-play me-1"></i>الجلسات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث المعاينة
function updatePreview() {
    const headerColor = document.getElementById('headerColor').value;
    const footerColor = document.getElementById('footerColor').value;
    const footerText = document.getElementById('footerText').value;
    const showQrCode = document.getElementById('showQrCode').checked;
    
    // تحديث ألوان المعاينة
    document.getElementById('previewHeader').style.backgroundColor = headerColor;
    document.getElementById('previewLogo').style.backgroundColor = headerColor;
    document.getElementById('previewTotal').style.backgroundColor = headerColor;
    document.getElementById('previewFooter').style.color = footerColor;
    document.getElementById('previewFooter').textContent = footerText || 'نص التذييل';
    
    // تحديث معاينة الألوان
    document.getElementById('headerColorPreview').style.backgroundColor = headerColor;
    document.getElementById('footerColorPreview').style.backgroundColor = footerColor;
    
    // إظهار/إخفاء QR Code
    document.getElementById('previewQr').style.display = showQrCode ? 'block' : 'none';
}

// حفظ الإعدادات
function saveSettings() {
    // الحصول على طريقة الحساب المحددة
    const billingMethod = document.querySelector('input[name="billingMethod"]:checked').value;

    const settings = {
        header_color: document.getElementById('headerColor').value,
        footer_color: document.getElementById('footerColor').value,
        footer_text: document.getElementById('footerText').value,
        company_address: document.getElementById('companyAddress').value,
        company_phone: document.getElementById('companyPhone').value,
        show_qr_code: document.getElementById('showQrCode').checked,
        billing_method: billingMethod
    };

    console.log('Sending settings:', settings);

    fetch('api/update_invoice_settings.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        // التحقق من نوع المحتوى
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            return response.text().then(text => {
                console.error('Non-JSON response:', text);
                throw new Error('الاستجابة ليست JSON صحيح');
            });
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');
            location.reload();
        } else {
            console.error('API Error:', data);
            let errorMsg = data.error || 'فشل في حفظ الإعدادات';
            if (data.debug) {
                errorMsg += '\n\nتفاصيل الخطأ:\n' + JSON.stringify(data.debug, null, 2);
            }
            alert('حدث خطأ: ' + errorMsg);
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        alert('حدث خطأ في الاتصال: ' + error.message + '\n\nيرجى التحقق من:\n1. تسجيل الدخول\n2. اتصال الإنترنت\n3. إعدادات الخادم');
    });
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        document.getElementById('headerColor').value = '#dc3545';
        document.getElementById('footerColor').value = '#000000';
        document.getElementById('footerText').value = 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى';
        document.getElementById('companyAddress').value = 'شارع فريد - الأحساء';
        document.getElementById('companyPhone').value = '01026362111';
        document.getElementById('showQrCode').checked = true;
        document.getElementById('actualTime').checked = true; // الإعداد الافتراضي
        updatePreview();
    }
}

// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});
</script>

<?php require_once 'includes/footer.php'; ?>
