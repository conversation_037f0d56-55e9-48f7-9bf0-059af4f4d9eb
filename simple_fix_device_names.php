<?php
require_once 'config/database.php';

echo "إصلاح أسماء الأجهزة...<br>";

try {
    // تحديث أسماء الأجهزة لتكون مختلفة عن أسماء العملاء
    $devices = $pdo->query("SELECT device_id, device_name, device_type FROM devices WHERE client_id = 1")->fetchAll();
    
    foreach ($devices as $device) {
        $new_name = $device['device_type'] . "_" . $device['device_id'];
        
        $stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_id = ?");
        $stmt->execute([$new_name, $device['device_id']]);
        
        echo "تم تحديث الجهاز {$device['device_id']}: {$device['device_name']} → $new_name<br>";
    }
    
    echo "<br>✅ تم الانتهاء من إصلاح أسماء الأجهزة<br>";
    echo "<a href='client/invoices.php'>الذهاب إلى صفحة الفواتير</a>";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
