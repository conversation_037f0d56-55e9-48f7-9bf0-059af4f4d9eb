<?php
session_start();
require_once 'config/database.php';

$error = '';
$success = '';

if ($_POST) {
    $business_name = $_POST['business_name'] ?? '';
    $owner_name = $_POST['owner_name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $address = $_POST['address'] ?? '';
    $city = $_POST['city'] ?? '';
    
    // التحقق من البيانات
    if (empty($business_name) || empty($owner_name) || empty($email) || empty($phone) || empty($password)) {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور غير متطابقة';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        // التحقق من البريد الإلكتروني
        $stmt = $pdo->prepare("SELECT email FROM clients WHERE email = ?");
        $stmt->execute([$email]);
        
        if ($stmt->fetch()) {
            $error = 'البريد الإلكتروني مسجل مسبقاً';
        } else {
            // إدراج العميل الجديد
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("INSERT INTO clients (business_name, owner_name, email, phone, password_hash, address, city, subscription_start, subscription_end) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH))");
            
            if ($stmt->execute([$business_name, $owner_name, $email, $phone, $password_hash, $address, $city])) {
                $success = 'تم التسجيل بنجاح! يمكنك الآن تسجيل الدخول';
            } else {
                $error = 'حدث خطأ أثناء التسجيل';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل عميل جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
    <link href="assets/css/auth-modern.css" rel="stylesheet">
    <link href="assets/css/auth-spacing.css" rel="stylesheet">
    <link href="assets/css/auth-enhanced.css" rel="stylesheet">
    <link href="assets/css/auth-animations.css" rel="stylesheet">
    <link href="assets/css/auth-minimal.css" rel="stylesheet">
    <link href="assets/css/no-shake.css" rel="stylesheet">
</head>
<body class="auth-page">
    <!-- تأثيرات الخلفية -->
    <div class="auth-particles"></div>

    <div class="container auth-container">
        <div class="row justify-content-center py-3">
            <div class="col-md-10 col-lg-8">
                <div class="auth-card auth-card-enhanced">
                    <div class="auth-card-header">
                        <div class="auth-icon">
                            <i class="fas fa-store-alt"></i>
                        </div>
                        <h3 class="auth-title">تسجيل محل جديد</h3>
                        <p class="auth-subtitle">انضم إلى منصة PlayGood الاحترافية</p>
                    </div>

                    <div class="auth-card-body">
                        <?php if ($error): ?>
                            <div class="auth-alert auth-alert-danger">
                                <i class="fas fa-exclamation-triangle ms-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="auth-alert auth-alert-success">
                                <i class="fas fa-check-circle ms-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="registerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="text" name="business_name" class="auth-form-control" placeholder=" " required value="<?php echo $_POST['business_name'] ?? ''; ?>">
                                        <i class="fas fa-store auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">اسم المحل *</label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="text" name="owner_name" class="auth-form-control" placeholder=" " required value="<?php echo $_POST['owner_name'] ?? ''; ?>">
                                        <i class="fas fa-user auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">اسم المدير *</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="email" name="email" class="auth-form-control" placeholder=" " required value="<?php echo $_POST['email'] ?? ''; ?>">
                                        <i class="fas fa-envelope auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">البريد الإلكتروني *</label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="tel" name="phone" class="auth-form-control" placeholder=" " required value="<?php echo $_POST['phone'] ?? ''; ?>">
                                        <i class="fas fa-phone auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">رقم الهاتف *</label>
                                    </div>
                                </div>
                            </div>

                            <div class="auth-form-floating">
                                <textarea name="address" class="auth-form-control" placeholder=" " rows="2" style="padding-top: 1rem; resize: vertical;"><?php echo $_POST['address'] ?? ''; ?></textarea>
                                <i class="fas fa-map-marker-alt auth-form-icon-enhanced"></i>
                                <label class="auth-form-label">العنوان</label>
                            </div>

                            <div class="auth-form-floating">
                                <input type="text" name="city" class="auth-form-control" placeholder=" " value="<?php echo $_POST['city'] ?? ''; ?>">
                                <i class="fas fa-city auth-form-icon-enhanced"></i>
                                <label class="auth-form-label">المدينة</label>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="password" name="password" class="auth-form-control" placeholder=" " required>
                                        <i class="fas fa-lock auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">كلمة المرور *</label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="auth-form-floating">
                                        <input type="password" name="confirm_password" class="auth-form-control" placeholder=" " required>
                                        <i class="fas fa-lock auth-form-icon-enhanced"></i>
                                        <label class="auth-form-label">تأكيد كلمة المرور *</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="auth-btn auth-btn-modern">
                                <i class="fas fa-user-plus ms-2"></i>
                                تسجيل المحل
                            </button>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-2">لديك حساب؟</p>
                            <a href="client/login.php" class="auth-link auth-link-modern">تسجيل الدخول</a>
                        </div>

                        <!-- روابط إضافية -->
                        <div class="text-center mt-5 pt-4" style="border-top: 1px solid rgba(102, 126, 234, 0.1);">
                            <p class="text-muted small mb-3" style="font-weight: 500;">لديك حساب بالفعل؟</p>
                            <div class="d-flex justify-content-center gap-4 flex-wrap">
                                <!--<a href="admin/login.php" class="auth-link-alt">
                                    <i class="fas fa-user-shield"></i>
                                    <span>مدير النظام</span>
                                </a>-->
                                <a href="client/employee-login.php" class="auth-link-alt">
                                    <i class="fas fa-users"></i>
                                    <span>موظف</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="text-center mt-4">
                    <p class="text-white-50 small">
                        <i class="fas fa-shield-alt me-1"></i>
                        جميع البيانات محمية ومشفرة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/auth-effects.js"></script>
    <script>
        // تحسين تجربة المستخدم المتقدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const submitBtn = form.querySelector('.auth-btn');
            const originalText = submitBtn.innerHTML;

            form.addEventListener('submit', function(e) {
                // التحقق من تطابق كلمات المرور
                const password = form.querySelector('input[name="password"]').value;
                const confirmPassword = form.querySelector('input[name="confirm_password"]').value;

                if (password !== confirmPassword) {
                    e.preventDefault();

                    // إظهار رسالة خطأ محسنة
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'auth-alert auth-alert-danger';
                    errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle ms-2"></i>كلمات المرور غير متطابقة';

                    const existingError = document.querySelector('.auth-alert-danger');
                    if (existingError) {
                        existingError.remove();
                    }

                    form.insertBefore(errorDiv, form.firstChild);

                    // تأثير تنبيه للحقول - بدون اهتزاز
                    const passwordFields = [
                        form.querySelector('input[name="password"]'),
                        form.querySelector('input[name="confirm_password"]')
                    ];

                    passwordFields.forEach(field => {
                        field.style.borderColor = '#dc3545';
                        field.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.2)';
                        setTimeout(() => {
                            field.style.borderColor = '';
                            field.style.boxShadow = '';
                        }, 2000);
                    });

                    return;
                }

                submitBtn.classList.add('auth-loading');
                submitBtn.disabled = true;

                // رسائل التحميل المتنوعة
                const loadingMessages = [
                    'جاري التسجيل...',
                    'جاري التحقق من البيانات...',
                    'جاري إنشاء الحساب...',
                    'جاري إعداد المحل...',
                    'تم الإنشاء بنجاح!'
                ];

                let messageIndex = 0;
                const messageInterval = setInterval(() => {
                    if (messageIndex < loadingMessages.length) {
                        submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingMessages[messageIndex]}`;
                        messageIndex++;
                    } else {
                        clearInterval(messageInterval);
                    }
                }, 1000);
            });

            // تأثيرات الحقول المحسنة
            const inputs = document.querySelectorAll('.auth-form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    this.style.transform = 'translateY(-3px) scale(1.01)';
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                    this.style.transform = '';
                });

                // تحديد الحقول المملوءة عند تحميل الصفحة
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }

                // تأثير الكتابة
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = 'var(--auth-primary)';
                        this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                    } else {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                    }
                });
            });

            // التحقق من قوة كلمة المرور المحسن
            const passwordInput = form.querySelector('input[name="password"]');
            const confirmPasswordInput = form.querySelector('input[name="confirm_password"]');

            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let strengthText = '';
                    let strengthColor = '';

                    if (password.length >= 8) strength++;
                    if (/[A-Z]/.test(password)) strength++;
                    if (/[a-z]/.test(password)) strength++;
                    if (/[0-9]/.test(password)) strength++;
                    if (/[^A-Za-z0-9]/.test(password)) strength++;

                    // تحديد قوة كلمة المرور
                    if (strength < 2) {
                        strengthText = 'ضعيفة';
                        strengthColor = '#dc3545';
                    } else if (strength < 4) {
                        strengthText = 'متوسطة';
                        strengthColor = '#ffc107';
                    } else {
                        strengthText = 'قوية';
                        strengthColor = '#198754';
                    }

                    // تغيير لون الحقل حسب القوة
                    this.style.borderColor = strengthColor;
                });
            }

            // التحقق من تطابق كلمات المرور في الوقت الفعلي
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    const password = passwordInput.value;
                    const confirmPassword = this.value;

                    if (confirmPassword.length > 0) {
                        if (password === confirmPassword) {
                            this.style.borderColor = '#198754';
                            this.style.boxShadow = '0 0 0 3px rgba(25, 135, 84, 0.1)';
                        } else {
                            this.style.borderColor = '#dc3545';
                            this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
                        }
                    } else {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                    }
                });
            }

            // تأثيرات الأيقونات
            const icons = document.querySelectorAll('.auth-form-icon-enhanced');
            icons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-50%) scale(1.2) rotate(5deg)';
                    this.style.color = 'var(--auth-accent)';
                });

                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-50%) scale(1) rotate(0deg)';
                    this.style.color = 'var(--auth-primary)';
                });
            });

            // تأثير الزر المحسن
            submitBtn.addEventListener('mouseenter', function() {
                if (!this.disabled) {
                    this.style.transform = 'translateY(-6px) scale(1.03)';
                }
            });

            submitBtn.addEventListener('mouseleave', function() {
                if (!this.disabled) {
                    this.style.transform = '';
                }
            });

            // تأثيرات البطاقة
            const card = document.querySelector('.auth-card-enhanced');
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.01)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });

        // إضافة تأثيرات CSS إضافية
        const style = document.createElement('style');
        style.textContent = `
            @keyframes errorHighlight {
                0% { border-color: #dc3545; box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2); }
                100% { border-color: #dc3545; box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>