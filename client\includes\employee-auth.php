<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define permissions for each role
define('EMPLOYEE_PERMISSIONS', [
    'manager' => [
        'level' => 4,
        'permissions' => [
            'manage_devices',
            'manage_sessions',
            'manage_rooms',
            'manage_customers',
            'manage_cafeteria',
            'manage_reservations',
            'view_finances',
            'view_reports',
            'manage_employees',
            'manage_settings'
        ]
    ],
    'cashier' => [
        'level' => 3,
        'permissions' => [
            'manage_sessions',
            'manage_cafeteria',
            'view_reports'
        ]
    ],
    'waiter' => [
        'level' => 2,
        'permissions' => [
            'manage_cafeteria',
            'view_sessions'
        ]
    ],
    'cleaner' => [
        'level' => 1,
        'permissions' => []
    ]
]);

function isEmployeeLoggedIn() {
    return isset($_SESSION['employee_id']) && !empty($_SESSION['employee_id']);
}

/**
 * التحقق من صحة جلسة الموظف وحالة العميل
 */
function isEmployeeSessionValid() {
    if (!isEmployeeLoggedIn()) {
        return false;
    }

    // التحقق من الجلسات المنتهية
    if (isEmployeeSessionInvalidated($_SESSION['employee_id'])) {
        return false;
    }

    // التحقق من حالة العميل في قاعدة البيانات
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT c.is_active as client_active, e.is_active as employee_active
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.id = ?
        ");
        $stmt->execute([$_SESSION['employee_id']]);
        $result = $stmt->fetch();

        if (!$result || !$result['client_active'] || !$result['employee_active']) {
            return false;
        }

        return true;
    } catch (PDOException $e) {
        error_log('خطأ في التحقق من صحة جلسة الموظف: ' . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من إنهاء جلسة الموظف
 */
function isEmployeeSessionInvalidated($employee_id) {
    $invalidated_sessions_file = '../temp/invalidated_sessions.json';

    if (!file_exists($invalidated_sessions_file)) {
        return false;
    }

    try {
        $content = file_get_contents($invalidated_sessions_file);
        $invalidated_sessions = json_decode($content, true) ?: [];

        return isset($invalidated_sessions[$employee_id]);
    } catch (Exception $e) {
        error_log('خطأ في قراءة الجلسات المنتهية: ' . $e->getMessage());
        return false;
    }
}

/**
 * إنهاء جلسة الموظف وتوجيهه لصفحة تسجيل الدخول
 */
function invalidateEmployeeSession($reason = 'session_expired') {
    if (isEmployeeLoggedIn()) {
        // تسجيل سبب إنهاء الجلسة
        error_log("إنهاء جلسة الموظف {$_SESSION['employee_id']}: {$reason}");

        // مسح بيانات الجلسة
        unset($_SESSION['employee_id']);
        unset($_SESSION['employee_name']);
        unset($_SESSION['employee_role']);
        unset($_SESSION['client_id']);
        unset($_SESSION['business_name']);
        unset($_SESSION['owner_name']);
    }
}

function requireEmployeeLogin() {
    // Check if we're already on the login page to prevent redirect loop
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'employee-login.php') {
        return;
    }

    if (!isEmployeeLoggedIn()) {
        header('Location: employee-login.php');
        exit;
    }

    // التحقق من صحة الجلسة
    if (!isEmployeeSessionValid()) {
        invalidateEmployeeSession('client_deactivated_or_employee_disabled');
        header('Location: employee-login.php?error=session_invalid');
        exit;
    }
}

function employeeHasPermission($permission) {
    if (!isEmployeeLoggedIn()) {
        return false;
    }

    // أولاً، تحقق من الصلاحيات المخصصة
    global $pdo;
    $employee_id = $_SESSION['employee_id'];

    try {
        // التحقق من استخدام الصلاحيات المخصصة
        $stmt = $pdo->prepare("SELECT custom_permissions FROM employees WHERE id = ?");
        $stmt->execute([$employee_id]);
        $employee = $stmt->fetch();

        if ($employee && $employee['custom_permissions']) {
            // استخدام الصلاحيات المخصصة
            return employeeHasCustomPermission($permission);
        }
    } catch (PDOException $e) {
        error_log('خطأ في التحقق من نوع الصلاحيات: ' . $e->getMessage());
    }

    // استخدام صلاحيات الدور التقليدية
    $employeeRole = $_SESSION['employee_role'] ?? '';
    if (!isset(EMPLOYEE_PERMISSIONS[$employeeRole])) {
        return false;
    }

    return in_array($permission, EMPLOYEE_PERMISSIONS[$employeeRole]['permissions']);
}

function getEmployeePermissions() {
    if (!isEmployeeLoggedIn()) {
        return [];
    }

    $employeeRole = $_SESSION['employee_role'] ?? '';
    return EMPLOYEE_PERMISSIONS[$employeeRole]['permissions'] ?? [];
}

/**
 * التحقق من الصلاحيات المخصصة للموظف
 */
function employeeHasCustomPermission($permission) {
    if (!isEmployeeLoggedIn()) {
        return false;
    }

    global $pdo;
    $employee_id = $_SESSION['employee_id'];

    try {
        // التحقق من استخدام الصلاحيات المخصصة
        $stmt = $pdo->prepare("SELECT custom_permissions FROM employees WHERE id = ?");
        $stmt->execute([$employee_id]);
        $employee = $stmt->fetch();

        if (!$employee || !$employee['custom_permissions']) {
            // استخدام صلاحيات الدور التقليدية
            return employeeHasPermission($permission);
        }

        // التحقق من الصلاحيات المخصصة
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM employee_permissions ep
            JOIN permissions p ON ep.permission_id = p.permission_id
            WHERE ep.employee_id = ? AND p.permission_name = ? AND p.is_active = 1
        ");
        $stmt->execute([$employee_id, $permission]);

        return $stmt->fetchColumn() > 0;

    } catch (PDOException $e) {
        error_log('خطأ في التحقق من الصلاحيات المخصصة: ' . $e->getMessage());
        // في حالة الخطأ، استخدم النظام التقليدي
        return employeeHasPermission($permission);
    }
}

/**
 * التحقق من إمكانية الوصول للصفحة
 */
function employeeCanAccessPage($page_name) {
    if (!isEmployeeLoggedIn()) {
        return false;
    }

    global $pdo;
    $employee_id = $_SESSION['employee_id'];

    try {
        // التحقق من استخدام الصلاحيات المخصصة
        $stmt = $pdo->prepare("SELECT custom_permissions FROM employees WHERE id = ?");
        $stmt->execute([$employee_id]);
        $employee = $stmt->fetch();

        if (!$employee || !$employee['custom_permissions']) {
            // استخدام النظام التقليدي - جميع الصفحات متاحة حسب الدور
            return true;
        }

        // التحقق من الصفحات المخصصة
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM employee_pages ep
            JOIN pages p ON ep.page_id = p.page_id
            WHERE ep.employee_id = ? AND p.page_name = ? AND p.is_active = 1
        ");
        $stmt->execute([$employee_id, $page_name]);

        return $stmt->fetchColumn() > 0;

    } catch (PDOException $e) {
        error_log('خطأ في التحقق من الوصول للصفحة: ' . $e->getMessage());
        // في حالة الخطأ، السماح بالوصول
        return true;
    }
}

/**
 * جلب قائمة صلاحيات الموظف
 */
function getEmployeeCustomPermissions($employee_id = null) {
    if (!$employee_id && isEmployeeLoggedIn()) {
        $employee_id = $_SESSION['employee_id'];
    }

    if (!$employee_id) {
        return [];
    }

    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT p.permission_name, p.permission_label, p.category
            FROM employee_permissions ep
            JOIN permissions p ON ep.permission_id = p.permission_id
            WHERE ep.employee_id = ? AND p.is_active = 1
            ORDER BY p.category, p.permission_label
        ");
        $stmt->execute([$employee_id]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        error_log('خطأ في جلب صلاحيات الموظف: ' . $e->getMessage());
        return [];
    }
}

/**
 * جلب قائمة صفحات الموظف
 */
function getEmployeeCustomPages($employee_id = null) {
    if (!$employee_id && isEmployeeLoggedIn()) {
        $employee_id = $_SESSION['employee_id'];
    }

    if (!$employee_id) {
        return [];
    }

    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT p.page_name, p.page_label, p.page_url, p.page_icon, p.category
            FROM employee_pages ep
            JOIN pages p ON ep.page_id = p.page_id
            WHERE ep.employee_id = ? AND p.is_active = 1
            ORDER BY p.category, p.page_label
        ");
        $stmt->execute([$employee_id]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        error_log('خطأ في جلب صفحات الموظف: ' . $e->getMessage());
        return [];
    }
}