<?php
/**
 * إعدادات متقدمة للنظام التجريبي - PlayGood
 * يمكن تخصيص هذه الإعدادات حسب احتياجات المشروع
 */

// إعدادات التجربة المجانية
define('TRIAL_SYSTEM_ENABLED', true); // تفعيل/إلغاء تفعيل النظام التجريبي
define('TRIAL_MAX_DEVICES', 10); // الحد الأقصى للأجهزة في التجربة
define('TRIAL_MAX_CUSTOMERS', 20); // الحد الأقصى للعملاء في التجربة
define('TRIAL_MAX_SESSIONS', 50); // الحد الأقصى للجلسات في التجربة
define('TRIAL_MAX_CAFETERIA_ITEMS', 30); // الحد الأقصى لمنتجات الكافتيريا

// إعدادات التنبيهات
define('TRIAL_WARNING_TIME', 1800); // التنبيه قبل 30 دقيقة من انتهاء التجربة (بالثواني)
define('TRIAL_FINAL_WARNING_TIME', 300); // التنبيه النهائي قبل 5 دقائق (بالثواني)

// إعدادات البيانات التجريبية الافتراضية
$TRIAL_DEFAULT_DEVICES = [
    ['جهاز PS5 - تجريبي 1', 'PS5', 20.00],
    ['جهاز PS4 - تجريبي 1', 'PS4', 15.00],
    ['جهاز Xbox - تجريبي 1', 'Xbox', 18.00],
    ['جهاز PC - تجريبي 1', 'PC', 25.00],
    ['جهاز PS5 - تجريبي 2', 'PS5', 20.00],
    ['جهاز PS4 - تجريبي 2', 'PS4', 15.00]
];

$TRIAL_DEFAULT_CUSTOMERS = [
    ['أحمد محمد علي', '01234567890', '<EMAIL>'],
    ['فاطمة حسن محمود', '01234567891', '<EMAIL>'],
    ['محمد عبدالله أحمد', '01234567892', '<EMAIL>'],
    ['سارة علي حسن', '01234567893', '<EMAIL>'],
    ['عمر خالد محمد', '01234567894', '<EMAIL>'],
    ['نور الدين أحمد', '01234567895', '<EMAIL>']
];

$TRIAL_DEFAULT_CAFETERIA = [
    // مشروبات ساخنة
    ['شاي سادة', 5.00, 'مشروبات ساخنة', 'شاي أحمر طازج'],
    ['شاي بحليب', 8.00, 'مشروبات ساخنة', 'شاي بحليب طازج'],
    ['قهوة تركي', 10.00, 'مشروبات ساخنة', 'قهوة تركية أصيلة'],
    ['نسكافية', 15.00, 'مشروبات ساخنة', 'نسكافية 3 في 1'],
    ['قهوة فرنساوي', 25.00, 'مشروبات ساخنة', 'قهوة فرنسية مميزة'],
    ['كابتشينو', 20.00, 'مشروبات ساخنة', 'كابتشينو كريمي'],
    
    // مشروبات باردة
    ['بيبسي', 12.00, 'مشروبات باردة', 'مشروب غازي'],
    ['كوكاكولا', 12.00, 'مشروبات باردة', 'مشروب غازي'],
    ['سبرايت', 12.00, 'مشروبات باردة', 'مشروب غازي بالليمون'],
    ['فانتا', 12.00, 'مشروبات باردة', 'مشروب غازي بالبرتقال'],
    ['ماء', 5.00, 'مشروبات باردة', 'مياه معدنية'],
    ['عصير برتقال', 15.00, 'مشروبات باردة', 'عصير برتقال طازج'],
    ['عصير تفاح', 15.00, 'مشروبات باردة', 'عصير تفاح طبيعي'],
    
    // مأكولات
    ['ساندويتش جبن', 25.00, 'مأكولات', 'ساندويتش جبن مشوي'],
    ['ساندويتش تونة', 30.00, 'مأكولات', 'ساندويتش تونة بالخضار'],
    ['برجر دجاج', 35.00, 'مأكولات', 'برجر دجاج مقرمش'],
    ['بيتزا صغيرة', 40.00, 'مأكولات', 'بيتزا مارجريتا'],
    ['شاورما', 20.00, 'مأكولات', 'شاورما دجاج'],
    
    // تسالي وحلويات
    ['شيبس', 8.00, 'تسالي', 'شيبس مقرمش'],
    ['فشار', 10.00, 'تسالي', 'فشار بالزبدة'],
    ['شوكولاتة', 15.00, 'حلويات', 'شوكولاتة بالحليب'],
    ['كيك', 20.00, 'حلويات', 'قطعة كيك'],
    ['آيس كريم', 18.00, 'حلويات', 'آيس كريم بالفانيليا']
];

// دالة إضافة البيانات التجريبية المتقدمة
function addAdvancedTrialData($trial_id) {
    global $pdo, $TRIAL_DEFAULT_DEVICES, $TRIAL_DEFAULT_CUSTOMERS, $TRIAL_DEFAULT_CAFETERIA;
    
    try {
        // إضافة الأجهزة
        foreach ($TRIAL_DEFAULT_DEVICES as $device) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_devices (trial_id, device_name, device_type, hourly_rate) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $device[0], $device[1], $device[2]]);
        }

        // إضافة العملاء
        foreach ($TRIAL_DEFAULT_CUSTOMERS as $customer) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_customers (trial_id, name, phone, email) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $customer[0], $customer[1], $customer[2]]);
        }

        // إضافة منتجات الكافتيريا
        foreach ($TRIAL_DEFAULT_CAFETERIA as $item) {
            $stmt = $pdo->prepare("
                INSERT INTO trial_cafeteria_items (trial_id, name, price, category, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$trial_id, $item[0], $item[1], $item[2], $item[3]]);
        }

        return true;
    } catch (PDOException $e) {
        error_log('خطأ في إضافة البيانات التجريبية المتقدمة: ' . $e->getMessage());
        return false;
    }
}

// دالة التحقق من حدود النظام التجريبي
function checkTrialLimits($trial_id, $type) {
    global $pdo;
    
    $limits = [
        'devices' => TRIAL_MAX_DEVICES,
        'customers' => TRIAL_MAX_CUSTOMERS,
        'sessions' => TRIAL_MAX_SESSIONS,
        'cafeteria_items' => TRIAL_MAX_CAFETERIA_ITEMS
    ];
    
    if (!isset($limits[$type])) {
        return true;
    }
    
    $table_map = [
        'devices' => 'trial_devices',
        'customers' => 'trial_customers',
        'sessions' => 'trial_sessions',
        'cafeteria_items' => 'trial_cafeteria_items'
    ];
    
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$table_map[$type]} WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $count = $stmt->fetchColumn();
        
        return $count < $limits[$type];
    } catch (PDOException $e) {
        error_log('خطأ في التحقق من حدود النظام التجريبي: ' . $e->getMessage());
        return false;
    }
}

// دالة الحصول على إحصائيات متقدمة
function getAdvancedTrialStats($trial_id) {
    global $pdo;
    
    try {
        $stats = [];
        
        // إحصائيات الاستخدام
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_devices,
                SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_devices,
                SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_devices,
                SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_devices
            FROM trial_devices WHERE trial_id = ?
        ");
        $stmt->execute([$trial_id]);
        $device_stats = $stmt->fetch();
        $stats['devices'] = $device_stats;
        
        // إحصائيات الجلسات
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_sessions,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_sessions,
                AVG(duration_minutes) as avg_duration,
                SUM(total_cost) as total_revenue
            FROM trial_sessions WHERE trial_id = ?
        ");
        $stmt->execute([$trial_id]);
        $session_stats = $stmt->fetch();
        $stats['sessions'] = $session_stats;
        
        // إحصائيات العملاء
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_customers FROM trial_customers WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $stats['customers'] = $stmt->fetch();
        
        // إحصائيات الكافتيريا
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_items,
                COUNT(DISTINCT category) as categories,
                AVG(price) as avg_price,
                MIN(price) as min_price,
                MAX(price) as max_price
            FROM trial_cafeteria_items WHERE trial_id = ?
        ");
        $stmt->execute([$trial_id]);
        $stats['cafeteria'] = $stmt->fetch();
        
        return $stats;
    } catch (PDOException $e) {
        error_log('خطأ في جلب الإحصائيات المتقدمة: ' . $e->getMessage());
        return [];
    }
}

// دالة تصدير بيانات التجربة (للمستخدمين الذين يريدون الاحتفاظ بالبيانات)
function exportTrialData($trial_id) {
    global $pdo;
    
    try {
        $export_data = [];
        
        // بيانات العميل
        $stmt = $pdo->prepare("SELECT * FROM trial_clients WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $export_data['client'] = $stmt->fetch();
        
        // الأجهزة
        $stmt = $pdo->prepare("SELECT * FROM trial_devices WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $export_data['devices'] = $stmt->fetchAll();
        
        // العملاء
        $stmt = $pdo->prepare("SELECT * FROM trial_customers WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $export_data['customers'] = $stmt->fetchAll();
        
        // الجلسات
        $stmt = $pdo->prepare("SELECT * FROM trial_sessions WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $export_data['sessions'] = $stmt->fetchAll();
        
        // منتجات الكافتيريا
        $stmt = $pdo->prepare("SELECT * FROM trial_cafeteria_items WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $export_data['cafeteria_items'] = $stmt->fetchAll();
        
        return json_encode($export_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    } catch (PDOException $e) {
        error_log('خطأ في تصدير بيانات التجربة: ' . $e->getMessage());
        return false;
    }
}

// دالة إرسال تقرير استخدام التجربة (للتحليلات)
function sendTrialUsageReport($trial_id) {
    $stats = getAdvancedTrialStats($trial_id);
    $client_data = getTrialClientData($trial_id);
    
    $report = [
        'trial_id' => $trial_id,
        'business_name' => $client_data['business_name'] ?? 'غير محدد',
        'trial_duration' => TRIAL_DURATION_HOURS,
        'usage_stats' => $stats,
        'completed_at' => date('Y-m-d H:i:s')
    ];
    
    // يمكن إرسال التقرير لنظام التحليلات أو حفظه في ملف
    $log_entry = "[" . date('Y-m-d H:i:s') . "] تقرير استخدام التجربة: " . json_encode($report, JSON_UNESCAPED_UNICODE) . "\n";
    error_log($log_entry, 3, 'logs/trial-usage-reports.log');
    
    return $report;
}

// إعدادات الرسائل والتنبيهات
$TRIAL_MESSAGES = [
    'welcome' => 'مرحباً بك في التجربة المجانية لنظام PlayGood! لديك 3 ساعات كاملة لاستكشاف جميع المميزات.',
    'warning_30min' => 'تنبيه: يتبقى 30 دقيقة فقط من التجربة المجانية.',
    'warning_5min' => 'تنبيه نهائي: يتبقى 5 دقائق فقط من التجربة المجانية.',
    'expired' => 'انتهت صلاحية التجربة المجانية. شكراً لاستخدام PlayGood!',
    'upgrade' => 'أعجبك النظام؟ احصل على النسخة الكاملة مع جميع المميزات المتقدمة!'
];

// دالة الحصول على رسالة حسب الوقت المتبقي
function getTrialMessage($seconds_remaining) {
    global $TRIAL_MESSAGES;
    
    if ($seconds_remaining <= 0) {
        return $TRIAL_MESSAGES['expired'];
    } elseif ($seconds_remaining <= TRIAL_FINAL_WARNING_TIME) {
        return $TRIAL_MESSAGES['warning_5min'];
    } elseif ($seconds_remaining <= TRIAL_WARNING_TIME) {
        return $TRIAL_MESSAGES['warning_30min'];
    } else {
        return $TRIAL_MESSAGES['welcome'];
    }
}
?>
