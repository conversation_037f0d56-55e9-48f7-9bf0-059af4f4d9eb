<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تخصيص المظهر - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .help-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }
        .help-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .help-content {
            padding: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .color-demo {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            margin: 0 5px;
            border: 2px solid #dee2e6;
        }
        .step-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="help-container">
            <div class="help-header">
                <i class="fas fa-palette fa-3x mb-3"></i>
                <h1>دليل تخصيص المظهر</h1>
                <p class="mb-0">تعلم كيفية تخصيص ألوان وشكل موقعك</p>
            </div>
            
            <div class="help-content">
                <div class="feature-card">
                    <h3><i class="fas fa-paint-brush text-success me-2"></i>تخصيص الألوان</h3>
                    <p>يمكنك تخصيص ثلاثة ألوان رئيسية:</p>
                    <ul>
                        <li><strong>اللون الأساسي:</strong> يؤثر على الهيدر والأزرار الرئيسية</li>
                        <li><strong>اللون الثانوي:</strong> يؤثر على النصوص الثانوية والحدود</li>
                        <li><strong>اللون المميز:</strong> يؤثر على عناصر التمييز والتأكيدات</li>
                    </ul>
                    
                    <h5>الألوان المحددة مسبقاً:</h5>
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="color-demo" style="background: linear-gradient(45deg, #0d6efd, #20c997);" title="الأزرق الكلاسيكي"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #dc3545, #fd7e14);" title="الأحمر النشط"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #198754, #20c997);" title="الأخضر الطبيعي"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #6f42c1, #e83e8c);" title="البنفسجي الملكي"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #fd7e14, #ffc107);" title="البرتقالي المشرق"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #0dcaf0, #20c997);" title="الأزرق السماوي"></span>
                        <span class="color-demo" style="background: linear-gradient(45deg, #212529, #6c757d);" title="الرمادي الداكن"></span>
                        <small class="text-muted ms-3">اضغط على أي لون لتطبيقه فوراً</small>
                    </div>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-window-maximize text-success me-2"></i>أنماط الهيدر</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-arrow-up me-2"></i>هيدر علوي مثبت</h5>
                            <p>النمط التقليدي حيث تظهر القائمة في أعلى الصفحة</p>
                            <ul>
                                <li>مناسب للشاشات الصغيرة</li>
                                <li>يوفر مساحة أكبر للمحتوى</li>
                                <li>سهل الاستخدام</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-bars me-2"></i>قائمة جانبية</h5>
                            <p>قائمة تنقل جانبية يمكن وضعها يميناً أو يساراً</p>
                            <ul>
                                <li>مناسب للشاشات الكبيرة</li>
                                <li>وصول سريع للقوائم</li>
                                <li>مظهر عصري ومتقدم</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-list-ol text-success me-2"></i>خطوات التخصيص</h3>
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">1</span>
                        <div>
                            <strong>انتقل لصفحة الإعدادات</strong>
                            <p class="mb-0 text-muted">من القائمة الرئيسية، اختر "إعدادات المحل"</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">2</span>
                        <div>
                            <strong>اختر تبويب "تخصيص المظهر"</strong>
                            <p class="mb-0 text-muted">ستجد أيقونة الفرشاة بجانب النص</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">3</span>
                        <div>
                            <strong>اختر الألوان المفضلة</strong>
                            <p class="mb-0 text-muted">استخدم منتقي الألوان أو الألوان المحددة مسبقاً</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">4</span>
                        <div>
                            <strong>حدد نمط الهيدر</strong>
                            <p class="mb-0 text-muted">اختر بين الهيدر العلوي أو القائمة الجانبية</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">5</span>
                        <div>
                            <strong>احفظ الإعدادات</strong>
                            <p class="mb-0 text-muted">اضغط على "حفظ إعدادات المظهر" لتطبيق التغييرات</p>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-lightbulb text-success me-2"></i>نصائح مفيدة</h3>
                    <ul>
                        <li><strong>المعاينة الفورية:</strong> شاهد التغييرات قبل الحفظ في قسم المعاينة</li>
                        <li><strong>الألوان المتناسقة:</strong> اختر ألوان متناسقة لمظهر احترافي</li>
                        <li><strong>إعادة التعيين:</strong> يمكنك العودة للألوان الافتراضية في أي وقت</li>
                        <li><strong>الأجهزة المحمولة:</strong> القائمة الجانبية تتحول تلقائياً للهيدر العلوي على الهواتف</li>
                        <li><strong>الحفظ التلقائي:</strong> إعداداتك محفوظة ولن تضيع عند إعادة تسجيل الدخول</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="settings.php" class="btn btn-success btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>ابدأ التخصيص الآن
                    </a>
                    <a href="dashboard.php" class="btn btn-outline-secondary btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
