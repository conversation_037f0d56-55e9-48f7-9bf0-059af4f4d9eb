<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من وجود معرف الجلسة
if (!isset($_GET['session_id']) || !is_numeric($_GET['session_id'])) {
    echo json_encode(['success' => false, 'error' => 'معرف الجلسة مطلوب']);
    exit;
}

$session_id = $_GET['session_id'];

try {
    // التحقق من وجود عمود customer_id في جدول customers
    $customer_id_column = null;
    try {
        $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'customer_id'");
        if ($columns_check->rowCount() > 0) {
            $customer_id_column = 'customer_id';
        } else {
            $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'id'");
            if ($columns_check->rowCount() > 0) {
                $customer_id_column = 'id';
            }
        }
    } catch (PDOException $e) {
        // في حالة عدم وجود جدول customers
        $customer_id_column = null;
    }

    // جلب تفاصيل الجلسة
    if ($customer_id_column) {
        $stmt = $pdo->prepare("
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   c.name as customer_name,
                   c.phone as customer_phone,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
                   COALESCE(s.game_type, 'single') as game_type
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.$customer_id_column
            WHERE s.session_id = ? AND d.client_id = ? AND s.status = 'active'
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   NULL as customer_name,
                   NULL as customer_phone,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
                   COALESCE(s.game_type, 'single') as game_type
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            WHERE s.session_id = ? AND d.client_id = ? AND s.status = 'active'
        ");
    }
    
    $stmt->execute([$session_id, $client_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo json_encode(['success' => false, 'error' => 'الجلسة غير موجودة أو غير نشطة']);
        exit;
    }
    
    // تنسيق المدة
    $duration_minutes = $session['duration_minutes'] ?? 0;
    if ($duration_minutes > 0) {
        $hours = floor($duration_minutes / 60);
        $minutes = $duration_minutes % 60;
        $session['duration_display'] = sprintf("%02d:%02d", $hours, $minutes);
    } else {
        $session['duration_display'] = "00:00";
    }
    
    // تنسيق وقت البدء
    $session['start_time_formatted'] = date('Y-m-d H:i:s', strtotime($session['start_time']));
    
    echo json_encode([
        'success' => true,
        'session' => $session
    ]);

} catch (PDOException $e) {
    error_log("Get session details error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'حدث خطأ أثناء جلب بيانات الجلسة']);
}
?>
