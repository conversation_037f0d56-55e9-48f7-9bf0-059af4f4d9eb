<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_shifts') && !employeeHasPermission('assign_shifts')) {
    http_response_code(403);
    exit('لا توجد صلاحية');
}

$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$shift_id = $_GET['shift_id'] ?? 0;

if (!$shift_id) {
    http_response_code(400);
    exit('معرف الوردية مطلوب');
}

try {
    // جلب بيانات الوردية
    $shift_stmt = $pdo->prepare("SELECT * FROM shifts WHERE shift_id = ? AND client_id = ?");
    $shift_stmt->execute([$shift_id, $client_id]);
    $shift = $shift_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$shift) {
        http_response_code(404);
        exit('الوردية غير موجودة');
    }
    
    // جلب الموظفين المخصصين للوردية
    $assigned_stmt = $pdo->prepare("
        SELECT es.*, e.name, e.role, e.phone
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        WHERE es.shift_id = ?
        ORDER BY es.role_in_shift, e.name
    ");
    $assigned_stmt->execute([$shift_id]);
    $assigned_employees = $assigned_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب جميع الموظفين المتاحين
    $all_employees_stmt = $pdo->prepare("
        SELECT id, name, role, phone
        FROM employees 
        WHERE client_id = ? AND is_active = 1
        ORDER BY name
    ");
    $all_employees_stmt->execute([$client_id]);
    $all_employees = $all_employees_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إنشاء قائمة الموظفين غير المخصصين
    $assigned_ids = array_column($assigned_employees, 'employee_id');
    $available_employees = array_filter($all_employees, function($emp) use ($assigned_ids) {
        return !in_array($emp['id'], $assigned_ids);
    });
    
} catch (PDOException $e) {
    http_response_code(500);
    exit('خطأ في قاعدة البيانات');
}
?>

<div class="row">
    <div class="col-md-6">
        <h6>معلومات الوردية</h6>
        <div class="card mb-3">
            <div class="card-body">
                <h6 class="card-title"><?php echo htmlspecialchars($shift['shift_name']); ?></h6>
                <p class="card-text">
                    <strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($shift['shift_date'])); ?><br>
                    <strong>الوقت:</strong> <?php echo date('H:i', strtotime($shift['start_time'])); ?> - <?php echo date('H:i', strtotime($shift['end_time'])); ?><br>
                    <strong>عدد الموظفين:</strong> <?php echo $shift['min_employees']; ?> - <?php echo $shift['max_employees']; ?>
                </p>
            </div>
        </div>
        
        <h6>إضافة موظف جديد</h6>
        <form method="POST" action="shifts.php" class="mb-3">
            <input type="hidden" name="action" value="assign_employee">
            <input type="hidden" name="shift_id" value="<?php echo $shift_id; ?>">
            
            <div class="mb-3">
                <label class="form-label">اختر الموظف</label>
                <select name="employee_id" class="form-select" required>
                    <option value="">-- اختر موظف --</option>
                    <?php foreach ($available_employees as $emp): ?>
                        <option value="<?php echo $emp['id']; ?>">
                            <?php echo htmlspecialchars($emp['name']); ?> (<?php echo $emp['role']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">دور الموظف في الوردية</label>
                <select name="role_in_shift" class="form-select">
                    <option value="regular">عادي</option>
                    <option value="supervisor">مشرف</option>
                    <option value="backup">احتياطي</option>
                </select>
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_mandatory" id="is_mandatory">
                    <label class="form-check-label" for="is_mandatory">
                        حضور إجباري
                    </label>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> إضافة الموظف
            </button>
        </form>
    </div>
    
    <div class="col-md-6">
        <h6>الموظفين المخصصين (<?php echo count($assigned_employees); ?>)</h6>
        
        <?php if (empty($assigned_employees)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لم يتم تخصيص أي موظفين لهذه الوردية بعد
            </div>
        <?php else: ?>
            <div class="list-group">
                <?php foreach ($assigned_employees as $emp): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($emp['name']); ?></h6>
                                <p class="mb-1">
                                    <span class="badge bg-secondary"><?php echo $emp['role']; ?></span>
                                    <?php
                                    $role_badges = [
                                        'supervisor' => 'bg-primary',
                                        'regular' => 'bg-success',
                                        'backup' => 'bg-warning'
                                    ];
                                    $role_labels = [
                                        'supervisor' => 'مشرف',
                                        'regular' => 'عادي',
                                        'backup' => 'احتياطي'
                                    ];
                                    ?>
                                    <span class="badge <?php echo $role_badges[$emp['role_in_shift']] ?? 'bg-secondary'; ?>">
                                        <?php echo $role_labels[$emp['role_in_shift']] ?? $emp['role_in_shift']; ?>
                                    </span>
                                    <?php if ($emp['is_mandatory']): ?>
                                        <span class="badge bg-danger">إجباري</span>
                                    <?php endif; ?>
                                </p>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($emp['phone']); ?>
                                    <?php
                                    $status_labels = [
                                        'assigned' => 'مخصص',
                                        'confirmed' => 'مؤكد',
                                        'declined' => 'مرفوض',
                                        'cancelled' => 'ملغي'
                                    ];
                                    $status_classes = [
                                        'assigned' => 'text-warning',
                                        'confirmed' => 'text-success',
                                        'declined' => 'text-danger',
                                        'cancelled' => 'text-muted'
                                    ];
                                    ?>
                                    | <span class="<?php echo $status_classes[$emp['status']] ?? 'text-muted'; ?>">
                                        <?php echo $status_labels[$emp['status']] ?? $emp['status']; ?>
                                    </span>
                                </small>
                            </div>
                            <div>
                                <form method="POST" action="shifts.php" style="display: inline;">
                                    <input type="hidden" name="action" value="remove_assignment">
                                    <input type="hidden" name="assignment_id" value="<?php echo $emp['assignment_id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                            onclick="return confirm('هل تريد إزالة هذا الموظف من الوردية؟')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                        <?php if ($emp['notes']): ?>
                            <small class="text-muted d-block mt-1">
                                <i class="fas fa-sticky-note"></i> <?php echo htmlspecialchars($emp['notes']); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- إحصائيات سريعة -->
        <div class="mt-3">
            <div class="row text-center">
                <div class="col-4">
                    <div class="border rounded p-2">
                        <div class="h6 mb-0"><?php echo count($assigned_employees); ?></div>
                        <small class="text-muted">مخصص</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="border rounded p-2">
                        <div class="h6 mb-0"><?php echo count(array_filter($assigned_employees, function($e) { return $e['status'] == 'confirmed'; })); ?></div>
                        <small class="text-muted">مؤكد</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="border rounded p-2">
                        <div class="h6 mb-0"><?php echo count(array_filter($assigned_employees, function($e) { return $e['is_mandatory']; })); ?></div>
                        <small class="text-muted">إجباري</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
