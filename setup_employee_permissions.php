<?php
/**
 * إعداد نظام صلاحيات الموظفين المتقدم - PlayGood
 * تشغيل هذا الملف لإنشاء نظام صلاحيات مخصص للموظفين
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد نظام صلاحيات الموظفين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card shadow'>
        <div class='card-header bg-primary text-white'>
            <h3 class='mb-0'><i class='fas fa-shield-alt me-2'></i>إعداد نظام صلاحيات الموظفين المتقدم</h3>
        </div>
        <div class='card-body'>";

try {
    require_once 'config/database.php';
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            بدء عملية إنشاء نظام الصلاحيات المتقدم...
          </div>";
    
    $setup_steps = 0;
    $errors = [];
    
    // قراءة وتنفيذ ملف SQL
    $sql_file = 'create_employee_permissions_system.sql';
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        $sql_statements = explode(';', $sql_content);
        
        echo "<h5>تنفيذ إعدادات قاعدة البيانات</h5>";
        
        foreach ($sql_statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                    $setup_steps++;
                } catch (PDOException $e) {
                    if (!strpos($e->getMessage(), 'already exists') && 
                        !strpos($e->getMessage(), 'Duplicate entry')) {
                        $errors[] = "خطأ في تنفيذ: " . $e->getMessage();
                    }
                }
            }
        }
        
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم تنفيذ {$setup_steps} عملية بنجاح</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>ملف SQL غير موجود</p>";
    }
    
    // التحقق من إنشاء الجداول
    echo "<h5>التحقق من الجداول المنشأة</h5>";
    $tables_to_check = ['permissions', 'pages', 'employee_permissions', 'employee_pages'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='success'><i class='fas fa-check me-2'></i>جدول {$table}: {$count} سجل</p>";
        } catch (PDOException $e) {
            echo "<p class='error'><i class='fas fa-times me-2'></i>جدول {$table}: غير موجود</p>";
            $errors[] = "جدول {$table} غير موجود";
        }
    }
    
    // إحصائيات النظام
    echo "<h5>إحصائيات النظام</h5>";
    try {
        // عدد الصلاحيات
        $permissions_count = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
        echo "<p class='info'><i class='fas fa-shield-alt me-2'></i>عدد الصلاحيات المتاحة: {$permissions_count}</p>";
        
        // عدد الصفحات
        $pages_count = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
        echo "<p class='info'><i class='fas fa-file me-2'></i>عدد الصفحات المتاحة: {$pages_count}</p>";
        
        // عدد الموظفين
        $employees_count = $pdo->query("SELECT COUNT(*) FROM employees")->fetchColumn();
        echo "<p class='info'><i class='fas fa-users me-2'></i>عدد الموظفين: {$employees_count}</p>";
        
        // عرض الصلاحيات حسب الفئة
        echo "<h6>الصلاحيات حسب الفئة:</h6>";
        $categories = $pdo->query("
            SELECT category, COUNT(*) as count 
            FROM permissions 
            GROUP BY category 
            ORDER BY category
        ")->fetchAll();
        
        echo "<ul class='list-group list-group-flush'>";
        foreach ($categories as $category) {
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>
                    {$category['category']}
                    <span class='badge bg-primary rounded-pill'>{$category['count']}</span>
                  </li>";
        }
        echo "</ul>";
        
        // عرض الصفحات حسب الفئة
        echo "<h6 class='mt-3'>الصفحات حسب الفئة:</h6>";
        $page_categories = $pdo->query("
            SELECT category, COUNT(*) as count 
            FROM pages 
            GROUP BY category 
            ORDER BY category
        ")->fetchAll();
        
        echo "<ul class='list-group list-group-flush'>";
        foreach ($page_categories as $category) {
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>
                    {$category['category']}
                    <span class='badge bg-success rounded-pill'>{$category['count']}</span>
                  </li>";
        }
        echo "</ul>";
        
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
    }
    
    // النتيجة النهائية
    echo "<div class='alert alert-" . (empty($errors) ? 'success' : 'warning') . " mt-4'>
            <h5><i class='fas fa-" . (empty($errors) ? 'check-circle' : 'exclamation-triangle') . " me-2'></i>النتيجة النهائية</h5>
            <p>تم إعداد نظام الصلاحيات بنجاح!</p>";
    
    if (!empty($errors)) {
        echo "<p>عدد الأخطاء: " . count($errors) . "</p>";
        echo "<details><summary>تفاصيل الأخطاء</summary><ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul></details>";
    }
    
    echo "</div>";
    
    if (empty($errors)) {
        echo "<div class='alert alert-info'>
                <h6><i class='fas fa-info-circle me-2'></i>الخطوات التالية:</h6>
                <ol>
                    <li>اذهب إلى <a href='client/employees.php' class='alert-link'>صفحة إدارة الموظفين</a></li>
                    <li>ستجد خيارات جديدة لإدارة الصلاحيات</li>
                    <li>يمكنك تحديد صلاحيات مخصصة لكل موظف</li>
                    <li>يمكنك تحديد الصفحات المسموح بالوصول إليها</li>
                </ol>
              </div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-circle me-2'></i>
            خطأ عام: " . htmlspecialchars($e->getMessage()) . "
          </div>";
}

echo "        </div>
    </div>
</div>
</body>
</html>";
?>
