<?php
/**
 * اختبار API التكلفة - PlayGood
 * فحص مباشر لـ API الذي يحدث التكلفة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 اختبار API التكلفة</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص الجلسات النشطة
    echo "<h2>1. فحص الجلسات النشطة</h2>";
    
    $sessions = $pdo->query("
        SELECT s.session_id, s.start_time, d.device_name, c.name as customer_name
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN customers c ON s.customer_id = c.customer_id
        WHERE s.status = 'active' AND d.client_id = 1
        ORDER BY s.session_id DESC
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>عدد الجلسات النشطة: <strong>" . count($sessions) . "</strong></p>";
    
    if (count($sessions) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>ID الجلسة</th>";
        echo "<th style='padding: 8px;'>اسم الجهاز</th>";
        echo "<th style='padding: 8px;'>اسم العميل</th>";
        echo "<th style='padding: 8px;'>اختبار API</th>";
        echo "</tr>";
        
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $session['session_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($session['device_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<button onclick='testSessionAPI(" . $session['session_id'] . ")' style='background: #007bff; color: white; padding: 5px 10px; border: none; border-radius: 3px;'>اختبار</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختبار الجلسة الأولى تلقائياً
        $first_session = $sessions[0];
        echo "<h3>اختبار تلقائي للجلسة رقم: " . $first_session['session_id'] . "</h3>";
        
        // محاكاة API add_session_product
        echo "<h4>أ. محاكاة API add_session_product:</h4>";
        
        try {
            // نفس الكود المستخدم في add_session_product.php
            $stmt = $pdo->prepare("
                SELECT 
                    s.session_id,
                    s.start_time,
                    d.single_rate,
                    d.hourly_rate,
                    COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost,
                    TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                LEFT JOIN session_products sp ON s.session_id = sp.session_id
                WHERE s.session_id = ?
                GROUP BY s.session_id, s.start_time, d.single_rate, d.hourly_rate
            ");
            $stmt->execute([$first_session['session_id']]);
            $session_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session_data) {
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h5>📊 بيانات الجلسة من API:</h5>";
                
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th style='padding: 8px;'>المتغير</th>";
                echo "<th style='padding: 8px;'>القيمة</th>";
                echo "</tr>";
                
                $vars = [
                    'session_id' => $session_data['session_id'],
                    'duration_minutes' => $session_data['duration_minutes'],
                    'single_rate' => $session_data['single_rate'],
                    'hourly_rate' => $session_data['hourly_rate'],
                    'products_cost' => $session_data['products_cost']
                ];
                
                foreach ($vars as $var_name => $var_value) {
                    echo "<tr>";
                    echo "<td style='padding: 8px; font-weight: bold;'>$var_name</td>";
                    echo "<td style='padding: 8px; color: blue;'>" . ($var_value ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // حساب التكلفة
                $hourly_rate = $session_data['single_rate'] ?? $session_data['hourly_rate'] ?? 0;
                $time_cost = $session_data['duration_minutes'] > 0 && $hourly_rate > 0 ? ceil($session_data['duration_minutes'] / 60) * $hourly_rate : 0;
                $total_cost = $time_cost + $session_data['products_cost'];
                
                echo "<h5>🧮 حساب التكلفة:</h5>";
                echo "<p><strong>السعر المستخدم:</strong> $hourly_rate ج.م/ساعة</p>";
                echo "<p><strong>تكلفة الوقت:</strong> $time_cost ج.م</p>";
                echo "<p><strong>تكلفة المنتجات:</strong> " . $session_data['products_cost'] . " ج.م</p>";
                echo "<p><strong>التكلفة الإجمالية:</strong> <span style='background: yellow; padding: 5px; font-weight: bold;'>$total_cost ج.م</span></p>";
                
                // محاكاة JSON response
                $api_response = [
                    'success' => true,
                    'total_cost' => number_format($total_cost, 2),
                    'time_cost' => number_format($time_cost, 2),
                    'products_cost' => number_format($session_data['products_cost'], 2)
                ];
                
                echo "<h5>📤 استجابة API المتوقعة:</h5>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; direction: ltr;'>";
                echo json_encode($api_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo "</pre>";
                
                echo "</div>";
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على بيانات الجلسة</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في محاكاة API: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
    }
    
    // 2. اختبار مباشر لـ API
    echo "<h2>2. اختبار مباشر لـ API</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>🔧 اختبار تفاعلي:</h4>";
    echo "<p>استخدم الأزرار أدناه لاختبار API مباشرة:</p>";
    
    if (count($sessions) > 0) {
        foreach ($sessions as $session) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h5>جلسة رقم: " . $session['session_id'] . "</h5>";
            echo "<p><strong>الجهاز:</strong> " . htmlspecialchars($session['device_name']) . "</p>";
            echo "<p><strong>العميل:</strong> " . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</p>";
            echo "<button onclick='testAddProduct(" . $session['session_id'] . ")' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>اختبار إضافة منتج</button>";
            echo "<button onclick='testDeleteProduct(" . $session['session_id'] . ")' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>اختبار حذف منتج</button>";
            echo "<div id='result_" . $session['session_id'] . "' style='margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; display: none;'></div>";
            echo "</div>";
        }
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/sessions.php?debug=1' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات + تشخيص</a>";
echo "<a href='debug_cost_direct.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>التشخيص المباشر</a>";
echo "</div>";

echo "</div>";
?>

<script>
function testSessionAPI(sessionId) {
    const resultDiv = document.getElementById('result_' + sessionId);
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</p>';
    
    // محاكاة استدعاء API
    fetch(`client/api/add_session_product.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId,
            product_id: 1, // منتج تجريبي
            quantity: 1
        })
    })
    .then(response => response.text())
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            
            if (data.success) {
                resultDiv.innerHTML = `
                    <h6 style="color: green;">✅ نجح الاختبار</h6>
                    <p><strong>التكلفة الإجمالية:</strong> ${data.total_cost} ج.م</p>
                    <p><strong>تكلفة الوقت:</strong> ${data.time_cost} ج.م</p>
                    <p><strong>تكلفة المنتجات:</strong> ${data.products_cost} ج.م</p>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; direction: ltr; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h6 style="color: red;">❌ فشل الاختبار</h6>
                    <p><strong>الخطأ:</strong> ${data.error}</p>
                    <pre style="background: #f8d7da; padding: 10px; border-radius: 4px; direction: ltr; font-size: 12px;">${text}</pre>
                `;
            }
        } catch (error) {
            resultDiv.innerHTML = `
                <h6 style="color: red;">❌ خطأ في تحليل JSON</h6>
                <p><strong>الخطأ:</strong> ${error.message}</p>
                <pre style="background: #f8d7da; padding: 10px; border-radius: 4px; direction: ltr; font-size: 12px;">${text}</pre>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <h6 style="color: red;">❌ خطأ في الطلب</h6>
            <p><strong>الخطأ:</strong> ${error.message}</p>
        `;
    });
}

function testAddProduct(sessionId) {
    testSessionAPI(sessionId);
}

function testDeleteProduct(sessionId) {
    const resultDiv = document.getElementById('result_' + sessionId);
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري اختبار الحذف...</p>';
    
    fetch(`client/api/delete_session_product.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId,
            product_id: 1
        })
    })
    .then(response => response.text())
    .then(text => {
        try {
            const data = JSON.parse(text);
            
            if (data.success) {
                resultDiv.innerHTML = `
                    <h6 style="color: green;">✅ نجح اختبار الحذف</h6>
                    <p><strong>التكلفة الإجمالية:</strong> ${data.total_cost} ج.م</p>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; direction: ltr; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h6 style="color: red;">❌ فشل اختبار الحذف</h6>
                    <p><strong>الخطأ:</strong> ${data.error}</p>
                `;
            }
        } catch (error) {
            resultDiv.innerHTML = `
                <h6 style="color: red;">❌ خطأ في تحليل JSON</h6>
                <p><strong>الخطأ:</strong> ${error.message}</p>
                <pre style="background: #f8d7da; padding: 10px; border-radius: 4px; direction: ltr; font-size: 12px;">${text}</pre>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <h6 style="color: red;">❌ خطأ في الطلب</h6>
            <p><strong>الخطأ:</strong> ${error.message}</p>
        `;
    });
}
</script>
