<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';
require_once '../includes/format_helpers.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الماليات
    if (!hasPagePermission('finances')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الماليات';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('finances')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('view_finances') && !employeeHasPermission('manage_finances')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "الماليات";
$active_page = "finances";

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$success_message = '';
$error_message = '';

// التحقق من وجود الجداول المطلوبة
try {
    $pdo->query("SELECT 1 FROM expense_types LIMIT 1");
    $pdo->query("SELECT 1 FROM expenses LIMIT 1");
    $pdo->query("SELECT 1 FROM income_types LIMIT 1");
    $pdo->query("SELECT 1 FROM additional_income LIMIT 1");
    $tables_exist = true;
} catch (PDOException $e) {
    $tables_exist = false;
}

if (!$tables_exist) {
    $_SESSION['error'] = "جداول الماليات غير موجودة. يرجى تشغيل سكريپت إنشاء الجداول أولاً.";
}

// معالجة إضافة مصروف جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'add_expense') {
    try {
        $expense_type_id = $_POST['expense_type_id'];
        $amount = $_POST['amount'];
        $description = $_POST['description'] ?? '';
        $expense_date = $_POST['expense_date'];
        $receipt_number = $_POST['receipt_number'] ?? '';

        // التحقق من صحة المبلغ وتنظيفه
        $amount = trim($amount);
        if (!is_numeric($amount) || $amount <= 0) {
            throw new Exception("المبلغ يجب أن يكون رقماً صحيحاً أكبر من صفر");
        }
        $amount = floatval($amount);

        $stmt = $pdo->prepare("
            INSERT INTO expenses (client_id, expense_type_id, amount, description, expense_date, receipt_number, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$client_id, $expense_type_id, $amount, $description, $expense_date, $receipt_number, $client_id]);

        $success_message = "تم إضافة المصروف بنجاح";
    } catch (Exception $e) {
        $error_message = "حدث خطأ في إضافة المصروف: " . $e->getMessage();
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في إضافة المصروف: " . $e->getMessage();
    }
}

// معالجة إضافة نوع مصروف جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'add_expense_type') {
    try {
        $name = $_POST['name'];
        $description = $_POST['description'] ?? '';

        $stmt = $pdo->prepare("
            INSERT INTO expense_types (client_id, name, description)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$client_id, $name, $description]);

        $success_message = "تم إضافة نوع المصروف بنجاح";
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في إضافة نوع المصروف: " . $e->getMessage();
    }
}

// معالجة إضافة إيراد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'add_income') {
    try {
        $income_type_id = $_POST['income_type_id'];
        $amount = $_POST['amount'];
        $description = $_POST['description'] ?? '';
        $income_date = $_POST['income_date'];
        $receipt_number = $_POST['receipt_number'] ?? '';

        // التحقق من صحة المبلغ وتنظيفه
        $amount = trim($amount);
        if (!is_numeric($amount) || $amount <= 0) {
            throw new Exception("المبلغ يجب أن يكون رقماً صحيحاً أكبر من صفر");
        }
        $amount = floatval($amount);

        $stmt = $pdo->prepare("
            INSERT INTO additional_income (client_id, income_type_id, amount, description, income_date, receipt_number, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$client_id, $income_type_id, $amount, $description, $income_date, $receipt_number, $client_id]);

        $success_message = "تم إضافة الإيراد بنجاح";
    } catch (Exception $e) {
        $error_message = "حدث خطأ في إضافة الإيراد: " . $e->getMessage();
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في إضافة الإيراد: " . $e->getMessage();
    }
}

// معالجة إضافة نوع إيراد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'add_income_type') {
    try {
        $name = $_POST['name'];
        $description = $_POST['description'] ?? '';

        $stmt = $pdo->prepare("
            INSERT INTO income_types (client_id, name, description)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$client_id, $name, $description]);

        $success_message = "تم إضافة نوع الإيراد بنجاح";
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في إضافة نوع الإيراد: " . $e->getMessage();
    }
}

// جلب البيانات للعرض
$expense_types = [];
$income_types = [];
$recent_expenses = [];
$recent_income = [];
$financial_summary = [
    'total_expenses' => 0,
    'total_additional_income' => 0,
    'total_sessions_income' => 0,
    'net_profit' => 0
];

if ($tables_exist) {
    try {
        // جلب أنواع المصروفات
        $stmt = $pdo->prepare("SELECT * FROM expense_types WHERE client_id = ? AND is_active = 1 ORDER BY name");
        $stmt->execute([$client_id]);
        $expense_types = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب أنواع الإيرادات
        $stmt = $pdo->prepare("SELECT * FROM income_types WHERE client_id = ? AND is_active = 1 ORDER BY name");
        $stmt->execute([$client_id]);
        $income_types = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب المصروفات الحديثة مع إصلاح البيانات الخاطئة
        $stmt = $pdo->prepare("
            SELECT
                e.id,
                e.client_id,
                e.expense_type_id,
                CASE
                    WHEN e.amount REGEXP '^[0-9]+\.?[0-9]*$' THEN e.amount
                    ELSE '0.00'
                END as amount,
                e.description,
                e.expense_date,
                e.receipt_number,
                e.created_at,
                COALESCE(et.name, 'نوع غير محدد') as expense_type_name
            FROM expenses e
            LEFT JOIN expense_types et ON e.expense_type_id = et.id AND et.client_id = e.client_id
            WHERE e.client_id = ?
            ORDER BY e.expense_date DESC, e.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$client_id]);
        $recent_expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب الإيرادات الحديثة مع إصلاح البيانات الخاطئة
        $stmt = $pdo->prepare("
            SELECT
                ai.id,
                ai.client_id,
                ai.income_type_id,
                CASE
                    WHEN ai.amount REGEXP '^[0-9]+\.?[0-9]*$' THEN ai.amount
                    ELSE '0.00'
                END as amount,
                ai.description,
                ai.income_date,
                ai.receipt_number,
                ai.created_at,
                COALESCE(it.name, 'نوع غير محدد') as income_type_name
            FROM additional_income ai
            LEFT JOIN income_types it ON ai.income_type_id = it.id AND it.client_id = ai.client_id
            WHERE ai.client_id = ?
            ORDER BY ai.income_date DESC, ai.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$client_id]);
        $recent_income = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب الملخص المالي للشهر الحالي
        $current_month = date('Y-m');

        // إجمالي المصروفات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM expenses
            WHERE client_id = ? AND DATE_FORMAT(expense_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $financial_summary['total_expenses'] = $stmt->fetchColumn();

        // إجمالي الإيرادات الإضافية
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_additional_income
            FROM additional_income
            WHERE client_id = ? AND DATE_FORMAT(income_date, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $financial_summary['total_additional_income'] = $stmt->fetchColumn();

        // إجمالي إيرادات الجلسات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(s.total_cost), 0) as total_sessions_income
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ?
            AND s.status = 'completed'
            AND DATE_FORMAT(s.start_time, '%Y-%m') = ?
        ");
        $stmt->execute([$client_id, $current_month]);
        $financial_summary['total_sessions_income'] = $stmt->fetchColumn();

        // حساب صافي الربح
        $total_income = $financial_summary['total_sessions_income'] + $financial_summary['total_additional_income'];
        $financial_summary['net_profit'] = $total_income - $financial_summary['total_expenses'];

    } catch (PDOException $e) {
        $error_message = "حدث خطأ في جلب البيانات: " . $e->getMessage();
    }
}

require_once 'includes/header.php';
?>

<style>
.finance-card {
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.finance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
}

.expense-card {
    border-left-color: #dc3545;
}

.income-card {
    border-left-color: #28a745;
}

.profit-card {
    border-left-color: #17a2b8;
}

.table-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.modal-header.bg-danger {
    color: white;
}

.modal-header.bg-success {
    color: white;
}
</style>

<div class="container-fluid py-4">
    <!-- العنوان والأزرار -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
        <h1 class="h2 mb-0">
            <i class="fas fa-money-bill-wave me-2 text-primary"></i>إدارة الماليات
        </h1>
        <div class="btn-toolbar">
            <?php if (!$tables_exist): ?>
                <a href="../create_finances_tables.php" class="btn btn-warning me-2">
                    <i class="fas fa-database me-1"></i>إنشاء جداول الماليات
                </a>
            <?php endif; ?>
            <a href="reports.php" class="btn btn-info me-2">
                <i class="fas fa-chart-bar me-1"></i>التقارير
            </a>
            <a href="dashboard.php" class="btn btn-primary">
                <i class="fas fa-home me-1"></i>لوحة التحكم
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!$tables_exist): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>جداول الماليات غير موجودة</h5>
            <p>يجب إنشاء جداول الماليات أولاً لتتمكن من استخدام هذه الصفحة.</p>
            <a href="../create_finances_tables.php" class="btn btn-warning">
                <i class="fas fa-database me-1"></i>إنشاء الجداول الآن
            </a>
        </div>
    <?php else: ?>

    <!-- الملخص المالي -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card finance-card expense-card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-minus-circle fa-3x text-danger"></i>
                    </div>
                    <h6 class="card-title text-danger">إجمالي المصروفات</h6>
                    <div class="stats-number text-danger"><?php echo formatAmount($financial_summary['total_expenses']); ?></div>
                    <small class="text-muted">جنيه مصري - <?php echo date('F Y'); ?></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card finance-card income-card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-plus-circle fa-3x text-success"></i>
                    </div>
                    <h6 class="card-title text-success">إيرادات الجلسات</h6>
                    <div class="stats-number text-success"><?php echo formatAmount($financial_summary['total_sessions_income']); ?></div>
                    <small class="text-muted">جنيه مصري - <?php echo date('F Y'); ?></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card finance-card income-card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-hand-holding-usd fa-3x text-success"></i>
                    </div>
                    <h6 class="card-title text-success">إيرادات إضافية</h6>
                    <div class="stats-number text-success"><?php echo formatAmount($financial_summary['total_additional_income']); ?></div>
                    <small class="text-muted">جنيه مصري - <?php echo date('F Y'); ?></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card finance-card profit-card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-info"></i>
                    </div>
                    <h6 class="card-title text-info">صافي الربح</h6>
                    <div class="stats-number <?php echo $financial_summary['net_profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                        <?php echo formatAmount($financial_summary['net_profit']); ?>
                    </div>
                    <small class="text-muted">جنيه مصري - <?php echo date('F Y'); ?></small>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإضافة السريعة -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <button class="btn btn-danger w-100" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                <i class="fas fa-minus-circle me-2"></i>إضافة مصروف
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#addIncomeModal">
                <i class="fas fa-plus-circle me-2"></i>إضافة إيراد
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-outline-danger w-100" data-bs-toggle="modal" data-bs-target="#addExpenseTypeModal">
                <i class="fas fa-tags me-2"></i>نوع مصروف جديد
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-outline-success w-100" data-bs-toggle="modal" data-bs-target="#addIncomeTypeModal">
                <i class="fas fa-tags me-2"></i>نوع إيراد جديد
            </button>
        </div>
    </div>

    <!-- المصروفات والإيرادات الحديثة -->
    <div class="row g-3 mb-4">
        <!-- المصروفات الحديثة -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-minus-circle me-2"></i>المصروفات الحديثة</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_expenses)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_expenses as $expense): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <?php echo htmlspecialchars($expense['expense_type_name']); ?>
                                                </span>
                                            </td>
                                            <td class="text-danger fw-bold">
                                                <?php echo formatAmount($expense['amount']); ?> ج.م
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($expense['expense_date'])); ?></td>
                                            <td>
                                                <small><?php echo htmlspecialchars($expense['description'] ?: 'لا يوجد وصف'); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-minus-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مصروفات مسجلة</p>
                            <button class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                                <i class="fas fa-plus me-1"></i>إضافة مصروف
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- الإيرادات الحديثة -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-plus-circle me-2"></i>الإيرادات الإضافية الحديثة</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_income)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_income as $income): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo htmlspecialchars($income['income_type_name']); ?>
                                                </span>
                                            </td>
                                            <td class="text-success fw-bold">
                                                <?php echo formatAmount($income['amount']); ?> ج.م
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($income['income_date'])); ?></td>
                                            <td>
                                                <small><?php echo htmlspecialchars($income['description'] ?: 'لا يوجد وصف'); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد إيرادات إضافية مسجلة</p>
                            <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addIncomeModal">
                                <i class="fas fa-plus me-1"></i>إضافة إيراد
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php endif; // end if tables_exist ?>
</div>

<!-- نموذج إضافة مصروف -->
<div class="modal fade" id="addExpenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-minus-circle me-2"></i>إضافة مصروف جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add_expense">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="expense_type_id" class="form-label">نوع المصروف</label>
                        <select class="form-select" id="expense_type_id" name="expense_type_id" required>
                            <option value="">اختر نوع المصروف</option>
                            <?php foreach ($expense_types as $type): ?>
                                <option value="<?php echo $type['id']; ?>">
                                    <?php echo htmlspecialchars($type['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="expense_amount" class="form-label">المبلغ</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="expense_amount" name="amount"
                                   step="0.01" min="0" required>
                            <span class="input-group-text">ج.م</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="expense_date" class="form-label">تاريخ المصروف</label>
                        <input type="date" class="form-control" id="expense_date" name="expense_date"
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="expense_receipt_number" class="form-label">رقم الإيصال (اختياري)</label>
                        <input type="text" class="form-control" id="expense_receipt_number" name="receipt_number">
                    </div>
                    <div class="mb-3">
                        <label for="expense_description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" id="expense_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-save me-1"></i>حفظ المصروف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج إضافة إيراد -->
<div class="modal fade" id="addIncomeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus-circle me-2"></i>إضافة إيراد جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add_income">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="income_type_id" class="form-label">نوع الإيراد</label>
                        <select class="form-select" id="income_type_id" name="income_type_id" required>
                            <option value="">اختر نوع الإيراد</option>
                            <?php foreach ($income_types as $type): ?>
                                <option value="<?php echo $type['id']; ?>">
                                    <?php echo htmlspecialchars($type['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="income_amount" class="form-label">المبلغ</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="income_amount" name="amount"
                                   step="0.01" min="0" required>
                            <span class="input-group-text">ج.م</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="income_date" class="form-label">تاريخ الإيراد</label>
                        <input type="date" class="form-control" id="income_date" name="income_date"
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="income_receipt_number" class="form-label">رقم الإيصال (اختياري)</label>
                        <input type="text" class="form-control" id="income_receipt_number" name="receipt_number">
                    </div>
                    <div class="mb-3">
                        <label for="income_description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" id="income_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>حفظ الإيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج إضافة نوع مصروف -->
<div class="modal fade" id="addExpenseTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-tags me-2"></i>إضافة نوع مصروف جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add_expense_type">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="expense_type_name" class="form-label">اسم نوع المصروف</label>
                        <input type="text" class="form-control" id="expense_type_name" name="name"
                               placeholder="مثال: فواتير الكهرباء" required>
                    </div>
                    <div class="mb-3">
                        <label for="expense_type_description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" id="expense_type_description" name="description"
                                  rows="3" placeholder="وصف تفصيلي لنوع المصروف"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-save me-1"></i>حفظ نوع المصروف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج إضافة نوع إيراد -->
<div class="modal fade" id="addIncomeTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-tags me-2"></i>إضافة نوع إيراد جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add_income_type">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="income_type_name" class="form-label">اسم نوع الإيراد</label>
                        <input type="text" class="form-control" id="income_type_name" name="name"
                               placeholder="مثال: خدمات إضافية" required>
                    </div>
                    <div class="mb-3">
                        <label for="income_type_description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" id="income_type_description" name="description"
                                  rows="3" placeholder="وصف تفصيلي لنوع الإيراد"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>حفظ نوع الإيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديث الصفحة بعد إضافة عنصر جديد
document.addEventListener('DOMContentLoaded', function() {
    // إغلاق النماذج بعد الإرسال الناجح
    <?php if ($success_message): ?>
        // إغلاق جميع النماذج المفتوحة
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    <?php endif; ?>

    // إضافة تأثيرات تفاعلية للبطاقات
    const cards = document.querySelectorAll('.finance-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث تلقائي للبيانات كل 5 دقائق
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 300000); // 5 دقائق
});

// وظيفة لتنسيق الأرقام
function formatNumber(num) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(num);
}
</script>

<?php require_once 'includes/footer.php'; ?>