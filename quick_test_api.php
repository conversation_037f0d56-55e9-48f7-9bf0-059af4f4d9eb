<?php
// اختبار سريع لـ API المنتجات
session_start();
$_SESSION['client_id'] = 1; // محاكاة تسجيل الدخول

// استدعاء API مباشرة
$api_url = 'http://localhost/playgood/client/api/get_products.php?page=1';

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . session_name() . '=' . session_id()
    ]
]);

$response = file_get_contents($api_url, false, $context);
$data = json_decode($response, true);

echo "<h2>اختبار API المنتجات</h2>";
echo "<h3>الاستجابة الخام:</h3>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<h3>البيانات المحللة:</h3>";
if ($data && $data['success']) {
    $products = $data['products'] ?? $data['data']['products'] ?? [];
    echo "<p>عدد المنتجات: " . count($products) . "</p>";
    
    if (count($products) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الاسم</th><th>السعر (رقم)</th><th>السعر المنسق</th><th>نوع البيانات</th></tr>";
        
        foreach (array_slice($products, 0, 5) as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . $product['price'] . "</td>";
            echo "<td>" . ($product['price_formatted'] ?? 'غير متوفر') . "</td>";
            echo "<td>" . gettype($product['price']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختبار JavaScript
        echo "<h3>اختبار JavaScript:</h3>";
        echo "<div id='js-test'></div>";
        echo "<script>
            const products = " . json_encode(array_slice($products, 0, 3)) . ";
            let html = '<ul>';
            products.forEach(product => {
                const priceType = typeof product.price;
                const isNumber = priceType === 'number';
                html += `<li>\${product.name}: \${product.price} (\${priceType}) \${isNumber ? '✅' : '❌'}</li>`;
            });
            html += '</ul>';
            document.getElementById('js-test').innerHTML = html;
        </script>";
    }
} else {
    echo "<p style='color: red;'>خطأ: " . ($data['error'] ?? 'استجابة غير صحيحة') . "</p>";
}
?>
