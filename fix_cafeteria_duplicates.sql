-- إصلاح تكرار المنتجات في جدول cafeteria_items
-- هذا الملف يضيف قيد فريد لمنع تكرار المنتجات بنفس الاسم والتصنيف للعميل الواحد

-- أولاً: حذف المنتجات المكررة (الاحتفاظ بأول منتج فقط)
DELETE c1 FROM cafeteria_items c1
INNER JOIN cafeteria_items c2 
WHERE c1.id > c2.id 
  AND c1.name = c2.name 
  AND c1.category = c2.category 
  AND c1.client_id = c2.client_id;

-- ثانياً: إضافة قيد فريد لمنع التكرار في المستقبل
-- التحقق من وجود الفهرس أولاً وحذفه إذا كان موجوداً
DROP INDEX IF EXISTS unique_product_per_client ON cafeteria_items;

-- إضافة الفهرس الفريد الجديد
ALTER TABLE cafeteria_items 
ADD UNIQUE INDEX unique_product_per_client (name, category, client_id);

-- إضافة فهرس للبحث السريع
ALTER TABLE cafeteria_items 
ADD INDEX IF NOT EXISTS idx_cafeteria_search (name, category, client_id);

-- التحقق من النتائج
SELECT 
    'إجمالي المنتجات' as النوع,
    COUNT(*) as العدد
FROM cafeteria_items
UNION ALL
SELECT 
    'المنتجات المكررة المتبقية' as النوع,
    COUNT(*) as العدد
FROM (
    SELECT name, category, client_id, COUNT(*) as count
    FROM cafeteria_items 
    GROUP BY name, category, client_id 
    HAVING COUNT(*) > 1
) as duplicates;
