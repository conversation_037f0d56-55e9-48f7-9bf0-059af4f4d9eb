<?php
class DatabaseSecurity {

    private $pdo;
    private $encryption_key;
    private $cipher_method = 'AES-256-CBC';

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->encryption_key = $this->getEncryptionKey();
        $this->setupSecurityTables();
    }

    private function getEncryptionKey() {
        $key_file = __DIR__ . '/../config/.encryption_key';

        if (!file_exists($key_file)) {
            $key = random_bytes(32);
            file_put_contents($key_file, base64_encode($key));
            chmod($key_file, 0600);
        } else {
            $key = base64_decode(file_get_contents($key_file));
        }

        return $key;
    }

    private function setupSecurityTables() {
        try {
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS sql_injection_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    user_id INT NULL,
                    query_attempted TEXT NOT NULL,
                    parameters JSON NULL,
                    detected_patterns TEXT,
                    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                    blocked BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ip_time (ip_address, created_at),
                    INDEX idx_severity (severity)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS encrypted_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    table_name VARCHAR(100) NOT NULL,
                    record_id INT NOT NULL,
                    field_name VARCHAR(100) NOT NULL,
                    encrypted_value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_record_field (table_name, record_id, field_name),
                    INDEX idx_table_record (table_name, record_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

        } catch (PDOException $e) {
            error_log("Security tables setup failed: " . $e->getMessage());
        }
    }

    public function encrypt($data) {
        if (empty($data)) return $data;

        $iv = random_bytes(openssl_cipher_iv_length($this->cipher_method));
        $encrypted = openssl_encrypt($data, $this->cipher_method, $this->encryption_key, 0, $iv);

        return base64_encode($iv . $encrypted);
    }

    public function decrypt($encrypted_data) {
        if (empty($encrypted_data)) return $encrypted_data;

        $data = base64_decode($encrypted_data);
        $iv_length = openssl_cipher_iv_length($this->cipher_method);
        $iv = substr($data, 0, $iv_length);
        $encrypted = substr($data, $iv_length);

        return openssl_decrypt($encrypted, $this->cipher_method, $this->encryption_key, 0, $iv);
    }

    public function detectSqlInjection($query, $parameters = []) {
        $suspicious_patterns = [
            '/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b/i',
            '/(\/\*|\*\/|--|#)/',
            '/(\bOR\b|\bAND\b).*[\'"].*[\'"].*(\bOR\b|\bAND\b)/i',
            '/\b(CONCAT|CHAR|ASCII|SUBSTRING|LENGTH|USER|DATABASE|VERSION)\s*\(/i',
            '/0x[0-9a-f]+/i',
            '/[\'"].*(\bOR\b|\bAND\b).*[\'"].*=/i',
            '/[\'"].*\b(UNION|SELECT)\b.*[\'"].*\b(FROM|WHERE)\b/i',
            '/\b(SLEEP|BENCHMARK|WAITFOR)\s*\(/i',
            '/[\'"].*\b(TRUE|FALSE)\b.*[\'"].*=/i'
        ];

        $detected_patterns = [];

        foreach ($suspicious_patterns as $pattern) {
            if (preg_match($pattern, $query)) {
                $detected_patterns[] = $pattern;
            }

            foreach ($parameters as $param) {
                if (is_string($param) && preg_match($pattern, $param)) {
                    $detected_patterns[] = $pattern . ' (in parameter)';
                }
            }
        }

        return $detected_patterns;
    }

    public function logSqlInjectionAttempt($query, $parameters, $detected_patterns, $user_id = null) {
        try {
            $ip = $this->getClientIp();
            $severity = count($detected_patterns) > 3 ? 'critical' :
                       (count($detected_patterns) > 1 ? 'high' : 'medium');

            $stmt = $this->pdo->prepare("
                INSERT INTO sql_injection_attempts
                (ip_address, user_id, query_attempted, parameters, detected_patterns, severity)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $ip,
                $user_id,
                $query,
                json_encode($parameters),
                implode(', ', $detected_patterns),
                $severity
            ]);

            if ($severity === 'critical') {
                $this->blockIpForSqlInjection($ip);
            }

        } catch (PDOException $e) {
            error_log("SQL injection logging failed: " . $e->getMessage());
        }
    }

    private function blockIpForSqlInjection($ip) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO blocked_ips (ip_address, reason, blocked_until, permanent)
                VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR), FALSE)
                ON DUPLICATE KEY UPDATE
                reason = 'محاولات SQL Injection متكررة',
                blocked_until = DATE_ADD(NOW(), INTERVAL 24 HOUR),
                blocked_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([$ip, 'محاولة SQL Injection خطيرة']);

        } catch (PDOException $e) {
            error_log("IP blocking for SQL injection failed: " . $e->getMessage());
        }
    }

    public function safeQuery($query, $parameters = [], $user_id = null) {
        $detected_patterns = $this->detectSqlInjection($query, $parameters);

        if (!empty($detected_patterns)) {
            $this->logSqlInjectionAttempt($query, $parameters, $detected_patterns, $user_id);
            throw new SecurityException('استعلام مشبوه تم رفضه لأسباب أمنية');
        }

        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($parameters);
            return $stmt;

        } catch (PDOException $e) {
            error_log("Safe query execution failed: " . $e->getMessage());
            throw new DatabaseException('خطأ في تنفيذ الاستعلام');
        }
    }

    public function saveEncryptedData($table_name, $record_id, $field_name, $data) {
        $encrypted_data = $this->encrypt($data);

        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO encrypted_data (table_name, record_id, field_name, encrypted_value)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                encrypted_value = VALUES(encrypted_value),
                updated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([$table_name, $record_id, $field_name, $encrypted_data]);

        } catch (PDOException $e) {
            error_log("Encrypted data save failed: " . $e->getMessage());
            throw new DatabaseException('فشل في حفظ البيانات المشفرة');
        }
    }

    public function getEncryptedData($table_name, $record_id, $field_name) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT encrypted_value FROM encrypted_data
                WHERE table_name = ? AND record_id = ? AND field_name = ?
            ");

            $stmt->execute([$table_name, $record_id, $field_name]);
            $result = $stmt->fetch();

            if ($result) {
                return $this->decrypt($result['encrypted_value']);
            }

            return null;

        } catch (PDOException $e) {
            error_log("Encrypted data retrieval failed: " . $e->getMessage());
            return null;
        }
    }

    private function getClientIp() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED',
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED',
                   'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP,
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

class SecurityException extends Exception {}
class DatabaseException extends Exception {}

if (isset($pdo)) {
    $db_security = new DatabaseSecurity($pdo);
}
