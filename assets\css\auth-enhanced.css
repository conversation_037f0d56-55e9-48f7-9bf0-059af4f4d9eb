/* ملف CSS للتحسينات المتقدمة لصفحات المصادقة - PlayGood */
/* تأثيرات إضافية وتحسينات بصرية متقدمة */

/* تحسينات الخلفية المتقدمة */
.auth-page {
    position: relative;
    background-attachment: fixed;
    background-size: cover;
}

/* تأثير الموجات المتحركة في الخلفية */
.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(ellipse at top, rgba(102, 126, 234, 0.1) 0%, transparent 70%),
        radial-gradient(ellipse at bottom, rgba(240, 147, 251, 0.1) 0%, transparent 70%),
        linear-gradient(45deg, 
            rgba(102, 126, 234, 0.05) 0%, 
            rgba(118, 75, 162, 0.05) 25%, 
            rgba(240, 147, 251, 0.05) 50%, 
            rgba(102, 126, 234, 0.05) 75%, 
            rgba(118, 75, 162, 0.05) 100%);
    animation: waveBackground 20s ease-in-out infinite;
    z-index: 1;
}

@keyframes waveBackground {
    0%, 100% { 
        transform: translateY(0) scale(1);
        opacity: 0.6;
    }
    25% { 
        transform: translateY(-20px) scale(1.02);
        opacity: 0.8;
    }
    50% { 
        transform: translateY(0) scale(1.05);
        opacity: 1;
    }
    75% { 
        transform: translateY(20px) scale(1.02);
        opacity: 0.8;
    }
}

/* تحسينات البطاقة مع تأثير الزجاج المحسن */
.auth-card-enhanced {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 40px 100px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 4px 0 rgba(255, 255, 255, 0.4) inset,
        0 0 80px rgba(102, 126, 234, 0.1);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-card-enhanced:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 0 0 2px rgba(255, 255, 255, 0.3) inset,
        0 4px 0 rgba(255, 255, 255, 0.4) inset,
        0 0 50px rgba(102, 126, 234, 0.15);
}

/* تأثير الضوء المتحرك المحسن */
.auth-card-enhanced::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        var(--auth-primary), 
        var(--auth-secondary), 
        var(--auth-accent), 
        var(--auth-primary));
    background-size: 400% 400%;
    border-radius: calc(var(--auth-border-radius) + 2px);
    z-index: -1;
    opacity: 0;
    animation: borderGlow 8s ease-in-out infinite;
    filter: blur(1px);
}

.auth-card-enhanced:hover::after {
    opacity: 0.8;
    filter: blur(0px);
}

/* تحسينات النماذج المتقدمة */
.auth-form-floating .auth-form-control {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 100%);
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    padding: 1.2rem 4rem 1.2rem 1.2rem;
    font-size: 1.05rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.auth-form-floating .auth-form-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(102, 126, 234, 0.15), 
        transparent);
    transition: left 1s ease;
}

.auth-form-floating .auth-form-control:hover::before {
    left: 100%;
}

.auth-form-floating .auth-form-control:focus {
    border-color: var(--auth-primary);
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.12),
        0 8px 20px rgba(102, 126, 234, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset;
    transform: translateY(-2px) scale(1.01);
}

/* تحسينات الأيقونات المتقدمة */
.auth-form-icon-enhanced {
    position: absolute;
    right: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--auth-primary);
    font-size: 1.2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 3;
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.auth-form-floating:hover .auth-form-icon-enhanced {
    transform: translateY(-50%) scale(1.1);
    color: var(--auth-secondary);
    text-shadow: 0 0 10px rgba(118, 75, 162, 0.3);
}

.auth-form-floating .auth-form-control:focus + .auth-form-icon-enhanced {
    transform: translateY(-50%) scale(1.2);
    color: var(--auth-accent);
    text-shadow: 0 0 15px rgba(240, 147, 251, 0.4);
}

/* تحسينات الأزرار المتقدمة */
.auth-btn-modern {
    position: relative;
    width: 100%;
    padding: 1.4rem 2.5rem;
    border: none;
    border-radius: 20px;
    font-size: 1.15rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: white;
    background: linear-gradient(135deg,
        var(--auth-primary) 0%,
        var(--auth-secondary) 30%,
        var(--auth-accent) 60%,
        var(--auth-primary) 100%);
    background-size: 400% 400%;
    box-shadow:
        0 15px 40px rgba(102, 126, 234, 0.4),
        0 0 0 2px rgba(255, 255, 255, 0.3) inset,
        0 2px 0 rgba(255, 255, 255, 0.4) inset;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    overflow: hidden;
    animation: buttonPulse 6s ease-in-out infinite;
    margin-top: 1.5rem;
}

.auth-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.5),
        transparent);
    transition: left 1s ease;
}

.auth-btn-modern:hover::before {
    left: 100%;
}

.auth-btn-modern:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        0 0 0 2px rgba(255, 255, 255, 0.3) inset,
        0 2px 0 rgba(255, 255, 255, 0.4) inset,
        0 0 30px rgba(102, 126, 234, 0.2);
    background-position: 100% 50%;
}

.auth-btn-modern:active {
    transform: translateY(-4px) scale(1.02);
    transition: all 0.1s ease;
}

/* تحسينات الروابط */
.auth-link-modern {
    position: relative;
    color: var(--auth-primary);
    text-decoration: none;
    font-weight: 700;
    padding: 0.8rem 1.5rem;
    border-radius: 15px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.auth-link-modern::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--auth-primary), var(--auth-accent));
    transition: all 0.4s ease;
    transform: translateX(-50%);
    border-radius: 2px;
}

.auth-link-modern:hover::before {
    width: 100%;
}

.auth-link-modern:hover {
    color: white;
    background: linear-gradient(135deg, var(--auth-primary), var(--auth-secondary));
    transform: translateY(-2px) scale(1.02);
    text-decoration: none;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
}

/* تحسينات الروابط البديلة */
.auth-link-alt {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.auth-link-alt:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    transform: translateY(-5px) scale(1.08);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.auth-link-alt i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.auth-link-alt:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* تحسينات التنبيهات */
.auth-alert {
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    backdrop-filter: blur(10px);
    animation: alertSlideIn 0.5s ease-out;
}

.auth-alert-danger {
    background: rgba(239, 68, 68, 0.15);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.1);
}

.auth-alert-success {
    background: rgba(16, 185, 129, 0.15);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
}

@keyframes alertSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .auth-card-enhanced {
        margin: 1rem;
        border-radius: 20px;
    }
    
    .auth-card-enhanced:hover {
        transform: translateY(-10px) scale(1.02);
    }
    
    .auth-form-floating .auth-form-control {
        padding: 1rem 3.5rem 1rem 1rem;
        font-size: 1rem;
        border-radius: 16px;
    }
    
    .auth-btn-modern {
        padding: 1.2rem 2rem;
        font-size: 1rem;
        border-radius: 16px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .auth-page::before {
        animation: none; /* تقليل الحركة */
    }
    
    .auth-card-enhanced {
        backdrop-filter: blur(20px);
        animation: none;
    }
    
    .auth-btn-modern {
        animation: none;
    }
}
