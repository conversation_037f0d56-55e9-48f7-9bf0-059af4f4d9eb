<?php
/**
 * إصلاح مشكلة عرض البيانات - PlayGood
 * المشكلة: يظهر اسم العميل في جميع الحقول (الجهاز، العميل، التكلفة)
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشكلة عرض البيانات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. فحص الجلسات والبيانات المرتبطة
    echo "<h2>1. فحص الجلسات والبيانات المرتبطة</h2>";
    
    try {
        // استعلام مفصل للجلسات
        $sessions_query = "
            SELECT 
                s.session_id,
                s.start_time,
                s.end_time,
                s.status,
                s.total_cost,
                s.customer_id,
                d.device_id,
                d.device_name,
                d.device_type,
                d.single_rate,
                d.multi_rate,
                r.room_name,
                c.customer_id as customer_table_id,
                c.name as customer_name,
                c.phone as customer_phone,
                TIMESTAMPDIFF(MINUTE, s.start_time, COALESCE(s.end_time, NOW())) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE d.client_id = 1
            ORDER BY s.session_id DESC
            LIMIT 5
        ";
        
        $sessions = $pdo->query($sessions_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الجلسات: <strong>" . count($sessions) . "</strong></p>";
        
        if (count($sessions) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 12px;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 5px;'>ID الجلسة</th>";
            echo "<th style='padding: 5px;'>اسم الجهاز</th>";
            echo "<th style='padding: 5px;'>نوع الجهاز</th>";
            echo "<th style='padding: 5px;'>اسم العميل</th>";
            echo "<th style='padding: 5px;'>هاتف العميل</th>";
            echo "<th style='padding: 5px;'>اسم الغرفة</th>";
            echo "<th style='padding: 5px;'>المدة (دقيقة)</th>";
            echo "<th style='padding: 5px;'>التكلفة</th>";
            echo "<th style='padding: 5px;'>الحالة</th>";
            echo "</tr>";
            
            foreach ($sessions as $session) {
                echo "<tr>";
                echo "<td style='padding: 5px;'>" . $session['session_id'] . "</td>";
                echo "<td style='padding: 5px; color: blue;'>" . htmlspecialchars($session['device_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 5px; color: green;'>" . htmlspecialchars($session['device_type'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 5px; color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($session['customer_phone'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($session['room_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 5px;'>" . $session['duration_minutes'] . "</td>";
                echo "<td style='padding: 5px;'>" . number_format($session['total_cost'] ?? 0, 2) . " ج.م</td>";
                echo "<td style='padding: 5px;'>" . $session['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p style='color: blue;'>ℹ️ الألوان: أزرق = اسم الجهاز، أخضر = نوع الجهاز، أحمر = اسم العميل</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات في النظام</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الجلسات: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص الأجهزة
    echo "<h2>2. فحص الأجهزة</h2>";
    
    try {
        $devices = $pdo->query("
            SELECT device_id, device_name, device_type, status, single_rate, multi_rate
            FROM devices 
            WHERE client_id = 1 
            ORDER BY device_id
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الأجهزة: <strong>" . count($devices) . "</strong></p>";
        
        if (count($devices) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>نوع الجهاز</th>";
            echo "<th style='padding: 8px;'>الحالة</th>";
            echo "<th style='padding: 8px;'>السعر/ساعة</th>";
            echo "</tr>";
            
            foreach ($devices as $device) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $device['device_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($device['device_type']) . "</td>";
                echo "<td style='padding: 8px;'>" . $device['status'] . "</td>";
                echo "<td style='padding: 8px;'>" . $device['single_rate'] . " ج.م</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الأجهزة: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص العملاء
    echo "<h2>3. فحص العملاء</h2>";
    
    try {
        $customers = $pdo->query("
            SELECT customer_id, name, phone, email
            FROM customers 
            WHERE client_id = 1 
            ORDER BY customer_id
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد العملاء: <strong>" . count($customers) . "</strong></p>";
        
        if (count($customers) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>الاسم</th>";
            echo "<th style='padding: 8px;'>الهاتف</th>";
            echo "<th style='padding: 8px;'>البريد</th>";
            echo "</tr>";
            
            foreach ($customers as $customer) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['email'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص العملاء: " . $e->getMessage() . "</p>";
    }
    
    // 4. فحص الفواتير
    echo "<h2>4. فحص الفواتير</h2>";
    
    try {
        $invoices_query = "
            SELECT 
                i.invoice_id,
                i.invoice_number,
                i.time_cost,
                i.products_cost,
                i.total_cost,
                i.created_at,
                s.session_id,
                d.device_name,
                d.device_type,
                c.name as customer_name
            FROM invoices i
            LEFT JOIN sessions s ON i.session_id = s.session_id
            LEFT JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE i.client_id = 1
            ORDER BY i.invoice_id DESC
            LIMIT 5
        ";
        
        $invoices = $pdo->query($invoices_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>عدد الفواتير: <strong>" . count($invoices) . "</strong></p>";
        
        if (count($invoices) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>رقم الفاتورة</th>";
            echo "<th style='padding: 8px;'>اسم الجهاز</th>";
            echo "<th style='padding: 8px;'>نوع الجهاز</th>";
            echo "<th style='padding: 8px;'>اسم العميل</th>";
            echo "<th style='padding: 8px;'>تكلفة الوقت</th>";
            echo "<th style='padding: 8px;'>تكلفة المنتجات</th>";
            echo "<th style='padding: 8px;'>التكلفة الإجمالية</th>";
            echo "</tr>";
            
            foreach ($invoices as $invoice) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($invoice['invoice_number']) . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . htmlspecialchars($invoice['device_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px; color: green;'>" . htmlspecialchars($invoice['device_type'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td style='padding: 8px;'>" . number_format($invoice['time_cost'], 2) . " ج.م</td>";
                echo "<td style='padding: 8px;'>" . number_format($invoice['products_cost'], 2) . " ج.م</td>";
                echo "<td style='padding: 8px; font-weight: bold;'>" . number_format($invoice['total_cost'], 2) . " ج.م</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد فواتير في النظام</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الفواتير: " . $e->getMessage() . "</p>";
    }
    
    // 5. اختبار استعلام محدد للمشكلة
    echo "<h2>5. اختبار استعلام محدد للمشكلة</h2>";
    
    try {
        // محاكاة الاستعلام المستخدم في صفحة الجلسات
        $test_query = "
            SELECT 
                s.session_id,
                s.start_time,
                s.customer_id,
                d.device_name,
                d.device_type,
                d.single_rate,
                c.name as customer_name,
                TIMESTAMPDIFF(MINUTE, s.start_time, NOW()) as duration_minutes
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN customers c ON s.customer_id = c.customer_id
            WHERE s.status = 'active' AND d.client_id = 1
            LIMIT 3
        ";
        
        $test_sessions = $pdo->query($test_query)->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>الجلسات النشطة للاختبار: <strong>" . count($test_sessions) . "</strong></p>";
        
        if (count($test_sessions) > 0) {
            foreach ($test_sessions as $session) {
                echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
                echo "<h4>جلسة رقم: " . $session['session_id'] . "</h4>";
                
                echo "<p><strong>اسم الجهاز:</strong> <span style='color: blue;'>" . htmlspecialchars($session['device_name']) . "</span></p>";
                echo "<p><strong>نوع الجهاز:</strong> <span style='color: green;'>" . htmlspecialchars($session['device_type']) . "</span></p>";
                echo "<p><strong>اسم العميل:</strong> <span style='color: red;'>" . htmlspecialchars($session['customer_name'] ?? 'غير محدد') . "</span></p>";
                
                // حساب التكلفة
                $duration_minutes = $session['duration_minutes'] ?? 0;
                $hourly_rate = $session['single_rate'] ?? 0;
                $time_cost = $duration_minutes > 0 && $hourly_rate > 0 ? ceil($duration_minutes / 60) * $hourly_rate : 0;
                
                echo "<p><strong>المدة:</strong> " . $duration_minutes . " دقيقة</p>";
                echo "<p><strong>السعر/ساعة:</strong> " . $hourly_rate . " ج.م</p>";
                echo "<p><strong>التكلفة المحسوبة:</strong> <span style='color: purple; font-weight: bold;'>" . number_format($time_cost, 2) . " ج.م</span></p>";
                
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة للاختبار</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
    
    // 6. تشخيص المشكلة المحتملة
    echo "<h2>6. تشخيص المشكلة المحتملة</h2>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🔍 أسباب محتملة للمشكلة:</h4>";
    echo "<ul>";
    echo "<li><strong>مشكلة في الاستعلام:</strong> قد يكون هناك خطأ في JOIN أو SELECT</li>";
    echo "<li><strong>مشكلة في عرض البيانات:</strong> قد يكون هناك خطأ في PHP echo</li>";
    echo "<li><strong>مشكلة في JavaScript:</strong> قد يكون هناك خطأ في تحديث DOM</li>";
    echo "<li><strong>مشكلة في CSS:</strong> قد تكون العناصر متداخلة أو مخفية</li>";
    echo "<li><strong>مشكلة في قاعدة البيانات:</strong> قد تكون البيانات مكررة أو خاطئة</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>الروابط المفيدة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الجلسات</a>";
echo "<a href='client/invoices.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>صفحة الفواتير</a>";
echo "<a href='client/dashboard.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>
