-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: station
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (1,'admin','<EMAIL>','$2a$12$vFAjvx8m93Bb76ogrJq4yuu1Pw38jFvhaRibQBQ0cmZ5IleS3jm8G','مدير النظام','super_admin','2025-06-08 08:28:08','2025-06-09 09:26:05',1);
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_settings`
--

DROP TABLE IF EXISTS `business_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_key`),
  CONSTRAINT `business_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_settings`
--

LOCK TABLES `business_settings` WRITE;
/*!40000 ALTER TABLE `business_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cafeteria_items`
--

DROP TABLE IF EXISTS `cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `cafeteria_items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cafeteria_items`
--

LOCK TABLES `cafeteria_items` WRITE;
/*!40000 ALTER TABLE `cafeteria_items` DISABLE KEYS */;
INSERT INTO `cafeteria_items` VALUES (13,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-09 10:38:25',NULL,1),(14,'شاي بحليب',8.00,'مشروبات ساخنة','شاي بحليب','2025-06-09 10:38:42',NULL,1),(15,'قهوة',10.00,'مشروبات ساخنة','','2025-06-09 11:06:50',NULL,1),(16,'نسكافية',15.00,'مشروبات ساخنة','','2025-06-09 11:07:01',NULL,1),(17,'قهوة تركي',25.00,'مشروبات ساخنة','','2025-06-09 11:07:13',NULL,1),(18,'قهوة فرنساوي',35.00,'مشروبات ساخنة','','2025-06-09 11:07:23',NULL,1),(19,'بيبسي',12.00,'مشروبات ساقعة','','2025-06-09 11:07:35',NULL,1),(20,'سبيرو سباتس ابيض',15.00,'مشروبات ساقعة','','2025-06-09 11:07:48',NULL,1),(21,'سبيرو سباتس اسود',15.00,'مشروبات ساقعة','','2025-06-09 11:07:59',NULL,1),(23,'سبرايت',12.00,'مشروبات ساقعة','','2025-06-10 18:34:10',NULL,1);
/*!40000 ALTER TABLE `cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
INSERT INTO `categories` VALUES (7,'مشروبات ساقعة',1),(8,'مشروبات ساخنة',1),(9,'مأكولات',1),(10,'مقبلات',1),(12,'تسالي',1);
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `name` varchar(255) NOT NULL DEFAULT 'مركز الألعاب',
  `password` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`client_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'يوزرسيف','احمد ','<EMAIL>','***********','$2y$10$txuFt9nn1DVao4bRaz2KoOQYi6tZAPh0nlymJpTLsB3XfN5v9b6J.','الجيزة','الجيزة','basic','2025-06-08','2025-07-08',1,'2025-06-08 08:35:48','2025-06-08 09:17:43','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(2,'محمد','سمير','<EMAIL>','01125454417','$2y$10$nbG/.9myzQn1ux2Hhuv2mOzFHiV/w1IT9p2tRadhGPsCi4dOtBH8.','7567\r\n33','7657','basic','2025-06-09','2025-07-09',1,'2025-06-09 09:31:49','2025-06-09 09:31:49','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(3,'egypto','تامر','<EMAIL>','***********','$2y$10$fmrMFKeY2BJjVHfutQpFT.3HTM1PjOZ1uWsn.PSy6kIBBQj6hZAnm','cairo','cairo','basic','2025-06-09','2025-07-09',1,'2025-06-09 11:52:31','2025-06-09 11:52:31','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(4,'PlayStation','ahmed','<EMAIL>','201141453034','$2y$10$kyPCoB5hoMRIhR66ms/JQ.ZrU1vsOfP1Ud4ZsMWaEiP0Dzj/oKF7.','مصر - الجيزة','الجيزة','basic','2025-06-12','2025-07-12',1,'2025-06-12 06:06:24','2025-06-12 06:49:05','gaming_center','','من 9 صباحاً إلى 12 منتصف الليل','online store',NULL);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`customer_id`),
  UNIQUE KEY `idx_phone_client` (`phone`,`client_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_customers_client_id` (`client_id`),
  KEY `idx_customers_phone` (`phone`),
  KEY `idx_customers_client` (`client_id`),
  CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (7,1,'أحمد محمد','01234567890','<EMAIL>',NULL,'2025-06-10 18:15:26',NULL,NULL,NULL),(8,1,'فاطمة علي','01234567891','<EMAIL>',NULL,'2025-06-10 18:15:26',NULL,NULL,NULL),(9,1,'محمد حسن','01234567892','<EMAIL>',NULL,'2025-06-10 18:15:26',NULL,NULL,NULL),(10,1,'سارة أحمد','01234567893','<EMAIL>',NULL,'2025-06-10 18:15:26',NULL,NULL,NULL),(11,1,'عمر خالد','01234567894','<EMAIL>',NULL,'2025-06-10 18:15:26',NULL,NULL,NULL),(22,4,'عمر يحيي','01111111111',NULL,NULL,'2025-06-12 07:12:39',NULL,NULL,NULL);
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL,
  `single_rate` decimal(10,2) DEFAULT NULL,
  `multi_rate` decimal(10,2) DEFAULT NULL,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `room_id` (`room_id`),
  KEY `idx_devices_client_status` (`client_id`,`status`),
  CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `devices_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES (1,1,'جهاز 1','PS5',15.00,15.00,25.00,'occupied',1,'2025-06-09 06:27:44','2025-06-10 19:46:26'),(3,1,'جهاز 2','PS4',10.00,10.00,20.00,'available',NULL,'2025-06-09 06:34:02','2025-06-10 19:43:41'),(4,2,'434','',0.00,NULL,NULL,'available',NULL,'2025-06-09 09:33:07','2025-06-09 09:33:07'),(5,2,'','',0.00,NULL,NULL,'available',NULL,'2025-06-09 09:33:09','2025-06-09 09:33:09'),(6,2,'','',0.00,NULL,NULL,'available',NULL,'2025-06-09 09:33:10','2025-06-09 09:33:10'),(7,2,'','',0.00,NULL,NULL,'available',NULL,'2025-06-09 09:33:11','2025-06-09 09:33:11'),(8,2,'','',0.00,NULL,NULL,'available',NULL,'2025-06-09 09:33:19','2025-06-09 09:33:19'),(10,1,'جهاز 3','Xbox',25.00,25.00,35.00,'occupied',NULL,'2025-06-09 09:48:22','2025-06-10 19:43:36'),(11,1,'جهاز 4','PC',20.00,20.00,25.00,'available',NULL,'2025-06-09 10:39:36','2025-06-10 19:15:23'),(13,1,'جهاز 5','PS4',10.00,10.00,15.00,'available',NULL,'2025-06-10 18:32:08','2025-06-10 18:32:08'),(14,4,'ps 1','PS4',10.00,10.00,15.00,'occupied',NULL,'2025-06-12 06:30:11','2025-06-12 07:13:12'),(15,4,'جهاز 2','PS5',15.00,15.00,20.00,'available',NULL,'2025-06-12 07:19:44','2025-06-12 07:19:44');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees`
--

DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner') NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `hire_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `fk_employee_client` (`client_id`),
  CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees`
--

LOCK TABLES `employees` WRITE;
/*!40000 ALTER TABLE `employees` DISABLE KEYS */;
INSERT INTO `employees` VALUES (1,1,'tamer','***********','cashier',5000.00,'2025-06-09','2025-06-09 15:26:27','2025-06-10 20:01:38','tamer746','$2y$10$zW0nyRAAARZdsLfG/6SWTewRWWzwJ5WQ0ptim4TX/ry89qbEQZRsK','2025-06-10 20:01:38',1,NULL,NULL);
/*!40000 ALTER TABLE `employees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_settings`
--

DROP TABLE IF EXISTS `invoice_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_settings`
--

LOCK TABLES `invoice_settings` WRITE;
/*!40000 ALTER TABLE `invoice_settings` DISABLE KEYS */;
INSERT INTO `invoice_settings` VALUES (1,1,'#2316d4','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي','#000000','شارع احمد يحيي','***********',0,'2025-06-10 19:53:56','2025-06-10 19:58:43'),(2,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 05:45:45','2025-06-12 05:45:45'),(3,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 05:52:09','2025-06-12 05:52:09'),(4,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 06:20:45','2025-06-12 06:20:45');
/*!40000 ALTER TABLE `invoice_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `time_cost` decimal(10,2) NOT NULL,
  `products_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`invoice_id`),
  KEY `session_id` (`session_id`),
  KEY `idx_invoices_payment_status` (`payment_status`),
  CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (1,5,'202506090005',25.00,15.00,40.00,'pending','2025-06-09 11:35:54',1),(2,6,'202506090006',20.00,35.00,55.00,'pending','2025-06-09 11:38:47',1),(3,4,'202506090004',30.00,18.00,48.00,'pending','2025-06-09 11:44:23',1),(4,3,'202506090003',75.00,5.00,80.00,'pending','2025-06-09 11:44:26',1),(5,7,'202506090007',25.00,0.00,25.00,'pending','2025-06-09 11:44:28',1),(6,8,'202506090008',20.00,0.00,20.00,'pending','2025-06-09 11:44:31',1);
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_order_number` (`client_id`,`order_number`),
  KEY `customer_id` (`customer_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_status_log`
--

DROP TABLE IF EXISTS `payment_status_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_status_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `old_status` enum('pending','paid','cancelled') DEFAULT NULL,
  `new_status` enum('pending','paid','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_payment_log_invoice` (`invoice_id`),
  KEY `idx_payment_log_date` (`changed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_status_log`
--

LOCK TABLES `payment_status_log` WRITE;
/*!40000 ALTER TABLE `payment_status_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_status_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  PRIMARY KEY (`product_id`),
  KEY `category_id` (`category_id`),
  KEY `section_id` (`section_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rooms`
--

DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_type` enum('VIP','regular','private') DEFAULT 'regular',
  `capacity` int(11) DEFAULT 1,
  `special_rate` decimal(8,2) DEFAULT 0.00,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`room_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rooms`
--

LOCK TABLES `rooms` WRITE;
/*!40000 ALTER TABLE `rooms` DISABLE KEYS */;
INSERT INTO `rooms` VALUES (1,1,'اساسي','regular',1,0.00,NULL,'2025-06-10 18:37:40','2025-06-10 18:37:40'),(2,1,'غرفه 1','regular',1,0.00,NULL,'2025-06-12 05:29:44','2025-06-12 05:29:44');
/*!40000 ALTER TABLE `rooms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sections`
--

DROP TABLE IF EXISTS `sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sections`
--

LOCK TABLES `sections` WRITE;
/*!40000 ALTER TABLE `sections` DISABLE KEYS */;
/*!40000 ALTER TABLE `sections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `session_products`
--

DROP TABLE IF EXISTS `session_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL COMMENT 'سعر المنتج وقت الإضافة',
  `total` decimal(10,2) GENERATED ALWAYS AS (`price` * `quantity`) STORED COMMENT 'إجمالي السعر',
  `created_by` int(11) DEFAULT NULL COMMENT 'معرف المستخدم الذي أضاف المنتج',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`),
  CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `session_products`
--

LOCK TABLES `session_products` WRITE;
/*!40000 ALTER TABLE `session_products` DISABLE KEYS */;
INSERT INTO `session_products` VALUES (11,4,14,1,'2025-06-09 11:05:57',8.00,8.00,1,NULL),(15,6,15,1,'2025-06-09 11:16:27',10.00,10.00,1,NULL),(17,3,13,1,'2025-06-09 11:17:09',5.00,5.00,1,NULL),(18,5,13,1,'2025-06-09 11:21:43',5.00,5.00,1,NULL),(20,4,15,1,'2025-06-09 11:27:33',10.00,10.00,NULL,NULL),(24,6,17,1,'2025-06-09 11:32:26',25.00,25.00,NULL,NULL),(25,5,15,1,'2025-06-09 11:35:47',10.00,10.00,NULL,NULL),(27,9,17,1,'2025-06-10 19:06:15',25.00,25.00,NULL,NULL),(28,10,13,1,'2025-06-10 19:06:30',5.00,5.00,NULL,NULL);
/*!40000 ALTER TABLE `session_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_id` int(11) DEFAULT NULL,
  `client_id` int(11) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`session_id`),
  KEY `customer_id` (`customer_id`),
  KEY `idx_sessions_client_id` (`client_id`),
  KEY `idx_sessions_status` (`status`),
  KEY `idx_sessions_device_id` (`device_id`),
  KEY `idx_sessions_start_time` (`start_time`),
  KEY `idx_sessions_device_status` (`device_id`,`status`),
  CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`),
  CONSTRAINT `sessions_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES (1,1,'2025-06-09 09:50:21','2025-06-09 09:53:17','completed',1,1,'2025-06-09 06:50:21','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(2,1,'2025-06-09 09:56:19','2025-06-09 09:56:26','completed',1,1,'2025-06-09 06:56:19','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(3,1,'2025-06-09 10:24:37','2025-06-09 14:44:26','completed',1,1,'2025-06-09 07:24:37','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(4,3,'2025-06-09 12:21:19','2025-06-09 14:44:23','completed',1,1,'2025-06-09 09:21:19','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(5,10,'2025-06-09 13:39:13','2025-06-09 14:35:54','completed',1,1,'2025-06-09 10:39:13','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(6,11,'2025-06-09 13:39:49','2025-06-09 14:38:47','completed',1,1,'2025-06-09 10:39:49','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(7,10,'2025-06-09 14:40:30','2025-06-09 14:44:28','completed',1,1,'2025-06-09 11:40:30','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(8,11,'2025-06-09 14:40:38','2025-06-09 14:44:31','completed',1,1,'2025-06-09 11:40:38','2025-06-10 18:04:17',NULL,1,0.00,NULL,NULL),(9,3,'2025-06-10 21:58:35','2025-06-10 22:43:41','completed',1,1,'2025-06-10 18:58:35','2025-06-10 19:43:41',NULL,1,35.00,NULL,''),(10,11,'2025-06-10 21:59:10','2025-06-10 22:15:23','completed',1,1,'2025-06-10 18:59:10','2025-06-10 19:15:23',NULL,1,25.00,NULL,''),(11,10,'2025-06-10 22:43:36',NULL,'active',1,NULL,'2025-06-10 19:43:36','2025-06-10 19:43:36',NULL,1,0.00,NULL,''),(12,1,'2025-06-10 22:46:26',NULL,'active',1,NULL,'2025-06-10 19:46:26','2025-06-10 19:46:26',NULL,1,0.00,'2025-06-10 19:16:26',''),(13,14,'2025-06-12 10:13:12',NULL,'active',4,NULL,'2025-06-12 07:13:12','2025-06-12 07:13:12',NULL,4,0.00,NULL,'');
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-12 10:20:01
