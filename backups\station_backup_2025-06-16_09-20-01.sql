-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: station
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `additional_income`
--

DROP TABLE IF EXISTS `additional_income`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_additional_income_client` (`client_id`),
  KEY `idx_additional_income_type` (`income_type_id`),
  KEY `idx_additional_income_date` (`income_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `additional_income`
--

LOCK TABLES `additional_income` WRITE;
/*!40000 ALTER TABLE `additional_income` DISABLE KEYS */;
INSERT INTO `additional_income` VALUES (1,1,15,100.00,'','2025-06-13','',1,'2025-06-13 00:04:08','2025-06-13 00:04:08'),(2,1,15,100.00,'','2025-06-13','',1,'2025-06-13 00:05:55','2025-06-13 00:05:55');
/*!40000 ALTER TABLE `additional_income` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (1,'admin','<EMAIL>','$2a$12$vFAjvx8m93Bb76ogrJq4yuu1Pw38jFvhaRibQBQ0cmZ5IleS3jm8G','مدير النظام','super_admin','2025-06-08 05:28:08','2025-06-14 14:59:28',1);
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `attendance_detailed`
--

DROP TABLE IF EXISTS `attendance_detailed`;
/*!50001 DROP VIEW IF EXISTS `attendance_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `attendance_detailed` AS SELECT
 1 AS `attendance_id`,
  1 AS `shift_id`,
  1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `shift_name`,
  1 AS `shift_date`,
  1 AS `scheduled_start`,
  1 AS `scheduled_end`,
  1 AS `check_in_time`,
  1 AS `check_out_time`,
  1 AS `break_start_time`,
  1 AS `break_end_time`,
  1 AS `actual_hours`,
  1 AS `overtime_hours`,
  1 AS `break_hours`,
  1 AS `attendance_status`,
  1 AS `late_minutes`,
  1 AS `early_leave_minutes`,
  1 AS `attendance_notes`,
  1 AS `role_in_shift`,
  1 AS `is_mandatory` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `business_settings`
--

DROP TABLE IF EXISTS `business_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_key`),
  CONSTRAINT `business_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_settings`
--

LOCK TABLES `business_settings` WRITE;
/*!40000 ALTER TABLE `business_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cafeteria_items`
--

DROP TABLE IF EXISTS `cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_product_per_client` (`name`,`category`,`client_id`),
  KEY `category_id` (`category_id`),
  KEY `idx_cafeteria_search` (`name`,`category`,`client_id`),
  CONSTRAINT `cafeteria_items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cafeteria_items`
--

LOCK TABLES `cafeteria_items` WRITE;
/*!40000 ALTER TABLE `cafeteria_items` DISABLE KEYS */;
INSERT INTO `cafeteria_items` VALUES (13,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-09 07:38:25',NULL,1),(14,'شاي بحليب',8.00,'مشروبات ساخنة','شاي بحليب','2025-06-09 07:38:42',NULL,1),(15,'قهوة',10.00,'مشروبات ساخنة','','2025-06-09 08:06:50',NULL,1),(16,'نسكافية',15.00,'مشروبات ساخنة','','2025-06-09 08:07:01',NULL,1),(17,'قهوة تركي',25.00,'مشروبات ساخنة','','2025-06-09 08:07:13',NULL,1),(18,'قهوة فرنساوي',35.00,'مشروبات ساخنة','','2025-06-09 08:07:23',NULL,1),(19,'بيبسي',12.00,'مشروبات ساقعة','','2025-06-09 08:07:35',NULL,1),(20,'سبيرو سباتس ابيض',15.00,'مشروبات ساقعة','','2025-06-09 08:07:48',NULL,1),(21,'سبيرو سباتس اسود',15.00,'مشروبات ساقعة','','2025-06-09 08:07:59',NULL,1),(23,'سبرايت',12.00,'مشروبات ساقعة','','2025-06-10 15:34:10',NULL,1),(24,'كافى مكس',343.00,'مشروبات76','','2025-06-13 09:53:10',NULL,2),(25,'شاي 1',10.00,'مشروبات ساخنة','','2025-06-14 13:42:20',NULL,1);
/*!40000 ALTER TABLE `cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
INSERT INTO `categories` VALUES (7,'مشروبات ساقعة',1),(8,'مشروبات ساخنة',1),(9,'مأكولات',1),(10,'مقبلات',1),(12,'تسالي',1),(25,'مشروبات76',2),(26,'مشروبات76',2),(27,'245',2);
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `name` varchar(255) NOT NULL DEFAULT 'مركز الألعاب',
  `password` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`client_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'playgood','احمد','<EMAIL>','***********','$2y$10$txuFt9nn1DVao4bRaz2KoOQYi6tZAPh0nlymJpTLsB3XfN5v9b6J.','الجيزة','الجيزة','basic','2025-06-08','2025-07-08',1,'2025-06-08 05:35:48','2025-06-14 15:01:43','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(2,'ريدوكس','محمد','<EMAIL>','01125454417','$2y$10$nbG/.9myzQn1ux2Hhuv2mOzFHiV/w1IT9p2tRadhGPsCi4dOtBH8.','7567\r\n33','7657','basic','2025-06-09','2025-07-09',0,'2025-06-09 06:31:49','2025-06-14 14:59:40','gaming_center','','من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(3,'egypto','تامر','<EMAIL>','***********','$2y$10$fmrMFKeY2BJjVHfutQpFT.3HTM1PjOZ1uWsn.PSy6kIBBQj6hZAnm','cairo','cairo','basic','2025-06-09','2025-07-09',1,'2025-06-09 08:52:31','2025-06-09 08:52:31','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل','مركز الألعاب',NULL),(4,'PlayStation','ahmed','<EMAIL>','2***********','$2y$10$kyPCoB5hoMRIhR66ms/JQ.ZrU1vsOfP1Ud4ZsMWaEiP0Dzj/oKF7.','مصر - الجيزة','الجيزة','basic','2025-06-12','2025-07-12',1,'2025-06-12 03:06:24','2025-06-12 03:49:05','gaming_center','','من 9 صباحاً إلى 12 منتصف الليل','online store',NULL);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`customer_id`),
  UNIQUE KEY `idx_phone_client` (`phone`,`client_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_customers_client_id` (`client_id`),
  KEY `idx_customers_phone` (`phone`),
  KEY `idx_customers_client` (`client_id`),
  CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (43,1,'عمر سيد','***********',NULL,NULL,'2025-06-16 05:14:13',NULL,NULL,NULL),(44,1,'عميل تجريبي 1','01000000001',NULL,NULL,'2025-06-16 06:53:57',NULL,NULL,NULL),(45,1,'عميل تجريبي 2','01000000002',NULL,NULL,'2025-06-16 06:53:57',NULL,NULL,NULL),(46,1,'عميل تجريبي 3','01000000003',NULL,NULL,'2025-06-16 06:53:57',NULL,NULL,NULL);
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL,
  `single_rate` decimal(10,2) DEFAULT NULL,
  `multi_rate` decimal(10,2) DEFAULT NULL,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `room_id` (`room_id`),
  KEY `idx_devices_client_status` (`client_id`,`status`),
  CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `devices_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES (16,1,'جهاز 1','PS5',15.00,15.00,25.00,'available',NULL,'2025-06-12 19:20:51','2025-06-16 06:27:04'),(17,2,'434','PS4',6.00,6.00,6.00,'occupied',NULL,'2025-06-12 21:17:12','2025-06-13 01:54:37'),(18,1,'جهاز 2','PS4',10.00,10.00,20.00,'available',NULL,'2025-06-12 23:10:16','2025-06-14 08:22:32'),(19,1,'جهاز 3','PS4',10.00,10.00,15.00,'available',NULL,'2025-06-13 11:28:54','2025-06-16 05:15:40');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employee_pages`
--

DROP TABLE IF EXISTS `employee_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_page` (`employee_id`,`page_id`),
  KEY `idx_employee_pages_employee` (`employee_id`),
  KEY `idx_employee_pages_page` (`page_id`),
  CONSTRAINT `employee_pages_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_pages_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `pages` (`page_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_pages`
--

LOCK TABLES `employee_pages` WRITE;
/*!40000 ALTER TABLE `employee_pages` DISABLE KEYS */;
INSERT INTO `employee_pages` VALUES (12,3,2,NULL,'2025-06-14 15:22:59'),(13,3,1,NULL,'2025-06-14 15:22:59'),(14,3,5,NULL,'2025-06-14 15:22:59'),(15,3,14,NULL,'2025-06-14 15:22:59'),(16,3,13,NULL,'2025-06-14 15:22:59');
/*!40000 ALTER TABLE `employee_pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `employee_pages_detailed`
--

DROP TABLE IF EXISTS `employee_pages_detailed`;
/*!50001 DROP VIEW IF EXISTS `employee_pages_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `employee_pages_detailed` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `custom_permissions`,
  1 AS `page_id`,
  1 AS `page_name`,
  1 AS `page_label`,
  1 AS `page_url`,
  1 AS `page_icon`,
  1 AS `page_category`,
  1 AS `granted_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `employee_permissions`
--

DROP TABLE IF EXISTS `employee_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_permission` (`employee_id`,`permission_id`),
  KEY `idx_employee_permissions_employee` (`employee_id`),
  KEY `idx_employee_permissions_permission` (`permission_id`),
  CONSTRAINT `employee_permissions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_permissions`
--

LOCK TABLES `employee_permissions` WRITE;
/*!40000 ALTER TABLE `employee_permissions` DISABLE KEYS */;
INSERT INTO `employee_permissions` VALUES (13,3,19,NULL,'2025-06-14 15:22:59'),(14,3,3,NULL,'2025-06-14 15:22:59'),(15,3,4,NULL,'2025-06-14 15:22:59'),(16,3,24,NULL,'2025-06-14 15:22:59');
/*!40000 ALTER TABLE `employee_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `employee_permissions_detailed`
--

DROP TABLE IF EXISTS `employee_permissions_detailed`;
/*!50001 DROP VIEW IF EXISTS `employee_permissions_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `employee_permissions_detailed` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `employee_role`,
  1 AS `custom_permissions`,
  1 AS `permission_id`,
  1 AS `permission_name`,
  1 AS `permission_label`,
  1 AS `permission_category`,
  1 AS `granted_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `employee_shifts`
--

DROP TABLE IF EXISTS `employee_shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_shifts` (
  `assignment_id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `role_in_shift` enum('supervisor','regular','backup') DEFAULT 'regular',
  `is_mandatory` tinyint(1) DEFAULT 0 COMMENT 'هل الحضور إجباري',
  `assigned_by` int(11) DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('assigned','confirmed','declined','cancelled') DEFAULT 'assigned',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`assignment_id`),
  UNIQUE KEY `unique_employee_shift` (`shift_id`,`employee_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `employee_shifts_ibfk_1` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `employee_shifts_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_shifts`
--

LOCK TABLES `employee_shifts` WRITE;
/*!40000 ALTER TABLE `employee_shifts` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees`
--

DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner') NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `hire_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'هل يستخدم صلاحيات مخصصة أم صلاحيات الدور',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `fk_employee_client` (`client_id`),
  CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees`
--

LOCK TABLES `employees` WRITE;
/*!40000 ALTER TABLE `employees` DISABLE KEYS */;
INSERT INTO `employees` VALUES (3,1,'sayed','01026362111','cashier',5000.00,'2025-06-01','2025-06-14 14:18:27','2025-06-14 15:34:11','sayed871','$2y$10$Dw6dvOyB/5jL.4MQikQjpe1pezD2IRDQq.h0JzNzIIVF.XBNdbmjO','2025-06-14 15:34:11',1,'<EMAIL>','شارع فريد - الأحساء',1);
/*!40000 ALTER TABLE `employees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expense_types`
--

DROP TABLE IF EXISTS `expense_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expense_types_client` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expense_types`
--

LOCK TABLES `expense_types` WRITE;
/*!40000 ALTER TABLE `expense_types` DISABLE KEYS */;
INSERT INTO `expense_types` VALUES (1,2,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(2,2,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(3,2,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(4,2,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(5,2,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(6,2,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(7,2,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(8,2,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(9,3,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(10,3,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(11,3,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(12,3,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(13,3,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(14,3,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(15,3,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(16,3,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(17,1,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(18,1,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(19,1,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(20,1,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(21,1,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(22,1,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(23,1,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(24,1,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(25,4,'فواتير الكهرباء','فواتير استهلاك الكهرباء الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(26,4,'فواتير الإنترنت','فواتير خدمة الإنترنت الشهرية',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(27,4,'إيجار المحل','إيجار المحل الشهري',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(28,4,'صيانة الأجهزة','تكاليف صيانة وإصلاح الأجهزة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(29,4,'مشتريات الكافتيريا','شراء منتجات ومشروبات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(30,4,'رواتب الموظفين','رواتب ومكافآت الموظفين',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(31,4,'مصروفات إدارية','مصروفات إدارية متنوعة',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(32,4,'تسويق وإعلان','تكاليف التسويق والإعلان',1,'2025-06-12 08:28:46','2025-06-12 08:28:46');
/*!40000 ALTER TABLE `expense_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expenses`
--

DROP TABLE IF EXISTS `expenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expenses_client` (`client_id`),
  KEY `idx_expenses_type` (`expense_type_id`),
  KEY `idx_expenses_date` (`expense_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expenses`
--

LOCK TABLES `expenses` WRITE;
/*!40000 ALTER TABLE `expenses` DISABLE KEYS */;
INSERT INTO `expenses` VALUES (2,1,19,1000.00,'','2025-06-13','',1,'2025-06-12 23:46:55','2025-06-12 23:46:55');
/*!40000 ALTER TABLE `expenses` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER IF NOT EXISTS check_expense_amount 
        BEFORE INSERT ON expenses 
        FOR EACH ROW 
        BEGIN 
            IF NEW.amount IS NULL OR NEW.amount = '' OR NOT NEW.amount REGEXP '^[0-9]+\.?[0-9]*$' THEN 
                SET NEW.amount = '0.00'; 
            END IF; 
        END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER IF NOT EXISTS check_expense_amount_update 
        BEFORE UPDATE ON expenses 
        FOR EACH ROW 
        BEGIN 
            IF NEW.amount IS NULL OR NEW.amount = '' OR NOT NEW.amount REGEXP '^[0-9]+\.?[0-9]*$' THEN 
                SET NEW.amount = '0.00'; 
            END IF; 
        END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `income_types`
--

DROP TABLE IF EXISTS `income_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_income_types_client` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_types`
--

LOCK TABLES `income_types` WRITE;
/*!40000 ALTER TABLE `income_types` DISABLE KEYS */;
INSERT INTO `income_types` VALUES (1,2,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(2,2,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(3,2,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(4,2,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(5,2,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(6,3,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(7,3,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(8,3,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(9,3,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(10,3,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(11,1,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(12,1,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(13,1,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(14,1,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(15,1,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(16,4,'إيرادات الجلسات','إيرادات من جلسات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(17,4,'مبيعات الكافتيريا','إيرادات من مبيعات الكافتيريا',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(18,4,'خدمات إضافية','إيرادات من خدمات إضافية أخرى',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(19,4,'تأجير قاعات','إيرادات من تأجير القاعات للمناسبات',1,'2025-06-12 08:28:46','2025-06-12 08:28:46'),(20,4,'بيع أكسسوارات','إيرادات من بيع أكسسوارات الألعاب',1,'2025-06-12 08:28:46','2025-06-12 08:28:46');
/*!40000 ALTER TABLE `income_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_settings`
--

DROP TABLE IF EXISTS `invoice_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_settings`
--

LOCK TABLES `invoice_settings` WRITE;
/*!40000 ALTER TABLE `invoice_settings` DISABLE KEYS */;
INSERT INTO `invoice_settings` VALUES (1,1,'#2316d4','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي','#000000','شارع احمد يحيي','***********',0,'2025-06-10 16:53:56','2025-06-10 16:58:43'),(2,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 02:45:45','2025-06-12 02:45:45'),(3,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 02:52:09','2025-06-12 02:52:09'),(4,1,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',1,'2025-06-12 03:20:45','2025-06-12 03:20:45');
/*!40000 ALTER TABLE `invoice_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `time_cost` decimal(10,2) NOT NULL,
  `products_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`invoice_id`),
  KEY `session_id` (`session_id`),
  KEY `idx_invoices_payment_status` (`payment_status`),
  KEY `idx_invoices_client_id` (`client_id`),
  CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (20,35,'202506140035',15.00,5.00,20.00,1,'pending','2025-06-14 13:35:13',1),(21,36,'202506140036',0.00,10.00,10.00,1,'pending','2025-06-14 14:22:12',1),(22,37,'202506160037',10.00,5.00,15.00,1,'pending','2025-06-16 05:15:40',1),(23,38,'202506160038',15.00,0.00,15.00,1,'pending','2025-06-16 06:27:04',1);
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `monthly_attendance_stats`
--

DROP TABLE IF EXISTS `monthly_attendance_stats`;
/*!50001 DROP VIEW IF EXISTS `monthly_attendance_stats`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `monthly_attendance_stats` AS SELECT
 1 AS `employee_id`,
  1 AS `employee_name`,
  1 AS `client_id`,
  1 AS `year`,
  1 AS `month`,
  1 AS `total_shifts`,
  1 AS `present_days`,
  1 AS `absent_days`,
  1 AS `late_days`,
  1 AS `total_hours`,
  1 AS `total_overtime`,
  1 AS `avg_late_minutes`,
  1 AS `attendance_percentage` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_order_number` (`client_id`,`order_number`),
  KEY `customer_id` (`customer_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pages`
--

DROP TABLE IF EXISTS `pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `page_name` (`page_name`),
  KEY `idx_pages_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pages`
--

LOCK TABLES `pages` WRITE;
/*!40000 ALTER TABLE `pages` DISABLE KEYS */;
INSERT INTO `pages` VALUES (1,'dashboard','لوحة التحكم','dashboard.php','fas fa-tachometer-alt','main',1,'2025-06-12 16:25:00'),(2,'profile','الملف الشخصي','profile.php','fas fa-user','main',1,'2025-06-12 16:25:00'),(3,'devices','إدارة الأجهزة','devices.php','fas fa-gamepad','devices',1,'2025-06-12 16:25:00'),(4,'rooms','إدارة الغرف','rooms.php','fas fa-door-open','devices',1,'2025-06-12 16:25:00'),(5,'sessions','إدارة الجلسات','sessions.php','fas fa-play-circle','sessions',1,'2025-06-12 16:25:00'),(6,'customers','إدارة العملاء','customers.php','fas fa-users','customers',1,'2025-06-12 16:25:00'),(7,'cafeteria','إدارة الكافتيريا','cafeteria.php','fas fa-coffee','cafeteria',1,'2025-06-12 16:25:00'),(8,'employees','إدارة الموظفين','employees.php','fas fa-user-tie','employees',1,'2025-06-12 16:25:00'),(9,'reports','التقارير','reports.php','fas fa-chart-bar','reports',1,'2025-06-12 16:25:00'),(10,'finances','المالية','finances.php','fas fa-money-bill-wave','finances',1,'2025-06-12 16:25:00'),(11,'invoices','الفواتير','invoices.php','fas fa-file-invoice','invoices',1,'2025-06-12 16:25:00'),(12,'settings','الإعدادات','settings.php','fas fa-cog','settings',1,'2025-06-12 16:25:00'),(13,'shifts','الورديات','shifts.php','fas fa-clock','shifts',1,'2025-06-14 15:05:01'),(14,'attendance','الحضور والانصراف','attendance.php','fas fa-user-check','shifts',1,'2025-06-14 15:05:01'),(15,'shift_reports','تقارير الورديات','shift_reports.php','fas fa-chart-line','shifts',1,'2025-06-14 15:05:01');
/*!40000 ALTER TABLE `pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_status_log`
--

DROP TABLE IF EXISTS `payment_status_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_status_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `old_status` enum('pending','paid','cancelled') DEFAULT NULL,
  `new_status` enum('pending','paid','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_payment_log_invoice` (`invoice_id`),
  KEY `idx_payment_log_date` (`changed_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_status_log`
--

LOCK TABLES `payment_status_log` WRITE;
/*!40000 ALTER TABLE `payment_status_log` DISABLE KEYS */;
INSERT INTO `payment_status_log` VALUES (2,16,'pending','cancelled',1,'2025-06-14 08:40:49'),(3,15,'pending','cancelled',1,'2025-06-14 08:40:51'),(4,14,'pending','cancelled',1,'2025-06-14 08:40:53');
/*!40000 ALTER TABLE `payment_status_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(100) NOT NULL,
  `permission_label` varchar(200) NOT NULL,
  `permission_description` text DEFAULT NULL,
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `permission_name` (`permission_name`),
  KEY `idx_permissions_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'manage_devices','إدارة الأجهزة','إضافة وتعديل وحذف الأجهزة','devices',1,'2025-06-12 16:25:00'),(2,'view_devices','عرض الأجهزة','عرض قائمة الأجهزة فقط','devices',1,'2025-06-12 16:25:00'),(3,'manage_sessions','إدارة الجلسات','بدء وإنهاء وتعديل الجلسات','sessions',1,'2025-06-12 16:25:00'),(4,'view_sessions','عرض الجلسات','عرض قائمة الجلسات فقط','sessions',1,'2025-06-12 16:25:00'),(5,'manage_rooms','إدارة الغرف','إضافة وتعديل وحذف الغرف','rooms',1,'2025-06-12 16:25:00'),(6,'view_rooms','عرض الغرف','عرض قائمة الغرف فقط','rooms',1,'2025-06-12 16:25:00'),(7,'manage_customers','إدارة العملاء','إضافة وتعديل وحذف العملاء','customers',1,'2025-06-12 16:25:00'),(8,'view_customers','عرض العملاء','عرض قائمة العملاء فقط','customers',1,'2025-06-12 16:25:00'),(9,'manage_cafeteria','إدارة الكافتيريا','إدارة منتجات وطلبات الكافتيريا','cafeteria',1,'2025-06-12 16:25:00'),(10,'view_cafeteria','عرض الكافتيريا','عرض منتجات الكافتيريا فقط','cafeteria',1,'2025-06-12 16:25:00'),(11,'manage_employees','إدارة الموظفين','إضافة وتعديل وحذف الموظفين','employees',1,'2025-06-12 16:25:00'),(12,'view_employees','عرض الموظفين','عرض قائمة الموظفين فقط','employees',1,'2025-06-12 16:25:00'),(13,'view_reports','عرض التقارير','عرض التقارير والإحصائيات','reports',1,'2025-06-12 16:25:00'),(14,'view_finances','عرض المالية','عرض البيانات المالية','finances',1,'2025-06-12 16:25:00'),(15,'manage_finances','إدارة المالية','إدارة المصروفات والإيرادات','finances',1,'2025-06-12 16:25:00'),(16,'manage_invoices','إدارة الفواتير','إنشاء وتعديل الفواتير','invoices',1,'2025-06-12 16:25:00'),(17,'view_invoices','عرض الفواتير','عرض الفواتير فقط','invoices',1,'2025-06-12 16:25:00'),(18,'manage_settings','إدارة الإعدادات','تعديل إعدادات النظام','settings',1,'2025-06-12 16:25:00'),(19,'view_profile','عرض الملف الشخصي','عرض وتعديل الملف الشخصي','profile',1,'2025-06-12 16:25:00'),(20,'manage_shifts','إدارة الورديات','إنشاء وتعديل وحذف الورديات','shifts',1,'2025-06-14 15:05:01'),(21,'view_shifts','عرض الورديات','عرض جدول الورديات فقط','shifts',1,'2025-06-14 15:05:01'),(22,'manage_attendance','إدارة الحضور','تسجيل حضور وانصراف الموظفين','shifts',1,'2025-06-14 15:05:01'),(23,'view_attendance','عرض الحضور','عرض سجلات الحضور والغياب','shifts',1,'2025-06-14 15:05:01'),(24,'assign_shifts','تخصيص الورديات','تخصيص الموظفين للورديات','shifts',1,'2025-06-14 15:05:01'),(25,'view_shift_reports','تقارير الورديات','عرض تقارير الحضور والورديات','shifts',1,'2025-06-14 15:05:01');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  PRIMARY KEY (`product_id`),
  KEY `category_id` (`category_id`),
  KEY `section_id` (`section_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rooms`
--

DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_type` enum('VIP','regular','private') DEFAULT 'regular',
  `capacity` int(11) DEFAULT 1,
  `special_rate` decimal(8,2) DEFAULT 0.00,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`room_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rooms`
--

LOCK TABLES `rooms` WRITE;
/*!40000 ALTER TABLE `rooms` DISABLE KEYS */;
/*!40000 ALTER TABLE `rooms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sections`
--

DROP TABLE IF EXISTS `sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sections`
--

LOCK TABLES `sections` WRITE;
/*!40000 ALTER TABLE `sections` DISABLE KEYS */;
/*!40000 ALTER TABLE `sections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `session_products`
--

DROP TABLE IF EXISTS `session_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL COMMENT 'سعر المنتج وقت الإضافة',
  `total` decimal(10,2) GENERATED ALWAYS AS (`price` * `quantity`) STORED COMMENT 'إجمالي السعر',
  `created_by` int(11) DEFAULT NULL COMMENT 'معرف المستخدم الذي أضاف المنتج',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`),
  CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `session_products`
--

LOCK TABLES `session_products` WRITE;
/*!40000 ALTER TABLE `session_products` DISABLE KEYS */;
INSERT INTO `session_products` VALUES (46,35,13,1,'2025-06-14 13:34:58',5.00,5.00,NULL,NULL),(47,36,25,1,'2025-06-14 14:22:03',10.00,10.00,NULL,NULL),(48,37,13,1,'2025-06-16 05:10:06',5.00,5.00,NULL,NULL);
/*!40000 ALTER TABLE `session_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_id` int(11) DEFAULT NULL,
  `client_id` int(11) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`session_id`),
  KEY `customer_id` (`customer_id`),
  KEY `idx_sessions_client_id` (`client_id`),
  KEY `idx_sessions_status` (`status`),
  KEY `idx_sessions_device_id` (`device_id`),
  KEY `idx_sessions_start_time` (`start_time`),
  KEY `idx_sessions_device_status` (`device_id`,`status`),
  KEY `idx_sessions_client_device` (`client_id`,`device_id`),
  CONSTRAINT `sessions_device_fk` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `sessions_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES (35,16,'2025-06-14 16:22:48','2025-06-14 16:35:13','completed',1,1,'2025-06-14 13:22:48','2025-06-14 13:35:13',NULL,1,20.00,NULL,''),(36,16,'2025-06-14 17:21:54','2025-06-14 17:22:12','completed',1,1,'2025-06-14 14:21:54','2025-06-14 14:22:12',NULL,1,10.00,NULL,''),(37,19,'2025-06-16 08:09:50','2025-06-16 08:15:40','completed',1,1,'2025-06-16 05:09:50','2025-06-16 05:15:40',43,1,15.00,'2025-06-16 04:39:50',''),(38,16,'2025-06-16 08:27:40','2025-06-16 09:27:04','completed',1,1,'2025-06-16 05:27:40','2025-06-16 06:27:04',43,1,15.00,'2025-06-16 04:57:40','');
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_attendance`
--

DROP TABLE IF EXISTS `shift_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_attendance` (
  `attendance_id` int(11) NOT NULL AUTO_INCREMENT,
  `assignment_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  `break_start_time` timestamp NULL DEFAULT NULL,
  `break_end_time` timestamp NULL DEFAULT NULL,
  `actual_hours` decimal(4,2) DEFAULT 0.00,
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `break_hours` decimal(4,2) DEFAULT 0.00,
  `status` enum('absent','present','late','early_leave','overtime') DEFAULT 'absent',
  `late_minutes` int(11) DEFAULT 0,
  `early_leave_minutes` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `recorded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`attendance_id`),
  KEY `assignment_id` (`assignment_id`),
  KEY `idx_attendance_date` (`check_in_time`),
  KEY `idx_attendance_employee` (`employee_id`),
  KEY `idx_attendance_shift` (`shift_id`),
  CONSTRAINT `shift_attendance_ibfk_1` FOREIGN KEY (`assignment_id`) REFERENCES `employee_shifts` (`assignment_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_attendance`
--

LOCK TABLES `shift_attendance` WRITE;
/*!40000 ALTER TABLE `shift_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_settings`
--

DROP TABLE IF EXISTS `shift_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_name`),
  CONSTRAINT `shift_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_settings`
--

LOCK TABLES `shift_settings` WRITE;
/*!40000 ALTER TABLE `shift_settings` DISABLE KEYS */;
INSERT INTO `shift_settings` VALUES (1,1,'grace_period_minutes','15','فترة السماح للتأخير بالدقائق','2025-06-14 15:05:01','2025-06-14 15:05:01'),(2,1,'overtime_threshold_minutes','30','الحد الأدنى للعمل الإضافي بالدقائق','2025-06-14 15:05:01','2025-06-14 15:05:01'),(3,1,'auto_checkout_hours','12','تسجيل خروج تلقائي بعد عدد ساعات','2025-06-14 15:05:01','2025-06-14 15:05:01'),(4,1,'break_duration_default','30','مدة الاستراحة الافتراضية بالدقائق','2025-06-14 15:05:01','2025-06-14 15:05:01'),(5,1,'notification_before_shift','60','إشعار قبل بداية الوردية بالدقائق','2025-06-14 15:05:01','2025-06-14 15:05:01'),(6,1,'allow_early_checkin','30','السماح بتسجيل الدخول المبكر بالدقائق','2025-06-14 15:05:01','2025-06-14 15:05:01');
/*!40000 ALTER TABLE `shift_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_templates`
--

DROP TABLE IF EXISTS `shift_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_templates` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0 COMMENT 'وردية ليلية تمتد لليوم التالي',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`template_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `shift_templates_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_templates`
--

LOCK TABLES `shift_templates` WRITE;
/*!40000 ALTER TABLE `shift_templates` DISABLE KEYS */;
INSERT INTO `shift_templates` VALUES (1,1,'الوردية الصباحية','08:00:00','16:00:00',60,0,'وردية صباحية من 8 صباحاً إلى 4 عصراً',1,'2025-06-14 15:05:01','2025-06-14 15:05:01'),(2,1,'الوردية المسائية','16:00:00','00:00:00',60,0,'وردية مسائية من 4 عصراً إلى 12 منتصف الليل',1,'2025-06-14 15:05:01','2025-06-14 15:05:01'),(3,1,'الوردية الليلية','00:00:00','08:00:00',60,0,'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً',1,'2025-06-14 15:05:01','2025-06-14 15:05:01'),(4,1,'الوردية الصباحية','08:00:00','16:00:00',60,0,'وردية صباحية من 8 صباحاً إلى 4 عصراً',1,'2025-06-14 15:10:54','2025-06-14 15:10:54'),(5,1,'الوردية المسائية','16:00:00','00:00:00',60,0,'وردية مسائية من 4 عصراً إلى 12 منتصف الليل',1,'2025-06-14 15:10:54','2025-06-14 15:10:54'),(6,1,'الوردية الليلية','00:00:00','08:00:00',60,0,'وردية ليلية من 12 منتصف الليل إلى 8 صباحاً',1,'2025-06-14 15:10:54','2025-06-14 15:10:54');
/*!40000 ALTER TABLE `shift_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shifts`
--

DROP TABLE IF EXISTS `shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shifts` (
  `shift_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `shift_name` varchar(100) NOT NULL,
  `shift_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0,
  `max_employees` int(11) DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
  `min_employees` int(11) DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
  `status` enum('scheduled','active','completed','cancelled') DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`shift_id`),
  KEY `template_id` (`template_id`),
  KEY `idx_shifts_date` (`shift_date`),
  KEY `idx_shifts_status` (`status`),
  KEY `idx_shifts_client` (`client_id`),
  CONSTRAINT `shifts_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `shifts_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `shift_templates` (`template_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shifts`
--

LOCK TABLES `shifts` WRITE;
/*!40000 ALTER TABLE `shifts` DISABLE KEYS */;
INSERT INTO `shifts` VALUES (1,1,NULL,'وردية صباحية','2025-06-14','07:00:00','19:00:00',30,0,1,1,'scheduled','',1,'2025-06-14 15:33:19','2025-06-14 15:33:19'),(2,1,NULL,'وردية مسائية','2025-06-14','19:00:00','07:00:00',30,0,1,1,'scheduled','',1,'2025-06-14 15:33:55','2025-06-14 15:33:55');
/*!40000 ALTER TABLE `shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `shifts_detailed`
--

DROP TABLE IF EXISTS `shifts_detailed`;
/*!50001 DROP VIEW IF EXISTS `shifts_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `shifts_detailed` AS SELECT
 1 AS `shift_id`,
  1 AS `client_id`,
  1 AS `shift_name`,
  1 AS `shift_date`,
  1 AS `start_time`,
  1 AS `end_time`,
  1 AS `break_duration`,
  1 AS `is_overnight`,
  1 AS `max_employees`,
  1 AS `min_employees`,
  1 AS `shift_status`,
  1 AS `shift_notes`,
  1 AS `template_name`,
  1 AS `assigned_employees`,
  1 AS `confirmed_employees`,
  1 AS `present_employees`,
  1 AS `absent_employees` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `trial_cafeteria_items`
--

DROP TABLE IF EXISTS `trial_cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_cafeteria_items_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_cafeteria_items`
--

LOCK TABLES `trial_cafeteria_items` WRITE;
/*!40000 ALTER TABLE `trial_cafeteria_items` DISABLE KEYS */;
INSERT INTO `trial_cafeteria_items` VALUES (5,3,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-16 04:46:29'),(6,3,'قهوة',10.00,'مشروبات ساخنة','قهوة تركي','2025-06-16 04:46:29'),(7,3,'بيبسي',12.00,'مشروبات باردة','مشروب غازي','2025-06-16 04:46:29'),(8,3,'ساندويتش',25.00,'مأكولات','ساندويتش جبن','2025-06-16 04:46:29'),(9,4,'شاي',5.00,'مشروبات ساخنة','شاي سادة','2025-06-16 06:08:07'),(10,4,'قهوة',10.00,'مشروبات ساخنة','قهوة تركي','2025-06-16 06:08:07'),(11,4,'بيبسي',12.00,'مشروبات باردة','مشروب غازي','2025-06-16 06:08:07'),(12,4,'ساندويتش',25.00,'مأكولات','ساندويتش جبن','2025-06-16 06:08:07'),(13,4,'سوشي',15.00,'مأكولات','سوشي','2025-06-16 06:10:07');
/*!40000 ALTER TABLE `trial_cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_clients`
--

DROP TABLE IF EXISTS `trial_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_clients` (
  `trial_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `trial_start` timestamp NOT NULL DEFAULT current_timestamp(),
  `trial_end` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`trial_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_clients`
--

LOCK TABLES `trial_clients` WRITE;
/*!40000 ALTER TABLE `trial_clients` DISABLE KEYS */;
INSERT INTO `trial_clients` VALUES (3,'test','tester','<EMAIL>','***********','$2y$10$FocbmFwrZ/H6pYllxOJS3uX4SEi8A9coHOOdKrM87mlUFFZedH5.m','2025-06-16 04:46:29','2025-06-16 09:46:29',1,'2025-06-16 04:46:29'),(4,'egypto','Tamer','<EMAIL>','***********','$2y$10$7/nFVriZwrTbtUNVIhEs.eImIXPUypSoQpf.1Enh568tvf.MTDwPq','2025-06-16 06:08:07','2025-06-16 11:08:07',1,'2025-06-16 06:08:07');
/*!40000 ALTER TABLE `trial_clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_customers`
--

DROP TABLE IF EXISTS `trial_customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`customer_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_customers_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_customers`
--

LOCK TABLES `trial_customers` WRITE;
/*!40000 ALTER TABLE `trial_customers` DISABLE KEYS */;
INSERT INTO `trial_customers` VALUES (5,3,'أحمد محمد','01234567890','<EMAIL>','2025-06-16 04:46:29'),(6,3,'فاطمة علي','01234567891','<EMAIL>','2025-06-16 04:46:29'),(7,3,'محمد حسن','01234567892','<EMAIL>','2025-06-16 04:46:29'),(8,3,'سارة أحمد','01234567893','<EMAIL>','2025-06-16 04:46:29'),(9,4,'أحمد محمد','01234567890','<EMAIL>','2025-06-16 06:08:07'),(10,4,'فاطمة علي','01234567891','<EMAIL>','2025-06-16 06:08:07'),(11,4,'محمد حسن','01234567892','<EMAIL>','2025-06-16 06:08:07'),(12,4,'سارة أحمد','01234567893','<EMAIL>','2025-06-16 06:08:07');
/*!40000 ALTER TABLE `trial_customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_devices`
--

DROP TABLE IF EXISTS `trial_devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 15.00,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_devices_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_devices`
--

LOCK TABLES `trial_devices` WRITE;
/*!40000 ALTER TABLE `trial_devices` DISABLE KEYS */;
INSERT INTO `trial_devices` VALUES (5,3,'جهاز PS5 - تجريبي','PS5',20.00,'available','2025-06-16 04:46:29'),(6,3,'جهاز PS4 - تجريبي','PS4',15.00,'available','2025-06-16 04:46:29'),(7,3,'جهاز Xbox - تجريبي','Xbox',18.00,'available','2025-06-16 04:46:29'),(8,3,'جهاز PC - تجريبي','PC',25.00,'available','2025-06-16 04:46:29'),(9,4,'جهاز PS5 - تجريبي','PS5',20.00,'available','2025-06-16 06:08:07'),(10,4,'جهاز PS4 - تجريبي','PS4',15.00,'available','2025-06-16 06:08:07'),(11,4,'جهاز Xbox - تجريبي','Xbox',18.00,'available','2025-06-16 06:08:07'),(12,4,'جهاز PC - تجريبي','PC',25.00,'available','2025-06-16 06:08:07');
/*!40000 ALTER TABLE `trial_devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_sessions`
--

DROP TABLE IF EXISTS `trial_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` datetime DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`session_id`),
  KEY `trial_id` (`trial_id`),
  KEY `device_id` (`device_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `trial_sessions_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `trial_devices` (`device_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `trial_customers` (`customer_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_sessions`
--

LOCK TABLES `trial_sessions` WRITE;
/*!40000 ALTER TABLE `trial_sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `attendance_detailed`
--

/*!50001 DROP VIEW IF EXISTS `attendance_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `attendance_detailed` AS select `sa`.`attendance_id` AS `attendance_id`,`sa`.`shift_id` AS `shift_id`,`sa`.`employee_id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`role` AS `employee_role`,`s`.`shift_name` AS `shift_name`,`s`.`shift_date` AS `shift_date`,`s`.`start_time` AS `scheduled_start`,`s`.`end_time` AS `scheduled_end`,`sa`.`check_in_time` AS `check_in_time`,`sa`.`check_out_time` AS `check_out_time`,`sa`.`break_start_time` AS `break_start_time`,`sa`.`break_end_time` AS `break_end_time`,`sa`.`actual_hours` AS `actual_hours`,`sa`.`overtime_hours` AS `overtime_hours`,`sa`.`break_hours` AS `break_hours`,`sa`.`status` AS `attendance_status`,`sa`.`late_minutes` AS `late_minutes`,`sa`.`early_leave_minutes` AS `early_leave_minutes`,`sa`.`notes` AS `attendance_notes`,`es`.`role_in_shift` AS `role_in_shift`,`es`.`is_mandatory` AS `is_mandatory` from (((`shift_attendance` `sa` join `employees` `e` on(`sa`.`employee_id` = `e`.`id`)) join `shifts` `s` on(`sa`.`shift_id` = `s`.`shift_id`)) join `employee_shifts` `es` on(`sa`.`assignment_id` = `es`.`assignment_id`)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `employee_pages_detailed`
--

/*!50001 DROP VIEW IF EXISTS `employee_pages_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `employee_pages_detailed` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`role` AS `employee_role`,`e`.`custom_permissions` AS `custom_permissions`,`pg`.`page_id` AS `page_id`,`pg`.`page_name` AS `page_name`,`pg`.`page_label` AS `page_label`,`pg`.`page_url` AS `page_url`,`pg`.`page_icon` AS `page_icon`,`pg`.`category` AS `page_category`,`epg`.`granted_at` AS `granted_at` from ((`employees` `e` left join `employee_pages` `epg` on(`e`.`id` = `epg`.`employee_id`)) left join `pages` `pg` on(`epg`.`page_id` = `pg`.`page_id`)) where `e`.`is_active` = 1 and (`pg`.`is_active` = 1 or `pg`.`is_active` is null) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `employee_permissions_detailed`
--

/*!50001 DROP VIEW IF EXISTS `employee_permissions_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `employee_permissions_detailed` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`role` AS `employee_role`,`e`.`custom_permissions` AS `custom_permissions`,`p`.`permission_id` AS `permission_id`,`p`.`permission_name` AS `permission_name`,`p`.`permission_label` AS `permission_label`,`p`.`category` AS `permission_category`,`ep`.`granted_at` AS `granted_at` from ((`employees` `e` left join `employee_permissions` `ep` on(`e`.`id` = `ep`.`employee_id`)) left join `permissions` `p` on(`ep`.`permission_id` = `p`.`permission_id`)) where `e`.`is_active` = 1 and (`p`.`is_active` = 1 or `p`.`is_active` is null) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `monthly_attendance_stats`
--

/*!50001 DROP VIEW IF EXISTS `monthly_attendance_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `monthly_attendance_stats` AS select `e`.`id` AS `employee_id`,`e`.`name` AS `employee_name`,`e`.`client_id` AS `client_id`,year(`s`.`shift_date`) AS `year`,month(`s`.`shift_date`) AS `month`,count(`sa`.`attendance_id`) AS `total_shifts`,count(case when `sa`.`status` = 'present' then 1 end) AS `present_days`,count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_days`,count(case when `sa`.`status` = 'late' then 1 end) AS `late_days`,sum(`sa`.`actual_hours`) AS `total_hours`,sum(`sa`.`overtime_hours`) AS `total_overtime`,avg(`sa`.`late_minutes`) AS `avg_late_minutes`,count(case when `sa`.`status` = 'present' then 1 end) * 100.0 / count(`sa`.`attendance_id`) AS `attendance_percentage` from (((`employees` `e` join `employee_shifts` `es` on(`e`.`id` = `es`.`employee_id`)) join `shifts` `s` on(`es`.`shift_id` = `s`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) group by `e`.`id`,year(`s`.`shift_date`),month(`s`.`shift_date`) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `shifts_detailed`
--

/*!50001 DROP VIEW IF EXISTS `shifts_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `shifts_detailed` AS select `s`.`shift_id` AS `shift_id`,`s`.`client_id` AS `client_id`,`s`.`shift_name` AS `shift_name`,`s`.`shift_date` AS `shift_date`,`s`.`start_time` AS `start_time`,`s`.`end_time` AS `end_time`,`s`.`break_duration` AS `break_duration`,`s`.`is_overnight` AS `is_overnight`,`s`.`max_employees` AS `max_employees`,`s`.`min_employees` AS `min_employees`,`s`.`status` AS `shift_status`,`s`.`notes` AS `shift_notes`,`st`.`template_name` AS `template_name`,count(`es`.`assignment_id`) AS `assigned_employees`,count(case when `es`.`status` = 'confirmed' then 1 end) AS `confirmed_employees`,count(case when `sa`.`status` = 'present' then 1 end) AS `present_employees`,count(case when `sa`.`status` = 'absent' then 1 end) AS `absent_employees` from (((`shifts` `s` left join `shift_templates` `st` on(`s`.`template_id` = `st`.`template_id`)) left join `employee_shifts` `es` on(`s`.`shift_id` = `es`.`shift_id`)) left join `shift_attendance` `sa` on(`es`.`assignment_id` = `sa`.`assignment_id`)) group by `s`.`shift_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-16 10:20:02
