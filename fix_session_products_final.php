<?php
require_once 'config/database.php';

echo "<h1>الإصلاح النهائي لمشكلة session_products</h1>";

try {
    echo "<h2>1. فحص البنية الحالية لجدول session_products</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_price = false;
    $has_unit_price = false;
    $has_total_price = false;
    $price_nullable = false;
    
    foreach ($columns as $column) {
        $color = '';
        if (in_array($column['Field'], ['price', 'unit_price', 'total_price'])) {
            $color = 'background-color: #fff3cd;'; // أصفر فاتح للتمييز
        }
        
        echo "<tr style='$color'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'price') {
            $has_price = true;
            $price_nullable = ($column['Null'] === 'YES');
        }
        if ($column['Field'] === 'unit_price') {
            $has_unit_price = true;
        }
        if ($column['Field'] === 'total_price') {
            $has_total_price = true;
        }
    }
    echo "</table>";
    
    echo "<h2>2. تحليل المشكلة</h2>";
    echo "<ul>";
    echo "<li>حقل price موجود: " . ($has_price ? "✅ نعم" : "❌ لا") . "</li>";
    echo "<li>حقل unit_price موجود: " . ($has_unit_price ? "✅ نعم" : "❌ لا") . "</li>";
    echo "<li>حقل total_price موجود: " . ($has_total_price ? "✅ نعم" : "❌ لا") . "</li>";
    if ($has_price) {
        echo "<li>حقل price قابل للقيم الفارغة: " . ($price_nullable ? "✅ نعم" : "❌ لا") . "</li>";
    }
    echo "</ul>";
    
    echo "<h2>3. إصلاح المشكلة</h2>";
    
    // الحل 1: إضافة قيمة افتراضية لحقل price إذا كان موجوداً
    if ($has_price && !$price_nullable) {
        echo "<p style='color: orange;'>⚠️ إضافة قيمة افتراضية لحقل price...</p>";
        try {
            $pdo->exec("ALTER TABLE session_products MODIFY COLUMN price DECIMAL(10,2) DEFAULT 0.00");
            echo "<p style='color: green;'>✅ تم إضافة قيمة افتراضية لحقل price</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في تعديل حقل price: " . $e->getMessage() . "</p>";
        }
    }
    
    // الحل 2: إضافة الحقول المفقودة
    if (!$has_unit_price) {
        echo "<p style='color: orange;'>⚠️ إضافة حقل unit_price...</p>";
        try {
            $pdo->exec("ALTER TABLE session_products ADD COLUMN unit_price DECIMAL(10,2) DEFAULT 0.00 AFTER quantity");
            echo "<p style='color: green;'>✅ تم إضافة حقل unit_price</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في إضافة unit_price: " . $e->getMessage() . "</p>";
        }
    }
    
    if (!$has_total_price) {
        echo "<p style='color: orange;'>⚠️ إضافة حقل total_price...</p>";
        try {
            $pdo->exec("ALTER TABLE session_products ADD COLUMN total_price DECIMAL(10,2) DEFAULT 0.00 AFTER unit_price");
            echo "<p style='color: green;'>✅ تم إضافة حقل total_price</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في إضافة total_price: " . $e->getMessage() . "</p>";
        }
    }
    
    // الحل 3: مزامنة البيانات
    if ($has_price && $has_unit_price) {
        echo "<p style='color: blue;'>ℹ️ مزامنة البيانات بين price و unit_price...</p>";
        try {
            // نسخ البيانات من price إلى unit_price حيث unit_price فارغ
            $pdo->exec("UPDATE session_products SET unit_price = price WHERE unit_price = 0 AND price > 0");
            // نسخ البيانات من unit_price إلى price حيث price فارغ
            $pdo->exec("UPDATE session_products SET price = unit_price WHERE price = 0 AND unit_price > 0");
            echo "<p style='color: green;'>✅ تم مزامنة البيانات</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في مزامنة البيانات: " . $e->getMessage() . "</p>";
        }
    }
    
    // الحل 4: حساب total_price
    if ($has_total_price) {
        echo "<p style='color: blue;'>ℹ️ حساب total_price...</p>";
        try {
            $pdo->exec("UPDATE session_products SET total_price = unit_price * quantity WHERE total_price = 0");
            echo "<p style='color: green;'>✅ تم حساب total_price</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في حساب total_price: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. فحص البنية الجديدة</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $new_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($new_columns as $column) {
        $color = '';
        if (in_array($column['Field'], ['price', 'unit_price', 'total_price'])) {
            $color = 'background-color: #d4edda;'; // أخضر فاتح
        }
        
        echo "<tr style='$color'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>5. اختبار الإدراج</h2>";
    
    // البحث عن بيانات للاختبار
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        echo "<p style='color: orange;'>⚠️ إنشاء جلسة تجريبية...</p>";
        $stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
        $device = $stmt->fetch();
        if ($device) {
            $pdo->exec("INSERT INTO sessions (device_id, status, start_time) VALUES ({$device['device_id']}, 'active', NOW())");
            $session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة: $session_id</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة</p>";
            exit;
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام الجلسة: $session_id</p>";
    }
    
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        echo "<p style='color: orange;'>⚠️ إنشاء منتج تجريبي...</p>";
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج اختبار نهائي', 8.50, 'اختبار', 1)");
        $product_id = $pdo->lastInsertId();
        $product_price = 8.50;
        echo "<p style='color: green;'>✅ تم إنشاء منتج: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_price = $product['price'];
        echo "<p style='color: blue;'>ℹ️ استخدام المنتج: {$product['name']} ($product_id)</p>";
    }
    
    // اختبار 1: الطريقة الجديدة (مع unit_price و total_price)
    echo "<h3>اختبار 1: الطريقة الجديدة</h3>";
    try {
        $quantity = 2;
        $unit_price = $product_price;
        $total_price = $unit_price * $quantity;
        
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);
        
        $test_id1 = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ نجح الاختبار 1 - ID: $test_id1</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل الاختبار 1: " . $e->getMessage() . "</p>";
    }
    
    // اختبار 2: الطريقة القديمة (مع price فقط)
    echo "<h3>اختبار 2: الطريقة القديمة</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$session_id, $product_id, 1, $product_price]);
        
        $test_id2 = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ نجح الاختبار 2 - ID: $test_id2</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل الاختبار 2: " . $e->getMessage() . "</p>";
    }
    
    // اختبار 3: بدون تحديد أي حقل سعر (اعتماد على القيم الافتراضية)
    echo "<h3>اختبار 3: القيم الافتراضية</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$session_id, $product_id, 1]);
        
        $test_id3 = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ نجح الاختبار 3 - ID: $test_id3</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل الاختبار 3: " . $e->getMessage() . "</p>";
    }
    
    // عرض البيانات المُدرجة
    echo "<h3>البيانات المُدرجة:</h3>";
    $stmt = $pdo->prepare("SELECT * FROM session_products WHERE session_id = ? ORDER BY id DESC LIMIT 3");
    $stmt->execute([$session_id]);
    $inserted_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($inserted_data) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Session ID</th><th>Product ID</th><th>Quantity</th><th>Price</th><th>Unit Price</th><th>Total Price</th></tr>";
        foreach ($inserted_data as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['session_id']}</td>";
            echo "<td>{$row['product_id']}</td>";
            echo "<td>{$row['quantity']}</td>";
            echo "<td>" . ($row['price'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['unit_price'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['total_price'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // تنظيف البيانات التجريبية
    if (isset($test_id1)) $pdo->exec("DELETE FROM session_products WHERE id = $test_id1");
    if (isset($test_id2)) $pdo->exec("DELETE FROM session_products WHERE id = $test_id2");
    if (isset($test_id3)) $pdo->exec("DELETE FROM session_products WHERE id = $test_id3");
    echo "<p style='color: blue;'>ℹ️ تم حذف البيانات التجريبية</p>";
    
    echo "<h2>6. الخلاصة النهائية</h2>";
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 تم إصلاح المشكلة بنجاح!</h3>";
    echo "<p><strong>المشكلة الأصلية:</strong> SQLSTATE[HY000]: General error: 1364 Field 'unit_price' doesn't have a default value</p>";
    echo "<p><strong>الحل المطبق:</strong></p>";
    echo "<ul>";
    echo "<li>إضافة قيم افتراضية لجميع حقول الأسعار</li>";
    echo "<li>توحيد بنية الجدول لتشمل price و unit_price و total_price</li>";
    echo "<li>مزامنة البيانات الموجودة</li>";
    echo "<li>اختبار جميع طرق الإدراج</li>";
    echo "</ul>";
    echo "<p><strong>النتيجة:</strong> يمكن الآن إضافة المنتجات بأي من الطرق دون أخطاء</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ حدث خطأ عام: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
