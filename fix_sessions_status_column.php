<?php
/**
 * Fix Sessions Status Column - PlayGood
 * This script fixes the missing s.status column error in SQL queries
 */

require_once 'config/database.php';

echo "<h1>إصلاح عمود الحالة في جدول الجلسات</h1>";

try {
    // فحص هيكل جدول sessions
    echo "<h2>1. فحص هيكل جدول sessions</h2>";
    
    $stmt = $pdo->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th><th>إضافي</th></tr>";
    
    $status_column_exists = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'status') {
            $status_column_exists = true;
        }
    }
    echo "</table>";
    
    if ($status_column_exists) {
        echo "<p style='color: green;'>✅ عمود status موجود في جدول sessions</p>";
    } else {
        echo "<p style='color: red;'>❌ عمود status غير موجود في جدول sessions</p>";
        
        // إضافة عمود status
        echo "<h3>إضافة عمود status</h3>";
        $pdo->exec("ALTER TABLE sessions ADD COLUMN status ENUM('active','completed','cancelled') NOT NULL DEFAULT 'active'");
        echo "<p style='color: green;'>✅ تم إضافة عمود status بنجاح</p>";
    }
    
    // فحص وإضافة الأعمدة المفقودة الأخرى
    echo "<h2>2. فحص وإضافة الأعمدة المفقودة</h2>";
    
    $required_columns = [
        'client_id' => 'INT NULL',
        'customer_id' => 'INT NULL',
        'total_cost' => 'DECIMAL(10,2) DEFAULT 0.00',
        'game_type' => "ENUM('single','multiplayer') DEFAULT 'single'",
        'created_by' => 'INT NULL',
        'updated_by' => 'INT NULL'
    ];
    
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            echo "<p style='color: orange;'>⚠️ عمود $column_name غير موجود - جاري الإضافة...</p>";
            try {
                $pdo->exec("ALTER TABLE sessions ADD COLUMN $column_name $column_definition");
                echo "<p style='color: green;'>✅ تم إضافة عمود $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ عمود $column_name موجود</p>";
        }
    }
    
    // اختبار الاستعلامات المشكلة
    echo "<h2>3. اختبار الاستعلامات</h2>";
    
    // اختبار استعلام customers.php
    echo "<h3>اختبار استعلام customers.php</h3>";
    try {
        $test_query = "
            SELECT 
                c.*,
                COUNT(DISTINCT s.session_id) as sessions_count,
                COALESCE(SUM(CASE WHEN i.invoice_id IS NOT NULL THEN i.total_cost ELSE 0 END), 0) as total_spent
            FROM customers c
            LEFT JOIN sessions s ON c.customer_id = s.customer_id 
                AND (s.status = 'completed' OR s.status IS NULL)
            LEFT JOIN invoices i ON s.session_id = i.session_id
            WHERE c.client_id = 1
            GROUP BY 
                c.customer_id,
                c.name,
                c.phone,
                c.email,
                c.notes,
                c.created_at
            ORDER BY c.created_at DESC
            LIMIT 1
        ";
        $stmt = $pdo->query($test_query);
        echo "<p style='color: green;'>✅ استعلام customers.php يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام customers.php: " . $e->getMessage() . "</p>";
    }
    
    // اختبار استعلام devices.php
    echo "<h3>اختبار استعلام devices.php</h3>";
    try {
        $test_query = "
            SELECT d.*, r.room_name,
                (SELECT COUNT(*) FROM sessions s WHERE s.device_id = d.device_id AND (s.status = 'active' OR s.status IS NULL)) as active_sessions_count
            FROM devices d
            LEFT JOIN rooms r ON d.room_id = r.room_id
            WHERE d.client_id = 1
            ORDER BY d.device_name
            LIMIT 1
        ";
        $stmt = $pdo->query($test_query);
        echo "<p style='color: green;'>✅ استعلام devices.php يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام devices.php: " . $e->getMessage() . "</p>";
    }
    
    // اختبار استعلام sessions.php
    echo "<h3>اختبار استعلام sessions.php</h3>";
    try {
        $test_query = "
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
                   COALESCE(s.game_type, 'single') as game_type
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            WHERE d.client_id = 1 AND (s.status = 'active' OR s.status IS NULL)
            ORDER BY s.start_time DESC
            LIMIT 1
        ";
        $stmt = $pdo->query($test_query);
        echo "<p style='color: green;'>✅ استعلام sessions.php يعمل بشكل صحيح</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام sessions.php: " . $e->getMessage() . "</p>";
    }
    
    // إضافة فهارس لتحسين الأداء
    echo "<h2>4. إضافة فهارس لتحسين الأداء</h2>";
    
    $indexes = [
        'idx_sessions_status' => 'CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)',
        'idx_sessions_client_id' => 'CREATE INDEX IF NOT EXISTS idx_sessions_client_id ON sessions(client_id)',
        'idx_sessions_device_id' => 'CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON sessions(device_id)',
        'idx_sessions_customer_id' => 'CREATE INDEX IF NOT EXISTS idx_sessions_customer_id ON sessions(customer_id)',
        'idx_sessions_start_time' => 'CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)'
    ];
    
    foreach ($indexes as $index_name => $index_sql) {
        try {
            $pdo->exec($index_sql);
            echo "<p style='color: green;'>✅ تم إنشاء فهرس $index_name</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ فهرس $index_name: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>5. ملخص الإصلاحات</h2>";
    echo "<p style='color: green; font-weight: bold;'>✅ تم إصلاح جميع المشاكل المتعلقة بعمود s.status في جدول sessions</p>";
    echo "<p>يمكنك الآن الوصول إلى الصفحات التالية بدون أخطاء:</p>";
    echo "<ul>";
    echo "<li><a href='client/customers.php'>صفحة العملاء</a></li>";
    echo "<li><a href='client/devices.php'>صفحة الأجهزة</a></li>";
    echo "<li><a href='client/sessions.php'>صفحة الجلسات</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}
?>
