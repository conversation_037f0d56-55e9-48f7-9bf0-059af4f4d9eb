<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

// تعيين client_id للاختبار
$client_id = 1;

echo "<h2>إضافة عملاء تجريبيين</h2>";

try {
    // التحقق من وجود جدول customers
    $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($table_check->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول customers غير موجود - جاري الإنشاء...</p>";
        
        // إنشاء الجدول
        $create_table = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_customers_client_id (client_id)
            )
        ";
        $pdo->exec($create_table);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
    }
    
    // التحقق من وجود عملاء
    $customers_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customers WHERE client_id = ?");
    $customers_stmt->execute([$client_id]);
    $customer_count = $customers_stmt->fetchColumn();
    
    echo "<p>عدد العملاء الحاليين للعميل $client_id: <strong>$customer_count</strong></p>";
    
    // إضافة عملاء تجريبيين
    $sample_customers = [
        ['أحمد محمد علي', '01234567890', '<EMAIL>'],
        ['فاطمة حسن أحمد', '01987654321', '<EMAIL>'],
        ['محمد حسن محمود', '01555666777', '<EMAIL>'],
        ['سارة أحمد علي', '01444555666', '<EMAIL>'],
        ['علي محمود حسن', '01333444555', '<EMAIL>'],
        ['نور الدين محمد', '01666777888', '<EMAIL>'],
        ['ليلى حسام الدين', '01777888999', '<EMAIL>'],
        ['يوسف أحمد محمد', '01888999000', '<EMAIL>'],
        ['مريم علي حسن', '01999000111', '<EMAIL>'],
        ['خالد محمود أحمد', '01000111222', '<EMAIL>']
    ];
    
    $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, ?)");
    $added_count = 0;
    
    foreach ($sample_customers as $customer) {
        // التحقق من عدم وجود العميل مسبقاً
        $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE name = ? AND client_id = ?");
        $check_stmt->execute([$customer[0], $client_id]);
        
        if ($check_stmt->fetchColumn() == 0) {
            $insert_stmt->execute([$customer[0], $customer[1], $customer[2], $client_id]);
            $added_count++;
            echo "<p style='color: green;'>✅ تم إضافة: " . htmlspecialchars($customer[0]) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ موجود مسبقاً: " . htmlspecialchars($customer[0]) . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>ملخص النتائج</h3>";
    echo "<p><strong>تم إضافة $added_count عميل جديد</strong></p>";
    
    // عرض جميع العملاء
    $all_customers_stmt = $pdo->prepare("SELECT customer_id, name, phone, email FROM customers WHERE client_id = ? ORDER BY name");
    $all_customers_stmt->execute([$client_id]);
    $all_customers = $all_customers_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>جميع العملاء الموجودين حالياً (" . count($all_customers) . " عميل)</h3>";
    
    if (count($all_customers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>ID</th><th style='padding: 8px;'>الاسم</th><th style='padding: 8px;'>الهاتف</th><th style='padding: 8px;'>البريد</th>";
        echo "</tr>";
        
        foreach ($all_customers as $customer) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone'] ?? '') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['email'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<p><a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى صفحة الجلسات</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
