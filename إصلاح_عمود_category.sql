-- =====================================================
-- إصلاح خطأ عمود category في جدول cafeteria_items
-- =====================================================
-- هذا السكريبت يحل مشكلة العمود المفقود 'category'
-- الذي يسبب خطأ SQLSTATE[42S22] في cafeteria.php
-- =====================================================

USE `station`;

-- التحقق من وجود قاعدة البيانات
SELECT 'بدء عملية الإصلاح...' AS status;

-- التحقق من وجود العمود category
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE()
    AND table_name = 'cafeteria_items' 
    AND column_name = 'category'
);

SELECT 
    CASE 
        WHEN @column_exists > 0 THEN 'العمود category موجود بالفعل'
        ELSE 'العمود category مفقود - سيتم إضافته'
    END AS column_status;

-- إضافة العمود إذا لم يكن موجوداً
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE cafeteria_items ADD COLUMN category varchar(100) DEFAULT NULL AFTER price',
    'SELECT "العمود category موجود بالفعل" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- التحقق من وجود جدول categories
SET @categories_table_exists = (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE()
    AND table_name = 'categories'
);

-- إنشاء جدول categories إذا لم يكن موجوداً
SET @create_categories_sql = IF(@categories_table_exists = 0, 
    'CREATE TABLE categories (
        category_id int(11) NOT NULL AUTO_INCREMENT,
        client_id int(11) NOT NULL DEFAULT 1,
        name varchar(100) NOT NULL,
        description text DEFAULT NULL,
        icon varchar(50) DEFAULT "fas fa-tag",
        color varchar(7) DEFAULT "#007bff",
        sort_order int(11) DEFAULT 0,
        is_active tinyint(1) DEFAULT 1,
        created_at timestamp NOT NULL DEFAULT current_timestamp(),
        updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (category_id),
        INDEX idx_category_client (client_id),
        INDEX idx_category_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci',
    'SELECT "جدول categories موجود بالفعل" AS message'
);

PREPARE stmt2 FROM @create_categories_sql;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- إضافة تصنيفات افتراضية إذا كان الجدول فارغاً
INSERT IGNORE INTO categories (name, description, client_id) VALUES
('مشروبات', 'المشروبات الساخنة والباردة', 1),
('وجبات خفيفة', 'الوجبات الخفيفة والمقبلات', 1),
('حلويات', 'الحلويات والكيك', 1),
('مأكولات', 'الوجبات الرئيسية', 1),
('عصائر', 'العصائر الطبيعية والمثلجة', 1);

-- تحديث العمود category بناءً على category_id إذا كان فارغاً
UPDATE cafeteria_items ci
LEFT JOIN categories cat ON ci.category_id = cat.category_id
SET ci.category = cat.name
WHERE ci.category IS NULL AND cat.name IS NOT NULL;

-- تحديث المنتجات التي ليس لها تصنيف
UPDATE cafeteria_items 
SET category = 'عام' 
WHERE category IS NULL OR category = '';

-- إضافة فهرس للعمود الجديد
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE()
    AND table_name = 'cafeteria_items' 
    AND index_name = 'idx_cafeteria_category'
);

SET @create_index_sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_cafeteria_category ON cafeteria_items(category)',
    'SELECT "الفهرس idx_cafeteria_category موجود بالفعل" AS message'
);

PREPARE stmt3 FROM @create_index_sql;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- التحقق من وجود عمود client_id في cafeteria_items
SET @client_id_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE()
    AND table_name = 'cafeteria_items' 
    AND column_name = 'client_id'
);

-- إضافة عمود client_id إذا لم يكن موجوداً
SET @add_client_id_sql = IF(@client_id_exists = 0, 
    'ALTER TABLE cafeteria_items ADD COLUMN client_id int(11) NOT NULL DEFAULT 1 AFTER id',
    'SELECT "عمود client_id موجود بالفعل" AS message'
);

PREPARE stmt4 FROM @add_client_id_sql;
EXECUTE stmt4;
DEALLOCATE PREPARE stmt4;

-- إضافة فهرس لعمود client_id
SET @client_index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE()
    AND table_name = 'cafeteria_items' 
    AND index_name = 'idx_item_client'
);

SET @create_client_index_sql = IF(@client_index_exists = 0, 
    'CREATE INDEX idx_item_client ON cafeteria_items(client_id)',
    'SELECT "الفهرس idx_item_client موجود بالفعل" AS message'
);

PREPARE stmt5 FROM @create_client_index_sql;
EXECUTE stmt5;
DEALLOCATE PREPARE stmt5;

-- عرض النتيجة النهائية
SELECT 
    '✅ تم إصلاح مشكلة عمود category بنجاح' AS message,
    COUNT(*) AS total_items,
    COUNT(CASE WHEN category IS NOT NULL AND category != '' THEN 1 END) AS items_with_category,
    COUNT(DISTINCT category) AS unique_categories
FROM cafeteria_items;

-- عرض التصنيفات المتاحة
SELECT 
    '📋 التصنيفات المتاحة:' AS section,
    name AS category_name,
    COUNT(ci.id) AS items_count
FROM categories c
LEFT JOIN cafeteria_items ci ON c.name = ci.category
GROUP BY c.category_id, c.name
ORDER BY c.name;

-- عرض حالة الجداول
SELECT 
    '📊 حالة الجداول:' AS section,
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND table_name IN ('cafeteria_items', 'categories')
ORDER BY table_name;

SELECT '🎉 تم الانتهاء من عملية الإصلاح بنجاح!' AS final_message;
