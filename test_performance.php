<?php
/**
 * اختبار أداء الموقع - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول إذا لم يكن مسجل
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    $_SESSION['user_id'] = 1;
}

echo "<h1>اختبار أداء الموقع - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// قياس أداء قاعدة البيانات
echo "<h2>1. اختبار أداء قاعدة البيانات</h2>";

try {
    require_once 'config/database.php';
    
    $start_time = microtime(true);
    
    // اختبار اتصال بسيط
    $simple_query = $pdo->query("SELECT 1");
    $db_connection_time = microtime(true) - $start_time;
    
    echo "<p>⏱️ <strong>وقت الاتصال بقاعدة البيانات:</strong> " . round($db_connection_time * 1000, 2) . " ms</p>";
    
    if ($db_connection_time > 0.1) {
        echo "<p style='color: red;'>⚠️ الاتصال بقاعدة البيانات بطيء (أكثر من 100ms)</p>";
    } else {
        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات سريع</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار أداء API إحصائيات لوحة التحكم
echo "<h2>2. اختبار أداء API إحصائيات لوحة التحكم</h2>";

$api_tests = [
    'get_dashboard_stats.php' => 'إحصائيات لوحة التحكم',
    'update_payment_status.php' => 'تحديث حالة الدفع (GET)',
    'update_invoice_settings.php' => 'إعدادات الفاتورة (GET)'
];

foreach ($api_tests as $api_file => $description) {
    $start_time = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/playgood/client/api/$api_file");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Cookie: ' . session_name() . '=' . session_id()
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    curl_close($ch);
    
    $api_time = microtime(true) - $start_time;
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<h4>$description</h4>";
    echo "<p>⏱️ <strong>وقت الاستجابة:</strong> " . round($api_time * 1000, 2) . " ms</p>";
    echo "<p>🌐 <strong>HTTP Code:</strong> $http_code</p>";
    
    if ($api_time > 1) {
        echo "<p style='color: red;'>⚠️ API بطيء جداً (أكثر من 1 ثانية)</p>";
    } elseif ($api_time > 0.5) {
        echo "<p style='color: orange;'>⚠️ API بطيء نسبياً (أكثر من 500ms)</p>";
    } else {
        echo "<p style='color: green;'>✅ API سريع</p>";
    }
    
    if ($response && $http_code == 200) {
        $json_data = json_decode($response, true);
        if ($json_data && isset($json_data['execution_time'])) {
            echo "<p>⚡ <strong>وقت تنفيذ الخادم:</strong> " . $json_data['execution_time'] . "</p>";
        }
    }
    echo "</div>";
}

// اختبار أداء الصفحات الرئيسية
echo "<h2>3. اختبار أداء الصفحات الرئيسية</h2>";

$pages_to_test = [
    'client/dashboard.php' => 'لوحة التحكم',
    'client/sessions.php' => 'صفحة الجلسات',
    'client/invoices.php' => 'صفحة الفواتير',
    'client/devices.php' => 'صفحة الأجهزة'
];

foreach ($pages_to_test as $page => $name) {
    $start_time = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/playgood/$page");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Cookie: ' . session_name() . '=' . session_id()
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $size = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    curl_close($ch);
    
    $page_time = microtime(true) - $start_time;
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<h4>$name</h4>";
    echo "<p>⏱️ <strong>وقت التحميل:</strong> " . round($page_time * 1000, 2) . " ms</p>";
    echo "<p>🌐 <strong>HTTP Code:</strong> $http_code</p>";
    echo "<p>📦 <strong>حجم الصفحة:</strong> " . round($size / 1024, 2) . " KB</p>";
    
    if ($page_time > 3) {
        echo "<p style='color: red;'>⚠️ الصفحة بطيئة جداً (أكثر من 3 ثواني)</p>";
    } elseif ($page_time > 1.5) {
        echo "<p style='color: orange;'>⚠️ الصفحة بطيئة نسبياً (أكثر من 1.5 ثانية)</p>";
    } else {
        echo "<p style='color: green;'>✅ الصفحة سريعة</p>";
    }
    echo "</div>";
}

// اختبار أداء الاستعلامات المعقدة
echo "<h2>4. اختبار أداء الاستعلامات</h2>";

try {
    $client_id = $_SESSION['client_id'];
    
    // اختبار استعلام الجلسات النشطة
    $start_time = microtime(true);
    $active_sessions_query = $pdo->prepare("
        SELECT COUNT(*) as active_sessions
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ? AND s.status = 'active'
    ");
    $active_sessions_query->execute([$client_id]);
    $active_sessions_time = microtime(true) - $start_time;
    
    echo "<p>⏱️ <strong>استعلام الجلسات النشطة:</strong> " . round($active_sessions_time * 1000, 2) . " ms</p>";
    
    // اختبار استعلام إحصائيات اليوم
    $start_time = microtime(true);
    $today_stats_query = $pdo->prepare("
        SELECT
            COUNT(s.session_id) as total_sessions,
            SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_sessions,
            COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as today_income
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        WHERE d.client_id = ? AND DATE(s.start_time) = CURRENT_DATE
    ");
    $today_stats_query->execute([$client_id]);
    $today_stats_time = microtime(true) - $start_time;
    
    echo "<p>⏱️ <strong>استعلام إحصائيات اليوم:</strong> " . round($today_stats_time * 1000, 2) . " ms</p>";
    
    // اختبار استعلام الأجهزة
    $start_time = microtime(true);
    $devices_query = $pdo->prepare("SELECT COUNT(*) as total FROM devices WHERE client_id = ?");
    $devices_query->execute([$client_id]);
    $devices_time = microtime(true) - $start_time;
    
    echo "<p>⏱️ <strong>استعلام الأجهزة:</strong> " . round($devices_time * 1000, 2) . " ms</p>";
    
    $total_query_time = $active_sessions_time + $today_stats_time + $devices_time;
    echo "<p><strong>إجمالي وقت الاستعلامات:</strong> " . round($total_query_time * 1000, 2) . " ms</p>";
    
    if ($total_query_time > 0.5) {
        echo "<p style='color: red;'>⚠️ الاستعلامات بطيئة - يحتاج تحسين</p>";
    } else {
        echo "<p style='color: green;'>✅ الاستعلامات سريعة</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</p>";
}

// توصيات التحسين
echo "<h2>5. توصيات التحسين</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>💡 نصائح لتحسين الأداء:</h3>";
echo "<ul>";
echo "<li><strong>فهرسة قاعدة البيانات:</strong> تأكد من وجود فهارس على الأعمدة المستخدمة في WHERE و JOIN</li>";
echo "<li><strong>تحسين الاستعلامات:</strong> استخدم LIMIT في الاستعلامات الكبيرة</li>";
echo "<li><strong>التخزين المؤقت:</strong> استخدم Redis أو Memcached للبيانات المتكررة</li>";
echo "<li><strong>ضغط الصور:</strong> تأكد من ضغط الصور والملفات الثابتة</li>";
echo "<li><strong>تحسين PHP:</strong> استخدم OPcache لتسريع PHP</li>";
echo "<li><strong>CDN:</strong> استخدم شبكة توصيل المحتوى للملفات الثابتة</li>";
echo "</ul>";
echo "</div>";

// أزرار الاختبار السريع
echo "<h2>6. اختبارات سريعة</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='?clear_cache=1' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>مسح التخزين المؤقت</a>";
echo "<a href='?optimize_db=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تحسين قاعدة البيانات</a>";
echo "<a href='?' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إعادة الاختبار</a>";
echo "</div>";

// تنفيذ الإجراءات
if (isset($_GET['clear_cache'])) {
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<p style='color: green;'>✅ تم مسح OPcache</p>";
    }
    
    // مسح ملفات التخزين المؤقت إذا كانت موجودة
    $cache_files = glob('cache/*.cache');
    foreach ($cache_files as $file) {
        unlink($file);
    }
    echo "<p style='color: green;'>✅ تم مسح ملفات التخزين المؤقت</p>";
}

if (isset($_GET['optimize_db'])) {
    try {
        $tables = ['sessions', 'devices', 'customers', 'invoices'];
        foreach ($tables as $table) {
            $pdo->exec("OPTIMIZE TABLE $table");
        }
        echo "<p style='color: green;'>✅ تم تحسين جداول قاعدة البيانات</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في تحسين قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>7. روابط مفيدة</h2>";
echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";
echo "<p><a href='test_active_sessions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الجلسات النشطة</a></p>";
echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>سكريپت الإصلاح الشامل</a></p>";

echo "</div>";
?>
