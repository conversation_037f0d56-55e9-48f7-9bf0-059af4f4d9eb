<?php
/**
 * سكريپت بسيط لإنشاء قاعدة البيانات
 * يستخدم الإعدادات المباشرة بدون تعقيد
 */

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد قاعدة البيانات - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>
                        <i class='fas fa-database me-2'></i>
                        إعداد قاعدة البيانات
                    </h3>
                </div>
                <div class='card-body'>";

// إعدادات قاعدة البيانات المباشرة
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'station';

echo "<div class='alert alert-info'>
        <h6>الإعدادات المستخدمة:</h6>
        <ul class='mb-0'>
            <li>الخادم: <strong>$host</strong></li>
            <li>المستخدم: <strong>$username</strong></li>
            <li>كلمة المرور: <strong>" . (empty($password) ? 'فارغة' : 'محددة') . "</strong></li>
            <li>قاعدة البيانات: <strong>$dbname</strong></li>
        </ul>
      </div>";

try {
    echo "<h5>الخطوة 1: اختبار الاتصال بخادم MySQL</h5>";
    
    // محاولة الاتصال بخادم MySQL
    $pdo_server = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم الاتصال بخادم MySQL بنجاح</p>";
    
    // فحص إصدار MySQL
    $version = $pdo_server->query('SELECT VERSION()')->fetchColumn();
    echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>إصدار MySQL: $version</p>";
    
    echo "<h5>الخطوة 2: إنشاء قاعدة البيانات</h5>";
    
    // إنشاء قاعدة البيانات
    $pdo_server->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='text-success'><i class='fas fa-plus me-2'></i>تم إنشاء قاعدة البيانات '$dbname' بنجاح</p>";
    
    echo "<h5>الخطوة 3: الاتصال بقاعدة البيانات</h5>";
    
    // الاتصال بقاعدة البيانات المنشأة
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='text-success'><i class='fas fa-link me-2'></i>تم الاتصال بقاعدة البيانات '$dbname' بنجاح</p>";
    
    echo "<h5>الخطوة 4: فحص ملف station.sql</h5>";
    
    $sql_file = 'station.sql';
    if (!file_exists($sql_file)) {
        echo "<div class='alert alert-danger'>
                <h6><i class='fas fa-exclamation-triangle me-2'></i>ملف station.sql غير موجود!</h6>
                <p>يرجى التأكد من وجود ملف station.sql في مجلد playgood</p>
              </div>";
    } else {
        $file_size = filesize($sql_file);
        echo "<p class='text-success'><i class='fas fa-file-alt me-2'></i>تم العثور على ملف station.sql (حجم: " . number_format($file_size) . " بايت)</p>";
        
        echo "<h5>الخطوة 5: استيراد البيانات من ملف SQL</h5>";
        
        // قراءة ملف SQL
        $sql_content = file_get_contents($sql_file);
        
        // تنظيف المحتوى
        $sql_content = preg_replace('/^--.*$/m', '', $sql_content);
        $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        $queries = array_filter($queries, function($query) {
            $query = trim($query);
            return !empty($query) && 
                   !preg_match('/^(SET|START|COMMIT)/i', $query);
        });
        
        echo "<p class='text-info'><i class='fas fa-list me-2'></i>تم العثور على " . count($queries) . " استعلام</p>";
        
        // تعطيل فحص المفاتيح الخارجية
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        $pdo->exec("SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO'");
        
        echo "<div class='progress mb-3'>
                <div class='progress-bar' role='progressbar' style='width: 0%' id='progressBar'>0%</div>
              </div>";
        
        echo "<div id='logOutput' style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;'>";
        
        $executed = 0;
        $errors = 0;
        $total = count($queries);
        
        foreach ($queries as $index => $query) {
            $query = trim($query);
            if (empty($query)) continue;
            
            try {
                $pdo->exec($query);
                $executed++;
                
                // تحديث شريط التقدم
                $progress = round(($executed / $total) * 100);
                echo "<script>
                        document.getElementById('progressBar').style.width = '$progress%';
                        document.getElementById('progressBar').textContent = '$progress%';
                      </script>";
                
                // عرض تقدم العملية
                if ($executed % 10 == 0) {
                    echo "<small class='text-success'>تم تنفيذ $executed من $total استعلام...</small><br>";
                    echo "<script>document.getElementById('logOutput').scrollTop = document.getElementById('logOutput').scrollHeight;</script>";
                    flush();
                }
                
            } catch (PDOException $e) {
                $errors++;
                if ($errors <= 5) { // عرض أول 5 أخطاء فقط
                    echo "<small class='text-warning'>تحذير في الاستعلام " . ($index + 1) . ": " . htmlspecialchars(substr($e->getMessage(), 0, 100)) . "...</small><br>";
                }
            }
        }
        
        echo "</div>";
        
        // إعادة تفعيل فحص المفاتيح الخارجية
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        echo "<div class='alert alert-success mt-3'>
                <h4><i class='fas fa-check-circle me-2'></i>تم استيراد قاعدة البيانات بنجاح! 🎉</h4>
                <ul class='mb-0'>
                    <li>تم تنفيذ: <strong>$executed</strong> استعلام</li>
                    <li>التحذيرات: <strong>$errors</strong></li>
                </ul>
              </div>";
        
        // فحص الجداول المنشأة
        $tables = $pdo->query('SHOW TABLES')->fetchAll(PDO::FETCH_COLUMN);
        echo "<p class='text-info'><i class='fas fa-table me-2'></i>تم إنشاء " . count($tables) . " جدول</p>";
        
        // إصلاحات سريعة
        echo "<h5>الخطوة 6: تطبيق إصلاحات سريعة</h5>";
        
        $device_fixes = [
            ['old' => 'جهاز 1', 'new' => 'PlayStation 5 - Station 1'],
            ['old' => 'جهاز 2', 'new' => 'PlayStation 4 - Station 2'],
            ['old' => 'جهاز 3', 'new' => 'Xbox Series - Station 3'],
            ['old' => 'جهاز 4', 'new' => 'Gaming PC - Station 4'],
            ['old' => 'جهاز 5', 'new' => 'PlayStation 4 - Station 5'],
            ['old' => 'ps 1', 'new' => 'PlayStation 4 - Station 1']
        ];
        
        $fix_stmt = $pdo->prepare("UPDATE devices SET device_name = ? WHERE device_name = ?");
        $fixed = 0;
        
        foreach ($device_fixes as $fix) {
            try {
                $fix_stmt->execute([$fix['new'], $fix['old']]);
                if ($fix_stmt->rowCount() > 0) {
                    echo "<small class='text-success'>✓ {$fix['old']} → {$fix['new']}</small><br>";
                    $fixed++;
                }
            } catch (PDOException $e) {
                // تجاهل الأخطاء
            }
        }
        
        if ($fixed > 0) {
            echo "<p class='text-success'><i class='fas fa-wrench me-2'></i>تم إصلاح $fixed اسم جهاز</p>";
        }
        
        echo "<div class='text-center mt-4'>
                <a href='client/dashboard.php' class='btn btn-success btn-lg me-2'>
                    <i class='fas fa-home me-1'></i>الذهاب للوحة التحكم
                </a>
                <a href='client/sessions.php' class='btn btn-primary btn-lg me-2'>
                    <i class='fas fa-play-circle me-1'></i>اختبار الجلسات
                </a>
                <a href='reset_and_fix_database.php' class='btn btn-info btn-lg'>
                    <i class='fas fa-tools me-1'></i>إصلاحات إضافية
                </a>
              </div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في إعداد قاعدة البيانات!</h5>
            <p><strong>تفاصيل الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    if (strpos($e->getMessage(), 'getaddrinfo') !== false) {
        echo "<h6>المشكلة: خطأ في اسم الخادم</h6>
              <p>يبدو أن هناك مشكلة في اسم الخادم. جرب الحلول التالية:</p>
              <ol>
                <li>تأكد من تشغيل XAMPP/WAMP</li>
                <li>تأكد من تشغيل خدمة MySQL في XAMPP Control Panel</li>
                <li>جرب استخدام <code>127.0.0.1</code> بدلاً من <code>localhost</code></li>
              </ol>";
    } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<h6>المشكلة: رفض الوصول</h6>
              <p>مشكلة في اسم المستخدم أو كلمة المرور:</p>
              <ol>
                <li>تأكد من صحة اسم المستخدم (عادة <code>root</code>)</li>
                <li>تأكد من كلمة المرور (عادة فارغة في XAMPP)</li>
                <li>تحقق من إعدادات MySQL في XAMPP</li>
              </ol>";
    }
    
    echo "</div>";
}

echo "                </div>
            </div>
        </div>
    </div>
</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
