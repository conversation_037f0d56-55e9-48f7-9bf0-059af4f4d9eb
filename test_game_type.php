<?php
// اختبار ميزة نوع اللعب

require_once 'config/database.php';

echo "<h2>اختبار ميزة نوع اللعب</h2>";

try {
    // التحقق من وجود العمود
    $stmt = $pdo->query("SHOW COLUMNS FROM sessions LIKE 'game_type'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ عمود game_type موجود في قاعدة البيانات</p>";
        
        // عرض تفاصيل العمود
        $column = $stmt->fetch();
        echo "<p><strong>تفاصيل العمود:</strong></p>";
        echo "<ul>";
        echo "<li>النوع: " . $column['Type'] . "</li>";
        echo "<li>القيمة الافتراضية: " . ($column['Default'] ?? 'NULL') . "</li>";
        echo "<li>يمكن أن يكون NULL: " . $column['Null'] . "</li>";
        echo "</ul>";
        
        // عرض إحصائيات الجلسات
        $stats = $pdo->query("SELECT 
            game_type, 
            COUNT(*) as count,
            status,
            COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sessions) as percentage
        FROM sessions 
        GROUP BY game_type, status 
        ORDER BY game_type, status")->fetchAll();
        
        if (count($stats) > 0) {
            echo "<h3>إحصائيات الجلسات حسب نوع اللعب:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>نوع اللعب</th><th>الحالة</th><th>العدد</th><th>النسبة المئوية</th></tr>";
            foreach ($stats as $stat) {
                $type_name = $stat['game_type'] == 'single' ? 'فردي' : ($stat['game_type'] == 'multiplayer' ? 'زوجي' : 'غير محدد');
                $status_name = $stat['status'] == 'active' ? 'نشط' : ($stat['status'] == 'completed' ? 'مكتمل' : 'ملغي');
                echo "<tr>";
                echo "<td>{$type_name}</td>";
                echo "<td>{$status_name}</td>";
                echo "<td>{$stat['count']}</td>";
                echo "<td>" . number_format($stat['percentage'], 1) . "%</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد جلسات في قاعدة البيانات</p>";
        }
        
        // اختبار إنشاء جلسة تجريبية
        echo "<h3>اختبار إنشاء جلسة تجريبية:</h3>";
        
        // البحث عن جهاز متاح
        $device = $pdo->query("SELECT device_id, device_name FROM devices WHERE status = 'available' LIMIT 1")->fetch();
        
        if ($device) {
            echo "<p>تم العثور على جهاز متاح: {$device['device_name']}</p>";
            echo "<form method='POST' style='margin: 10px 0;'>";
            echo "<input type='hidden' name='test_device_id' value='{$device['device_id']}'>";
            echo "<label>نوع اللعب: </label>";
            echo "<select name='test_game_type'>";
            echo "<option value='single'>فردي</option>";
            echo "<option value='multiplayer'>زوجي</option>";
            echo "</select> ";
            echo "<button type='submit' name='create_test_session'>إنشاء جلسة تجريبية</button>";
            echo "</form>";
            
            // معالجة إنشاء الجلسة التجريبية
            if (isset($_POST['create_test_session'])) {
                $test_device_id = $_POST['test_device_id'];
                $test_game_type = $_POST['test_game_type'];
                
                try {
                    $pdo->beginTransaction();
                    
                    // إنشاء جلسة تجريبية
                    $stmt = $pdo->prepare("INSERT INTO sessions (device_id, client_id, game_type, status, created_by) VALUES (?, 1, ?, 'active', 1)");
                    $stmt->execute([$test_device_id, $test_game_type]);
                    $session_id = $pdo->lastInsertId();
                    
                    // تحديث حالة الجهاز
                    $pdo->prepare("UPDATE devices SET status = 'occupied' WHERE device_id = ?")->execute([$test_device_id]);
                    
                    $pdo->commit();
                    
                    $type_name = $test_game_type == 'single' ? 'فردي' : 'زوجي';
                    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية برقم {$session_id} من نوع {$type_name}</p>";
                    
                } catch (Exception $e) {
                    $pdo->rollBack();
                    echo "<p style='color: red;'>❌ فشل في إنشاء الجلسة: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أجهزة متاحة للاختبار</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ عمود game_type غير موجود في قاعدة البيانات</p>";
        echo "<p>يرجى تشغيل ملف run_game_type_update.php أولاً</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<br><br>";
echo "<a href='client/sessions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>صفحة الجلسات</a>";
echo "<a href='run_game_type_update.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشغيل التحديث</a>";
?>
