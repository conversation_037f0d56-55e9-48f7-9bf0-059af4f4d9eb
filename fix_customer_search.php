<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

// تعيين client_id للاختبار
$client_id = 1;
$_SESSION['client_id'] = $client_id;

echo "<h2>إصلاح مشكلة البحث عن العملاء</h2>";

try {
    // 1. التحقق من وجود جدول customers
    echo "<h3>1. فحص جدول customers</h3>";
    $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($table_check->rowCount() == 0) {
        echo "<p style='color: red;'>❌ جدول customers غير موجود - جاري الإنشاء...</p>";
        
        // إنشاء الجدول
        $create_table = "
            CREATE TABLE customers (
                customer_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                client_id INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_customers_client_id (client_id)
            )
        ";
        $pdo->exec($create_table);
        echo "<p style='color: green;'>✅ تم إنشاء جدول customers</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول customers موجود</p>";
    }
    
    // 2. التحقق من وجود عملاء
    echo "<h3>2. فحص العملاء الموجودين</h3>";
    $customers_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customers WHERE client_id = ?");
    $customers_stmt->execute([$client_id]);
    $customer_count = $customers_stmt->fetchColumn();
    
    echo "<p>عدد العملاء للعميل $client_id: <strong>$customer_count</strong></p>";
    
    if ($customer_count == 0) {
        echo "<p style='color: orange;'>⚠️ لا يوجد عملاء - جاري إضافة عملاء تجريبيين...</p>";
        
        // إضافة عملاء تجريبيين
        $sample_customers = [
            ['أحمد محمد', '01234567890', '<EMAIL>'],
            ['فاطمة علي', '01987654321', '<EMAIL>'],
            ['محمد حسن', '01555666777', '<EMAIL>'],
            ['سارة أحمد', '01444555666', '<EMAIL>'],
            ['علي محمود', '01333444555', '<EMAIL>'],
            ['نور الدين', '01666777888', '<EMAIL>'],
            ['ليلى حسام', '01777888999', '<EMAIL>']
        ];
        
        $insert_stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, client_id) VALUES (?, ?, ?, ?)");
        foreach ($sample_customers as $customer) {
            $insert_stmt->execute([$customer[0], $customer[1], $customer[2], $client_id]);
        }
        echo "<p style='color: green;'>✅ تم إضافة " . count($sample_customers) . " عملاء تجريبيين</p>";
    }
    
    // 3. عرض العملاء الموجودين
    echo "<h3>3. العملاء الموجودين حالياً</h3>";
    $all_customers_stmt = $pdo->prepare("SELECT customer_id, name, phone, email FROM customers WHERE client_id = ? ORDER BY name");
    $all_customers_stmt->execute([$client_id]);
    $all_customers = $all_customers_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($all_customers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>ID</th><th style='padding: 8px;'>الاسم</th><th style='padding: 8px;'>الهاتف</th><th style='padding: 8px;'>البريد</th>";
        echo "</tr>";
        
        foreach ($all_customers as $customer) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $customer['customer_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['phone'] ?? '') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['email'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. اختبار البحث مباشرة
    echo "<h3>4. اختبار البحث المباشر</h3>";
    $search_queries = ['أحمد', 'محمد', 'فاطمة', '012'];
    
    foreach ($search_queries as $query) {
        echo "<h4>البحث عن: '$query'</h4>";
        $search_term = "%{$query}%";
        
        $search_sql = "
            SELECT customer_id as id, name, phone, email
            FROM customers
            WHERE client_id = ?
            AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
            ORDER BY name ASC
            LIMIT 10
        ";
        
        $search_stmt = $pdo->prepare($search_sql);
        $search_stmt->execute([$client_id, $search_term, $search_term, $search_term]);
        $search_results = $search_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($search_results) > 0) {
            echo "<ul>";
            foreach ($search_results as $result) {
                echo "<li><strong>" . htmlspecialchars($result['name']) . "</strong> - " . htmlspecialchars($result['phone'] ?? '') . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>لا توجد نتائج</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>5. اختبار API البحث</h3>";
echo "<div>";
echo "<input type='text' id='search-input' placeholder='ابحث عن عميل...' style='padding: 8px; width: 300px;'>";
echo "<button onclick='testSearch()' style='padding: 8px 16px; margin-left: 10px;'>بحث</button>";
echo "</div>";
echo "<div id='search-results' style='margin-top: 20px; padding: 10px; border: 1px solid #ddd; min-height: 100px;'></div>";
?>

<script>
function testSearch() {
    const query = document.getElementById('search-input').value;
    const resultsDiv = document.getElementById('search-results');
    
    if (query.length < 2) {
        resultsDiv.innerHTML = '<p style="color: orange;">يرجى إدخال حرفين على الأقل</p>';
        return;
    }
    
    resultsDiv.innerHTML = '<p>جاري البحث...</p>';
    
    fetch(`client/api/search-customers.php?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            
            try {
                const data = JSON.parse(text);
                console.log('Parsed data:', data);
                
                if (data.success && data.customers && data.customers.length > 0) {
                    let html = '<h4>نتائج البحث:</h4><ul>';
                    data.customers.forEach(customer => {
                        html += `<li><strong>${customer.name}</strong> - ${customer.phone || 'لا يوجد هاتف'}</li>`;
                    });
                    html += '</ul>';
                    resultsDiv.innerHTML = html;
                } else if (data.success && data.customers && data.customers.length === 0) {
                    resultsDiv.innerHTML = '<p style="color: orange;">لا توجد نتائج للبحث</p>';
                } else {
                    resultsDiv.innerHTML = `<p style="color: red;">خطأ: ${data.message || 'استجابة غير متوقعة'}</p>`;
                }
            } catch (e) {
                console.error('JSON parse error:', e);
                resultsDiv.innerHTML = `
                    <p style="color: red;">خطأ في تحليل الاستجابة</p>
                    <details>
                        <summary>تفاصيل الاستجابة</summary>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px;">${text}</pre>
                    </details>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultsDiv.innerHTML = `<p style="color: red;">خطأ في الشبكة: ${error.message}</p>`;
        });
}

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('search-input').value = 'أحمد';
    setTimeout(testSearch, 1000);
});
</script>
