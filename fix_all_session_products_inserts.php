<?php
require_once 'config/database.php';

echo "<h1>إصلاح جميع ملفات إدراج session_products</h1>";

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'test_delete_session_product.php',
    'test_session_products_display.php', 
    'test_delete_product.php',
    'debug_delete_issue.php',
    'test_complete_delete_functionality.php',
    'fix_products_display_issue.php'
];

echo "<h2>1. الملفات المطلوب إصلاحها:</h2>";
echo "<ul>";
foreach ($files_to_fix as $file) {
    $exists = file_exists($file) ? "✅" : "❌";
    echo "<li>$exists $file</li>";
}
echo "</ul>";

echo "<h2>2. إصلاح الملفات:</h2>";

foreach ($files_to_fix as $file) {
    if (!file_exists($file)) {
        echo "<p style='color: orange;'>⚠️ تخطي $file - الملف غير موجود</p>";
        continue;
    }
    
    echo "<h3>إصلاح $file:</h3>";
    
    $content = file_get_contents($file);
    $original_content = $content;
    
    // البحث عن أنماط INSERT القديمة وإصلاحها
    $patterns = [
        // النمط 1: INSERT مع price فقط
        '/INSERT INTO session_products \(session_id, product_id, quantity, price\) VALUES \(\?, \?, \?, \?\)/' => 
        'INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)',
        
        // النمط 2: execute مع 4 معاملات
        '/\$stmt->execute\(\[\$([^,]+), \$([^,]+), \$?([^,]+), \$([^\]]+)\]\);/' => 
        '$quantity = $3; $unit_price = $4; $total_price = $unit_price * $quantity; $stmt->execute([$1, $2, $quantity, $unit_price, $total_price]);'
    ];
    
    $changes_made = false;
    
    foreach ($patterns as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
            echo "<p style='color: blue;'>ℹ️ تم العثور على نمط وإصلاحه</p>";
        }
    }
    
    // إصلاحات خاصة لكل ملف
    if (strpos($file, 'test_delete_session_product.php') !== false) {
        // إصلاح خاص لهذا الملف
        $content = preg_replace(
            '/\$stmt->execute\(\[\$session_id, \$product_id, (\d+), \$product_price\]\);/',
            '$quantity = $1; $unit_price = $product_price; $total_price = $unit_price * $quantity; $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);',
            $content
        );
        $changes_made = true;
    }
    
    if (strpos($file, 'test_session_products_display.php') !== false) {
        // إصلاح خاص لهذا الملف
        $content = preg_replace(
            '/\$stmt->execute\(\[\$session_id, \$product_ids\[(\d+)\], (\d+), ([0-9.]+)\]\);/',
            '$quantity = $2; $unit_price = $3; $total_price = $unit_price * $quantity; $stmt->execute([$session_id, $product_ids[$1], $quantity, $unit_price, $total_price]);',
            $content
        );
        $changes_made = true;
    }
    
    if ($changes_made) {
        // حفظ الملف المُصلح
        $backup_file = $file . '.backup.' . date('Y-m-d_H-i-s');
        file_put_contents($backup_file, $original_content);
        file_put_contents($file, $content);
        
        echo "<p style='color: green;'>✅ تم إصلاح $file</p>";
        echo "<p style='color: blue;'>ℹ️ تم إنشاء نسخة احتياطية: $backup_file</p>";
    } else {
        echo "<p style='color: gray;'>➖ لا يحتاج $file إلى إصلاح</p>";
    }
}

echo "<h2>3. التحقق من بنية قاعدة البيانات:</h2>";

try {
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_columns = ['unit_price', 'total_price'];
    $missing_columns = [];
    
    foreach ($required_columns as $col) {
        $found = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $col) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $missing_columns[] = $col;
        }
    }
    
    if (empty($missing_columns)) {
        echo "<p style='color: green;'>✅ جميع الأعمدة المطلوبة موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ الأعمدة المفقودة: " . implode(', ', $missing_columns) . "</p>";
        echo "<p>يرجى تشغيل ملف إصلاح قاعدة البيانات أولاً</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار سريع:</h2>";

try {
    // اختبار إضافة منتج
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT id, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if ($session && $product) {
        $quantity = 1;
        $unit_price = $product['price'];
        $total_price = $unit_price * $quantity;
        
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$session['session_id'], $product['id'], $quantity, $unit_price, $total_price]);
        
        $test_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ اختبار الإدراج نجح - ID: $test_id</p>";
        
        // حذف السجل التجريبي
        $pdo->exec("DELETE FROM session_products WHERE id = $test_id");
        echo "<p style='color: blue;'>ℹ️ تم حذف السجل التجريبي</p>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد بيانات كافية للاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ فشل الاختبار: " . $e->getMessage() . "</p>";
}

echo "<h2>5. الخلاصة:</h2>";
echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>تم إصلاح مشكلة unit_price!</h3>";
echo "<p><strong>المشكلة:</strong> كانت ملفات الاختبار تستخدم حقل 'price' بدلاً من 'unit_price' و 'total_price'</p>";
echo "<p><strong>الحل:</strong> تم تحديث جميع استعلامات INSERT لتستخدم البنية الصحيحة</p>";
echo "<p><strong>النتيجة:</strong> يمكن الآن إضافة المنتجات بدون أخطاء SQLSTATE[HY000]: 1364</p>";
echo "</div>";

echo "<h2>6. الخطوات التالية:</h2>";
echo "<ul>";
echo "<li>تأكد من تشغيل ملف إصلاح قاعدة البيانات إذا لم تفعل ذلك بعد</li>";
echo "<li>اختبر إضافة المنتجات من واجهة المستخدم</li>";
echo "<li>تحقق من أن جميع الحسابات تعمل بشكل صحيح</li>";
echo "</ul>";

?>
