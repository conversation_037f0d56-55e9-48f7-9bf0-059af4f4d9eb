<?php
/**
 * اختبار طرق الحساب الثلاثة
 * 1. الوقت الفعلي
 * 2. تقريب الساعة
 * 3. ساعة كاملة من أول دقيقة
 */

require_once 'config/database.php';
require_once 'includes/billing_helper.php';

echo "<h1>اختبار طرق الحساب الثلاثة</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // شرح الطرق الثلاثة
    echo "<h3>📖 شرح طرق الحساب الثلاثة</h3>";
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    // الطريقة الأولى
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; border-left: 5px solid #17a2b8;'>";
    echo "<h4 style='color: #0c5460; margin-top: 0;'>⏱️ الوقت الفعلي</h4>";
    echo "<p><strong>actual_time</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>حساب دقيق بناءً على الدقائق الفعلية</li>";
    echo "<li>مثال: 45 دقيقة = 0.75 ساعة</li>";
    echo "<li>الأكثر عدالة للعملاء</li>";
    echo "<li>مناسب للمحلات الحديثة</li>";
    echo "</ul>";
    echo "</div>";
    
    // الطريقة الثانية
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; border-left: 5px solid #ffc107;'>";
    echo "<h4 style='color: #856404; margin-top: 0;'>🕐 تقريب الساعة</h4>";
    echo "<p><strong>hourly_rounding</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>تقريب أي جزء من الساعة للساعة الكاملة</li>";
    echo "<li>مثال: 45 دقيقة = ساعة كاملة</li>";
    echo "<li>النظام التقليدي المتوسط</li>";
    echo "<li>مناسب للمحلات التقليدية</li>";
    echo "</ul>";
    echo "</div>";
    
    // الطريقة الثالثة
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; border-left: 5px solid #dc3545;'>";
    echo "<h4 style='color: #721c24; margin-top: 0;'>⚡ ساعة من أول دقيقة</h4>";
    echo "<p><strong>first_minute_full_hour</strong></p>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>ساعة كاملة بمجرد بدء الجلسة</li>";
    echo "<li>مثال: دقيقة واحدة = ساعة كاملة</li>";
    echo "<li>النظام الأكثر صرامة</li>";
    echo "<li>يضمن حد أدنى من الربح</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    // مقارنة شاملة
    echo "<h3>💰 مقارنة شاملة بين الطرق الثلاثة</h3>";
    
    $test_durations = [1, 5, 10, 15, 30, 45, 60, 75, 90, 120, 150];
    $hourly_rate = 20;
    
    echo "<div style='overflow-x: auto;'>";
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1); background: white;'>";
    echo "<thead>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 120px;'>المدة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 100px;'>الوقت الفعلي</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 100px;'>تقريب الساعة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 120px;'>ساعة من أول دقيقة</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 100px;'>الأرخص</th>";
    echo "<th style='padding: 12px; border: 1px solid #ddd; min-width: 100px;'>الأغلى</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($test_durations as $duration) {
        $comparison = compareBillingMethods($duration, $hourly_rate);
        
        $actual_cost = $comparison['actual_time']['cost'];
        $rounded_cost = $comparison['hourly_rounding']['cost'];
        $first_minute_cost = $comparison['first_minute_full_hour']['cost'];
        
        $cheapest = $comparison['comparison']['cheapest'];
        $most_expensive = $comparison['comparison']['most_expensive'];
        
        echo "<tr style='background: " . ($duration % 2 == 0 ? '#f8f9fa' : 'white') . ";'>";
        
        // المدة
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; font-weight: bold;'>";
        if ($duration < 60) {
            echo "$duration دقيقة";
        } else {
            $hours = floor($duration / 60);
            $minutes = $duration % 60;
            if ($minutes > 0) {
                echo "$hours ساعة و $minutes دقيقة";
            } else {
                echo "$hours ساعة";
            }
        }
        echo "</td>";
        
        // الوقت الفعلي
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #17a2b8; font-weight: bold;'>";
        echo number_format($actual_cost, 2) . " ج.م";
        if ($actual_cost == $cheapest) echo " 🏆";
        echo "</td>";
        
        // تقريب الساعة
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #ffc107; font-weight: bold;'>";
        echo number_format($rounded_cost, 2) . " ج.م";
        if ($rounded_cost == $cheapest) echo " 🏆";
        echo "</td>";
        
        // ساعة من أول دقيقة
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;'>";
        echo number_format($first_minute_cost, 2) . " ج.م";
        if ($first_minute_cost == $cheapest) echo " 🏆";
        echo "</td>";
        
        // الأرخص
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #28a745; font-weight: bold;'>";
        echo number_format($cheapest, 2) . " ج.م";
        echo "</td>";
        
        // الأغلى
        echo "<td style='padding: 10px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;'>";
        echo number_format($most_expensive, 2) . " ج.م";
        echo "</td>";
        
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // اختبار الدوال المساعدة
    echo "<h3>🧪 اختبار الدوال المساعدة</h3>";
    
    $test_client_id = 1;
    $test_duration = 45; // 45 دقيقة
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;'>";
    
    // اختبار كل طريقة
    $methods = [
        'actual_time' => ['name' => 'الوقت الفعلي', 'color' => '#17a2b8'],
        'hourly_rounding' => ['name' => 'تقريب الساعة', 'color' => '#ffc107'],
        'first_minute_full_hour' => ['name' => 'ساعة من أول دقيقة', 'color' => '#dc3545']
    ];
    
    foreach ($methods as $method => $info) {
        // محاكاة تغيير الإعداد
        updateBillingMethod($pdo, $test_client_id, $method);
        
        $cost = calculateTimeCost($pdo, $test_client_id, $test_duration, $hourly_rate);
        $details = calculateTimeCostWithDetails($pdo, $test_client_id, $test_duration, $hourly_rate);
        
        echo "<div style='background: white; padding: 20px; border-radius: 10px; border-left: 5px solid {$info['color']}; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
        echo "<h4 style='color: {$info['color']}; margin-top: 0;'>{$info['name']}</h4>";
        echo "<p><strong>المدة:</strong> $test_duration دقيقة</p>";
        echo "<p><strong>السعر:</strong> $hourly_rate ج.م/ساعة</p>";
        echo "<p><strong>التكلفة:</strong> <span style='font-size: 18px; font-weight: bold; color: {$info['color']};'>" . number_format($cost, 2) . " ج.م</span></p>";
        echo "<p><strong>التفاصيل:</strong> " . $details['calculation_note'] . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // إحصائيات مفيدة
    echo "<h3>📊 إحصائيات مفيدة</h3>";
    
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>💡 متى تستخدم كل طريقة؟</h4>";
    
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 15px;'>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px;'>";
    echo "<h5 style='color: #17a2b8;'>الوقت الفعلي</h5>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>للمحلات التي تريد جذب العملاء</li>";
    echo "<li>عندما تكون المنافسة شديدة</li>";
    echo "<li>للعملاء الذين يلعبون فترات قصيرة</li>";
    echo "<li>لبناء ثقة العملاء</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px;'>";
    echo "<h5 style='color: #ffc107;'>تقريب الساعة</h5>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>النظام التقليدي المتوازن</li>";
    echo "<li>مناسب لمعظم المحلات</li>";
    echo "<li>يحقق ربح معقول</li>";
    echo "<li>مألوف للعملاء</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px;'>";
    echo "<h5 style='color: #dc3545;'>ساعة من أول دقيقة</h5>";
    echo "<ul style='font-size: 14px;'>";
    echo "<li>للمحلات في المناطق المميزة</li>";
    echo "<li>عندما تكون الأجهزة محدودة</li>";
    echo "<li>لضمان حد أدنى من الربح</li>";
    echo "<li>للعملاء الذين يقدرون الجودة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // روابط للاختبار
    echo "<hr>";
    echo "<h3>🔗 روابط للاختبار</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #c3e6cb;'>";
    echo "<h4>📝 خطوات الاختبار:</h4>";
    echo "<ol>";
    echo "<li><a href='client/invoice_settings.php' target='_blank' style='color: #007bff; text-decoration: underline;'>انتقل لصفحة إعدادات الفاتورة</a> وجرب الخيارات الثلاثة</li>";
    echo "<li><a href='client/sessions.php' target='_blank' style='color: #007bff; text-decoration: underline;'>ابدأ جلسة جديدة</a> واتركها تعمل لفترات مختلفة</li>";
    echo "<li>أنه الجلسة وانظر الفرق في التكلفة</li>";
    echo "<li>غير طريقة الحساب وكرر الاختبار</li>";
    echo "<li><a href='client/invoice.php?session_id=1' target='_blank' style='color: #007bff; text-decoration: underline;'>اعرض الفاتورة</a> لرؤية التكلفة النهائية</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>✅ تم تطبيق الخيارات الثلاثة بنجاح!</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>الآن لديك مرونة كاملة في اختيار طريقة حساب التكلفة.</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ في قاعدة البيانات:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>خطأ عام:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4, h5 {
    color: #333;
}

table {
    background: white;
}

ul, ol {
    line-height: 1.8;
}

hr {
    border: none;
    border-top: 2px solid #ddd;
    margin: 30px 0;
}

@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}
</style>
