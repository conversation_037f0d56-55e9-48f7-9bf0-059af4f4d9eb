<?php
/**
 * اختبار هيكل قاعدة البيانات والفهارس - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

echo "<h1>اختبار هيكل قاعدة البيانات - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// فحص الجداول الموجودة
echo "<h2>1. الجداول الموجودة</h2>";

try {
    $tables_query = $pdo->query("SHOW TABLES");
    $tables = $tables_query->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم الجدول</th>";
    echo "<th style='padding: 10px;'>عدد الصفوف</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "</tr>";
    
    foreach ($tables as $table) {
        try {
            $count_query = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $count_query->fetch()['count'];
            $status = "✅ موجود";
            $status_color = "color: green;";
        } catch (PDOException $e) {
            $count = "خطأ";
            $status = "❌ خطأ";
            $status_color = "color: red;";
        }
        
        echo "<tr>";
        echo "<td style='padding: 10px;'>$table</td>";
        echo "<td style='padding: 10px;'>$count</td>";
        echo "<td style='padding: 10px; $status_color'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في جلب الجداول: " . $e->getMessage() . "</p>";
}

// فحص هيكل الجداول المهمة
echo "<h2>2. هيكل الجداول المهمة</h2>";

$important_tables = ['sessions', 'devices', 'customers', 'invoices'];

foreach ($important_tables as $table) {
    echo "<h3>جدول $table</h3>";
    
    try {
        $columns_query = $pdo->query("DESCRIBE `$table`");
        $columns = $columns_query->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>اسم العمود</th>";
        echo "<th style='padding: 8px;'>النوع</th>";
        echo "<th style='padding: 8px;'>Null</th>";
        echo "<th style='padding: 8px;'>Key</th>";
        echo "<th style='padding: 8px;'>Default</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Key'] . "</td>";
            echo "<td style='padding: 8px;'>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ الجدول $table غير موجود أو يحتوي على خطأ: " . $e->getMessage() . "</p>";
    }
}

// فحص الفهارس الموجودة
echo "<h2>3. الفهارس الموجودة</h2>";

foreach ($important_tables as $table) {
    echo "<h3>فهارس جدول $table</h3>";
    
    try {
        $indexes_query = $pdo->query("SHOW INDEX FROM `$table`");
        $indexes = $indexes_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($indexes) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>اسم الفهرس</th>";
            echo "<th style='padding: 8px;'>العمود</th>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>فريد</th>";
            echo "</tr>";
            
            foreach ($indexes as $index) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $index['Key_name'] . "</td>";
                echo "<td style='padding: 8px;'>" . $index['Column_name'] . "</td>";
                echo "<td style='padding: 8px;'>" . $index['Index_type'] . "</td>";
                echo "<td style='padding: 8px;'>" . ($index['Non_unique'] ? 'لا' : 'نعم') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد فهارس في جدول $table</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في جلب فهارس $table: " . $e->getMessage() . "</p>";
    }
}

// اختبار الفهارس المطلوبة
echo "<h2>4. اختبار الفهارس المطلوبة</h2>";

$required_indexes = [
    'sessions' => ['idx_sessions_status', 'idx_sessions_start_time', 'idx_sessions_device_status'],
    'devices' => ['idx_devices_client_status', 'idx_devices_status'],
    'customers' => ['idx_customers_client'],
    'invoices' => ['idx_invoices_client', 'idx_invoices_payment_status']
];

foreach ($required_indexes as $table => $indexes) {
    echo "<h3>فهارس مطلوبة لجدول $table</h3>";
    
    foreach ($indexes as $index_name) {
        try {
            $check_query = $pdo->query("SHOW INDEX FROM `$table` WHERE Key_name = '$index_name'");
            if ($check_query->rowCount() > 0) {
                echo "<p style='color: green;'>✅ الفهرس $index_name موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ الفهرس $index_name غير موجود</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في فحص الفهرس $index_name: " . $e->getMessage() . "</p>";
        }
    }
}

// اختبار أداء الاستعلامات
echo "<h2>5. اختبار أداء الاستعلامات</h2>";

$test_queries = [
    "الجلسات النشطة" => "SELECT COUNT(*) FROM sessions WHERE status = 'active'",
    "الأجهزة المتاحة" => "SELECT COUNT(*) FROM devices WHERE status = 'available'",
    "جلسات اليوم" => "SELECT COUNT(*) FROM sessions WHERE DATE(start_time) = CURRENT_DATE",
    "العملاء" => "SELECT COUNT(*) FROM customers"
];

foreach ($test_queries as $description => $query) {
    $start_time = microtime(true);
    
    try {
        $result = $pdo->query($query);
        $count = $result->fetch(PDO::FETCH_COLUMN);
        $execution_time = microtime(true) - $start_time;
        
        $time_ms = round($execution_time * 1000, 2);
        $time_color = $time_ms > 100 ? 'red' : ($time_ms > 50 ? 'orange' : 'green');
        
        echo "<p><strong>$description:</strong> $count صف - ";
        echo "<span style='color: $time_color;'>$time_ms ms</span></p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام $description: " . $e->getMessage() . "</p>";
    }
}

// إضافة الفهارس المفقودة
echo "<h2>6. إضافة الفهارس المفقودة</h2>";

if (isset($_GET['add_indexes'])) {
    echo "<h3>جاري إضافة الفهارس...</h3>";
    
    // فحص وجود الأعمدة قبل إضافة الفهارس
    function columnExists($pdo, $table, $column) {
        try {
            $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
            $stmt->execute([$column]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    function tableExists($pdo, $table) {
        try {
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    $indexes_to_add = [];
    
    // فهارس الجلسات
    if (tableExists($pdo, 'sessions')) {
        $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)";
        $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)";
        
        if (columnExists($pdo, 'sessions', 'device_id')) {
            $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_sessions_device_status ON sessions(device_id, status)";
        }
    }
    
    // فهارس الأجهزة
    if (tableExists($pdo, 'devices')) {
        if (columnExists($pdo, 'devices', 'client_id') && columnExists($pdo, 'devices', 'status')) {
            $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_devices_client_status ON devices(client_id, status)";
        } elseif (columnExists($pdo, 'devices', 'status')) {
            $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status)";
        }
    }
    
    // فهارس العملاء
    if (tableExists($pdo, 'customers') && columnExists($pdo, 'customers', 'client_id')) {
        $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_customers_client ON customers(client_id)";
    }
    
    // فهارس الفواتير
    if (tableExists($pdo, 'invoices')) {
        if (columnExists($pdo, 'invoices', 'client_id')) {
            $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_invoices_client ON invoices(client_id)";
        }
        if (columnExists($pdo, 'invoices', 'payment_status')) {
            $indexes_to_add[] = "CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON invoices(payment_status)";
        }
    }
    
    foreach ($indexes_to_add as $index_sql) {
        try {
            $pdo->exec($index_sql);
            echo "<p style='color: green;'>✅ تم إضافة فهرس بنجاح</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: blue;'>ℹ️ الفهرس موجود مسبقاً</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة فهرس: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p><a href='?' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث الصفحة</a></p>";
    
} else {
    echo "<p><a href='?add_indexes=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة الفهارس المفقودة</a></p>";
}

echo "<h2>7. روابط مفيدة</h2>";
echo "<p><a href='simple_fix_all.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>سكريپت الإصلاح الشامل</a></p>";
echo "<p><a href='test_performance.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الأداء</a></p>";
echo "<p><a href='client/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a></p>";

echo "</div>";
?>
