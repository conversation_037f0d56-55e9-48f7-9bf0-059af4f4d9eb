<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

require_once 'config/database.php';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار البحث البسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="container mt-5">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-search me-2"></i>اختبار البحث البسيط</h3>
        </div>
        <div class="card-body">
            
            <?php
            try {
                // فحص العملاء
                $customers_count = $pdo->query("SELECT COUNT(*) FROM customers WHERE client_id = 1")->fetchColumn();
                echo "<div class='alert alert-info'>عدد العملاء: $customers_count</div>";
                
                if ($customers_count > 0) {
                    $sample = $pdo->query("SELECT customer_id, name, phone FROM customers WHERE client_id = 1 LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
                    echo "<h6>عينة من العملاء:</h6><ul>";
                    foreach ($sample as $customer) {
                        echo "<li>" . htmlspecialchars($customer['name']) . " - " . htmlspecialchars($customer['phone']) . "</li>";
                    }
                    echo "</ul>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            ?>
            
            <!-- نموذج البحث -->
            <div class="mb-3">
                <label class="form-label">البحث عن عميل:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="customer_search" 
                           placeholder="ابحث عن عميل..." autocomplete="off">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="customer_results" class="list-group mt-2" style="display: none;"></div>
            </div>
            
            <!-- معلومات التشخيص -->
            <div class="alert alert-secondary">
                <h6>معلومات التشخيص:</h6>
                <div id="debug_info">جاهز للاختبار...</div>
            </div>
            
            <div class="text-center">
                <a href="client/dashboard.php" class="btn btn-success">الذهاب للوحة التحكم</a>
            </div>
        </div>
    </div>
</div>

<script>
let searchTimeout;

document.getElementById('customer_search').addEventListener('input', function() {
    const query = this.value.trim();
    
    addDebugInfo('تم كتابة: "' + query + '"');
    
    clearTimeout(searchTimeout);
    
    if (query.length < 2) {
        document.getElementById('customer_results').style.display = 'none';
        addDebugInfo('النص قصير جداً');
        return;
    }
    
    addDebugInfo('انتظار 300ms...');
    
    searchTimeout = setTimeout(function() {
        performSearch(query);
    }, 300);
});

function performSearch(query) {
    const resultsDiv = document.getElementById('customer_results');
    
    addDebugInfo('بدء البحث عن: "' + query + '"');
    
    resultsDiv.innerHTML = '<div class="list-group-item text-muted"><i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...</div>';
    resultsDiv.style.display = 'block';
    
    const url = 'client/api/search-customers.php?q=' + encodeURIComponent(query);
    addDebugInfo('استدعاء: ' + url);
    
    fetch(url)
        .then(function(response) {
            addDebugInfo('استجابة: ' + response.status);
            
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            
            return response.text();
        })
        .then(function(text) {
            addDebugInfo('البيانات: ' + text.substring(0, 100) + '...');
            
            try {
                const customers = JSON.parse(text);
                
                if (Array.isArray(customers)) {
                    addDebugInfo('تم تحليل JSON - عدد النتائج: ' + customers.length);
                    
                    if (customers.length === 0) {
                        resultsDiv.innerHTML = '<div class="list-group-item text-muted">لا توجد نتائج</div>';
                    } else {
                        let html = '';
                        for (let i = 0; i < customers.length; i++) {
                            const customer = customers[i];
                            html += '<button type="button" class="list-group-item list-group-item-action" onclick="selectCustomer(' + customer.id + ', \'' + customer.name + '\', \'' + (customer.phone || '') + '\')">';
                            html += '<strong>' + customer.name + '</strong>';
                            if (customer.phone) {
                                html += '<br><small class="text-muted">' + customer.phone + '</small>';
                            }
                            html += '</button>';
                        }
                        resultsDiv.innerHTML = html;
                    }
                } else {
                    addDebugInfo('خطأ: البيانات ليست مصفوفة');
                    resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في البيانات</div>';
                }
                
                resultsDiv.style.display = 'block';
            } catch (error) {
                addDebugInfo('خطأ JSON: ' + error.message);
                resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في تحليل البيانات</div>';
                resultsDiv.style.display = 'block';
            }
        })
        .catch(function(error) {
            addDebugInfo('خطأ شبكة: ' + error.message);
            resultsDiv.innerHTML = '<div class="list-group-item text-danger">خطأ في الاتصال: ' + error.message + '</div>';
            resultsDiv.style.display = 'block';
        });
}

function selectCustomer(id, name, phone) {
    document.getElementById('customer_search').value = name + (phone ? ' - ' + phone : '');
    document.getElementById('customer_results').style.display = 'none';
    addDebugInfo('تم اختيار: ' + name + ' (ID: ' + id + ')');
}

function clearSearch() {
    document.getElementById('customer_search').value = '';
    document.getElementById('customer_results').style.display = 'none';
    addDebugInfo('تم مسح البحث');
}

function addDebugInfo(message) {
    const debugDiv = document.getElementById('debug_info');
    const time = new Date().toLocaleTimeString();
    debugDiv.innerHTML = '<small>[' + time + '] ' + message + '</small><br>' + debugDiv.innerHTML;
}

// إخفاء النتائج عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('#customer_search') && !e.target.closest('#customer_results')) {
        document.getElementById('customer_results').style.display = 'none';
    }
});

addDebugInfo('تم تحميل الصفحة - جاهز للاختبار');
</script>

</body>
</html>
