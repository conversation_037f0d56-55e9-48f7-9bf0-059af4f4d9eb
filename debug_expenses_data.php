<?php
/**
 * تشخيص مفصل لبيانات المصروفات - PlayGood
 * للعثور على سبب ظهور اسم العميل بدلاً من المبلغ
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔍 تشخيص مفصل لبيانات المصروفات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    $client_id = $_SESSION['client_id'];
    
    // 1. فحص جدول المصروفات مباشرة
    echo "<h2>1. فحص جدول المصروفات مباشرة</h2>";
    
    try {
        $direct_expenses = $pdo->prepare("
            SELECT id, expense_type_id, amount, expense_date, description, created_at
            FROM expenses 
            WHERE client_id = ? 
            ORDER BY expense_date DESC, created_at DESC
            LIMIT 5
        ");
        $direct_expenses->execute([$client_id]);
        $expenses_raw = $direct_expenses->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($expenses_raw)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>expense_type_id</th>";
            echo "<th style='padding: 8px;'>amount (خام)</th>";
            echo "<th style='padding: 8px;'>نوع البيانات</th>";
            echo "<th style='padding: 8px;'>expense_date</th>";
            echo "<th style='padding: 8px;'>description</th>";
            echo "</tr>";
            
            foreach ($expenses_raw as $expense) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $expense['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . $expense['expense_type_id'] . "</td>";
                echo "<td style='padding: 8px; color: red; font-weight: bold;'>" . htmlspecialchars($expense['amount']) . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . gettype($expense['amount']) . " - " . (is_numeric($expense['amount']) ? "رقم ✅" : "ليس رقم ❌") . "</td>";
                echo "<td style='padding: 8px;'>" . $expense['expense_date'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($expense['description'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد مصروفات في الجدول</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص جدول المصروفات: " . $e->getMessage() . "</p>";
    }
    
    // 2. فحص الاستعلام المستخدم في صفحة الماليات
    echo "<h2>2. فحص الاستعلام المستخدم في صفحة الماليات</h2>";
    
    try {
        $finances_query = $pdo->prepare("
            SELECT e.*, et.name as expense_type_name
            FROM expenses e
            JOIN expense_types et ON e.expense_type_id = et.id
            WHERE e.client_id = ?
            ORDER BY e.expense_date DESC, e.created_at DESC
            LIMIT 5
        ");
        $finances_query->execute([$client_id]);
        $expenses_joined = $finances_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($expenses_joined)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>expense_type_name</th>";
            echo "<th style='padding: 8px;'>amount</th>";
            echo "<th style='padding: 8px;'>نوع البيانات</th>";
            echo "<th style='padding: 8px;'>number_format</th>";
            echo "<th style='padding: 8px;'>جميع الحقول</th>";
            echo "</tr>";
            
            foreach ($expenses_joined as $expense) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $expense['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($expense['expense_type_name']) . "</td>";
                echo "<td style='padding: 8px; color: red; font-weight: bold;'>" . htmlspecialchars($expense['amount']) . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . gettype($expense['amount']) . " - " . (is_numeric($expense['amount']) ? "رقم ✅" : "ليس رقم ❌") . "</td>";
                echo "<td style='padding: 8px; color: green;'>" . (is_numeric($expense['amount']) ? number_format($expense['amount'], 2) . " ج.م" : "غير قابل للتنسيق") . "</td>";
                echo "<td style='padding: 8px; font-size: 10px;'>" . htmlspecialchars(json_encode($expense, JSON_UNESCAPED_UNICODE)) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد نتائج من الاستعلام المدمج</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في الاستعلام المدمج: " . $e->getMessage() . "</p>";
    }
    
    // 3. فحص جدول أنواع المصروفات
    echo "<h2>3. فحص جدول أنواع المصروفات</h2>";
    
    try {
        $expense_types_query = $pdo->prepare("
            SELECT id, name, description, client_id
            FROM expense_types 
            WHERE client_id = ?
            ORDER BY name
        ");
        $expense_types_query->execute([$client_id]);
        $expense_types = $expense_types_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($expense_types)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Name</th>";
            echo "<th style='padding: 8px;'>Description</th>";
            echo "<th style='padding: 8px;'>Client ID</th>";
            echo "</tr>";
            
            foreach ($expense_types as $type) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $type['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($type['name']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($type['description'] ?? '') . "</td>";
                echo "<td style='padding: 8px;'>" . $type['client_id'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد أنواع مصروفات</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص أنواع المصروفات: " . $e->getMessage() . "</p>";
    }
    
    // 4. فحص احتمالية خلط البيانات مع جداول أخرى
    echo "<h2>4. فحص احتمالية خلط البيانات</h2>";
    
    try {
        // فحص إذا كان هناك خلط مع جدول العملاء
        $cross_check = $pdo->prepare("
            SELECT 
                'expenses' as table_name,
                e.id,
                e.amount,
                'N/A' as customer_name
            FROM expenses e
            WHERE e.client_id = ?
            
            UNION ALL
            
            SELECT 
                'customers' as table_name,
                c.customer_id as id,
                'N/A' as amount,
                c.name as customer_name
            FROM customers c
            WHERE c.client_id = ?
            
            ORDER BY table_name, id
            LIMIT 10
        ");
        $cross_check->execute([$client_id, $client_id]);
        $cross_data = $cross_check->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($cross_data)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>الجدول</th>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Amount</th>";
            echo "<th style='padding: 8px;'>Customer Name</th>";
            echo "</tr>";
            
            foreach ($cross_data as $data) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $data['table_name'] . "</td>";
                echo "<td style='padding: 8px;'>" . $data['id'] . "</td>";
                echo "<td style='padding: 8px; color: red;'>" . htmlspecialchars($data['amount']) . "</td>";
                echo "<td style='padding: 8px; color: blue;'>" . htmlspecialchars($data['customer_name']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص الخلط: " . $e->getMessage() . "</p>";
    }
    
    // 5. محاكاة عرض البيانات كما في صفحة الماليات
    echo "<h2>5. محاكاة عرض البيانات كما في صفحة الماليات</h2>";
    
    try {
        $simulation_query = $pdo->prepare("
            SELECT e.*, et.name as expense_type_name
            FROM expenses e
            JOIN expense_types et ON e.expense_type_id = et.id
            WHERE e.client_id = ?
            ORDER BY e.expense_date DESC, e.created_at DESC
            LIMIT 3
        ");
        $simulation_query->execute([$client_id]);
        $simulation_expenses = $simulation_query->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($simulation_expenses)) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>محاكاة جدول المصروفات:</h4>";
            echo "<table class='table table-hover' style='border: 1px solid #ddd;'>";
            echo "<thead style='background: #e9ecef;'>";
            echo "<tr>";
            echo "<th style='padding: 8px;'>النوع</th>";
            echo "<th style='padding: 8px;'>المبلغ</th>";
            echo "<th style='padding: 8px;'>التاريخ</th>";
            echo "<th style='padding: 8px;'>الوصف</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($simulation_expenses as $expense) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>";
                echo "<span style='background: #dc3545; color: white; padding: 2px 8px; border-radius: 3px;'>";
                echo htmlspecialchars($expense['expense_type_name']);
                echo "</span>";
                echo "</td>";
                echo "<td style='padding: 8px; color: #dc3545; font-weight: bold;'>";
                // هنا المشكلة المحتملة
                echo number_format($expense['amount'], 2) . " ج.م";
                echo "</td>";
                echo "<td style='padding: 8px;'>" . date('d/m/Y', strtotime($expense['expense_date'])) . "</td>";
                echo "<td style='padding: 8px;'>";
                echo "<small>" . htmlspecialchars($expense['description'] ?: 'لا يوجد وصف') . "</small>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في المحاكاة: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<h2>📋 الخلاصة</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔍 ما يجب البحث عنه:</h4>";
echo "<ol>";
echo "<li><strong>في الجدول الأول:</strong> تحقق من أن حقل amount يحتوي على أرقام وليس أسماء</li>";
echo "<li><strong>في الجدول الثاني:</strong> تحقق من أن الاستعلام المدمج يعطي نتائج صحيحة</li>";
echo "<li><strong>في المحاكاة:</strong> تحقق من أن العرض يطابق ما تراه في صفحة الماليات</li>";
echo "<li><strong>إذا كان amount يحتوي على أسماء:</strong> هناك مشكلة في إدخال البيانات</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
