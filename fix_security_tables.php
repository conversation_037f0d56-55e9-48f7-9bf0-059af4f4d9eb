<?php
/**
 * إصلاح سريع - إنشاء جداول الأمان
 * PlayGood Gaming Center Management System
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح جداول الأمان - PlayGood</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .fix-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
<div class='container py-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-8'>
            <div class='fix-card p-4 shadow'>
                <h1 class='text-center mb-4'>
                    <i class='fas fa-tools text-primary'></i>
                    إصلاح جداول الأمان
                </h1>";

try {
    echo "<h3>إنشاء جداول الأمان...</h3>";
    
    // جدول الجلسات الآمنة
    $sql1 = "
        CREATE TABLE IF NOT EXISTS secure_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(128) NOT NULL,
            user_id INT NOT NULL,
            user_type ENUM('admin', 'client', 'employee') NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent_hash VARCHAR(64) NOT NULL,
            security_token VARCHAR(64) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_valid BOOLEAN DEFAULT TRUE,
            INDEX idx_session_id (session_id),
            INDEX idx_user_id (user_id, user_type),
            INDEX idx_security_token (security_token)
        ) ENGINE=InnoDB
    ";
    
    if ($pdo->exec($sql1) !== false) {
        echo "<p class='success'>✅ تم إنشاء جدول secure_sessions بنجاح</p>";
    } else {
        echo "<p class='error'>❌ خطأ في إنشاء جدول secure_sessions</p>";
    }
    
    // جدول سجلات الأمان
    $sql2 = "
        CREATE TABLE IF NOT EXISTS session_security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(128),
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            attack_type ENUM('hijack_attempt', 'invalid_token', 'ip_mismatch', 'agent_mismatch', 'expired_session', 'admin_termination') NOT NULL,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip_address (ip_address),
            INDEX idx_attack_type (attack_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB
    ";
    
    if ($pdo->exec($sql2) !== false) {
        echo "<p class='success'>✅ تم إنشاء جدول session_security_logs بنجاح</p>";
    } else {
        echo "<p class='error'>❌ خطأ في إنشاء جدول session_security_logs</p>";
    }
    
    echo "<hr>";
    echo "<h3>فحص الجداول الموجودة...</h3>";
    
    // فحص الجداول
    $tables = ['secure_sessions', 'session_security_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ جدول $table موجود</p>";
            
            // عرض عدد السجلات
            $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch()['count'];
            echo "<p class='text-muted'>   عدد السجلات: $count</p>";
        } else {
            echo "<p class='error'>❌ جدول $table غير موجود</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>اختبار النظام...</h3>";
    
    // اختبار إنشاء SessionSecurityManager
    try {
        require_once 'includes/session_security.php';
        $sessionManager = new SessionSecurityManager($pdo);
        echo "<p class='success'>✅ تم إنشاء SessionSecurityManager بنجاح</p>";
        
        // اختبار الحصول على إحصائيات
        $stats = $sessionManager->getSecurityStats();
        echo "<p class='success'>✅ تم الحصول على إحصائيات الأمان بنجاح</p>";
        echo "<p class='text-muted'>   الجلسات النشطة: " . ($stats['active_sessions'] ?? 0) . "</p>";
        echo "<p class='text-muted'>   هجمات اليوم: " . ($stats['attacks_today'] ?? 0) . "</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في اختبار SessionSecurityManager: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<div class='alert alert-success'>
        <h4>✅ تم الإصلاح بنجاح!</h4>
        <p>جداول الأمان جاهزة الآن. يمكنك استخدام الموقع بشكل طبيعي.</p>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <h4>❌ خطأ في الإصلاح</h4>
        <p>حدث خطأ: " . $e->getMessage() . "</p>
    </div>";
}

echo "<div class='text-center mt-4'>
    <a href='admin/login.php' class='btn btn-primary me-2'>
        <i class='fas fa-user-shield'></i> دخول المدير
    </a>
    <a href='client/login.php' class='btn btn-success me-2'>
        <i class='fas fa-store'></i> دخول العميل
    </a>
    <a href='client/employee-login.php' class='btn btn-info me-2'>
        <i class='fas fa-user-tie'></i> دخول الموظف
    </a>
    <a href='test_security_system.php' class='btn btn-warning'>
        <i class='fas fa-shield-alt'></i> اختبار الأمان
    </a>
</div>";

echo "</div></div></div></div></body></html>";
?>
