<?php
/**
 * إصلاح مشاكل إضافة المنتجات للجلسات
 * PlayGood Gaming Center Management System
 */

echo "<h1>إصلاح مشاكل إضافة المنتجات للجلسات</h1>";
echo "<hr>";

try {
    // إعدادات قاعدة البيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    // محاولة الاتصال بقواعد البيانات المختلفة
    $databases = ['station', 'playgood'];
    $pdo = null;
    $connected_db = '';
    
    foreach ($databases as $dbname) {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            $connected_db = $dbname;
            echo "<p style='color: green;'>✅ متصل بقاعدة البيانات: $dbname</p>";
            break;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    if (!$pdo) {
        throw new Exception("فشل في الاتصال بأي من قواعد البيانات");
    }

    echo "<h2>1. فحص الجداول المطلوبة</h2>";
    
    // فحص وجود الجداول
    $required_tables = ['sessions', 'cafeteria_items', 'session_products', 'devices'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
            $missing_tables[] = $table;
        }
    }

    echo "<h2>2. إنشاء الجداول المفقودة</h2>";
    
    // إنشاء جدول session_products إذا كان مفقود
    if (in_array('session_products', $missing_tables)) {
        echo "<p style='color: orange;'>⚠️ إنشاء جدول session_products...</p>";
        $create_session_products = "
            CREATE TABLE session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INT NULL DEFAULT NULL,
                notes TEXT NULL DEFAULT NULL,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($create_session_products);
        echo "<p style='color: green;'>✅ تم إنشاء جدول session_products</p>";
    }

    echo "<h2>3. فحص أعمدة الجداول</h2>";
    
    // فحص أعمدة session_products
    try {
        $stmt = $pdo->query("DESCRIBE session_products");
        $session_products_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>أعمدة جدول session_products:</h3>";
        echo "<ul>";
        foreach ($session_products_columns as $column) {
            echo "<li>$column</li>";
        }
        echo "</ul>";
        
        // الأعمدة المطلوبة
        $required_sp_columns = [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'session_id' => 'INT NOT NULL',
            'product_id' => 'INT NOT NULL',
            'quantity' => 'INT NOT NULL DEFAULT 1',
            'price' => 'DECIMAL(10,2) NOT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'created_by' => 'INT NULL DEFAULT NULL',
            'notes' => 'TEXT NULL DEFAULT NULL'
        ];
        
        foreach ($required_sp_columns as $column => $definition) {
            if (!in_array($column, $session_products_columns)) {
                echo "<p style='color: orange;'>⚠️ إضافة عمود $column...</p>";
                try {
                    $pdo->exec("ALTER TABLE session_products ADD COLUMN $column $definition");
                    echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ فشل في إضافة عمود $column: " . $e->getMessage() . "</p>";
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص جدول session_products: " . $e->getMessage() . "</p>";
    }

    // فحص أعمدة cafeteria_items
    try {
        $stmt = $pdo->query("DESCRIBE cafeteria_items");
        $cafeteria_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_cafeteria_columns = [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'name' => 'VARCHAR(100) NOT NULL',
            'price' => 'DECIMAL(10,2) NOT NULL',
            'category' => 'VARCHAR(50) DEFAULT NULL',
            'client_id' => 'INT NOT NULL DEFAULT 1'
        ];
        
        foreach ($required_cafeteria_columns as $column => $definition) {
            if (!in_array($column, $cafeteria_columns)) {
                echo "<p style='color: orange;'>⚠️ إضافة عمود $column لجدول cafeteria_items...</p>";
                try {
                    $pdo->exec("ALTER TABLE cafeteria_items ADD COLUMN $column $definition");
                    echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ فشل في إضافة عمود $column: " . $e->getMessage() . "</p>";
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص جدول cafeteria_items: " . $e->getMessage() . "</p>";
    }

    echo "<h2>4. فحص الفهارس والمفاتيح الخارجية</h2>";
    
    // إضافة الفهارس المطلوبة
    $indexes_to_add = [
        'session_products' => [
            'idx_session_products_session_id' => 'CREATE INDEX idx_session_products_session_id ON session_products(session_id)',
            'idx_session_products_product_id' => 'CREATE INDEX idx_session_products_product_id ON session_products(product_id)'
        ],
        'cafeteria_items' => [
            'idx_cafeteria_items_client_id' => 'CREATE INDEX idx_cafeteria_items_client_id ON cafeteria_items(client_id)',
            'idx_cafeteria_items_category' => 'CREATE INDEX idx_cafeteria_items_category ON cafeteria_items(category)'
        ]
    ];

    foreach ($indexes_to_add as $table => $indexes) {
        foreach ($indexes as $index_name => $index_sql) {
            try {
                $stmt = $pdo->query("SHOW INDEX FROM $table WHERE Key_name = '$index_name'");
                if ($stmt->rowCount() == 0) {
                    $pdo->exec($index_sql);
                    echo "<p style='color: green;'>✅ تم إضافة فهرس $index_name</p>";
                } else {
                    echo "<p style='color: blue;'>ℹ️ فهرس $index_name موجود مسبقاً</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>⚠️ تحذير: فهرس $index_name: " . $e->getMessage() . "</p>";
            }
        }
    }

    echo "<h2>5. اختبار إضافة منتج للجلسة</h2>";
    
    try {
        // البحث عن جلسة نشطة للاختبار
        $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
        $session = $stmt->fetch();
        
        // البحث عن منتج للاختبار
        $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
        $product = $stmt->fetch();
        
        if ($session && $product) {
            // محاولة إضافة منتج تجريبي
            $test_insert = "INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)";
            $stmt = $pdo->prepare($test_insert);
            $stmt->execute([$session['session_id'], $product['id'], 1, $product['price']]);
            
            $test_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إضافة منتج تجريبي برقم: $test_id</p>";
            
            // حذف المنتج التجريبي
            $pdo->exec("DELETE FROM session_products WHERE id = $test_id");
            echo "<p style='color: blue;'>ℹ️ تم حذف المنتج التجريبي</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات نشطة أو منتجات للاختبار</p>";
            
            // إنشاء بيانات تجريبية للاختبار
            if (!$product) {
                echo "<p style='color: orange;'>⚠️ إنشاء منتج تجريبي...</p>";
                $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)");
                echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي</p>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ فشل في اختبار إضافة المنتج: " . $e->getMessage() . "</p>";
    }

    echo "<h2>6. فحص ملف API</h2>";
    
    $api_file = 'client/api/add_session_product.php';
    if (file_exists($api_file)) {
        echo "<p style='color: green;'>✅ ملف API موجود: $api_file</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف API غير موجود: $api_file</p>";
    }

    echo "<hr>";
    echo "<h2>النتيجة النهائية</h2>";
    echo "<p style='color: green; font-size: 18px;'><strong>✅ تم إصلاح مشاكل إضافة المنتجات للجلسات</strong></p>";
    echo "<p>يمكنك الآن إضافة المنتجات للجلسات بدون مشاكل</p>";

} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . $e->getMessage() . "</strong></p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
p {
    margin: 8px 0;
    padding: 8px;
    border-radius: 4px;
}
ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #007bff, #28a745);
    margin: 20px 0;
}
</style>
