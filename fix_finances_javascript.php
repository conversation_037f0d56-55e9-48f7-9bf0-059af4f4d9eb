<?php
/**
 * إصلاح مشاكل الجافاسكريبت في صفحة الماليات - PlayGood
 * يصلح مشاكل عرض المبالغ والتنسيق
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
}

echo "<h1>🔧 إصلاح مشاكل الجافاسكريبت في صفحة الماليات</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

// 1. فحص ملف الجافاسكريبت الرئيسي
echo "<h2>1. فحص ملف assets/js/main.js</h2>";

$main_js_path = 'assets/js/main.js';
if (file_exists($main_js_path)) {
    echo "<p style='color: green;'>✅ ملف main.js موجود</p>";
    
    $main_js_content = file_get_contents($main_js_path);
    
    // فحص وجود وظائف التنسيق
    $functions_to_check = [
        'formatCurrency' => 'وظيفة تنسيق العملة',
        'formatNumber' => 'وظيفة تنسيق الأرقام',
        'updateSessionTimers' => 'وظيفة تحديث مؤقتات الجلسات'
    ];
    
    foreach ($functions_to_check as $function => $description) {
        if (strpos($main_js_content, "function $function") !== false) {
            echo "<p style='color: green;'>✅ $description موجودة</p>";
        } else {
            echo "<p style='color: red;'>❌ $description غير موجودة</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ملف main.js غير موجود</p>";
}

// 2. فحص كود الجافاسكريبت في صفحة الماليات
echo "<h2>2. فحص كود الجافاسكريبت في صفحة الماليات</h2>";

$finances_php_path = 'client/finances.php';
if (file_exists($finances_php_path)) {
    echo "<p style='color: green;'>✅ ملف finances.php موجود</p>";
    
    $finances_content = file_get_contents($finances_php_path);
    
    // فحص وجود وظيفة formatNumber في صفحة الماليات
    if (strpos($finances_content, 'function formatNumber') !== false) {
        echo "<p style='color: green;'>✅ وظيفة formatNumber موجودة في صفحة الماليات</p>";
    } else {
        echo "<p style='color: red;'>❌ وظيفة formatNumber غير موجودة في صفحة الماليات</p>";
    }
    
    // فحص استخدام number_format في PHP
    $number_format_count = substr_count($finances_content, 'number_format');
    echo "<p style='color: blue;'>ℹ️ عدد استخدامات number_format في PHP: $number_format_count</p>";
    
} else {
    echo "<p style='color: red;'>❌ ملف finances.php غير موجود</p>";
}

// 3. إنشاء ملف جافاسكريبت محسن للماليات
echo "<h2>3. إنشاء ملف جافاسكريبت محسن للماليات</h2>";

$enhanced_js_content = '
/**
 * Enhanced JavaScript for Finances Page - PlayGood
 * يحسن عرض وتنسيق المبالغ المالية
 */

// وظيفة محسنة لتنسيق العملة
function formatCurrencyEnhanced(amount) {
    // التأكد من أن القيمة رقم
    const numAmount = parseFloat(amount) || 0;
    
    try {
        return new Intl.NumberFormat(\'ar-EG\', {
            style: \'currency\',
            currency: \'EGP\',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(numAmount);
    } catch (error) {
        console.error(\'Error formatting currency:\', error);
        return numAmount.toFixed(2) + \' ج.م\';
    }
}

// وظيفة محسنة لتنسيق الأرقام
function formatNumberEnhanced(num) {
    const numValue = parseFloat(num) || 0;
    
    try {
        return new Intl.NumberFormat(\'ar-EG\', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(numValue);
    } catch (error) {
        console.error(\'Error formatting number:\', error);
        return numValue.toFixed(2);
    }
}

// وظيفة لإصلاح عرض المبالغ في الصفحة
function fixAmountDisplay() {
    // البحث عن جميع عناصر المبالغ
    const amountElements = document.querySelectorAll(\'.stats-number, .session-cost, .amount-display\');
    
    amountElements.forEach(element => {
        const currentText = element.textContent || element.innerText;
        
        // استخراج الرقم من النص
        const numberMatch = currentText.match(/([0-9]+\.?[0-9]*)/);
        
        if (numberMatch) {
            const amount = parseFloat(numberMatch[1]);
            
            if (!isNaN(amount)) {
                // تحديث النص بالتنسيق الصحيح
                const formattedAmount = formatNumberEnhanced(amount) + \' ج.م\';
                element.innerHTML = formattedAmount;
                
                console.log(\'Fixed amount display:\', currentText, \'→\', formattedAmount);
            }
        }
    });
}

// وظيفة لمراقبة تغييرات DOM
function observeAmountChanges() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === \'childList\' || mutation.type === \'characterData\') {
                // إعادة إصلاح المبالغ عند تغيير المحتوى
                setTimeout(fixAmountDisplay, 100);
            }
        });
    });
    
    // مراقبة التغييرات في الصفحة
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
}

// وظيفة لتحديث الإحصائيات المالية
function updateFinancialStats() {
    const statsElements = document.querySelectorAll(\'[data-amount]\');
    
    statsElements.forEach(element => {
        const amount = parseFloat(element.dataset.amount) || 0;
        const formattedAmount = formatCurrencyEnhanced(amount);
        element.innerHTML = formattedAmount;
    });
}

// تشغيل الإصلاحات عند تحميل الصفحة
document.addEventListener(\'DOMContentLoaded\', function() {
    console.log(\'Finances page JavaScript enhancements loaded\');
    
    // إصلاح المبالغ الموجودة
    fixAmountDisplay();
    
    // بدء مراقبة التغييرات
    observeAmountChanges();
    
    // تحديث دوري للإحصائيات
    setInterval(function() {
        if (document.visibilityState === \'visible\') {
            fixAmountDisplay();
        }
    }, 30000); // كل 30 ثانية
});

// إضافة وظائف للنافذة العامة
window.formatCurrencyEnhanced = formatCurrencyEnhanced;
window.formatNumberEnhanced = formatNumberEnhanced;
window.fixAmountDisplay = fixAmountDisplay;
';

$enhanced_js_path = 'assets/js/finances-enhanced.js';
if (file_put_contents($enhanced_js_path, $enhanced_js_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء ملف finances-enhanced.js بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف finances-enhanced.js</p>";
}

// 4. تحديث صفحة الماليات لتضمين الملف الجديد
echo "<h2>4. تحديث صفحة الماليات</h2>";

if (file_exists($finances_php_path)) {
    $finances_content = file_get_contents($finances_php_path);
    
    // البحث عن نهاية الصفحة قبل footer
    $footer_include = "<?php require_once 'includes/footer.php'; ?>";
    
    if (strpos($finances_content, $footer_include) !== false) {
        // إضافة سكريبت الجافاسكريبت المحسن
        $enhanced_script = '
<script src="../assets/js/finances-enhanced.js"></script>
<script>
// تطبيق الإصلاحات فور تحميل الصفحة
document.addEventListener(\'DOMContentLoaded\', function() {
    // إصلاح فوري للمبالغ
    setTimeout(function() {
        if (typeof fixAmountDisplay === \'function\') {
            fixAmountDisplay();
        }
    }, 500);
});
</script>

';
        
        $updated_content = str_replace($footer_include, $enhanced_script . $footer_include, $finances_content);
        
        if (file_put_contents($finances_php_path . '.backup', $finances_content)) {
            echo "<p style='color: blue;'>ℹ️ تم إنشاء نسخة احتياطية من finances.php</p>";
        }
        
        if (file_put_contents($finances_php_path, $updated_content)) {
            echo "<p style='color: green;'>✅ تم تحديث صفحة الماليات بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في تحديث صفحة الماليات</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لم يتم العثور على نقطة الإدراج في صفحة الماليات</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف finances.php غير موجود</p>";
}

// 5. ملخص الإصلاحات
echo "<h2>📊 ملخص الإصلاحات</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ الإصلاحات المطبقة:</h4>";
echo "<ul>";
echo "<li>إنشاء ملف جافاسكريبت محسن للماليات</li>";
echo "<li>إضافة وظائف تنسيق محسنة للعملة والأرقام</li>";
echo "<li>إضافة مراقب للتغييرات في DOM</li>";
echo "<li>تحديث صفحة الماليات لتضمين الإصلاحات</li>";
echo "<li>إنشاء نسخة احتياطية من الملف الأصلي</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔄 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>قم بزيارة صفحة الماليات للتحقق من الإصلاحات</li>";
echo "<li>افتح أدوات المطور في المتصفح (F12) لمراقبة رسائل وحدة التحكم</li>";
echo "<li>اختبر إضافة مصروفات وإيرادات جديدة</li>";
echo "<li>تحقق من عرض المبالغ في جميع أقسام الصفحة</li>";
echo "<li>إذا استمرت المشكلة، شغل ملف test_finances_javascript.php للاختبار التفصيلي</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>
