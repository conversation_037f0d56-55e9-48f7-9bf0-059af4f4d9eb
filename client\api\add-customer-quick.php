<?php
// تنظيف أي إخراج سابق
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح'], JSON_UNESCAPED_UNICODE);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_customers')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإضافة العملاء'], JSON_UNESCAPED_UNICODE);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة'], JSON_UNESCAPED_UNICODE);
    exit;
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['name']) || empty(trim($input['name']))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب'], JSON_UNESCAPED_UNICODE);
    exit;
}

$customer_name = trim($input['name']);

// التحقق من طول الاسم
if (strlen($customer_name) < 2) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'اسم العميل يجب أن يكون حرفين على الأقل'], JSON_UNESCAPED_UNICODE);
    exit;
}

if (strlen($customer_name) > 100) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'اسم العميل طويل جداً'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // التحقق من وجود جدول customers
    $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($table_check->rowCount() == 0) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'جدول العملاء غير موجود'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // التحقق من هيكل جدول customers
    $customer_id_column = 'customer_id';
    try {
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
            $customer_id_column = 'id';
        }
    } catch (PDOException $e) {
        $customer_id_column = 'customer_id'; // افتراضي
    }

    // التحقق من عدم وجود عميل بنفس الاسم
    $check_stmt = $pdo->prepare("
        SELECT $customer_id_column 
        FROM customers 
        WHERE client_id = ? AND LOWER(name) = LOWER(?)
    ");
    $check_stmt->execute([$client_id, $customer_name]);
    
    if ($check_stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم مسبقاً'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // إضافة العميل الجديد
    $insert_stmt = $pdo->prepare("
        INSERT INTO customers (client_id, name, created_at) 
        VALUES (?, ?, NOW())
    ");
    
    if ($insert_stmt->execute([$client_id, $customer_name])) {
        $new_customer_id = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة العميل بنجاح',
            'customer_id' => $new_customer_id,
            'customer_name' => $customer_name
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'فشل في إضافة العميل'], JSON_UNESCAPED_UNICODE);
    }

} catch (PDOException $e) {
    error_log('خطأ في إضافة عميل جديد: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات'], JSON_UNESCAPED_UNICODE);
}
