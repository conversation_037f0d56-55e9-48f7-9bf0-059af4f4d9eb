<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../config/trial-config.php';

// التحقق من تسجيل الدخول التجريبي
function isTrialLoggedIn() {
    return isset($_SESSION['trial_id']) && !empty($_SESSION['trial_id']);
}

// التحقق من صحة الجلسة التجريبية
function isTrialSessionValid() {
    if (!isTrialLoggedIn()) {
        return false;
    }
    
    $trial_id = $_SESSION['trial_id'];
    $timeRemaining = getTrialTimeRemaining($trial_id);
    
    if ($timeRemaining['expired']) {
        // إنهاء الجلسة إذا انتهت المدة التجريبية
        endTrialSession();
        return false;
    }
    
    return true;
}

// طلب تسجيل الدخول التجريبي
function requireTrialLogin() {
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'trial-login.php' || $currentPage === 'trial-register.php') {
        return;
    }

    if (!isTrialLoggedIn()) {
        header('Location: trial-login.php');
        exit;
    }

    if (!isTrialSessionValid()) {
        header('Location: trial-login.php?error=trial_expired');
        exit;
    }
}

// إنهاء الجلسة التجريبية
function endTrialSession() {
    if (isset($_SESSION['trial_id'])) {
        unset($_SESSION['trial_id']);
        unset($_SESSION['trial_business_name']);
        unset($_SESSION['trial_owner_name']);
        unset($_SESSION['trial_email']);
    }
}

// تسجيل دخول تجريبي
function trialLogin($email, $password) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM trial_clients 
            WHERE email = ? AND is_active = 1 AND trial_end > NOW()
        ");
        $stmt->execute([$email]);
        $trial_client = $stmt->fetch();
        
        if ($trial_client && password_verify($password, $trial_client['password_hash'])) {
            $_SESSION['trial_id'] = $trial_client['trial_id'];
            $_SESSION['trial_business_name'] = $trial_client['business_name'];
            $_SESSION['trial_owner_name'] = $trial_client['owner_name'];
            $_SESSION['trial_email'] = $trial_client['email'];
            
            return [
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح!'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة أو انتهت صلاحية التجربة'
            ];
        }
    } catch (PDOException $e) {
        error_log('خطأ في تسجيل الدخول التجريبي: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء تسجيل الدخول'
        ];
    }
}

// تسجيل حساب تجريبي جديد
function trialRegister($business_name, $owner_name, $email, $phone, $password) {
    global $pdo;
    
    try {
        // التحقق من وجود البريد الإلكتروني مسبقاً
        $stmt = $pdo->prepare("SELECT trial_id FROM trial_clients WHERE email = ?");
        $stmt->execute([$email]);
        
        if ($stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'البريد الإلكتروني مسجل مسبقاً'
            ];
        }
        
        // حساب وقت انتهاء التجربة
        $trial_end = new DateTime();
        $trial_end->add(new DateInterval('PT' . TRIAL_DURATION_HOURS . 'H'));
        
        // إنشاء الحساب التجريبي
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO trial_clients (business_name, owner_name, email, phone, password_hash, trial_end) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute([$business_name, $owner_name, $email, $phone, $password_hash, $trial_end->format('Y-m-d H:i:s')])) {
            $trial_id = $pdo->lastInsertId();
            
            // إضافة البيانات التجريبية الأساسية
            if (addTrialSampleData($trial_id)) {
                // تسجيل الدخول تلقائياً
                $_SESSION['trial_id'] = $trial_id;
                $_SESSION['trial_business_name'] = $business_name;
                $_SESSION['trial_owner_name'] = $owner_name;
                $_SESSION['trial_email'] = $email;
                
                return [
                    'success' => true,
                    'message' => 'تم إنشاء الحساب التجريبي بنجاح! مدة التجربة: ' . TRIAL_DURATION_HOURS . ' ساعات'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'تم إنشاء الحساب ولكن فشل في إضافة البيانات التجريبية'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الحساب التجريبي'
            ];
        }
    } catch (PDOException $e) {
        error_log('خطأ في تسجيل الحساب التجريبي: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء إنشاء الحساب'
        ];
    }
}

// الحصول على بيانات العميل التجريبي
function getTrialClientData($trial_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM trial_clients 
            WHERE trial_id = ? AND is_active = 1
        ");
        $stmt->execute([$trial_id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('خطأ في جلب بيانات العميل التجريبي: ' . $e->getMessage());
        return false;
    }
}

// الحصول على إحصائيات التجربة
function getTrialStats($trial_id) {
    global $pdo;
    
    try {
        $stats = [];
        
        // عدد الأجهزة
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trial_devices WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $stats['devices'] = $stmt->fetchColumn();
        
        // عدد العملاء
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trial_customers WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $stats['customers'] = $stmt->fetchColumn();
        
        // عدد الجلسات النشطة
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trial_sessions WHERE trial_id = ? AND status = 'active'");
        $stmt->execute([$trial_id]);
        $stats['active_sessions'] = $stmt->fetchColumn();
        
        // إجمالي الجلسات
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trial_sessions WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $stats['total_sessions'] = $stmt->fetchColumn();
        
        // إجمالي الإيرادات
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total_cost), 0) as total FROM trial_sessions WHERE trial_id = ? AND status = 'completed'");
        $stmt->execute([$trial_id]);
        $stats['total_revenue'] = $stmt->fetchColumn();
        
        // عدد منتجات الكافتيريا
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trial_cafeteria_items WHERE trial_id = ?");
        $stmt->execute([$trial_id]);
        $stats['cafeteria_items'] = $stmt->fetchColumn();
        
        return $stats;
    } catch (PDOException $e) {
        error_log('خطأ في جلب إحصائيات التجربة: ' . $e->getMessage());
        return [
            'devices' => 0,
            'customers' => 0,
            'active_sessions' => 0,
            'total_sessions' => 0,
            'total_revenue' => 0,
            'cafeteria_items' => 0
        ];
    }
}

// تنظيف البيانات التجريبية المنتهية الصلاحية (يتم استدعاؤها دورياً)
function performTrialCleanup() {
    static $last_cleanup = 0;
    $current_time = time();
    
    // تنظيف كل 5 دقائق
    if ($current_time - $last_cleanup > TRIAL_CLEANUP_INTERVAL) {
        cleanupExpiredTrials();
        $last_cleanup = $current_time;
    }
}

// استدعاء تنظيف البيانات عند تحميل الملف
performTrialCleanup();
?>
