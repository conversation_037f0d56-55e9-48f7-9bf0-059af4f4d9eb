<?php
session_start();

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // استخدام العميل رقم 1 للاختبار
}

require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث عن العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-search me-2"></i>اختبار البحث عن العملاء</h4>
                    </div>
                    <div class="card-body">
                        <!-- معلومات الجلسة -->
                        <div class="alert alert-info">
                            <strong>معلومات الجلسة:</strong><br>
                            Client ID: <?php echo $_SESSION['client_id']; ?><br>
                            Employee ID: <?php echo $_SESSION['employee_id'] ?? 'غير محدد'; ?>
                        </div>

                        <!-- فحص قاعدة البيانات -->
                        <div class="mb-4">
                            <h5>فحص قاعدة البيانات:</h5>
                            <?php
                            try {
                                // فحص الاتصال بقاعدة البيانات
                                $pdo->query("SELECT 1");
                                echo '<div class="alert alert-success">✓ الاتصال بقاعدة البيانات يعمل</div>';
                                
                                // فحص جدول العملاء
                                $table_check = $pdo->query("SHOW TABLES LIKE 'customers'");
                                if ($table_check->rowCount() > 0) {
                                    echo '<div class="alert alert-success">✓ جدول العملاء موجود</div>';
                                    
                                    // عرض هيكل الجدول
                                    $columns = $pdo->query("DESCRIBE customers")->fetchAll(PDO::FETCH_ASSOC);
                                    echo '<div class="alert alert-info"><strong>أعمدة جدول العملاء:</strong><br>';
                                    foreach ($columns as $column) {
                                        echo "- {$column['Field']} ({$column['Type']})<br>";
                                    }
                                    echo '</div>';
                                    
                                    // عدد العملاء
                                    $count = $pdo->query("SELECT COUNT(*) FROM customers WHERE client_id = " . $_SESSION['client_id'])->fetchColumn();
                                    echo "<div class='alert alert-info'><strong>عدد العملاء للعميل {$_SESSION['client_id']}:</strong> $count</div>";
                                    
                                    // عرض بعض العملاء
                                    if ($count > 0) {
                                        $customers = $pdo->query("SELECT customer_id, name, phone, email FROM customers WHERE client_id = " . $_SESSION['client_id'] . " LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
                                        echo '<div class="alert alert-success"><strong>عينة من العملاء:</strong><br>';
                                        foreach ($customers as $customer) {
                                            echo "- ID: {$customer['customer_id']}, الاسم: {$customer['name']}, الهاتف: {$customer['phone']}<br>";
                                        }
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-danger">✗ جدول العملاء غير موجود</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
                            }
                            ?>
                        </div>

                        <!-- اختبار البحث -->
                        <div class="mb-4">
                            <h5>اختبار البحث:</h5>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="searchInput" placeholder="ادخل نص للبحث">
                                <button class="btn btn-primary" onclick="testSearch()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                            <div id="searchResults"></div>
                        </div>

                        <!-- اختبار API مباشر -->
                        <div class="mb-4">
                            <h5>اختبار API مباشر:</h5>
                            <button class="btn btn-info" onclick="testAPI('أحمد')">اختبار بـ "أحمد"</button>
                            <button class="btn btn-info" onclick="testAPI('محمد')">اختبار بـ "محمد"</button>
                            <button class="btn btn-info" onclick="testAPI('012')">اختبار بـ "012"</button>
                            <div id="apiResults" class="mt-3"></div>
                        </div>

                        <!-- سجل الأخطاء -->
                        <div class="mb-4">
                            <h5>سجل الأخطاء:</h5>
                            <div id="errorLog" class="bg-light p-3" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const errorLog = document.getElementById('errorLog');
            const timestamp = new Date().toLocaleTimeString();
            errorLog.innerHTML += `[${timestamp}] ${message}\n`;
            errorLog.scrollTop = errorLog.scrollHeight;
            console.log(message);
        }

        function testSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (query.length < 2) {
                alert('يجب إدخال حرفين على الأقل');
                return;
            }
            
            log(`بدء البحث عن: "${query}"`);
            
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
            
            fetch(`client/api/search-customers.php?q=${encodeURIComponent(query)}`)
                .then(response => {
                    log(`استجابة الخادم: ${response.status} ${response.statusText}`);
                    return response.text();
                })
                .then(text => {
                    log(`النص المستلم: ${text}`);
                    
                    try {
                        const data = JSON.parse(text);
                        log(`البيانات المحللة: ${JSON.stringify(data)}`);
                        
                        if (Array.isArray(data)) {
                            if (data.length > 0) {
                                let html = '<div class="alert alert-success">تم العثور على ' + data.length + ' نتيجة:</div>';
                                html += '<div class="list-group">';
                                data.forEach(customer => {
                                    html += `<div class="list-group-item">
                                                <strong>${customer.name}</strong><br>
                                                <small>الهاتف: ${customer.phone || 'غير محدد'}</small><br>
                                                <small>البريد: ${customer.email || 'غير محدد'}</small>
                                             </div>`;
                                });
                                html += '</div>';
                                resultsDiv.innerHTML = html;
                            } else {
                                resultsDiv.innerHTML = '<div class="alert alert-warning">لم يتم العثور على نتائج</div>';
                            }
                        } else if (data.success === false) {
                            resultsDiv.innerHTML = `<div class="alert alert-danger">خطأ: ${data.message}</div>`;
                        } else {
                            resultsDiv.innerHTML = '<div class="alert alert-danger">تنسيق استجابة غير متوقع</div>';
                        }
                    } catch (e) {
                        log(`خطأ في تحليل JSON: ${e.message}`);
                        resultsDiv.innerHTML = `<div class="alert alert-danger">خطأ في تحليل البيانات: ${e.message}<br><pre>${text}</pre></div>`;
                    }
                })
                .catch(error => {
                    log(`خطأ في الشبكة: ${error.message}`);
                    resultsDiv.innerHTML = `<div class="alert alert-danger">خطأ في الاتصال: ${error.message}</div>`;
                });
        }

        function testAPI(query) {
            log(`اختبار API مع: "${query}"`);
            
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="alert alert-info">جاري الاختبار...</div>';
            
            fetch('client/api/search-customers.php?q=' + encodeURIComponent(query))
                .then(response => response.text())
                .then(text => {
                    log(`نتيجة API لـ "${query}": ${text}`);
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>نتيجة API لـ "${query}":</h6>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;">${text}</pre>
                        </div>
                    `;
                })
                .catch(error => {
                    log(`خطأ في اختبار API: ${error.message}`);
                    resultsDiv.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
                });
        }

        // تسجيل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار');
        });
    </script>
</body>
</html>
