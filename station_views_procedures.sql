-- =====================================================
-- Views والإجراءات المخزنة - Station System
-- =====================================================
-- ملف إضافي يحتوي على Views مفيدة وإجراءات مخزنة محسنة
-- يتم تشغيله بعد تطبيق station_fixed.sql
-- =====================================================

USE `station`;

-- =====================================================
-- Views - العروض المفيدة
-- =====================================================

-- عرض تفاصيل الجلسات مع معلومات العميل والجهاز
CREATE OR REPLACE VIEW `sessions_detailed` AS
SELECT 
    s.session_id,
    s.client_id,
    cl.business_name,
    s.device_id,
    d.device_name,
    d.device_type,
    s.customer_id,
    c.name AS customer_name,
    c.phone AS customer_phone,
    s.session_type,
    s.game_type,
    s.players_count,
    s.start_time,
    s.end_time,
    s.duration_minutes,
    s.hourly_rate,
    s.time_cost,
    s.products_cost,
    s.total_cost,
    s.payment_status,
    s.notes,
    s.created_at
FROM sessions s
LEFT JOIN clients cl ON s.client_id = cl.client_id
LEFT JOIN devices d ON s.device_id = d.device_id
LEFT JOIN customers c ON s.customer_id = c.customer_id;

-- عرض إحصائيات الأجهزة
CREATE OR REPLACE VIEW `devices_stats` AS
SELECT 
    d.device_id,
    d.client_id,
    cl.business_name,
    d.device_name,
    d.device_type,
    d.status,
    d.hourly_rate,
    COUNT(s.session_id) AS total_sessions,
    COALESCE(SUM(s.duration_minutes), 0) AS total_minutes,
    COALESCE(SUM(s.total_cost), 0) AS total_revenue,
    COALESCE(AVG(s.duration_minutes), 0) AS avg_session_duration,
    MAX(s.start_time) AS last_used
FROM devices d
LEFT JOIN clients cl ON d.client_id = cl.client_id
LEFT JOIN sessions s ON d.device_id = s.device_id
GROUP BY d.device_id, d.client_id, cl.business_name, d.device_name, d.device_type, d.status, d.hourly_rate;

-- عرض إحصائيات العملاء
CREATE OR REPLACE VIEW `customers_stats` AS
SELECT 
    c.customer_id,
    c.client_id,
    cl.business_name,
    c.name,
    c.phone,
    c.email,
    COUNT(s.session_id) AS total_sessions,
    COALESCE(SUM(s.total_cost), 0) AS total_spent,
    COALESCE(AVG(s.total_cost), 0) AS avg_session_cost,
    MAX(s.start_time) AS last_visit,
    MIN(s.start_time) AS first_visit,
    c.created_at AS registration_date
FROM customers c
LEFT JOIN clients cl ON c.client_id = cl.client_id
LEFT JOIN sessions s ON c.customer_id = s.customer_id
GROUP BY c.customer_id, c.client_id, cl.business_name, c.name, c.phone, c.email, c.created_at;

-- عرض المخزون مع التفاصيل
CREATE OR REPLACE VIEW `inventory_detailed` AS
SELECT 
    ci.id,
    ci.client_id,
    cl.business_name,
    ci.name,
    ci.description,
    ci.price,
    ci.cost_price,
    (ci.price - ci.cost_price) AS profit_per_unit,
    CASE 
        WHEN ci.cost_price > 0 THEN ROUND(((ci.price - ci.cost_price) / ci.cost_price) * 100, 2)
        ELSE 0 
    END AS profit_percentage,
    ci.stock_quantity,
    ci.min_stock_level,
    ci.max_stock_level,
    CASE 
        WHEN ci.stock_quantity <= 0 THEN 'نفد المخزون'
        WHEN ci.stock_quantity <= ci.min_stock_level THEN 'مخزون منخفض'
        WHEN ci.stock_quantity >= ci.max_stock_level THEN 'مخزون مرتفع'
        ELSE 'مخزون طبيعي'
    END AS stock_status,
    ci.status,
    cat.name AS category_name,
    ci.barcode,
    ci.supplier,
    ci.last_restock_date,
    ci.created_at,
    ci.updated_at
FROM cafeteria_items ci
LEFT JOIN clients cl ON ci.client_id = cl.client_id
LEFT JOIN categories cat ON ci.category_id = cat.category_id;

-- عرض الإيرادات والمصروفات الشهرية
CREATE OR REPLACE VIEW `monthly_financial_summary` AS
SELECT 
    client_id,
    YEAR(income_date) AS year,
    MONTH(income_date) AS month,
    'income' AS type,
    SUM(amount) AS total_amount
FROM (
    SELECT client_id, created_at AS income_date, total_cost AS amount 
    FROM sessions WHERE payment_status = 'paid'
    UNION ALL
    SELECT client_id, income_date, amount 
    FROM additional_income
) AS income_data
GROUP BY client_id, YEAR(income_date), MONTH(income_date)

UNION ALL

SELECT 
    client_id,
    YEAR(expense_date) AS year,
    MONTH(expense_date) AS month,
    'expense' AS type,
    SUM(amount) AS total_amount
FROM expenses
GROUP BY client_id, YEAR(expense_date), MONTH(expense_date);

-- =====================================================
-- الإجراءات المخزنة - Stored Procedures
-- =====================================================

DELIMITER $$

-- إجراء لحساب إحصائيات العميل
CREATE PROCEDURE `GetClientStatistics`(
    IN p_client_id INT,
    IN p_start_date DATE,
    IN p_end_date DATE
)
BEGIN
    DECLARE v_total_sessions INT DEFAULT 0;
    DECLARE v_total_revenue DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_total_expenses DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_net_profit DECIMAL(10,2) DEFAULT 0.00;
    
    -- حساب عدد الجلسات والإيرادات
    SELECT 
        COUNT(*),
        COALESCE(SUM(total_cost), 0)
    INTO v_total_sessions, v_total_revenue
    FROM sessions 
    WHERE client_id = p_client_id 
    AND DATE(start_time) BETWEEN p_start_date AND p_end_date
    AND payment_status = 'paid';
    
    -- حساب المصروفات
    SELECT COALESCE(SUM(amount), 0)
    INTO v_total_expenses
    FROM expenses 
    WHERE client_id = p_client_id 
    AND expense_date BETWEEN p_start_date AND p_end_date;
    
    -- حساب صافي الربح
    SET v_net_profit = v_total_revenue - v_total_expenses;
    
    -- إرجاع النتائج
    SELECT 
        p_client_id AS client_id,
        p_start_date AS start_date,
        p_end_date AS end_date,
        v_total_sessions AS total_sessions,
        v_total_revenue AS total_revenue,
        v_total_expenses AS total_expenses,
        v_net_profit AS net_profit;
END$$

-- إجراء لبدء جلسة جديدة
CREATE PROCEDURE `StartNewSession`(
    IN p_client_id INT,
    IN p_device_id INT,
    IN p_customer_id INT,
    IN p_session_type ENUM('hourly','single','multi'),
    IN p_game_type VARCHAR(100),
    IN p_players_count INT,
    OUT p_session_id INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_device_status VARCHAR(20);
    DECLARE v_hourly_rate DECIMAL(8,2);
    DECLARE v_single_rate DECIMAL(10,2);
    DECLARE v_multi_rate DECIMAL(10,2);
    DECLARE v_session_rate DECIMAL(10,2);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء بدء الجلسة';
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
    END;
    
    START TRANSACTION;
    
    -- التحقق من حالة الجهاز
    SELECT status, hourly_rate, single_rate, multi_rate
    INTO v_device_status, v_hourly_rate, v_single_rate, v_multi_rate
    FROM devices 
    WHERE device_id = p_device_id AND client_id = p_client_id;
    
    IF v_device_status IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'الجهاز غير موجود';
        ROLLBACK;
    ELSEIF v_device_status != 'available' THEN
        SET p_success = FALSE;
        SET p_message = 'الجهاز غير متاح حالياً';
        ROLLBACK;
    ELSE
        -- تحديد السعر حسب نوع الجلسة
        CASE p_session_type
            WHEN 'hourly' THEN SET v_session_rate = v_hourly_rate;
            WHEN 'single' THEN SET v_session_rate = v_single_rate;
            WHEN 'multi' THEN SET v_session_rate = v_multi_rate;
            ELSE SET v_session_rate = v_hourly_rate;
        END CASE;
        
        -- إنشاء الجلسة
        INSERT INTO sessions (
            client_id, device_id, customer_id, session_type, 
            game_type, players_count, hourly_rate, start_time
        ) VALUES (
            p_client_id, p_device_id, p_customer_id, p_session_type,
            p_game_type, p_players_count, v_session_rate, NOW()
        );
        
        SET p_session_id = LAST_INSERT_ID();
        
        -- تحديث حالة الجهاز
        UPDATE devices 
        SET status = 'occupied' 
        WHERE device_id = p_device_id;
        
        SET p_success = TRUE;
        SET p_message = 'تم بدء الجلسة بنجاح';
        COMMIT;
    END IF;
END$$

-- إجراء لإنهاء الجلسة
CREATE PROCEDURE `EndSession`(
    IN p_session_id INT,
    IN p_client_id INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255),
    OUT p_total_cost DECIMAL(10,2)
)
BEGIN
    DECLARE v_device_id INT;
    DECLARE v_start_time TIMESTAMP;
    DECLARE v_hourly_rate DECIMAL(8,2);
    DECLARE v_duration_minutes INT;
    DECLARE v_time_cost DECIMAL(10,2);
    DECLARE v_products_cost DECIMAL(10,2);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء إنهاء الجلسة';
    END;
    
    START TRANSACTION;
    
    -- جلب بيانات الجلسة
    SELECT device_id, start_time, hourly_rate, products_cost
    INTO v_device_id, v_start_time, v_hourly_rate, v_products_cost
    FROM sessions 
    WHERE session_id = p_session_id AND client_id = p_client_id AND end_time IS NULL;
    
    IF v_device_id IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'الجلسة غير موجودة أو منتهية بالفعل';
        ROLLBACK;
    ELSE
        -- حساب المدة والتكلفة
        SET v_duration_minutes = TIMESTAMPDIFF(MINUTE, v_start_time, NOW());
        SET v_time_cost = (v_duration_minutes / 60.0) * v_hourly_rate;
        SET p_total_cost = v_time_cost + COALESCE(v_products_cost, 0);
        
        -- تحديث الجلسة
        UPDATE sessions 
        SET 
            end_time = NOW(),
            duration_minutes = v_duration_minutes,
            time_cost = v_time_cost,
            total_cost = p_total_cost
        WHERE session_id = p_session_id;
        
        -- تحديث حالة الجهاز
        UPDATE devices 
        SET status = 'available' 
        WHERE device_id = v_device_id;
        
        SET p_success = TRUE;
        SET p_message = 'تم إنهاء الجلسة بنجاح';
        COMMIT;
    END IF;
END$$

-- إجراء لتحديث المخزون
CREATE PROCEDURE `UpdateInventory`(
    IN p_product_id INT,
    IN p_client_id INT,
    IN p_movement_type ENUM('in','out','adjustment'),
    IN p_quantity INT,
    IN p_unit_cost DECIMAL(10,2),
    IN p_reference_type ENUM('purchase','sale','session','order','manual','system'),
    IN p_reference_id INT,
    IN p_notes TEXT,
    IN p_created_by INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_current_stock INT DEFAULT 0;
    DECLARE v_new_stock INT DEFAULT 0;
    DECLARE v_total_cost DECIMAL(10,2) DEFAULT 0.00;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء تحديث المخزون';
    END;
    
    START TRANSACTION;
    
    -- جلب المخزون الحالي
    SELECT stock_quantity INTO v_current_stock
    FROM cafeteria_items 
    WHERE id = p_product_id AND client_id = p_client_id;
    
    IF v_current_stock IS NULL THEN
        SET p_success = FALSE;
        SET p_message = 'المنتج غير موجود';
        ROLLBACK;
    ELSE
        -- حساب المخزون الجديد
        CASE p_movement_type
            WHEN 'in' THEN SET v_new_stock = v_current_stock + p_quantity;
            WHEN 'out' THEN SET v_new_stock = v_current_stock - p_quantity;
            WHEN 'adjustment' THEN SET v_new_stock = p_quantity;
        END CASE;
        
        -- التحقق من عدم وجود مخزون سالب
        IF v_new_stock < 0 THEN
            SET p_success = FALSE;
            SET p_message = 'لا يمكن أن يكون المخزون سالباً';
            ROLLBACK;
        ELSE
            SET v_total_cost = p_quantity * p_unit_cost;
            
            -- تسجيل حركة المخزون
            INSERT INTO inventory_movements (
                client_id, product_id, movement_type, quantity,
                previous_quantity, new_quantity, unit_cost, total_cost,
                reference_type, reference_id, notes, created_by
            ) VALUES (
                p_client_id, p_product_id, p_movement_type, p_quantity,
                v_current_stock, v_new_stock, p_unit_cost, v_total_cost,
                p_reference_type, p_reference_id, p_notes, p_created_by
            );
            
            -- تحديث المخزون
            UPDATE cafeteria_items 
            SET 
                stock_quantity = v_new_stock,
                last_restock_date = CASE WHEN p_movement_type = 'in' THEN NOW() ELSE last_restock_date END,
                status = CASE 
                    WHEN v_new_stock <= 0 THEN 'out_of_stock'
                    WHEN v_new_stock <= min_stock_level THEN 'low_stock'
                    ELSE 'available'
                END
            WHERE id = p_product_id;
            
            SET p_success = TRUE;
            SET p_message = 'تم تحديث المخزون بنجاح';
            COMMIT;
        END IF;
    END IF;
END$$

-- إجراء للتحقق من حدود العميل (محسن)
CREATE PROCEDURE `CheckClientLimit`(
    IN p_client_id INT,
    IN p_feature_type VARCHAR(50),
    IN p_feature_name VARCHAR(100),
    OUT p_limit_value INT,
    OUT p_current_usage INT,
    OUT p_can_add BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_plan_limit INT DEFAULT 0;
    DECLARE v_custom_limit INT DEFAULT NULL;
    DECLARE v_current_count INT DEFAULT 0;
    DECLARE v_client_exists INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        SET p_can_add = FALSE;
        SET p_message = 'حدث خطأ أثناء التحقق من الحدود';
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
    END;

    -- التحقق من وجود العميل
    SELECT COUNT(*) INTO v_client_exists
    FROM clients
    WHERE client_id = p_client_id AND is_active = 1;

    IF v_client_exists = 0 THEN
        SET p_limit_value = 0;
        SET p_current_usage = 0;
        SET p_can_add = FALSE;
        SET p_message = 'العميل غير موجود أو غير نشط';
    ELSE
        -- جلب حد الخطة الأساسية من جدول subscription_plans
        SELECT
            CASE p_feature_type
                WHEN 'devices' THEN sp.max_devices
                WHEN 'employees' THEN sp.max_employees
                WHEN 'customers' THEN sp.max_customers
                WHEN 'products' THEN sp.max_products
                ELSE 0
            END INTO v_plan_limit
        FROM clients c
        JOIN subscription_plans sp ON c.subscription_plan = sp.plan_name
        WHERE c.client_id = p_client_id;

        -- جلب الحد المخصص إن وجد (من جدول client_plan_limits إذا كان موجوداً)
        SELECT custom_limit INTO v_custom_limit
        FROM client_plan_limits
        WHERE client_id = p_client_id
        AND feature_type = p_feature_type
        AND feature_name = p_feature_name
        AND is_active = 1
        AND (expires_at IS NULL OR expires_at > NOW())
        LIMIT 1;

        -- تحديد الحد النهائي
        SET p_limit_value = COALESCE(v_custom_limit, v_plan_limit, 0);

        -- حساب الاستخدام الحالي
        CASE p_feature_type
            WHEN 'devices' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM devices
                WHERE client_id = p_client_id AND status != 'inactive';
            WHEN 'products' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM cafeteria_items
                WHERE client_id = p_client_id AND status != 'discontinued';
            WHEN 'employees' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM employees
                WHERE client_id = p_client_id AND is_active = 1;
            WHEN 'customers' THEN
                SELECT COUNT(*) INTO v_current_count
                FROM customers
                WHERE client_id = p_client_id AND is_active = 1;
            ELSE
                SET v_current_count = 0;
        END CASE;

        SET p_current_usage = v_current_count;

        -- تحديد إمكانية الإضافة
        IF p_limit_value = -1 THEN
            SET p_can_add = TRUE;
            SET p_message = 'غير محدود - يمكن الإضافة';
        ELSEIF p_limit_value = 0 THEN
            SET p_can_add = FALSE;
            SET p_message = 'غير مسموح بالإضافة';
        ELSEIF v_current_count < p_limit_value THEN
            SET p_can_add = TRUE;
            SET p_message = CONCAT('يمكن إضافة ', (p_limit_value - v_current_count), ' عنصر إضافي');
        ELSE
            SET p_can_add = FALSE;
            SET p_message = CONCAT('تم الوصول للحد الأقصى (', p_limit_value, ')');
        END IF;
    END IF;
END$$

-- إجراء لمنح صلاحيات افتراضية للعميل الجديد
CREATE PROCEDURE `GrantDefaultPermissionsToClient`(
    IN p_client_id INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_client_exists INT DEFAULT 0;
    DECLARE v_permissions_count INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء منح الصلاحيات';
    END;

    START TRANSACTION;

    -- التحقق من وجود العميل
    SELECT COUNT(*) INTO v_client_exists
    FROM clients
    WHERE client_id = p_client_id;

    IF v_client_exists = 0 THEN
        SET p_success = FALSE;
        SET p_message = 'العميل غير موجود';
        ROLLBACK;
    ELSE
        -- منح الصلاحيات الافتراضية (إذا كان جدول client_page_permissions موجود)
        INSERT IGNORE INTO client_page_permissions (client_id, page_id, is_enabled)
        SELECT p_client_id, page_id, TRUE
        FROM client_pages
        WHERE is_default = TRUE AND is_active = TRUE;

        -- حساب عدد الصلاحيات الممنوحة
        SELECT ROW_COUNT() INTO v_permissions_count;

        SET p_success = TRUE;
        SET p_message = CONCAT('تم منح ', v_permissions_count, ' صلاحية افتراضية');
        COMMIT;
    END IF;
END$$

-- إجراء لتنظيف البيانات القديمة
CREATE PROCEDURE `CleanupOldData`(
    IN p_client_id INT,
    IN p_days_old INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_deleted_sessions INT DEFAULT 0;
    DECLARE v_deleted_movements INT DEFAULT 0;
    DECLARE v_cutoff_date DATE;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'حدث خطأ أثناء تنظيف البيانات';
    END;

    START TRANSACTION;

    SET v_cutoff_date = DATE_SUB(CURDATE(), INTERVAL p_days_old DAY);

    -- حذف الجلسات القديمة المدفوعة
    DELETE FROM sessions
    WHERE client_id = p_client_id
    AND DATE(start_time) < v_cutoff_date
    AND payment_status = 'paid';

    SET v_deleted_sessions = ROW_COUNT();

    -- حذف حركات المخزون القديمة
    DELETE FROM inventory_movements
    WHERE client_id = p_client_id
    AND DATE(created_at) < v_cutoff_date;

    SET v_deleted_movements = ROW_COUNT();

    SET p_success = TRUE;
    SET p_message = CONCAT('تم حذف ', v_deleted_sessions, ' جلسة و ', v_deleted_movements, ' حركة مخزون');
    COMMIT;
END$$

DELIMITER ;

-- =====================================================
-- جداول إضافية مطلوبة للإجراءات
-- =====================================================

-- جدول حدود العملاء المخصصة (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `client_plan_limits` (
  `limit_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `custom_limit` int(11) DEFAULT -1 COMMENT 'حد مخصص يتجاوز حد الخطة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الحد المخصص',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الحد المخصص',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`limit_id`),
  UNIQUE KEY `uk_client_feature_limit` (`client_id`, `feature_type`, `feature_name`),
  INDEX `idx_client_limits_client` (`client_id`),
  INDEX `idx_client_limits_active` (`is_active`),
  CONSTRAINT `fk_client_limits_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حدود العملاء المخصصة';

-- جدول صفحات العملاء (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `client_pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `uk_page_name` (`page_name`),
  INDEX `idx_page_active` (`is_active`),
  INDEX `idx_page_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صفحات النظام المتاحة للعملاء';

-- جدول صلاحيات صفحات العملاء (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `client_page_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_page_permission` (`client_id`, `page_id`),
  INDEX `idx_client_page_perm_client` (`client_id`),
  INDEX `idx_client_page_perm_page` (`page_id`),
  CONSTRAINT `fk_client_page_perm_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_client_page_perm_page` FOREIGN KEY (`page_id`) REFERENCES `client_pages` (`page_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات صفحات العملاء';

-- إدراج الصفحات الافتراضية
INSERT IGNORE INTO `client_pages` (`page_name`, `page_label`, `page_url`, `page_icon`, `category`, `description`, `is_active`, `is_default`) VALUES
('dashboard', 'لوحة التحكم', 'dashboard.php', 'fas fa-tachometer-alt', 'main', 'الصفحة الرئيسية وإحصائيات المحل', 1, 1),
('profile', 'الملف الشخصي', 'profile.php', 'fas fa-user', 'main', 'إدارة بيانات الحساب الشخصي', 1, 1),
('devices', 'إدارة الأجهزة', 'devices.php', 'fas fa-gamepad', 'devices', 'إضافة وإدارة أجهزة الألعاب', 1, 1),
('sessions', 'إدارة الجلسات', 'sessions.php', 'fas fa-play-circle', 'sessions', 'بدء وإنهاء جلسات اللعب', 1, 1),
('customers', 'إدارة العملاء', 'customers.php', 'fas fa-users', 'customers', 'إدارة بيانات عملاء المحل', 1, 1),
('cafeteria', 'إدارة الكافتيريا', 'cafeteria.php', 'fas fa-coffee', 'cafeteria', 'إدارة منتجات الكافتيريا', 1, 0),
('invoices', 'الفواتير', 'invoices.php', 'fas fa-file-invoice', 'invoices', 'إدارة وطباعة الفواتير', 1, 1),
('reports', 'التقارير', 'reports.php', 'fas fa-chart-bar', 'reports', 'تقارير مالية وإحصائية', 1, 1),
('settings', 'الإعدادات', 'settings.php', 'fas fa-cog', 'settings', 'إعدادات النظام والمحل', 1, 1);

-- =====================================================
-- إنهاء الملف
-- =====================================================
