<?php
/**
 * إصلاح مشكلة حذف التصنيفات في صفحة الكافتيريا
 * يحل مشكلة القيود المرجعية التي تمنع حذف التصنيفات
 * PlayGood System - Category Deletion Fix
 */

require_once 'config/database.php';

// تعيين ترميز UTF-8
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشكلة حذف التصنيفات - PlayGood</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".container { max-width: 900px; margin: 20px auto; padding: 20px; }";
echo ".card { margin-bottom: 20px; } .step { margin-bottom: 30px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h1 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح مشكلة حذف التصنيفات</h1>";
echo "</div>";
echo "<div class='card-body'>";

$fixes_applied = 0;
$errors = [];

try {
    echo "<div class='step'>";
    echo "<h2><i class='fas fa-search me-2'></i>1. فحص المشكلة الحالية</h2>";

    // فحص القيود المرجعية
    echo "<h5>فحص القيود المرجعية:</h5>";

    // التحقق من وجود الجداول أولاً
    $tables_check = $pdo->query("SHOW TABLES LIKE 'categories'")->rowCount();
    if ($tables_check == 0) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول categories غير موجود!</p>";
        throw new Exception("جدول categories غير موجود");
    }

    $tables_check = $pdo->query("SHOW TABLES LIKE 'cafeteria_items'")->rowCount();
    if ($tables_check == 0) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>جدول cafeteria_items غير موجود!</p>";
        throw new Exception("جدول cafeteria_items غير موجود");
    }
    $constraints = $pdo->query("
        SELECT
            rc.CONSTRAINT_NAME,
            kcu.TABLE_NAME,
            kcu.COLUMN_NAME,
            kcu.REFERENCED_TABLE_NAME,
            kcu.REFERENCED_COLUMN_NAME,
            rc.DELETE_RULE,
            rc.UPDATE_RULE
        FROM information_schema.REFERENTIAL_CONSTRAINTS rc
        JOIN information_schema.KEY_COLUMN_USAGE kcu
            ON rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            AND rc.CONSTRAINT_SCHEMA = kcu.CONSTRAINT_SCHEMA
        WHERE rc.CONSTRAINT_SCHEMA = DATABASE()
        AND (kcu.TABLE_NAME = 'cafeteria_items' OR kcu.REFERENCED_TABLE_NAME = 'categories')
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
    ")->fetchAll();
    
    $problematic_constraint = null;
    if (!empty($constraints)) {
        echo "<ul>";
        foreach ($constraints as $constraint) {
            echo "<li><strong>" . htmlspecialchars($constraint['CONSTRAINT_NAME']) . "</strong>: ";
            echo htmlspecialchars($constraint['TABLE_NAME']) . "." . htmlspecialchars($constraint['COLUMN_NAME']);
            echo " → " . htmlspecialchars($constraint['REFERENCED_TABLE_NAME']) . "." . htmlspecialchars($constraint['REFERENCED_COLUMN_NAME']);
            echo " (DELETE: " . htmlspecialchars($constraint['DELETE_RULE']) . ", UPDATE: " . htmlspecialchars($constraint['UPDATE_RULE']) . ")";

            if ($constraint['DELETE_RULE'] === 'RESTRICT' || $constraint['DELETE_RULE'] === 'NO ACTION') {
                echo " <span class='error'><i class='fas fa-exclamation-triangle'></i> مشكل</span>";
                $problematic_constraint = $constraint['CONSTRAINT_NAME'];
            } else {
                echo " <span class='success'><i class='fas fa-check'></i> جيد</span>";
            }
            echo "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='success'><i class='fas fa-check me-2'></i>لا توجد قيود مرجعية مرتبطة بجدول التصنيفات</p>";
    }
    echo "</div>";

    echo "<div class='step'>";
    echo "<h2><i class='fas fa-wrench me-2'></i>2. إصلاح القيود المرجعية</h2>";
    
    if ($problematic_constraint) {
        echo "<h5>إزالة القيد المرجعي المشكل:</h5>";
        try {
            $pdo->exec("ALTER TABLE cafeteria_items DROP FOREIGN KEY $problematic_constraint");
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم إزالة القيد المرجعي '$problematic_constraint'</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في إزالة القيد: " . $e->getMessage() . "</p>";
            $errors[] = $e->getMessage();
        }
        
        echo "<h5>إضافة قيد مرجعي محسن:</h5>";
        try {
            // التأكد من أن عمود category_id يقبل NULL
            $pdo->exec("ALTER TABLE cafeteria_items MODIFY COLUMN category_id INT NULL");
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم تعديل عمود category_id ليقبل NULL</p>";
            
            // إضافة قيد جديد يسمح بـ SET NULL عند حذف التصنيف
            $pdo->exec("
                ALTER TABLE cafeteria_items 
                ADD CONSTRAINT fk_cafeteria_category_safe 
                FOREIGN KEY (category_id) REFERENCES categories(category_id) 
                ON DELETE SET NULL ON UPDATE CASCADE
            ");
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم إضافة قيد مرجعي محسن يسمح بحذف التصنيفات</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
            // قد يكون العمود category_id غير موجود أو القيد موجود مسبقاً
        }
    } else {
        echo "<p class='success'><i class='fas fa-check me-2'></i>لا توجد قيود مرجعية مشكلة</p>";
    }
    echo "</div>";

    echo "<div class='step'>";
    echo "<h2><i class='fas fa-database me-2'></i>3. فحص البيانات</h2>";
    
    // فحص التصنيفات
    echo "<h5>التصنيفات الموجودة:</h5>";
    $categories = $pdo->query("SELECT category_id, name, client_id FROM categories ORDER BY name")->fetchAll();
    if (!empty($categories)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-striped'>";
        echo "<thead><tr><th>معرف</th><th>الاسم</th><th>معرف العميل</th><th>عدد المنتجات</th></tr></thead>";
        echo "<tbody>";
        foreach ($categories as $category) {
            // عد المنتجات المرتبطة بكل تصنيف
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM cafeteria_items WHERE category = ? AND client_id = ?");
            $stmt->execute([$category['name'], $category['client_id']]);
            $product_count = $stmt->fetchColumn();
            
            echo "<tr>";
            echo "<td>" . $category['category_id'] . "</td>";
            echo "<td>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td>" . $category['client_id'] . "</td>";
            echo "<td>" . $product_count . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد تصنيفات</p>";
    }
    echo "</div>";

    echo "<div class='step'>";
    echo "<h2><i class='fas fa-test-tube me-2'></i>4. اختبار الحذف</h2>";
    
    // إنشاء تصنيف تجريبي
    echo "<h5>إنشاء تصنيف تجريبي:</h5>";
    try {
        $test_category_name = 'تصنيف تجريبي ' . date('H:i:s');
        $stmt = $pdo->prepare("INSERT INTO categories (name, client_id) VALUES (?, 1)");
        $stmt->execute([$test_category_name]);
        $test_category_id = $pdo->lastInsertId();
        
        echo "<p class='success'><i class='fas fa-check me-2'></i>تم إنشاء تصنيف تجريبي: '$test_category_name' (معرف: $test_category_id)</p>";
        
        // محاولة حذف التصنيف التجريبي
        echo "<h5>اختبار حذف التصنيف:</h5>";
        $stmt = $pdo->prepare("DELETE FROM categories WHERE category_id = ? AND client_id = 1");
        $stmt->execute([$test_category_id]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'><i class='fas fa-check me-2'></i>تم حذف التصنيف التجريبي بنجاح! المشكلة تم حلها.</p>";
            $fixes_applied++;
        } else {
            echo "<p class='error'><i class='fas fa-times me-2'></i>فشل في حذف التصنيف التجريبي</p>";
            $errors[] = "فشل في حذف التصنيف التجريبي";
        }
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في اختبار الحذف: " . $e->getMessage() . "</p>";
        $errors[] = $e->getMessage();
    }
    echo "</div>";

} catch (PDOException $e) {
    echo "<p class='error'><i class='fas fa-times me-2'></i>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

// ملخص النتائج
echo "<div class='step'>";
echo "<h2><i class='fas fa-clipboard-check me-2'></i>ملخص النتائج</h2>";

if ($fixes_applied > 0) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>تم تطبيق $fixes_applied إصلاح بنجاح!</h5>";
    echo "<p>يجب أن تعمل وظيفة حذف التصنيفات الآن بشكل طبيعي.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>لم يتم تطبيق أي إصلاحات</h5>";
    echo "<p>قد تكون المشكلة محلولة مسبقاً أو تحتاج إلى فحص يدوي.</p>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تحذيرات:</h5>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-lightbulb me-2'></i>خطوات إضافية:</h5>";
echo "<ol>";
echo "<li>تأكد من أن صفحة الكافتيريا تعمل بشكل طبيعي</li>";
echo "<li>جرب حذف تصنيف فارغ (بدون منتجات) للتأكد من عمل الوظيفة</li>";
echo "<li>إذا استمرت المشكلة، تحقق من سجلات الأخطاء في PHP</li>";
echo "</ol>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='client/cafeteria.php' class='btn btn-primary'>";
echo "<i class='fas fa-arrow-left me-2'></i>العودة إلى صفحة الكافتيريا";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
