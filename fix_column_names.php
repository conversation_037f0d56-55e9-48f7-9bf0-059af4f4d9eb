<?php
/**
 * إصلاح أسماء الأعمدة في قاعدة البيانات - PlayGood
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// زيادة حد الذاكرة
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);

echo "<h1>🔧 إصلاح أسماء الأعمدة - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; direction: rtl;'>";

try {
    require_once 'config/database.php';
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص هيكل جدول clients الحالي
    echo "<h2>1. فحص هيكل جدول clients</h2>";
    
    $columns_check = $pdo->query("DESCRIBE clients");
    $existing_columns = [];
    while ($row = $columns_check->fetch()) {
        $existing_columns[$row['Field']] = $row['Type'];
    }
    
    echo "<p><strong>الأعمدة الموجودة:</strong></p>";
    echo "<ul>";
    foreach ($existing_columns as $column => $type) {
        echo "<li><strong>$column:</strong> $type</li>";
    }
    echo "</ul>";
    
    $fixes_applied = 0;
    
    // إصلاح أسماء الأعمدة
    echo "<h2>2. توحيد أسماء الأعمدة</h2>";
    
    // إضافة الأعمدة المطلوبة إذا لم تكن موجودة
    $required_columns = [
        'business_name' => 'VARCHAR(255) NOT NULL DEFAULT "مركز الألعاب"',
        'owner_name' => 'VARCHAR(255) NOT NULL DEFAULT "صاحب المحل"',
        'email' => 'VARCHAR(255) DEFAULT NULL',
        'phone' => 'VARCHAR(20) DEFAULT NULL',
        'address' => 'TEXT DEFAULT NULL',
        'business_type' => 'VARCHAR(50) DEFAULT "gaming_center"',
        'description' => 'TEXT DEFAULT NULL',
        'working_hours' => 'VARCHAR(255) DEFAULT "من 9 صباحاً إلى 12 منتصف الليل"',
        'password' => 'VARCHAR(255) DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!array_key_exists($column_name, $existing_columns)) {
            try {
                $sql = "ALTER TABLE clients ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ تم إضافة عمود: $column_name</p>";
                $fixes_applied++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column_name موجود مسبقاً</p>";
        }
    }
    
    // نقل البيانات من العمود القديم إلى الجديد
    echo "<h2>3. نقل البيانات بين الأعمدة</h2>";
    
    // إذا كان هناك عمود name، انقل البيانات إلى business_name
    if (array_key_exists('name', $existing_columns)) {
        try {
            $pdo->exec("UPDATE clients SET business_name = name WHERE business_name IS NULL OR business_name = ''");
            echo "<p style='color: green;'>✅ تم نقل البيانات من name إلى business_name</p>";
            $fixes_applied++;
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ خطأ في نقل البيانات: " . $e->getMessage() . "</p>";
        }
    }
    
    // تحديث البيانات الفارغة
    echo "<h2>4. تحديث البيانات الفارغة</h2>";
    
    $updates = [
        "UPDATE clients SET business_name = 'مركز الألعاب' WHERE business_name IS NULL OR business_name = ''" => "أسماء المحلات",
        "UPDATE clients SET owner_name = 'صاحب المحل' WHERE owner_name IS NULL OR owner_name = ''" => "أسماء الملاك",
        "UPDATE clients SET business_type = 'gaming_center' WHERE business_type IS NULL OR business_type = ''" => "أنواع المحلات",
        "UPDATE clients SET working_hours = 'من 9 صباحاً إلى 12 منتصف الليل' WHERE working_hours IS NULL OR working_hours = ''" => "ساعات العمل"
    ];
    
    foreach ($updates as $sql => $description) {
        try {
            $affected = $pdo->exec($sql);
            if ($affected > 0) {
                echo "<p style='color: green;'>✅ تم تحديث $affected صف في $description</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: blue;'>ℹ️ لا توجد صفوف تحتاج تحديث في $description</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ خطأ في تحديث $description: " . $e->getMessage() . "</p>";
        }
    }
    
    // إضافة بيانات افتراضية إذا لم تكن موجودة
    echo "<h2>5. إضافة بيانات افتراضية</h2>";
    
    $client_check = $pdo->query("SELECT COUNT(*) as count FROM clients");
    $client_count = $client_check->fetch()['count'];
    
    if ($client_count == 0) {
        $default_client = $pdo->prepare("
            INSERT INTO clients (client_id, business_name, owner_name, email, phone, address, business_type, password) 
            VALUES (1, 'مركز الألعاب', 'صاحب المحل', '<EMAIL>', '***********', 'شارع فريد - الأحساء', 'gaming_center', ?)
        ");
        $default_password = password_hash('123456', PASSWORD_DEFAULT);
        $default_client->execute([$default_password]);
        echo "<p style='color: green;'>✅ تم إضافة عميل افتراضي</p>";
        $fixes_applied++;
    } else {
        echo "<p style='color: blue;'>ℹ️ يوجد $client_count عميل في النظام</p>";
    }
    
    // اختبار الاستعلامات الجديدة
    echo "<h2>6. اختبار الاستعلامات</h2>";
    
    try {
        $test_client = $pdo->query("SELECT client_id, business_name, owner_name, email FROM clients LIMIT 1");
        $client_data = $test_client->fetch();
        if ($client_data) {
            echo "<p style='color: green;'>✅ استعلام العملاء يعمل:</p>";
            echo "<ul>";
            echo "<li><strong>اسم المحل:</strong> " . htmlspecialchars($client_data['business_name']) . "</li>";
            echo "<li><strong>اسم المالك:</strong> " . htmlspecialchars($client_data['owner_name']) . "</li>";
            echo "<li><strong>البريد:</strong> " . htmlspecialchars($client_data['email']) . "</li>";
            echo "</ul>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام العملاء: " . $e->getMessage() . "</p>";
    }
    
    // عرض الهيكل النهائي
    echo "<h2>7. الهيكل النهائي لجدول clients</h2>";
    
    $final_columns = $pdo->query("DESCRIBE clients");
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم العمود</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    while ($column = $final_columns->fetch()) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // النتائج النهائية
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم إصلاح أسماء الأعمدة بنجاح!</h3>";
    echo "<ul>";
    echo "<li>تم تطبيق $fixes_applied إصلاح</li>";
    echo "<li><strong>business_name:</strong> اسم المحل</li>";
    echo "<li><strong>owner_name:</strong> اسم صاحب المحل</li>";
    echo "<li><strong>name:</strong> (إذا كان موجود) تم نقل بياناته إلى business_name</li>";
    echo "<li>جميع الأعمدة المطلوبة موجودة ومحدثة</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الإصلاح</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>اختبر النظام الآن</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='client/profile.php' style='background: #667eea; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>الملف الشخصي</a>";
echo "<a href='client/settings.php' style='background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>إعدادات المحل</a>";
echo "<a href='client/dashboard.php' style='background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
?>
