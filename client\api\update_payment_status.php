<?php
/**
 * API لتحديث حالة الدفع للفاتورة
 */

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير مدعومة']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'بيانات غير صحيحة']);
    exit;
}

$invoice_id = isset($input['invoice_id']) ? intval($input['invoice_id']) : 0;
$payment_status = isset($input['payment_status']) ? trim($input['payment_status']) : '';

// التحقق من صحة البيانات
if ($invoice_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'معرف الفاتورة غير صحيح']);
    exit;
}

$allowed_statuses = ['pending', 'paid', 'cancelled'];
if (!in_array($payment_status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'error' => 'حالة الدفع غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود الفاتورة وملكيتها للعميل
    $check_stmt = $pdo->prepare("
        SELECT i.invoice_id, i.payment_status, s.session_id
        FROM invoices i
        JOIN sessions s ON i.session_id = s.session_id
        JOIN devices d ON s.device_id = d.device_id
        WHERE i.invoice_id = ? AND d.client_id = ?
    ");
    $check_stmt->execute([$invoice_id, $client_id]);
    $invoice = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo json_encode(['success' => false, 'error' => 'الفاتورة غير موجودة أو غير مصرح بالوصول إليها']);
        exit;
    }
    
    // التحقق من أن الحالة مختلفة
    if ($invoice['payment_status'] === $payment_status) {
        echo json_encode(['success' => false, 'error' => 'الحالة مطابقة للحالة الحالية']);
        exit;
    }
    
    // تحديث حالة الدفع
    $update_stmt = $pdo->prepare("
        UPDATE invoices
        SET payment_status = ?
        WHERE invoice_id = ?
    ");

    $result = $update_stmt->execute([$payment_status, $invoice_id]);
    
    if ($result) {
        // تسجيل العملية في سجل التغييرات (إذا كان الجدول موجود)
        try {
            $log_stmt = $pdo->prepare("
                INSERT INTO payment_status_log (
                    invoice_id, 
                    old_status, 
                    new_status, 
                    changed_by, 
                    changed_at
                ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ");
            $log_stmt->execute([
                $invoice_id, 
                $invoice['payment_status'], 
                $payment_status, 
                $client_id
            ]);
        } catch (PDOException $e) {
            // إذا لم يكن جدول السجل موجود، تجاهل الخطأ
        }
        
        // تحديد رسالة النجاح
        $status_messages = [
            'pending' => 'تم تعيين الفاتورة كمعلقة',
            'paid' => 'تم تأكيد دفع الفاتورة',
            'cancelled' => 'تم إلغاء الفاتورة'
        ];
        
        echo json_encode([
            'success' => true, 
            'message' => $status_messages[$payment_status],
            'new_status' => $payment_status,
            'invoice_id' => $invoice_id
        ]);
        
    } else {
        echo json_encode(['success' => false, 'error' => 'فشل في تحديث حالة الدفع']);
    }
    
} catch (PDOException $e) {
    error_log("Payment status update error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'حدث خطأ في قاعدة البيانات']);
}
?>
