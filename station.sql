-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 12, 2025 at 01:43 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `station`
--

-- --------------------------------------------------------

--
-- Table structure for table `additional_income`
--

CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `email`, `password_hash`, `full_name`, `role`, `created_at`, `last_login`, `is_active`) VALUES
(1, 'admin', '<EMAIL>', '$2a$12$vFAjvx8m93Bb76ogrJq4yuu1Pw38jFvhaRibQBQ0cmZ5IleS3jm8G', 'مدير النظام', 'super_admin', '2025-06-08 08:28:08', '2025-06-09 09:26:05', 1);

-- --------------------------------------------------------

--
-- Table structure for table `business_settings`
--

CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cafeteria_items`
--

CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category_id` int(11) DEFAULT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cafeteria_items`
--

INSERT INTO `cafeteria_items` (`id`, `name`, `price`, `category`, `description`, `created_at`, `category_id`, `client_id`) VALUES
(13, 'شاي', 5.00, 'مشروبات ساخنة', 'شاي سادة', '2025-06-09 10:38:25', NULL, 1),
(14, 'شاي بحليب', 8.00, 'مشروبات ساخنة', 'شاي بحليب', '2025-06-09 10:38:42', NULL, 1),
(15, 'قهوة', 10.00, 'مشروبات ساخنة', '', '2025-06-09 11:06:50', NULL, 1),
(16, 'نسكافية', 15.00, 'مشروبات ساخنة', '', '2025-06-09 11:07:01', NULL, 1),
(17, 'قهوة تركي', 25.00, 'مشروبات ساخنة', '', '2025-06-09 11:07:13', NULL, 1),
(18, 'قهوة فرنساوي', 35.00, 'مشروبات ساخنة', '', '2025-06-09 11:07:23', NULL, 1),
(19, 'بيبسي', 12.00, 'مشروبات ساقعة', '', '2025-06-09 11:07:35', NULL, 1),
(20, 'سبيرو سباتس ابيض', 15.00, 'مشروبات ساقعة', '', '2025-06-09 11:07:48', NULL, 1),
(21, 'سبيرو سباتس اسود', 15.00, 'مشروبات ساقعة', '', '2025-06-09 11:07:59', NULL, 1),
(23, 'سبرايت', 12.00, 'مشروبات ساقعة', '', '2025-06-10 18:34:10', NULL, 1);

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `client_id` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`category_id`, `name`, `client_id`) VALUES
(7, 'مشروبات ساقعة', 1),
(8, 'مشروبات ساخنة', 1),
(9, 'مأكولات', 1),
(10, 'مقبلات', 1),
(12, 'تسالي', 1);

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `name` varchar(255) NOT NULL DEFAULT 'مركز الألعاب',
  `password` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`client_id`, `business_name`, `owner_name`, `email`, `phone`, `password_hash`, `address`, `city`, `subscription_plan`, `subscription_start`, `subscription_end`, `is_active`, `created_at`, `updated_at`, `business_type`, `description`, `working_hours`, `name`, `password`) VALUES
(1, 'playgood', 'وائل', '<EMAIL>', '***********', '$2y$10$txuFt9nn1DVao4bRaz2KoOQYi6tZAPh0nlymJpTLsB3XfN5v9b6J.', 'الجيزة', 'الجيزة', 'basic', '2025-06-08', '2025-07-08', 1, '2025-06-08 08:35:48', '2025-06-12 07:37:01', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL),
(2, 'محمد', 'سمير', '<EMAIL>', '***********', '$2y$10$nbG/.9myzQn1ux2Hhuv2mOzFHiV/w1IT9p2tRadhGPsCi4dOtBH8.', '7567\r\n33', '7657', 'basic', '2025-06-09', '2025-07-09', 1, '2025-06-09 09:31:49', '2025-06-09 09:31:49', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL),
(3, 'egypto', 'تامر', '<EMAIL>', '***********', '$2y$10$fmrMFKeY2BJjVHfutQpFT.3HTM1PjOZ1uWsn.PSy6kIBBQj6hZAnm', 'cairo', 'cairo', 'basic', '2025-06-09', '2025-07-09', 1, '2025-06-09 11:52:31', '2025-06-09 11:52:31', 'gaming_center', NULL, 'من 9 صباحاً إلى 12 منتصف الليل', 'مركز الألعاب', NULL),
(4, 'PlayStation', 'ahmed', '<EMAIL>', '201141453034', '$2y$10$kyPCoB5hoMRIhR66ms/JQ.ZrU1vsOfP1Ud4ZsMWaEiP0Dzj/oKF7.', 'مصر - الجيزة', 'الجيزة', 'basic', '2025-06-12', '2025-07-12', 1, '2025-06-12 06:06:24', '2025-06-12 06:49:05', 'gaming_center', '', 'من 9 صباحاً إلى 12 منتصف الليل', 'online store', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`customer_id`, `client_id`, `name`, `phone`, `email`, `notes`, `created_at`, `created_by`, `updated_at`, `updated_by`) VALUES
(7, 1, 'أحمد محمد', '01234567890', '<EMAIL>', NULL, '2025-06-10 18:15:26', NULL, NULL, NULL),
(8, 1, 'فاطمة علي', '01234567891', '<EMAIL>', NULL, '2025-06-10 18:15:26', NULL, NULL, NULL),
(9, 1, 'محمد حسن', '01234567892', '<EMAIL>', NULL, '2025-06-10 18:15:26', NULL, NULL, NULL),
(10, 1, 'سارة أحمد', '01234567893', '<EMAIL>', NULL, '2025-06-10 18:15:26', NULL, NULL, NULL),
(11, 1, 'عمر خالد', '01234567894', '<EMAIL>', NULL, '2025-06-10 18:15:26', NULL, NULL, NULL),
(22, 4, 'عمر يحيي', '01111111111', NULL, NULL, '2025-06-12 07:12:39', NULL, NULL, NULL),
(23, 4, 'سيد عرفات', '010001000100', NULL, NULL, '2025-06-12 07:29:49', NULL, NULL, NULL),
(25, 1, 'فاطمة حسن محمود', '01987654321', '<EMAIL>', NULL, '2025-06-12 08:22:34', NULL, NULL, NULL),
(26, 1, 'محمد عبدالله أحمد', '01122334455', '<EMAIL>', NULL, '2025-06-12 08:22:34', NULL, NULL, NULL),
(27, 1, 'سارة علي حسن', '01555666777', '<EMAIL>', NULL, '2025-06-12 08:22:34', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `devices`
--

CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL,
  `single_rate` decimal(10,2) DEFAULT NULL,
  `multi_rate` decimal(10,2) DEFAULT NULL,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `devices`
--

INSERT INTO `devices` (`device_id`, `client_id`, `device_name`, `device_type`, `hourly_rate`, `single_rate`, `multi_rate`, `status`, `room_id`, `created_at`, `updated_at`) VALUES
(1, 1, 'جهاز 1', 'PS5', 15.00, 15.00, 25.00, 'occupied', 1, '2025-06-09 06:27:44', '2025-06-10 19:46:26'),
(3, 1, 'جهاز 2', 'PS4', 10.00, 10.00, 20.00, 'available', NULL, '2025-06-09 06:34:02', '2025-06-10 19:43:41'),
(4, 2, '434', '', 0.00, NULL, NULL, 'available', NULL, '2025-06-09 09:33:07', '2025-06-09 09:33:07'),
(5, 2, '', '', 0.00, NULL, NULL, 'available', NULL, '2025-06-09 09:33:09', '2025-06-09 09:33:09'),
(6, 2, '', '', 0.00, NULL, NULL, 'available', NULL, '2025-06-09 09:33:10', '2025-06-09 09:33:10'),
(7, 2, '', '', 0.00, NULL, NULL, 'available', NULL, '2025-06-09 09:33:11', '2025-06-09 09:33:11'),
(8, 2, '', '', 0.00, NULL, NULL, 'available', NULL, '2025-06-09 09:33:19', '2025-06-09 09:33:19'),
(10, 1, 'جهاز 3', 'Xbox', 25.00, 25.00, 35.00, 'occupied', NULL, '2025-06-09 09:48:22', '2025-06-10 19:43:36'),
(11, 1, 'جهاز 4', 'PC', 20.00, 20.00, 25.00, 'available', NULL, '2025-06-09 10:39:36', '2025-06-10 19:15:23'),
(13, 1, 'جهاز 5', 'PS4', 10.00, 10.00, 15.00, 'available', NULL, '2025-06-10 18:32:08', '2025-06-10 18:32:08'),
(14, 4, 'ps 1', 'PS4', 10.00, 10.00, 15.00, 'occupied', NULL, '2025-06-12 06:30:11', '2025-06-12 07:13:12'),
(15, 4, 'جهاز 2', 'PS5', 15.00, 15.00, 20.00, 'available', NULL, '2025-06-12 07:19:44', '2025-06-12 07:19:44');

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `role` enum('manager','cashier','waiter','cleaner') NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `hire_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`id`, `client_id`, `name`, `phone`, `role`, `salary`, `hire_date`, `created_at`, `updated_at`, `username`, `password_hash`, `last_login`, `is_active`, `email`, `address`) VALUES
(1, 1, 'tamer', '***********', 'cashier', 5000.00, '2025-06-09', '2025-06-09 15:26:27', '2025-06-10 20:01:38', 'tamer746', '$2y$10$zW0nyRAAARZdsLfG/6SWTewRWWzwJ5WQ0ptim4TX/ry89qbEQZRsK', '2025-06-10 20:01:38', 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`id`, `client_id`, `expense_type_id`, `amount`, `description`, `expense_date`, `receipt_number`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 19, 1000.00, '', '2025-06-12', '', 1, '2025-06-12 11:29:22', '2025-06-12 11:29:22');

-- --------------------------------------------------------

--
-- Table structure for table `expense_types`
--

CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expense_types`
--

INSERT INTO `expense_types` (`id`, `client_id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 2, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(2, 2, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(3, 2, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(4, 2, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(5, 2, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(6, 2, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(7, 2, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(8, 2, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(9, 3, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(10, 3, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(11, 3, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(12, 3, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(13, 3, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(14, 3, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(15, 3, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(16, 3, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(17, 1, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(18, 1, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(19, 1, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(20, 1, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(21, 1, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(22, 1, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(23, 1, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(24, 1, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(25, 4, 'فواتير الكهرباء', 'فواتير استهلاك الكهرباء الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(26, 4, 'فواتير الإنترنت', 'فواتير خدمة الإنترنت الشهرية', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(27, 4, 'إيجار المحل', 'إيجار المحل الشهري', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(28, 4, 'صيانة الأجهزة', 'تكاليف صيانة وإصلاح الأجهزة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(29, 4, 'مشتريات الكافتيريا', 'شراء منتجات ومشروبات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(30, 4, 'رواتب الموظفين', 'رواتب ومكافآت الموظفين', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(31, 4, 'مصروفات إدارية', 'مصروفات إدارية متنوعة', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(32, 4, 'تسويق وإعلان', 'تكاليف التسويق والإعلان', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46');

-- --------------------------------------------------------

--
-- Table structure for table `income_types`
--

CREATE TABLE `income_types` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `income_types`
--

INSERT INTO `income_types` (`id`, `client_id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 2, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(2, 2, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(3, 2, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(4, 2, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(5, 2, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(6, 3, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(7, 3, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(8, 3, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(9, 3, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(10, 3, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(11, 1, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(12, 1, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(13, 1, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(14, 1, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(15, 1, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(16, 4, 'إيرادات الجلسات', 'إيرادات من جلسات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(17, 4, 'مبيعات الكافتيريا', 'إيرادات من مبيعات الكافتيريا', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(18, 4, 'خدمات إضافية', 'إيرادات من خدمات إضافية أخرى', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(19, 4, 'تأجير قاعات', 'إيرادات من تأجير القاعات للمناسبات', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46'),
(20, 4, 'بيع أكسسوارات', 'إيرادات من بيع أكسسوارات الألعاب', 1, '2025-06-12 11:28:46', '2025-06-12 11:28:46');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `time_cost` decimal(10,2) NOT NULL,
  `products_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`invoice_id`, `session_id`, `invoice_number`, `time_cost`, `products_cost`, `total_cost`, `payment_status`, `created_at`, `created_by`) VALUES
(1, 5, '202506090005', 25.00, 15.00, 40.00, 'pending', '2025-06-09 11:35:54', 1),
(2, 6, '202506090006', 20.00, 35.00, 55.00, 'pending', '2025-06-09 11:38:47', 1),
(3, 4, '202506090004', 30.00, 18.00, 48.00, 'pending', '2025-06-09 11:44:23', 1),
(4, 3, '202506090003', 75.00, 5.00, 80.00, 'pending', '2025-06-09 11:44:26', 1),
(5, 7, '202506090007', 25.00, 0.00, 25.00, 'pending', '2025-06-09 11:44:28', 1),
(6, 8, '202506090008', 20.00, 0.00, 20.00, 'pending', '2025-06-09 11:44:31', 1);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_settings`
--

CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoice_settings`
--

INSERT INTO `invoice_settings` (`id`, `client_id`, `header_color`, `footer_text`, `footer_color`, `company_address`, `company_phone`, `show_qr_code`, `created_at`, `updated_at`) VALUES
(1, 1, '#2316d4', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي', '#000000', 'شارع احمد يحيي', '***********', 0, '2025-06-10 19:53:56', '2025-06-10 19:58:43'),
(2, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-12 05:45:45', '2025-06-12 05:45:45'),
(3, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-12 05:52:09', '2025-06-12 05:52:09'),
(4, 1, '#dc3545', 'شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى', '#000000', 'شارع فريد - الأحساء', '01026362111', 1, '2025-06-12 06:20:45', '2025-06-12 06:20:45');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_status_log`
--

CREATE TABLE `payment_status_log` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `old_status` enum('pending','paid','cancelled') DEFAULT NULL,
  `new_status` enum('pending','paid','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `product_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rooms`
--

CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_type` enum('VIP','regular','private') DEFAULT 'regular',
  `capacity` int(11) DEFAULT 1,
  `special_rate` decimal(8,2) DEFAULT 0.00,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `rooms`
--

INSERT INTO `rooms` (`room_id`, `client_id`, `room_name`, `room_type`, `capacity`, `special_rate`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'اساسي', 'regular', 1, 0.00, NULL, '2025-06-10 18:37:40', '2025-06-10 18:37:40'),
(2, 1, 'غرفه 1', 'regular', 1, 0.00, NULL, '2025-06-12 05:29:44', '2025-06-12 05:29:44');

-- --------------------------------------------------------

--
-- Table structure for table `sections`
--

CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_id` int(11) DEFAULT NULL,
  `client_id` int(11) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `game_type` enum('single','multiplayer') DEFAULT 'single' COMMENT 'نوع اللعب: فردي أو زوجي'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`session_id`, `device_id`, `start_time`, `end_time`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`, `customer_id`, `client_id`, `total_cost`, `expected_end_time`, `notes`) VALUES
(1, 1, '2025-06-09 09:50:21', '2025-06-09 09:53:17', 'completed', 1, 1, '2025-06-09 06:50:21', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(2, 1, '2025-06-09 09:56:19', '2025-06-09 09:56:26', 'completed', 1, 1, '2025-06-09 06:56:19', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(3, 1, '2025-06-09 10:24:37', '2025-06-09 14:44:26', 'completed', 1, 1, '2025-06-09 07:24:37', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(4, 3, '2025-06-09 12:21:19', '2025-06-09 14:44:23', 'completed', 1, 1, '2025-06-09 09:21:19', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(5, 10, '2025-06-09 13:39:13', '2025-06-09 14:35:54', 'completed', 1, 1, '2025-06-09 10:39:13', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(6, 11, '2025-06-09 13:39:49', '2025-06-09 14:38:47', 'completed', 1, 1, '2025-06-09 10:39:49', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(7, 10, '2025-06-09 14:40:30', '2025-06-09 14:44:28', 'completed', 1, 1, '2025-06-09 11:40:30', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(8, 11, '2025-06-09 14:40:38', '2025-06-09 14:44:31', 'completed', 1, 1, '2025-06-09 11:40:38', '2025-06-10 18:04:17', NULL, 1, 0.00, NULL, NULL),
(9, 3, '2025-06-10 21:58:35', '2025-06-10 22:43:41', 'completed', 1, 1, '2025-06-10 18:58:35', '2025-06-10 19:43:41', NULL, 1, 35.00, NULL, ''),
(10, 11, '2025-06-10 21:59:10', '2025-06-10 22:15:23', 'completed', 1, 1, '2025-06-10 18:59:10', '2025-06-10 19:15:23', NULL, 1, 25.00, NULL, ''),
(11, 10, '2025-06-10 22:43:36', NULL, 'active', 1, NULL, '2025-06-10 19:43:36', '2025-06-10 19:43:36', NULL, 1, 0.00, NULL, ''),
(12, 1, '2025-06-10 22:46:26', NULL, 'active', 1, NULL, '2025-06-10 19:46:26', '2025-06-10 19:46:26', NULL, 1, 0.00, '2025-06-10 19:16:26', ''),
(13, 14, '2025-06-12 10:13:12', NULL, 'active', 4, NULL, '2025-06-12 07:13:12', '2025-06-12 07:13:12', NULL, 4, 0.00, NULL, '');

-- --------------------------------------------------------

--
-- Table structure for table `session_products`
--

CREATE TABLE `session_products` (
  `id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `price` decimal(10,2) NOT NULL COMMENT 'سعر المنتج وقت الإضافة',
  `total` decimal(10,2) GENERATED ALWAYS AS (`price` * `quantity`) STORED COMMENT 'إجمالي السعر',
  `created_by` int(11) DEFAULT NULL COMMENT 'معرف المستخدم الذي أضاف المنتج',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `session_products`
--

INSERT INTO `session_products` (`id`, `session_id`, `product_id`, `quantity`, `created_at`, `price`, `created_by`, `notes`) VALUES
(11, 4, 14, 1, '2025-06-09 11:05:57', 8.00, 1, NULL),
(15, 6, 15, 1, '2025-06-09 11:16:27', 10.00, 1, NULL),
(17, 3, 13, 1, '2025-06-09 11:17:09', 5.00, 1, NULL),
(18, 5, 13, 1, '2025-06-09 11:21:43', 5.00, 1, NULL),
(20, 4, 15, 1, '2025-06-09 11:27:33', 10.00, NULL, NULL),
(24, 6, 17, 1, '2025-06-09 11:32:26', 25.00, NULL, NULL),
(25, 5, 15, 1, '2025-06-09 11:35:47', 10.00, NULL, NULL),
(27, 9, 17, 1, '2025-06-10 19:06:15', 25.00, NULL, NULL),
(28, 10, 13, 1, '2025-06-10 19:06:30', 5.00, NULL, NULL),
(29, 12, 13, 1, '2025-06-12 07:32:05', 5.00, NULL, NULL),
(30, 12, 15, 1, '2025-06-12 11:40:17', 10.00, NULL, NULL),
(31, 12, 13, 1, '2025-06-12 11:40:24', 5.00, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `additional_income`
--
ALTER TABLE `additional_income`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_additional_income_client` (`client_id`),
  ADD KEY `idx_additional_income_type` (`income_type_id`),
  ADD KEY `idx_additional_income_date` (`income_date`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `unique_client_setting` (`client_id`,`setting_key`);

--
-- Indexes for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`category_id`);

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`client_id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`customer_id`),
  ADD UNIQUE KEY `idx_phone_client` (`phone`,`client_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_client_id` (`client_id`),
  ADD KEY `idx_customers_client_id` (`client_id`),
  ADD KEY `idx_customers_phone` (`phone`),
  ADD KEY `idx_customers_client` (`client_id`);

--
-- Indexes for table `devices`
--
ALTER TABLE `devices`
  ADD PRIMARY KEY (`device_id`),
  ADD KEY `room_id` (`room_id`),
  ADD KEY `idx_devices_client_status` (`client_id`,`status`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `fk_employee_client` (`client_id`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expenses_client` (`client_id`),
  ADD KEY `idx_expenses_type` (`expense_type_id`),
  ADD KEY `idx_expenses_date` (`expense_date`);

--
-- Indexes for table `expense_types`
--
ALTER TABLE `expense_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_expense_types_client` (`client_id`);

--
-- Indexes for table `income_types`
--
ALTER TABLE `income_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_income_types_client` (`client_id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`invoice_id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `idx_invoices_payment_status` (`payment_status`);

--
-- Indexes for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_order_number` (`client_id`,`order_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `session_id` (`session_id`);

--
-- Indexes for table `payment_status_log`
--
ALTER TABLE `payment_status_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_payment_log_invoice` (`invoice_id`),
  ADD KEY `idx_payment_log_date` (`changed_at`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`product_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `section_id` (`section_id`);

--
-- Indexes for table `rooms`
--
ALTER TABLE `rooms`
  ADD PRIMARY KEY (`room_id`),
  ADD KEY `client_id` (`client_id`);

--
-- Indexes for table `sections`
--
ALTER TABLE `sections`
  ADD PRIMARY KEY (`section_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `idx_sessions_client_id` (`client_id`),
  ADD KEY `idx_sessions_status` (`status`),
  ADD KEY `idx_sessions_device_id` (`device_id`),
  ADD KEY `idx_sessions_start_time` (`start_time`),
  ADD KEY `idx_sessions_device_status` (`device_id`,`status`);

--
-- Indexes for table `session_products`
--
ALTER TABLE `session_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `additional_income`
--
ALTER TABLE `additional_income`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `business_settings`
--
ALTER TABLE `business_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `clients`
--
ALTER TABLE `clients`
  MODIFY `client_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `devices`
--
ALTER TABLE `devices`
  MODIFY `device_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `expense_types`
--
ALTER TABLE `expense_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `income_types`
--
ALTER TABLE `income_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `invoice_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `invoice_settings`
--
ALTER TABLE `invoice_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_status_log`
--
ALTER TABLE `payment_status_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `product_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rooms`
--
ALTER TABLE `rooms`
  MODIFY `room_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sections`
--
ALTER TABLE `sections`
  MODIFY `section_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sessions`
--
ALTER TABLE `sessions`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `session_products`
--
ALTER TABLE `session_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `business_settings`
--
ALTER TABLE `business_settings`
  ADD CONSTRAINT `business_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `cafeteria_items`
--
ALTER TABLE `cafeteria_items`
  ADD CONSTRAINT `cafeteria_items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`);

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`);

--
-- Constraints for table `devices`
--
ALTER TABLE `devices`
  ADD CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `devices_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`);

--
-- Constraints for table `employees`
--
ALTER TABLE `employees`
  ADD CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`);

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  ADD CONSTRAINT `products_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`);

--
-- Constraints for table `rooms`
--
ALTER TABLE `rooms`
  ADD CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`),
  ADD CONSTRAINT `sessions_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`);

--
-- Constraints for table `session_products`
--
ALTER TABLE `session_products`
  ADD CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`),
  ADD CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
