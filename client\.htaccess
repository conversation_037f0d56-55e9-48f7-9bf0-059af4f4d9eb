# Client Directory .htaccess
# This file handles URL rewriting specifically for the client directory

RewriteEngine On

# Allow direct access to PHP files (important for forms and AJAX)
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule \.php$ - [L]

# Allow direct access to existing files and directories
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Remove .php extension from URLs (only for non-existing files)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Specific rules for important pages
RewriteRule ^dashboard$ dashboard.php [NC,L]
RewriteRule ^sessions$ sessions.php [NC,L]
RewriteRule ^devices$ devices.php [NC,L]
RewriteRule ^customers$ customers.php [NC,L]
RewriteRule ^invoices$ invoices.php [NC,L]
RewriteRule ^reports$ reports.php [NC,L]
RewriteRule ^profile$ profile.php [NC,L]
RewriteRule ^settings$ settings.php [NC,L]

# Security: Block access to sensitive files
<Files ~ "^\.ht">
    Order allow,deny
    Deny from all
</Files>

# Block access to config files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Allow API directory access
RewriteRule ^api/(.*)$ api/$1 [L]

# Custom Error Pages for client directory
ErrorDocument 404 /playgood/client/404.php
ErrorDocument 403 /playgood/client/403.php
ErrorDocument 500 /playgood/client/500.php
