<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id'];
} else {
    $client_id = $_SESSION['client_id'];
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_devices')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل الأجهزة']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['device_id']) || !isset($input['status'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

$device_id = $input['device_id'];
$status = $input['status'];

// التحقق من صحة الحالة
$valid_statuses = ['available', 'occupied', 'maintenance'];
if (!in_array($status, $valid_statuses)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    // التحقق من أن الجهاز ينتمي للعميل
    $check_stmt = $pdo->prepare("SELECT device_id FROM devices WHERE device_id = ? AND client_id = ?");
    $check_stmt->execute([$device_id, $client_id]);
    
    if (!$check_stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الجهاز غير موجود']);
        exit;
    }
    
    // تحديث حالة الجهاز
    $update_stmt = $pdo->prepare("UPDATE devices SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE device_id = ? AND client_id = ?");
    $result = $update_stmt->execute([$status, $device_id, $client_id]);
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث حالة الجهاز بنجاح']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة الجهاز']);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>
