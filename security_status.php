<?php
/**
 * فحص حالة نظام الحماية
 * يعرض حالة جميع مكونات الأمان
 */

require_once 'config/database.php';

// تهيئة نظام الحماية الأساسي
if (!class_exists('SecurityManager')) {
    require_once 'includes/security.php';
}

// إنشاء مثيل من مدير الحماية إذا لم يكن موجوداً
if (!isset($security)) {
    $security = new SecurityManager($pdo);
}

$status = [
    'database' => false,
    'security_tables' => false,
    'security_class' => false,
    'csrf_protection' => false,
    'file_protection' => false,
    'directories' => false
];

// فحص الاتصال بقاعدة البيانات
try {
    $pdo->query("SELECT 1");
    $status['database'] = true;
} catch (Exception $e) {
    $status['database'] = false;
}

// فحص وجود الجداول الأمنية
$required_tables = [
    'login_attempts', 'suspicious_activities', 'blocked_ips', 
    'detected_threats', 'detection_rules'
];

$existing_tables = 0;
foreach ($required_tables as $table) {
    try {
        $pdo->query("SELECT 1 FROM $table LIMIT 1");
        $existing_tables++;
    } catch (Exception $e) {
        // الجدول غير موجود
    }
}
$status['security_tables'] = ($existing_tables === count($required_tables));

// فحص فئة الحماية
$status['security_class'] = class_exists('SecurityManager') && isset($security);

// فحص CSRF Protection
try {
    $token = $security->generateCsrfToken('test');
    $status['csrf_protection'] = !empty($token);
} catch (Exception $e) {
    $status['csrf_protection'] = false;
}

// فحص ملفات الحماية
$protection_files = [
    '.htaccess',
    'config/.htaccess',
    'includes/.htaccess',
    'uploads/.htaccess'
];

$existing_files = 0;
foreach ($protection_files as $file) {
    if (file_exists($file)) {
        $existing_files++;
    }
}
$status['file_protection'] = ($existing_files === count($protection_files));

// فحص المجلدات
$required_dirs = ['uploads', 'quarantine', 'backups'];
$existing_dirs = 0;
foreach ($required_dirs as $dir) {
    if (is_dir($dir)) {
        $existing_dirs++;
    }
}
$status['directories'] = ($existing_dirs === count($required_dirs));

// حساب النسبة الإجمالية
$total_checks = count($status);
$passed_checks = array_sum($status);
$success_rate = round(($passed_checks / $total_checks) * 100, 2);

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة نظام الحماية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .status-pass { border-left-color: #28a745; }
        .status-fail { border-left-color: #dc3545; }
        .status-icon-pass { color: #28a745; }
        .status-icon-fail { color: #dc3545; }
        .progress-ring {
            width: 120px;
            height: 120px;
        }
        .progress-ring-circle {
            stroke: #e9ecef;
            stroke-width: 8;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
        }
        .progress-ring-progress {
            stroke: #28a745;
            stroke-width: 8;
            stroke-linecap: round;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
            stroke-dasharray: 326.73;
            stroke-dashoffset: <?php echo 326.73 - (326.73 * $success_rate / 100); ?>;
            transform: rotate(-90deg);
            transform-origin: 60px 60px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="display-4">
                        <i class="fas fa-shield-alt text-primary me-3"></i>
                        حالة نظام الحماية
                    </h1>
                    <p class="lead text-muted">فحص شامل لجميع مكونات الأمان</p>
                </div>
            </div>
        </div>

        <!-- نسبة النجاح الإجمالية -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="me-4">
                                <svg class="progress-ring">
                                    <circle class="progress-ring-circle"></circle>
                                    <circle class="progress-ring-progress"></circle>
                                </svg>
                                <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                    <h2 class="mb-0"><?php echo $success_rate; ?>%</h2>
                                    <small class="text-muted">مكتمل</small>
                                </div>
                            </div>
                            <div class="text-start">
                                <h3>حالة النظام الأمني</h3>
                                <p class="mb-1">
                                    <strong><?php echo $passed_checks; ?></strong> من 
                                    <strong><?php echo $total_checks; ?></strong> فحوصات نجحت
                                </p>
                                <div class="mt-3">
                                    <?php if ($success_rate >= 90): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check-circle me-1"></i>
                                            نظام آمن ومحمي
                                        </span>
                                    <?php elseif ($success_rate >= 70): ?>
                                        <span class="badge bg-warning fs-6">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            يحتاج تحسينات
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-times-circle me-1"></i>
                                            يحتاج إعداد
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الفحوصات -->
        <div class="row">
            <?php
            $check_details = [
                'database' => [
                    'title' => 'الاتصال بقاعدة البيانات',
                    'description' => 'التحقق من الاتصال بقاعدة البيانات',
                    'icon' => 'fas fa-database'
                ],
                'security_tables' => [
                    'title' => 'الجداول الأمنية',
                    'description' => 'وجود جميع الجداول المطلوبة للحماية',
                    'icon' => 'fas fa-table'
                ],
                'security_class' => [
                    'title' => 'فئة الحماية',
                    'description' => 'تحميل وتهيئة فئة SecurityManager',
                    'icon' => 'fas fa-code'
                ],
                'csrf_protection' => [
                    'title' => 'حماية CSRF',
                    'description' => 'نظام حماية من هجمات Cross-Site Request Forgery',
                    'icon' => 'fas fa-shield-virus'
                ],
                'file_protection' => [
                    'title' => 'حماية الملفات',
                    'description' => 'ملفات .htaccess لحماية المجلدات الحساسة',
                    'icon' => 'fas fa-file-shield'
                ],
                'directories' => [
                    'title' => 'المجلدات المطلوبة',
                    'description' => 'وجود مجلدات الرفع والحجر الصحي والنسخ الاحتياطي',
                    'icon' => 'fas fa-folder-open'
                ]
            ];
            ?>

            <?php foreach ($status as $check => $passed): ?>
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card status-card <?php echo $passed ? 'status-pass' : 'status-fail'; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title">
                                        <i class="<?php echo $check_details[$check]['icon']; ?> me-2"></i>
                                        <?php echo $check_details[$check]['title']; ?>
                                    </h6>
                                    <p class="card-text small text-muted">
                                        <?php echo $check_details[$check]['description']; ?>
                                    </p>
                                </div>
                                <div class="text-end">
                                    <i class="fas <?php echo $passed ? 'fa-check-circle status-icon-pass' : 'fa-times-circle status-icon-fail'; ?> fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- إجراءات سريعة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="setup_security_tables.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-cog me-2"></i>
                                    إعداد الجداول
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="security_test_suite.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-vial me-2"></i>
                                    اختبار الأمان
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="client/login.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    دخول العميل
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="admin/login.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-user-shield me-2"></i>
                                    دخول الإدمن
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></p>
                                <p><strong>إصدار MySQL:</strong> 
                                    <?php 
                                    try {
                                        echo $pdo->query("SELECT VERSION()")->fetchColumn();
                                    } catch (Exception $e) {
                                        echo "غير متاح";
                                    }
                                    ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></p>
                                <p><strong>وقت الفحص:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
