<?php
/**
 * اختبار إصلاح دالة الحذف في modal تعديل الجلسة
 * PlayGood Gaming Center Management System
 */

session_start();
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1; // للاختبار
}

require_once 'config/database.php';

echo "<h1>اختبار إصلاح دالة الحذف في modal تعديل الجلسة</h1>";
echo "<hr>";

try {
    echo "<h2>1. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        // إنشاء جلسة تجريبية
        $pdo->exec("INSERT INTO sessions (device_id, start_time, status, client_id) VALUES (1, NOW(), 'active', 1)");
        $session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: green;'>✅ تم العثور على جلسة نشطة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاؤه
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        // إنشاء منتج تجريبي
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج تجريبي', 5.00, 'مشروبات', 1)");
        $product_id = $pdo->lastInsertId();
        $product_name = 'منتج تجريبي';
        $product_price = 5.00;
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: green;'>✅ تم العثور على منتج: $product_name ($product_id)</p>";
    }
    
    echo "<h2>2. إضافة منتج للجلسة</h2>";
    
    // حذف أي منتجات موجودة مسبقاً
    $pdo->prepare("DELETE FROM session_products WHERE session_id = ?")->execute([$session_id]);
    
    // إضافة منتج للجلسة
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, 2, $product_price]);
    
    echo "<p style='color: green;'>✅ تم إضافة منتج للجلسة</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح دالة الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>محاكاة modal تعديل الجلسة</h2>
        
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>تعديل الجلسة - اختبار دالة الحذف
                </h5>
            </div>
            <div class="card-body">
                <!-- محاكاة النموذج -->
                <form id="testEditSessionForm">
                    <input type="hidden" id="edit_session_id" value="<?php echo $session_id; ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-coffee me-1"></i>إدارة المنتجات
                        </label>
                        <div class="border rounded p-3">
                            <div class="mb-3">
                                <h6 class="text-muted">المنتجات المضافة:</h6>
                                <div id="edit_session_products" class="mb-3">
                                    <div class="text-center p-3">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">جاري تحميل المنتجات...</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-info btn-sm" onclick="loadEditSessionProducts(<?php echo $session_id; ?>)">
                                    <i class="fas fa-refresh me-1"></i>إعادة تحميل المنتجات
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>حفظ التعديلات (اختبار)
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearLog()">
                            <i class="fas fa-trash me-1"></i>مسح السجل
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">سجل الأحداث</h5>
            </div>
            <div class="card-body">
                <div id="event-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                    <!-- سيتم ملء السجل هنا -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    const testSessionId = <?php echo $session_id; ?>;
    
    // دالة تسجيل الأحداث
    function log(message, type = 'info') {
        const logDiv = document.getElementById('event-log');
        const timestamp = new Date().toLocaleTimeString('ar-EG');
        const colors = {
            'info': '#007bff',
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107'
        };
        
        logDiv.innerHTML += `<div style="color: ${colors[type]}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function clearLog() {
        document.getElementById('event-log').innerHTML = '';
    }
    
    // مستمع إرسال النموذج للاختبار
    document.getElementById('testEditSessionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        log('تم الضغط على زر حفظ التعديلات - هذا يعني أن دالة الحذف لا تعمل بشكل صحيح!', 'error');
        alert('تم الضغط على زر حفظ التعديلات!\nهذا يعني أن دالة الحذف لا تعمل بشكل صحيح.');
    });
    
    // تحميل المنتجات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        log('بدء اختبار تحميل المنتجات...', 'info');
        loadEditSessionProducts(testSessionId);
    });
    
    // نسخ دوال JavaScript من ملف sessions.php
    
    // دالة تحميل المنتجات في modal التعديل
    function loadEditSessionProducts(sessionId) {
        log(`تحميل المنتجات للجلسة: ${sessionId}`, 'info');
        
        if (!sessionId) {
            log('خطأ: معرف الجلسة مطلوب', 'error');
            return;
        }
        
        const productsContainer = document.getElementById('edit_session_products');
        if (!productsContainer) {
            log('خطأ: لم يتم العثور على حاوية المنتجات', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        productsContainer.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري تحميل المنتجات...</p></div>';
        
        fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
            .then(response => {
                log(`استجابة API: ${response.status}`, 'info');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(responseText => {
                log('تم استلام البيانات من API', 'info');
                
                try {
                    const data = JSON.parse(responseText);
                    log(`تم تحليل البيانات: ${data.products ? data.products.length : 0} منتج`, 'success');
                    
                    if (data.success) {
                        if (data.products && data.products.length > 0) {
                            // حساب إجمالي تكلفة المنتجات
                            const totalProductsCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);

                            productsContainer.innerHTML = `
                                <div class="list-group">
                                    ${data.products.map(product => `
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">${product.product_name}</h6>
                                                <small class="text-muted">
                                                    ${product.quantity} × ${product.price} ج.م
                                                </small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="me-3 fw-bold">${product.total} ج.م</span>
                                                <button type="button" class="btn btn-danger btn-sm"
                                                        onclick="deleteEditSessionProduct(${sessionId}, ${product.product_id}); return false;"
                                                        title="حذف المنتج">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                <div class="mt-2 p-2 bg-light rounded">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                        <span class="fw-bold text-primary">${totalProductsCost.toFixed(2)} ج.م</span>
                                    </div>
                                </div>
                            `;
                            log('تم عرض المنتجات بنجاح', 'success');
                        } else {
                            productsContainer.innerHTML = `
                                <div class="text-center p-3">
                                    <i class="fas fa-coffee fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد منتجات مضافة</p>
                                    <small class="text-muted">اضغط على "إضافة منتج" لإضافة منتجات للجلسة</small>
                                </div>
                            `;
                            log('لا توجد منتجات في الجلسة', 'warning');
                        }
                    } else {
                        throw new Error(data.error || 'فشل في جلب المنتجات');
                    }
                } catch (jsonError) {
                    log(`خطأ في تحليل JSON: ${jsonError.message}`, 'error');
                    productsContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>خطأ في تحليل البيانات:</strong> ${jsonError.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                log(`خطأ في جلب البيانات: ${error.message}`, 'error');
                productsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ في تحميل المنتجات:</strong> ${error.message}
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadEditSessionProducts(${sessionId})">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                `;
            });
    }
    
    // دالة حذف منتج من modal التعديل
    function deleteEditSessionProduct(sessionId, productId) {
        // منع إرسال النموذج
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        log(`محاولة حذف المنتج: ${productId} من الجلسة: ${sessionId}`, 'warning');

        if (!sessionId || !productId) {
            log('خطأ: بيانات غير مكتملة', 'error');
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'بيانات غير مكتملة'
            });
            return false;
        }

        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف هذا المنتج؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                log('تم تأكيد الحذف، جاري إرسال طلب الحذف...', 'info');
                
                // إظهار مؤشر التحميل
                Swal.fire({
                    title: 'جاري الحذف...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                const deleteData = {
                    session_id: parseInt(sessionId),
                    product_id: parseInt(productId)
                };

                log(`إرسال بيانات الحذف: ${JSON.stringify(deleteData)}`, 'info');

                fetch('client/api/delete_session_product.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(deleteData)
                })
                .then(response => {
                    log(`استجابة API الحذف: ${response.status}`, 'info');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(responseText => {
                    log('تم استلام استجابة الحذف', 'info');
                    
                    try {
                        const data = JSON.parse(responseText);
                        log(`نتيجة الحذف: ${data.success ? 'نجح' : 'فشل'}`, data.success ? 'success' : 'error');

                        if (data.success) {
                            // إظهار رسالة النجاح أولاً
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف بنجاح',
                                text: data.message,
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // تحديث قائمة المنتجات في modal التعديل فوراً
                            setTimeout(() => {
                                loadEditSessionProducts(sessionId);
                            }, 100);
                        } else {
                            throw new Error(data.error || 'فشل في حذف المنتج');
                        }
                    } catch (jsonError) {
                        log(`خطأ في تحليل استجابة الحذف: ${jsonError.message}`, 'error');
                        throw new Error('خطأ في تحليل استجابة الخادم: ' + jsonError.message);
                    }
                })
                .catch(error => {
                    log(`خطأ في عملية الحذف: ${error.message}`, 'error');
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في الحذف',
                        text: error.message,
                        footer: 'تحقق من اتصال الإنترنت وحاول مرة أخرى'
                    });
                });
            } else {
                log('تم إلغاء عملية الحذف', 'info');
            }
        });
        
        return false; // منع إرسال النموذج
    }
    </script>
</body>
</html>
