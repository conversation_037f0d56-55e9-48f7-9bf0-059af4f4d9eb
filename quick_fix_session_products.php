<?php
// حل سريع لمشكلة session_products
session_start();
$_SESSION['client_id'] = 1;

require_once 'config/database.php';

echo "<h1>حل سريع لمشكلة session_products</h1>";

try {
    echo "<h2>1. فحص الجدول الحالي</h2>";
    
    // فحص وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'session_products'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ الجدول غير موجود - سأنشئه</p>";
        
        $create_sql = "CREATE TABLE session_products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($create_sql);
        echo "<p style='color: green;'>✅ تم إنشاء الجدول</p>";
    } else {
        echo "<p style='color: green;'>✅ الجدول موجود</p>";
        
        // فحص الأعمدة
        $stmt = $pdo->query("DESCRIBE session_products");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $has_price = false;
        $has_unit_price = false;
        $has_total_price = false;
        
        foreach ($columns as $col) {
            if ($col['Field'] == 'price') $has_price = true;
            if ($col['Field'] == 'unit_price') $has_unit_price = true;
            if ($col['Field'] == 'total_price') $has_total_price = true;
        }
        
        echo "<p>الأعمدة الموجودة: ";
        if ($has_price) echo "price ✅ ";
        if ($has_unit_price) echo "unit_price ⚠️ ";
        if ($has_total_price) echo "total_price ⚠️ ";
        echo "</p>";
        
        // إصلاح سريع
        if (!$has_price && $has_unit_price) {
            echo "<p>إعادة تسمية unit_price إلى price...</p>";
            $pdo->exec("ALTER TABLE session_products CHANGE unit_price price DECIMAL(10,2) NOT NULL DEFAULT 0.00");
            echo "<p style='color: green;'>✅ تم</p>";
        }
        
        if ($has_total_price) {
            echo "<p>حذف عمود total_price...</p>";
            $pdo->exec("ALTER TABLE session_products DROP COLUMN total_price");
            echo "<p style='color: green;'>✅ تم</p>";
        }
        
        if (!$has_price && !$has_unit_price) {
            echo "<p>إضافة عمود price...</p>";
            $pdo->exec("ALTER TABLE session_products ADD COLUMN price DECIMAL(10,2) NOT NULL DEFAULT 0.00");
            echo "<p style='color: green;'>✅ تم</p>";
        }
    }
    
    echo "<h2>2. اختبار سريع</h2>";
    
    // البحث عن جلسة
    $session = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1")->fetch();
    if (!$session) {
        echo "<p style='color: orange;'>لا توجد جلسة نشطة</p>";
    } else {
        echo "<p>جلسة نشطة: {$session['session_id']}</p>";
        
        // البحث عن منتج
        $product = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE price > 0 LIMIT 1")->fetch();
        if (!$product) {
            echo "<p style='color: orange;'>لا توجد منتجات</p>";
        } else {
            echo "<p>منتج متاح: {$product['name']} - {$product['price']} ج.م</p>";
            
            // اختبار الإدراج
            try {
                $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
                $stmt->execute([$session['session_id'], $product['id'], 1, $product['price']]);
                
                $test_id = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ تم إضافة المنتج بنجاح - ID: $test_id</p>";
                
                // حذف السجل التجريبي
                $pdo->exec("DELETE FROM session_products WHERE id = $test_id");
                echo "<p>تم حذف السجل التجريبي</p>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ في الإدراج: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>3. النتيجة</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<p style='color: green; font-weight: bold;'>✅ تم إصلاح المشكلة!</p>";
    echo "<p>يمكنك الآن إضافة المنتجات للجلسات بدون أخطاء</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<br><a href='sessions.php'>العودة للجلسات</a>";
?>
