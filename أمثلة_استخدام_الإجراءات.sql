-- =====================================================
-- أمثلة استخدام الإجراءات المخزنة - Station System
-- =====================================================
-- هذا الملف يحتوي على أمثلة عملية لاستخدام الإجراءات المخزنة
-- =====================================================

USE `station`;

-- =====================================================
-- 1. أمثلة على إجراء CheckClientLimit
-- =====================================================

-- مثال 1: التحقق من حد الأجهزة للعميل رقم 1
CALL CheckClientLimit(1, 'devices', 'max_devices', @limit, @usage, @can_add, @message);
SELECT 
    'فحص حد الأجهزة' AS test_name,
    @limit AS limit_value, 
    @usage AS current_usage, 
    @can_add AS can_add, 
    @message AS message;

-- مثال 2: التحقق من حد المنتجات للعميل رقم 1
CALL CheckClientLimit(1, 'products', 'max_products', @limit, @usage, @can_add, @message);
SELECT 
    'فحص حد المنتجات' AS test_name,
    @limit AS limit_value, 
    @usage AS current_usage, 
    @can_add AS can_add, 
    @message AS message;

-- مثال 3: التحقق من حد الموظفين للعميل رقم 1
CALL CheckClientLimit(1, 'employees', 'max_employees', @limit, @usage, @can_add, @message);
SELECT 
    'فحص حد الموظفين' AS test_name,
    @limit AS limit_value, 
    @usage AS current_usage, 
    @can_add AS can_add, 
    @message AS message;

-- مثال 4: التحقق من حد العملاء للعميل رقم 1
CALL CheckClientLimit(1, 'customers', 'max_customers', @limit, @usage, @can_add, @message);
SELECT 
    'فحص حد العملاء' AS test_name,
    @limit AS limit_value, 
    @usage AS current_usage, 
    @can_add AS can_add, 
    @message AS message;

-- =====================================================
-- 2. أمثلة على إجراء GrantDefaultPermissionsToClient
-- =====================================================

-- مثال: منح الصلاحيات الافتراضية لعميل جديد
CALL GrantDefaultPermissionsToClient(1, @success, @message);
SELECT 
    'منح الصلاحيات الافتراضية' AS test_name,
    @success AS success, 
    @message AS message;

-- =====================================================
-- 3. أمثلة على إجراء GetClientStatistics
-- =====================================================

-- مثال: الحصول على إحصائيات العميل للشهر الحالي
CALL GetClientStatistics(1, DATE_FORMAT(NOW(), '%Y-%m-01'), LAST_DAY(NOW()));

-- مثال: الحصول على إحصائيات العميل لآخر 30 يوم
CALL GetClientStatistics(1, DATE_SUB(CURDATE(), INTERVAL 30 DAY), CURDATE());

-- =====================================================
-- 4. أمثلة على إجراء StartNewSession
-- =====================================================

-- مثال: بدء جلسة جديدة
CALL StartNewSession(
    1,              -- معرف العميل
    16,             -- معرف الجهاز
    43,             -- معرف العميل (زائر المحل)
    'hourly',       -- نوع الجلسة
    'FIFA 2024',    -- نوع اللعبة
    2,              -- عدد اللاعبين
    @session_id, @success, @message
);

SELECT 
    'بدء جلسة جديدة' AS test_name,
    @session_id AS session_id,
    @success AS success, 
    @message AS message;

-- =====================================================
-- 5. أمثلة على إجراء EndSession
-- =====================================================

-- مثال: إنهاء الجلسة (استخدم معرف الجلسة من المثال السابق)
CALL EndSession(@session_id, 1, @success, @message, @total_cost);

SELECT 
    'إنهاء الجلسة' AS test_name,
    @success AS success, 
    @message AS message,
    @total_cost AS total_cost;

-- =====================================================
-- 6. أمثلة على إجراء UpdateInventory
-- =====================================================

-- مثال 1: إضافة مخزون جديد
CALL UpdateInventory(
    13,             -- معرف المنتج
    1,              -- معرف العميل
    'in',           -- نوع الحركة (إدخال)
    50,             -- الكمية
    3.00,           -- سعر الوحدة
    'purchase',     -- نوع المرجع
    NULL,           -- معرف المرجع
    'شراء جديد من المورد',  -- ملاحظات
    1,              -- المستخدم المنفذ
    @success, @message
);

SELECT 
    'إضافة مخزون' AS test_name,
    @success AS success, 
    @message AS message;

-- مثال 2: خصم من المخزون (بيع)
CALL UpdateInventory(
    13,             -- معرف المنتج
    1,              -- معرف العميل
    'out',          -- نوع الحركة (إخراج)
    5,              -- الكمية
    3.00,           -- سعر الوحدة
    'sale',         -- نوع المرجع
    @session_id,    -- معرف المرجع (الجلسة)
    'بيع للعميل',    -- ملاحظات
    1,              -- المستخدم المنفذ
    @success, @message
);

SELECT 
    'خصم من المخزون' AS test_name,
    @success AS success, 
    @message AS message;

-- مثال 3: تعديل المخزون (جرد)
CALL UpdateInventory(
    13,             -- معرف المنتج
    1,              -- معرف العميل
    'adjustment',   -- نوع الحركة (تعديل)
    40,             -- الكمية الجديدة
    3.00,           -- سعر الوحدة
    'manual',       -- نوع المرجع
    NULL,           -- معرف المرجع
    'تعديل بعد الجرد',  -- ملاحظات
    1,              -- المستخدم المنفذ
    @success, @message
);

SELECT 
    'تعديل المخزون' AS test_name,
    @success AS success, 
    @message AS message;

-- =====================================================
-- 7. أمثلة على إجراء CleanupOldData
-- =====================================================

-- مثال: تنظيف البيانات الأقدم من 90 يوم
CALL CleanupOldData(1, 90, @success, @message);

SELECT 
    'تنظيف البيانات القديمة' AS test_name,
    @success AS success, 
    @message AS message;

-- =====================================================
-- 8. استعلامات مفيدة للمتابعة
-- =====================================================

-- عرض حالة الأجهزة
SELECT 
    device_name,
    device_type,
    status,
    hourly_rate
FROM devices 
WHERE client_id = 1
ORDER BY device_name;

-- عرض آخر الجلسات
SELECT 
    session_id,
    device_id,
    customer_id,
    start_time,
    end_time,
    total_cost,
    payment_status
FROM sessions 
WHERE client_id = 1
ORDER BY start_time DESC
LIMIT 10;

-- عرض حالة المخزون
SELECT 
    name,
    stock_quantity,
    min_stock_level,
    status,
    CASE 
        WHEN stock_quantity <= 0 THEN 'نفد المخزون'
        WHEN stock_quantity <= min_stock_level THEN 'مخزون منخفض'
        ELSE 'مخزون طبيعي'
    END AS stock_status
FROM cafeteria_items 
WHERE client_id = 1
ORDER BY name;

-- عرض إحصائيات سريعة
SELECT 
    'الأجهزة المتاحة' AS metric,
    COUNT(*) AS value
FROM devices 
WHERE client_id = 1 AND status = 'available'

UNION ALL

SELECT 
    'الجلسات النشطة' AS metric,
    COUNT(*) AS value
FROM sessions 
WHERE client_id = 1 AND end_time IS NULL

UNION ALL

SELECT 
    'إجمالي العملاء' AS metric,
    COUNT(*) AS value
FROM customers 
WHERE client_id = 1 AND is_active = 1

UNION ALL

SELECT 
    'المنتجات المتاحة' AS metric,
    COUNT(*) AS value
FROM cafeteria_items 
WHERE client_id = 1 AND status = 'available';

-- =====================================================
-- 9. اختبار شامل للنظام
-- =====================================================

-- إجراء اختبار شامل
DELIMITER $$

CREATE PROCEDURE `SystemHealthCheck`(IN p_client_id INT)
BEGIN
    DECLARE v_devices_count INT DEFAULT 0;
    DECLARE v_active_sessions INT DEFAULT 0;
    DECLARE v_customers_count INT DEFAULT 0;
    DECLARE v_products_count INT DEFAULT 0;
    DECLARE v_low_stock_count INT DEFAULT 0;
    
    -- فحص الأجهزة
    SELECT COUNT(*) INTO v_devices_count 
    FROM devices WHERE client_id = p_client_id;
    
    -- فحص الجلسات النشطة
    SELECT COUNT(*) INTO v_active_sessions 
    FROM sessions WHERE client_id = p_client_id AND end_time IS NULL;
    
    -- فحص العملاء
    SELECT COUNT(*) INTO v_customers_count 
    FROM customers WHERE client_id = p_client_id AND is_active = 1;
    
    -- فحص المنتجات
    SELECT COUNT(*) INTO v_products_count 
    FROM cafeteria_items WHERE client_id = p_client_id AND status != 'discontinued';
    
    -- فحص المخزون المنخفض
    SELECT COUNT(*) INTO v_low_stock_count 
    FROM cafeteria_items 
    WHERE client_id = p_client_id 
    AND stock_quantity <= min_stock_level;
    
    -- عرض النتائج
    SELECT 
        'فحص صحة النظام' AS report_title,
        v_devices_count AS total_devices,
        v_active_sessions AS active_sessions,
        v_customers_count AS total_customers,
        v_products_count AS total_products,
        v_low_stock_count AS low_stock_items,
        CASE 
            WHEN v_low_stock_count > 0 THEN 'تحذير: يوجد منتجات منخفضة المخزون'
            ELSE 'النظام يعمل بشكل طبيعي'
        END AS system_status;
END$$

DELIMITER ;

-- تشغيل فحص صحة النظام
CALL SystemHealthCheck(1);

-- =====================================================
-- إنهاء الملف
-- =====================================================
