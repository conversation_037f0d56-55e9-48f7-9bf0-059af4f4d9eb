<?php
require_once 'config/database.php';
session_start();

// تعيين client_id للاختبار
$_SESSION['client_id'] = 1;

echo "<h1>اختبار إضافة المنتجات - الاختبار النهائي</h1>";

try {
    echo "<h2>1. إعداد بيانات الاختبار</h2>";
    
    // البحث عن جلسة نشطة أو إنشاء واحدة
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        echo "<p style='color: orange;'>⚠️ إنشاء جلسة تجريبية...</p>";
        $stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
        $device = $stmt->fetch();
        if ($device) {
            $pdo->exec("INSERT INTO sessions (device_id, status, start_time) VALUES ({$device['device_id']}, 'active', NOW())");
            $session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة: $session_id</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد أجهزة</p>";
            exit;
        }
    } else {
        $session_id = $session['session_id'];
        echo "<p style='color: blue;'>ℹ️ استخدام الجلسة: $session_id</p>";
    }
    
    // البحث عن منتج أو إنشاء واحد
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 LIMIT 1");
    $product = $stmt->fetch();
    
    if (!$product) {
        echo "<p style='color: orange;'>⚠️ إنشاء منتج تجريبي...</p>";
        $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('منتج اختبار نهائي', 12.50, 'اختبار', 1)");
        $product_id = $pdo->lastInsertId();
        $product_name = 'منتج اختبار نهائي';
        $product_price = 12.50;
        echo "<p style='color: green;'>✅ تم إنشاء منتج: $product_id</p>";
    } else {
        $product_id = $product['id'];
        $product_name = $product['name'];
        $product_price = $product['price'];
        echo "<p style='color: blue;'>ℹ️ استخدام المنتج: $product_name ($product_id) - $product_price ج.م</p>";
    }
    
    echo "<h2>2. اختبار إضافة منتج عبر API</h2>";
    
    // تحضير بيانات الطلب
    $api_data = [
        'session_id' => $session_id,
        'product_id' => $product_id,
        'quantity' => 2
    ];
    
    echo "<p><strong>بيانات الطلب:</strong></p>";
    echo "<pre>" . json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // استدعاء API باستخدام cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($api_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: ' . $_SERVER['HTTP_COOKIE'] // تمرير الجلسة
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>كود الاستجابة:</strong> $http_code</p>";
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ خطأ cURL: $curl_error</p>";
    } else {
        echo "<p><strong>الاستجابة:</strong></p>";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>" . $response . "</pre>";
        
        $response_data = json_decode($response, true);
        
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3 style='color: green;'>✅ نجح إضافة المنتج!</h3>";
            echo "<ul>";
            echo "<li><strong>اسم المنتج:</strong> {$response_data['product_name']}</li>";
            echo "<li><strong>الكمية:</strong> {$response_data['quantity']}</li>";
            echo "<li><strong>التكلفة الإجمالية:</strong> {$response_data['total_cost']} ج.م</li>";
            echo "<li><strong>تكلفة المنتجات:</strong> {$response_data['products_cost']} ج.م</li>";
            echo "<li><strong>تكلفة الوقت:</strong> {$response_data['time_cost']} ج.م</li>";
            echo "</ul>";
            echo "</div>";
            
            // التحقق من البيانات في قاعدة البيانات
            echo "<h2>3. التحقق من البيانات المحفوظة</h2>";
            
            $stmt = $pdo->prepare("SELECT * FROM session_products WHERE session_id = ? ORDER BY id DESC LIMIT 1");
            $stmt->execute([$session_id]);
            $saved_product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($saved_product) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th style='background-color: #e9ecef; padding: 8px;'>الحقل</th><th style='background-color: #e9ecef; padding: 8px;'>القيمة</th></tr>";
                foreach ($saved_product as $key => $value) {
                    $display_value = $value ?? 'NULL';
                    if (in_array($key, ['unit_price', 'total_price', 'price']) && is_numeric($value)) {
                        $display_value = number_format($value, 2) . ' ج.م';
                    }
                    echo "<tr><td style='padding: 8px;'>$key</td><td style='padding: 8px;'>$display_value</td></tr>";
                }
                echo "</table>";
                
                // التحقق من صحة الحسابات
                $expected_total = $product_price * $api_data['quantity'];
                $actual_total = $saved_product['total_price'] ?? $saved_product['unit_price'] * $saved_product['quantity'];
                
                if (abs($actual_total - $expected_total) < 0.01) {
                    echo "<p style='color: green;'>✅ الحسابات صحيحة: " . number_format($actual_total, 2) . " = " . number_format($product_price, 2) . " × {$api_data['quantity']}</p>";
                } else {
                    echo "<p style='color: red;'>❌ خطأ في الحسابات: " . number_format($actual_total, 2) . " ≠ " . number_format($expected_total, 2) . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على البيانات المحفوظة</p>";
            }
            
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3 style='color: red;'>❌ فشل في إضافة المنتج</h3>";
            if ($response_data && isset($response_data['error'])) {
                echo "<p><strong>الخطأ:</strong> {$response_data['error']}</p>";
            }
            if ($response_data && isset($response_data['debug_error'])) {
                echo "<p><strong>تفاصيل الخطأ:</strong> {$response_data['debug_error']}</p>";
            }
            echo "</div>";
        }
    }
    
    echo "<h2>4. اختبار إضافة منتج مباشرة</h2>";
    
    try {
        $quantity = 1;
        $unit_price = $product_price;
        $total_price = $unit_price * $quantity;
        
        $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);
        
        $direct_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إضافة منتج مباشرة برقم: $direct_id</p>";
        echo "<p>التفاصيل: الكمية = $quantity، السعر = " . number_format($unit_price, 2) . "، الإجمالي = " . number_format($total_price, 2) . "</p>";
        
        // حذف السجل التجريبي
        $pdo->exec("DELETE FROM session_products WHERE id = $direct_id");
        echo "<p style='color: blue;'>ℹ️ تم حذف السجل التجريبي</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل في الإضافة المباشرة: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. النتيجة النهائية</h2>";
    
    $api_success = ($response_data && isset($response_data['success']) && $response_data['success']);
    
    echo "<div style='background-color: " . ($api_success ? "#d4edda" : "#f8d7da") . "; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3 style='color: " . ($api_success ? "green" : "red") . ";'>" . ($api_success ? "🎉 المشكلة تم حلها!" : "⚠️ المشكلة ما زالت موجودة") . "</h3>";
    
    if ($api_success) {
        echo "<p><strong>✅ تم إصلاح خطأ unit_price بنجاح!</strong></p>";
        echo "<p>يمكنك الآن إضافة المنتجات من واجهة المستخدم بدون أي مشاكل.</p>";
        echo "<ul>";
        echo "<li>API إضافة المنتج يعمل بشكل صحيح</li>";
        echo "<li>البيانات تُحفظ في قاعدة البيانات</li>";
        echo "<li>الحسابات صحيحة</li>";
        echo "</ul>";
    } else {
        echo "<p><strong>❌ المشكلة ما زالت تحتاج إلى إصلاح</strong></p>";
        echo "<p>يرجى مراجعة رسائل الخطأ أعلاه والتأكد من تشغيل ملف الإصلاح.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: red;'>❌ حدث خطأ في الاختبار</h3>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<pre style='font-size: 12px;'>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>
