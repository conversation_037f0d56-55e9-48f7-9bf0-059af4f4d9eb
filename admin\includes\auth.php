<?php
/**
 * نظام المصادقة والصلاحيات للإدمن - PlayGood
 * ملف المصادقة الرئيسي مع نظام الصلاحيات المتقدم
 */

// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تضمين نظام الصلاحيات
require_once __DIR__ . '/admin-permissions.php';

/**
 * التحقق من جلسة الإدمن
 * @param bool $check_permissions هل يتم فحص صلاحيات الصفحة الحالية
 */
function checkAdminSession($check_permissions = true) {
    if (!isset($_SESSION['admin_id'])) {
        header("Location: login.php");
        exit();
    }

    // التحقق من صلاحيات الصفحة الحالية
    if ($check_permissions) {
        checkCurrentAdminPagePermission();
    }
}

/**
 * التحقق من جلسة الإدمن بدون فحص الصلاحيات
 */
function checkAdminSessionOnly() {
    checkAdminSession(false);
}

/**
 * التحقق من دور الإدمن المطلوب للصفحة
 * @param string $required_role الدور المطلوب
 */
function requireAdminRole($required_role) {
    checkAdminSessionOnly();

    if (!hasAdminRole($required_role)) {
        $_SESSION['error_message'] = 'ليس لديك الصلاحيات الكافية للوصول إلى هذه الصفحة';
        header('Location: dashboard.php');
        exit();
    }
}

/**
 * التحقق من صلاحية الوصول لصفحة معينة
 * @param string $page_name اسم الصفحة المطلوبة
 */
function requirePagePermission($page_name) {
    checkAdminSessionOnly();

    if (!hasAdminPagePermission($page_name)) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        header('Location: dashboard.php');
        exit();
    }
}
