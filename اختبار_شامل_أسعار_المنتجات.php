<?php
// اختبار شامل لإصلاح أسعار المنتجات
session_start();
require_once 'config/database.php';

// محاكاة تسجيل الدخول
$_SESSION['client_id'] = 1;

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار شامل - إصلاح أسعار المنتجات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center mb-4'>
        <i class='fas fa-check-circle text-success'></i>
        اختبار شامل - إصلاح أسعار المنتجات
    </h1>";

$all_tests_passed = true;
$test_results = [];

try {
    // اختبار 1: فحص API جلب المنتجات
    echo "<div class='test-section'>
        <h2><i class='fas fa-list'></i> اختبار 1: API جلب المنتجات</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_products.php?page=1';
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . session_name() . '=' . session_id()
        ]
    ]);
    
    $response = file_get_contents($api_url, false, $context);
    $data = json_decode($response, true);
    
    if ($data && $data['success'] && isset($data['products'])) {
        $products = $data['products'];
        $price_test_passed = true;
        
        foreach ($products as $product) {
            if (!is_numeric($product['price']) || $product['price'] <= 0) {
                $price_test_passed = false;
                break;
            }
        }
        
        if ($price_test_passed && count($products) > 0) {
            echo "<p class='success'>✅ API جلب المنتجات يعمل بشكل صحيح</p>";
            echo "<p>عدد المنتجات: " . count($products) . "</p>";
            $test_results['get_products'] = true;
        } else {
            echo "<p class='error'>❌ مشكلة في أسعار المنتجات</p>";
            $test_results['get_products'] = false;
            $all_tests_passed = false;
        }
    } else {
        echo "<p class='error'>❌ فشل في جلب المنتجات</p>";
        $test_results['get_products'] = false;
        $all_tests_passed = false;
    }
    echo "</div>";
    
    // اختبار 2: إضافة منتج للجلسة
    echo "<div class='test-section'>
        <h2><i class='fas fa-plus'></i> اختبار 2: إضافة منتج للجلسة</h2>";
    
    // البحث عن جلسة نشطة أو إنشاؤها
    $stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $stmt->fetch();
    
    if (!$session) {
        $stmt = $pdo->prepare("INSERT INTO sessions (device_id, customer_name, start_time, status) VALUES (1, 'اختبار شامل', NOW(), 'active')");
        $stmt->execute();
        $session_id = $pdo->lastInsertId();
        echo "<p class='info'>ℹ️ تم إنشاء جلسة تجريبية: $session_id</p>";
    } else {
        $session_id = $session['session_id'];
        echo "<p class='info'>ℹ️ استخدام الجلسة الموجودة: $session_id</p>";
    }
    
    // البحث عن منتج
    $stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE client_id = 1 AND price > 0 LIMIT 1");
    $product = $stmt->fetch();
    
    if ($product) {
        // إضافة المنتج عبر API
        $api_data = [
            'session_id' => $session_id,
            'product_id' => $product['id'],
            'quantity' => 2
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($api_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Cookie: ' . $_SERVER['HTTP_COOKIE']
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $api_response = curl_exec($ch);
        $api_result = json_decode($api_response, true);
        curl_close($ch);
        
        if ($api_result && $api_result['success']) {
            echo "<p class='success'>✅ تم إضافة المنتج بنجاح</p>";
            echo "<p>المنتج: {$product['name']} × 2 = " . ($product['price'] * 2) . " ج.م</p>";
            $test_results['add_product'] = true;
        } else {
            echo "<p class='error'>❌ فشل في إضافة المنتج</p>";
            echo "<p>الخطأ: " . ($api_result['error'] ?? 'غير معروف') . "</p>";
            $test_results['add_product'] = false;
            $all_tests_passed = false;
        }
    } else {
        echo "<p class='warning'>⚠️ لا توجد منتجات متاحة للاختبار</p>";
        $test_results['add_product'] = false;
    }
    echo "</div>";
    
    // اختبار 3: جلب منتجات الجلسة
    echo "<div class='test-section'>
        <h2><i class='fas fa-shopping-cart'></i> اختبار 3: جلب منتجات الجلسة</h2>";
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $session_id;
    $response = file_get_contents($api_url, false, $context);
    $session_data = json_decode($response, true);
    
    if ($session_data && $session_data['success'] && isset($session_data['products'])) {
        $session_products = $session_data['products'];
        $session_price_test_passed = true;
        
        echo "<p class='info'>عدد المنتجات في الجلسة: " . count($session_products) . "</p>";
        
        if (count($session_products) > 0) {
            echo "<table class='table table-bordered'>";
            echo "<tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th><th>الحالة</th></tr>";
            
            foreach ($session_products as $sp) {
                $price = floatval($sp['price']);
                $total = floatval($sp['total']);
                $quantity = intval($sp['quantity']);
                
                $is_valid = ($price > 0 && $total > 0 && abs($total - ($price * $quantity)) < 0.01);
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($sp['product_name']) . "</td>";
                echo "<td>$quantity</td>";
                echo "<td>$price ج.م</td>";
                echo "<td>$total ج.م</td>";
                echo "<td>" . ($is_valid ? "<span class='success'>✅ صحيح</span>" : "<span class='error'>❌ خطأ</span>") . "</td>";
                echo "</tr>";
                
                if (!$is_valid) {
                    $session_price_test_passed = false;
                }
            }
            echo "</table>";
            
            if ($session_price_test_passed) {
                echo "<p class='success'>✅ جميع أسعار منتجات الجلسة صحيحة</p>";
                $test_results['session_products'] = true;
            } else {
                echo "<p class='error'>❌ يوجد أخطاء في أسعار منتجات الجلسة</p>";
                $test_results['session_products'] = false;
                $all_tests_passed = false;
            }
        } else {
            echo "<p class='warning'>⚠️ لا توجد منتجات في الجلسة</p>";
            $test_results['session_products'] = false;
        }
    } else {
        echo "<p class='error'>❌ فشل في جلب منتجات الجلسة</p>";
        $test_results['session_products'] = false;
        $all_tests_passed = false;
    }
    echo "</div>";
    
    // ملخص النتائج
    echo "<div class='test-section'>
        <h2><i class='fas fa-chart-bar'></i> ملخص النتائج</h2>";
    
    $passed_tests = array_sum($test_results);
    $total_tests = count($test_results);
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h4>نتائج الاختبارات:</h4>";
    echo "<ul>";
    foreach ($test_results as $test_name => $result) {
        $test_names = [
            'get_products' => 'جلب المنتجات',
            'add_product' => 'إضافة منتج للجلسة',
            'session_products' => 'جلب منتجات الجلسة'
        ];
        
        $icon = $result ? "✅" : "❌";
        $class = $result ? "success" : "error";
        echo "<li class='$class'>$icon " . $test_names[$test_name] . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<h4>الإحصائيات:</h4>";
    echo "<p>الاختبارات الناجحة: <strong>$passed_tests</strong> من <strong>$total_tests</strong></p>";
    
    $success_rate = round(($passed_tests / $total_tests) * 100, 2);
    echo "<p>معدل النجاح: <strong>$success_rate%</strong></p>";
    
    if ($all_tests_passed) {
        echo "<div class='alert alert-success'>";
        echo "<h4><i class='fas fa-check-circle'></i> تهانينا!</h4>";
        echo "<p>جميع الاختبارات نجحت. إصلاح أسعار المنتجات مكتمل وجاهز للاستخدام.</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4><i class='fas fa-exclamation-triangle'></i> تحتاج مراجعة</h4>";
        echo "<p>بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه وتشغيل ملفات الإصلاح.</p>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-times-circle'></i> خطأ في الاختبار</h4>";
    echo "<p>حدث خطأ أثناء تشغيل الاختبارات: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
