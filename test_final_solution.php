<?php
// اختبار الحل النهائي لمشكلة المنتجات
session_start();
$_SESSION['client_id'] = 1;

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار الحل النهائي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .test-box { margin: 15px 0; padding: 15px; border: 2px solid #ddd; border-radius: 8px; }
        .test-pass { border-color: green; background: #f0fff0; }
        .test-fail { border-color: red; background: #fff0f0; }
    </style>
</head>
<body>
<h1>🧪 اختبار الحل النهائي لمشكلة المنتجات</h1>";

$tests_passed = 0;
$total_tests = 0;

try {
    // اختبار 1: فحص بنية الجدول
    $total_tests++;
    echo "<div class='test-box'>";
    echo "<h2>اختبار 1: فحص بنية جدول session_products</h2>";
    
    $stmt = $pdo->query("DESCRIBE session_products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_price = false;
    $has_unit_price = false;
    $has_total_price = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'price') $has_price = true;
        if ($column['Field'] === 'unit_price') $has_unit_price = true;
        if ($column['Field'] === 'total_price') $has_total_price = true;
    }
    
    if ($has_price && !$has_unit_price && !$has_total_price) {
        echo "<p class='success'>✅ البنية صحيحة: يوجد عمود price فقط</p>";
        $tests_passed++;
        echo "</div>";
    } else {
        echo "<p class='error'>❌ البنية غير صحيحة:</p>";
        echo "<ul>";
        echo "<li>price: " . ($has_price ? "موجود ✅" : "غير موجود ❌") . "</li>";
        echo "<li>unit_price: " . ($has_unit_price ? "موجود ❌" : "غير موجود ✅") . "</li>";
        echo "<li>total_price: " . ($has_total_price ? "موجود ❌" : "غير موجود ✅") . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // اختبار 2: إضافة منتج مباشرة
    $total_tests++;
    echo "<div class='test-box'>";
    echo "<h2>اختبار 2: إضافة منتج مباشرة لقاعدة البيانات</h2>";
    
    // البحث عن جلسة ومنتج
    $session_stmt = $pdo->query("SELECT session_id FROM sessions WHERE status = 'active' LIMIT 1");
    $session = $session_stmt->fetch();
    
    $product_stmt = $pdo->query("SELECT id, name, price FROM cafeteria_items WHERE price > 0 LIMIT 1");
    $product = $product_stmt->fetch();
    
    if ($session && $product) {
        try {
            $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
            $stmt->execute([$session['session_id'], $product['id'], 1, $product['price']]);
            
            $test_id = $pdo->lastInsertId();
            echo "<p class='success'>✅ تم إضافة المنتج بنجاح - ID: $test_id</p>";
            
            // التحقق من البيانات
            $check_stmt = $pdo->prepare("SELECT * FROM session_products WHERE id = ?");
            $check_stmt->execute([$test_id]);
            $inserted = $check_stmt->fetch();
            
            if ($inserted) {
                echo "<p class='info'>البيانات: {$product['name']} × {$inserted['quantity']} = {$inserted['price']} ج.م</p>";
                $tests_passed++;
                
                // حذف السجل التجريبي
                $pdo->exec("DELETE FROM session_products WHERE id = $test_id");
                echo "<p class='info'>تم حذف السجل التجريبي</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ فشل في إضافة المنتج: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ لا توجد جلسة نشطة أو منتجات متاحة</p>";
    }
    echo "</div>";
    
    // اختبار 3: API إضافة المنتج
    $total_tests++;
    echo "<div class='test-box'>";
    echo "<h2>اختبار 3: API إضافة المنتج</h2>";
    
    if ($session && $product) {
        $api_data = [
            'session_id' => $session['session_id'],
            'product_id' => $product['id'],
            'quantity' => 2
        ];
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\nCookie: " . session_name() . '=' . session_id(),
                'content' => json_encode($api_data)
            ]
        ]);
        
        $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/add_session_product.php';
        $response = @file_get_contents($api_url, false, $context);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if ($result && $result['success']) {
                echo "<p class='success'>✅ API يعمل بشكل صحيح</p>";
                echo "<p class='info'>التكلفة الإجمالية: {$result['total_cost']} ج.م</p>";
                if (isset($result['products_cost'])) {
                    echo "<p class='info'>تكلفة المنتجات: {$result['products_cost']} ج.م</p>";
                }
                $tests_passed++;
            } else {
                echo "<p class='error'>❌ API أرجع خطأ: " . ($result['error'] ?? 'خطأ غير معروف') . "</p>";
            }
        } else {
            echo "<p class='error'>❌ فشل في الاتصال بـ API</p>";
        }
    } else {
        echo "<p class='error'>❌ لا توجد بيانات كافية للاختبار</p>";
    }
    echo "</div>";
    
    // اختبار 4: جلب منتجات الجلسة
    $total_tests++;
    echo "<div class='test-box'>";
    echo "<h2>اختبار 4: جلب منتجات الجلسة</h2>";
    
    if ($session) {
        $get_api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $session['session_id'];
        $get_context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'Cookie: ' . session_name() . '=' . session_id()
            ]
        ]);
        
        $get_response = @file_get_contents($get_api_url, false, $get_context);
        
        if ($get_response) {
            $get_result = json_decode($get_response, true);
            
            if ($get_result && $get_result['success']) {
                echo "<p class='success'>✅ جلب المنتجات يعمل بشكل صحيح</p>";
                echo "<p class='info'>عدد المنتجات في الجلسة: " . count($get_result['products']) . "</p>";
                
                $total_products_cost = 0;
                foreach ($get_result['products'] as $item) {
                    $item_cost = $item['quantity'] * $item['price'];
                    $total_products_cost += $item_cost;
                }
                
                if ($total_products_cost > 0) {
                    echo "<p class='info'>إجمالي تكلفة المنتجات: " . number_format($total_products_cost, 2) . " ج.م</p>";
                }
                
                $tests_passed++;
            } else {
                echo "<p class='error'>❌ فشل في جلب المنتجات: " . ($get_result['error'] ?? 'خطأ غير معروف') . "</p>";
            }
        } else {
            echo "<p class='error'>❌ فشل في الاتصال بـ API جلب المنتجات</p>";
        }
    }
    echo "</div>";
    
    // النتيجة النهائية
    echo "<div class='test-box " . ($tests_passed == $total_tests ? 'test-pass' : 'test-fail') . "'>";
    echo "<h2>🏁 النتيجة النهائية</h2>";
    echo "<p><strong>الاختبارات المجتازة: $tests_passed من $total_tests</strong></p>";
    
    if ($tests_passed == $total_tests) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<h3 class='success'>🎉 جميع الاختبارات نجحت!</h3>";
        echo "<ul>";
        echo "<li>✅ بنية الجدول صحيحة</li>";
        echo "<li>✅ إضافة المنتجات تعمل</li>";
        echo "<li>✅ APIs تعمل بشكل صحيح</li>";
        echo "<li>✅ حساب التكلفة دقيق</li>";
        echo "</ul>";
        echo "<p class='success'><strong>النظام جاهز للاستخدام! 🚀</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<h3 class='error'>❌ بعض الاختبارات فشلت</h3>";
        echo "<p>يرجى تشغيل ملف fix_all_session_products_issues.php أولاً</p>";
        echo "</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-box test-fail'>";
    echo "<p class='error'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
