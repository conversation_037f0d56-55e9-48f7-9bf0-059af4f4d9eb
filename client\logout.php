<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';

// تسجيل خروج آمن
if (isset($_SESSION['admin_id'])) {
    secureLogout($pdo, '../admin/login.php');
} elseif (isset($_SESSION['client_id'])) {
    secureLogout($pdo, 'login.php');
} elseif (isset($_SESSION['employee_id'])) {
    secureLogout($pdo, 'employee-login.php');
} else {
    // تنظيف الجلسة العادية كـ fallback
    session_start();
    session_destroy();
    header('Location: login.php');
    exit;
}
?>