<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الأجهزة
    if (!hasPagePermission('devices')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الأجهزة';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - التحقق من الوصول للصفحة والصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('devices')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_devices') && !employeeHasPermission('view_devices')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة الأجهزة";
$active_page = "devices";

// Initialize messages array
$messages = [];

// Handle all POST/GET operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['delete'])) {
    // إضافة جهاز جديد
    if (isset($_POST['add_device'])) {
        // Validate required fields
        $required_fields = [
            'device_name' => 'اسم الجهاز',
            'device_type' => 'نوع الجهاز',
            'hourly_rate' => 'سعر الساعة العادي',
            'single_rate' => 'سعر الساعة (فردي)',
            'multi_rate' => 'سعر الساعة (مالتي)'
        ];

        $errors = [];
        foreach ($required_fields as $field => $label) {
            if (empty($_POST[$field])) {
                $errors[] = "حقل {$label} مطلوب";
            }
        }

        // التحقق من عدم تكرار اسم الجهاز للعميل الواحد
        if (!empty($_POST['device_name'])) {
            $check_device_name = $pdo->prepare("SELECT device_id FROM devices WHERE device_name = ? AND client_id = ?");
            $check_device_name->execute([trim($_POST['device_name']), $client_id]);
            if ($check_device_name->rowCount() > 0) {
                $errors[] = "اسم الجهاز موجود مسبقاً، يرجى اختيار اسم آخر";
            }
        }

        if (!empty($errors)) {
            $messages['error'] = implode('<br>', $errors);
        } else {
            try {
                $stmt = $pdo->prepare('INSERT INTO devices
                    (client_id, device_name, device_type, hourly_rate, single_rate, multi_rate, status, room_id)
                    VALUES (:client_id, :device_name, :device_type, :hourly_rate, :single_rate, :multi_rate, :status, :room_id)');
                $stmt->execute([
                    'client_id' => $client_id,
                    'device_name' => trim($_POST['device_name']),
                    'device_type' => $_POST['device_type'],
                    'hourly_rate' => floatval($_POST['hourly_rate']),
                    'single_rate' => !empty($_POST['single_rate']) ? floatval($_POST['single_rate']) : null,
                    'multi_rate' => !empty($_POST['multi_rate']) ? floatval($_POST['multi_rate']) : null,
                    'status' => 'available',
                    'room_id' => !empty($_POST['room_id']) ? $_POST['room_id'] : null
                ]);
                $_SESSION['success'] = "تم إضافة الجهاز بنجاح";
                header('Location: devices.php');
                exit;
            } catch (PDOException $e) {
                $messages['error'] = "حدث خطأ أثناء إضافة الجهاز";
            }
        }
    }

    // تعديل جهاز
    if (isset($_POST['edit_device'])) {
        $errors = [];
        $device_id = $_POST['device_id'];
        $new_status = $_POST['status'];
        $new_device_name = trim($_POST['device_name']);

        // التحقق من عدم تكرار اسم الجهاز للعميل الواحد (باستثناء الجهاز الحالي)
        if (!empty($new_device_name)) {
            $check_device_name = $pdo->prepare("SELECT device_id FROM devices WHERE device_name = ? AND client_id = ? AND device_id != ?");
            $check_device_name->execute([$new_device_name, $client_id, $device_id]);
            if ($check_device_name->rowCount() > 0) {
                $errors[] = "اسم الجهاز موجود مسبقاً، يرجى اختيار اسم آخر";
            }
        }

        // الحصول على الحالة الحالية للجهاز
        $current_device_stmt = $pdo->prepare("SELECT status FROM devices WHERE device_id = ? AND client_id = ?");
        $current_device_stmt->execute([$device_id, $client_id]);
        $current_device = $current_device_stmt->fetch();

        if (!$current_device) {
            $errors[] = "الجهاز غير موجود";
        } else {
            // التحقق من وجود جلسات نشطة إذا كان المستخدم يحاول تغيير الحالة
            if ($current_device['status'] !== $new_status) {
                $active_sessions_stmt = $pdo->prepare("SELECT COUNT(*) FROM sessions WHERE device_id = ? AND status = 'active'");
                $active_sessions_stmt->execute([$device_id]);
                $active_sessions_count = $active_sessions_stmt->fetchColumn();

                if ($active_sessions_count > 0) {
                    $errors[] = "لا يمكن تغيير حالة الجهاز لأنه مرتبط بجلسة نشطة. يرجى إنهاء الجلسة أولاً";
                }
            }
        }

        if (!empty($errors)) {
            $messages['error'] = implode('<br>', $errors);
        } else {
            try {
                $stmt = $pdo->prepare('UPDATE devices SET
                    device_name = :device_name,
                    device_type = :device_type,
                    hourly_rate = :hourly_rate,
                    single_rate = :single_rate,
                    multi_rate = :multi_rate,
                    status = :status,
                    room_id = :room_id,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE device_id = :device_id AND client_id = :client_id');

                $stmt->execute([
                    'device_name' => $new_device_name,
                    'device_type' => $_POST['device_type'],
                    'hourly_rate' => floatval($_POST['hourly_rate']),
                    'single_rate' => !empty($_POST['single_rate']) ? floatval($_POST['single_rate']) : null,
                    'multi_rate' => !empty($_POST['multi_rate']) ? floatval($_POST['multi_rate']) : null,
                    'status' => $new_status,
                    'room_id' => !empty($_POST['room_id']) ? $_POST['room_id'] : null,
                    'device_id' => $device_id,
                    'client_id' => $client_id
                ]);
                $_SESSION['success'] = "تم تعديل الجهاز بنجاح";
                header('Location: devices.php');
                exit;
            } catch (PDOException $e) {
                $messages['error'] = "حدث خطأ أثناء تعديل الجهاز";
            }
        }
    }

    // حذف جهاز
    if (isset($_GET['delete'])) {
        try {
            $device_id = intval($_GET['delete']);

            // التحقق من وجود الجهاز وأنه ينتمي للعميل
            $check_device = $pdo->prepare('SELECT device_name FROM devices WHERE device_id = ? AND client_id = ?');
            $check_device->execute([$device_id, $client_id]);
            $device = $check_device->fetch();

            if (!$device) {
                $messages['error'] = "الجهاز غير موجود أو لا تملك صلاحية حذفه";
            } else {
                // التحقق من وجود جلسات مرتبطة بالجهاز
                $sessions_check = $pdo->prepare('SELECT COUNT(*) FROM sessions WHERE device_id = ?');
                $sessions_check->execute([$device_id]);
                $sessions_count = $sessions_check->fetchColumn();

                if ($sessions_count > 0) {
                    // إذا كان هناك جلسات مرتبطة، نسأل المستخدم عما يريد فعله
                    if (isset($_GET['force']) && $_GET['force'] == '1') {
                        // المستخدم اختار حذف الجهاز مع إلغاء ربط الجلسات
                        $pdo->beginTransaction();

                        // تحديث الجلسات لإلغاء ربطها بالجهاز (تعيين device_id = NULL)
                        $update_sessions = $pdo->prepare('UPDATE sessions SET device_id = NULL WHERE device_id = ?');
                        $update_sessions->execute([$device_id]);

                        // حذف الجهاز
                        $delete_stmt = $pdo->prepare('DELETE FROM devices WHERE device_id = ? AND client_id = ?');
                        $delete_stmt->execute([$device_id, $client_id]);

                        // التحقق من نجاح الحذف
                        if ($delete_stmt->rowCount() === 0) {
                            throw new Exception("فشل في حذف الجهاز - قد يكون محذوف مسبقاً");
                        }

                        $pdo->commit();
                        $_SESSION['success'] = "تم حذف الجهاز '{$device['device_name']}' بنجاح مع إلغاء ربط {$sessions_count} جلسة";
                        header('Location: devices.php');
                        exit;
                    } else {
                        // عرض رسالة تحذيرية مع خيارات
                        $messages['warning'] = "
                            <strong>تحذير:</strong> لا يمكن حذف الجهاز '{$device['device_name']}' لأنه مرتبط بـ {$sessions_count} جلسة.<br>
                            <div class='mt-2'>
                                <a href='devices.php?delete={$device_id}&force=1' class='btn btn-danger btn-sm me-2'
                                   onclick='return confirm(\"هل أنت متأكد من حذف الجهاز وإلغاء ربط جميع الجلسات المرتبطة به؟\")'>
                                    <i class='fas fa-trash me-1'></i>حذف الجهاز وإلغاء ربط الجلسات
                                </a>
                                <a href='sessions.php?device_id={$device_id}' class='btn btn-info btn-sm'>
                                    <i class='fas fa-eye me-1'></i>عرض الجلسات المرتبطة
                                </a>
                            </div>
                        ";
                    }
                } else {
                    // لا توجد جلسات مرتبطة، يمكن حذف الجهاز مباشرة
                    $stmt = $pdo->prepare('DELETE FROM devices WHERE device_id = ? AND client_id = ?');
                    $result = $stmt->execute([$device_id, $client_id]);

                    if ($result && $stmt->rowCount() > 0) {
                        $_SESSION['success'] = "تم حذف الجهاز '{$device['device_name']}' بنجاح";
                        header('Location: devices.php');
                        exit;
                    } else {
                        $messages['error'] = "فشل في حذف الجهاز";
                    }
                }
            }
        } catch (PDOException $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log('خطأ في حذف الجهاز: ' . $e->getMessage());

            // التحقق من نوع الخطأ
            if (strpos($e->getMessage(), 'foreign key constraint') !== false ||
                strpos($e->getMessage(), 'FOREIGN KEY') !== false) {
                $messages['error'] = "
                    <strong>خطأ في قيود قاعدة البيانات:</strong> لا يمكن حذف الجهاز بسبب وجود جلسات مرتبطة به.<br>
                    <div class='mt-2'>
                        <a href='fix_device_deletion.php' class='btn btn-warning btn-sm me-2' target='_blank'>
                            <i class='fas fa-tools me-1'></i>إصلاح قاعدة البيانات
                        </a>
                        <small class='text-muted'>قم بتشغيل أداة الإصلاح أولاً ثم حاول مرة أخرى</small>
                    </div>
                ";
            } else {
                $messages['error'] = "حدث خطأ أثناء حذف الجهاز: " . $e->getMessage();
            }
        } catch (Exception $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log('خطأ عام في حذف الجهاز: ' . $e->getMessage());
            $messages['error'] = $e->getMessage();
        }
    }
}

// Fetch data after all operations
$rooms_stmt = $pdo->prepare('SELECT * FROM rooms WHERE client_id = ?');
$rooms_stmt->execute([$client_id]);
$rooms = $rooms_stmt->fetchAll();

$devices = $pdo->prepare('SELECT d.*, r.room_name,
    (SELECT COUNT(*) FROM sessions s WHERE s.device_id = d.device_id AND (s.status = "active" OR s.status IS NULL)) as active_sessions_count
    FROM devices d
    LEFT JOIN rooms r ON d.room_id = r.room_id
    WHERE d.client_id = ?
    ORDER BY d.device_name');
$devices->execute([$client_id]);
$devices = $devices->fetchAll();

// Now include the header and rest of the HTML
require_once 'includes/header.php';

// Display any messages stored in session
if (isset($_SESSION['success'])) {
    $messages['success'] = $_SESSION['success'];
    unset($_SESSION['success']);
}
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($messages['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $messages['success']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if (isset($messages['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $messages['error']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if (isset($messages['warning'])): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $messages['warning']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إضافة جهاز جديد -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>إضافة جهاز جديد</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" id="addDeviceForm" novalidate>
                        <div class="mb-3">
                            <label for="device_name" class="form-label">اسم الجهاز</label>
                            <input type="text" class="form-control" id="device_name" name="device_name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الجهاز</div>
                        </div>
                        <div class="mb-3">
                            <label for="device_type" class="form-label">نوع الجهاز</label>
                            <select class="form-select" id="device_type" name="device_type" required>
                                <option value="">اختر النوع</option>
                                <option value="PS4">PS4</option>
                                <option value="PS5">PS5</option>
                                <option value="XBOX">XBOX</option>
                                <option value="PC">PC</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار نوع الجهاز</div>
                        </div>
                        <div class="mb-3">
                            <label for="hourly_rate" class="form-label">سعر الساعة العادي</label>
                            <div class="input-group has-validation">
                                <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" step="0.01" required>
                                <span class="input-group-text">ج.م</span>
                                <div class="invalid-feedback">يرجى إدخال سعر الساعة العادي</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="single_rate" class="form-label">سعر الساعة (فردي)</label>
                            <div class="input-group has-validation">
                                <input type="number" class="form-control" id="single_rate" name="single_rate" step="0.01" required>
                                <span class="input-group-text">ج.م</span>
                                <div class="invalid-feedback">يرجى إدخال سعر الساعة الفردي</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="multi_rate" class="form-label">سعر الساعة (مالتي)</label>
                            <div class="input-group has-validation">
                                <input type="number" class="form-control" id="multi_rate" name="multi_rate" step="0.01" required>
                                <span class="input-group-text">ج.م</span>
                                <div class="invalid-feedback">يرجى إدخال سعر الساعة للمالتي</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="room_id" class="form-label">الغرفة (اختياري)</label>
                            <select class="form-select" id="room_id" name="room_id">
                                <option value="">بدون غرفة</option>
                                <?php foreach ($rooms as $room): ?>
                                    <option value="<?php echo $room['room_id']; ?>">
                                        <?php echo htmlspecialchars($room['room_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" name="add_device" class="btn btn-primary w-100" onclick="return validateForm()">
                            <i class="fas fa-plus-circle me-2"></i>إضافة الجهاز
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- عرض الأجهزة -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الأجهزة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-gamepad me-2"></i>الجهاز</th>
                                    <th><i class="fas fa-tag me-2"></i>النوع</th>
                                    <th><i class="fas fa-money-bill me-2"></i>السعر العادي</th>
                                    <th><i class="fas fa-user me-2"></i>سعر فردي</th>
                                    <th><i class="fas fa-users me-2"></i>سعر مالتي</th>
                                    <th><i class="fas fa-door-open me-2"></i>الغرفة</th>
                                    <th><i class="fas fa-circle me-2"></i>الحالة</th>
                                    <th><i class="fas fa-play me-2"></i>جلسات نشطة</th>
                                    <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($devices as $device): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($device['device_name']); ?></td>
                                    <td><?php echo htmlspecialchars($device['device_type']); ?></td>
                                    <td><?php echo number_format($device['hourly_rate'], 2); ?> ج.م</td>
                                    <td><?php echo $device['single_rate'] ? number_format($device['single_rate'], 2) . ' ج.م' : '-'; ?></td>
                                    <td><?php echo $device['multi_rate'] ? number_format($device['multi_rate'], 2) . ' ج.م' : '-'; ?></td>
                                    <td><?php echo $device['room_name'] ?? 'بدون غرفة'; ?></td>
                                    <td>
                                        <?php
                                        $status_class = 'danger';
                                        $status_text = 'مشغول';

                                        if ($device['status'] == 'available') {
                                            $status_class = 'success';
                                            $status_text = 'متاح';
                                        } elseif ($device['status'] == 'maintenance') {
                                            $status_class = 'warning';
                                            $status_text = 'صيانة';
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($device['active_sessions_count'] > 0): ?>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                <?php echo $device['active_sessions_count']; ?> جلسة نشطة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-check me-1"></i>
                                                لا توجد جلسات
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info me-1"
                                                onclick="editDevice(<?php echo htmlspecialchars(json_encode($device)); ?>)"
                                                title="تعديل"
                                                <?php echo $device['active_sessions_count'] > 0 ? 'data-has-active-sessions="true"' : ''; ?>>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger"
                                                onclick="deleteDevice(<?php echo $device['device_id']; ?>)"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل الجهاز -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الجهاز</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="device_id" id="edit_device_id">
                    <div class="mb-3">
                        <label class="form-label">اسم الجهاز</label>
                        <input type="text" class="form-control" name="device_name" id="edit_device_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الجهاز</label>
                        <select class="form-select" name="device_type" id="edit_device_type" required>
                            <option value="PS4">PS4</option>
                            <option value="PS5">PS5</option>
                            <option value="XBOX">XBOX</option>
                            <option value="PC">PC</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر الساعة العادي</label>
                        <input type="number" class="form-control" name="hourly_rate" id="edit_hourly_rate" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر الساعة (فردي)</label>
                        <input type="number" class="form-control" name="single_rate" id="edit_single_rate" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر الساعة (مالتي)</label>
                        <input type="number" class="form-control" name="multi_rate" id="edit_multi_rate" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الغرفة</label>
                        <select class="form-select" name="room_id" id="edit_room_id">
                            <?php foreach ($rooms as $room): ?>
                                <option value="<?php echo $room['room_id']; ?>">
                                    <?php echo htmlspecialchars($room['room_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status" id="edit_status" required>
                            <option value="available">متاح</option>
                            <option value="occupied">مشغول</option>
                            <option value="maintenance">صيانة</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_device" class="btn btn-primary" onclick="return validateEditForm()">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editDevice(device) {
    // التحقق من وجود جلسات نشطة
    if (device.active_sessions_count > 0) {
        const confirmEdit = confirm(
            `تحذير: هذا الجهاز مرتبط بـ ${device.active_sessions_count} جلسة نشطة.\n` +
            'لن تتمكن من تغيير حالة الجهاز حتى تنتهي الجلسات.\n\n' +
            'هل تريد المتابعة لتعديل البيانات الأخرى؟'
        );

        if (!confirmEdit) {
            return;
        }
    }

    document.getElementById('edit_device_id').value = device.device_id;
    document.getElementById('edit_device_name').value = device.device_name;
    document.getElementById('edit_device_type').value = device.device_type;
    document.getElementById('edit_hourly_rate').value = device.hourly_rate;
    document.getElementById('edit_single_rate').value = device.single_rate || '';
    document.getElementById('edit_multi_rate').value = device.multi_rate || '';
    document.getElementById('edit_room_id').value = device.room_id || '';
    document.getElementById('edit_status').value = device.status;

    // حفظ الحالة الأصلية للجهاز وعدد الجلسات النشطة
    document.getElementById('edit_status').setAttribute('data-original-status', device.status);
    document.getElementById('edit_status').setAttribute('data-active-sessions', device.active_sessions_count || 0);

    // تعطيل حقل الحالة إذا كان هناك جلسات نشطة
    if (device.active_sessions_count > 0) {
        document.getElementById('edit_status').disabled = true;
        document.getElementById('edit_status').title = 'لا يمكن تغيير الحالة لوجود جلسات نشطة';
    } else {
        document.getElementById('edit_status').disabled = false;
        document.getElementById('edit_status').title = '';
    }

    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// إضافة مستمع للتحقق من تغيير الحالة
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('edit_status');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            const originalStatus = this.getAttribute('data-original-status');
            const newStatus = this.value;
            const deviceId = document.getElementById('edit_device_id').value;

            if (originalStatus && originalStatus !== newStatus && deviceId) {
                // التحقق من وجود جلسات نشطة عبر AJAX
                checkActiveSessionsForDevice(deviceId, originalStatus, newStatus, this);
            }
        });
    }
});

function checkActiveSessionsForDevice(deviceId, originalStatus, newStatus, selectElement) {
    // إنشاء طلب AJAX للتحقق من الجلسات النشطة
    fetch('check_active_sessions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.has_active_sessions) {
            // إظهار تحذير وإعادة الحالة الأصلية
            alert('تحذير: لا يمكن تغيير حالة الجهاز لأنه مرتبط بجلسة نشطة. يرجى إنهاء الجلسة أولاً.');
            selectElement.value = originalStatus;
        }
    })
    .catch(error => {
        console.error('خطأ في التحقق من الجلسات النشطة:', error);
    });
}

function deleteDevice(id) {
    if (confirm('هل أنت متأكد من حذف هذا الجهاز؟')) {
        window.location.href = 'devices.php?delete=' + id;
    }
}

function validateForm() {
    const form = document.getElementById('addDeviceForm');
    const deviceName = form.querySelector('#device_name').value.trim();

    // التحقق من عدم تكرار اسم الجهاز
    if (deviceName) {
        const existingDevices = <?php echo json_encode(array_column($devices, 'device_name')); ?>;
        if (existingDevices.includes(deviceName)) {
            alert('اسم الجهاز موجود مسبقاً، يرجى اختيار اسم آخر');
            form.querySelector('#device_name').focus();
            return false;
        }
    }

    if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
    }
    form.classList.add('was-validated');
    return form.checkValidity();
}

function validateEditForm() {
    const deviceName = document.getElementById('edit_device_name').value.trim();
    const currentDeviceId = document.getElementById('edit_device_id').value;

    // التحقق من عدم تكرار اسم الجهاز (باستثناء الجهاز الحالي)
    if (deviceName) {
        const existingDevices = <?php echo json_encode($devices); ?>;
        const duplicateDevice = existingDevices.find(device =>
            device.device_name === deviceName && device.device_id != currentDeviceId
        );

        if (duplicateDevice) {
            alert('اسم الجهاز موجود مسبقاً، يرجى اختيار اسم آخر');
            document.getElementById('edit_device_name').focus();
            return false;
        }
    }

    return true;
}

// Validate numbers are positive
document.querySelectorAll('input[type="number"]').forEach(input => {
    input.addEventListener('input', function() {
        if (this.value < 0) {
            this.value = '';
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});

// Initialize Bootstrap validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms)
        .forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
})()
</script>

<?php require_once 'includes/footer.php'; ?>