<?php
/**
 * إصلاح تحذيرات الجلسات في النظام
 * PlayGood - Gaming Center Management System
 */

// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إصلاح تحذيرات الجلسات";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .result-box { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
        .success-box { border-left-color: #28a745; }
        .error-box { border-left-color: #dc3545; }
        .warning-box { border-left-color: #ffc107; }
    </style>
</head>
<body class="bg-light">

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إصلاح تحذيرات الجلسات في النظام
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_sessions'])): ?>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            جاري إصلاح تحذيرات الجلسات...
                        </div>

                        <?php
                        $files_to_fix = [
                            'admin/includes/auth.php' => 'ملف المصادقة الرئيسي للإدمن',
                            'admin/client_permissions.php' => 'صفحة إدارة صلاحيات العملاء',
                            'setup_missing_client_pages.php' => 'ملف إعداد الصفحات المفقودة',
                            'apply_admin_pages_update.php' => 'ملف تطبيق التحديثات'
                        ];

                        $fixed_count = 0;
                        $error_count = 0;

                        foreach ($files_to_fix as $file_path => $description) {
                            echo "<div class='result-box'>";
                            echo "<h6><i class='fas fa-file-code me-2'></i>$description</h6>";
                            echo "<small class='text-muted'>$file_path</small><br>";

                            if (file_exists($file_path)) {
                                $content = file_get_contents($file_path);
                                $original_content = $content;

                                // البحث عن session_start() وإصلاحها
                                $patterns = [
                                    '/^session_start\(\);$/m' => 'if (session_status() === PHP_SESSION_NONE) {
    session_start();
}',
                                    '/^<\?php\s*\n\s*session_start\(\);/m' => '<?php
// بدء الجلسة فقط إذا لم تكن مبدوءة مسبقاً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}'
                                ];

                                $changes_made = false;
                                foreach ($patterns as $pattern => $replacement) {
                                    if (preg_match($pattern, $content)) {
                                        $content = preg_replace($pattern, $replacement, $content);
                                        $changes_made = true;
                                    }
                                }

                                if ($changes_made && $content !== $original_content) {
                                    if (file_put_contents($file_path, $content)) {
                                        echo "<span class='text-success'><i class='fas fa-check me-1'></i>تم الإصلاح بنجاح</span>";
                                        $fixed_count++;
                                    } else {
                                        echo "<span class='text-danger'><i class='fas fa-times me-1'></i>فشل في كتابة الملف</span>";
                                        $error_count++;
                                    }
                                } else {
                                    echo "<span class='text-info'><i class='fas fa-info-circle me-1'></i>الملف محدث مسبقاً أو لا يحتاج إصلاح</span>";
                                }
                            } else {
                                echo "<span class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>الملف غير موجود</span>";
                            }

                            echo "</div>";
                        }

                        // إصلاح إضافي لملفات أخرى قد تحتوي على نفس المشكلة
                        echo "<div class='result-box'>";
                        echo "<h6><i class='fas fa-search me-2'></i>البحث عن ملفات أخرى تحتاج إصلاح</h6>";

                        $additional_files = [];
                        $directories = ['admin', 'client', '.'];
                        
                        foreach ($directories as $dir) {
                            if (is_dir($dir)) {
                                $files = glob($dir . '/*.php');
                                foreach ($files as $file) {
                                    if (is_file($file)) {
                                        $content = file_get_contents($file);
                                        if (preg_match('/^session_start\(\);$/m', $content) && 
                                            !preg_match('/session_status\(\)/', $content)) {
                                            $additional_files[] = $file;
                                        }
                                    }
                                }
                            }
                        }

                        if (!empty($additional_files)) {
                            echo "<p>تم العثور على ملفات إضافية تحتاج إصلاح:</p>";
                            echo "<ul>";
                            foreach ($additional_files as $file) {
                                echo "<li><code>$file</code></li>";
                            }
                            echo "</ul>";
                            echo "<p class='text-info'><i class='fas fa-info-circle me-1'></i>يمكنك إصلاح هذه الملفات يدوياً باستبدال <code>session_start();</code> بالكود المحدث.</p>";
                        } else {
                            echo "<p class='text-success'><i class='fas fa-check me-1'></i>لم يتم العثور على ملفات إضافية تحتاج إصلاح.</p>";
                        }

                        echo "</div>";

                        // النتيجة النهائية
                        if ($error_count === 0) {
                            echo "<div class='alert alert-success mt-4'>";
                            echo "<h5><i class='fas fa-check-circle me-2'></i>تم إصلاح تحذيرات الجلسات بنجاح!</h5>";
                            echo "<p>تم إصلاح <strong>$fixed_count</strong> ملف بنجاح.</p>";
                            echo "<p class='mb-0'>لن تظهر تحذيرات الجلسات بعد الآن.</p>";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-warning mt-4'>";
                            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>تم الإصلاح مع بعض الأخطاء</h5>";
                            echo "<p>تم إصلاح: <strong>$fixed_count</strong> ملف، فشل: <strong>$error_count</strong> ملف.</p>";
                            echo "<p class='mb-0'>يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً.</p>";
                            echo "</div>";
                        }

                        echo "<div class='text-center mt-4'>";
                        echo "<a href='admin/client_permissions.php' class='btn btn-primary me-2'>";
                        echo "<i class='fas fa-users-cog me-1'></i>اختبار إدارة الصلاحيات";
                        echo "</a>";
                        echo "<a href='admin/dashboard.php' class='btn btn-secondary'>";
                        echo "<i class='fas fa-home me-1'></i>لوحة تحكم الإدمن";
                        echo "</a>";
                        echo "</div>";
                        ?>

                    <?php else: ?>
                        
                        <!-- معلومات حول المشكلة -->
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2'></i>حول مشكلة تحذيرات الجلسات</h5>
                            <p>تظهر رسالة الخطأ التالية عند محاولة بدء جلسة مرتين:</p>
                            <code class="d-block bg-light p-2 rounded">
                                Notice: session_start(): Ignoring session_start() because a session is already active
                            </code>
                            <p class="mt-2 mb-0">هذا يحدث عندما يتم استدعاء <code>session_start()</code> في أكثر من ملف في نفس الطلب.</p>
                        </div>

                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>الحل</h5>
                            <p>سيتم استبدال جميع استدعاءات <code>session_start()</code> بالكود التالي:</p>
                            <pre class="bg-light p-3 rounded"><code>if (session_status() === PHP_SESSION_NONE) {
    session_start();
}</code></pre>
                            <p class="mb-0">هذا الكود يتحقق من حالة الجلسة قبل بدء جلسة جديدة.</p>
                        </div>

                        <div class="alert alert-success">
                            <h5><i class="fas fa-list me-2"></i>الملفات التي سيتم إصلاحها</h5>
                            <ul class="mb-0">
                                <li><strong>admin/includes/auth.php</strong> - ملف المصادقة الرئيسي للإدمن</li>
                                <li><strong>admin/client_permissions.php</strong> - صفحة إدارة صلاحيات العملاء</li>
                                <li><strong>setup_missing_client_pages.php</strong> - ملف إعداد الصفحات المفقودة</li>
                                <li><strong>apply_admin_pages_update.php</strong> - ملف تطبيق التحديثات</li>
                            </ul>
                        </div>

                        <!-- نموذج التأكيد -->
                        <form method="POST" class="text-center">
                            <button type="submit" name="fix_sessions" class="btn btn-primary btn-lg">
                                <i class="fas fa-tools me-2"></i>
                                إصلاح تحذيرات الجلسات
                            </button>
                        </form>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
